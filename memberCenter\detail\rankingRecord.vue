<template>
	<view class="ranking_content_style">
		<view class="f-56 c-ff lh-72 font_Weight">领取记录</view>
		<view class="ranking_content_list plr-32 bg-ff">
			<view v-for="(item,index) in rankingList" :key="index" class="border-bottom_css pb-20 flex-a-c flex-x-b f-28 lh-40 plr-30">
				<view>
					<view class="c-55">{{item.eventName}}</view>
					<view class="c-ab mt-16">{{item.time}}</view>
				</view>
				<view class="integral_css">
					+{{item.integral}}积分
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	const {
			$navigationTo,
			$getSceneData,
			$showError,
			$http
		} = require("@/util/methods.js")
	export default {
		data() {
			return {
				rankingList:[]
			}
		},
		onLoad() {
			this.getEventRecord()
		},
		methods: {
			async getEventRecord(){
				let _this = this
				const res = await $http({
					url: 'zx/wap/rank/getEventRecord',
					showLoading:true,
					data: {
						eventType:'RANKING_INCREASE',
						userId:uni.getStorageSync('user_id')?uni.getStorageSync('user_id'):'',
					}
				})
				if(res){
					this.rankingList=res.data.details
				}
			}
		}
	}
</script>

<style lang="scss" scoped>
.ranking_content_style{
	background: url('https://document.dxznjy.com/course/a79760c439d34a66aa46b80aae2a38c5.png') no-repeat;
	background-size: 100%;
	height: 100vh;
	.font_Weight{
		font-weight: WenYiHei;
		padding-top: 116rpx;
		padding-left: 60rpx;
	}
	.ranking_content_list{
		border-radius: 40rpx 40rpx 0rpx 0rpx;
		margin-top: 100rpx;
		padding-top: 50rpx;
		height: calc(100vh - 282rpx);
		.integral_css{
			color:#EE9F00;
		}
		.border-bottom_css{
			border-bottom: 1rpx solid #ECF0F4;
			margin-bottom: 24rpx;
		}
	}
}
</style>
