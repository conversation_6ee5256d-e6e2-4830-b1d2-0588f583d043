<template>
  <view class="report_Container plr-25" style="background-image: url('https://document.dxznjy.com/applet/newimages/report/reportBg.png')">
    <view class="arrow">
      <uni-icons type="left" size="20" @click="back"></uni-icons>
    </view>
    <view class="title">
      <view class="title-text">
        <image src="https://document.dxznjy.com/applet/newimages/report/report_HeadText.png" mode=""></image>
      </view>
    </view>
    <!-- 学员信息盒子 -->
    <view class="information" style="background-image: url('https://document.dxznjy.com/dxSelect/d5a6f0fd-af2e-4f27-bd56-0dc1c0bf1fe4.png')">
      <view class="information-bootom">
        <view class="studentInfo">
          <view
            class="studentTitle plr-20 mb-30"
            style="max-width: 89%; white-space: nowrap; overflow: hidden; text-overflow: ellipsis"
            :title="reportDate.name"
            @click="showFullName"
          >
            {{ reportDate.name }}
          </view>
          <view class="pb-15 f-28">
            学员姓名：
            <span class="textColor">{{ reportDate.studentName }}</span>
          </view>
          <view class="pb-15 f-28">
            {{ reportDate.type == 2 ? '课程内容：' : '试卷名称：' }}
            <span class="textColor">{{ reportDate.courseName }}</span>
          </view>
          <view class="pb-15 f-28">
            {{ reportDate.type == 2 ? '课程时长：' : '考试时间：' }}
            <span class="textColor">{{ reportDate.examTime }}</span>
          </view>
          <view class="pb-15 f-28">
            {{ reportDate.type == 2 ? '随堂训练成绩：' : '测试成绩：' }}
            <span class="textColor">{{ reportDate.score }}分（满分{{ reportDate.fullScore }}分）</span>
          </view>
          <view class="pb-15 f-28">
            {{ reportDate.type == 2 ? '随堂训练用时：' : '测试用时：' }}
            <span class="textColor">{{ reportDate.usedExamTime }}{{ reportDate.type == 2 ? `(测试时长${reportDate.examTime})` : '' }}</span>
          </view>
          <view class="pb-15 f-28">
            整体掌握程度：
            <span class="textColor">
              {{ reportDate.proficiencyScore > 80 ? '优秀' : reportDate.proficiencyScore > 50 ? '良好' : '未掌握' }}{{ `(${reportDate.proficiencyScore}%)` }}
            </span>
          </view>
          <view class="f-28">
            {{ reportDate.type == 2 ? '学习日期：' : '测试日期：' }}
            <span class="textColor">{{ reportDate.learningDate }}</span>
          </view>
        </view>
      </view>
    </view>
    <view class="studyInfo mt-28">
      <view style="background-image: url('https://document.dxznjy.com/dxSelect/7a90f288-8e3c-41e9-978a-9f7918ece0a8.png')" class="study_headbg">
        <view class="studentTitle plr-20 mb-30 mt-35 ml-30">一、学习情况分析</view>
      </view>
      <view class="overallPer borderSolid pt-30" v-if="reportDate.type == 2">
        <image src="https://document.dxznjy.com/dxSelect/5979c4e5-9eb1-4437-ac1a-7b13b9ca34e7.png" style="width: 32rpx" mode="widthFix" class="mr-25 ml-30"></image>
        <text class="f-28 bold">整体表现</text>
        <view class="p-20 mt-10 radius-25 performance">
          <text class="ml-30 mr-30 textColorGreen">互动参与</text>
          <text>主动回答{{ reportDate.participateTime }}次，小组讨论积极</text>
        </view>
      </view>
      <view class="correctAnswer borderSolid pt-28">
        <image src="https://document.dxznjy.com/dxSelect/5979c4e5-9eb1-4437-ac1a-7b13b9ca34e7.png" style="width: 32rpx" mode="widthFix" class="mr-25 ml-30"></image>
        <text class="f-28 bold">答题正确率</text>
        <view class="p-20 mt-10 performance" style="display: flex">
          <view class="progress-bar-container">
            <!-- 绿色区域 -->
            <view
              class="greenCorrect"
              :class="correctRate == 100 ? 'greenCorrectBorder' : 'greenCorrectClip'"
              :style="{ width: correctRate + '%', backgroundColor: '#00D226' }"
            ></view>
            <!-- 中间斜角遮罩层 -->
            <view v-if="correctRate > 0 && correctRate < 100" class="slant-mask" :style="{ left: 'calc(' + correctRate + '% - 10rpx)' }"></view>
            <!-- 黄色区域 -->
            <view
              class="yellowError"
              :class="correctRate == 0 ? 'yellowErrorBorder' : 'yellowErrorClip'"
              :style="{ width: 100 - correctRate + '%', backgroundColor: 'yellow' }"
            ></view>
          </view>
          <view style="margin-left: 74rpx">
            <view class="textColorGreen f-26 mb-20">
              正确率
              <span class="ml-20">{{ correctRate }}%</span>
            </view>
            <view class="f-24">共{{ totalQuestions }}题，正确{{ correctAnswers }}题</view>
          </view>
        </view>
      </view>
    </view>
    <view class="pt-30 borderSolid errorType" v-if="reportDate.wrongQuestionSummarize && reportDate.wrongQuestionSummarize.length > 0">
      <view style="display: flex">
        <image src="https://document.dxznjy.com/dxSelect/5979c4e5-9eb1-4437-ac1a-7b13b9ca34e7.png" style="width: 32rpx" mode="widthFix" class="mr-25 ml-30"></image>
        <view class="f-28 bold">错题类型归纳</view>
      </view>
      <view class="gridContainer mt-25">
        <view class="flex-space-around">
          <text>错题类型</text>
          <text>错题原因</text>
          <text>改进建议</text>
        </view>
        <view class="flex-space-aroundInfo" v-for="(baseListItem, baseListIndex) in reportDate.wrongQuestionSummarize" :key="baseListIndex">
          <text class="grid-text f-12">{{ baseListItem.type }}</text>
          <text class="grid-text f-12">{{ baseListItem.reason }}</text>
          <text class="grid-text f-12">{{ baseListItem.suggest }}</text>
        </view>
      </view>
    </view>
    <view class="p-20 pt-30 knowLevel borderSolid">
      <view style="display: flex">
        <image src="https://document.dxznjy.com/dxSelect/5979c4e5-9eb1-4437-ac1a-7b13b9ca34e7.png" style="width: 32rpx" mode="widthFix" class="mr-25 ml-30"></image>
        <view class="f-28 bold mb-20">知识掌握等级</view>
      </view>
      <image src="https://document.dxznjy.com/dxSelect/74311591-7f7d-43b9-b494-df4bd9aa3428.png" style="width: 100%" mode="widthFix"></image>
      <view class="flex-s mb-20" style="width: 100%">
        <view class="contentWrapper radius-25 plr-30 knowBox" v-if="reportDate.excellentKnowledgeNames && reportDate.excellentKnowledgeNames.length > 0">
          <view class="knowText" v-for="(item, index) in reportDate.excellentKnowledgeNames.slice(0, 4)" :key="index">
            <view>{{ item }}</view>
          </view>
          <view class="knowIcon pr-20 iconContainer">
            <image src="https://document.dxznjy.com/dxSelect/bb16c5d4-8f0a-490a-bc0a-ecf551574976.png" style="width: 60rpx" mode="widthFix"></image>
            <image src="https://document.dxznjy.com/dxSelect/34effbf2-c54e-41ce-b4bd-a0b43c937401.png" style="width: 60rpx" mode="widthFix"></image>
          </view>
        </view>

        <!-- 如果两个模块都不存在，则不显示中间的空白 view -->
        <view
          style="width: 20rpx"
          v-if="
            reportDate.excellentKnowledgeNames &&
            reportDate.excellentKnowledgeNames.length > 0 &&
            reportDate.consolidateKnowledgeNames &&
            reportDate.consolidateKnowledgeNames.length > 0
          "
        ></view>

        <!-- 巩固知识点模块  -->
        <view class="radius-25 plr-30 knowBox" v-if="reportDate.consolidateKnowledgeNames && reportDate.consolidateKnowledgeNames.length > 0">
          <view class="knowText" v-for="(item, index) in reportDate.consolidateKnowledgeNames.slice(0, 4)" :key="index">
            <view>{{ item }}</view>
          </view>
          <view class="knowIcon pr-20 iconContainer">
            <image src="https://document.dxznjy.com/dxSelect/54429840-1171-45d9-84e3-03dabc80a886.png" style="width: 60rpx" mode="widthFix"></image>
            <image src="https://document.dxznjy.com/dxSelect/cd981110-c00b-457f-afa1-9e72c3bed7e7.png" style="width: 60rpx" mode="widthFix"></image>
          </view>
        </view>
      </view>
      <view class="gridContainer">
        <view class="flex-space-around">
          <text>知识点小节名称</text>
          <text>掌握状态</text>
        </view>
        <view class="flex-space-aroundInfo" v-for="(baseListItem, baseListIndex) in reportDate.knowledgeProficiencyList" :key="baseListIndex">
          <text class="grid-text f-15" style="width: 50%">{{ baseListItem.knowledgeName }}</text>
          <view class="knowledgeStatus">
            <image
              :src="
                baseListItem.proficiencyScore > 80
                  ? 'https://document.dxznjy.com/dxSelect/9ae638f6-ec03-431b-9416-5b0f920b6ead.png'
                  : baseListItem.proficiencyScore > 50
                  ? 'https://document.dxznjy.com/dxSelect/1c48892a-983a-4946-8023-65c326a982d1.png'
                  : 'https://document.dxznjy.com/dxSelect/228e22f6-7653-4f22-a7c1-792607aaccf3.png'
              "
              mode="widthFix"
              style="width: 117rpx"
            ></image>
            <text class="mlr-20" style="color: #14806c; width: 35px">{{ baseListItem.proficiencyScore }}%</text>
          </view>
        </view>
      </view>
    </view>
    <!-- todo 饼图 -->
    <view class="p-20 pt-30 barChart borderSolid" v-if="reportDate.type == 2">
      <view style="display: flex">
        <image src="https://document.dxznjy.com/dxSelect/5979c4e5-9eb1-4437-ac1a-7b13b9ca34e7.png" style="width: 32rpx" mode="widthFix" class="mr-25 ml-30"></image>
        <text class="f-28 bold mb-20">知识点掌握程度</text>
      </view>

      <view style="display: flex; justify-content: space-evenly">
        <view style="width: 250rpx; height: 250rpx">
          <qiun-data-charts type="ring" :opts="opts1" :chartData="chartDataBefore" />
        </view>
        <image src="https://document.dxznjy.com/dxSelect/3a332448-c73a-47ad-b698-11a6eb53ac5e.png" style="width: 100rpx; align-self: center" mode="widthFix"></image>
        <view style="width: 250rpx; height: 250rpx">
          <qiun-data-charts type="ring" :opts="opts2" :chartData="chartDataAfter" />
        </view>
      </view>
    </view>
    <!-- 学习建议 -->
    <view class="studySuggest mt-28">
      <view style="background-image: url('https://document.dxznjy.com/dxSelect/7a90f288-8e3c-41e9-978a-9f7918ece0a8.png')" class="study_headbg">
        <view class="studentTitle plr-20 mb-30 mt-35 ml-30">二、学习建议</view>
      </view>
      <view class="studySuggestInfo pl-30 borderSolid">
        <view class="radius-25">
          <view class="p-20 color-report textColorGreen bold black" style="height: 40rpx">针对性练习</view>
          <view v-for="(item, index) in reportDate.targetedExercises" :key="index" class="pl-10 mt-10">
            <view>{{ item }}</view>
          </view>
        </view>
        <view class="mt-10 radius-25">
          <view class="p-20 color-report textColorGreen bold black" style="height: 40rpx">学习方法优化</view>
          <view v-for="(item, index) in reportDate.learningMethodOptimize" :key="index" class="pl-10 mt-10">
            <view>{{ item }}</view>
          </view>
        </view>
        <view class="mt-10 pb-20 radius-25">
          <view class="color-report p-20 textColorGreen bold black" style="height: 40rpx">家长配合</view>
          <view v-for="(item, index) in reportDate.parentCooperation" :key="index" class="pl-10 mt-10">
            <view>{{ item }}</view>
          </view>
        </view>
      </view>
      <view class="t-c reportDate f-20" v-if="reportDate.type == 1">生成日期：{{ reportDate.createTime }}</view>
    </view>
    <!-- 教师评语 -->
    <view class="teacherComment mt-40" v-if="reportDate.type == 2">
      <view style="background-image: url('https://document.dxznjy.com/dxSelect/7a90f288-8e3c-41e9-978a-9f7918ece0a8.png')" class="study_headbg">
        <view class="studentTitle plr-20 mb-30 mt-35 ml-30">三、教师评语</view>
      </view>
      <view class="teacherCommentBox">
        <view class="circle"></view>
        <view class="mb-10 pl-30 textColorGreen color-report black">{{ reportDate.studentName }}同学</view>
        <view class="pl-40 m-35">{{ reportDate.teacherComment }}</view>
        <view class="t-c reportDate f-20">生成日期：{{ reportDate.createTime }}</view>
      </view>
    </view>
    <view class="m-20 c-a6 f-18 pb-20 mt-40" style="color: #498972">注：本报告基于AI数据分析生成，仅供参考，建议结合老师实际观察调整教学方案</view>
    <uni-popup ref="namePopup" type="center">
      <view style="padding: 40rpx; font-size: 32rpx; max-width: 80vw; word-break: break-all; background-color: aliceblue; border-radius: 20px">
        {{ reportDate.name }}
      </view>
    </uni-popup>
  </view>
</template>

<script>
  import qiunDataCharts from '../components/qiun-data-charts/components/qiun-data-charts/qiun-data-charts.vue';
  export default {
    components: {
      qiunDataCharts
    },
    data() {
      return {
        studentCode: '', //学生code
        itemId: '', //传述过来的排课id
        reportDate: {}, // 报告数据
        correctRate: 0, // 正确率
        totalQuestions: 0, // 总题数
        correctAnswers: 0, // 正确题数
        chartDataBefore: {},
        chartDataAfter: {},
        opts1: {},
        opts2: {}
      };
    },
    onLoad(option) {
      // 获取传递过来的id参数
      let itemId = option.classPlanStudyId;
      let studentCode = option.studentCode;
      this.itemId = itemId;
      this.studentCode = studentCode;
      this.fetchReport();
    },
    onReady() {},
    methods: {
      async fetchReport() {
        let res = await this.$httpUser.post('dyf/math/wap/learningReport/get', {
          studentCode: this.studentCode,
          businessId: this.itemId
        });
        if (res) {
          this.reportDate = res.data.data;
          console.log('reportDate', this.reportDate.excellentKnowledgeNames);
          this.reportDate.courseDuration = this.convertSeconds(this.reportDate.courseDuration, 1);
          this.reportDate.usedExamTime = this.convertSeconds(this.reportDate.usedExamTime);
          this.reportDate.examTime = this.convertSeconds(this.reportDate.examTime);
          this.totalQuestions = this.reportDate.allQuestionNumber;
          this.correctAnswers = this.reportDate.answerRightNumber;
          this.correctRate = ((this.correctAnswers / this.totalQuestions) * 100).toFixed(2);
          (this.chartDataBefore = {
            series: [
              {
                data: [
                  { name: '正确', value: this.reportDate.learnBeforeScore },
                  { name: '错误', value: 100 - this.reportDate.learnBeforeScore }
                ]
              }
            ]
          }),
            (this.chartDataAfter = {
              series: [
                {
                  data: [
                    { name: '正确', value: this.reportDate.learnAfterScore },
                    { name: '错误', value: 100 - this.reportDate.learnAfterScore }
                  ]
                }
              ]
            });
          this.opts1 = {
            rotate: false,
            rotateLock: false,
            color: ['#31BD82', '#CDFCE1'],
            padding: [5, 5, 5, 5],
            dataLabel: false,
            enableScroll: false,
            legend: {
              show: false,
              position: 'right',
              lineHeight: 25
            },
            subtitle: {
              name: '掌握程度',
              fontSize: 12,
              color: '#555'
            },
            extra: {
              ring: {
                ringWidth: 15,
                activeOpacity: 0.5,
                activeRadius: 10,
                offsetAngle: 0,
                labelWidth: 15,
                border: false,
                centerColor: '#f4fef8'
              }
            },
            title: {
              name: `${this.reportDate.learnBeforeScore}%`,
              fontSize: 20,
              color: '#004D2C'
            }
          };
          this.opts2 = {
            rotate: false,
            rotateLock: false,
            color: ['#F5E33F', '#FFF4C4'],
            // padding: [5, 5, 5, 5],
            dataLabel: false,
            enableScroll: false,
            legend: {
              show: false,
              position: 'right',
              lineHeight: 25
            },
            subtitle: {
              name: '掌握程度',
              fontSize: 12,
              color: '#666666'
            },
            extra: {
              ring: {
                ringWidth: 15,
                activeOpacity: 0.5,
                activeRadius: 10,
                offsetAngle: 0,
                labelWidth: 15,
                border: false,
                centerColor: '#f4fef8'
              }
            },
            title: {
              name: `${this.reportDate.learnAfterScore}%`,
              fontSize: 20,
              color: '#663903'
            }
          };
        } else {
          uni.showToast({
            icon: 'none',
            title: '暂无数据！',
            duration: 2000
          });
        }
      },
      showFullName() {
        this.$refs.namePopup.open();
      },
      convertSeconds(seconds, excludeSeconds = 0) {
        const hours = Math.floor(seconds / 3600); // 计算小时
        const minutes = Math.floor((seconds % 3600) / 60); // 计算分钟
        const remainingSeconds = seconds % 60; // 计算秒数

        let result = '';

        // 如果小时大于 0，显示小时
        if (hours > 0) {
          result += `${hours}小时`;
        }

        // 如果分钟大于 0，显示分钟
        if (minutes > 0) {
          result += `${minutes}分钟`;
        }

        // 如果秒数大于 0，且未被排除，显示秒数
        if (remainingSeconds > 0 && excludeSeconds !== 1) {
          result += `${remainingSeconds}秒`;
        }

        // 如果没有任何时间显示，返回 0
        return result || '0 秒';
      },
      back() {
        uni.navigateBack();
      }
    }
  };
</script>

<style lang="scss" scope>
  .report_Container {
    width: 750rpx;
    height: auto;
    min-height: 1176rpx;
    // background-image: url('../../static/dictation/report/reportBg.png');
    // background-size: cover;
    background-size: 375px 588px;
    background-repeat: no-repeat;
    // background-position: 50% 50%;
    padding-top: 68rpx;
    padding-bottom: 48rpx;
    box-sizing: border-box;
    background-color: #b4ebcd;
    // 顶部文字
    .title {
      margin-top: 65rpx;
      margin-bottom: 60rpx;
      width: 710rpx;
      height: 258rpx;
      display: flex;
      justify-content: space-between;

      .title-text {
        font-size: 46rpx;
        font-family: AlibabaPuHuiTiM;
        color: #ffffff;

        image {
          width: 720rpx;
          height: 262rpx;
        }
      }

      .title-img image {
        width: 200rpx;
        height: 260rpx;
      }
    }
    // 学员信息盒子
    .information {
      .studentInfo {
        image {
          width: 180rpx;
          height: 56rpx;
        }

        .textColor {
          color: #30b094;
        }
      }

      margin-top: 380rpx;
      width: 100%;
      height: 512rpx;
      // background-image:url('../../static/dictation/report/report_HeadBg.png');
      background-size: contain;
      background-repeat: no-repeat;

      .information-bootom {
        line-height: 40rpx;
        padding-left: 32rpx;
        padding-top: 37rpx;
        font-size: 30rpx;
        color: #1c3a2b;

        .bootom-text {
          display: flex;
          justify-content: space-between;
        }

        .green {
          color: #3ac544;
        }
      }
    }
    .studyInfo {
      width: 100%;
      .overallPer {
        background-color: #f4fef8;
      }
      .correctAnswer {
        background-color: #f4fef8;
        .progress-bar-container {
          position: relative;
          display: flex;
          height: 67rpx;
          flex: 1;
          overflow: hidden;
          border-radius: 34rpx;
          background-color: transparent;
          .greenCorrect {
            position: absolute;
            left: 0;
            height: 100%;
            border-top-left-radius: 34rpx;
            border-bottom-left-radius: 34rpx;
          }
          .greenCorrectClip {
            // clip-path: polygon(0 0, 100% 0, 254rpx 100%, 0 100%);
          }
          .greenCorrectBorder {
            border-top-right-radius: 34rpx;
            border-bottom-right-radius: 34rpx;
          }

          .yellowError {
            position: absolute;
            right: 0;
            height: 100%;
            border-top-right-radius: 34rpx;
            border-bottom-right-radius: 34rpx;
          }
          .yellowErrorClip {
            // clip-path: polygon(20rpx 0, 100% 0, 100% 100%, 0 100%);
          }
          .yellowErrorBorder {
            border-top-left-radius: 34rpx;
            border-bottom-left-radius: 34rpx;
          }
          .slant-mask {
            position: absolute;
            top: 0;
            width: 20rpx; /* 控制斜角的宽度 */
            height: 100%;
            background-color: #f4fef8; /* 或者和背景一样的颜色，实现“透明错位”效果 */
            transform: skewX(-15deg);
            pointer-events: none; /* 避免遮罩阻挡事件 */
            z-index: 2; /* 确保遮罩盖在绿色黄色之间 */
          }
        }
      }
    }
    .studySuggest {
      width: 100%;
      .studySuggestInfo {
        background-color: #f4fef8;
      }
    }
    .teacherComment {
      width: 100%;
      .teacherCommentBox {
        background-color: #f4fef8;
      }
    }
    .errorType {
      background-color: #f4fef8;
    }
    .knowLevel {
      background-color: #f4fef8;
      .knowBox {
        width: 100%;
        height: 150rpx;
        border: 2rpx solid #00d226;
        position: relative;
        padding: 14rpx;
        // display: flex;
        // justify-content: space-between;
        .knowIcon {
          display: flex;
          flex-direction: column;
          justify-content: space-between;
        }
        .contentWrapper {
          /* 确保内容正常显示 */
          padding-right: 80rpx; /* 为图标预留空间 */
        }
        .iconContainer {
          position: absolute;
          top: 28rpx; /* 调整顶部距离 */
          right: 20rpx; /* 调整右侧距离 */
          display: flex;
          flex-direction: column;
          justify-content: flex-start;
          align-items: flex-end;
        }
        .knowText {
          display: flex;
          flex-direction: column;
          justify-content: space-evenly;
          font-size: 28rpx;
          width: 60%;
        }
      }
    }
    .barChart {
      background: linear-gradient(to right, #f4fef8 0%, #e9fdf1 70%);
    }
    .textColorGreen {
      color: #3ac544;
    }
    .borderSolid {
      border-left: 18rpx solid #e9fdf1;
      border-right: 18rpx solid #e9fdf1;
    }
    .performance {
      width: 90%;
      margin: auto;
      height: 110rpx;
      display: flex;
      align-items: center;
      background: linear-gradient(to bottom, #f4fef8 0%, #e9fdf1 50%);
    }
  }
  .studentTitle {
    font-weight: bold;
    display: inline-block;
    background: linear-gradient(to bottom, transparent 0%, transparent 55%, #f4df35 55%, #f4df35 100%);
  }
  .study_headbg {
    width: 100%;
    height: 90rpx;
    background-repeat: no-repeat;
    background-size: cover;
  }
  .grid-text {
    flex: 1;
    // font-size: 14px;
    padding: 20rpx;
    /* #ifndef APP-PLUS */
    box-sizing: border-box;
    /* #endif */
  }
  .knowledgeStatus {
    display: flex;
    align-items: center;
  }
  .gridContainer {
    border-radius: 10rpx;
    border: 2rpx solid #00d226;
  }
  .flex-space-around {
    display: flex;
    justify-content: space-around;
    align-items: center;
    font-size: 26rpx;
    background-color: #b3ebcd;
    border-radius: 5rpx 5rpx 0rpx 0rpx;
    height: 60rpx;
    margin: 5rpx;
    font-weight: bold;
  }
  .flex-space-aroundInfo {
    display: flex;
    justify-content: space-evenly;
    align-items: center;
    font-size: 26rpx;
    background-color: #efffee;
    border-radius: 5rpx 5rpx 0rpx 0rpx;
    margin: 5rpx;
  }
  .reportDate {
    line-height: 60rpx;
    text-align: center;
    color: #3eaa8c;
    background-color: #e6fcec;
  }
  .black::before {
    background-color: #35c017;
  }
</style>
