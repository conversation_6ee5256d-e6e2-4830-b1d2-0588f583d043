<template>
  <view class="subjectTest">
    <view class="title c-33 mb-20">
      <text class="questionType f-24 mr-10 t-c" :class="item.questionDifficulty == '0' ? 'questionType3' : item.questionDifficulty == '1' ? 'questionType2' : 'questionType1'">
        {{ item.questionDifficulty == '0' ? '基础' : item.questionDifficulty == '1' ? '进阶' : '拔高' }}
      </text>
      <text>{{ item.serialNumber ? item.serialNumber : index + 1 }}.【{{ tyepName }}】</text>
      <view style="">
        <view v-for="(segment, index) in parseContent(item.questionText)" :key="index" style="display: inline; vertical-align: top">
          <text v-if="segment.type === 'text'" style="display: inline; vertical-align: top">{{ segment.content }}</text>
          <text v-else style="font-size: 14px; max-width: 600rpx; overflow: auto; display: inline; vertical-align: top" v-html="option(segment.content)"></text>
        </view>
      </view>
      <text v-if="item.questionScore != 0">({{ item.questionScore }}分)</text>
    </view>

    <view class="imgs" v-if="item.questionImage && item.questionImage.length">
      <image v-for="(item, index) in item.questionImage" :src="item" :key="index" style="width: 200rpx; height: 200rpx" @click="previewImage(item)"></image>
    </view>

    <view class="questions" v-for="(obj, index) in item.mathSmallQuestionList" :key="index">
      <view class="questionsTitle" v-if="item.mathSmallQuestionList.length > 1">
        <view style="">
          {{ item.questionType == 1 ? tyepName + (index + 1) : '问' + (index + 1) }}:
          <view v-for="(segment, index) in parseContent(obj.question)" :key="index" style="display: inline; vertical-align: top">
            <text v-if="segment.type === 'text'" style="display: inline; vertical-align: top">{{ segment.content }}</text>
            <text v-else style="font-size: 14px; max-width: 600rpx; overflow: auto; display: inline; vertical-align: top" v-html="option(segment.content)"></text>
          </view>
        </view>
      </view>
      <!-- 提交时要获取到当前页面中选中的数据 -->
      <view class="flex-s h-80 mt-20 mb-20 componentMargin" style="background: #fff5ef; padding: 0 56rpx" v-if="status == '3'">
        <view class="f-28">
          <text class="mr-12">答案：</text>
          <text style="color: #ff5706">{{ currentAnswer || '未填写' }}</text>
        </view>
      </view>
      <view
        class="questionsItem"
        :class="[
          answerList[index].answer == e.choiceOption ? answerList[index].class : '',
          (status == '1' || status == '2') && answerList[index].answer == e.choiceOption && answerList[index].class === 'error' ? 'error' : ''
        ]"
        @click="status == '3' && check(e, index)"
        v-for="(e, i) in obj.optionList"
        :key="i"
      >
        {{ e.choiceOption }}.
        <view style="">
          <view v-for="(segment, index) in parseContent(e.content)" :key="index" style="display: inline; vertical-align: top">
            <text v-if="segment.type === 'text'" style="display: inline; vertical-align: top">{{ segment.content }}</text>
            <text v-else style="font-size: 14px; max-width: 600rpx; overflow: auto; display: inline; vertical-align: top" v-html="option(segment.content)"></text>
          </view>
        </view>
      </view>
      <view v-if="status == '3'">
        <view class="" v-if="photoTemp">
          <u-upload
            :fileList="answerList[index].fileList"
            @afterRead="(e) => afterRead(e, index)"
            @delete="(e) => deletePic(e, index)"
            multiple
            width="90"
            height="90"
            :maxCount="1"
          >
            <view class="uplaodImg">
              <image src="https://document.dxznjy.com/dxSelect/a2e85787-99da-41f7-b107-aecd64f6c190.png" style="width: 40rpx; height: 36rpx"></image>
              <view style="font-size: 22rpx; color: #616161">拍照上传答案</view>
            </view>
          </u-upload>
        </view>
      </view>
      <view v-else>
        <view class="imgs" v-if="answerList[index].fileList && answerList[index].fileList.length">
          <image v-for="(item, index) in answerList[index].fileList" :src="item.url" :key="index" style="width: 180rpx; height: 180rpx" @click="previewImage(item.url)"></image>
        </view>
      </view>
    </view>
    <view v-if="status == '3'">
      <view class="photo" v-if="photoTemp">
        <view style="height: 42rpx; line-height: 42rpx; font-weight: bold; color: #000">拍照上传答案请注意以下几点哦</view>
        <view style="height: 42rpx; line-height: 42rpx">1.请保存卷面整洁</view>
        <view style="height: 42rpx; line-height: 42rpx">2.作答过程完整</view>
        <view style="height: 42rpx; line-height: 42rpx">3.单个题目的答案要在一张图片上</view>
        <view style="height: 42rpx; line-height: 42rpx">4.小题号书写规范，如(1) (2) (3)</view>
      </view>
    </view>
    <!-- 中间区域内容 -->
    <view>
      <view class="flex-s h-80 mt-20 mb-20 componentMargin" style="background: #fff5ef; padding: 0 56rpx" v-if="status != '3'">
        <view class="f-28">
          <text class="mr-12">回答：</text>
          <text style="color: #ff5706">{{ item.myAnswer || '未填写' }}</text>
        </view>
        <view class="f-28">
          <text class="mr-12">得分：</text>
          <text style="color: #428a6f">{{ item.myScore }}分</text>
        </view>
      </view>
      <!-- 按钮区域 -->
      <view v-if="status == '1'">
        <!-- syncStatus   0 不展示   1 已下架  2 展示原题  3 已下架 -->
        <view class="h-40 f-30 c-99 lh-42 pt-20 pb-20 t-c componentMargin" v-if="item.syncStatus == 1">
          <text>已修改</text>
          <image
            src="https://document.dxznjy.com/dxSelect/c98bf3a4-b813-48be-9e1d-08d3f3618116.png"
            mode=""
            style="width: 26rpx; height: 26rpx; vertical-align: -4rpx; margin-left: 17rpx"
            @click="singleQuestionUpdate(item.id)"
          ></image>
        </view>
        <!-- 查看原题 -->
        <view class="componentMargin h-80 f-26 lh-80 t-c" style="background: #fbfbfb" @click="viewQuestionClick(item.id)" v-if="item.syncStatus == '2'">
          <text style="color: #428a6f">查看原题</text>
        </view>
        <!-- 已下架 -->
        <view class="h-40 f-30 c-99 lh-42 pt-20 pb-20 t-c componentMargin" v-if="item.syncStatus == '3'">
          <text>已下架</text>
        </view>

        <!-- 按钮 -->
        <view class="question-btn">
          <view class="bg-ff radius-35 t-c btn-setting flex-c" style="width: 196rpx; height: 69rpx" @click="goStudyBut(item.id)">
            <image src="https://document.dxznjy.com/dxSelect/4ad0599e-9340-4287-81f0-339d8f6f6f91.png" mode="" style="width: 132rpx; height: 40rpx"></image>
          </view>
          <!-- 举一反三 -->
          <view class="bg-ff radius-35 ml-20 mr-20" style="width: 183rpx; height: 69rpx">
            <image src="https://document.dxznjy.com/dxSelect/46ba37e5-8ef7-48a2-a841-98e8e7520fc0.png" mode="" class="grayImageBorder" v-if="item.syncStatus == 3"></image>
            <image
              v-else
              src="https://document.dxznjy.com/dxSelect/ecd43c61-ac5a-436b-a29b-111bfe8703d4.png"
              mode=""
              @click.stop="goExpandImprove(item.id)"
              style="width: 100%; height: 100%"
            ></image>
          </view>
          <view class="bg-ff radius-35 t-c flex-c" :class="deleteDisabled ? 'btn-setting1' : 'btn-setting'" style="width: 170rpx; height: 69rpx">
            <image
              v-if="!deleteDisabled"
              src="https://document.dxznjy.com/dxSelect/1e3fbb0c-f66a-4b28-a24a-9a59ff5cdcb2.png"
              mode=""
              style="width: 98rpx; height: 40rpx"
              @click="deleteQuestion(item.id)"
            ></image>
            <image v-else src="https://document.dxznjy.com/dxSelect/0afbbce7-9d97-4496-b4a4-8427232fe138.png" mode="" style="width: 98rpx; height: 40rpx"></image>
          </view>
        </view>
      </view>

      <!-- 查看解析 -->
      <view class="parsing componentMargin mt-20" v-if="status != '3'">
        <u-collapse :border="false" :value="collapseValue">
          <u-collapse-item class="custom-title" title="查看解析">
            <view style="font-weight: 600" class="mb-12">
              <text class="f-28 c-33 lh-40">正确答案：</text>
              <text class="f-28 ml-20 lh-40" style="color: #428a6f">{{ item.correctAnswer }}</text>
            </view>
            <view class="imgs" v-if="item.analysisImg && item.analysisImg.length">
              <image v-for="(item, index) in item.analysisImg" :src="item" :key="index" style="width: 200rpx; height: 200rpx" @click="previewImage(item)"></image>
            </view>
            <view style="">
              <view v-for="(segment, index) in parseContent(item.analysis)" :key="index" style="display: inline; vertical-align: top">
                <text v-if="segment.type === 'text'" style="display: inline; vertical-align: top">{{ segment.content }}</text>
                <text v-else style="font-size: 14px; max-width: 600rpx; overflow: auto; display: inline; vertical-align: top" v-html="option(segment.content)"></text>
              </view>
            </view>
          </u-collapse-item>
        </u-collapse>
      </view>
    </view>
  </view>
</template>

<script>
  import Config from '../../util/config.js';
  export default {
    props: {
      //题目
      item: {
        type: Object,
        default: () => {}
      },
      //索引
      index: {
        type: Number
      },
      //状态
      status: {
        type: Number
      },
      deleteDisabled: {
        type: Boolean,
        default: false
      }
    },
    watch: {
      index: {
        handler(val) {
          this.init();
        },
        immediate: true // 页面加载时立即执行一次
      },
      item: {
        handler(val) {
          this.init();
        },
        immediate: true // 页面加载时立即执行一次
      },
      deleteDisabled: {
        handler(val) {
          // console.log('deleteDisabled changed:', val);
        },
        immediate: true
      }
    },
    data() {
      return {
        baseUrl: uni.getStorageSync('baseUrl'),
        collapseValue: [0],
        fileList: [],
        latexHTML: '',
        typeArr: [
          { name: '单选', id: 0 },
          { name: '填空', id: 1 },
          { name: '计算', id: 2 },
          { name: '解方程', id: 3 },
          { name: '证明题', id: 4 },
          { name: '几何综合题', id: 5 }
        ],
        tyepName: '',
        photoTemp: false,
        answerList: [
          {
            fileList: []
          }
        ],
        expandImproveShow: false,
        // 获取提交的答案
        currentAnswer: ''
      };
    },
    mounted() {},
    methods: {
      option(e) {
        return this.renderFormula(e);
      },
      check(e, i) {
        this.answerList[i].answer = e.choiceOption;
        this.$forceUpdate();
        let a = this.answerList.map((e) => e.answer).join(',');
        this.currentAnswer = a;
        this.$emit('checked', a);
      },
      init() {
        this.answerList = [];
        this.tyepName = this.typeArr.find((e) => e.id == this.item.questionType).name;
        // this.questionDifficulty = this.typeArr.find((e) => e.id == this.item.questionDifficulty).name;
        this.photoTemp = this.item.questionType != 0 && this.item.questionType != 1;
        this.latexHTML = this.renderFormula(this.item.questionText);

        this.item.mathSmallQuestionList.forEach((e, i) => {
          let obj = {
            index: i,
            answer: '',
            class: 'right',
            fileList: []
          };
          if (this.item.myAnswerImage && this.item.myAnswerImage[i]) {
            obj.fileList[0] = { url: this.item.myAnswerImage[i] };
          }
          this.answerList.push(obj);
        });
        if (this.item.myAnswer) {
          let arr = this.item.myAnswer.split(',');
          arr.forEach((e, i) => {
            this.answerList[i].answer = e;
          });
        }
        if (this.status == '1' || this.status == '2') {
          let arr2 = this.item.correctAnswer.split(',');
          this.answerList.forEach((e, i) => {
            if (e.answer != arr2[i]) {
              e.class = 'error';
            }
          });
        }
      },
      parseContent(e) {
        // 正则表达式匹配 \\(...\\)
        const regex = /\\\((.*?)\\\)/g;
        let lastIndex = 0;
        let match;
        const segments = [];

        // 循环匹配所有公式
        while ((match = regex.exec(e)) !== null) {
          // 添加前面的文本
          if (match.index > lastIndex) {
            segments.push({
              type: 'text',
              content: e.substring(lastIndex, match.index)
            });
          }

          // 添加公式
          segments.push({
            type: 'formula',
            content: match[1] // 获取括号内的内容
          });

          lastIndex = match.index + match[0].length;
        }

        // 添加剩余的文本
        if (lastIndex < e.length) {
          segments.push({
            type: 'text',
            content: e.substring(lastIndex)
          });
        }
        return segments;
      },
      renderFormula(text) {
        try {
          if (!text) return;
          text = text.replace(/%/g, '\\%');

          // 处理分数
          text = text.replace(/-(\d+)(\d+)(\d+)(\d+)​/g, (match, p1, p2, p3, p4) => {
            return `-\\frac{${p1}${p2}}{${p3}${p4}}`;
          });
          text = text.replace(/(\d+)(\d+)(\d+)(\d+)​/g, (match, p1, p2, p3, p4) => {
            return `\\frac{${p1}${p2}}{${p3}${p4}}`;
          });

          // 修复特定的分数格式
          text = text.replace(/\\frac\{\\mathrm\{\\pi\}\}\{(\d+)\}/g, '\\frac{\\pi}{$1}');
          text = text.replace(/\\frac\{\\pi\{1\}\}\{(\d+)\}/g, '\\frac{\\pi}{$1}');
          text = text.replace(/\\frac\{([^}]*)\}\{(\d+)\}/g, '\\frac{$1}{$2}');

          // 处理不完整的 \frac 表达式
          text = text.replace(/\\frac\{([^}]*)\}(?!\{)/g, (match, p1) => {
            return `\\frac{${p1}}{1}`;
          });

          // 处理不完整的分数
          text = text.replace(/\\frac{(\d+)}\{(\d+)}/g, '\\frac{$1}{$2}');

          // 处理特殊字符
          text = text.replace(/\\cdot\\cdot/g, '\\cdots');
          text = text.replace(/\\ldots/g, '\\dots');
          text = text.replace(/\\dot{([^}]*)}/g, '\\dot{$1}');

          // 处理带指数的根号
          text = text.replace(/\\sqrt\[(\d+)\]\{(\d+)\}/g, (match, p1, p2) => {
            return `\\sqrt[${p1}]{${p2}}`;
          });

          // 处理循环小数
          text = text.replace(/0\.(\d+)˙(\d+)˙(\d+)˙(\d+)˙/g, (match, p1, p2, p3, p4) => {
            return `0.\\dot{${p1}${p2}${p3}${p4}}`;
          });

          // 处理希腊字母
          text = text.replace(/\\mathrm\{\\pi\}/g, '\\pi');
          text = text.replace(/\\mathrm\{\\Pi\}/g, '\\Pi');
          text = text.replace(/ππ/g, '\\pi');

          // 处理省略号
          text = text.replace(/……/g, '\\dots');
          text = text.replace(/⋅⋅⋅⋅⋅⋅/g, '\\dots');

          // return text;
          // 使用 KaTeX 渲染
          const rendered = this.$katex.renderToString(text, {
            throwOnError: false,
            displayMode: false,
            strict: false,
            trust: true
          });

          // 如果渲染失败，尝试直接返回原始表达式
          if (rendered.includes('ParseError')) {
            return text;
          }
          return rendered;
        } catch (error) {
          return text;
        }
      },
      previewImage(currentUrl) {
        // 设置标记，表示即将进行图片预览
        this.$parent.isPreviewingImage = true;
        // 如果要预览多张图片，可以传入 urls 数组
        uni.previewImage({
          current: currentUrl, // 当前显示图片的链接
          urls: [currentUrl] // 需要预览的图片链接列表
        });
      },
      deletePic(event, index) {
        this.answerList[index][`fileList${event.name}`].splice(event.index, 1);
        this.$emit('addImgae', index, this.answerList[index].fileList[0].url);
      },
      // 新增图片
      async afterRead(event, index) {
        // 当设置 multiple 为 true 时, file 为数组格式，否则为对象格式
        let lists = [].concat(event.file);
        let fileListLen = this.answerList[index][`fileList${event.name}`].length;
        lists.map((item) => {
          this.answerList[index][`fileList${event.name}`].push({
            ...item,
            status: 'uploading',
            message: '上传中'
          });
        });
        for (let i = 0; i < lists.length; i++) {
          const result = await this.uploadFilePromise(lists[i].url);
          let item = this.answerList[index][`fileList${event.name}`][fileListLen];
          this.answerList[index][`fileList${event.name}`].splice(
            fileListLen,
            1,
            Object.assign(item, {
              status: 'success',
              message: '',
              url: result.fileUrl
            })
          );
          fileListLen++;
        }
        this.$emit('addImgae', index, this.answerList[index].fileList[0].url);
      },
      uploadFilePromise(url) {
        let that = this;
        let arrimg = [];
        // that.image=[]
        return new Promise((resolve, reject) => {
          let a = uni.uploadFile({
            url: `${this.baseUrl}zxAdminCourse/common/uploadFile`,
            filePath: url,
            name: 'file',
            formData: {
              user: 'test'
            },
            header: {
              Token: uni.getStorageSync('token')
            },
            success: (res) => {
              setTimeout(() => {
                let data = JSON.parse(res.data);
                resolve(data.data);
              }, 1000);
            }
          });
        });
      },
      goStudyBut(id) {
        this.$parent.goStudyBut(id);
      },
      // 查看原题
      viewQuestionClick(id) {
        this.$parent.viewQuestionClick(id);
      },
      // 删除操作
      deleteQuestion(id) {
        this.$parent.deleteQuestion(id);
      },
      singleQuestionUpdate(id) {
        this.$parent.singleQuestionUpdate(id);
      },
      goExpandImprove(id) {
        this.$parent.$refs.doingExercises.open();
        this.$parent.answerQuestionId = id;
      },
      getUploadImages() {
        return this.answerList
          .map((item) => {
            if (item.fileList && item.fileList.length > 0) {
              return item.fileList[0].url;
            }
            return '';
          })
          .filter((url) => url !== '')
          .join(',');
      }
    }
  };
</script>

<style scoped lang="scss">
  .title {
    display: flex;
    flex-wrap: wrap;
  }
  .uplaodImg {
    width: 167rpx;
    height: 166rpx;
    background: #f7f7f7;
    border-radius: 10rpx;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
  }
  .right {
    border: 1rpx solid #94e6c7 !important;
    background: rgba(148, 230, 199, 0.15) !important;
    color: #31cf93 !important;
  }
  .error {
    border: 1rpx solid #ffaf85 !important;
    background: rgba(255, 172, 129, 0.1) !important;
    color: #ffac80 !important;
  }
  .subjectTest {
    border-radius: 10rpx;
    background-color: #fff;
    padding: 20rpx;
  }
  .imgs {
    display: flex;
    padding: 24rpx;
    background-color: #f7f7f7;
    // height: 300rpx;
    justify-content: space-around;
  }
  .photo {
    height: 271rpx;
    background-color: #f6f6f6;
    padding: 28rpx 0 0 56rpx;
    font-size: 26rpx;
    margin-top: 20rpx;
    color: #999;
  }
  .questions {
    .questionsItem {
      display: flex;
      // flex-wrap: wrap;
      margin: 20rpx auto 18rpx;
      padding: 26rpx 30rpx;
      box-sizing: border-box;
      border: 1rpx solid #dfdfdf;
      border-radius: 10rpx;
      font-size: 28rpx;
      color: #333333;
      margin: 20rpx auto 18rpx;
      width: 650rpx;
      border-radius: 10rpx;
    }
    .questionsTitle {
      margin-top: 20rpx;
      padding: 26rpx 30rpx;
      background-color: #fff5ef;
      color: #999;
    }
    .questionsRight {
      color: #009e74;
      font-weight: bold;
    }
  }
  .grayImageBorder {
    width: 100%;
    height: 100%;
    border: 2rpx solid #f7f7f7;
    box-sizing: border-box;
  }
  .questionType {
    display: inline-block;
    width: 78rpx;
    height: 40rpx;
    line-height: 40rpx;
    border-radius: 8rpx;
  }
  .questionType1 {
    color: #dc0000;
    background: rgba(220, 0, 0, 0.15);
    border: 1rpx solid #dc0000;
  }
  .questionType2 {
    color: #ffa416;
    background: rgba(255, 164, 22, 0.15);
    border: 1rpx solid #ffa416;
  }
  .questionType3 {
    color: #00d1ff;
    background: rgba(0, 186, 255, 0.15);
    border: 1rpx solid #00d1ff;
  }
  .componentMargin {
    margin-left: -20rpx;
    margin-right: -20rpx;
  }
  .question-list-area {
    padding: 0 20rpx 20rpx;
    box-sizing: border-box;
  }
  .container > .question-list-area + .question-list-area {
    margin-top: 20rpx;
  }
  .moment-area {
    border-bottom: 1rpx solid #979797;
  }
  /deep/ .u-cell__body {
    padding: 22rpx 50rpx !important;
  }
  /deep/ .u-cell {
    background: #fbfbfb;
  }
  /deep/ .u-cell__title-text {
    font-size: 28rpx !important;
    color: #428a6f !important;
  }
  /deep/ .u-icon__icon {
    font-size: 32rpx !important;
    color: #428a6f !important;
  }
  /deep/.u-collapse-item__content__text {
    background: #effbf6;
    padding: 5rpx 12rpx 29rpx 47rpx !important;
  }
  .question-btn {
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 20rpx 0;
  }
  .btn-setting {
    border: 2rpx solid #428a6f;
    box-sizing: border-box;
  }
  .btn-setting1 {
    border: 2rpx solid #979797;
    box-sizing: border-box;
  }
  .option {
    width: 626rpx;
    height: 90rpx;
    line-height: 90rpx;
    border-radius: 20rpx;
    padding-left: 33rpx;
    box-sizing: border-box;
    border: 1rpx solid #efeff0;
  }
  .questionsItem.disabled {
    cursor: not-allowed;
    opacity: 0.7;
  }
  .imgs {
    display: flex;
    padding: 24rpx;
    background-color: #f7f7f7;
    // height: 300rpx;
    justify-content: space-around;
    flex-wrap: wrap; /* 允许子元素换行 */
  }
</style>
