<template>
  <view class="funContent">
    <!-- 趣味复习标题 -->
    <view class="interesting_title">
      <image style="width: 500rpx; height: 256rpx" src="https://document.dxznjy.com/applet/interesting/study_logo.png" mode=""></image>
    </view>

    <!-- 四种玩法列表 -->
    <view class="interesting_list">
      <view class="interesting_listContent" v-for="(item, index) in interestingList" :class="item.isNoData ? 'default' : ''" @click="goUrl(item, index)">
        <image class="interesting_listIcon" :src="item.isNoData ? imgHost + item.imgUrlNodata : imgHost + item.imgUrl" mode=""></image>
        <text>{{ item.text }}</text>
        <image class="interet_isChoose" v-if="item.ischoose" :src="imgHost + 'interesting/interesting_selete.png'" mode=""></image>
      </view>
    </view>

    <!-- 查看学情报告 -->
    <!-- <view class="look_history" @click="lookHistory()">
			<text style="color: #3a271b;font-size: 30rpx;">查看学情报告</text>
		</view> -->

    <!-- 温馨提示 -->
    <uni-popup ref="popopPower" type="center" :maskClick="true" :classBG="''">
      <interesting-dialog
        :title="'温馨提示'"
        :isRed="true"
        :isReview="false"
        :isSingle="true"
        :textContent="'您还没有开通使用权限'"
        @nowBuy="nowBuy()"
        @closeDialog="closeDialog()"
      ></interesting-dialog>
    </uni-popup>

    <!-- 选择词库弹窗 -->
    <uni-popup ref="popopChooseWord" type="center" :maskClick="true" :classBG="''" @touchmove.stop.prevent="() => false">
      <view class="interesting_popupCenter chooseWordList_pop">
        <view class="interet_head">
          <image class="interet_popupTitle interet_popupTitle1" :src="imgHost + 'interesting/dialog_title.png'" mode=""></image>
          <text>选择词库</text>
        </view>
        <scroll-view scroll-y class="popup_listContent" style="padding: 0">
          <view class="" style="height: 100%">
            <view class="popup_content popup_listContent">
              <view
                class="chooseWordList"
                v-for="(item, index) in wordList"
                :class="!item.status ? 'boxDefault' : courseWordCurrent == index ? 'active' : ''"
                @click="chooseWordCourse(item, index)"
              >
                <text>{{ item.courseName }}1111</text>
                <!-- <text class="chooseWord_round">Round {{item.nowRound}}</text> -->
              </view>
            </view>
          </view>
        </scroll-view>

        <view class="popupBottom">
          <view class="popBtnGroup">
            <view class="popBtnGroup_list" @click="confirmWord()">确认</view>
            <view class="popBtnGroup_list" @click="closeDialog()">取消</view>
          </view>
        </view>
        <image @click="closeDialog()" class="interesting_close" :src="imgHost + 'interesting/dialog_closed.png'" mode=""></image>
      </view>
    </uni-popup>
  </view>
</template>

<script>
  import interestingHead from '../components/interesting-head/interestingHead.vue';
  import interestingDialog from '../components/interesting-dialog/index.vue';
  import interestingReview from '../components/interesting-dialog/review.vue';

  export default {
    components: {
      interestingHead,
      interestingDialog,
      interestingReview
    },
    data() {
      return {
        imgHost: getApp().globalData.imguseHost,
        titleText: '',
        studentCode: '',
        merchantCode: '',
        courseWordCurrent: -1, //点击课程列表的下标
        courseCode: '',
        gradScreenWheel: -1,
        wordCourse: {
          courseName: '请选择词库'
        }, //当前选择课程的信息
        showData: {
          cos: null,
          levelGroup: 0,
          levelTotalGroup: 0,
          noLearnedWords: true,
          nowGroup: 0,
          nowLevel: 0,
          status: false,
          totalLevel: 0
        }, //需要展示的数据
        interestingList: [
          {
            imgUrl: 'interesting/interesting_item1.png',
            imgUrlNodata: 'interesting/interesting_nodata_item1.png',
            text: '识词冲关',
            interestName: 'Sccg',
            ischoose: false,
            isNoData: false
          },
          {
            imgUrl: 'interesting/interesting_item2.png',
            imgUrlNodata: 'interesting/interesting_nodata_item2.png',
            text: '听音辨意',
            interestName: 'Tyby',
            ischoose: false,
            isNoData: false
          },
          {
            imgUrl: 'interesting/interesting_item3.png',
            imgUrlNodata: 'interesting/interesting_nodata_item3.png',
            text: '连连看',
            interestName: 'Llk',
            ischoose: false,
            isNoData: false
          },
          {
            imgUrl: 'interesting/interesting_item4.png',
            imgUrlNodata: 'interesting/interesting_nodata_item4.png',
            text: '拼拼乐',
            interestName: 'Ppl',
            ischoose: false,
            isNoData: false
          }
        ],
        wordList: []
      };
    },
    onLoad(option) {
      this.$util.fetchFile(1);
      this.$util.fetchFile(0);
      this.studentCode = option.studentCode;
      this.merchantCode = option.merchantCode;
      this.getAbleClick(); //小程序缓存的课程数据
    },
    onShow() {},
    methods: {
      // 是否可以点击
      getAbleClick() {
        this.$httpUser
          .get('znyy/liberty/course/query/play/words', {
            studentCode: this.studentCode,
            play: 1,
            pageNum: 1,
            pageSize: 5
          })
          .then((res) => {
            console.log('1212121212');
            if (!res.data.success) {
              that.$util.alter(res.data.message);
            } else {
              if (!res.data.data.data.length) {
                this.interestingList[0].isNoData = true;
              }
            }
          });
        this.$httpUser
          .get('znyy/liberty/course/query/play/words', {
            studentCode: this.studentCode,
            play: 2,
            pageNum: 1,
            pageSize: 5
          })
          .then((res) => {
            console.log('1212121212');
            if (!res.data.success) {
              that.$util.alter(res.data.message);
            } else {
              if (!res.data.data.data.length) {
                this.interestingList[1].isNoData = true;
              }
            }
          });
        this.$httpUser
          .get('znyy/liberty/course/query/play/words', {
            studentCode: this.studentCode,
            play: 3,
            pageNum: 1,
            pageSize: 5
          })
          .then((res) => {
            console.log('1212121212');
            if (!res.data.success) {
              that.$util.alter(res.data.message);
            } else {
              if (!res.data.data.data.length) {
                this.interestingList[2].isNoData = true;
              }
            }
          });
        this.$httpUser
          .get('znyy/liberty/course/query/play/words', {
            studentCode: this.studentCode,
            play: 4,
            pageNum: 1,
            pageSize: 5
          })
          .then((res) => {
            console.log('1212121212');
            if (!res.data.success) {
              that.$util.alter(res.data.message);
            } else {
              if (!res.data.data.data.length) {
                this.interestingList[3].isNoData = true;
              }
            }
          });
      },

      //识词冲关等页面跳转
      goUrl(ele, index) {
        let that = this;
        // if(that.wordCourse.courseCode==undefined){
        // 	that.$util.alter("请先选择词库哦");
        // 	return
        // }
        if (that.interestingList[index].isNoData) {
          that.$util.alter('当前玩法无可复习单词~');
          return;
        }
        ele.ischoose = true;
        var url = `/pages/study/interesting${ele.interestName}?studentCode=${that.studentCode}&scheduleCode=${that.scheduleCode}`;
        setTimeout(function () {
          ele.ischoose = false;
          uni.redirectTo({
            // url: url + `?params=` + encodeURIComponent(JSON.stringify(that.showData)),
            url: url
          });
        }, 1000);
      },

      //查看学情报告
      lookHistory() {
        let that = this;
        if (this.wordCourse.scheduleCode == undefined) {
          this.$util.alter('请先选择词库哦');
          return;
        }
        that.$util.alter('当前暂无相关学情报告');
        return;

        that.$httpUser.get(`znyy/stats/review/getAnalysisList?scheduleCode=${that.scheduleCode}`).then((res) => {
          if (res.data.success) {
            if (res.data.data.length != 0) {
              // uni.navigateTo({
              // 	url: "/pages/study/pupilReport?scheduleCode="+this.wordCourse.scheduleCode
              // })
            }
          } else {
            that.$util.alter('当前暂无相关学情报告');
          }
        });
      },

      //关闭弹窗
      closeDialog() {
        this.$refs.popopChooseWord.close();
        this.$refs.popopPowerReview.close();
      },

      // 返回上一页
      backPage() {
        console.log('返回上一页');
        // uni.removeStorageSync('wordCourse');
        uni.navigateBack({
          delta: 999
        });
      }
    },
    // 可以在这个周期清除数据，但是隐藏再打开还是这一页，所以暂时不需要 只记录一下
    onHide() {
      console.log('隐藏');
    }
  };
</script>

<style>
  page {
    height: 100vh;
    padding: 0;
  }

  .funContent {
    overflow: hidden;
    width: 100%;
    padding: 0 30rpx;
    box-sizing: border-box;
    /* height: 100%; */
    background: url('https://document.dxznjy.com/applet/interesting/interesting_bg.png') 100% 100% no-repeat;
    background-size: 100% 100%;
  }

  .interesting_title {
    height: 260rpx;
    margin: 90rpx 0 45rpx;
    text-align: center;
  }

  .interesting_surplus {
    text-align: center;
    font-size: 22rpx;
    color: #ffffff;
    letter-spacing: 2rpx;
  }

  .interesting_surplus > view {
    display: inline-block;
    text-align: center;
    padding: 0 26rpx;
    height: 70rpx;
    line-height: 70rpx;
    background-color: rgba(28, 16, 8, 0.8);
    border-radius: 50rpx;
  }

  .interesting_surplus text {
    color: #f57401;
  }

  .interesting_list {
    display: flex;
    flex-wrap: wrap;
    padding: 22rpx 50rpx;
  }

  .interesting_listContent {
    width: 223rpx;
    height: 230rpx;
    margin: 22rpx 37rpx;
    position: relative;
    text-align: center;
    background: url('https://document.dxznjy.com/applet/interesting/interesting_item.png') center no-repeat;
    background-size: 100% 100%;
  }

  .interesting_listContent.default {
    background: url('https://document.dxznjy.com/applet/interesting/interesting_item_bg.png') center no-repeat;
    background-size: 100% 100%;
  }

  .interesting_listIcon {
    width: 80rpx;
    height: 80rpx;
    margin: 36rpx auto 10rpx auto;
  }

  .interesting_listContent text {
    display: block;
    width: 100%;
    color: #411c0c;
  }

  .interesting_listContent.default text {
    color: #a97540;
  }

  .interet_isChoose {
    width: 56rpx;
    height: 56rpx;
    margin-top: 16rpx;
  }

  .look_history {
    width: 417rpx;
    height: 80rpx;
    margin: 30rpx auto;
    letter-spacing: 2rpx;
    line-height: 76rpx;
    font-weight: bold;
    text-align: center;
    background: url('https://document.dxznjy.com/applet/interesting/look-bg.png') 100% 100% no-repeat;
    background-size: 100% 100%;
  }

  .DateContent {
    width: 600rpx;
    border-radius: 20rpx;
  }

  .DateContent .calendar-wrapper {
    border-radius: 20rpx;
    padding-bottom: 30rpx;
  }

  .chooseWordList.boxDefault {
    background: #dddddd;
    border-color: #dddddd;
  }
</style>
