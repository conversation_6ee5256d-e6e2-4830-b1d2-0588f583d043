<template>
  <page-meta :page-style="'overflow:' + (show ? 'hidden' : 'visible')"></page-meta>
  <view class="plr-30 pb-80">
    <view class="pt-30 radius-15 mb-20">
      <view class="plr-20 flex">
        <view class="bold">搜索合伙人:</view>
        <view class="search-css pl-25">
          <u-icon name="search" class="box-50 search-image" color="#CFCFCF" size="60"></u-icon>
          <input class="search-input f-28 c-55" v-model="searchValue" type="text" @blur="blur" placeholder="请输入用户名称......" />
        </view>
      </view>
      <view class="f-28 p-20 mt-45" v-if="listS.data && listS.data.length > 0" style="padding-bottom: 100rpx">
        <view class="radius-16 mb-20" v-for="(item, index) in listS.data" :key="index">
          <view class="bg-ff p-10 sizing">
            <view class="flex-s lh-90" style="line-height: 90rpx; border-bottom: 2rpx solid #f3f4f5">
              <view class="type">
                {{ item.merchantId }}
              </view>
              <view class="f-24 typeStatus t-c">
                <view class="">
                  {{ item.isCheck == 0 ? '未审核' : item.isCheck == 1 ? '审核通过' : '审核驳回' }}
                </view>
                <view class="">
                  {{ item.paymentStatus == 0 ? '未完款' : '已完款' }}
                </view>
              </view>
            </view>
            <view class="" style="border-bottom: 2rpx solid #f3f4f5">
              <view class="mtb-25">合伙人名称: {{ item.merchantName }}</view>
              <view class="">合伙人编号: {{ item.merchantCode }}</view>
              <view class="mtb-25">合伙人手机号: {{ item.phone }}</view>
              <view class="mb-25">合伙人到期时间: {{ item.expireDate == null ? '-' : item.expireDate }}</view>
            </view>
            <view class="flex-a-c flex-x-e f-28 mt-24">
              <view class="button" @tap="examineInfo(index, 3)" :class="item.status === 0 ? 'clickButton' : ''" :key="index" v-if="item.isCheck == 0">审核</view>
              <view class="button ml-30" @tap="updateInfo(index, 0, item.merchantId)" :class="item.status === 0 ? 'clickButton' : ''" :key="index">编辑</view>
              <view class="button mlr-30" @tap="updateClickIndex(index, 1)" :class="item.status === 1 ? 'clickButton' : ''" :key="index" v-if="item.paymentStatus == 0">完款</view>
              <view class="button" @tap="updateClickIndex(index, 2)" :class="item.status === 2 ? 'clickButton' : ''" :key="index" v-if="item.paymentStatus == 0">抵扣</view>
            </view>
          </view>
        </view>
      </view>
      <view v-else class="curriculum_css_no pt-30 pb-55 f-28">
        <image class="curriculum_image" src="https://document.dxznjy.com/course/ac587707bf314badadb28a158852c77d.png"></image>
        <view class="c-66 f-24 mtb-25">暂无明细</view>
      </view>
      <view v-if="no_more && listS.list.length > 0">
        <u-divider text="到底了"></u-divider>
      </view>
    </view>
    <view class="submitButtonParent">
      <button @tap.stop="addPartner" class="f-28 c-ff submitButton">添加合伙人</button>
    </view>

    <uni-popup ref="examine" type="center" @change="change">
      <view class="shareCard">
        <view class="activityRule">审核</view>
        <view class="review_close" @click="closeexamine">
          <uni-icons type="clear" size="26" color="#B1B1B1"></uni-icons>
        </view>
        <view class="rule">
          <radio-group @change="radioChange">
            <label class="radio">
              <radio value="1" checked="true" style="transform: scale(0.7)" />
              通过
            </label>
            <label class="radio">
              <radio value="2" style="transform: scale(0.7)" />
              驳回
            </label>
          </radio-group>
        </view>
        <view class="flex plr-12 sizing">
          <view class="exmaine" style="background-color: darkgray" @click="closeexamine">取消</view>
          <view class="exmaine" style="background-color: #339378" @click="examineed">确认</view>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script>
  const { $http } = require('@/util/methods.js');
  export default {
    data() {
      return {
        showSort: false,
        temp: false,
        searchValue: '', //搜索框
        payStatus: -1,
        listS: {},
        page: 1,
        avaUrl: 'https://document.dxznjy.com/dxSelect/home_avaUrl.png',
        no_more: false,
        useHeight: 0, //除头部之外高度
        imgHost: getApp().globalData.imgsomeHost,
        totalList: '', // 数据总数
        userId: '', // 当前列表id
        userCode: '',
        remark: '',
        showClearIcon: false,
        information: {}, // 当前点击的客户列表
        importShow: false,
        activityList: [],
        clickIndex: -1,
        type: '',
        partnerInvitationCodeNum: '',
        radio: '1',
        shows: false,
        merchantId: ''
      };
    },
    watch: {},
    onLoad(options) {
      console.log(options);
      if (options != null) {
        this.userCode = options.userCode;
      }
    },
    onReady() {
      uni.getSystemInfo({
        success: (res) => {
          // 可使用窗口高度，将px转换rpx
          let h = res.windowHeight * (750 / res.windowWidth);
          this.useHeight = h - 285;
        }
      });
    },
    onShow() {
      this.supermanClublist();
      this.fetchinvitaCode();
    },
    onReachBottom() {
      if (this.page >= this.listS.totalPages) {
        this.no_more = true;
        return false;
      }
      this.supermanClublist(true, ++this.page);
    },
    methods: {
      //获取剩余邀请码
      async fetchinvitaCode() {
        const res = await $http({
          url: 'zxAdminCourse/web/invitation/codeNum',
          data: {
            merchantCode: this.userCode
          }
        });
        if (res) {
          this.partnerInvitationCodeNum = res.data.partnerInvitationCodeNum;
        }
      },
      // 下级合伙人列表
      async supermanClublist(isPage, page) {
        let _this = this;
        uni.showLoading({
          title: '加载中...'
        });
        const res = await $http({
          url: 'zxAdminCourse/web/piMerchant/subMerchantList',
          data: {
            subMerchantType: '1',
            subMerchantCodeName: _this.searchValue,
            pageNum: page || 1,
            pageSize: 10,
            merchantCode: this.userCode
          }
        });
        if (res) {
          _this.totalList = res.data.totalCount;
          if (isPage) {
            let old = _this.listS.data;
            _this.listS.data = [...old, ...res.data.data];
          } else {
            _this.listS = res.data;
            console.log(res.data);
          }
          uni.hideLoading();
        }
      },
      updateClickIndex(index, value) {
        console.log(this.temp, 22222);
        if (this.temp) return;
        console.log(this.temp, 33333);
        this.temp = true;

        this.listS.data.forEach(async (item, idx) => {
          if (idx === index) {
            item.status = value;
            if (value === 2) {
              uni.showLoading({
                title: '抵扣中'
              });
              if (this.partnerInvitationCodeNum > 0) {
                const res = await $http({
                  url: 'zxAdminCourse/web/piMerchant/merchantDeduction',
                  method: 'post',
                  data: {
                    merchantId: item.merchantId
                  }
                });
                uni.hideLoading();
                if (res) {
                  this.temp = false;
                  uni.showToast({
                    title: '抵扣成功',
                    icon: 'success'
                  });
                  setTimeout(() => {
                    this.supermanClublist();
                  }, 500);
                }
              } else {
                this.temp = false;
                uni.hideLoading();
                uni.showToast({
                  title: '无合伙人邀请码，请采购',
                  icon: 'none'
                });
              }
            }
            if (value === 1) {
              uni.showLoading({
                title: '完款中'
              });
              this.$httpUser
                .post('zxAdminCourse/web/piMerchant/merchantPayment', {
                  merchantId: item.merchantId
                })
                .then((res) => {
                  this.temp = false;
                  uni.hideLoading();
                  console.log(res, 11);
                  let orderId = res.data.data.data.order.sourceOrderId;
                  let remark = res.data.data.data.order.remark;
                  let amount = res.data.data.data.order.amount;
                  let codeToken = res.data.data.data.token;
                  uni.navigateTo({
                    url: '/Recharge/Collection/Collection?type=code&codePrice=' + amount + '&orderId=' + orderId + '&remark=' + remark + '&codeToken=' + codeToken
                  });
                });
            }
          } else {
            item.status = null;
          }
        });
      },
      examineInfo(index, value) {
        this.listS.data.forEach((item, idx) => {
          if (idx === index) {
            item.status = value;
            this.merchantId = item.merchantId;
            this.$refs.examine.open();
          } else {
            item.status = null;
          }
        });
      },
      examineed() {
        this.$httpUser
          .post('zxAdminCourse/web/piMerchant/merchantAudit', {
            merchantId: this.merchantId,
            isCheck: this.radio
          })
          .then((res) => {
            if (res.data.status == 1) {
              uni.showToast({
                title: res.data.message,
                icon: 'success'
              });
              this.supermanClublist();
              this.$refs.examine.close();
              this.$forceUpdate();
            } else {
              uni.showToast({
                title: res.data.message,
                icon: 'success'
              });
            }
          });
      },
      closeexamine() {
        this.$refs.examine.close();
      },
      radioChange(val) {
        this.radio = val.target.value;
      },
      updateInfo(index, value, id) {
        uni.navigateTo({
          url: '/clubApplication/addPartner?type=2&id=' + id
        });
      },
      addPartner() {
        uni.navigateTo({
          url: '/clubApplication/addPartner?type=2' + '&merchantType=1&userCode=' + this.userCode
        });
      },
      //弹窗穿透
      change(e) {
        this.show = e.show;
      },
      blur(e) {
        this.searchValue = e.detail.value;
        this.page = 1;
        this.supermanClublist();
      }
    }
  };
</script>

<style lang="scss" scoped>
  .search {
    position: fixed;
    top: 0;
    width: 690rpx;
  }

  /* 搜索框 */
  .search-css {
    display: flex;
    margin-left: 20rpx;
    flex: 1;
    line-height: 80rpx;
    background-color: #fff;

    .search-image {
      vertical-align: middle;
    }

    .search-input {
      display: inline-block;
      height: 80rpx;
      line-height: 80rpx;
      vertical-align: middle;
    }
  }

  .img_s {
    width: 160rpx;
    height: 160rpx;
  }

  .input {
    border: 1px solid #c8c8c8;
    border-radius: 50rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .typeStatus {
    display: flex;
    line-height: 40rpx;
    background: rgba(255, 221, 167, 0.15);
    border-radius: 8rpx;
    border: 2rpx solid #ffdda7;
    color: #fd9b2a;
  }

  .button {
    width: 120rpx;
    line-height: 60rpx;
    text-align: center;
    border-radius: 60rpx;
    color: #4e9f87;
    border: 1px solid #4e9f87;
  }

  .clickButton {
    background-color: #4e9f87;
    color: #fff;
  }

  .submitButtonParent {
    width: 100vw;
    height: 100rpx;
    background-color: #fff;
    position: fixed;
    left: 0;
    bottom: 0;
    z-index: 1;
  }

  .submitButton {
    width: 686rpx;
    line-height: 74rpx;
    margin: 0 auto;
    background: #339378;
    border-radius: 38rpx;
  }

  .curriculum_css_no {
    position: relative;
    width: 710rpx;
    margin: auto;
    text-align: center;

    .curriculum_image {
      width: 74rpx;
      height: 76rpx;
      display: block;
      margin: 16rpx auto;
    }

    .curriculum_title {
      text-align: left;
    }
  }
  // 活动规则弹窗
  .activityRule {
    text-align: center;
    font-weight: bold;
    position: absolute;
    top: 40rpx;
    left: 0;
    width: 100%;
    background: white;
    z-index: 10;
  }

  /*分享弹窗样式*/
  .shareCard {
    position: relative;
    background: #ffffff;
    color: #000;
    padding-top: 50upx;
    box-sizing: border-box;
    overflow: hidden;
    width: 90vw;
  }

  .rule {
    padding: 20rpx;
    line-height: 50rpx;
    margin-top: 60rpx;
  }

  .review_close {
    position: absolute;
    /* 固定在右上角 */
    top: 40rpx;
    right: 20rpx;
    z-index: 10;
    /* 确保在上层 */
  }
  .exmaine {
    width: 45%;
    line-height: 74rpx;
    margin-bottom: 20rpx;
    border-radius: 38rpx;
    color: #fff;
    text-align: center;
  }
</style>
