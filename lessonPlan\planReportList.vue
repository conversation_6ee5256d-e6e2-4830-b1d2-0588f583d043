<!-- 学习打印页面 -->
<template>
  <view class="study_print_page">
    <!--    <view class="flex-a-c nav-bar plr-20 mt-40">
      <uni-icons type="left" size="24" @click="returnBack"></uni-icons>
      <view class="nav_title f-40">AI课程规划单报告</view>
    </view> -->
    <view class="bg_box"></view>
    <!-- 学员选择框 -->
    <view class="list_box">
      <view class="student_pick row_flex">
        <view>学员编号：</view>
        <view class="pick pl-20">
          <input type="number" class="form-input" @blur="getDataList" v-model="studentCode" />
        </view>
      </view>
    </view>
    <!-- 学员选择框 -->

    <!-- 下部内容 -->
    <view v-if="detectionList.length > 0">
      <view v-for="(item, index) in detectionList" :key="index">
        <view class="subject p-30" @click="reportFn(item)">
          <view>
            <view class="title">
              <text>姓名：{{ item.studentName || '' }}</text>
            </view>
            <view class="title">
              <text>规划时间：{{ item.createTime || '' }}</text>
            </view>
          </view>
          <view class="imgBox">
            <image src="../static/index/arrow.png" class="arrow" mode="widthFix"></image>
          </view>
        </view>
      </view>
    </view>

    <view v-else class="t-c flex-col mt-30 bg-ff radius-15 mlr-30" :style="{ height: useHeight + 'rpx' }">
      <image src="https://document.dxznjy.com/alading/correcting/no_data.png" class="mb-20" style="width: 160rpx" mode="widthFix"></image>
      <view style="color: #bdbdbd">暂无数据</view>
    </view>
  </view>
</template>

<script>
  import { uniqueObjectsByPropertyMap } from '@/util/util.js';
  export default {
    data() {
      return {
        studentCode: '',
        studentIndex: 0,
        studentArray: [
          {
            studentCode: '',
            realName: '请选择学员'
          }
        ],
        detectionList: [], // 检测列表
        pageNum: 1, // 当前页
        pageSize: 20, // 页数
        loadingType: 'nodata', //加载更多状态
        urlId: '', //跳转带过去的当前id
        merchantCode: '',
        useHeight: 0, //除头部之外高度
        app: 0,
        startX: 0,
        startY: 0,
        threshold: 80, // 触发滑动的最小距离（px）
        edgeThreshold: 30 // 屏幕边缘触发范围
      };
    },
    onReady() {
      uni.getSystemInfo({
        success: (res) => {
          // 可使用窗口高度，将px转换rpx
          let h = res.windowHeight * (750 / res.windowWidth);
          this.useHeight = h - 560;
        }
      });
    },
    onLoad(option) {
      // uni.setStorageSync(
      //   'token',
      //   '****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************'
      // );
      // this.merchantCode = '8240928796';
      // #ifdef APP-PLUS
      if (option != null) {
        this.app = option.app;
        this.merchantCode = option.merchantCode;
        this.$handleTokenFormNative(option);
      }
      // #endif
    },
    onUnload() {
      // #ifdef APP-PLUS
      if (this.app) {
        plus.runtime.quit();
      }
      // #endif
    },
    onShow() {
      this.getDataList();
    },

    //加载更多
    onReachBottom() {
      if (this.studentCode != '') {
        this.pageNum++;
        // this.getDataList();
      }
    },
    onBackPress() {
      this.returnBack();
      return true; // 阻止默认行为
    },
    methods: {
      // 获取学员信息内容
      async getDataList(change) {
        let that = this;
        // let res2 = await that.$httpUser.get('znyy/deliver/student/coursePage/' + this.pageNum + '/' + this
        let result = await that.$httpUser
          .get('znyy/wap/student-lesson-plan/ai-report-list', {
            studentCode: this.studentCode,
            merchantCode: this.merchantCode
          })
          .then((result) => {
            if (result) {
              var list = result.data.data;
              if (!list || !Array.isArray(list) || list.length === 0) {
                that.loadingType = 'nodata';
                uni.showToast({
                  icon: 'none',
                  title: '暂无更多内容了！',
                  duration: 2000
                });
                that.detectionList = [];
              } else {
                that.detectionList = result.data.data;
                that.loadingType = that.pageNum >= Number(result.data.data.totalPage) ? 'nomore' : 'more';
              }
            }
          });
      },

      // 跳转到报告页面
      reportFn(item) {
        uni.navigateTo({
          url: `/parentEnd/report/aiLessonPlanReport?id=${item.planCode}&isBack=true&studentCode=${item.studentCode}&merchantCode=${item.merchantCode}`
        });
      },
      returnBack() {
        plus.runtime.quit();
      }
    }
  };
</script>

<style lang="scss">
  .study_print_page {
    background-color: #f3f8fc;
    padding-bottom: 60rpx;
  }
  .nav-bar {
    // position: fixed;
    background-color: #f3f8fc;
    width: 100%;
    height: 120rpx;
    padding-top: 30rpx;
    z-index: 9;
  }

  .nav_title {
    margin-left: 25%;
  }

  .bg_box {
    width: 690rpx;
    height: 312rpx;
    margin: 0 auto;
    background-image: url('https://document.dxznjy.com/dxSelect/8e90eadf-3d59-4130-b00f-cfe5f9339540.png');
    background-size: cover;
    background-repeat: no-repeat;
  }

  .list_box {
    margin: 30upx 30upx 0upx 26upx;
    border-radius: 14upx;
    background: #ffffff;
    padding: 40upx;
    font-size: 26upx;
    color: rgba(102, 102, 102, 1);
    position: relative;
  }

  .row_flex {
    display: flex;
    align-items: center;
    /* 		justify-content: center; */
  }

  .pick {
    position: relative;
    width: 490upx;
    height: 60upx;
    line-height: 60upx;
    border: 1px solid rgba(199, 199, 199, 1);
    border-radius: 12upx;
    box-sizing: border-box;
  }
  .form-input {
    flex: 1;
    height: 100%;
    font-size: 28rpx;
    border: none;
    background: transparent;
  }

  .jiantou {
    position: absolute;
    right: 15upx;
    top: 15upx;
  }

  // 下面内容
  .subject {
    margin: 30rpx;
    width: 630rpx;
    display: flex;
    justify-content: space-between;
    line-height: 70rpx;
    background: #ffffff;
    border-radius: 8rpx;
    font-size: 28rpx;
    font-family: AlibabaPuHuiTiR;
    color: #555;
  }
  .imgBox {
    align-self: center;
  }
</style>