<template>
	<view class="plr-30">
		<view class="bg-ff radius-15 ptb-60 t-c">
			<image v-if="withdrawalList.verifyResult==0" class="img" :src="imgHost+'dxSelect/secondPhase/afoot.png'"></image>
			<image v-if="withdrawalList.verifyResult==1" class="img" :src="imgHost+'dxSelect/secondPhase/success.png'"></image>
			<image v-if="withdrawalList.verifyResult==2" class="img" :src="imgHost+'dxSelect/secondPhase/error.png'"></image>
			<view class="mt-12 f-32">{{withdrawalList.verifyResult!=0?(withdrawalList.verifyResult==1?'提现成功':'提现失败'):'处理中'}}</view>
			<view class="money mt-20 f-34 bold">¥{{withdrawalList.withdrawAmount}}</view>
		</view>
		<view class="bg-ff radius-15 mt-30 plr-30 pb-30 f-30 c-33" :style="{height: useHeight+'rpx'}">
			<view class="flex_s">
				<text class="mt-40">当前状态：</text>
				<view class="steps">
					<view class="out-box">
						<view class="self-box mb-50 wh78">
							<view class="line"></view>
							<uni-icons type="smallcircle-filled" size="18" color="#CFCFCF" style="margin-top: 10rpx;"></uni-icons>
							<!-- <text :class="{ currentFontColor: currentStep >= 0 }" class="ml-15">步骤1</text> -->
							<view class="mt-50 ml-15 c-66">
								<view>发起提现</view>
								<view class="mt-10 f-26">{{withdrawalList.createdTime}}</view>
							</view>
						</view>
						<view class="self-box mb-50 wh78" v-if="withdrawalList.verifyResult==0">
							<view class="line"></view>
							<image :src="imgHost + 'dxSelect/secondPhase/afoot_icon.png'"/>
							<view class="mt-50 ml-15">
								<view>银行处理中</view>
								<view class="mt-10 f-26 c-66">{{withdrawalList.checkTime}}</view>
							</view>
						</view>
						<view class="self-box mb-50 wh78" v-if="withdrawalList.verifyResult!=0">
							<view class="line"></view>
							<image :src="imgHost + 'dxSelect/secondPhase/afoot_icon2.png'"/>
							<view class="mt-50 ml-15">
								<view>银行处理中</view>
								<view class="mt-10 f-26 c-66">{{withdrawalList.checkTime}}</view>
							</view>
						</view>
						<view class="self-box wh78" v-if="withdrawalList.verifyResult==2">
							<image :src="imgHost + 'dxSelect/secondPhase/error_icon.png'"/>
							<view class="mt-50 ml-15">
								<view>提现失败</view>
								<view class="mt-10 f-26 c-66">{{withdrawalList.updatedTime}}</view>
							</view>
						</view>
						<view class="self-box wh78" v-if="withdrawalList.verifyResult==1">
							<image :src="imgHost + 'dxSelect/secondPhase/success_icon.png'"/>
							<view class="mt-50 ml-15">
								<view>提现成功</view>
								<view class="mt-10 f-26 c-66">{{withdrawalList.updatedTime}}</view>
							</view>
						</view>
					</view>
				</view>
			</view>
			<view class="mt-30">提现金额：￥{{withdrawalList.withdrawAmount}}</view>
			<view class="mt-30" v-if="withdrawalList.verifyResult!=0">手续费：¥{{withdrawalList.fee}}</view>
			<view class="mt-30">申请时间：{{withdrawalList.createdTime}}</view>
			<view class="mt-30" v-if="withdrawalList.verifyResult==1">到账时间：{{withdrawalList.updatedTime}}</view>
			<view class="mt-30">银行卡号：{{withdrawalList.bankCard}}</view>
			<view class="mt-30">提现单号：{{withdrawalList.withdrawId}}</view>
			<view class="mt-30" v-if="withdrawalList.verifyResult==2">失败原因：{{withdrawalList.errorMessage}}</view>
			
		</view>
	</view>
</template>

<script>
	const { $navigationTo, $http } = require("@/util/methods.js")
	export default {
		data() {
			return {
				imgHost: getApp().globalData.imgsomeHost,
				useHeight: 0, //除头部之外高度
				list2: [{
					title: '买家下单',
					desc: '2018-11-11'
				}, {
					title: '卖家发货',
					desc: '2018-11-12'
				}, {
					title: '买家签收',
					desc: '2018-11-13'
				}, {
					title: '交易完成',
					desc: '2018-11-14'
				}],

				currentStep: 0, //当前步骤
				withdrawId:"", // 详情id
				withdrawalList:{}, // 提现详情
			};
		},
		onReady() {
			uni.getSystemInfo({
				success: (res) => {
					// 可使用窗口高度，将px转换rpx
					let h = (res.windowHeight * (750 / res.windowWidth));
					this.useHeight = h - 440;
				}
			})
		},
		onShow() {
			this.withdrawalDetails()
		},
		onLoad(e) {
			this.withdrawId = e.id;
		},
		methods: {
			// 获取当前进程的图片
			currentIcon(data) {
				if (data === 1) { 
					return this.imgHost + 'dxSelect/secondPhase/afoot_icon.png'
				} else if (data === 2) { 
					return this.imgHost + 'dxSelect/secondPhase/error_icon.png'
				}
			},
			
			// 提现详情
			async withdrawalDetails() {
				let _this = this;
				const res = await $http({
					url: 'zx/user/userWithdrawApplyDetail',
					data: {
						withdrawId:_this.withdrawId
					}
				})
				if (res) {
					console.log(res);
					_this.withdrawalList = res.data;
					console.log(_this.withdrawalList);
				}
			},
		}
	}
</script>

<style lang="scss" scoped>
	.money {
		color: #FA370E;
	}

	.img {
		width: 100rpx;
		height: 100rpx;
	}

	.flex_s {
		display: flex;
		align-items: flex-start;
	}

	/deep/ .u-text__value {
		font-size: 28rpx !important;
	}

	/deep/ .u-text__value--tips {
		margin-top: 15rpx !important;
		padding-bottom: 70rpx;
	}

	/deep/ .u-steps-item__line {
		height: 200rpx !important;
	}
	.steps {
		// height: 196rpx;
		background-color: #fff;
		// padding: 40rpx;

		.out-box {
			flex-direction: column;
			align-items: center;

			.self-box {
				position: relative;
				// width: 590rpx;
				height: 120rpx;
				display: flex;
				// flex-direction: column;
				align-items: center;

				.line {
					width: 2rpx;
					height: 120rpx;
					top: 40rpx;
					left: 12rpx;
					background: #dedede;
					position: absolute;
					margin-top: 45rpx;
					margin-left: 6rpx;
				}

				image {
					width: 28rpx;
					height: 28rpx;
					margin-left: 6rpx;
				}

				text {
					// margin-top: 20rpx;
					font-size: 28rpx;
					color: #909399;
				}

				.currentFontColor {
					color: #409eff;
				}

				.currentLineColor {
					background-color: #409eff;
				}
			}
		}
	}
</style>
