<template>
	<view class="plr-30">
		<view class="p-30 f-30 bg-ff radius-15 positionRelative" :style="{height: detailsList.type!=5?useHeight-20 +'rpx':''}">
			<view class="flex_s">
				<view>
					<image :src="detailsList.headPortrait == ''? avaUrl : detailsList.headPortrait" class="img_s"></image>
				</view>
				
				<view class="ml-20 w100">
					<view class="f-32 flex-s">
						<view class="bold">{{detailsList.merchantName || ''}}</view>
						<view v-if="detailsList.gradeLevel==4" :class="detailsList.type==5?'grade':'grade_s'" class="grade plr-15 ptb-5">{{detailsList.applyType!=0?(detailsList.applyType==1?'申请B1俱乐部':'升级B1俱乐部'):'B1俱乐部'}}</view>
						<view v-if="detailsList.gradeLevel==5" :class="detailsList.type==5?'grade':'grade_s'" class="grade plr-15 ptb-5">{{detailsList.applyType!=0?(detailsList.applyType==1?'申请B2俱乐部':'升级B2俱乐部'):'B2俱乐部'}}</view>
						<view v-if="detailsList.gradeLevel==6" :class="detailsList.type==5?'grade':'grade_s'" class="grade plr-15 ptb-5">{{detailsList.applyType!=0?(detailsList.applyType==1?'申请B3俱乐部':'升级B3俱乐部'):'B3俱乐部'}}</view>
					</view>
					
					<view class="c-66 mt-15">{{detailsList.createdTime}}</view>
					
					<view class="c-66 mt-15" :class="detailsList.type!=5?'pb-30':''">
						联系方式：{{detailsList.mobile || ''}}
					</view>
				</view>
			</view>
			
			<view v-if="detailsList.type!=5">
				<view class="border_t c-66 flex-x mb-30 pt-30">
					<view class="w50">本次升级所需超人码：</view>
					<view>{{detailsList.needCodeNum || 0}}</view>
				</view>
				<view class="border_t c-66 flex-x mb-30 pt-30">
					<view class="w50">您剩余超人码：</view>
					<view>{{detailsList.haveCodeNum || 0}}</view>
				</view>
				<view v-if="detailsList.type==1" class="border_t c-66 pt-30 lh-44 f-26">
					{{detailsList.merchantName}}升级成为{{detailsList.gradeLevel!=4?(detailsList.gradeLevel==5?'B2俱乐部':'B3俱乐部'):'B1俱乐部'}}，升级后等级和您的等级一致，
					您需要升为{{gradeLevel==5?'B2俱乐部':'B3俱乐部'}}才可继续享有{{detailsList.merchantName}}带来的后续收益。
				</view>
				
				<view v-if="detailsList.type==2" class="border_t c-66 pt-30 lh-44 f-26 mt-30">
					<view class="time">
						<view>{{detailsList.merchantName}}申请升级成为{{detailsList.gradeLevel!=4?(detailsList.gradeLevel==5?'B2俱乐部':'B3俱乐部'):'B1俱乐部'}}，由于您的超</view>
						<view class="flex-x">人码不足请在<u-count-down :time="countdown" format="HH:mm:ss"></u-count-down>内完成超人码的申购</view>
					</view>
				</view>
				<view v-if="detailsList.type==2" class="prompt mt-20 f-28">
					若您超时未处理或者暂不进货，则系统会把该客户归到您的上级
				</view>
				
				<view v-if="detailsList.type==3" class="border_t c-66 pt-30 lh-44 f-26 mt-30">
					<view class="c-fea">请在确认完款后，再点击通过！</view>
				</view>
				<!-- <view v-if="detailsList.type==3" class="prompt mt-20 f-28">
					超时未处理时如果您的超人码不足，则系统会转为把该客户转到您上级下
				</view> -->
			</view>
			
			
			<view v-if="detailsList.type==1" class="flex-s positionAbsolute" style="bottom: 30rpx;width: 91.5%;">
				<view class="give_up mr-25" @click="goback">暂不升级</view>
				<view class="count_down flex-c f-26" @click="skintap('supermanClub/superman/superman?type=4')">
					<view>我要升级</view><view class="ml-5">(</view>
					<u-count-down :time="countdown" format="HH:mm:ss"></u-count-down><view>内完成升级 )</view>
				</view>
			</view>
			
			<view v-if="detailsList.type==2" class="flex-s positionAbsolute" style="bottom: 30rpx;width: 91.5%;">
				<view class="give_up mr-25" @click="goback">暂不进货</view>
				<view class="count_down flex-c f-26" @click="skintap('supermanClub/supermanSign/sign')">
					<view>我要进货</view><view class="ml-5">(</view>
					<u-count-down :time="countdown" format="HH:mm:ss"></u-count-down><view>内完成进货 )</view>
				</view>
			</view>
			
			<view v-if="detailsList.type==3" class="positionAbsolute" style="bottom: 30rpx;width: 91.5%;">
				<view class="count_down f-28" @click="openPopup">
					<view>通过</view>
				</view>
			</view>
		</view>
		
		<!-- <view v-if="detailsList.type==5" class="bg-ff radius-15 mt-30 pr-30 pt-30 pl-50">
			<u-steps current="1" direction="column" activeColor="#1D755C" inactiveColor="#C6C6C6" :dot="true">
				<u-steps-item :title="detailsList.gradeLevel!=4?(detailsList.gradeLevel==5?'成为鼎校甄选B2俱乐部':'成为鼎校甄选B3俱乐部'):'成为鼎校甄选B1俱乐部'" 
				:desc="detailsList.merchantTime"></u-steps-item>
				<u-steps-item v-if="detailsList.mealTime!=''" title="升级为鼎校甄选学习超人" :desc="detailsList.mealTime || 0"></u-steps-item>
				<u-steps-item title="成为鼎校甄选家长" :desc="detailsList.memberTime || 0"></u-steps-item>
			</u-steps>
		</view> -->
		
		<view v-if="detailsList.type==5" class="bg-ff radius-15 mt-30 pr-30 pl-50" :style="{height: useHeight-200 +'rpx'}">
			<scroll-view class="steps mt-30" :scroll-top="scrollTop" scroll-y="true">
				<view class="out-box">
					<view class="list_content">
						<view class="self-box marginB140 wh78" v-for="(item,index) in detailsList.changeRecordList" :key="index" @click="add(item)">
							<view class="line"></view>
							<view class="icon_round"></view>
							<view class="ml-60 mt-10">
								<view v-if="item.type==1||item.type==2">{{item.gradeLevel!=4?(item.gradeLevel==5?'申请鼎校甄选B2俱乐部':'申请鼎校甄选B3俱乐部'):'申请鼎校甄选B1俱乐部'}}</view>
								<view v-if="item.type!=1&&item.type!=2">{{item.gradeLevel!=4?(item.gradeLevel==5?'成为鼎校甄选B2俱乐部':'成为鼎校甄选B3俱乐部'):'成为鼎校甄选B1俱乐部'}}</view>
								<view class="c-66 mt-10 f-26">{{item.createdTime}}</view>
								<view class="c-66 f-28 mt-15" v-if="item.type==1">由于您等级不足,该俱乐部已转到其他上级俱乐部下级<text v-if="detailsList.isFirstParent==0" class="prompt-subordinate">,若您升级的等级大于该俱乐部等级，可再次获得该俱乐部分润</text></view>
							    <view class="c-66 f-28 mt-15" v-if="item.type==2">由于您的等级没有充足的超人码，该俱乐部已转到其他上级俱乐部下</view>
								<view class="c-fea f-28 mt-15" v-if="item.type==4">由于该俱乐部上级等级不足，找到您为新的上级，您成为{{detailsList.merchantName}}新的上级</view>
								<view class="c-fea f-28 mt-15" v-if="item.type==5">由于该俱乐部上级超人码不足，找到您为新的上级，您成为{{detailsList.merchantName}}新的上级</view>
								<view class="c-fea f-28 mt-15" v-if="item.type==6">由于您的等级已升级，该俱乐部已重回到您的下级，开始分润</view>
								<view class="c-fea f-28 mt-15" v-if="item.type==7">由于该俱乐部之前的上级已升级，该俱乐部重回原上级，暂无分润</view>
								<view class="c-fea f-28 mt-15" v-if="item.type==8">由于该俱乐部上级等级不足，上级的上级超人码不足，找到您为新的上级，您成为{{detailsList.merchantName}}新的上级</view>
								<view class="c-fea f-28 mt-15" v-if="item.type==9">{{item.createdTime}},平台将您更改为{{detailsList.merchantName}}的上级</view>
								<view class="c-fea f-28 mt-15" v-if="item.type==10">由于平台更改了该俱乐部的上级，目前暂不分润</view>
								<view class="c-fea f-28 mt-15" v-if="item.type==12">由于该俱乐部升级为B3 则该俱乐部归属总部</view>
								<view class="c-fea f-28 mt-15" v-if="item.type==13">由于该俱乐部升级为B3 您成为该俱乐部新上级</view>
							</view>
						</view>
					</view>
					
					
					<view class="self-box marginB140 wh78" v-if="detailsList.mealTime!=''">
						<view class="line" style="color: #2E896F;"></view>
						<view class="icon_round" :class="detailsList.changeRecordList.length!=0?'icon_round':'round_icon'"></view>
						<view class="ml-60 mt-10">
							<view>成为鼎校甄选学习超人</view>
							<view class="mt-10 f-26 c-66">{{detailsList.mealTime}}</view>
						</view>
					</view>
					
					<view class="self-box wh78">
						<view class="icon_round"></view>
						<view class="ml-60 mt-10">
							<view>成为鼎校甄选家长</view>
							<view class="mt-10 f-26 c-66">{{detailsList.memberTime}}</view>
						</view>
					</view>
				</view>
			</scroll-view>
		
		</view>
	
	  
		<!-- 联系家长 -->
	    <uni-popup ref="contactPopup" type="center">
	    	<view class="dialogBG">
	    		<view class="reviewCard_box positionRelative">
	    			<view class="">
	    				<image :src="dialog_iconUrl" class="cartoom_image"></image>
	    			</view>
	    			<view class="reviewCard positionRelative">
						<view v-if="detailsList.type==1 || detailsList.type==2" class="close-icon">
							<uni-icons type="clear" size="30" color="#b1b1b1" @click="closeDialog"></uni-icons>
						</view>
						<view v-if="detailsList.type==1 || detailsList.type==2" class="t-c mt-20">
							<image :src="imgHost+'dxSelect/three/icon/prompt-icon.png'" class="prompt-img"></image>
						</view>
						<view v-if="detailsList.type==3" class="t-c">
							<u-icon name="checkmark-circle-fill" color="#2DC032" size="136"></u-icon>
						</view>
	    				<view v-if="detailsList.type==1" class="reviewTitles">是否确定暂不升级？</view>
	    				<view v-if="detailsList.type==2" class="reviewTitles">是否确定暂不进货？</view>
	    				<view v-if="detailsList.type==3" class="f-32 mt-30 t-c lh-50">您已通过{{detailsList.merchantName}}超人俱乐部的申请，已成为您的下级</view>
	    				<view class="flex-s mt-55" v-if="detailsList.type==1 || detailsList.type==2" >
	    					<view class="review_btn" @click="contactType">确定</view>
	    					<view class="close_btn" @click="closeDialog">取消</view>
	    				</view>
	    			</view>
	    		</view>
	    	</view>
	    </uni-popup>
		
		<!-- 申請成功等待 -->
		<uni-popup ref="popup" type="center" @change="changePopup">
			<view class="t-c bg-ff content positionRelative">
				<view class="close-icon">
					<uni-icons type="clear" size="30" color="#b1b1b1" @click="closeDialog"></uni-icons>
				</view>
				<image :src="imgHost+'dxSelect/three/icon/progress-icon.png'" class="image_class"></image>
				<view class="mt-20 plr-80 lh-50">您进货的200个超人码申请正在等待上级的处理！</view>
			</view>
		</uni-popup>
		
		
		<!-- 通过提醒 -->
		<uni-popup ref="remindPopup" type="center">
			<view class="dialogBG">
				<view class="reviewCard_box positionRelative">
					<view class="">
						<image :src="dialog_iconUrl" class="cartoom_image"></image>
					</view>
					<view class="reviewCard positionRelative">
						<view class="close-icon">
							<uni-icons type="clear" size="30" color="#b1b1b1" @click="closeDialog"></uni-icons>
						</view>
						<view class="t-c bold f-34">
							提示
							<!-- <image :src="imgHost+'dxSelect/three/icon/prompt-icon.png'" class="prompt-img"></image> -->
						</view>
						
						<view class="f-32 mtb-40 t-c">是否确定通过该俱乐部的申请？</view>
						
						<view class="flex-s mt-55">
							<view class="review_btn" @click="getWithdraw">确定</view>
							<view class="close_btn" @click="closeDialog">取消</view>
						</view>
					</view>
				</view>
			</view>
		</uni-popup>
	</view>
</template>

<script>
    import Util from '@/util/util.js'
	const { $navigationTo, $http } = require("@/util/methods.js")
	import dayjs from "dayjs"
	export default {
		data() {
			return {
				detailHeight:100, 
				useHeight: 0,     //除头部之外高度
				merchantCode:"",  // 俱乐部编号
				detailsList:{},   //申请详情
				avaUrl: Util.getCachedPic("https://document.dxznjy.com/dxSelect/home_avaUrl.png","home_avaUrl_path"),
				countdown:"",     // 倒计时
				gradeLevel:"",    // 需要升级的等级
				
				currentStep: 0, //当前步骤
				imgHost: getApp().globalData.imgsomeHost,
                
                dialog_iconUrl:Util.getCachedPic("https://document.dxznjy.com/dxSelect/dialog_icon.png","dialog_icon_path"),
			};
		},
		onLoad(e) {
			this.merchantCode = e.merchantCode;
		},
		onShow() {
			this.getDetails();
			// this.getCountdown();
			// this.$refs.remindPopup.open();
			// this.$refs.popup.open();
		},
		
		onReady() {
		    uni.getSystemInfo({
		        success: (res) => {
		            // 可使用窗口高度，将px转换rpx
		            let h = (res.windowHeight * (750 / res.windowWidth));
		            this.useHeight = h - 80;
		        }
		    })
		},
		methods:{
			add(item){
				debugger
				console.log(item);
			},
			openPopup(){
				this.$refs.remindPopup.open();
			},
			
			getWithdraw(){
				this.$refs.remindPopup.close();
				this.clubApply();
			},
			// 申请详情
			async getDetails(){
				let _this = this
				const res = await $http({
					url: 'zx/merchant/getMerchantApplyUpDetail',
					data: {
						merchantCode: this.merchantCode
					}
				})
				if (res) {
					_this.detailsList=res.data;
					_this.getCountdown(_this.detailsList.expireTime);
					if(_this.detailsList.type==1){
						if(_this.detailsList.gradeLevel!==6){
							_this.gradeLevel=_this.detailsList.gradeLevel+1;
						}else{
							_this.gradeLevel=_this.detailsList.gradeLevel;
						}
					}
				}
			},
			// 倒计时处理
			getCountdown(time){
				let dateTime = dayjs().unix() * 1000;
				console.log(dateTime);
				let setTime = dayjs(time).unix() * 1000;
				console.log(setTime);
				this.countdown = setTime - dateTime;
			},
			// 通过俱乐部申请或升级
			async clubApply(){
				let _this = this
				uni.showLoading();
				const res = await $http({
					url: 'zx/merchant/auditMerchantApplyUp',
					data: {
						merchantCode: _this.merchantCode
					}
				})
				uni.hideLoading();
				if (res) {
					_this.$refs.contactPopup.open();
					setTimeout(() => {
						_this.$refs.contactPopup.close();
					}, 1500)
					
					setTimeout(()=>{
						uni.navigateBack()
					},2000)
				}else{
					_this.$util.alter('操作失败啦，请稍后再试');
				}
			},

			// 确定升级
			// async confirmUpgrade(){
			// 	let _this = this;
			// 	if(_this.detailsList.gradeLevel!==6){
			// 		_this.gradeLevel=_this.detailsList.gradeLevel+1;
			// 	}else{
			// 		_this.gradeLevel=_this.detailsList.gradeLevel;
			// 	}
			// 	let param = {
			// 		"auditGradeLevel": _this.gradeLevel
			// 	}
			// 	const res = await $http({
			// 		url: 'zx/merchant/upMerchant',
			// 		method: 'POST',
			// 		data: param
			// 	})
			// 	uni.hideLoading();
			// 	if(res){
			// 		_this.$util.alter('升级成功');
			// 		setTimeout(()=>{
			// 			uni.navigateBack()
			// 		},2000)
			// 	}
			// },
			
			// 暂不进货
			async cancelPurchase(){
				let _this = this;
				uni.showLoading();
				const res = await $http({
					url: 'zx/merchant/notOutCode',
					data: {
						merchantCode: _this.merchantCode
					}
				})
				uni.hideLoading();
				if (res) {
					_this.$refs.contactPopup.close();
					setTimeout(() => {
						_this.$refs.contactPopup.close();
					}, 1500);
					setTimeout(()=>{
						uni.navigateBack();
					},2000)
				}else{
					_this.$util.alter('操作失败啦，请稍后再试');
				}
			},
			
			// 暂不升级
			async cancelUpgrade(){
				let _this = this;
				uni.showLoading();
				const res = await $http({
					url: 'zx/merchant/notUpMerchant',
					data: {
						merchantCode: _this.merchantCode
					}
				})
				uni.hideLoading();
				if (res) {
					_this.$refs.contactPopup.close();
					setTimeout(() => {
						_this.$refs.contactPopup.close();
					}, 1500);
					setTimeout(()=>{
						uni.navigateBack();
					},2000)
				}else{
					_this.$util.alter('操作失败啦，请稍后再试');
				}
			},
			
			skintap(url) {
				$navigationTo(url)
			},
			
			goback(){
				this.$refs.contactPopup.open();
			},
			
			contactType() {
				if(this.detailsList.type==2){
					this.cancelPurchase()
				}else if(this.detailsList.type==1){
					this.cancelUpgrade()
				}
			},
			
			//关闭弹窗
			closeDialog() {
				this.$refs.contactPopup.close();
				this.$refs.remindPopup.close();
				this.$refs.popup.close();
			}
		}
	}
</script>

<style lang="scss" scoped>
	.img_s{
		width: 100rpx;
	    height: 100rpx;
		border-radius: 50%;
	}
	
	.flex_s{
		display: flex;
		align-items: center;
	}
	
	.grade{
		height: 36rpx;
		color: #fff;
		font-size: 26rpx;
		border-radius: 5rpx;
		line-height: 36rpx;
		text-align: center;
		background-color: #1D755C;
	}
	
	.grade_s{
		height: 36rpx;
		color: #fff;
		font-size: 26rpx;
		border-radius: 5rpx;
		line-height: 36rpx;
		text-align: center;
		background-color: #EA6031;
	}
	
	.border_t{
		border-top: 1px solid #EFEFEF;
	}
	
	.w50{
		width: 50%;
	}
	
	.flex-x{
		display: flex;
		align-items: center;
	}
	
	.give_up{
		color: #2E896F;
		width: 170rpx;
		height: 90rpx;
		line-height: 90rpx;
		text-align: center;
		border: 1px solid #2E896F;
		border-radius: 45rpx;
		box-sizing: border-box;
	}
	
	.count_down{
		color: #fff;
		height: 90rpx;
		padding: 0 25rpx;
		line-height: 90rpx;
		text-align: center;
		border-radius: 45rpx;
		background: linear-gradient(to bottom,#88CFBA,#1D755C);
		
		/deep/ .u-count-down__text {
			color: #fff !important;
			font-size: 26rpx !important;
			padding: 0 5rpx !important;
		}
	}
	
	.prompt{
		color: #E57126;
	}
	
	.time{
		/deep/ .u-count-down__text {
			color: #E57126 !important;
		}
	}
	
	// 申请路线
	/deep/.u-steps-item__content--column{
		margin-left: 50rpx !important;
	}
	
	/deep/.u-steps-item__content{
		margin-bottom: 30rpx !important;
	}
	
	/deep/.u-text__value--content{
		font-size: 30rpx !important;
		color: #000 !important;
	}
	/deep/.u-text__value--main{
		font-size: 30rpx !important;
		color: #000 !important;
	}
	
	/deep/.u-text__value--tips{
		font-size: 28rpx !important;
		color: #666 !important;
		margin-top: 14rpx !important;
	}
	
	.steps {
		height: 100%;
		background-color: #fff;
	
		.out-box {
			flex-direction: column;
			align-items: center;
	
			.self-box {
				position: relative;
				height: 120rpx;
				display: flex;
				align-items: flex-start;
	
				.line {
					width: 1rpx;
					height: 220rpx;
					top: 0rpx;
					left: 9rpx;
					background: #2E896F;
					position: absolute;
					margin-top: 45rpx;
					margin-left: 6rpx;
				}
	
				image {
					width: 28rpx;
					height: 28rpx;
					margin-left: 6rpx;
				}
	
				text {
					font-size: 28rpx;
					color: #909399;
				}
	
				.currentFontColor {
					color: #409eff;
				}
	
				.currentLineColor {
					background-color: #409eff;
				}
			}
		}
	}
	
	.marginB140{
		margin-bottom: 140rpx;
	}
	
	// 分享选项弹出层
	.dialogBG {
		width: 100%;
		/* height: 100%; */
	}
	
	
	/* 21天结束复习弹窗样式 */
	.reviewCard_box {
		width: 670rpx;
		position: relative;
	}
	
	// .reviewCard_box image {
	// 	width: 100%;
	// 	height: 100%;
	// }
	
	.reviewCard {
		position: relative;
		width: 100%;
		height: 100%;
		background: #FFFFFF;
		color: #000;
		border-radius: 24upx;
		padding: 50upx 60upx;
		box-sizing: border-box;
	}
	
	.prompt-img{
		width: 130rpx;
		height: 130rpx;
	}
	
	.cartoom_image {
		width: 420rpx;
		height: 272rpx;
		position: absolute;
		top: -255rpx;
		left: 145rpx;
		z-index: -1;
	}
	
	.review_close {
		position: absolute;
		top: 20rpx;
		right: 20rpx;
		z-index: 1;
	}
	
	.reviewTitles {
		font-size: 32rpx;
		margin-top: 40rpx;
		margin-bottom: 80rpx;
		text-align: center;
	}
	
	.review_btn {
		width: 250upx;
		height: 80upx;
		background: linear-gradient(180deg, #88CFBA 0%, #1D755C 100%);
		border-radius: 45upx;
		font-size: 30upx;
		color: #FFFFFF;
		line-height: 80upx;
		justify-content: center;
		text-align: center;
	}
	
	.close_btn {
		width: 250upx;
		height: 80upx;
		color: #2E896F;
		font-size: 30upx;
		line-height: 80upx;
		text-align: center;
		border-radius: 45upx;
		box-sizing: border-box;
		border: 1px solid #2E896F;
	}
	
	// 短信
	.content {
		color: #333;
		border-radius: 25rpx;
		padding: 60rpx 20rpx;
		width: 600rpx;
	}
	
	/deep/.u-icon--right {
		justify-content: center;
	}
	
	.image_class{
		width: 130rpx;
		height: 130rpx;
	}
	
	.close-icon{
		position: absolute;
		top: 20rpx;
		right: 20rpx;
	}
	
	.icon_round{
		width: 20rpx;
		height: 20rpx;
		border-radius: 50%;
		background-color: #2E896F;
		margin-left: 6rpx;
		margin-top: 15rpx;
		min-width: 20rpx;
	}
	
	.round_icon{
		width: 20rpx;
		height: 20rpx;
		border-radius: 50%;
		margin-left: 6rpx;
		margin-top: 15rpx;
		min-width: 20rpx;
		background-color: #c6c6c6;
	}
	
	.list_content .self-box:first-child .icon_round{
		background-color: #c6c6c6;
	}
	
	.prompt-subordinate{
		color: #666 !important; 
	}
</style>
