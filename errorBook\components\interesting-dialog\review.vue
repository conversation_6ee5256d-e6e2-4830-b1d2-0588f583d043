<template>
    <view class="reviewCard_box positionRelative">
        <view class="cartoom_image">
             <image :src="dialog_iconUrl" mode="widthFix"></image>
            <!-- <image style="width: 316rpx;height: 265rpx;" src="https://document.dxznjy.com/applet/newimages/xiezi.png"></image> -->
        </view>
        <view class="review_close" @click="closeDialog">
            <uni-icons type="clear" size="28" color="#B1B1B1"></uni-icons>
        </view>
        <view class="interesting_popupCenter">
                <view class="reviewTitle bold pb-20">温馨提示</view>
        		<!-- <view class="interet_head" :style="gradScreenWheel==2?'width:225rpx':''">
        				<image v-if="gradScreenWheel==1||gradScreenWheel==4" class="interet_popupTitle" :src="imgHost+'interesting/dialog_interesting_review_title.png'">
        				<image v-if="gradScreenWheel==2" class="interet_popupTitle interet_popupTitle2" mode="" />
        				<image v-if="gradScreenWheel==3" class="interet_popupTitle interet_popupTitle2" mode="" />
        		</view> -->
    
        		<!-- <view class="popupText" v-if="gradScreenWheel==1&&reviewWordNum.length+errorWordNum.length==0">
        			<text style="color: red;">是否开启下一组继续复习</text>
        		</view>
        		<view class="popup_content" v-if="gradScreenWheel==4">
        			<view class="reviewPopup_list">
        				<text>复习词数</text>
        				<text class="DesignFont">{{reviewWordNum.length+errorWordNum.length}}</text>
        			</view>
        			<view class="reviewPopup_list">
        				<text>错误词数</text>
        				<text class="DesignFont" style="color: #ff3c3c;">{{errorWordNum.length}}</text>
        			</view>
        			<view class="reviewPopup_list">
        				<text>正确率</text>
        				<text class="DesignFont" style="color: #3c6c3c;">{{correctRate}}%</text>
        			</view>
        		</view> -->
        		<!-- 组的文字 -->
        		<!-- <view class="finishAnd" v-if="gradScreenWheel==1&&reviewWordNum.length+errorWordNum.length!=0">
        			已完成第 <text>{{showData.nowGroup}}</text> 组复习,
        			当前关卡待复习 <text>{{showData.levelTotalGroup - showData.levelGroup}}</text> 组
        		</view> -->
        
        		<!-- 关的文字 -->
        		<view class="finishAnd" v-if="gradScreenWheel==2">恭喜你已经完成啦</view>
        <!-- 		<view class="finishAnd" v-if="gradScreenWheel==2" :style="isIndex?'margin:60rpx':''">恭喜您通过第{{gradChinese[showData.nowLevel-1]}}关</view> -->
        
        		<!-- 复习报告复习词数，错误词数，正确率 -->
        		<view class="popup_content" v-if="gradScreenWheel<3&&reviewWordNum.length+errorWordNum.length!=0&&!isIndex">
        			<view class="reviewPopup_list">
        				<text>复习词数</text>
        				<text class="DesignFont">{{reviewWordNum.length+errorWordNum.length}}</text>
        			</view>
        			<view class="reviewPopup_list">
        				<text>错误词数</text>
        				<text class="DesignFont" style="color: #ff3c3c;">{{errorWordNum.length}}</text>
        			</view>
        			<view class="reviewPopup_list">
        				<text>正确率</text>
        				<text class="DesignFont" style="color: #3c6c3c;">{{correctRate}}%</text>
        			</view>
        		</view>
        
        		<!-- 轮的文字 -->
        		<view class="finishAnd1" v-if="gradScreenWheel==3">恭喜您完成本轮学习</view>
    
        		<view class="popBtnGroup popBtnGroup1" v-if="gradScreenWheel==1">
        			<view class="popBtnGroup_list_left" @click="confirm()">查看错词</view>
        			<view class="popBtnGroup_list_right" @click="rightClick()">继续复习</view>
        		</view>
        		<view class="popBtnGroup popBtnGroup1" v-if="gradScreenWheel==2" :style="isIndex?'margin-top: 90rpx;':''">
        			<!-- <view class="popBtnGroup_list" @click="confirm()">查看错词</view>
        			<view class="popBtnGroup_list" @click="rightClick()">下一关</view> -->
                    <view class="popBtnGroup_list_left" @click="cancelFunc()">取消</view>
                    <view class="popBtnGroup_list_right" @click="rightClick()">再来一轮</view>
        		</view>
        		<view class="popBtnGroup" v-if="gradScreenWheel==3" >
        			<view class="popBtnGroup_list_left" @click="backFunc()">返回</view>
        			<view class="popBtnGroup_list_right" @click="rightClick()">再来一轮</view>
                    <!-- <view class="popBtnGroup_list" @click="confirm()">查看报告</view>
        			<view class="popBtnGroup_list" @click="rightClick()">再来一轮</view> -->
        		</view>
        		<view class="popBtnGroup" v-if="gradScreenWheel==4" style="margin-top: 90rpx;">
        			<view class="popBtnGroup_list_left" @click="confirm()">查看错词</view>
        			<view class="popBtnGroup_list_right" @click="rightClick()">其它玩法</view>
        		</view>
        	</view>
    </view>
</template>

<script>
    import Util from '@/util/util.js'
	import interestChart from "@/common/interestChart.js"
	export default {
		//是否是复习弹窗 //gradScreenWheel 1是组，2是关卡，3是轮    4是不可进入下一组
		//只有是组的时候展示需要nowGroup当前组 waitGroup 待复习的组
		//grad 一关结束，第几关    
		props: {
			reviewWordNum: Array,
			errorWordNum: Array,
			correctRate: Number,
			gradScreenWheel: Number,
			showData:{
				roundId:Number,	
				nowGroup: Number,
				waitGroup: Number,
				studentCode: Number
			},
			isIndex:false,	
			play:String,
			scheduleCode:String,
			pageUrl:String
		},
		data() {
			return {
				imgHost: getApp().globalData.imguseHost,
				titleIcon: [
					'class_icon1.png',
					'class_icon2.png',
					'class_icon3.png',
					'class_icon4.png',
					'class_icon5.png',
					'class_icon6.png',
					'class_icon7.png',
					'class_icon8.png'
				],
				gradChinese: ['一', '二', '三', '四', '五', '六', '七', '八'],
				showDataList:[],
                
                dialog_iconUrl:Util.getCachedPic("https://document.dxznjy.com/dxSelect/dialog_icon.png","dialog_icon_path"),
			}
		},
		
		methods: {

			//确认
			confirm() {
				let that = this;
				if (that.gradScreenWheel != 3) {
					console.log("查看错词")
					let status;
					if(that.gradScreenWheel==1){status=3;}
					if(that.gradScreenWheel==2){status=2;}
					if(that.gradScreenWheel==3){status=1;}
					if(that.gradScreenWheel==4){status=4;}
					var wrongData={
						"roundId":that.showData.roundId,
						"nowLevel":that.showData.nowLevel,
						"nowGroup":that.showData.nowGroup,
						"play":that.play,
						"status":status,
						scheduleCode:that.scheduleCode,
						"merchantCode":that.showData.merchantCode
					}
					if(that.isIndex){
						that.$httpUser.get(`znyy/course/getWrongWords?play=${wrongData.play}&status=${wrongData.status}&nowGroup=${wrongData.nowGroup}&nowLevel=${wrongData.nowLevel}&roundId=${wrongData.roundId}`).then((res) => {
							if (res.data.success) {
								if(res.data.data.data.length==0){
									that.$util.alter("太棒了，答题全部正确");
								}else{
									uni.navigateTo({
										url: `/interestModule/wordIdentifying?wrongData=${encodeURIComponent(JSON.stringify(wrongData))}`
									})
								}
								
							} else {
								that.$util.alter(res.data.message)
							}
						})
						return;
					}
					
					if(that.errorWordNum.length==0){
						that.$util.alter("太棒了，答题全部正确");
						setTimeout(function(){
							uni.navigateTo({
								url: '/antiAmnesia/review/funReview?scheduleCode=' + that.scheduleCode + '&merchantCode='+that.showData.merchantCode
							})
						},1000);
					}else{
						uni.navigateTo({
							url: `/interestModule/wordIdentifying?wrongData=${encodeURIComponent(JSON.stringify(wrongData))}`
						})
					}
					return;
				}
				if (that.gradScreenWheel == 3) {	
					that.$httpUser.get(`znyy/stats/review/getAnalysisList?scheduleCode=${that.scheduleCode}`).then((res) => {
						if(res.data.success){						
							if(res.data.data.length!=0){
								that.showDataList = res.data.data.data;
								for(let i=0;i<that.showDataList.length;i++){
									let item = that.showDataList[i];
									if(item.roundId==that.showData.roundId){
										var data = {
											scheduleCode:that.scheduleCode,
											roundId:item.roundId,
											rightRate:item.rightRate,
											scoreGrade:item.scoreGrade
										};
										
										let turnGradeStudyData;
										// 轮次报告学情走势
										interestChart.turnGradeStudy(item.roundId).then((res)=>{
											turnGradeStudyData = res;
											// uni.navigateTo({
											// 	url: "/pages/interest/turnGrade?turnGradeData="+ encodeURIComponent(JSON.stringify(data))+'&turnGradeStudyData='+encodeURIComponent(JSON.stringify(turnGradeStudyData))
											// })
										});
										return;
									}
								}	
								
							}
						}else{
							uni.hideLoading()
							that.$util.alter(res.data.message)
						}										
					})
                }
			},
            
            backFunc(){
                uni.navigateBack({delta:1});
              	// uni.redirectTo({
              	// 	url: '/antiAmnesia/review/funReview?scheduleCode=' + this.scheduleCode + '&merchantCode='+this.showData.merchantCode
              	// })
            },
            
            cancelFunc(){
                this.closeDialog();
            },
            
			//取消
			rightClick() {
				let that = this;
				if (that.gradScreenWheel == 1) {
					console.log("继续复习")
					that.startNewGroup();
					return;
				}
				if (that.gradScreenWheel == 2) {
					console.log("开启下一轮")
					// that.startNewLevel();
					that.levelPlayAgain();
					return;
				}
				if (that.gradScreenWheel == 3) {
					console.log("开启下一轮")
					that.levelPlayAgain();
					return;
				}
				if (that.gradScreenWheel == 4) {
					uni.navigateTo({
						url: '/antiAmnesia/review/funReview?scheduleCode=' + that.scheduleCode + '&merchantCode='+that.showData.merchantCode
					})
					return;
				}		
			},
			// 进入下一组
			startNewGroup() {
				let that = this;
				that.$httpUser.get(`znyy/course/start/new/group?scheduleCode=${that.scheduleCode}`).then(
					(res) => {
						if (res.data.success) {
							console.log("进入下一组");
							that.getShowDataToNextPage();
						} else {
							that.$util.alter(res.data.message)
						}
					})
			},
			// 进入下一关
			startNewLevel() {
				let that = this;
				
				that.$httpUser.get(`znyy/course/start/new/level?scheduleCode=${that.scheduleCode}`).then(
					(res) => {
						if (res.data.success) {
							console.log("进入下一关");
							that.getShowDataToNextPage();
						} else {
							that.$util.alter(res.data.message)
						}
					})
			},
			// 进入下一轮
			levelPlayAgain() {
				let that = this;
				that.$httpUser.get(`znyy/course/noLevel/play/again?scheduleCode=${that.scheduleCode}`).then((res) => {
				// that.$httpUser.get(`znyy/course/level/play/again?scheduleCode=${that.scheduleCode}`).then((res) => {
					if (res.data.success) {
						console.log("进入下一轮");
						this.getShowDataToNextPage()
					} else {
						that.$util.alter(res.data.message)
					}
				})
			},
			
			// 获取返回页展示数据
			getShowDataToNextPage() {
				let that = this;
				console.log(909090909090)
				console.log(that.scheduleCode)
				let merchantCode = that.showData.merchantCode;
				if(that.pageUrl=='funReview'){
					uni.redirectTo({
						url: '/antiAmnesia/review/funReview?scheduleCode=' + that.scheduleCode + '&merchantCode='+merchantCode
					})
					return;
				}
				that.$httpUser.get(`znyy/course/query/fun/words?scheduleCode=${that.scheduleCode}`).then((res) => {
					if (res.data.success) {
						let showData = res.data.data
						console.log('跳转数据')
						console.log(showData)
						console.log('跳转数据')
						showData.scheduleCode = that.scheduleCode;
						showData.merchantCode = merchantCode;
						showData.studentCode = that.showData.studentCode;
						var url = "/interestModule/" + that.pageUrl;
						uni.redirectTo({
							url: url + `?params=` + encodeURIComponent(JSON.stringify(showData))
						})		
					} else {
						that.$util.alter(res.data.message)
					}
				})
			},
			
			
			
			//关闭弹窗
			closeDialog() {
				this.$emit('closeDialog')
			}
		}
	}
</script>

<style>
	.interesting_popupCenter {
		position: relative;
		width: 100%;
		height: 100%;
		background: #FFFFFF;
		color: #000;
		border-radius: 24upx;
		padding: 50upx 55upx;
		box-sizing: border-box;
	}

	.popupText {
		height: 280rpx;
		line-height: 280rpx;
	}

	.popupText1 {
		height: 280rpx;
		padding: 0 30rpx;
		line-height: 80rpx;
		font-size: 36rpx;
	}

	.finishAnd {
		display: inline-block;
		font-size: 35rpx;
		color: #000;
		margin: 10rpx 0;
		text-align: center;
		padding: 0 30rpx;
		height: 60rpx;
		line-height: 60rpx;
        margin-top: 50rpx;
	}

	.finishAnd text {
		color: #000;
	}

	.interet_popupTitle1 {
		width: 210rpx;
		height: 230rpx;
		margin-bottom: 0;
	}

	.interet_popupTitle2 {
		width: 470rpx;
		height: 394rpx;
		margin: -85rpx 45rpx 10rpx 60rpx;
	}

	.finishAnd1 {
		font-size: 35rpx;
		color: #000;
		margin-bottom: 70rpx;
        margin-top: 50rpx;
	}
    
    /* 弹窗样式 */
    .dialogBG {
    	width: 100%;
    	/* height: 100%; */
    }
    
    /* 21天结束复习弹窗样式 */
    .reviewCard_box {
    	width: 670rpx;
    	/* height: 560rpx; */
    	position: relative;
    }
    
    .reviewCard_box image {
    	width: 100%;
    	height: 100%;
    }
    
    .cartoom_image {
    	width: 420rpx;
    	position: absolute;
    	top: -265rpx;
    	left: 145rpx;
    	z-index: -1;
    }
    
    .reviewCard {
    	position: relative;
    	width: 100%;
    	height: 100%;
    	background: #FFFFFF;
    	color: #000;
    	border-radius: 24upx;
    	padding: 50upx 55upx;
    	box-sizing: border-box;
    }
    .review_close {
    	position: absolute;
    	top: 20rpx;
    	right: 20rpx;
    	z-index: 1;
    }
    
    .reviewTitle {
    	width: 100%;
    	text-align: center;
    	font-size: 34upx;
    	display: flex;
    	justify-content: center;
    }
    .dialogContent{
    	box-sizing: border-box;
    	font-size: 32upx;
    	line-height:  45upx;
    	text-align: center;
    	margin-top: 40rpx;
    }
    .popBtnGroup_list_left{
        width: 250rpx;
        height: 80rpx;
        background: #2E896F;
        border-radius: 45rpx;
        font-size: 30rpx;
        color: #FFFFFF;
        line-height: 80rpx;
        text-align: center;
    }
    .popBtnGroup_list_right{
        width: 250rpx;
        height: 80rpx;
        background: #FFFFFF;
        border-radius: 45rpx;
        border: 2rpx solid #2E896F;
        font-size: 30rpx;
        color: #2E896F;
        line-height: 80rpx;
        text-align: center;
        margin-left: 54rpx;
    }
</style>
