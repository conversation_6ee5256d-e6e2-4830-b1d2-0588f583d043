<template>
  <page-meta :page-style="'overflow:' + (rollShow ? 'hidden' : 'visible')"></page-meta>
  <view class="container" v-if="data">
    <view class="testName">
      <image src="https://document.dxznjy.com/dxSelect/ceb7c256-3d92-4ff4-84b1-da01280b0535.png" style="width: 38rpx; height: 40rpx"></image>
      <view class="testNameText">{{ data && data.testPaperName }}</view>
    </view>
    <view class="criterion">
      <view class="criterionTop">
        <view style="flex: 1; display: flex; padding-left: 35rpx">
          <image src="https://document.dxznjy.com/dxSelect/0b6b594e-fdb3-448b-9c1c-631fece9a731.png" style="width: 40rpx; height: 40rpx" mode=""></image>
          <view class="criterionText" style="margin-left: 12rpx" v-if="status == 2">得分{{ data && data.studentScore }}分</view>
          <view class="criterionText" style="margin-left: 12rpx" v-else>总分{{ data && data.score }}分</view>
        </view>
        <view style="flex: 1; display: flex">
          <image src="https://document.dxznjy.com/dxSelect/35f6ac7e-a7ad-467f-a4c2-7430e4d97186.png" style="width: 40rpx; height: 40rpx" mode=""></image>
          <view class="criterionText" style="margin: 0 12rpx">
            <span v-if="status == 0">时间限制{{ data && data.examTime }}分钟</span>
            <span v-if="status == 1 || status == 3">剩余时间{{ examTime }}</span>
            <span v-if="status == 2">用时{{ data && data.answerTime }}</span>
          </view>
        </view>
      </view>
      <view class="line"></view>
      <view class="criterionBtns" v-if="status == 1 || (status == 3 && data.questionList)">
        <view class="criterionBtn" @click="$noMultipleClicks(last)" :style="{ visibility: index == 0 ? 'hidden' : '' }">上一题</view>
        <view style="font-size: 30rpx; font-weight: bold; display: flex; align-items: center" @click="openOptions">
          {{ index + 1 }}/{{ data.questionList && data.questionList.length }}
          <uni-icons type="down" size="12"></uni-icons>
        </view>
        <view class="criterionBtn" @click="$noMultipleClicks(next)">{{ index == data.questionList.length - 1 ? '提交' : '下一题' }}</view>
      </view>
      <view class="questionType" v-else-if="status == 0">
        <view style="margin-bottom: 15rpx">题型</view>
        <view class="questionTypes">
          <view class="" v-for="(e, i) in data.questionTypeCountMap" :key="i">{{ e.keyValue }}【{{ e.value }}】</view>
        </view>
      </view>
      <view class="questionType" v-else>
        <view style="margin-bottom: 15rpx">题型</view>
        <view class="questionTypes">
          <view>题目总数【{{ data.questionCount }}】</view>
          <view>错误总数【{{ data.errQuestionCount }}】</view>
        </view>
      </view>
    </view>
    <view class="selectLook" v-if="status == 2">
      <view :class="type == 0 ? 'selectedAll' : ''" @click="checkSelect(0)">全部</view>
      <view :class="type == 1 ? 'selectedAll' : ''" @click="checkSelect(1)" style="margin-left: 50rpx">只看错题</view>
    </view>
    <view v-if="status == 1 || status == 3">
      <SubjectTest ref="subject" :status="status" @addImgae="addimage" :item="data.questionList[index]" :index="index" @checked="subChecked"></SubjectTest>
    </view>
    <view v-else>
      <view class="" v-for="(e, i) in data.questionList" :key="e.id">
        <SubjectTest :item="e" :status="status" :index="i" @look="lookAnalysis" @goShowVideo="goShowVideo"></SubjectTest>
      </view>
    </view>
    <view style="height: 100rpx" v-if="status == 0"></view>
    <view class="sumbit start" v-if="status == 0" @click="changeStatus">开始答题</view>
    <u-popup :show="show" mode="bottom" @close="closeShow">
      <view class="questions">
        <view class="options" v-for="(e, i) in data.questionList" :key="e.id" @click="goIndex(i)">
          <view>{{ i + 1 }}.【{{ typeArr.find((o) => o.id == e.questionType).name }}】</view>
          <view>
            <span v-if="e.myAnswer" style="color: #428a6f">已填写</span>
            <span v-else style="color: #d85555">未填写</span>
          </view>
        </view>
      </view>
    </u-popup>
    <uni-popup ref="popopTest" :mask-click="false" type="center" @change="changeStudent">
      <view class="dialogBG">
        <view class="reviewCard_box positionRelative">
          <view class="reviewCard">
            <view class="reviewTitle bold pb-10">开始答题</view>
            <view class="">此试卷严格限时,到达考试限制时间将自动交卷</view>
            <view class="mask-footer">
              <button class="cancel-button" @click="closeStartTest">取消</button>
              <button class="confirm-button" @click="startTestComfig">知道了</button>
            </view>
          </view>
        </view>
      </view>
    </uni-popup>
    <uni-popup ref="popopSumbit" :mask-click="false" type="center" @change="changeStudent">
      <view class="dialogBG">
        <view class="reviewCard_box positionRelative">
          <view class="reviewCard">
            <view class="reviewTitle bold pb-10">{{ sumbitTitle }}</view>
            <view class="">{{ sumbitContent }}</view>
            <view class="mask-footer">
              <button class="cancel-button" v-if="submitStatus !== 3" @click="close">取消</button>
              <button class="confirm-button" @click="lastSumbit">
                {{ submitStatus == 3 ? '知道了' : '提交' }}
                <sapn v-if="submitStatus == 3">({{ propNum }})</sapn>
              </button>
            </view>
          </view>
        </view>
      </view>
    </uni-popup>
    <u-popup :show="videoShow" @close="videoClose" mode="bottom" :round="10">
      <view class="videoBg" v-if="videoShow">
        <view class="videoTitle">知识点视频</view>
        <view class="videoClose">
          <uni-icons type="closeempty" size="16" @click="videoClose"></uni-icons>
        </view>
        <view class="line"></view>
        <view class="videoList">
          <view class="videoItem" v-for="itemvideo in videoList" :key="itemvideo.id" @click="goVideoPlay(itemvideo)">
            <view :class="itemvideo.isUnlock ? 'videoText' : 'disabled'">{{ itemvideo.videoName }}</view>
            <image src="https://document.dxznjy.com/dxSelect/icon_video_play.png" style="width: 48rpx; height: 48rpx"></image>
          </view>
        </view>
      </view>
    </u-popup>
  </view>
</template>

<script>
  import SubjectTest from './components/subjectTest.vue';
  export default {
    data() {
      return {
        noClick: true, //防抖
        videoShow: false,
        show: false,
        rollShow: false,
        sumbitTitle: '',
        sumbitContent: '',
        activeAll: 1,
        videoList: [],
        data: null,
        index: 0,
        status: 0, //0查看试卷 1 开始答题 2查看解析
        time: 0,
        type: 0, //查看错题
        setIn: '',
        submitStatus: 0,
        testPaperId: '',
        studentCode: '',
        propSet: '',
        propNum: 5,
        typeArr: [
          { name: '单选', id: 0 },
          { name: '填空', id: 1 },
          { name: '计算', id: 2 },
          { name: '解方程', id: 3 },
          { name: '证明题', id: 4 },
          { name: '几何综合题', id: 5 }
        ]
      };
    },
    components: {
      SubjectTest
    },
    computed: {
      examTime() {
        const sec = parseInt(this.time, 10);
        if (isNaN(sec)) {
          return '00:00:00';
        }
        const minutes = Math.floor(sec / 60);
        const remainingSeconds = sec % 60;

        // 格式化为两位数，不足补零
        const formattedMinutes = String(minutes).padStart(2, '0');
        const formattedSeconds = String(remainingSeconds).padStart(2, '0');
        return `${formattedMinutes}:${formattedSeconds}`;
      }
    },
    onLoad(e) {
      this.status = e.status - 0;
      this.studentCode = e.studentCode;
      this.testPaperId = e.id;
      if (this.status == 2) {
        this.analysis();
      } else {
        this.init();
      }
    },
    methods: {
      closeShow() {
        this.show = false;
      },
      closeStartTest() {
        this.$refs.popopTest.close();
      },
      async startTestComfig() {
        this.$refs.popopTest.close();
        let obj = { testPaperId: this.testPaperId, studentCode: this.studentCode };
        await this.$httpUser.post(`dyf/math/wap/studentMathTestPaper/startAnswer`, obj);
        this.status = 1;
        this.time = this.data.examTime * 60;
        this.setIntiem();
      },
      checkSelect(e) {
        if (e == this.type) return;
        this.type = e;
        this.analysis();
      },
      goVideoPlay(e) {
        if (e.isUnlock == 0)
          return uni.showToast({
            title: '该视频暂未解锁',
            icon: 'none'
          });
        let arr = [e];
        uni.navigateTo({
          url: `/knowledgeGraph/knowVideoPlay?type=1&index=0&videoInfo=` + JSON.stringify(arr)
        });
      },
      openOptions() {
        this.rollShow = true;
        this.show = true;
      },
      lookAnalysis(e) {
        this.rollShow = e;
      },
      changeStudent(e) {
        this.rollShow = e.show;
      },
      videoClose() {
        this.videoShow = false;
        this.rollShow = false;
      },
      async goShowVideo(e) {
        let { data } = await this.$httpUser.get(`dyf/math/wap/correctionNoteBook/videoPage?pageNum=1&pageSize=1000&answerQuestionId=${e}&studentCode=${this.studentCode}`);
        if (data.success) {
          if (!data.data.data.length)
            return uni.showToast({
              title: '暂无视频',
              icon: 'none'
            });
          console.log(data.data.data);
          this.videoList = data.data.data;
          console.log(this.videoList);
          this.videoShow = true;
          this.rollShow = true;
        }
      },
      async init() {
        let { data } = await this.$httpUser.get(`dyf/math/wap/studentMathTestPaper/testPaperInfo?testPaperId=${this.testPaperId}&studentCode=${this.studentCode}`);
        if (data.success) {
          this.data = data.data;
          console.log(this.data);
          this.data.questionList.forEach((e) => {
            e.mathSmallQuestionList.forEach((o, i) => {
              o.myAnswerImage = e.myAnswerImage[i];
            });
          });
          if (this.status == 1) {
            this.changeStatus();
          } else if (this.status == 3) {
            this.time = data.data.testRestTime;
            // this.time = 50;
            this.setIntiem();
          }
          // if(this.status==)
        }
      },
      async analysis() {
        uni.showLoading({
          title: '加载中...'
        });
        let { data } = await this.$httpUser.get(
          `dyf/math/wap/studentMathTestPaper/getStudentPauseNodeInfo?testPaperId=${this.testPaperId}&studentCode=${this.studentCode}&type=${this.type}`
        );
        uni.hideLoading();
        if (data.success) {
          this.data = data.data;
          this.data.questionList.forEach((e) => {
            e.mathSmallQuestionList.forEach((o, i) => {
              o.myAnswerImage = e.myAnswerImage[i];
            });
          });
          // if(this.status==)
        }
      },
      last() {
        if (this.index <= 0) return;
        this.index--;
      },
      goIndex(e) {
        this.index = e;
        this.rollShow = false;
        this.show = false;
      },
      addimage(i, e) {
        console.log(i, e);
        this.data.questionList[this.index].mathSmallQuestionList[i].myAnswerImage = e;
      },
      subChecked(e) {
        this.data.questionList[this.index].myAnswer = e;
      },
      async next() {
        if (!this.data.questionList[this.index].myAnswer && this.index < this.data.questionList.length - 1) return this.index++;

        let arr = [];
        this.data.questionList[this.index].mathSmallQuestionList.forEach((e) => {
          if (e.myAnswerImage) {
            arr.push(e.myAnswerImage);
          } else {
            arr.push('');
          }
        });

        let myAnswer = this.data.questionList[this.index].myAnswer;
        let obj = {
          paperQuestionId: this.data.questionList[this.index].id,
          studentCode: this.studentCode,
          myAnswer
        };
        const allEmpty = arr.every((item) => item == '');
        if (!allEmpty) {
          obj.myAnswerImage = arr.join(',');
        }
        // let myAnswerImage=this.$refs.subject.fileList.map()
        await this.$httpUser.post(`dyf/math/wap/studentMathTestPaper/commitOne`, obj);
        if (this.index == this.data.questionList.length - 1) {
          this.submit();

          return;
        }
        this.index++;
      },
      submit() {
        if (!this.data.questionList.every((e) => e.myAnswer)) {
          this.sumbitTitle = '提交答题卡';
          this.sumbitContent = '你还未写完所有题目答案，是否提交';
          this.submitStatus = 1;
        } else {
          this.sumbitTitle = '提交答题卡';
          this.sumbitContent = '你已写完所有题目，提交后无法修改，确定是否提交';
          this.submitStatus = 2;
        }
        this.$refs.popopSumbit.open();
      },
      close() {
        this.$refs.popopSumbit.close();
      },
      async lastSumbit() {
        if (this.submitStatus == 3) {
          clearInterval(this.propSet);
          this.propSet = null;
        }
        this.$refs.popopSumbit.close();
        uni.showLoading({
          title: '提交中...'
        });
        let res = {
          testPaperId: this.testPaperId,
          studentCode: this.studentCode
        };
        await this.$httpUser.post(`dyf/math/wap/studentMathTestPaper/commitAll`, res);
        uni.hideLoading();
        uni.navigateBack();
      },
      setIntiem() {
        let that = this;
        this.setIn = setInterval(() => {
          if (this.time <= 0) {
            clearInterval(this.setIn);
            if (that.data.isForce) {
              this.force();
            }
          } else {
            this.time--;
          }
        }, 1000);
      },
      force() {
        this.sumbitTitle = '考试结束';
        this.sumbitContent = '考试时间已结束，答题卡自动提交';
        this.submitStatus = 3;
        this.propNum = 5;
        this.propSet = setInterval(() => {
          if (this.propNum == 1) {
            clearInterval(this.propSet);
            this.propSet = null;
            this.lastSumbit();
          } else {
            this.propNum--;
          }
        }, 1000);
        this.$refs.popopSumbit.open();
      },
      async changeStatus() {
        if (this.data.isForce) {
          return this.$refs.popopTest.open();
        }
        let obj = { testPaperId: this.testPaperId, studentCode: this.studentCode };
        await this.$httpUser.post(`dyf/math/wap/studentMathTestPaper/startAnswer`, obj);
        this.status = 1;
        this.time = this.data.examTime * 60;
        this.setIntiem();
      }
    }
  };
</script>

<style lang="scss" scoped>
  .disabled {
    color: #666;
  }
  .videoText {
    color: #000;
  }
  .selectLook {
    height: 86rpx;
    background-color: #fbfbfb;
    color: #999999;
    display: flex;
    align-items: center;
    padding-left: 33rpx;
    font-size: 15px;
  }
  .selectedAll {
    color: #009e74;
    font-weight: bold;
  }
  .videoBg {
    position: relative;
    height: 750rpx;
    .videoTitle {
      height: 80rpx;
      line-height: 80rpx;
      text-align: center;
      font-size: 18px;
    }
    .line {
      width: 690rpx;
      height: 2rpx;
      margin: 0 auto;
      background-color: #e3e3e3;
    }
    .videoList {
      height: 570rpx;
      overflow-y: auto;
      padding: 0 40rpx;
    }
    .videoItem {
      height: 100rpx;
      display: flex;
      align-items: center;
      justify-content: space-between;
      color: #000;
      font-size: 16px;
    }
    .videoClose {
      position: absolute;
      top: 20rpx;
      right: 20rpx;
      height: 28rpx;
      width: 28rpx;
    }
  }
  /* 弹窗样式 */
  .dialogBG {
    width: 100%;
    /* height: 100%; */
  }
  /* 21天结束复习弹窗样式 */
  .reviewCard_box {
    width: 670rpx;
    /* height: 560rpx; */
    position: relative;
  }
  .reviewCard_box {
    width: 670rpx;
    /* height: 560rpx; */
    position: relative;
  }
  .confirm-button {
    display: flex;
    justify-content: center;
    align-items: center;
    /* 文本垂直居中对齐 */
    background: #2e896f;
    color: #ffffff !important;
  }
  .mask-footer {
    margin-top: 20px;
    display: flex;
    justify-content: space-around;
  }

  .mask-footer button {
    width: 250rpx;
    height: 80rpx;
    font-size: 30rpx;
    border-radius: 45rpx;
  }
  .cancel-button {
    display: flex;
    justify-content: space-around;
    align-items: center;
    /* 文本垂直居中对齐 */
    color: #2e896f !important;
    border: 1rpx solid #2e896f !important;
  }
  .reviewCard_box image {
    width: 100%;
    height: 100%;
  }
  .reviewCard {
    position: relative;
    width: 100%;
    height: 100%;
    background: #ffffff;
    color: #000;
    border-radius: 24upx;
    padding: 50upx 55upx;
    box-sizing: border-box;
  }
  .reviewTitle {
    width: 100%;
    text-align: center;
    font-size: 34upx;
    display: flex;
    justify-content: center;
  }
  .sumbit {
    position: fixed;
    bottom: 30rpx;
    left: 50%;
    transform: translateX(-50%);
    width: 328rpx;
    line-height: 80rpx;
    text-align: center;
    height: 80rpx;
    font-size: 30rpx;
    border-radius: 80rpx;
    color: #fff;
  }
  .questions {
    overflow-y: auto;
    height: 545rpx;
    padding: 43rpx 53rpx 0;
    .options {
      height: 45rpx;
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 52rpx;
      font-size: 32rpx;
    }
  }
  .start {
    background: linear-gradient(180deg, #88cfba 0%, #1d755c 100%);
  }
  .pause {
    background: #fda324;
  }
  .container {
    background-color: #f3f8fc;
    padding: 21rpx 24rpx;
    .line {
      height: 2rpx;
      width: 636rpx;
      margin: 0 auto;
      background-color: #dddddd;
    }
    .questionType {
      font-size: 30rpx;
      padding-left: 35rpx;
      padding-top: 28rpx;
      .questionTypes {
        display: flex;
        min-height: 40rpx;
        font-weight: bold;
        flex-wrap: wrap;
        align-items: center;
        justify-content: space-between;
      }
    }
    .criterion {
      padding: 0 0 16rpx;
      border-radius: 20rpx;
      // height: 200rpx;
      background: #ffffff;
      .criterionBtns {
        display: flex;
        justify-content: space-between;
        padding: 0 10rpx;
        align-items: center;
        height: 80rpx;
        background-color: #fbfbfb;
        .criterionBtn {
          height: 60rpx;
          line-height: 60rpx;
          border-radius: 60rpx;
          text-align: center;
          font-size: 28rpx;
          background-color: #428a6f;
          color: #fff;
          width: 180rpx;
        }
      }
      .criterionTop {
        height: 118rpx;
        display: flex;
        align-items: center;
      }
      .criterionText {
        font-size: 30rpx;
        color: #333333;
      }
    }
    .testName {
      height: 100rpx;
      background: #ffffff;
      border-radius: 20rpx;
      display: flex;
      justify-content: center;
      align-items: center;
      margin-bottom: 16rpx;
    }
    .testNameText {
      width: 570rpx; /* 容器宽度（必填） */
      white-space: nowrap; /* 禁止换行 */
      overflow: hidden; /* 隐藏溢出内容 */
      text-overflow: ellipsis; /* 显示省略号 */
      margin-left: 6rpx;
      font-size: 32rpx;
      font-weight: bold;
    }
  }
</style>
