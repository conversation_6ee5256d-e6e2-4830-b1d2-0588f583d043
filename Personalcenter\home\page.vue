<template>
  <view v-if="pageShow">
    <view v-if="studyCentre">
      <web-view :src="studyCentre"></web-view>
    </view>
    <view v-if="!studyCentre">
      <u-empty mode="history" icon="https://cdn.uviewui.com/uview/empty/list.png" text="暂无内容"></u-empty>
    </view>
  </view>
</template>

<script>
  const { $http } = require('@/util/methods.js');
  export default {
    name: 'webview',
    data() {
      return {
        pageShow: false,
        webUrl: '',
        studyCentre: ''
      };
    },
    onShow() {
      this.setting();
    },
    methods: {
      async setting() {
        let _this = this;
        const res = await $http({
          url: 'zx/setting/getSysSetting'
        });
        if (res) {
          _this.pageShow = true;
          _this.studyCentre = res.data.studyCentre;
        }
      }
    }
  };
</script>

<style></style>
