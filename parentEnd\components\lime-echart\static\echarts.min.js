!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?e(exports):"function"==typeof define&&define.amd?define(["exports"],e):e((t="undefined"!=typeof globalThis?globalThis:t||self).echarts={})}(this,function(t){"use strict";var x=function(t,e){return(x=Object.setPrototypeOf||({__proto__:[]}instanceof Array?function(t,e){t.__proto__=e}:function(t,e){for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n])}))(t,e)};function u(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function n(){this.constructor=t}x(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)}var b=function(){this.firefox=!1,this.ie=!1,this.edge=!1,this.newEdge=!1,this.weChat=!1},p=new function(){this.browser=new b,this.node=!1,this.wxa=!1,this.worker=!1,this.svgSupported=!1,this.touchEventsSupported=!1,this.pointerEventsSupported=!1,this.domSupported=!1,this.transformSupported=!1,this.transform3dSupported=!1,this.hasGlobalWindow="undefined"!=typeof window};"object"==typeof wx&&"function"==typeof wx.getSystemInfoSync?(p.wxa=!0,p.touchEventsSupported=!0):"undefined"==typeof document&&"undefined"!=typeof self?p.worker=!0:"undefined"==typeof navigator||0===navigator.userAgent.indexOf("Node.js")?(p.node=!0,p.svgSupported=!0):(J=navigator.userAgent,re=(Ht=p).browser,rt=J.match(/Firefox\/([\d.]+)/),W=J.match(/MSIE\s([\d.]+)/)||J.match(/Trident\/.+?rv:(([\d.]+))/),Q=J.match(/Edge?\/([\d.]+)/),J=/micromessenger/i.test(J),rt&&(re.firefox=!0,re.version=rt[1]),W&&(re.ie=!0,re.version=W[1]),Q&&(re.edge=!0,re.version=Q[1],re.newEdge=18<+Q[1].split(".")[0]),J&&(re.weChat=!0),Ht.svgSupported="undefined"!=typeof SVGRect,Ht.touchEventsSupported="ontouchstart"in window&&!re.ie&&!re.edge,Ht.pointerEventsSupported="onpointerdown"in window&&(re.edge||re.ie&&11<=+re.version),Ht.domSupported="undefined"!=typeof document,rt=document.documentElement.style,Ht.transform3dSupported=(re.ie&&"transition"in rt||re.edge||"WebKitCSSMatrix"in window&&"m11"in new WebKitCSSMatrix||"MozPerspective"in rt)&&!("OTransition"in rt),Ht.transformSupported=Ht.transform3dSupported||re.ie&&9<=+re.version);var K="12px sans-serif";var w,S,T=function(t){var e={};if("undefined"!=typeof JSON)for(var n=0;n<t.length;n++){var i=String.fromCharCode(n+32),r=(t.charCodeAt(n)-20)/100;e[i]=r}return e}("007LLmW'55;N0500LLLLLLLLLL00NNNLzWW\\\\WQb\\0FWLg\\bWb\\WQ\\WrWWQ000CL5LLFLL0LL**F*gLLLL5F0LF\\FFF5.5N"),H={createCanvas:function(){return"undefined"!=typeof document&&document.createElement("canvas")},measureText:function(t,e){if(w||(n=H.createCanvas(),w=n&&n.getContext("2d")),w)return S!==e&&(S=w.font=e||K),w.measureText(t);t=t||"",e=e||K;var n=/((?:\d+)?\.?\d*)px/.exec(e),i=n&&+n[1]||12,r=0;if(0<=e.indexOf("mono"))r=i*t.length;else for(var o=0;o<t.length;o++){var a=T[t[o]];r+=null==a?i:a*i}return{width:r}},loadImage:function(t,e,n){var i=new Image;return i.onload=e,i.onerror=n,i.src=t,i}};function C(t){for(var e in H)t[e]&&(H[e]=t[e])}var A=lt(["Function","RegExp","Date","Error","CanvasGradient","CanvasPattern","Image","Canvas"],function(t,e){return t["[object "+e+"]"]=!0,t},{}),L=lt(["Int8","Uint8","Uint8Clamped","Int16","Uint16","Int32","Uint32","Float32","Float64"],function(t,e){return t["[object "+e+"Array]"]=!0,t},{}),G=Object.prototype.toString,W=Array.prototype,U=W.forEach,q=W.filter,Z=W.slice,$=W.map,Q=function(){}.constructor,J=Q?Q.prototype:null,tt="__proto__",et=2311;function nt(){return et++}function it(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];"undefined"!=typeof console&&console.error.apply(console,t)}function y(t){if(null==t||"object"!=typeof t)return t;var e=t,n=G.call(t);if("[object Array]"===n){if(!Dt(t))for(var e=[],i=0,r=t.length;i<r;i++)e[i]=y(t[i])}else if(L[n]){if(!Dt(t)){var o=t.constructor;if(o.from)e=o.from(t);else{e=new o(t.length);for(i=0,r=t.length;i<r;i++)e[i]=t[i]}}}else if(!A[n]&&!Dt(t)&&!yt(t))for(var a in e={},t)t.hasOwnProperty(a)&&a!==tt&&(e[a]=y(t[a]));return e}function d(t,e,n){if(!R(e)||!R(t))return n?y(e):t;for(var i in e){var r,o;e.hasOwnProperty(i)&&i!==tt&&(r=t[i],!R(o=e[i])||!R(r)||F(o)||F(r)||yt(o)||yt(r)||ft(o)||ft(r)||Dt(o)||Dt(r)?!n&&i in t||(t[i]=y(e[i])):d(r,o,n))}return t}function P(t,e){if(Object.assign)Object.assign(t,e);else for(var n in e)e.hasOwnProperty(n)&&n!==tt&&(t[n]=e[n]);return t}function z(t,e,n){for(var i=I(e),r=0;r<i.length;r++){var o=i[r];(n?null!=e[o]:null==t[o])&&(t[o]=e[o])}return t}var rt=H.createCanvas;function k(t,e){if(t){if(t.indexOf)return t.indexOf(e);for(var n=0,i=t.length;n<i;n++)if(t[n]===e)return n}return-1}function ot(t,e){var n,i=t.prototype;function r(){}for(n in r.prototype=e.prototype,t.prototype=new r,i)i.hasOwnProperty(n)&&(t.prototype[n]=i[n]);(t.prototype.constructor=t).superClass=e}function at(t,e,n){if(t="prototype"in t?t.prototype:t,e="prototype"in e?e.prototype:e,Object.getOwnPropertyNames)for(var i=Object.getOwnPropertyNames(e),r=0;r<i.length;r++){var o=i[r];"constructor"!==o&&(n?null!=e[o]:null==t[o])&&(t[o]=e[o])}else z(t,e,n)}function st(t){return!!t&&"string"!=typeof t&&"number"==typeof t.length}function O(t,e,n){if(t&&e)if(t.forEach&&t.forEach===U)t.forEach(e,n);else if(t.length===+t.length)for(var i=0,r=t.length;i<r;i++)e.call(n,t[i],i,t);else for(var o in t)t.hasOwnProperty(o)&&e.call(n,t[o],o,t)}function B(t,e,n){if(!t)return[];if(!e)return St(t);if(t.map&&t.map===$)return t.map(e,n);for(var i=[],r=0,o=t.length;r<o;r++)i.push(e.call(n,t[r],r,t));return i}function lt(t,e,n,i){if(t&&e){for(var r=0,o=t.length;r<o;r++)n=e.call(i,n,t[r],r,t);return n}}function ut(t,e,n){if(!t)return[];if(!e)return St(t);if(t.filter&&t.filter===q)return t.filter(e,n);for(var i=[],r=0,o=t.length;r<o;r++)e.call(n,t[r],r,t)&&i.push(t[r]);return i}function I(t){if(!t)return[];if(Object.keys)return Object.keys(t);var e,n=[];for(e in t)t.hasOwnProperty(e)&&n.push(e);return n}var ht=J&&D(J.bind)?J.call.bind(J.bind):function(t,e){for(var n=[],i=2;i<arguments.length;i++)n[i-2]=arguments[i];return function(){return t.apply(e,n.concat(Z.call(arguments)))}};function ct(t){for(var e=[],n=1;n<arguments.length;n++)e[n-1]=arguments[n];return function(){return t.apply(this,e.concat(Z.call(arguments)))}}function F(t){return Array.isArray?Array.isArray(t):"[object Array]"===G.call(t)}function D(t){return"function"==typeof t}function V(t){return"string"==typeof t}function pt(t){return"[object String]"===G.call(t)}function dt(t){return"number"==typeof t}function R(t){var e=typeof t;return"function"==e||!!t&&"object"==e}function ft(t){return!!A[G.call(t)]}function gt(t){return!!L[G.call(t)]}function yt(t){return"object"==typeof t&&"number"==typeof t.nodeType&&"object"==typeof t.ownerDocument}function mt(t){return null!=t.colorStops}function vt(t){return null!=t.image}function _t(t){return"[object RegExp]"===G.call(t)}function xt(t){return t!=t}function bt(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];for(var n=0,i=t.length;n<i;n++)if(null!=t[n])return t[n]}function N(t,e){return null!=t?t:e}function wt(t,e,n){return null!=t?t:null!=e?e:n}function St(t){for(var e=[],n=1;n<arguments.length;n++)e[n-1]=arguments[n];return Z.apply(t,e)}function Mt(t){var e;return"number"==typeof t?[t,t,t,t]:2===(e=t.length)?[t[0],t[1],t[0],t[1]]:3===e?[t[0],t[1],t[2],t[1]]:t}function Tt(t,e){if(!t)throw new Error(e)}function Ct(t){return null==t?null:"function"==typeof t.trim?t.trim():t.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"")}var kt="__ec_primitive__";function It(t){t[kt]=!0}function Dt(t){return t[kt]}Lt.prototype.delete=function(t){var e=this.has(t);return e&&delete this.data[t],e},Lt.prototype.has=function(t){return this.data.hasOwnProperty(t)},Lt.prototype.get=function(t){return this.data[t]},Lt.prototype.set=function(t,e){return this.data[t]=e,this},Lt.prototype.keys=function(){return I(this.data)},Lt.prototype.forEach=function(t){var e,n=this.data;for(e in n)n.hasOwnProperty(e)&&t(n[e],e)};var At=Lt;function Lt(){this.data={}}var Pt="function"==typeof Map;Rt.prototype.hasKey=function(t){return this.data.has(t)},Rt.prototype.get=function(t){return this.data.get(t)},Rt.prototype.set=function(t,e){return this.data.set(t,e),e},Rt.prototype.each=function(n,i){this.data.forEach(function(t,e){n.call(i,t,e)})},Rt.prototype.keys=function(){var t=this.data.keys();return Pt?Array.from(t):t},Rt.prototype.removeKey=function(t){this.data.delete(t)};var Ot=Rt;function Rt(t){var n=F(t),i=(this.data=new(Pt?Map:At),this);function e(t,e){n?i.set(t,e):i.set(e,t)}t instanceof Rt?t.each(e):t&&O(t,e)}function E(t){return new Ot(t)}function Nt(t,e){for(var n=new t.constructor(t.length+e.length),i=0;i<t.length;i++)n[i]=t[i];for(var r=t.length,i=0;i<e.length;i++)n[i+r]=e[i];return n}function Et(t,e){var n,t=Object.create?Object.create(t):((n=function(){}).prototype=t,new n);return e&&P(t,e),t}function zt(t){t=t.style;t.webkitUserSelect="none",t.userSelect="none",t.webkitTapHighlightColor="rgba(0,0,0,0)",t["-webkit-touch-callout"]="none"}function Bt(t,e){return t.hasOwnProperty(e)}function Ft(){}var Vt=180/Math.PI,Ht=Object.freeze({__proto__:null,HashMap:Ot,RADIAN_TO_DEGREE:Vt,assert:Tt,bind:ht,clone:y,concatArray:Nt,createCanvas:rt,createHashMap:E,createObject:Et,curry:ct,defaults:z,disableUserSelect:zt,each:O,eqNaN:xt,extend:P,filter:ut,find:function(t,e,n){if(t&&e)for(var i=0,r=t.length;i<r;i++)if(e.call(n,t[i],i,t))return t[i]},guid:nt,hasOwn:Bt,indexOf:k,inherits:ot,isArray:F,isArrayLike:st,isBuiltInObject:ft,isDom:yt,isFunction:D,isGradientObject:mt,isImagePatternObject:vt,isNumber:dt,isObject:R,isPrimitive:Dt,isRegExp:_t,isString:V,isStringSafe:pt,isTypedArray:gt,keys:I,logError:it,map:B,merge:d,mergeAll:function(t,e){for(var n=t[0],i=1,r=t.length;i<r;i++)n=d(n,t[i],e);return n},mixin:at,noop:Ft,normalizeCssArray:Mt,reduce:lt,retrieve:bt,retrieve2:N,retrieve3:wt,setAsPrimitive:It,slice:St,trim:Ct});function Gt(t,e){return[t=null==t?0:t,e=null==e?0:e]}function Wt(t){return[t[0],t[1]]}function Ut(t,e,n){return t[0]=e[0]+n[0],t[1]=e[1]+n[1],t}function Xt(t,e,n){return t[0]=e[0]-n[0],t[1]=e[1]-n[1],t}function Yt(t){return Math.sqrt(qt(t))}function qt(t){return t[0]*t[0]+t[1]*t[1]}function Zt(t,e,n){return t[0]=e[0]*n,t[1]=e[1]*n,t}function jt(t,e){var n=Yt(e);return 0===n?(t[0]=0,t[1]=0):(t[0]=e[0]/n,t[1]=e[1]/n),t}function Kt(t,e){return Math.sqrt((t[0]-e[0])*(t[0]-e[0])+(t[1]-e[1])*(t[1]-e[1]))}var $t=Kt;function Qt(t,e){return(t[0]-e[0])*(t[0]-e[0])+(t[1]-e[1])*(t[1]-e[1])}var Jt=Qt;function te(t,e,n,i){return t[0]=e[0]+i*(n[0]-e[0]),t[1]=e[1]+i*(n[1]-e[1]),t}function ee(t,e,n){var i=e[0],e=e[1];return t[0]=n[0]*i+n[2]*e+n[4],t[1]=n[1]*i+n[3]*e+n[5],t}function ne(t,e,n){return t[0]=Math.min(e[0],n[0]),t[1]=Math.min(e[1],n[1]),t}function ie(t,e,n){return t[0]=Math.max(e[0],n[0]),t[1]=Math.max(e[1],n[1]),t}var re=Object.freeze({__proto__:null,add:Ut,applyTransform:ee,clone:Wt,copy:function(t,e){return t[0]=e[0],t[1]=e[1],t},create:Gt,dist:$t,distSquare:Jt,distance:Kt,distanceSquare:Qt,div:function(t,e,n){return t[0]=e[0]/n[0],t[1]=e[1]/n[1],t},dot:function(t,e){return t[0]*e[0]+t[1]*e[1]},len:Yt,lenSquare:qt,length:Yt,lengthSquare:qt,lerp:te,max:ie,min:ne,mul:function(t,e,n){return t[0]=e[0]*n[0],t[1]=e[1]*n[1],t},negate:function(t,e){return t[0]=-e[0],t[1]=-e[1],t},normalize:jt,scale:Zt,scaleAndAdd:function(t,e,n,i){return t[0]=e[0]+n[0]*i,t[1]=e[1]+n[1]*i,t},set:function(t,e,n){return t[0]=e,t[1]=n,t},sub:Xt}),oe=function(t,e){this.target=t,this.topTarget=e&&e.topTarget},ae=(se.prototype._dragStart=function(t){for(var e=t.target;e&&!e.draggable;)e=e.parent||e.__hostTarget;e&&((this._draggingTarget=e).dragging=!0,this._x=t.offsetX,this._y=t.offsetY,this.handler.dispatchToElement(new oe(e,t),"dragstart",t.event))},se.prototype._drag=function(t){var e,n,i,r,o=this._draggingTarget;o&&(e=t.offsetX,n=t.offsetY,i=e-this._x,r=n-this._y,this._x=e,this._y=n,o.drift(i,r,t),this.handler.dispatchToElement(new oe(o,t),"drag",t.event),i=this.handler.findHover(e,n,o).target,r=this._dropTarget,o!==(this._dropTarget=i))&&(r&&i!==r&&this.handler.dispatchToElement(new oe(r,t),"dragleave",t.event),i)&&i!==r&&this.handler.dispatchToElement(new oe(i,t),"dragenter",t.event)},se.prototype._dragEnd=function(t){var e=this._draggingTarget;e&&(e.dragging=!1),this.handler.dispatchToElement(new oe(e,t),"dragend",t.event),this._dropTarget&&this.handler.dispatchToElement(new oe(this._dropTarget,t),"drop",t.event),this._draggingTarget=null,this._dropTarget=null},se);function se(t){(this.handler=t).on("mousedown",this._dragStart,this),t.on("mousemove",this._drag,this),t.on("mouseup",this._dragEnd,this)}ue.prototype.on=function(t,e,n,i){this._$handlers||(this._$handlers={});var r=this._$handlers;if("function"==typeof e&&(i=n,n=e,e=null),n&&t){var o=this._$eventProcessor;null!=e&&o&&o.normalizeQuery&&(e=o.normalizeQuery(e)),r[t]||(r[t]=[]);for(var a=0;a<r[t].length;a++)if(r[t][a].h===n)return this;o={h:n,query:e,ctx:i||this,callAtLast:n.zrEventfulCallAtLast},e=r[t].length-1,i=r[t][e];i&&i.callAtLast?r[t].splice(e,0,o):r[t].push(o)}return this},ue.prototype.isSilent=function(t){var e=this._$handlers;return!e||!e[t]||!e[t].length},ue.prototype.off=function(t,e){var n=this._$handlers;if(n)if(t)if(e){if(n[t]){for(var i=[],r=0,o=n[t].length;r<o;r++)n[t][r].h!==e&&i.push(n[t][r]);n[t]=i}n[t]&&0===n[t].length&&delete n[t]}else delete n[t];else this._$handlers={};return this},ue.prototype.trigger=function(t){for(var e=[],n=1;n<arguments.length;n++)e[n-1]=arguments[n];if(this._$handlers){var i=this._$handlers[t],r=this._$eventProcessor;if(i)for(var o=e.length,a=i.length,s=0;s<a;s++){var l=i[s];if(!r||!r.filter||null==l.query||r.filter(t,l.query))switch(o){case 0:l.h.call(l.ctx);break;case 1:l.h.call(l.ctx,e[0]);break;case 2:l.h.call(l.ctx,e[0],e[1]);break;default:l.h.apply(l.ctx,e)}}r&&r.afterTrigger&&r.afterTrigger(t)}return this},ue.prototype.triggerWithContext=function(t){for(var e=[],n=1;n<arguments.length;n++)e[n-1]=arguments[n];if(this._$handlers){var i=this._$handlers[t],r=this._$eventProcessor;if(i)for(var o=e.length,a=e[o-1],s=i.length,l=0;l<s;l++){var u=i[l];if(!r||!r.filter||null==u.query||r.filter(t,u.query))switch(o){case 0:u.h.call(a);break;case 1:u.h.call(a,e[0]);break;case 2:u.h.call(a,e[0],e[1]);break;default:u.h.apply(a,e.slice(1,o-1))}}r&&r.afterTrigger&&r.afterTrigger(t)}return this};var le=ue;function ue(t){t&&(this._$eventProcessor=t)}var he=Math.log(2);function ce(t,e,n,i,r,o){var a,s=i+"-"+r,l=t.length;if(o.hasOwnProperty(s))return o[s];if(1===e)return a=Math.round(Math.log((1<<l)-1&~r)/he),t[n][a];for(var u=i|1<<n,h=n+1;i&1<<h;)h++;for(var c=0,p=0,d=0;p<l;p++){var f=1<<p;f&r||(c+=(d%2?-1:1)*t[n][p]*ce(t,e-1,h,u,r|f,o),d++)}return o[s]=c}function pe(t,e){var n=[[t[0],t[1],1,0,0,0,-e[0]*t[0],-e[0]*t[1]],[0,0,0,t[0],t[1],1,-e[1]*t[0],-e[1]*t[1]],[t[2],t[3],1,0,0,0,-e[2]*t[2],-e[2]*t[3]],[0,0,0,t[2],t[3],1,-e[3]*t[2],-e[3]*t[3]],[t[4],t[5],1,0,0,0,-e[4]*t[4],-e[4]*t[5]],[0,0,0,t[4],t[5],1,-e[5]*t[4],-e[5]*t[5]],[t[6],t[7],1,0,0,0,-e[6]*t[6],-e[6]*t[7]],[0,0,0,t[6],t[7],1,-e[7]*t[6],-e[7]*t[7]]],i={},r=ce(n,8,0,0,0,i);if(0!==r){for(var o=[],a=0;a<8;a++)for(var s=0;s<8;s++)null==o[s]&&(o[s]=0),o[s]+=((a+s)%2?-1:1)*ce(n,7,0===a?1:0,1<<a,1<<s,i)/r*e[a];return function(t,e,n){var i=e*o[6]+n*o[7]+1;t[0]=(e*o[0]+n*o[1]+o[2])/i,t[1]=(e*o[3]+n*o[4]+o[5])/i}}}var de="___zrEVENTSAVED";function fe(t,e,n,i,r){if(e.getBoundingClientRect&&p.domSupported&&!ge(e)){var o=e[de]||(e[de]={}),e=function(t,e,n){for(var i=n?"invTrans":"trans",r=e[i],o=e.srcCoords,a=[],s=[],l=!0,u=0;u<4;u++){var h=t[u].getBoundingClientRect(),c=2*u,p=h.left,h=h.top;a.push(p,h),l=l&&o&&p===o[c]&&h===o[1+c],s.push(t[u].offsetLeft,t[u].offsetTop)}return l&&r?r:(e.srcCoords=a,e[i]=n?pe(s,a):pe(a,s))}(function(t,e){var n=e.markers;if(!n){n=e.markers=[];for(var i=["left","right"],r=["top","bottom"],o=0;o<4;o++){var a=document.createElement("div"),s=a.style,l=o%2,u=(o>>1)%2;s.cssText=["position: absolute","visibility: hidden","padding: 0","margin: 0","border-width: 0","user-select: none","width:0","height:0",i[l]+":0",r[u]+":0",i[1-l]+":auto",r[1-u]+":auto",""].join("!important;"),t.appendChild(a),n.push(a)}}return n}(e,o),o,r);if(e)return e(t,n,i),1}}function ge(t){return"CANVAS"===t.nodeName.toUpperCase()}var ye=/([&<>"'])/g,me={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"};function ve(t){return null==t?"":(t+"").replace(ye,function(t,e){return me[e]})}var _e=/^(?:mouse|pointer|contextmenu|drag|drop)|click/,xe=[],be=p.browser.firefox&&+p.browser.version.split(".")[0]<39;function we(t,e,n,i){return n=n||{},i?Se(t,e,n):be&&null!=e.layerX&&e.layerX!==e.offsetX?(n.zrX=e.layerX,n.zrY=e.layerY):null!=e.offsetX?(n.zrX=e.offsetX,n.zrY=e.offsetY):Se(t,e,n),n}function Se(t,e,n){if(p.domSupported&&t.getBoundingClientRect){var i,r=e.clientX,e=e.clientY;if(ge(t))return i=t.getBoundingClientRect(),n.zrX=r-i.left,void(n.zrY=e-i.top);if(fe(xe,t,r,e))return n.zrX=xe[0],void(n.zrY=xe[1])}n.zrX=n.zrY=0}function Me(t){return t||window.event}function Te(t,e,n){var i;return null==(e=Me(e)).zrX&&((i=e.type)&&0<=i.indexOf("touch")?(i=("touchend"!==i?e.targetTouches:e.changedTouches)[0])&&we(t,i,e,n):(we(t,e,e,n),i=function(t){var e=t.wheelDelta;if(e)return e;var n=t.deltaX,t=t.deltaY;return null!=n&&null!=t?3*(0!==t?Math.abs(t):Math.abs(n))*(0<t||!(t<0)&&0<n?-1:1):e}(e),e.zrDelta=i?i/120:-(e.detail||0)/3),t=e.button,null==e.which&&void 0!==t&&_e.test(e.type))&&(e.which=1&t?1:2&t?3:4&t?2:0),e}var Ce=function(t){t.preventDefault(),t.stopPropagation(),t.cancelBubble=!0},ke=(Ie.prototype.recognize=function(t,e,n){return this._doTrack(t,e,n),this._recognize(t)},Ie.prototype.clear=function(){return this._track.length=0,this},Ie.prototype._doTrack=function(t,e,n){var i=t.touches;if(i){for(var r={points:[],touches:[],target:e,event:t},o=0,a=i.length;o<a;o++){var s=i[o],l=we(n,s,{});r.points.push([l.zrX,l.zrY]),r.touches.push(s)}this._track.push(r)}},Ie.prototype._recognize=function(t){for(var e in Ae)if(Ae.hasOwnProperty(e)){e=Ae[e](this._track,t);if(e)return e}},Ie);function Ie(){this._track=[]}function De(t){var e=t[1][0]-t[0][0],t=t[1][1]-t[0][1];return Math.sqrt(e*e+t*t)}var Ae={pinch:function(t,e){var n,i=t.length;if(i)return n=(t[i-1]||{}).points,(i=(t[i-2]||{}).points||n)&&1<i.length&&n&&1<n.length?(i=De(n)/De(i),isFinite(i)||(i=1),e.pinchScale=i,i=[(n[0][0]+n[1][0])/2,(n[0][1]+n[1][1])/2],e.pinchX=i[0],e.pinchY=i[1],{type:"pinch",target:t[0].target,event:e}):void 0}};function Le(){return[1,0,0,1,0,0]}function Pe(t){return t[0]=1,t[1]=0,t[2]=0,t[3]=1,t[4]=0,t[5]=0,t}function Oe(t,e){return t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[3],t[4]=e[4],t[5]=e[5],t}function Re(t,e,n){var i=e[0]*n[0]+e[2]*n[1],r=e[1]*n[0]+e[3]*n[1],o=e[0]*n[2]+e[2]*n[3],a=e[1]*n[2]+e[3]*n[3],s=e[0]*n[4]+e[2]*n[5]+e[4],n=e[1]*n[4]+e[3]*n[5]+e[5];return t[0]=i,t[1]=r,t[2]=o,t[3]=a,t[4]=s,t[5]=n,t}function Ne(t,e,n){return t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[3],t[4]=e[4]+n[0],t[5]=e[5]+n[1],t}function Ee(t,e,n,i){void 0===i&&(i=[0,0]);var r=e[0],o=e[2],a=e[4],s=e[1],l=e[3],e=e[5],u=Math.sin(n),n=Math.cos(n);return t[0]=r*n+s*u,t[1]=-r*u+s*n,t[2]=o*n+l*u,t[3]=-o*u+n*l,t[4]=n*(a-i[0])+u*(e-i[1])+i[0],t[5]=n*(e-i[1])-u*(a-i[0])+i[1],t}function ze(t,e,n){var i=n[0],n=n[1];return t[0]=e[0]*i,t[1]=e[1]*n,t[2]=e[2]*i,t[3]=e[3]*n,t[4]=e[4]*i,t[5]=e[5]*n,t}function Be(t,e){var n=e[0],i=e[2],r=e[4],o=e[1],a=e[3],e=e[5],s=n*a-o*i;return s?(t[0]=a*(s=1/s),t[1]=-o*s,t[2]=-i*s,t[3]=n*s,t[4]=(i*e-a*r)*s,t[5]=(o*r-n*e)*s,t):null}var Fe=Object.freeze({__proto__:null,clone:function(t){var e=Le();return Oe(e,t),e},copy:Oe,create:Le,identity:Pe,invert:Be,mul:Re,rotate:Ee,scale:ze,translate:Ne}),M=(e.prototype.copy=function(t){return this.x=t.x,this.y=t.y,this},e.prototype.clone=function(){return new e(this.x,this.y)},e.prototype.set=function(t,e){return this.x=t,this.y=e,this},e.prototype.equal=function(t){return t.x===this.x&&t.y===this.y},e.prototype.add=function(t){return this.x+=t.x,this.y+=t.y,this},e.prototype.scale=function(t){this.x*=t,this.y*=t},e.prototype.scaleAndAdd=function(t,e){this.x+=t.x*e,this.y+=t.y*e},e.prototype.sub=function(t){return this.x-=t.x,this.y-=t.y,this},e.prototype.dot=function(t){return this.x*t.x+this.y*t.y},e.prototype.len=function(){return Math.sqrt(this.x*this.x+this.y*this.y)},e.prototype.lenSquare=function(){return this.x*this.x+this.y*this.y},e.prototype.normalize=function(){var t=this.len();return this.x/=t,this.y/=t,this},e.prototype.distance=function(t){var e=this.x-t.x,t=this.y-t.y;return Math.sqrt(e*e+t*t)},e.prototype.distanceSquare=function(t){var e=this.x-t.x,t=this.y-t.y;return e*e+t*t},e.prototype.negate=function(){return this.x=-this.x,this.y=-this.y,this},e.prototype.transform=function(t){var e,n;if(t)return e=this.x,n=this.y,this.x=t[0]*e+t[2]*n+t[4],this.y=t[1]*e+t[3]*n+t[5],this},e.prototype.toArray=function(t){return t[0]=this.x,t[1]=this.y,t},e.prototype.fromArray=function(t){this.x=t[0],this.y=t[1]},e.set=function(t,e,n){t.x=e,t.y=n},e.copy=function(t,e){t.x=e.x,t.y=e.y},e.len=function(t){return Math.sqrt(t.x*t.x+t.y*t.y)},e.lenSquare=function(t){return t.x*t.x+t.y*t.y},e.dot=function(t,e){return t.x*e.x+t.y*e.y},e.add=function(t,e,n){t.x=e.x+n.x,t.y=e.y+n.y},e.sub=function(t,e,n){t.x=e.x-n.x,t.y=e.y-n.y},e.scale=function(t,e,n){t.x=e.x*n,t.y=e.y*n},e.scaleAndAdd=function(t,e,n,i){t.x=e.x+n.x*i,t.y=e.y+n.y*i},e.lerp=function(t,e,n,i){var r=1-i;t.x=r*e.x+i*n.x,t.y=r*e.y+i*n.y},e);function e(t,e){this.x=t||0,this.y=e||0}var Ve=Math.min,He=Math.max,Ge=new M,We=new M,Ue=new M,Xe=new M,Ye=new M,qe=new M,X=(Ze.prototype.union=function(t){var e=Ve(t.x,this.x),n=Ve(t.y,this.y);isFinite(this.x)&&isFinite(this.width)?this.width=He(t.x+t.width,this.x+this.width)-e:this.width=t.width,isFinite(this.y)&&isFinite(this.height)?this.height=He(t.y+t.height,this.y+this.height)-n:this.height=t.height,this.x=e,this.y=n},Ze.prototype.applyTransform=function(t){Ze.applyTransform(this,this,t)},Ze.prototype.calculateTransform=function(t){var e=t.width/this.width,n=t.height/this.height,i=Le();return Ne(i,i,[-this.x,-this.y]),ze(i,i,[e,n]),Ne(i,i,[t.x,t.y]),i},Ze.prototype.intersect=function(t,e){if(!t)return!1;t instanceof Ze||(t=Ze.create(t));var n,i,r,o,a,s,l,u,h=this,c=h.x,p=h.x+h.width,d=h.y,h=h.y+h.height,f=t.x,g=t.x+t.width,y=t.y,t=t.y+t.height,m=!(p<f||g<c||h<y||t<d);return e&&(n=1/0,i=0,r=Math.abs(p-f),o=Math.abs(g-c),a=Math.abs(h-y),s=Math.abs(t-d),l=Math.min(r,o),u=Math.min(a,s),p<f||g<c?i<l&&(i=l,r<o?M.set(qe,-r,0):M.set(qe,o,0)):l<n&&(n=l,r<o?M.set(Ye,r,0):M.set(Ye,-o,0)),h<y||t<d?i<u&&(i=u,a<s?M.set(qe,0,-a):M.set(qe,0,s)):l<n&&(n=l,a<s?M.set(Ye,0,a):M.set(Ye,0,-s))),e&&M.copy(e,m?Ye:qe),m},Ze.prototype.contain=function(t,e){var n=this;return t>=n.x&&t<=n.x+n.width&&e>=n.y&&e<=n.y+n.height},Ze.prototype.clone=function(){return new Ze(this.x,this.y,this.width,this.height)},Ze.prototype.copy=function(t){Ze.copy(this,t)},Ze.prototype.plain=function(){return{x:this.x,y:this.y,width:this.width,height:this.height}},Ze.prototype.isFinite=function(){return isFinite(this.x)&&isFinite(this.y)&&isFinite(this.width)&&isFinite(this.height)},Ze.prototype.isZero=function(){return 0===this.width||0===this.height},Ze.create=function(t){return new Ze(t.x,t.y,t.width,t.height)},Ze.copy=function(t,e){t.x=e.x,t.y=e.y,t.width=e.width,t.height=e.height},Ze.applyTransform=function(t,e,n){var i,r,o,a;n?n[1]<1e-5&&-1e-5<n[1]&&n[2]<1e-5&&-1e-5<n[2]?(i=n[0],r=n[3],o=n[4],a=n[5],t.x=e.x*i+o,t.y=e.y*r+a,t.width=e.width*i,t.height=e.height*r,t.width<0&&(t.x+=t.width,t.width=-t.width),t.height<0&&(t.y+=t.height,t.height=-t.height)):(Ge.x=Ue.x=e.x,Ge.y=Xe.y=e.y,We.x=Xe.x=e.x+e.width,We.y=Ue.y=e.y+e.height,Ge.transform(n),Xe.transform(n),We.transform(n),Ue.transform(n),t.x=Ve(Ge.x,We.x,Ue.x,Xe.x),t.y=Ve(Ge.y,We.y,Ue.y,Xe.y),o=He(Ge.x,We.x,Ue.x,Xe.x),a=He(Ge.y,We.y,Ue.y,Xe.y),t.width=o-t.x,t.height=a-t.y):t!==e&&Ze.copy(t,e)},Ze);function Ze(t,e,n,i){n<0&&(t+=n,n=-n),i<0&&(e+=i,i=-i),this.x=t,this.y=e,this.width=n,this.height=i}var je="silent";function Ke(){Ce(this.event)}u(Je,$e=le),Je.prototype.dispose=function(){},Je.prototype.setCursor=function(){};var $e,Qe=Je;function Je(){var t=null!==$e&&$e.apply(this,arguments)||this;return t.handler=null,t}var tn,en=function(t,e){this.x=t,this.y=e},nn=["click","dblclick","mousewheel","mouseout","mouseup","mousedown","mousemove","contextmenu"],rn=new X(0,0,0,0),on=(u(an,tn=le),an.prototype.setHandlerProxy=function(e){this.proxy&&this.proxy.dispose(),e&&(O(nn,function(t){e.on&&e.on(t,this[t],this)},this),e.handler=this),this.proxy=e},an.prototype.mousemove=function(t){var e=t.zrX,n=t.zrY,i=ln(this,e,n),r=this._hovered,o=r.target,i=(o&&!o.__zr&&(o=(r=this.findHover(r.x,r.y)).target),this._hovered=i?new en(e,n):this.findHover(e,n)),e=i.target,n=this.proxy;n.setCursor&&n.setCursor(e?e.cursor:"default"),o&&e!==o&&this.dispatchToElement(r,"mouseout",t),this.dispatchToElement(i,"mousemove",t),e&&e!==o&&this.dispatchToElement(i,"mouseover",t)},an.prototype.mouseout=function(t){var e=t.zrEventControl;"only_globalout"!==e&&this.dispatchToElement(this._hovered,"mouseout",t),"no_globalout"!==e&&this.trigger("globalout",{type:"globalout",event:t})},an.prototype.resize=function(){this._hovered=new en(0,0)},an.prototype.dispatch=function(t,e){t=this[t];t&&t.call(this,e)},an.prototype.dispose=function(){this.proxy.dispose(),this.storage=null,this.proxy=null,this.painter=null},an.prototype.setCursorStyle=function(t){var e=this.proxy;e.setCursor&&e.setCursor(t)},an.prototype.dispatchToElement=function(t,e,n){var i=(t=t||{}).target;if(!i||!i.silent){for(var r="on"+e,o={type:e,event:n,target:(t=t).target,topTarget:t.topTarget,cancelBubble:!1,offsetX:n.zrX,offsetY:n.zrY,gestureEvent:n.gestureEvent,pinchX:n.pinchX,pinchY:n.pinchY,pinchScale:n.pinchScale,wheelDelta:n.zrDelta,zrByTouch:n.zrByTouch,which:n.which,stop:Ke};i&&(i[r]&&(o.cancelBubble=!!i[r].call(i,o)),i.trigger(e,o),i=i.__hostTarget||i.parent,!o.cancelBubble););o.cancelBubble||(this.trigger(e,o),this.painter&&this.painter.eachOtherLayer&&this.painter.eachOtherLayer(function(t){"function"==typeof t[r]&&t[r].call(t,o),t.trigger&&t.trigger(e,o)}))}},an.prototype.findHover=function(t,e,n){var i=this.storage.getDisplayList(),r=new en(t,e);if(sn(i,r,t,e,n),this._pointerSize&&!r.target){for(var o=[],a=this._pointerSize,s=a/2,l=new X(t-s,e-s,a,a),u=i.length-1;0<=u;u--){var h=i[u];h===n||h.ignore||h.ignoreCoarsePointer||h.parent&&h.parent.ignoreCoarsePointer||(rn.copy(h.getBoundingRect()),h.transform&&rn.applyTransform(h.transform),rn.intersect(l)&&o.push(h))}if(o.length)for(var c=Math.PI/12,p=2*Math.PI,d=0;d<s;d+=4)for(var f=0;f<p;f+=c)if(sn(o,r,t+d*Math.cos(f),e+d*Math.sin(f),n),r.target)return r}return r},an.prototype.processGesture=function(t,e){this._gestureMgr||(this._gestureMgr=new ke);var n=this._gestureMgr,i=("start"===e&&n.clear(),n.recognize(t,this.findHover(t.zrX,t.zrY,null).target,this.proxy.dom));"end"===e&&n.clear(),i&&(e=i.type,t.gestureEvent=e,(n=new en).target=i.target,this.dispatchToElement(n,e,i.event))},an);function an(t,e,n,i,r){var o=tn.call(this)||this;return o._hovered=new en(0,0),o.storage=t,o.painter=e,o.painterRoot=i,o._pointerSize=r,n=n||new Qe,o.proxy=null,o.setHandlerProxy(n),o._draggingMgr=new ae(o),o}function sn(t,e,n,i,r){for(var o=t.length-1;0<=o;o--){var a=t[o],s=void 0;if(a!==r&&!a.ignore&&(s=function(t,e,n){if(t[t.rectHover?"rectContain":"contain"](e,n)){for(var i=t,r=void 0,o=!1;i;){if(!(o=i.ignoreClip?!0:o)){var a=i.getClipPath();if(a&&!a.contain(e,n))return!1}i.silent&&(r=!0);a=i.__hostTarget,i=a||i.parent}return!r||je}return!1}(a,n,i))&&(e.topTarget||(e.topTarget=a),s!==je)){e.target=a;break}}}function ln(t,e,n){t=t.painter;return e<0||e>t.getWidth()||n<0||n>t.getHeight()}O(["click","mousedown","mouseup","mousewheel","dblclick","contextmenu"],function(a){on.prototype[a]=function(t){var e,n,i=t.zrX,r=t.zrY,o=ln(this,i,r);if("mouseup"===a&&o||(n=(e=this.findHover(i,r)).target),"mousedown"===a)this._downEl=n,this._downPoint=[t.zrX,t.zrY],this._upEl=n;else if("mouseup"===a)this._upEl=n;else if("click"===a){if(this._downEl!==this._upEl||!this._downPoint||4<$t(this._downPoint,[t.zrX,t.zrY]))return;this._downPoint=null}this.dispatchToElement(e,a,t)}});var un=32,hn=7;function cn(t,e,n,i){var r=e+1;if(r===n)return 1;if(i(t[r++],t[e])<0){for(;r<n&&i(t[r],t[r-1])<0;)r++;var o=t,a=e,s=r;for(s--;a<s;){var l=o[a];o[a++]=o[s],o[s--]=l}}else for(;r<n&&0<=i(t[r],t[r-1]);)r++;return r-e}function pn(t,e,n,i,r){for(i===e&&i++;i<n;i++){for(var o,a=t[i],s=e,l=i;s<l;)r(a,t[o=s+l>>>1])<0?l=o:s=1+o;var u=i-s;switch(u){case 3:t[s+3]=t[s+2];case 2:t[s+2]=t[s+1];case 1:t[s+1]=t[s];break;default:for(;0<u;)t[s+u]=t[s+u-1],u--}t[s]=a}}function dn(t,e,n,i,r,o){var a=0,s=0,l=1;if(0<o(t,e[n+r])){for(s=i-r;l<s&&0<o(t,e[n+r+l]);)(l=1+((a=l)<<1))<=0&&(l=s);s<l&&(l=s),a+=r,l+=r}else{for(s=r+1;l<s&&o(t,e[n+r-l])<=0;)(l=1+((a=l)<<1))<=0&&(l=s);i=a,a=r-(l=s<l?s:l),l=r-i}for(a++;a<l;){var u=a+(l-a>>>1);0<o(t,e[n+u])?a=u+1:l=u}return l}function fn(t,e,n,i,r,o){var a=0,s=0,l=1;if(o(t,e[n+r])<0){for(s=r+1;l<s&&o(t,e[n+r-l])<0;)(l=1+((a=l)<<1))<=0&&(l=s);var u=a,a=r-(l=s<l?s:l),l=r-u}else{for(s=i-r;l<s&&0<=o(t,e[n+r+l]);)(l=1+((a=l)<<1))<=0&&(l=s);s<l&&(l=s),a+=r,l+=r}for(a++;a<l;){var h=a+(l-a>>>1);o(t,e[n+h])<0?l=h:a=h+1}return l}function gn(A,L){var P,O,R=hn,N=0,E=[];function e(t){var e=P[t],n=O[t],i=P[t+1],r=O[t+1],t=(O[t]=n+r,t===N-3&&(P[t+1]=P[t+2],O[t+1]=O[t+2]),N--,fn(A[i],A,e,n,0,L));if(e+=t,0!=(n-=t)&&0!==(r=dn(A[e+n-1],A,i,r,r-1,L)))if(n<=r){var o=e,a=n,t=i,s=r,l=0;for(l=0;l<a;l++)E[l]=A[o+l];var u=0,h=t,c=o;if(A[c++]=A[h++],0==--s)for(l=0;l<a;l++)A[c+l]=E[u+l];else if(1===a){for(l=0;l<s;l++)A[c+l]=A[h+l];A[c+s]=E[u]}else{for(var p,d,f,g=R;;){d=p=0,f=!1;do{if(L(A[h],E[u])<0){if(A[c++]=A[h++],d++,(p=0)==--s){f=!0;break}}else if(A[c++]=E[u++],p++,d=0,1==--a){f=!0;break}}while((p|d)<g);if(f)break;do{if(0!==(p=fn(A[h],E,u,a,0,L))){for(l=0;l<p;l++)A[c+l]=E[u+l];if(c+=p,u+=p,(a-=p)<=1){f=!0;break}}if(A[c++]=A[h++],0==--s){f=!0;break}if(0!==(d=dn(E[u],A,h,s,0,L))){for(l=0;l<d;l++)A[c+l]=A[h+l];if(c+=d,h+=d,0===(s-=d)){f=!0;break}}if(A[c++]=E[u++],1==--a){f=!0;break}}while(g--,hn<=p||hn<=d);if(f)break;g<0&&(g=0),g+=2}if((R=g)<1&&(R=1),1===a){for(l=0;l<s;l++)A[c+l]=A[h+l];A[c+s]=E[u]}else{if(0===a)throw new Error;for(l=0;l<a;l++)A[c+l]=E[u+l]}}}else{var y=e,m=n,v=i,_=r,x=0;for(x=0;x<_;x++)E[x]=A[v+x];var b=y+m-1,w=_-1,S=v+_-1,M=0,T=0;if(A[S--]=A[b--],0==--m)for(M=S-(_-1),x=0;x<_;x++)A[M+x]=E[x];else if(1===_){for(T=(S-=m)+1,M=(b-=m)+1,x=m-1;0<=x;x--)A[T+x]=A[M+x];A[S]=E[w]}else{for(var C=R;;){var k=0,I=0,D=!1;do{if(L(E[w],A[b])<0){if(A[S--]=A[b--],k++,(I=0)==--m){D=!0;break}}else if(A[S--]=E[w--],I++,k=0,1==--_){D=!0;break}}while((k|I)<C);if(D)break;do{if(0!==(k=m-fn(E[w],A,y,m,m-1,L))){for(m-=k,T=(S-=k)+1,M=(b-=k)+1,x=k-1;0<=x;x--)A[T+x]=A[M+x];if(0===m){D=!0;break}}if(A[S--]=E[w--],1==--_){D=!0;break}if(0!==(I=_-dn(A[b],E,0,_,_-1,L))){for(_-=I,T=(S-=I)+1,M=(w-=I)+1,x=0;x<I;x++)A[T+x]=E[M+x];if(_<=1){D=!0;break}}if(A[S--]=A[b--],0==--m){D=!0;break}}while(C--,hn<=k||hn<=I);if(D)break;C<0&&(C=0),C+=2}if((R=C)<1&&(R=1),1===_){for(T=(S-=m)+1,M=(b-=m)+1,x=m-1;0<=x;x--)A[T+x]=A[M+x];A[S]=E[w]}else{if(0===_)throw new Error;for(M=S-(_-1),x=0;x<_;x++)A[M+x]=E[x]}}}}return P=[],O=[],{mergeRuns:function(){for(;1<N;){var t=N-2;if(1<=t&&O[t-1]<=O[t]+O[t+1]||2<=t&&O[t-2]<=O[t]+O[t-1])O[t-1]<O[t+1]&&t--;else if(O[t]>O[t+1])break;e(t)}},forceMergeRuns:function(){for(;1<N;){var t=N-2;0<t&&O[t-1]<O[t+1]&&t--,e(t)}},pushRun:function(t,e){P[N]=t,O[N]=e,N+=1}}}function yn(t,e,n,i){var r=(i=i||t.length)-(n=n||0);if(!(r<2)){var o=0;if(r<un)pn(t,n,i,n+(o=cn(t,n,i,e)),e);else{var a,s=gn(t,e),l=function(t){for(var e=0;un<=t;)e|=1&t,t>>=1;return t+e}(r);do{}while((o=cn(t,n,i,e))<l&&(pn(t,n,n+(a=l<(a=r)?l:r),n+o,e),o=a),s.pushRun(n,o),s.mergeRuns(),n+=o,0!==(r-=o));s.forceMergeRuns()}}}var mn=1,vn=4,_n=!1;function xn(){_n||(_n=!0,console.warn("z / z2 / zlevel of displayable is invalid, which may cause unexpected errors"))}function bn(t,e){return t.zlevel===e.zlevel?t.z===e.z?t.z2-e.z2:t.z-e.z:t.zlevel-e.zlevel}Sn.prototype.traverse=function(t,e){for(var n=0;n<this._roots.length;n++)this._roots[n].traverse(t,e)},Sn.prototype.getDisplayList=function(t,e){e=e||!1;var n=this._displayList;return!t&&n.length||this.updateDisplayList(e),n},Sn.prototype.updateDisplayList=function(t){this._displayListLen=0;for(var e=this._roots,n=this._displayList,i=0,r=e.length;i<r;i++)this._updateAndAddDisplayable(e[i],null,t);n.length=this._displayListLen,yn(n,bn)},Sn.prototype._updateAndAddDisplayable=function(t,e,n){if(!t.ignore||n){t.beforeUpdate(),t.update(),t.afterUpdate();var i=t.getClipPath();if(t.ignoreClip)e=null;else if(i){e=e?e.slice():[];for(var r=i,o=t;r;)r.parent=o,r.updateTransform(),e.push(r),r=(o=r).getClipPath()}if(t.childrenRef){for(var a=t.childrenRef(),s=0;s<a.length;s++){var l=a[s];t.__dirty&&(l.__dirty|=mn),this._updateAndAddDisplayable(l,e,n)}t.__dirty=0}else{i=t;e&&e.length?i.__clipPaths=e:i.__clipPaths&&0<i.__clipPaths.length&&(i.__clipPaths=[]),isNaN(i.z)&&(xn(),i.z=0),isNaN(i.z2)&&(xn(),i.z2=0),isNaN(i.zlevel)&&(xn(),i.zlevel=0),this._displayList[this._displayListLen++]=i}i=t.getDecalElement&&t.getDecalElement(),i=(i&&this._updateAndAddDisplayable(i,e,n),t.getTextGuideLine()),i=(i&&this._updateAndAddDisplayable(i,e,n),t.getTextContent());i&&this._updateAndAddDisplayable(i,e,n)}},Sn.prototype.addRoot=function(t){t.__zr&&t.__zr.storage===this||this._roots.push(t)},Sn.prototype.delRoot=function(t){if(t instanceof Array)for(var e=0,n=t.length;e<n;e++)this.delRoot(t[e]);else{var i=k(this._roots,t);0<=i&&this._roots.splice(i,1)}},Sn.prototype.delAllRoots=function(){this._roots=[],this._displayList=[],this._displayListLen=0},Sn.prototype.getRoots=function(){return this._roots},Sn.prototype.dispose=function(){this._displayList=null,this._roots=null};var wn=Sn;function Sn(){this._roots=[],this._displayList=[],this._displayListLen=0,this.displayableSortFunc=bn}var Mn=p.hasGlobalWindow&&(window.requestAnimationFrame&&window.requestAnimationFrame.bind(window)||window.msRequestAnimationFrame&&window.msRequestAnimationFrame.bind(window)||window.mozRequestAnimationFrame||window.webkitRequestAnimationFrame)||function(t){return setTimeout(t,16)},Tn={linear:function(t){return t},quadraticIn:function(t){return t*t},quadraticOut:function(t){return t*(2-t)},quadraticInOut:function(t){return(t*=2)<1?.5*t*t:-.5*(--t*(t-2)-1)},cubicIn:function(t){return t*t*t},cubicOut:function(t){return--t*t*t+1},cubicInOut:function(t){return(t*=2)<1?.5*t*t*t:.5*((t-=2)*t*t+2)},quarticIn:function(t){return t*t*t*t},quarticOut:function(t){return 1- --t*t*t*t},quarticInOut:function(t){return(t*=2)<1?.5*t*t*t*t:-.5*((t-=2)*t*t*t-2)},quinticIn:function(t){return t*t*t*t*t},quinticOut:function(t){return--t*t*t*t*t+1},quinticInOut:function(t){return(t*=2)<1?.5*t*t*t*t*t:.5*((t-=2)*t*t*t*t+2)},sinusoidalIn:function(t){return 1-Math.cos(t*Math.PI/2)},sinusoidalOut:function(t){return Math.sin(t*Math.PI/2)},sinusoidalInOut:function(t){return.5*(1-Math.cos(Math.PI*t))},exponentialIn:function(t){return 0===t?0:Math.pow(1024,t-1)},exponentialOut:function(t){return 1===t?1:1-Math.pow(2,-10*t)},exponentialInOut:function(t){return 0===t?0:1===t?1:(t*=2)<1?.5*Math.pow(1024,t-1):.5*(2-Math.pow(2,-10*(t-1)))},circularIn:function(t){return 1-Math.sqrt(1-t*t)},circularOut:function(t){return Math.sqrt(1- --t*t)},circularInOut:function(t){return(t*=2)<1?-.5*(Math.sqrt(1-t*t)-1):.5*(Math.sqrt(1-(t-=2)*t)+1)},elasticIn:function(t){var e,n=.1;return 0===t?0:1===t?1:(e=!n||n<1?(n=1,.1):.4*Math.asin(1/n)/(2*Math.PI),-(n*Math.pow(2,10*--t)*Math.sin((t-e)*(2*Math.PI)/.4)))},elasticOut:function(t){var e,n=.1;return 0===t?0:1===t?1:(e=!n||n<1?(n=1,.1):.4*Math.asin(1/n)/(2*Math.PI),n*Math.pow(2,-10*t)*Math.sin((t-e)*(2*Math.PI)/.4)+1)},elasticInOut:function(t){var e,n=.1;return 0===t?0:1===t?1:(e=!n||n<1?(n=1,.1):.4*Math.asin(1/n)/(2*Math.PI),(t*=2)<1?n*Math.pow(2,10*--t)*Math.sin((t-e)*(2*Math.PI)/.4)*-.5:n*Math.pow(2,-10*--t)*Math.sin((t-e)*(2*Math.PI)/.4)*.5+1)},backIn:function(t){return t*t*(2.70158*t-1.70158)},backOut:function(t){return--t*t*(2.70158*t+1.70158)+1},backInOut:function(t){var e=2.5949095;return(t*=2)<1?t*t*((1+e)*t-e)*.5:.5*((t-=2)*t*((1+e)*t+e)+2)},bounceIn:function(t){return 1-Tn.bounceOut(1-t)},bounceOut:function(t){return t<1/2.75?7.5625*t*t:t<2/2.75?7.5625*(t-=1.5/2.75)*t+.75:t<2.5/2.75?7.5625*(t-=2.25/2.75)*t+.9375:7.5625*(t-=2.625/2.75)*t+.984375},bounceInOut:function(t){return t<.5?.5*Tn.bounceIn(2*t):.5*Tn.bounceOut(2*t-1)+.5}},Cn=Math.pow,kn=Math.sqrt,In=1e-8,Dn=kn(3),An=1/3,Ln=Gt(),Pn=Gt(),On=Gt();function Rn(t){return-In<t&&t<In}function Nn(t){return In<t||t<-In}function En(t,e,n,i,r){var o=1-r;return o*o*(o*t+3*r*e)+r*r*(r*i+3*o*n)}function zn(t,e,n,i,r){var o=1-r;return 3*(((e-t)*o+2*(n-e)*r)*o+(i-n)*r*r)}function Bn(t,e,n,i,r,o){var a,s,i=i+3*(e-n)-t,n=3*(n-2*e+t),e=3*(e-t),t=t-r,r=n*n-3*i*e,l=n*e-9*i*t,t=e*e-3*n*t,u=0;return Rn(r)&&Rn(l)?Rn(n)?o[0]=0:0<=(a=-e/n)&&a<=1&&(o[u++]=a):Rn(e=l*l-4*r*t)?(s=-(t=l/r)/2,0<=(a=-n/i+t)&&a<=1&&(o[u++]=a),0<=s&&s<=1&&(o[u++]=s)):0<e?(e=r*n+1.5*i*(-l-(t=kn(e))),0<=(a=(-n-((t=(t=r*n+1.5*i*(-l+t))<0?-Cn(-t,An):Cn(t,An))+(e=e<0?-Cn(-e,An):Cn(e,An))))/(3*i))&&a<=1&&(o[u++]=a)):(t=(2*r*n-3*i*l)/(2*kn(r*r*r)),e=Math.acos(t)/3,a=(-n-2*(l=kn(r))*(t=Math.cos(e)))/(3*i),s=(-n+l*(t+Dn*Math.sin(e)))/(3*i),r=(-n+l*(t-Dn*Math.sin(e)))/(3*i),0<=a&&a<=1&&(o[u++]=a),0<=s&&s<=1&&(o[u++]=s),0<=r&&r<=1&&(o[u++]=r)),u}function Fn(t,e,n,i,r){var o,a=6*n-12*e+6*t,i=9*e+3*i-3*t-9*n,n=3*e-3*t,e=0;return Rn(i)?Nn(a)&&0<=(o=-n/a)&&o<=1&&(r[e++]=o):Rn(t=a*a-4*i*n)?r[0]=-a/(2*i):0<t&&(t=(-a-(n=kn(t)))/(2*i),0<=(o=(-a+n)/(2*i))&&o<=1&&(r[e++]=o),0<=t)&&t<=1&&(r[e++]=t),e}function Vn(t,e,n,i,r,o){var a=(e-t)*r+t,e=(n-e)*r+e,n=(i-n)*r+n,s=(e-a)*r+a,e=(n-e)*r+e,r=(e-s)*r+s;o[0]=t,o[1]=a,o[2]=s,o[3]=r,o[4]=r,o[5]=e,o[6]=n,o[7]=i}function Hn(t,e,n,i,r,o,a,s,l,u,h){var c,p,d,f,g=.005,y=1/0;Ln[0]=l,Ln[1]=u;for(var m=0;m<1;m+=.05)Pn[0]=En(t,n,r,a,m),Pn[1]=En(e,i,o,s,m),(d=Jt(Ln,Pn))<y&&(c=m,y=d);for(var y=1/0,v=0;v<32&&!(g<1e-4);v++)p=c+g,Pn[0]=En(t,n,r,a,f=c-g),Pn[1]=En(e,i,o,s,f),d=Jt(Pn,Ln),0<=f&&d<y?(c=f,y=d):(On[0]=En(t,n,r,a,p),On[1]=En(e,i,o,s,p),f=Jt(On,Ln),p<=1&&f<y?(c=p,y=f):g*=.5);return h&&(h[0]=En(t,n,r,a,c),h[1]=En(e,i,o,s,c)),kn(y)}function Gn(t,e,n,i){var r=1-i;return r*(r*t+2*i*e)+i*i*n}function Wn(t,e,n,i){return 2*((1-i)*(e-t)+i*(n-e))}function Un(t,e,n){n=t+n-2*e;return 0==n?.5:(t-e)/n}function Xn(t,e,n,i,r){var o=(e-t)*i+t,e=(n-e)*i+e,i=(e-o)*i+o;r[0]=t,r[1]=o,r[2]=i,r[3]=i,r[4]=e,r[5]=n}function Yn(t,e,n,i,r,o,a,s,l){var u,h=.005,c=1/0;Ln[0]=a,Ln[1]=s;for(var p=0;p<1;p+=.05)Pn[0]=Gn(t,n,r,p),Pn[1]=Gn(e,i,o,p),(y=Jt(Ln,Pn))<c&&(u=p,c=y);for(var c=1/0,d=0;d<32&&!(h<1e-4);d++){var f=u-h,g=u+h,y=(Pn[0]=Gn(t,n,r,f),Pn[1]=Gn(e,i,o,f),Jt(Pn,Ln));0<=f&&y<c?(u=f,c=y):(On[0]=Gn(t,n,r,g),On[1]=Gn(e,i,o,g),f=Jt(On,Ln),g<=1&&f<c?(u=g,c=f):h*=.5)}return l&&(l[0]=Gn(t,n,r,u),l[1]=Gn(e,i,o,u)),kn(c)}var qn=/cubic-bezier\(([0-9,\.e ]+)\)/;function Zn(t){t=t&&qn.exec(t);if(t){var e,t=t[1].split(","),n=+Ct(t[0]),i=+Ct(t[1]),r=+Ct(t[2]),o=+Ct(t[3]);if(!isNaN(n+i+r+o))return e=[],function(t){return t<=0?0:1<=t?1:Bn(0,n,r,1,t,e)&&En(0,i,o,1,e[0])}}}Kn.prototype.step=function(t,e){if(this._inited||(this._startTime=t+this._delay,this._inited=!0),!this._paused){var n=this._life,i=t-this._startTime-this._pausedTime,r=i/n,o=(r<0&&(r=0),r=Math.min(r,1),this.easingFunc),o=o?o(r):r;if(this.onframe(o),1===r){if(!this.loop)return!0;this._startTime=t-i%n,this._pausedTime=0,this.onrestart()}return!1}this._pausedTime+=e},Kn.prototype.pause=function(){this._paused=!0},Kn.prototype.resume=function(){this._paused=!1},Kn.prototype.setEasing=function(t){this.easing=t,this.easingFunc=D(t)?t:Tn[t]||Zn(t)};var jn=Kn;function Kn(t){this._inited=!1,this._startTime=0,this._pausedTime=0,this._paused=!1,this._life=t.life||1e3,this._delay=t.delay||0,this.loop=t.loop||!1,this.onframe=t.onframe||Ft,this.ondestroy=t.ondestroy||Ft,this.onrestart=t.onrestart||Ft,t.easing&&this.setEasing(t.easing)}var $n=function(t){this.value=t},Qn=(Jn.prototype.insert=function(t){t=new $n(t);return this.insertEntry(t),t},Jn.prototype.insertEntry=function(t){this.head?((this.tail.next=t).prev=this.tail,t.next=null,this.tail=t):this.head=this.tail=t,this._len++},Jn.prototype.remove=function(t){var e=t.prev,n=t.next;e?e.next=n:this.head=n,n?n.prev=e:this.tail=e,t.next=t.prev=null,this._len--},Jn.prototype.len=function(){return this._len},Jn.prototype.clear=function(){this.head=this.tail=null,this._len=0},Jn);function Jn(){this._len=0}ei.prototype.put=function(t,e){var n,i,r=this._list,o=this._map,a=null;return null==o[t]&&(i=r.len(),n=this._lastRemovedEntry,i>=this._maxSize&&0<i&&(i=r.head,r.remove(i),delete o[i.key],a=i.value,this._lastRemovedEntry=i),n?n.value=e:n=new $n(e),n.key=t,r.insertEntry(n),o[t]=n),a},ei.prototype.get=function(t){var t=this._map[t],e=this._list;if(null!=t)return t!==e.tail&&(e.remove(t),e.insertEntry(t)),t.value},ei.prototype.clear=function(){this._list.clear(),this._map={}},ei.prototype.len=function(){return this._list.len()};var ti=ei;function ei(t){this._list=new Qn,this._maxSize=10,this._map={},this._maxSize=t}var ni={transparent:[0,0,0,0],aliceblue:[240,248,255,1],antiquewhite:[250,235,215,1],aqua:[0,255,255,1],aquamarine:[127,255,212,1],azure:[240,255,255,1],beige:[245,245,220,1],bisque:[255,228,196,1],black:[0,0,0,1],blanchedalmond:[255,235,205,1],blue:[0,0,255,1],blueviolet:[138,43,226,1],brown:[165,42,42,1],burlywood:[222,184,135,1],cadetblue:[95,158,160,1],chartreuse:[127,255,0,1],chocolate:[210,105,30,1],coral:[255,127,80,1],cornflowerblue:[100,149,237,1],cornsilk:[255,248,220,1],crimson:[220,20,60,1],cyan:[0,255,255,1],darkblue:[0,0,139,1],darkcyan:[0,139,139,1],darkgoldenrod:[184,134,11,1],darkgray:[169,169,169,1],darkgreen:[0,100,0,1],darkgrey:[169,169,169,1],darkkhaki:[189,183,107,1],darkmagenta:[139,0,139,1],darkolivegreen:[85,107,47,1],darkorange:[255,140,0,1],darkorchid:[153,50,204,1],darkred:[139,0,0,1],darksalmon:[233,150,122,1],darkseagreen:[143,188,143,1],darkslateblue:[72,61,139,1],darkslategray:[47,79,79,1],darkslategrey:[47,79,79,1],darkturquoise:[0,206,209,1],darkviolet:[148,0,211,1],deeppink:[255,20,147,1],deepskyblue:[0,191,255,1],dimgray:[105,105,105,1],dimgrey:[105,105,105,1],dodgerblue:[30,144,255,1],firebrick:[178,34,34,1],floralwhite:[255,250,240,1],forestgreen:[34,139,34,1],fuchsia:[255,0,255,1],gainsboro:[220,220,220,1],ghostwhite:[248,248,255,1],gold:[255,215,0,1],goldenrod:[218,165,32,1],gray:[128,128,128,1],green:[0,128,0,1],greenyellow:[173,255,47,1],grey:[128,128,128,1],honeydew:[240,255,240,1],hotpink:[255,105,180,1],indianred:[205,92,92,1],indigo:[75,0,130,1],ivory:[255,255,240,1],khaki:[240,230,140,1],lavender:[230,230,250,1],lavenderblush:[255,240,245,1],lawngreen:[124,252,0,1],lemonchiffon:[255,250,205,1],lightblue:[173,216,230,1],lightcoral:[240,128,128,1],lightcyan:[224,255,255,1],lightgoldenrodyellow:[250,250,210,1],lightgray:[211,211,211,1],lightgreen:[144,238,144,1],lightgrey:[211,211,211,1],lightpink:[255,182,193,1],lightsalmon:[255,160,122,1],lightseagreen:[32,178,170,1],lightskyblue:[135,206,250,1],lightslategray:[119,136,153,1],lightslategrey:[119,136,153,1],lightsteelblue:[176,196,222,1],lightyellow:[255,255,224,1],lime:[0,255,0,1],limegreen:[50,205,50,1],linen:[250,240,230,1],magenta:[255,0,255,1],maroon:[128,0,0,1],mediumaquamarine:[102,205,170,1],mediumblue:[0,0,205,1],mediumorchid:[186,85,211,1],mediumpurple:[147,112,219,1],mediumseagreen:[60,179,113,1],mediumslateblue:[123,104,238,1],mediumspringgreen:[0,250,154,1],mediumturquoise:[72,209,204,1],mediumvioletred:[199,21,133,1],midnightblue:[25,25,112,1],mintcream:[245,255,250,1],mistyrose:[255,228,225,1],moccasin:[255,228,181,1],navajowhite:[255,222,173,1],navy:[0,0,128,1],oldlace:[253,245,230,1],olive:[128,128,0,1],olivedrab:[107,142,35,1],orange:[255,165,0,1],orangered:[255,69,0,1],orchid:[218,112,214,1],palegoldenrod:[238,232,170,1],palegreen:[152,251,152,1],paleturquoise:[175,238,238,1],palevioletred:[219,112,147,1],papayawhip:[255,239,213,1],peachpuff:[255,218,185,1],peru:[205,133,63,1],pink:[255,192,203,1],plum:[221,160,221,1],powderblue:[176,224,230,1],purple:[128,0,128,1],red:[255,0,0,1],rosybrown:[188,143,143,1],royalblue:[65,105,225,1],saddlebrown:[139,69,19,1],salmon:[250,128,114,1],sandybrown:[244,164,96,1],seagreen:[46,139,87,1],seashell:[255,245,238,1],sienna:[160,82,45,1],silver:[192,192,192,1],skyblue:[135,206,235,1],slateblue:[106,90,205,1],slategray:[112,128,144,1],slategrey:[112,128,144,1],snow:[255,250,250,1],springgreen:[0,255,127,1],steelblue:[70,130,180,1],tan:[210,180,140,1],teal:[0,128,128,1],thistle:[216,191,216,1],tomato:[255,99,71,1],turquoise:[64,224,208,1],violet:[238,130,238,1],wheat:[245,222,179,1],white:[255,255,255,1],whitesmoke:[245,245,245,1],yellow:[255,255,0,1],yellowgreen:[154,205,50,1]};function ii(t){return(t=Math.round(t))<0?0:255<t?255:t}function ri(t){return t<0?0:1<t?1:t}function oi(t){return t.length&&"%"===t.charAt(t.length-1)?ii(parseFloat(t)/100*255):ii(parseInt(t,10))}function ai(t){return t.length&&"%"===t.charAt(t.length-1)?ri(parseFloat(t)/100):ri(parseFloat(t))}function si(t,e,n){return n<0?n+=1:1<n&&--n,6*n<1?t+(e-t)*n*6:2*n<1?e:3*n<2?t+(e-t)*(2/3-n)*6:t}function li(t,e,n){return t+(e-t)*n}function ui(t,e,n,i,r){return t[0]=e,t[1]=n,t[2]=i,t[3]=r,t}function hi(t,e){return t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[3],t}var ci=new ti(20),pi=null;function di(t,e){pi&&hi(pi,e),pi=ci.put(t,pi||e.slice())}function fi(t,e){if(t){e=e||[];var n=ci.get(t);if(n)return hi(e,n);n=(t+="").replace(/ /g,"").toLowerCase();if(n in ni)return hi(e,ni[n]),di(t,e),e;var i=n.length;if("#"===n.charAt(0))return 4===i||5===i?0<=(r=parseInt(n.slice(1,4),16))&&r<=4095?(ui(e,(3840&r)>>4|(3840&r)>>8,240&r|(240&r)>>4,15&r|(15&r)<<4,5===i?parseInt(n.slice(4),16)/15:1),di(t,e),e):void ui(e,0,0,0,1):7===i||9===i?0<=(r=parseInt(n.slice(1,7),16))&&r<=16777215?(ui(e,(16711680&r)>>16,(65280&r)>>8,255&r,9===i?parseInt(n.slice(7),16)/255:1),di(t,e),e):void ui(e,0,0,0,1):void 0;var r=n.indexOf("("),o=n.indexOf(")");if(-1!==r&&o+1===i){var i=n.substr(0,r),a=n.substr(r+1,o-(r+1)).split(","),s=1;switch(i){case"rgba":if(4!==a.length)return 3===a.length?ui(e,+a[0],+a[1],+a[2],1):ui(e,0,0,0,1);s=ai(a.pop());case"rgb":return 3<=a.length?(ui(e,oi(a[0]),oi(a[1]),oi(a[2]),3===a.length?s:ai(a[3])),di(t,e),e):void ui(e,0,0,0,1);case"hsla":return 4!==a.length?void ui(e,0,0,0,1):(a[3]=ai(a[3]),gi(a,e),di(t,e),e);case"hsl":return 3!==a.length?void ui(e,0,0,0,1):(gi(a,e),di(t,e),e);default:return}}ui(e,0,0,0,1)}}function gi(t,e){var n=(parseFloat(t[0])%360+360)%360/360,i=ai(t[1]),r=ai(t[2]),i=r<=.5?r*(i+1):r+i-r*i,r=2*r-i;return ui(e=e||[],ii(255*si(r,i,n+1/3)),ii(255*si(r,i,n)),ii(255*si(r,i,n-1/3)),1),4===t.length&&(e[3]=t[3]),e}function yi(t,e){var n=fi(t);if(n){for(var i=0;i<3;i++)n[i]=e<0?n[i]*(1-e)|0:(255-n[i])*e+n[i]|0,255<n[i]?n[i]=255:n[i]<0&&(n[i]=0);return xi(n,4===n.length?"rgba":"rgb")}}function mi(t,e,n){var i,r,o;if(e&&e.length&&0<=t&&t<=1)return n=n||[],t=t*(e.length-1),i=Math.floor(t),o=Math.ceil(t),r=e[i],e=e[o],n[0]=ii(li(r[0],e[0],o=t-i)),n[1]=ii(li(r[1],e[1],o)),n[2]=ii(li(r[2],e[2],o)),n[3]=ri(li(r[3],e[3],o)),n}var vi=mi;function _i(t,e,n){var i,r,o,a;if(e&&e.length&&0<=t&&t<=1)return t=t*(e.length-1),i=Math.floor(t),r=Math.ceil(t),a=fi(e[i]),e=fi(e[r]),a=xi([ii(li(a[0],e[0],o=t-i)),ii(li(a[1],e[1],o)),ii(li(a[2],e[2],o)),ri(li(a[3],e[3],o))],"rgba"),n?{color:a,leftIndex:i,rightIndex:r,value:t}:a}var n=_i;function xi(t,e){var n;if(t&&t.length)return n=t[0]+","+t[1]+","+t[2],"rgba"!==e&&"hsva"!==e&&"hsla"!==e||(n+=","+t[3]),e+"("+n+")"}function bi(t,e){t=fi(t);return t?(.299*t[0]+.587*t[1]+.114*t[2])*t[3]/255+(1-t[3])*e:0}var wi=new ti(100);function Si(t){var e;return V(t)?((e=wi.get(t))||(e=yi(t,-.1),wi.put(t,e)),e):mt(t)?((e=P({},t)).colorStops=B(t.colorStops,function(t){return{offset:t.offset,color:yi(t.color,-.1)}}),e):t}vi=Object.freeze({__proto__:null,fastLerp:mi,fastMapToColor:vi,lerp:_i,lift:yi,liftColor:Si,lum:bi,mapToColor:n,modifyAlpha:function(t,e){if((t=fi(t))&&null!=e)return t[3]=ri(e),xi(t,"rgba")},modifyHSL:function(t,e,n,i){var r=fi(t);if(t)return r=function(t){var e,n,i,r,o,a,s,l,u,h;if(t)return h=t[0]/255,e=t[1]/255,n=t[2]/255,s=Math.min(h,e,n),r=((i=Math.max(h,e,n))+s)/2,0==(u=i-s)?a=o=0:(a=r<.5?u/(i+s):u/(2-i-s),s=((i-h)/6+u/2)/u,l=((i-e)/6+u/2)/u,u=((i-n)/6+u/2)/u,h===i?o=u-l:e===i?o=1/3+s-u:n===i&&(o=2/3+l-s),o<0&&(o+=1),1<o&&--o),h=[360*o,a,r],null!=t[3]&&h.push(t[3]),h}(r),null!=e&&(r[0]=(t=e,(t=Math.round(t))<0?0:360<t?360:t)),null!=n&&(r[1]=ai(n)),null!=i&&(r[2]=ai(i)),xi(gi(r),"rgba")},parse:fi,random:function(){return xi([Math.round(255*Math.random()),Math.round(255*Math.random()),Math.round(255*Math.random())],"rgb")},stringify:xi,toHex:function(t){if(t=fi(t))return((1<<24)+(t[0]<<16)+(t[1]<<8)+ +t[2]).toString(16).slice(1)}});p.hasGlobalWindow&&D(window.btoa);var Mi=Array.prototype.slice;function Ti(t,e,n){return(e-t)*n+t}function Ci(t,e,n,i){for(var r=e.length,o=0;o<r;o++)t[o]=Ti(e[o],n[o],i);return t}function ki(t,e,n,i){for(var r=e.length,o=0;o<r;o++)t[o]=e[o]+n[o]*i;return t}function Ii(t,e,n,i){for(var r=e.length,o=r&&e[0].length,a=0;a<r;a++){t[a]||(t[a]=[]);for(var s=0;s<o;s++)t[a][s]=e[a][s]+n[a][s]*i}return t}function Di(t){if(st(t)){var e=t.length;if(st(t[0])){for(var n=[],i=0;i<e;i++)n.push(Mi.call(t[i]));return n}return Mi.call(t)}return t}function Ai(t){return t[0]=Math.floor(t[0])||0,t[1]=Math.floor(t[1])||0,t[2]=Math.floor(t[2])||0,t[3]=null==t[3]?1:t[3],"rgba("+t.join(",")+")"}function Li(t){return 4===t||5===t}function Pi(t){return 1===t||2===t}var Oi=[0,0,0,0],Ri=(Ni.prototype.isFinished=function(){return this._finished},Ni.prototype.setFinished=function(){this._finished=!0,this._additiveTrack&&this._additiveTrack.setFinished()},Ni.prototype.needsAnimate=function(){return 1<=this.keyframes.length},Ni.prototype.getAdditiveTrack=function(){return this._additiveTrack},Ni.prototype.addKeyframe=function(t,e,n){this._needsSort=!0;var i,r=this.keyframes,o=r.length,a=!1,s=6,l=e,u=(st(e)?(1==(s=i=st((i=e)&&i[0])?2:1)&&!dt(e[0])||2==i&&!dt(e[0][0]))&&(a=!0):dt(e)&&!xt(e)?s=0:V(e)?isNaN(+e)?(i=fi(e))&&(l=i,s=3):s=0:mt(e)&&((u=P({},l)).colorStops=B(e.colorStops,function(t){return{offset:t.offset,color:fi(t.color)}}),"linear"===e.type?s=4:"radial"===e.type&&(s=5),l=u),0===o?this.valType=s:s===this.valType&&6!==s||(a=!0),this.discrete=this.discrete||a,{time:t,value:l,rawValue:e,percent:0});return n&&(u.easing=n,u.easingFunc=D(n)?n:Tn[n]||Zn(n)),r.push(u),u},Ni.prototype.prepare=function(t,e){for(var n=this.keyframes,i=(this._needsSort&&n.sort(function(t,e){return t.time-e.time}),this.valType),r=n.length,o=n[r-1],a=this.discrete,s=Pi(i),l=Li(i),u=0;u<r;u++){var h=n[u],c=h.value,p=o.value;if(h.percent=h.time/t,!a)if(s&&u!==r-1){x=_=v=m=y=g=f=d=h=void 0;var d=p,f=i,g=h=c,y=d;if(g.push&&y.push){var h=g.length,m=y.length;if(h!==m)if(m<h)g.length=m;else for(var v=h;v<m;v++)g.push(1===f?y[v]:Mi.call(y[v]));for(var _=g[0]&&g[0].length,v=0;v<g.length;v++)if(1===f)isNaN(g[v])&&(g[v]=y[v]);else for(var x=0;x<_;x++)isNaN(g[v][x])&&(g[v][x]=y[v][x])}}else if(l){T=M=S=w=b=h=d=void 0;for(var d=c.colorStops,h=p.colorStops,b=d.length,w=h.length,S=w<b?h:d,h=Math.min(b,w),M=S[h-1]||{color:[0,0,0,0],offset:0},T=h;T<Math.max(b,w);T++)S.push({offset:M.offset,color:M.color.slice()})}}if(!a&&5!==i&&e&&this.needsAnimate()&&e.needsAnimate()&&i===e.valType&&!e._finished){this._additiveTrack=e;for(var C=n[0].value,u=0;u<r;u++)0===i?n[u].additiveValue=n[u].value-C:3===i?n[u].additiveValue=ki([],n[u].value,C,-1):Pi(i)&&(n[u].additiveValue=(1===i?ki:Ii)([],n[u].value,C,-1))}},Ni.prototype.step=function(t,e){if(!this._finished){this._additiveTrack&&this._additiveTrack._finished&&(this._additiveTrack=null);var n,i,r,o,a=null!=this._additiveTrack,s=a?"additiveValue":"value",l=this.valType,u=this.keyframes,h=u.length,c=this.propName,p=3===l,d=this._lastFr,f=Math.min;if(1===h)n=i=u[0];else{if(e<0)g=0;else if(e<this._lastFrP){for(var g=f(d+1,h-1);0<=g&&!(u[g].percent<=e);g--);g=f(g,h-2)}else{for(g=d;g<h&&!(u[g].percent>e);g++);g=f(g-1,h-2)}i=u[g+1],n=u[g]}n&&i&&(this._lastFr=g,this._lastFrP=e,d=i.percent-n.percent,r=0==d?1:f((e-n.percent)/d,1),i.easingFunc&&(r=i.easingFunc(r)),f=a?this._additiveValue:p?Oi:t[c],(Pi(l)||p)&&(f=f||(this._additiveValue=[])),this.discrete?t[c]=(r<1?n:i).rawValue:Pi(l)?(1===l?Ci:function(t,e,n,i){for(var r=e.length,o=r&&e[0].length,a=0;a<r;a++){t[a]||(t[a]=[]);for(var s=0;s<o;s++)t[a][s]=Ti(e[a][s],n[a][s],i)}})(f,n[s],i[s],r):Li(l)?(d=n[s],o=i[s],t[c]={type:(l=4===l)?"linear":"radial",x:Ti(d.x,o.x,r),y:Ti(d.y,o.y,r),colorStops:B(d.colorStops,function(t,e){e=o.colorStops[e];return{offset:Ti(t.offset,e.offset,r),color:Ai(Ci([],t.color,e.color,r))}}),global:o.global},l?(t[c].x2=Ti(d.x2,o.x2,r),t[c].y2=Ti(d.y2,o.y2,r)):t[c].r=Ti(d.r,o.r,r)):p?(Ci(f,n[s],i[s],r),a||(t[c]=Ai(f))):(l=Ti(n[s],i[s],r),a?this._additiveValue=l:t[c]=l),a)&&this._addToTarget(t)}},Ni.prototype._addToTarget=function(t){var e=this.valType,n=this.propName,i=this._additiveValue;0===e?t[n]=t[n]+i:3===e?(fi(t[n],Oi),ki(Oi,Oi,i,1),t[n]=Ai(Oi)):1===e?ki(t[n],t[n],i,1):2===e&&Ii(t[n],t[n],i,1)},Ni);function Ni(t){this.keyframes=[],this.discrete=!1,this._invalid=!1,this._needsSort=!1,this._lastFr=0,this._lastFrP=0,this.propName=t}i.prototype.getMaxTime=function(){return this._maxTime},i.prototype.getDelay=function(){return this._delay},i.prototype.getLoop=function(){return this._loop},i.prototype.getTarget=function(){return this._target},i.prototype.changeTarget=function(t){this._target=t},i.prototype.when=function(t,e,n){return this.whenWithKeys(t,e,I(e),n)},i.prototype.whenWithKeys=function(t,e,n,i){for(var r=this._tracks,o=0;o<n.length;o++){var a=n[o];if(!(l=r[a])){var s,l=r[a]=new Ri(a),u=void 0,h=this._getAdditiveTrack(a);if(h?(u=(s=(s=h.keyframes)[s.length-1])&&s.value,3===h.valType&&(u=u&&Ai(u))):u=this._target[a],null==u)continue;0<t&&l.addKeyframe(0,Di(u),i),this._trackKeys.push(a)}l.addKeyframe(t,Di(e[a]),i)}return this._maxTime=Math.max(this._maxTime,t),this},i.prototype.pause=function(){this._clip.pause(),this._paused=!0},i.prototype.resume=function(){this._clip.resume(),this._paused=!1},i.prototype.isPaused=function(){return!!this._paused},i.prototype.duration=function(t){return this._maxTime=t,this._force=!0,this},i.prototype._doneCallback=function(){this._setTracksFinished(),this._clip=null;var t=this._doneCbs;if(t)for(var e=t.length,n=0;n<e;n++)t[n].call(this)},i.prototype._abortedCallback=function(){this._setTracksFinished();var t=this.animation,e=this._abortedCbs;if(t&&t.removeClip(this._clip),this._clip=null,e)for(var n=0;n<e.length;n++)e[n].call(this)},i.prototype._setTracksFinished=function(){for(var t=this._tracks,e=this._trackKeys,n=0;n<e.length;n++)t[e[n]].setFinished()},i.prototype._getAdditiveTrack=function(t){var e,n=this._additiveAnimators;if(n)for(var i=0;i<n.length;i++){var r=n[i].getTrack(t);r&&(e=r)}return e},i.prototype.start=function(t){if(!(0<this._started)){this._started=1;for(var e,o=this,a=[],n=this._maxTime||0,i=0;i<this._trackKeys.length;i++){var r=this._trackKeys[i],s=this._tracks[r],r=this._getAdditiveTrack(r),l=s.keyframes,u=l.length;s.prepare(n,r),s.needsAnimate()&&(!this._allowDiscrete&&s.discrete?((r=l[u-1])&&(o._target[s.propName]=r.rawValue),s.setFinished()):a.push(s))}return a.length||this._force?(e=new jn({life:n,loop:this._loop,delay:this._delay||0,onframe:function(t){o._started=2;var e=o._additiveAnimators;if(e){for(var n=!1,i=0;i<e.length;i++)if(e[i]._clip){n=!0;break}n||(o._additiveAnimators=null)}for(i=0;i<a.length;i++)a[i].step(o._target,t);var r=o._onframeCbs;if(r)for(i=0;i<r.length;i++)r[i](o._target,t)},ondestroy:function(){o._doneCallback()}}),this._clip=e,this.animation&&this.animation.addClip(e),t&&e.setEasing(t)):this._doneCallback(),this}},i.prototype.stop=function(t){var e;this._clip&&(e=this._clip,t&&e.onframe(1),this._abortedCallback())},i.prototype.delay=function(t){return this._delay=t,this},i.prototype.during=function(t){return t&&(this._onframeCbs||(this._onframeCbs=[]),this._onframeCbs.push(t)),this},i.prototype.done=function(t){return t&&(this._doneCbs||(this._doneCbs=[]),this._doneCbs.push(t)),this},i.prototype.aborted=function(t){return t&&(this._abortedCbs||(this._abortedCbs=[]),this._abortedCbs.push(t)),this},i.prototype.getClip=function(){return this._clip},i.prototype.getTrack=function(t){return this._tracks[t]},i.prototype.getTracks=function(){var e=this;return B(this._trackKeys,function(t){return e._tracks[t]})},i.prototype.stopTracks=function(t,e){if(!t.length||!this._clip)return!0;for(var n=this._tracks,i=this._trackKeys,r=0;r<t.length;r++){var o=n[t[r]];o&&!o.isFinished()&&(e?o.step(this._target,1):1===this._started&&o.step(this._target,0),o.setFinished())}for(var a=!0,r=0;r<i.length;r++)if(!n[i[r]].isFinished()){a=!1;break}return a&&this._abortedCallback(),a},i.prototype.saveTo=function(t,e,n){if(t){e=e||this._trackKeys;for(var i=0;i<e.length;i++){var r=e[i],o=this._tracks[r];o&&!o.isFinished()&&(o=(o=o.keyframes)[n?0:o.length-1])&&(t[r]=Di(o.rawValue))}}},i.prototype.__changeFinalValue=function(t,e){e=e||I(t);for(var n=0;n<e.length;n++){var i,r=e[n],o=this._tracks[r];o&&1<(i=o.keyframes).length&&(i=i.pop(),o.addKeyframe(i.time,t[r]),o.prepare(this._maxTime,o.getAdditiveTrack()))}};var Ei=i;function i(t,e,n,i){this._tracks={},this._trackKeys=[],this._maxTime=0,this._started=0,this._clip=null,this._target=t,(this._loop=e)&&i?it("Can' use additive animation on looped animation."):(this._additiveAnimators=i,this._allowDiscrete=n)}function zi(){return(new Date).getTime()}u(Vi,Bi=le),Vi.prototype.addClip=function(t){t.animation&&this.removeClip(t),this._head?((this._tail.next=t).prev=this._tail,t.next=null,this._tail=t):this._head=this._tail=t,t.animation=this},Vi.prototype.addAnimator=function(t){t.animation=this;t=t.getClip();t&&this.addClip(t)},Vi.prototype.removeClip=function(t){var e,n;t.animation&&(e=t.prev,n=t.next,e?e.next=n:this._head=n,n?n.prev=e:this._tail=e,t.next=t.prev=t.animation=null)},Vi.prototype.removeAnimator=function(t){var e=t.getClip();e&&this.removeClip(e),t.animation=null},Vi.prototype.update=function(t){for(var e=zi()-this._pausedTime,n=e-this._time,i=this._head;i;)var r=i.next,i=(i.step(e,n)&&(i.ondestroy(),this.removeClip(i)),r);this._time=e,t||(this.trigger("frame",n),this.stage.update&&this.stage.update())},Vi.prototype._startLoop=function(){var e=this;this._running=!0,Mn(function t(){e._running&&(Mn(t),e._paused||e.update())})},Vi.prototype.start=function(){this._running||(this._time=zi(),this._pausedTime=0,this._startLoop())},Vi.prototype.stop=function(){this._running=!1},Vi.prototype.pause=function(){this._paused||(this._pauseStart=zi(),this._paused=!0)},Vi.prototype.resume=function(){this._paused&&(this._pausedTime+=zi()-this._pauseStart,this._paused=!1)},Vi.prototype.clear=function(){for(var t=this._head;t;){var e=t.next;t.prev=t.next=t.animation=null,t=e}this._head=this._tail=null},Vi.prototype.isFinished=function(){return null==this._head},Vi.prototype.animate=function(t,e){e=e||{},this.start();t=new Ei(t,e.loop);return this.addAnimator(t),t};var Bi,Fi=Vi;function Vi(t){var e=Bi.call(this)||this;return e._running=!1,e._time=0,e._pausedTime=0,e._pauseStart=0,e._paused=!1,e.stage=(t=t||{}).stage||{},e}var Hi,Gi=p.domSupported,Wi=(Hi={pointerdown:1,pointerup:1,pointermove:1,pointerout:1},{mouse:n=["click","dblclick","mousewheel","wheel","mouseout","mouseup","mousedown","mousemove","contextmenu"],touch:["touchstart","touchend","touchmove"],pointer:B(n,function(t){var e=t.replace("mouse","pointer");return Hi.hasOwnProperty(e)?e:t})}),Ui=["mousemove","mouseup"],Xi=["pointermove","pointerup"],Yi=!1;function qi(t){t=t.pointerType;return"pen"===t||"touch"===t}function Zi(t){t&&(t.zrByTouch=!0)}function ji(t,e){for(var n=e,i=!1;n&&9!==n.nodeType&&!(i=n.domBelongToZr||n!==e&&n===t.painterRoot);)n=n.parentNode;return i}var Ki=function(t,e){this.stopPropagation=Ft,this.stopImmediatePropagation=Ft,this.preventDefault=Ft,this.type=e.type,this.target=this.currentTarget=t.dom,this.pointerType=e.pointerType,this.clientX=e.clientX,this.clientY=e.clientY},$i={mousedown:function(t){t=Te(this.dom,t),this.__mayPointerCapture=[t.zrX,t.zrY],this.trigger("mousedown",t)},mousemove:function(t){t=Te(this.dom,t);var e=this.__mayPointerCapture;!e||t.zrX===e[0]&&t.zrY===e[1]||this.__togglePointerCapture(!0),this.trigger("mousemove",t)},mouseup:function(t){t=Te(this.dom,t),this.__togglePointerCapture(!1),this.trigger("mouseup",t)},mouseout:function(t){ji(this,(t=Te(this.dom,t)).toElement||t.relatedTarget)||(this.__pointerCapturing&&(t.zrEventControl="no_globalout"),this.trigger("mouseout",t))},wheel:function(t){Yi=!0,t=Te(this.dom,t),this.trigger("mousewheel",t)},mousewheel:function(t){Yi||(t=Te(this.dom,t),this.trigger("mousewheel",t))},touchstart:function(t){Zi(t=Te(this.dom,t)),this.__lastTouchMoment=new Date,this.handler.processGesture(t,"start"),$i.mousemove.call(this,t),$i.mousedown.call(this,t)},touchmove:function(t){Zi(t=Te(this.dom,t)),this.handler.processGesture(t,"change"),$i.mousemove.call(this,t)},touchend:function(t){Zi(t=Te(this.dom,t)),this.handler.processGesture(t,"end"),$i.mouseup.call(this,t),+new Date-+this.__lastTouchMoment<300&&$i.click.call(this,t)},pointerdown:function(t){$i.mousedown.call(this,t)},pointermove:function(t){qi(t)||$i.mousemove.call(this,t)},pointerup:function(t){$i.mouseup.call(this,t)},pointerout:function(t){qi(t)||$i.mouseout.call(this,t)}},Qi=(O(["click","dblclick","contextmenu"],function(e){$i[e]=function(t){t=Te(this.dom,t),this.trigger(e,t)}}),{pointermove:function(t){qi(t)||Qi.mousemove.call(this,t)},pointerup:function(t){Qi.mouseup.call(this,t)},mousemove:function(t){this.trigger("mousemove",t)},mouseup:function(t){var e=this.__pointerCapturing;this.__togglePointerCapture(!1),this.trigger("mouseup",t),e&&(t.zrEventControl="only_globalout",this.trigger("mouseout",t))}});function Ji(i,r){var o=r.domHandlers;p.pointerEventsSupported?O(Wi.pointer,function(e){er(r,e,function(t){o[e].call(i,t)})}):(p.touchEventsSupported&&O(Wi.touch,function(n){er(r,n,function(t){var e;o[n].call(i,t),(e=r).touching=!0,null!=e.touchTimer&&(clearTimeout(e.touchTimer),e.touchTimer=null),e.touchTimer=setTimeout(function(){e.touching=!1,e.touchTimer=null},700)})}),O(Wi.mouse,function(e){er(r,e,function(t){t=Me(t),r.touching||o[e].call(i,t)})}))}function tr(i,r){function t(n){er(r,n,function(t){var e;t=Me(t),ji(i,t.target)||(e=t,t=Te(i.dom,new Ki(i,e),!0),r.domHandlers[n].call(i,t))},{capture:!0})}p.pointerEventsSupported?O(Xi,t):p.touchEventsSupported||O(Ui,t)}function er(t,e,n,i){t.mounted[e]=n,t.listenerOpts[e]=i,t.domTarget.addEventListener(e,n,i)}function nr(t){var e,n,i,r,o,a=t.mounted;for(e in a)a.hasOwnProperty(e)&&(n=t.domTarget,r=a[i=e],o=t.listenerOpts[e],n.removeEventListener(i,r,o));t.mounted={}}var ir,rr=function(t,e){this.mounted={},this.listenerOpts={},this.touching=!1,this.domTarget=t,this.domHandlers=e},or=(u(ar,ir=le),ar.prototype.dispose=function(){nr(this._localHandlerScope),Gi&&nr(this._globalHandlerScope)},ar.prototype.setCursor=function(t){this.dom.style&&(this.dom.style.cursor=t||"default")},ar.prototype.__togglePointerCapture=function(t){var e;this.__mayPointerCapture=null,Gi&&+this.__pointerCapturing^+t&&(this.__pointerCapturing=t,e=this._globalHandlerScope,t?tr(this,e):nr(e))},ar);function ar(t,e){var n=ir.call(this)||this;return n.__pointerCapturing=!1,n.dom=t,n.painterRoot=e,n._localHandlerScope=new rr(t,$i),Gi&&(n._globalHandlerScope=new rr(document,Qi)),Ji(n,n._localHandlerScope),n}var n=1,sr=n=p.hasGlobalWindow?Math.max(window.devicePixelRatio||window.screen&&window.screen.deviceXDPI/window.screen.logicalXDPI||1,1):n,lr="#333",ur="#ccc",hr=Pe;function cr(t){return 5e-5<t||t<-5e-5}var pr=[],dr=[],fr=Le(),gr=Math.abs,yr=(mr.prototype.getLocalTransform=function(t){return mr.getLocalTransform(this,t)},mr.prototype.setPosition=function(t){this.x=t[0],this.y=t[1]},mr.prototype.setScale=function(t){this.scaleX=t[0],this.scaleY=t[1]},mr.prototype.setSkew=function(t){this.skewX=t[0],this.skewY=t[1]},mr.prototype.setOrigin=function(t){this.originX=t[0],this.originY=t[1]},mr.prototype.needLocalTransform=function(){return cr(this.rotation)||cr(this.x)||cr(this.y)||cr(this.scaleX-1)||cr(this.scaleY-1)||cr(this.skewX)||cr(this.skewY)},mr.prototype.updateTransform=function(){var t=this.parent&&this.parent.transform,e=this.needLocalTransform(),n=this.transform;e||t?(n=n||Le(),e?this.getLocalTransform(n):hr(n),t&&(e?Re(n,t,n):Oe(n,t)),this.transform=n,this._resolveGlobalScaleRatio(n)):n&&(hr(n),this.invTransform=null)},mr.prototype._resolveGlobalScaleRatio=function(t){var e,n,i=this.globalScaleRatio;null!=i&&1!==i&&(this.getGlobalScale(pr),n=((pr[1]-(n=pr[1]<0?-1:1))*i+n)/pr[1]||0,t[0]*=i=((pr[0]-(e=pr[0]<0?-1:1))*i+e)/pr[0]||0,t[1]*=i,t[2]*=n,t[3]*=n),this.invTransform=this.invTransform||Le(),Be(this.invTransform,t)},mr.prototype.getComputedTransform=function(){for(var t=this,e=[];t;)e.push(t),t=t.parent;for(;t=e.pop();)t.updateTransform();return this.transform},mr.prototype.setLocalTransform=function(t){var e,n,i,r;t&&(r=t[0]*t[0]+t[1]*t[1],i=t[2]*t[2]+t[3]*t[3],e=Math.atan2(t[1],t[0]),n=Math.PI/2+e-Math.atan2(t[3],t[2]),i=Math.sqrt(i)*Math.cos(n),r=Math.sqrt(r),this.skewX=n,this.skewY=0,this.rotation=-e,this.x=+t[4],this.y=+t[5],this.scaleX=r,this.scaleY=i,this.originX=0,this.originY=0)},mr.prototype.decomposeTransform=function(){var t,e,n;this.transform&&(e=this.parent,t=this.transform,e&&e.transform&&(e.invTransform=e.invTransform||Le(),Re(dr,e.invTransform,t),t=dr),e=this.originX,n=this.originY,(e||n)&&(fr[4]=e,fr[5]=n,Re(dr,t,fr),dr[4]-=e,dr[5]-=n,t=dr),this.setLocalTransform(t))},mr.prototype.getGlobalScale=function(t){var e=this.transform;return t=t||[],e?(t[0]=Math.sqrt(e[0]*e[0]+e[1]*e[1]),t[1]=Math.sqrt(e[2]*e[2]+e[3]*e[3]),e[0]<0&&(t[0]=-t[0]),e[3]<0&&(t[1]=-t[1])):(t[0]=1,t[1]=1),t},mr.prototype.transformCoordToLocal=function(t,e){t=[t,e],e=this.invTransform;return e&&ee(t,t,e),t},mr.prototype.transformCoordToGlobal=function(t,e){t=[t,e],e=this.transform;return e&&ee(t,t,e),t},mr.prototype.getLineScale=function(){var t=this.transform;return t&&1e-10<gr(t[0]-1)&&1e-10<gr(t[3]-1)?Math.sqrt(gr(t[0]*t[3]-t[2]*t[1])):1},mr.prototype.copyTransform=function(t){for(var e=this,n=t,i=0;i<vr.length;i++){var r=vr[i];e[r]=n[r]}},mr.getLocalTransform=function(t,e){e=e||[];var n=t.originX||0,i=t.originY||0,r=t.scaleX,o=t.scaleY,a=t.anchorX,s=t.anchorY,l=t.rotation||0,u=t.x,h=t.y,c=t.skewX?Math.tan(t.skewX):0,t=t.skewY?Math.tan(-t.skewY):0;return n||i||a||s?(e[4]=-(a=n+a)*r-c*(s=i+s)*o,e[5]=-s*o-t*a*r):e[4]=e[5]=0,e[0]=r,e[3]=o,e[1]=t*r,e[2]=c*o,l&&Ee(e,e,l),e[4]+=n+u,e[5]+=i+h,e},mr.initDefaultProps=((n=mr.prototype).scaleX=n.scaleY=n.globalScaleRatio=1,void(n.x=n.y=n.originX=n.originY=n.skewX=n.skewY=n.rotation=n.anchorX=n.anchorY=0)),mr);function mr(){}var vr=["x","y","originX","originY","anchorX","anchorY","rotation","scaleX","scaleY","skewX","skewY"];var _r={};function xr(t,e){var n=_r[e=e||K],i=(n=n||(_r[e]=new ti(500))).get(t);return null==i&&(i=H.measureText(t,e).width,n.put(t,i)),i}function br(t,e,n,i){t=xr(t,e),e=Tr(e),n=Sr(0,t,n),i=Mr(0,e,i);return new X(n,i,t,e)}function wr(t,e,n,i){var r=((t||"")+"").split("\n");if(1===r.length)return br(r[0],e,n,i);for(var o=new X(0,0,0,0),a=0;a<r.length;a++){var s=br(r[a],e,n,i);0===a?o.copy(s):o.union(s)}return o}function Sr(t,e,n){return"right"===n?t-=e:"center"===n&&(t-=e/2),t}function Mr(t,e,n){return"middle"===n?t-=e/2:"bottom"===n&&(t-=e),t}function Tr(t){return xr("国",t)}function Cr(t,e){return"string"==typeof t?0<=t.lastIndexOf("%")?parseFloat(t)/100*e:parseFloat(t):t}function kr(t,e,n){var i=e.position||"inside",r=null!=e.distance?e.distance:5,o=n.height,a=n.width,s=o/2,l=n.x,u=n.y,h="left",c="top";if(i instanceof Array)l+=Cr(i[0],n.width),u+=Cr(i[1],n.height),c=h=null;else switch(i){case"left":l-=r,u+=s,h="right",c="middle";break;case"right":l+=r+a,u+=s,c="middle";break;case"top":l+=a/2,u-=r,h="center",c="bottom";break;case"bottom":l+=a/2,u+=o+r,h="center";break;case"inside":l+=a/2,u+=s,h="center",c="middle";break;case"insideLeft":l+=r,u+=s,c="middle";break;case"insideRight":l+=a-r,u+=s,h="right",c="middle";break;case"insideTop":l+=a/2,u+=r,h="center";break;case"insideBottom":l+=a/2,u+=o-r,h="center",c="bottom";break;case"insideTopLeft":l+=r,u+=r;break;case"insideTopRight":l+=a-r,u+=r,h="right";break;case"insideBottomLeft":l+=r,u+=o-r,c="bottom";break;case"insideBottomRight":l+=a-r,u+=o-r,h="right",c="bottom"}return(t=t||{}).x=l,t.y=u,t.align=h,t.verticalAlign=c,t}var Ir,Dr="__zr_normal__",Ar=vr.concat(["ignore"]),Lr=lt(vr,function(t,e){return t[e]=!0,t},{ignore:!1}),Pr={},Or=new X(0,0,0,0),n=(r.prototype._init=function(t){this.attr(t)},r.prototype.drift=function(t,e,n){switch(this.draggable){case"horizontal":e=0;break;case"vertical":t=0}var i=this.transform;(i=i||(this.transform=[1,0,0,1,0,0]))[4]+=t,i[5]+=e,this.decomposeTransform(),this.markRedraw()},r.prototype.beforeUpdate=function(){},r.prototype.afterUpdate=function(){},r.prototype.update=function(){this.updateTransform(),this.__dirty&&this.updateInnerText()},r.prototype.updateInnerText=function(t){var e,n,i,r,o,a,s,l,u,h,c=this._textContent;!c||c.ignore&&!t||(this.textConfig||(this.textConfig={}),l=(t=this.textConfig).local,i=n=void 0,r=!1,(e=c.innerTransformable).parent=l?this:null,h=!1,e.copyTransform(c),null!=t.position&&(u=Or,t.layoutRect?u.copy(t.layoutRect):u.copy(this.getBoundingRect()),l||u.applyTransform(this.transform),this.calculateTextPosition?this.calculateTextPosition(Pr,t,u):kr(Pr,t,u),e.x=Pr.x,e.y=Pr.y,n=Pr.align,i=Pr.verticalAlign,o=t.origin)&&null!=t.rotation&&(s=a=void 0,s="center"===o?(a=.5*u.width,.5*u.height):(a=Cr(o[0],u.width),Cr(o[1],u.height)),h=!0,e.originX=-e.x+a+(l?0:u.x),e.originY=-e.y+s+(l?0:u.y)),null!=t.rotation&&(e.rotation=t.rotation),(o=t.offset)&&(e.x+=o[0],e.y+=o[1],h||(e.originX=-o[0],e.originY=-o[1])),a=null==t.inside?"string"==typeof t.position&&0<=t.position.indexOf("inside"):t.inside,s=this._innerTextDefaultStyle||(this._innerTextDefaultStyle={}),h=u=l=void 0,a&&this.canBeInsideText()?(l=t.insideFill,u=t.insideStroke,null!=l&&"auto"!==l||(l=this.getInsideTextFill()),null!=u&&"auto"!==u||(u=this.getInsideTextStroke(l),h=!0)):(l=t.outsideFill,u=t.outsideStroke,null!=l&&"auto"!==l||(l=this.getOutsideFill()),null!=u&&"auto"!==u||(u=this.getOutsideStroke(l),h=!0)),(l=l||"#000")===s.fill&&u===s.stroke&&h===s.autoStroke&&n===s.align&&i===s.verticalAlign||(r=!0,s.fill=l,s.stroke=u,s.autoStroke=h,s.align=n,s.verticalAlign=i,c.setDefaultTextStyle(s)),c.__dirty|=mn,r&&c.dirtyStyle(!0))},r.prototype.canBeInsideText=function(){return!0},r.prototype.getInsideTextFill=function(){return"#fff"},r.prototype.getInsideTextStroke=function(t){return"#000"},r.prototype.getOutsideFill=function(){return this.__zr&&this.__zr.isDarkMode()?ur:lr},r.prototype.getOutsideStroke=function(t){for(var e=this.__zr&&this.__zr.getBackgroundColor(),n="string"==typeof e&&fi(e),i=(n=n||[255,255,255,1])[3],r=this.__zr.isDarkMode(),o=0;o<3;o++)n[o]=n[o]*i+(r?0:255)*(1-i);return n[3]=1,xi(n,"rgba")},r.prototype.traverse=function(t,e){},r.prototype.attrKV=function(t,e){"textConfig"===t?this.setTextConfig(e):"textContent"===t?this.setTextContent(e):"clipPath"===t?this.setClipPath(e):"extra"===t?(this.extra=this.extra||{},P(this.extra,e)):this[t]=e},r.prototype.hide=function(){this.ignore=!0,this.markRedraw()},r.prototype.show=function(){this.ignore=!1,this.markRedraw()},r.prototype.attr=function(t,e){if("string"==typeof t)this.attrKV(t,e);else if(R(t))for(var n=I(t),i=0;i<n.length;i++){var r=n[i];this.attrKV(r,t[r])}return this.markRedraw(),this},r.prototype.saveCurrentToNormalState=function(t){this._innerSaveToNormal(t);for(var e=this._normalState,n=0;n<this.animators.length;n++){var i=this.animators[n],r=i.__fromStateTransition;i.getLoop()||r&&r!==Dr||(r=(r=i.targetName)?e[r]:e,i.saveTo(r))}},r.prototype._innerSaveToNormal=function(t){var e=(e=this._normalState)||(this._normalState={});t.textConfig&&!e.textConfig&&(e.textConfig=this.textConfig),this._savePrimaryToNormal(t,e,Ar)},r.prototype._savePrimaryToNormal=function(t,e,n){for(var i=0;i<n.length;i++){var r=n[i];null==t[r]||r in e||(e[r]=this[r])}},r.prototype.hasState=function(){return 0<this.currentStates.length},r.prototype.getState=function(t){return this.states[t]},r.prototype.ensureState=function(t){var e=this.states;return e[t]||(e[t]={}),e[t]},r.prototype.clearStates=function(t){this.useState(Dr,!1,t)},r.prototype.useState=function(t,e,n,i){var r=t===Dr,o=this.hasState();if(o||!r){var a,o=this.currentStates,s=this.stateTransition;if(!(0<=k(o,t))||!e&&1!==o.length){if((a=(a=this.stateProxy&&!r?this.stateProxy(t):a)||this.states&&this.states[t])||r)return r||this.saveCurrentToNormalState(a),(o=!!(a&&a.hoverLayer||i))&&this._toggleHoverLayerFlag(!0),this._applyStateObj(t,a,this._normalState,e,!n&&!this.__inHover&&s&&0<s.duration,s),i=this._textContent,s=this._textGuide,i&&i.useState(t,e,n,o),s&&s.useState(t,e,n,o),r?(this.currentStates=[],this._normalState={}):e?this.currentStates.push(t):this.currentStates=[t],this._updateAnimationTargets(),this.markRedraw(),!o&&this.__inHover&&(this._toggleHoverLayerFlag(!1),this.__dirty&=~mn),a;it("State "+t+" not exists.")}}},r.prototype.useStates=function(t,e,n){if(t.length){var i=[],r=this.currentStates,o=t.length,a=o===r.length;if(a)for(var s=0;s<o;s++)if(t[s]!==r[s]){a=!1;break}if(!a){for(s=0;s<o;s++){var l=t[s],u=void 0;(u=(u=this.stateProxy?this.stateProxy(l,t):u)||this.states[l])&&i.push(u)}var h=i[o-1],h=!!(h&&h.hoverLayer||n),n=(h&&this._toggleHoverLayerFlag(!0),this._mergeStates(i)),c=this.stateTransition,n=(this.saveCurrentToNormalState(n),this._applyStateObj(t.join(","),n,this._normalState,!1,!e&&!this.__inHover&&c&&0<c.duration,c),this._textContent),c=this._textGuide;n&&n.useStates(t,e,h),c&&c.useStates(t,e,h),this._updateAnimationTargets(),this.currentStates=t.slice(),this.markRedraw(),!h&&this.__inHover&&(this._toggleHoverLayerFlag(!1),this.__dirty&=~mn)}}else this.clearStates()},r.prototype.isSilent=function(){for(var t=this.silent,e=this.parent;!t&&e;){if(e.silent){t=!0;break}e=e.parent}return t},r.prototype._updateAnimationTargets=function(){for(var t=0;t<this.animators.length;t++){var e=this.animators[t];e.targetName&&e.changeTarget(this[e.targetName])}},r.prototype.removeState=function(t){var e,t=k(this.currentStates,t);0<=t&&((e=this.currentStates.slice()).splice(t,1),this.useStates(e))},r.prototype.replaceState=function(t,e,n){var i=this.currentStates.slice(),t=k(i,t),r=0<=k(i,e);0<=t?r?i.splice(t,1):i[t]=e:n&&!r&&i.push(e),this.useStates(i)},r.prototype.toggleState=function(t,e){e?this.useState(t,!0):this.removeState(t)},r.prototype._mergeStates=function(t){for(var e,n={},i=0;i<t.length;i++){var r=t[i];P(n,r),r.textConfig&&P(e=e||{},r.textConfig)}return e&&(n.textConfig=e),n},r.prototype._applyStateObj=function(t,e,n,i,r,o){for(var a=!(e&&i),s=(e&&e.textConfig?(this.textConfig=P({},(i?this:n).textConfig),P(this.textConfig,e.textConfig)):a&&n.textConfig&&(this.textConfig=n.textConfig),{}),l=!1,u=0;u<Ar.length;u++){var h=Ar[u],c=r&&Lr[h];e&&null!=e[h]?c?(l=!0,s[h]=e[h]):this[h]=e[h]:a&&null!=n[h]&&(c?(l=!0,s[h]=n[h]):this[h]=n[h])}if(!r)for(u=0;u<this.animators.length;u++){var p=this.animators[u],d=p.targetName;p.getLoop()||p.__changeFinalValue(d?(e||n)[d]:e||n)}l&&this._transitionState(t,s,o)},r.prototype._attachComponent=function(t){var e;t.__zr&&!t.__hostTarget||t!==this&&((e=this.__zr)&&t.addSelfToZr(e),t.__zr=e,t.__hostTarget=this)},r.prototype._detachComponent=function(t){t.__zr&&t.removeSelfFromZr(t.__zr),t.__zr=null,t.__hostTarget=null},r.prototype.getClipPath=function(){return this._clipPath},r.prototype.setClipPath=function(t){this._clipPath&&this._clipPath!==t&&this.removeClipPath(),this._attachComponent(t),this._clipPath=t,this.markRedraw()},r.prototype.removeClipPath=function(){var t=this._clipPath;t&&(this._detachComponent(t),this._clipPath=null,this.markRedraw())},r.prototype.getTextContent=function(){return this._textContent},r.prototype.setTextContent=function(t){var e=this._textContent;e!==t&&(e&&e!==t&&this.removeTextContent(),t.innerTransformable=new yr,this._attachComponent(t),this._textContent=t,this.markRedraw())},r.prototype.setTextConfig=function(t){this.textConfig||(this.textConfig={}),P(this.textConfig,t),this.markRedraw()},r.prototype.removeTextConfig=function(){this.textConfig=null,this.markRedraw()},r.prototype.removeTextContent=function(){var t=this._textContent;t&&(t.innerTransformable=null,this._detachComponent(t),this._textContent=null,this._innerTextDefaultStyle=null,this.markRedraw())},r.prototype.getTextGuideLine=function(){return this._textGuide},r.prototype.setTextGuideLine=function(t){this._textGuide&&this._textGuide!==t&&this.removeTextGuideLine(),this._attachComponent(t),this._textGuide=t,this.markRedraw()},r.prototype.removeTextGuideLine=function(){var t=this._textGuide;t&&(this._detachComponent(t),this._textGuide=null,this.markRedraw())},r.prototype.markRedraw=function(){this.__dirty|=mn;var t=this.__zr;t&&(this.__inHover?t.refreshHover():t.refresh()),this.__hostTarget&&this.__hostTarget.markRedraw()},r.prototype.dirty=function(){this.markRedraw()},r.prototype._toggleHoverLayerFlag=function(t){this.__inHover=t;var e=this._textContent,n=this._textGuide;e&&(e.__inHover=t),n&&(n.__inHover=t)},r.prototype.addSelfToZr=function(t){if(this.__zr!==t){this.__zr=t;var e=this.animators;if(e)for(var n=0;n<e.length;n++)t.animation.addAnimator(e[n]);this._clipPath&&this._clipPath.addSelfToZr(t),this._textContent&&this._textContent.addSelfToZr(t),this._textGuide&&this._textGuide.addSelfToZr(t)}},r.prototype.removeSelfFromZr=function(t){if(this.__zr){this.__zr=null;var e=this.animators;if(e)for(var n=0;n<e.length;n++)t.animation.removeAnimator(e[n]);this._clipPath&&this._clipPath.removeSelfFromZr(t),this._textContent&&this._textContent.removeSelfFromZr(t),this._textGuide&&this._textGuide.removeSelfFromZr(t)}},r.prototype.animate=function(t,e,n){var i=t?this[t]:this,i=new Ei(i,e,n);return t&&(i.targetName=t),this.addAnimator(i,t),i},r.prototype.addAnimator=function(n,t){var e=this.__zr,i=this;n.during(function(){i.updateDuringAnimation(t)}).done(function(){var t=i.animators,e=k(t,n);0<=e&&t.splice(e,1)}),this.animators.push(n),e&&e.animation.addAnimator(n),e&&e.wakeUp()},r.prototype.updateDuringAnimation=function(t){this.markRedraw()},r.prototype.stopAnimation=function(t,e){for(var n=this.animators,i=n.length,r=[],o=0;o<i;o++){var a=n[o];t&&t!==a.scope?r.push(a):a.stop(e)}return this.animators=r,this},r.prototype.animateTo=function(t,e,n){Nr(this,t,e,n)},r.prototype.animateFrom=function(t,e,n){Nr(this,t,e,n,!0)},r.prototype._transitionState=function(t,e,n,i){for(var r=Nr(this,e,n,i),o=0;o<r.length;o++)r[o].__fromStateTransition=t},r.prototype.getBoundingRect=function(){return null},r.prototype.getPaintRect=function(){return null},r.initDefaultProps=((Ir=r.prototype).type="element",Ir.name="",Ir.ignore=Ir.silent=Ir.isGroup=Ir.draggable=Ir.dragging=Ir.ignoreClip=Ir.__inHover=!1,Ir.__dirty=mn,void(Object.defineProperty&&(Rr("position","_legacyPos","x","y"),Rr("scale","_legacyScale","scaleX","scaleY"),Rr("origin","_legacyOrigin","originX","originY")))),r);function r(t){this.id=et++,this.animators=[],this.currentStates=[],this.states={},this._init(t)}function Rr(t,e,n,i){function r(e,t){Object.defineProperty(t,0,{get:function(){return e[n]},set:function(t){e[n]=t}}),Object.defineProperty(t,1,{get:function(){return e[i]},set:function(t){e[i]=t}})}Object.defineProperty(Ir,t,{get:function(){var t;return this[e]||(t=this[e]=[],r(this,t)),this[e]},set:function(t){this[n]=t[0],this[i]=t[1],this[e]=t,r(this,t)}})}function Nr(t,e,n,i,r){function o(){u=!0,--l<=0&&(u?h&&h():c&&c())}function a(){--l<=0&&(u?h&&h():c&&c())}var s=[],l=(!function t(e,n,i,r,o,a,s,l){var u=I(r);var h=o.duration;var c=o.delay;var p=o.additive;var d=o.setToFinal;var f=!R(a);var g=e.animators;var y=[];for(var m=0;m<u.length;m++){var v=u[m],_=r[v];null!=_&&null!=i[v]&&(f||a[v])?!R(_)||st(_)||mt(_)?y.push(v):n?l||(i[v]=_,e.updateDuringAnimation(n)):t(e,v,i[v],_,o,a&&a[v],s,l):l||(i[v]=_,e.updateDuringAnimation(n),y.push(v))}var x=y.length;if(!p&&x)for(var b,w=0;w<g.length;w++)(S=g[w]).targetName===n&&S.stopTracks(y)&&(b=k(g,S),g.splice(b,1));o.force||(y=ut(y,function(t){return!Br(r[t],i[t])}),x=y.length);if(0<x||o.force&&!s.length){var S,M=void 0,T=void 0,C=void 0;if(l){T={},d&&(M={});for(w=0;w<x;w++){v=y[w];T[v]=i[v],d?M[v]=r[v]:i[v]=r[v]}}else if(d){C={};for(w=0;w<x;w++){v=y[w];C[v]=Di(i[v]),zr(i,r,v)}}(S=new Ei(i,!1,!1,p?ut(g,function(t){return t.targetName===n}):null)).targetName=n,o.scope&&(S.scope=o.scope),d&&M&&S.whenWithKeys(0,M,y),C&&S.whenWithKeys(0,C,y),S.whenWithKeys(null==h?500:h,l?T:r,y).delay(c||0),e.addAnimator(S,n),s.push(S)}}(t,"",t,e,n=n||{},i,s,r),s.length),u=!1,h=n.done,c=n.aborted;l||h&&h(),0<s.length&&n.during&&s[0].during(function(t,e){n.during(e)});for(var p=0;p<s.length;p++){var d=s[p];d.done(o),d.aborted(a),n.force&&d.duration(n.duration),d.start(n.easing)}return s}function Er(t,e,n){for(var i=0;i<n;i++)t[i]=e[i]}function zr(t,e,n){if(st(e[n]))if(st(t[n])||(t[n]=[]),gt(e[n])){var i=e[n].length;t[n].length!==i&&(t[n]=new e[n].constructor(i),Er(t[n],e[n],i))}else{var r=e[n],o=t[n],a=r.length;if(st(r[0]))for(var s=r[0].length,l=0;l<a;l++)o[l]?Er(o[l],r[l],s):o[l]=Array.prototype.slice.call(r[l]);else Er(o,r,a);o.length=r.length}else t[n]=e[n]}function Br(t,e){return t===e||st(t)&&st(e)&&function(t,e){var n=t.length;if(n!==e.length)return!1;for(var i=0;i<n;i++)if(t[i]!==e[i])return!1;return!0}(t,e)}at(n,le),at(n,yr);u(Hr,Fr=n),Hr.prototype.childrenRef=function(){return this._children},Hr.prototype.children=function(){return this._children.slice()},Hr.prototype.childAt=function(t){return this._children[t]},Hr.prototype.childOfName=function(t){for(var e=this._children,n=0;n<e.length;n++)if(e[n].name===t)return e[n]},Hr.prototype.childCount=function(){return this._children.length},Hr.prototype.add=function(t){return t&&t!==this&&t.parent!==this&&(this._children.push(t),this._doAdd(t)),this},Hr.prototype.addBefore=function(t,e){var n;return t&&t!==this&&t.parent!==this&&e&&e.parent===this&&0<=(e=(n=this._children).indexOf(e))&&(n.splice(e,0,t),this._doAdd(t)),this},Hr.prototype.replace=function(t,e){t=k(this._children,t);return 0<=t&&this.replaceAt(e,t),this},Hr.prototype.replaceAt=function(t,e){var n=this._children,i=n[e];return t&&t!==this&&t.parent!==this&&t!==i&&(n[e]=t,i.parent=null,(n=this.__zr)&&i.removeSelfFromZr(n),this._doAdd(t)),this},Hr.prototype._doAdd=function(t){t.parent&&t.parent.remove(t);var e=(t.parent=this).__zr;e&&e!==t.__zr&&t.addSelfToZr(e),e&&e.refresh()},Hr.prototype.remove=function(t){var e=this.__zr,n=this._children,i=k(n,t);return i<0||(n.splice(i,1),t.parent=null,e&&t.removeSelfFromZr(e),e&&e.refresh()),this},Hr.prototype.removeAll=function(){for(var t=this._children,e=this.__zr,n=0;n<t.length;n++){var i=t[n];e&&i.removeSelfFromZr(e),i.parent=null}return t.length=0,this},Hr.prototype.eachChild=function(t,e){for(var n=this._children,i=0;i<n.length;i++){var r=n[i];t.call(e,r,i)}return this},Hr.prototype.traverse=function(t,e){for(var n=0;n<this._children.length;n++){var i=this._children[n],r=t.call(e,i);i.isGroup&&!r&&i.traverse(t,e)}return this},Hr.prototype.addSelfToZr=function(t){Fr.prototype.addSelfToZr.call(this,t);for(var e=0;e<this._children.length;e++)this._children[e].addSelfToZr(t)},Hr.prototype.removeSelfFromZr=function(t){Fr.prototype.removeSelfFromZr.call(this,t);for(var e=0;e<this._children.length;e++)this._children[e].removeSelfFromZr(t)},Hr.prototype.getBoundingRect=function(t){for(var e=new X(0,0,0,0),n=t||this._children,i=[],r=null,o=0;o<n.length;o++){var a,s=n[o];s.ignore||s.invisible||(a=s.getBoundingRect(),(s=s.getLocalTransform(i))?(X.applyTransform(e,a,s),(r=r||e.clone()).union(e)):(r=r||a.clone()).union(a))}return r||e};var Fr,Vr=Hr;function Hr(t){var e=Fr.call(this)||this;return e.isGroup=!0,e._children=[],e.attr(t),e}Vr.prototype.type="group";var Gr={},Wr={};Yr.prototype.add=function(t){!this._disposed&&t&&(this.storage.addRoot(t),t.addSelfToZr(this),this.refresh())},Yr.prototype.remove=function(t){!this._disposed&&t&&(this.storage.delRoot(t),t.removeSelfFromZr(this),this.refresh())},Yr.prototype.configLayer=function(t,e){this._disposed||(this.painter.configLayer&&this.painter.configLayer(t,e),this.refresh())},Yr.prototype.setBackgroundColor=function(t){this._disposed||(this.painter.setBackgroundColor&&this.painter.setBackgroundColor(t),this.refresh(),this._backgroundColor=t,this._darkMode=function(t){if(t){if("string"==typeof t)return bi(t,1)<.4;if(t.colorStops){for(var e=t.colorStops,n=0,i=e.length,r=0;r<i;r++)n+=bi(e[r].color,1);return(n/=i)<.4}}return!1}(t))},Yr.prototype.getBackgroundColor=function(){return this._backgroundColor},Yr.prototype.setDarkMode=function(t){this._darkMode=t},Yr.prototype.isDarkMode=function(){return this._darkMode},Yr.prototype.refreshImmediately=function(t){this._disposed||(t||this.animation.update(!0),this._needsRefresh=!1,this.painter.refresh(),this._needsRefresh=!1)},Yr.prototype.refresh=function(){this._disposed||(this._needsRefresh=!0,this.animation.start())},Yr.prototype.flush=function(){this._disposed||this._flush(!1)},Yr.prototype._flush=function(t){var e,n=zi(),t=(this._needsRefresh&&(e=!0,this.refreshImmediately(t)),this._needsRefreshHover&&(e=!0,this.refreshHoverImmediately()),zi());e?(this._stillFrameAccum=0,this.trigger("rendered",{elapsedTime:t-n})):0<this._sleepAfterStill&&(this._stillFrameAccum++,this._stillFrameAccum>this._sleepAfterStill)&&this.animation.stop()},Yr.prototype.setSleepAfterStill=function(t){this._sleepAfterStill=t},Yr.prototype.wakeUp=function(){this._disposed||(this.animation.start(),this._stillFrameAccum=0)},Yr.prototype.refreshHover=function(){this._needsRefreshHover=!0},Yr.prototype.refreshHoverImmediately=function(){this._disposed||(this._needsRefreshHover=!1,this.painter.refreshHover&&"canvas"===this.painter.getType()&&this.painter.refreshHover())},Yr.prototype.resize=function(t){this._disposed||(this.painter.resize((t=t||{}).width,t.height),this.handler.resize())},Yr.prototype.clearAnimation=function(){this._disposed||this.animation.clear()},Yr.prototype.getWidth=function(){if(!this._disposed)return this.painter.getWidth()},Yr.prototype.getHeight=function(){if(!this._disposed)return this.painter.getHeight()},Yr.prototype.setCursorStyle=function(t){this._disposed||this.handler.setCursorStyle(t)},Yr.prototype.findHover=function(t,e){if(!this._disposed)return this.handler.findHover(t,e)},Yr.prototype.on=function(t,e,n){return this._disposed||this.handler.on(t,e,n),this},Yr.prototype.off=function(t,e){this._disposed||this.handler.off(t,e)},Yr.prototype.trigger=function(t,e){this._disposed||this.handler.trigger(t,e)},Yr.prototype.clear=function(){if(!this._disposed){for(var t=this.storage.getRoots(),e=0;e<t.length;e++)t[e]instanceof Vr&&t[e].removeSelfFromZr(this);this.storage.delAllRoots(),this.painter.clear()}},Yr.prototype.dispose=function(){var t;this._disposed||(this.animation.stop(),this.clear(),this.storage.dispose(),this.painter.dispose(),this.handler.dispose(),this.animation=this.storage=this.painter=this.handler=null,this._disposed=!0,t=this.id,delete Wr[t])};var Ur,Xr=Yr;function Yr(t,e,n){var i,r=this,o=(this._sleepAfterStill=10,this._stillFrameAccum=0,this._needsRefresh=!0,this._needsRefreshHover=!0,this._darkMode=!1,n=n||{},this.dom=e,this.id=t,new wn),a=n.renderer||"canvas",a=(Gr[a]||(a=I(Gr)[0]),n.useDirtyRect=null!=n.useDirtyRect&&n.useDirtyRect,new Gr[a](e,o,n,t)),e=n.ssr||a.ssrOnly,t=(this.storage=o,this.painter=a,p.node||p.worker||e?null:new or(a.getViewportRoot(),a.root)),s=n.useCoarsePointer;(null==s||"auto"===s?p.touchEventsSupported:!!s)&&(i=N(n.pointerSize,44)),this.handler=new on(o,a,t,a.root,i),this.animation=new Fi({stage:{update:e?null:function(){return r._flush(!0)}}}),e||this.animation.start()}function qr(t,e){t=new Xr(et++,t,e);return Wr[t.id]=t}function Zr(t,e){Gr[t]=e}function jr(t){Ur=t}var Kr=Object.freeze({__proto__:null,dispose:function(t){t.dispose()},disposeAll:function(){for(var t in Wr)Wr.hasOwnProperty(t)&&Wr[t].dispose();Wr={}},getElementSSRData:function(t){if("function"==typeof Ur)return Ur(t)},getInstance:function(t){return Wr[t]},init:qr,registerPainter:Zr,registerSSRDataGetter:jr,version:"5.6.0"}),$r=20;function Qr(t,e,n,i){var r=e[0],e=e[1],o=n[0],n=n[1],a=e-r,s=n-o;if(0==a)return 0==s?o:(o+n)/2;if(i)if(0<a){if(t<=r)return o;if(e<=t)return n}else{if(r<=t)return o;if(t<=e)return n}else{if(t===r)return o;if(t===e)return n}return(t-r)/a*s+o}function Jr(t,e){switch(t){case"center":case"middle":t="50%";break;case"left":case"top":t="0%";break;case"right":case"bottom":t="100%"}return V(t)?t.replace(/^\s+|\s+$/g,"").match(/%$/)?parseFloat(t)/100*e:parseFloat(t):null==t?NaN:+t}function to(t,e,n){return null==e&&(e=10),e=Math.min(Math.max(0,e),$r),t=(+t).toFixed(e),n?t:+t}function eo(t){return t.sort(function(t,e){return t-e}),t}function no(t){if(t=+t,isNaN(t))return 0;if(1e-14<t)for(var e=1,n=0;n<15;n++,e*=10)if(Math.round(t*e)/e===t)return n;return io(t)}function io(t){var t=t.toString().toLowerCase(),e=t.indexOf("e"),n=0<e?+t.slice(e+1):0,e=0<e?e:t.length,t=t.indexOf(".");return Math.max(0,(t<0?0:e-1-t)-n)}function ro(t,e){var n=Math.log,i=Math.LN10,t=Math.floor(n(t[1]-t[0])/i),n=Math.round(n(Math.abs(e[1]-e[0]))/i),e=Math.min(Math.max(-t+n,0),20);return isFinite(e)?e:20}function oo(t){var e=2*Math.PI;return(t%e+e)%e}function ao(t){return-1e-4<t&&t<1e-4}var so=/^(?:(\d{4})(?:[-\/](\d{1,2})(?:[-\/](\d{1,2})(?:[T ](\d{1,2})(?::(\d{1,2})(?::(\d{1,2})(?:[.,](\d+))?)?)?(Z|[\+\-]\d\d:?\d\d)?)?)?)?)?$/;function lo(t){var e,n;return t instanceof Date?t:V(t)?(e=so.exec(t))?e[8]?(n=+e[4]||0,"Z"!==e[8].toUpperCase()&&(n-=+e[8].slice(0,3)),new Date(Date.UTC(+e[1],+(e[2]||1)-1,+e[3]||1,n,+(e[5]||0),+e[6]||0,e[7]?+e[7].substring(0,3):0))):new Date(+e[1],+(e[2]||1)-1,+e[3]||1,+e[4]||0,+(e[5]||0),+e[6]||0,e[7]?+e[7].substring(0,3):0):new Date(NaN):null==t?new Date(NaN):new Date(Math.round(t))}function uo(t){return Math.pow(10,ho(t))}function ho(t){var e;return 0===t?0:(e=Math.floor(Math.log(t)/Math.LN10),10<=t/Math.pow(10,e)&&e++,e)}function co(t,e){var n=ho(t),i=Math.pow(10,n),r=t/i,e=e?r<1.5?1:r<2.5?2:r<4?3:r<7?5:10:r<1?1:r<2?2:r<3?3:r<5?5:10;return t=e*i,-20<=n?+t.toFixed(n<0?-n:0):t}function po(t){var e=parseFloat(t);return e==t&&(0!==e||!V(t)||t.indexOf("x")<=0)?e:NaN}function fo(t){return!isNaN(po(t))}function go(t,e){return null==t?e:null==e?t:t*e/function t(e,n){return 0===n?e:t(n,e%n)}(t,e)}function f(t){throw new Error(t)}function yo(t,e,n){return(e-t)*n+t}var mo="series\0";function vo(t){return t instanceof Array?t:null==t?[]:[t]}function _o(t,e,n){if(t){t[e]=t[e]||{},t.emphasis=t.emphasis||{},t.emphasis[e]=t.emphasis[e]||{};for(var i=0,r=n.length;i<r;i++){var o=n[i];!t.emphasis[e].hasOwnProperty(o)&&t[e].hasOwnProperty(o)&&(t.emphasis[e][o]=t[e][o])}}}var xo=["fontStyle","fontWeight","fontSize","fontFamily","rich","tag","color","textBorderColor","textBorderWidth","width","height","lineHeight","align","verticalAlign","baseline","shadowColor","shadowBlur","shadowOffsetX","shadowOffsetY","textShadowColor","textShadowBlur","textShadowOffsetX","textShadowOffsetY","backgroundColor","borderColor","borderWidth","borderRadius","padding"];function bo(t){return!R(t)||F(t)||t instanceof Date?t:t.value}function wo(t,n,e){var o,a,s,l,r,u,i,h,c,p,d="normalMerge"===e,f="replaceMerge"===e,g="replaceAll"===e,y=(t=t||[],n=(n||[]).slice(),E()),e=(O(n,function(t,e){R(t)||(n[e]=null)}),function(t,e,n){var i=[];if("replaceAll"!==n)for(var r=0;r<t.length;r++){var o=t[r];o&&null!=o.id&&e.set(o.id,r),i.push({existing:"replaceMerge"===n||ko(o)?null:o,newOption:null,keyInfo:null,brandNew:null})}return i}(t,y,e));return(d||f)&&(o=e,a=t,s=y,O(l=n,function(t,e){var n,i,r;t&&null!=t.id&&(n=Mo(t.id),null!=(i=s.get(n)))&&(Tt(!(r=o[i]).newOption,'Duplicated option on id "'+n+'".'),r.newOption=t,r.existing=a[i],l[e]=null)})),d&&(r=e,O(u=n,function(t,e){if(t&&null!=t.name)for(var n=0;n<r.length;n++){var i=r[n].existing;if(!r[n].newOption&&i&&(null==i.id||null==t.id)&&!ko(t)&&!ko(i)&&So("name",i,t))return r[n].newOption=t,void(u[e]=null)}})),d||f?(h=e,c=f,O(n,function(t){if(t){for(var e,n=0;(e=h[n])&&(e.newOption||ko(e.existing)||e.existing&&null!=t.id&&!So("id",t,e.existing));)n++;e?(e.newOption=t,e.brandNew=c):h.push({newOption:t,brandNew:c,existing:null,keyInfo:null}),n++}})):g&&(i=e,O(n,function(t){i.push({newOption:t,brandNew:!0,existing:null,keyInfo:null})})),t=e,p=E(),O(t,function(t){var e=t.existing;e&&p.set(e.id,t)}),O(t,function(t){var e=t.newOption;Tt(!e||null==e.id||!p.get(e.id)||p.get(e.id)===t,"id duplicates: "+(e&&e.id)),e&&null!=e.id&&p.set(e.id,t),t.keyInfo||(t.keyInfo={})}),O(t,function(t,e){var n=t.existing,i=t.newOption,r=t.keyInfo;if(R(i)){if(r.name=null!=i.name?Mo(i.name):n?n.name:mo+e,n)r.id=Mo(n.id);else if(null!=i.id)r.id=Mo(i.id);else for(var o=0;r.id="\0"+r.name+"\0"+o++,p.get(r.id););p.set(r.id,t)}}),e}function So(t,e,n){e=To(e[t],null),n=To(n[t],null);return null!=e&&null!=n&&e===n}function Mo(t){return To(t,"")}function To(t,e){return null==t?e:V(t)?t:dt(t)||pt(t)?t+"":e}function Co(t){t=t.name;return!(!t||!t.indexOf(mo))}function ko(t){return t&&null!=t.id&&0===Mo(t.id).indexOf("\0_ec_\0")}function Io(t,r,o){O(t,function(t){var e,n,i=t.newOption;R(i)&&(t.keyInfo.mainType=r,t.keyInfo.subType=(e=r,i=i,t=t.existing,n=o,i.type||(t?t.subType:n.determineSubType(e,i))))})}function Do(e,t){return null!=t.dataIndexInside?t.dataIndexInside:null!=t.dataIndex?F(t.dataIndex)?B(t.dataIndex,function(t){return e.indexOfRawIndex(t)}):e.indexOfRawIndex(t.dataIndex):null!=t.name?F(t.name)?B(t.name,function(t){return e.indexOfName(t)}):e.indexOfName(t.name):void 0}function Ao(){var e="__ec_inner_"+Lo++;return function(t){return t[e]||(t[e]={})}}var Lo=Math.round(9*Math.random());function Po(n,t,i){var t=Oo(t,i),e=t.mainTypeSpecified,r=t.queryOptionMap,o=t.others,a=i?i.defaultMainType:null;return!e&&a&&r.set(a,{}),r.each(function(t,e){t=No(n,e,t,{useDefault:a===e,enableAll:!i||null==i.enableAll||i.enableAll,enableNone:!i||null==i.enableNone||i.enableNone});o[e+"Models"]=t.models,o[e+"Model"]=t.models[0]}),o}function Oo(t,i){var e=V(t)?((e={})[t+"Index"]=0,e):t,r=E(),o={},a=!1;return O(e,function(t,e){var n;"dataIndex"===e||"dataIndexInside"===e?o[e]=t:(n=(e=e.match(/^(\w+)(Index|Id|Name)$/)||[])[1],e=(e[2]||"").toLowerCase(),!n||!e||i&&i.includeMainTypes&&k(i.includeMainTypes,n)<0||(a=a||!!n,(r.get(n)||r.set(n,{}))[e]=t))}),{mainTypeSpecified:a,queryOptionMap:r,others:o}}var Ro={useDefault:!0,enableAll:!1,enableNone:!1};function No(t,e,n,i){i=i||Ro;var r=n.index,o=n.id,n=n.name,a={models:null,specified:null!=r||null!=o||null!=n};return a.specified?"none"===r||!1===r?(Tt(i.enableNone,'`"none"` or `false` is not a valid value on index option.'),a.models=[]):("all"===r&&(Tt(i.enableAll,'`"all"` is not a valid value on index option.'),r=o=n=null),a.models=t.queryComponents({mainType:e,index:r,id:o,name:n})):(r=void 0,a.models=i.useDefault&&(r=t.getComponent(e))?[r]:[]),a}function Eo(t,e,n){t.setAttribute?t.setAttribute(e,n):t[e]=n}function zo(t,e,n,i,r){var o=null==e||"auto"===e;if(null==i)return i;if(dt(i))return to(p=yo(n||0,i,r),o?Math.max(no(n||0),no(i)):e);if(V(i))return r<1?n:i;for(var a=[],s=n,l=i,u=Math.max(s?s.length:0,l.length),h=0;h<u;++h){var c,p,d=t.getDimensionInfo(h);d&&"ordinal"===d.type?a[h]=(r<1&&s?s:l)[h]:(p=yo(d=s&&s[h]?s[h]:0,c=l[h],r),a[h]=to(p,o?Math.max(no(d),no(c)):e))}return a}var Bo=".",Fo="___EC__COMPONENT__CONTAINER___",Vo="___EC__EXTENDED_CLASS___";function Ho(t){var e={main:"",sub:""};return t&&(t=t.split(Bo),e.main=t[0]||"",e.sub=t[1]||""),e}function Go(t){(t.$constructor=t).extend=function(t){var e,n,i,r=this;function o(){return n.apply(this,arguments)||this}return D(i=r)&&/^class\s/.test(Function.prototype.toString.call(i))?(u(o,n=r),e=o):ot(e=function(){(t.$constructor||r).apply(this,arguments)},this),P(e.prototype,t),e[Vo]=!0,e.extend=this.extend,e.superCall=Xo,e.superApply=Yo,e.superClass=r,e}}function Wo(t,e){t.extend=e.extend}var Uo=Math.round(10*Math.random());function Xo(t,e){for(var n=[],i=2;i<arguments.length;i++)n[i-2]=arguments[i];return this.superClass.prototype[e].apply(t,n)}function Yo(t,e,n){return this.superClass.prototype[e].apply(t,n)}function qo(t){var r={};t.registerClass=function(t){var e,n=t.type||t.prototype.type;return n&&(Tt(/^[a-zA-Z0-9_]+([.][a-zA-Z0-9_]+)?$/.test(e=n),'componentType "'+e+'" illegal'),(e=Ho(t.prototype.type=n)).sub?e.sub!==Fo&&(function(t){var e=r[t.main];e&&e[Fo]||(e=r[t.main]={___EC__COMPONENT__CONTAINER___:!0});return e}(e)[e.sub]=t):r[e.main]=t),t},t.getClass=function(t,e,n){var i=r[t];if(i&&i[Fo]&&(i=e?i[e]:null),n&&!i)throw new Error(e?"Component "+t+"."+(e||"")+" is used but not imported.":t+".type should be specified.");return i},t.getClassesByMainType=function(t){var t=Ho(t),n=[],t=r[t.main];return t&&t[Fo]?O(t,function(t,e){e!==Fo&&n.push(t)}):n.push(t),n},t.hasClass=function(t){t=Ho(t);return!!r[t.main]},t.getAllClassMainTypes=function(){var n=[];return O(r,function(t,e){n.push(e)}),n},t.hasSubTypes=function(t){t=Ho(t),t=r[t.main];return t&&t[Fo]}}function Zo(a,s){for(var t=0;t<a.length;t++)a[t][1]||(a[t][1]=a[t][0]);return s=s||!1,function(t,e,n){for(var i={},r=0;r<a.length;r++){var o=a[r][1];e&&0<=k(e,o)||n&&k(n,o)<0||null!=(o=t.getShallow(o,s))&&(i[a[r][0]]=o)}return i}}var jo=Zo([["fill","color"],["shadowBlur"],["shadowOffsetX"],["shadowOffsetY"],["opacity"],["shadowColor"]]),Ko=($o.prototype.getAreaStyle=function(t,e){return jo(this,t,e)},$o);function $o(){}var Qo=new ti(50);function Jo(t,e,n,i,r){return t?"string"==typeof t?(e&&e.__zrImageSrc===t||!n||(n={hostEl:n,cb:i,cbPayload:r},(i=Qo.get(t))?ea(e=i.image)||i.pending.push(n):((e=H.loadImage(t,ta,ta)).__zrImageSrc=t,Qo.put(t,e.__cachedImgObj={image:e,pending:[n]}))),e):t:e}function ta(){var t=this.__cachedImgObj;this.onload=this.onerror=this.__cachedImgObj=null;for(var e=0;e<t.pending.length;e++){var n=t.pending[e],i=n.cb;i&&i(this,n.cbPayload),n.hostEl.dirty()}t.pending.length=0}function ea(t){return t&&t.width&&t.height}var na=/\{([a-zA-Z0-9_]+)\|([^}]*)\}/g;function ia(t,e,n,i,r){if(!e)return"";var o=(t+"").split("\n");r=ra(e,n,i,r);for(var a=0,s=o.length;a<s;a++)o[a]=oa(o[a],r);return o.join("\n")}function ra(t,e,n,i){for(var r=P({},i=i||{}),o=(r.font=e,n=N(n,"..."),r.maxIterations=N(i.maxIterations,2),r.minChar=N(i.minChar,0)),a=(r.cnCharWidth=xr("国",e),r.ascCharWidth=xr("a",e)),s=(r.placeholder=N(i.placeholder,""),t=Math.max(0,t-1)),l=0;l<o&&a<=s;l++)s-=a;i=xr(n,e);return s<i&&(n="",i=0),s=t-i,r.ellipsis=n,r.ellipsisWidth=i,r.contentWidth=s,r.containerWidth=t,r}function oa(t,e){var n=e.containerWidth,i=e.font,r=e.contentWidth;if(!n)return"";var o=xr(t,i);if(!(o<=n)){for(var a=0;;a++){if(o<=r||a>=e.maxIterations){t+=e.ellipsis;break}var s=0===a?function(t,e,n,i){for(var r=0,o=0,a=t.length;o<a&&r<e;o++){var s=t.charCodeAt(o);r+=0<=s&&s<=127?n:i}return o}(t,r,e.ascCharWidth,e.cnCharWidth):0<o?Math.floor(t.length*r/o):0,o=xr(t=t.substr(0,s),i)}""===t&&(t=e.placeholder)}return t}var aa=function(){},sa=function(t){this.tokens=[],t&&(this.tokens=t)},la=function(){this.width=0,this.height=0,this.contentWidth=0,this.contentHeight=0,this.outerWidth=0,this.outerHeight=0,this.lines=[]};function ua(t,e){var n=new la;if(null!=t&&(t+=""),t){for(var i,r=e.width,o=e.height,a=e.overflow,s="break"!==a&&"breakAll"!==a||null==r?null:{width:r,accumWidth:0,breakAll:"breakAll"===a},l=na.lastIndex=0;null!=(i=na.exec(t));){var u=i.index;l<u&&ha(n,t.substring(l,u),e,s),ha(n,i[2],e,s,i[1]),l=na.lastIndex}l<t.length&&ha(n,t.substring(l,t.length),e,s);var h,c=[],p=0,d=0,f=e.padding,g="truncate"===a,y="truncate"===e.lineOverflow;t:for(var m=0;m<n.lines.length;m++){for(var v=n.lines[m],_=0,x=0,b=0;b<v.tokens.length;b++){var w=(D=v.tokens[b]).styleName&&e.rich[D.styleName]||{},S=D.textPadding=w.padding,M=S?S[1]+S[3]:0,T=D.font=w.font||e.font,C=(D.contentHeight=Tr(T),N(w.height,D.contentHeight));if(D.innerHeight=C,S&&(C+=S[0]+S[2]),D.height=C,D.lineHeight=wt(w.lineHeight,e.lineHeight,C),D.align=w&&w.align||e.align,D.verticalAlign=w&&w.verticalAlign||"middle",y&&null!=o&&p+D.lineHeight>o){0<b?(v.tokens=v.tokens.slice(0,b),L(v,x,_),n.lines=n.lines.slice(0,m+1)):n.lines=n.lines.slice(0,m);break t}var k,S=w.width,I=null==S||"auto"===S;"string"==typeof S&&"%"===S.charAt(S.length-1)?(D.percentWidth=S,c.push(D),D.contentWidth=xr(D.text,T)):(I&&(S=(S=w.backgroundColor)&&S.image)&&(k=void 0,ea(S="string"==typeof(h=S)?(k=Qo.get(h))&&k.image:h))&&(D.width=Math.max(D.width,S.width*C/S.height)),null!=(k=g&&null!=r?r-x:null)&&k<D.width?!I||k<M?(D.text="",D.width=D.contentWidth=0):(D.text=ia(D.text,k-M,T,e.ellipsis,{minChar:e.truncateMinChar}),D.width=D.contentWidth=xr(D.text,T)):D.contentWidth=xr(D.text,T)),D.width+=M,x+=D.width,w&&(_=Math.max(_,D.lineHeight))}L(v,x,_)}n.outerWidth=n.width=N(r,d),n.outerHeight=n.height=N(o,p),n.contentHeight=p,n.contentWidth=d,f&&(n.outerWidth+=f[1]+f[3],n.outerHeight+=f[0]+f[2]);for(m=0;m<c.length;m++){var D,A=(D=c[m]).percentWidth;D.width=parseInt(A,10)/100*n.width}}return n;function L(t,e,n){t.width=e,t.lineHeight=n,p+=n,d=Math.max(d,e)}}function ha(t,e,n,i,r){var o,a,s=""===e,l=r&&n.rich[r]||{},u=t.lines,h=l.font||n.font,c=!1;i?(n=(t=l.padding)?t[1]+t[3]:0,null!=l.width&&"auto"!==l.width?(t=Cr(l.width,i.width)+n,0<u.length&&t+i.accumWidth>i.width&&(o=e.split("\n"),c=!0),i.accumWidth=t):(t=pa(e,h,i.width,i.breakAll,i.accumWidth),i.accumWidth=t.accumWidth+n,a=t.linesWidths,o=t.lines)):o=e.split("\n");for(var p=0;p<o.length;p++){var d,f,g=o[p],y=new aa;y.styleName=r,y.text=g,y.isLineHolder=!g&&!s,"number"==typeof l.width?y.width=l.width:y.width=a?a[p]:xr(g,h),p||c?u.push(new sa([y])):1===(f=(d=(u[u.length-1]||(u[0]=new sa)).tokens).length)&&d[0].isLineHolder?d[0]=y:!g&&f&&!s||d.push(y)}}var ca=lt(",&?/;] ".split(""),function(t,e){return t[e]=!0,t},{});function pa(t,e,n,i,r){for(var o,a=[],s=[],l="",u="",h=0,c=0,p=0;p<t.length;p++){var d,f,g=t.charAt(p);"\n"===g?(u&&(l+=u,c+=h),a.push(l),s.push(c),u=l="",c=h=0):(d=xr(g,e),f=!(i||(f=void 0,!(32<=(f=(f=o=g).charCodeAt(0))&&f<=591||880<=f&&f<=4351||4608<=f&&f<=5119||7680<=f&&f<=8303))||!!ca[o]),(a.length?n<c+d:n<r+c+d)?c?(l||u)&&(c=f?(l||(l=u,u="",c=h=0),a.push(l),s.push(c-h),u+=g,l="",h+=d):(u&&(l+=u,u="",h=0),a.push(l),s.push(c),l=g,d)):f?(a.push(u),s.push(h),u=g,h=d):(a.push(g),s.push(d)):(c+=d,f?(u+=g,h+=d):(u&&(l+=u,u="",h=0),l+=g)))}return a.length||l||(l=t,u="",h=0),u&&(l+=u),l&&(a.push(l),s.push(c)),1===a.length&&(c+=r),{accumWidth:c,lines:a,linesWidths:s}}var da,fa="__zr_style_"+Math.round(10*Math.random()),ga={shadowBlur:0,shadowOffsetX:0,shadowOffsetY:0,shadowColor:"#000",opacity:1,blend:"source-over"},ya={style:{shadowBlur:!0,shadowOffsetX:!0,shadowOffsetY:!0,shadowColor:!0,opacity:!0}},ma=(ga[fa]=!0,["z","z2","invisible"]),va=["invisible"],n=(u(o,da=n),o.prototype._init=function(t){for(var e=I(t),n=0;n<e.length;n++){var i=e[n];"style"===i?this.useStyle(t[i]):da.prototype.attrKV.call(this,i,t[i])}this.style||this.useStyle({})},o.prototype.beforeBrush=function(){},o.prototype.afterBrush=function(){},o.prototype.innerBeforeBrush=function(){},o.prototype.innerAfterBrush=function(){},o.prototype.shouldBePainted=function(t,e,n,i){var r=this.transform;if(this.ignore||this.invisible||0===this.style.opacity||this.culling&&function(t,e,n){_a.copy(t.getBoundingRect()),t.transform&&_a.applyTransform(t.transform);return xa.width=e,xa.height=n,!_a.intersect(xa)}(this,t,e)||r&&!r[0]&&!r[3])return!1;if(n&&this.__clipPaths)for(var o=0;o<this.__clipPaths.length;++o)if(this.__clipPaths[o].isZeroArea())return!1;if(i&&this.parent)for(var a=this.parent;a;){if(a.ignore)return!1;a=a.parent}return!0},o.prototype.contain=function(t,e){return this.rectContain(t,e)},o.prototype.traverse=function(t,e){t.call(e,this)},o.prototype.rectContain=function(t,e){t=this.transformCoordToLocal(t,e);return this.getBoundingRect().contain(t[0],t[1])},o.prototype.getPaintRect=function(){var t,e,n,i,r,o=this._paintRect;return this._paintRect&&!this.__dirty||(r=this.transform,t=this.getBoundingRect(),e=(i=this.style).shadowBlur||0,n=i.shadowOffsetX||0,i=i.shadowOffsetY||0,o=this._paintRect||(this._paintRect=new X(0,0,0,0)),r?X.applyTransform(o,t,r):o.copy(t),(e||n||i)&&(o.width+=2*e+Math.abs(n),o.height+=2*e+Math.abs(i),o.x=Math.min(o.x,o.x+n-e),o.y=Math.min(o.y,o.y+i-e)),r=this.dirtyRectTolerance,o.isZero())||(o.x=Math.floor(o.x-r),o.y=Math.floor(o.y-r),o.width=Math.ceil(o.width+1+2*r),o.height=Math.ceil(o.height+1+2*r)),o},o.prototype.setPrevPaintRect=function(t){t?(this._prevPaintRect=this._prevPaintRect||new X(0,0,0,0),this._prevPaintRect.copy(t)):this._prevPaintRect=null},o.prototype.getPrevPaintRect=function(){return this._prevPaintRect},o.prototype.animateStyle=function(t){return this.animate("style",t)},o.prototype.updateDuringAnimation=function(t){"style"===t?this.dirtyStyle():this.markRedraw()},o.prototype.attrKV=function(t,e){"style"!==t?da.prototype.attrKV.call(this,t,e):this.style?this.setStyle(e):this.useStyle(e)},o.prototype.setStyle=function(t,e){return"string"==typeof t?this.style[t]=e:P(this.style,t),this.dirtyStyle(),this},o.prototype.dirtyStyle=function(t){t||this.markRedraw(),this.__dirty|=2,this._rect&&(this._rect=null)},o.prototype.dirty=function(){this.dirtyStyle()},o.prototype.styleChanged=function(){return!!(2&this.__dirty)},o.prototype.styleUpdated=function(){this.__dirty&=-3},o.prototype.createStyle=function(t){return Et(ga,t)},o.prototype.useStyle=function(t){t[fa]||(t=this.createStyle(t)),this.__inHover?this.__hoverStyle=t:this.style=t,this.dirtyStyle()},o.prototype.isStyleObject=function(t){return t[fa]},o.prototype._innerSaveToNormal=function(t){da.prototype._innerSaveToNormal.call(this,t);var e=this._normalState;t.style&&!e.style&&(e.style=this._mergeStyle(this.createStyle(),this.style)),this._savePrimaryToNormal(t,e,ma)},o.prototype._applyStateObj=function(t,e,n,i,r,o){da.prototype._applyStateObj.call(this,t,e,n,i,r,o);var a,s=!(e&&i);if(e&&e.style?r?i?a=e.style:(a=this._mergeStyle(this.createStyle(),n.style),this._mergeStyle(a,e.style)):(a=this._mergeStyle(this.createStyle(),(i?this:n).style),this._mergeStyle(a,e.style)):s&&(a=n.style),a)if(r){var l=this.style;if(this.style=this.createStyle(s?{}:l),s)for(var u=I(l),h=0;h<u.length;h++)(p=u[h])in a&&(a[p]=a[p],this.style[p]=l[p]);for(var c=I(a),h=0;h<c.length;h++){var p=c[h];this.style[p]=this.style[p]}this._transitionState(t,{style:a},o,this.getAnimationStyleProps())}else this.useStyle(a);for(var d=this.__inHover?va:ma,h=0;h<d.length;h++){p=d[h];e&&null!=e[p]?this[p]=e[p]:s&&null!=n[p]&&(this[p]=n[p])}},o.prototype._mergeStates=function(t){for(var e,n=da.prototype._mergeStates.call(this,t),i=0;i<t.length;i++){var r=t[i];r.style&&this._mergeStyle(e=e||{},r.style)}return e&&(n.style=e),n},o.prototype._mergeStyle=function(t,e){return P(t,e),t},o.prototype.getAnimationStyleProps=function(){return ya},o.initDefaultProps=((n=o.prototype).type="displayable",n.invisible=!1,n.z=0,n.z2=0,n.zlevel=0,n.culling=!1,n.cursor="pointer",n.rectHover=!1,n.incremental=!1,n._rect=null,n.dirtyRectTolerance=0,void(n.__dirty=2|mn)),o);function o(t){return da.call(this,t)||this}var _a=new X(0,0,0,0),xa=new X(0,0,0,0);var ba=Math.min,wa=Math.max,Sa=Math.sin,Ma=Math.cos,Ta=2*Math.PI,Ca=Gt(),ka=Gt(),Ia=Gt();function Da(t,e,n,i,r,o){r[0]=ba(t,n),r[1]=ba(e,i),o[0]=wa(t,n),o[1]=wa(e,i)}var Aa=[],La=[];var Y={M:1,L:2,C:3,Q:4,A:5,Z:6,R:7},Pa=[],Oa=[],Ra=[],Na=[],Ea=[],za=[],Ba=Math.min,Fa=Math.max,Va=Math.cos,Ha=Math.sin,Ga=Math.abs,Wa=Math.PI,Ua=2*Wa,Xa="undefined"!=typeof Float32Array,Ya=[];function qa(t){return Math.round(t/Wa*1e8)/1e8%2*Wa}a.prototype.increaseVersion=function(){this._version++},a.prototype.getVersion=function(){return this._version},a.prototype.setScale=function(t,e,n){0<(n=n||0)&&(this._ux=Ga(n/sr/t)||0,this._uy=Ga(n/sr/e)||0)},a.prototype.setDPR=function(t){this.dpr=t},a.prototype.setContext=function(t){this._ctx=t},a.prototype.getContext=function(){return this._ctx},a.prototype.beginPath=function(){return this._ctx&&this._ctx.beginPath(),this.reset(),this},a.prototype.reset=function(){this._saveData&&(this._len=0),this._pathSegLen&&(this._pathSegLen=null,this._pathLen=0),this._version++},a.prototype.moveTo=function(t,e){return this._drawPendingPt(),this.addData(Y.M,t,e),this._ctx&&this._ctx.moveTo(t,e),this._x0=t,this._y0=e,this._xi=t,this._yi=e,this},a.prototype.lineTo=function(t,e){var n=Ga(t-this._xi),i=Ga(e-this._yi),r=n>this._ux||i>this._uy;return this.addData(Y.L,t,e),this._ctx&&r&&this._ctx.lineTo(t,e),r?(this._xi=t,this._yi=e,this._pendingPtDist=0):(r=n*n+i*i)>this._pendingPtDist&&(this._pendingPtX=t,this._pendingPtY=e,this._pendingPtDist=r),this},a.prototype.bezierCurveTo=function(t,e,n,i,r,o){return this._drawPendingPt(),this.addData(Y.C,t,e,n,i,r,o),this._ctx&&this._ctx.bezierCurveTo(t,e,n,i,r,o),this._xi=r,this._yi=o,this},a.prototype.quadraticCurveTo=function(t,e,n,i){return this._drawPendingPt(),this.addData(Y.Q,t,e,n,i),this._ctx&&this._ctx.quadraticCurveTo(t,e,n,i),this._xi=n,this._yi=i,this},a.prototype.arc=function(t,e,n,i,r,o){this._drawPendingPt(),Ya[0]=i,Ya[1]=r,s=o,(l=qa((a=Ya)[0]))<0&&(l+=Ua),h=l-a[0],u=a[1],u+=h,!s&&Ua<=u-l?u=l+Ua:s&&Ua<=l-u?u=l-Ua:!s&&u<l?u=l+(Ua-qa(l-u)):s&&l<u&&(u=l-(Ua-qa(u-l))),a[0]=l,a[1]=u;var a,s,l,u,h=(r=Ya[1])-(i=Ya[0]);return this.addData(Y.A,t,e,n,n,i,h,0,o?0:1),this._ctx&&this._ctx.arc(t,e,n,i,r,o),this._xi=Va(r)*n+t,this._yi=Ha(r)*n+e,this},a.prototype.arcTo=function(t,e,n,i,r){return this._drawPendingPt(),this._ctx&&this._ctx.arcTo(t,e,n,i,r),this},a.prototype.rect=function(t,e,n,i){return this._drawPendingPt(),this._ctx&&this._ctx.rect(t,e,n,i),this.addData(Y.R,t,e,n,i),this},a.prototype.closePath=function(){this._drawPendingPt(),this.addData(Y.Z);var t=this._ctx,e=this._x0,n=this._y0;return t&&t.closePath(),this._xi=e,this._yi=n,this},a.prototype.fill=function(t){t&&t.fill(),this.toStatic()},a.prototype.stroke=function(t){t&&t.stroke(),this.toStatic()},a.prototype.len=function(){return this._len},a.prototype.setData=function(t){var e=t.length;this.data&&this.data.length===e||!Xa||(this.data=new Float32Array(e));for(var n=0;n<e;n++)this.data[n]=t[n];this._len=e},a.prototype.appendPath=function(t){for(var e=(t=t instanceof Array?t:[t]).length,n=0,i=this._len,r=0;r<e;r++)n+=t[r].len();Xa&&this.data instanceof Float32Array&&(this.data=new Float32Array(i+n));for(r=0;r<e;r++)for(var o=t[r].data,a=0;a<o.length;a++)this.data[i++]=o[a];this._len=i},a.prototype.addData=function(t,e,n,i,r,o,a,s,l){if(this._saveData){var u=this.data;this._len+arguments.length>u.length&&(this._expandData(),u=this.data);for(var h=0;h<arguments.length;h++)u[this._len++]=arguments[h]}},a.prototype._drawPendingPt=function(){0<this._pendingPtDist&&(this._ctx&&this._ctx.lineTo(this._pendingPtX,this._pendingPtY),this._pendingPtDist=0)},a.prototype._expandData=function(){if(!(this.data instanceof Array)){for(var t=[],e=0;e<this._len;e++)t[e]=this.data[e];this.data=t}},a.prototype.toStatic=function(){var t;this._saveData&&(this._drawPendingPt(),(t=this.data)instanceof Array)&&(t.length=this._len,Xa)&&11<this._len&&(this.data=new Float32Array(t))},a.prototype.getBoundingRect=function(){Ra[0]=Ra[1]=Ea[0]=Ea[1]=Number.MAX_VALUE,Na[0]=Na[1]=za[0]=za[1]=-Number.MAX_VALUE;for(var t,e=this.data,n=0,i=0,r=0,o=0,a=0;a<this._len;){var E=e[a++],z=1===a;switch(z&&(r=n=e[a],o=i=e[a+1]),E){case Y.M:n=r=e[a++],i=o=e[a++],Ea[0]=r,Ea[1]=o,za[0]=r,za[1]=o;break;case Y.L:Da(n,i,e[a],e[a+1],Ea,za),n=e[a++],i=e[a++];break;case Y.C:G=H=m=y=V=g=f=d=p=c=F=B=h=u=l=s=void 0;var s=n,l=i,u=e[a++],h=e[a++],B=e[a++],F=e[a++],c=e[a],p=e[a+1],d=Ea,f=za,g=Fn,V=En,y=g(s,u,B,c,Aa);d[0]=1/0,d[1]=1/0,f[0]=-1/0,f[1]=-1/0;for(var m=0;m<y;m++){var H=V(s,u,B,c,Aa[m]);d[0]=ba(H,d[0]),f[0]=wa(H,f[0])}for(y=g(l,h,F,p,La),m=0;m<y;m++){var G=V(l,h,F,p,La[m]);d[1]=ba(G,d[1]),f[1]=wa(G,f[1])}d[0]=ba(s,d[0]),f[0]=wa(s,f[0]),d[0]=ba(c,d[0]),f[0]=wa(c,f[0]),d[1]=ba(l,d[1]),f[1]=wa(l,f[1]),d[1]=ba(p,d[1]),f[1]=wa(p,f[1]),n=e[a++],i=e[a++];break;case Y.Q:g=n,P=i,M=e[a++],x=e[a++],S=e[a],v=e[a+1],w=Ea,T=za,t=b=t=_=void 0,_=Gn,t=wa(ba((b=Un)(g,M,S),1),0),b=wa(ba(b(P,x,v),1),0),M=_(g,M,S,t),t=_(P,x,v,b),w[0]=ba(g,S,M),w[1]=ba(P,v,t),T[0]=wa(g,S,M),T[1]=wa(P,v,t),n=e[a++],i=e[a++];break;case Y.A:var v,_=e[a++],x=e[a++],b=e[a++],w=e[a++],S=e[a++],M=e[a++]+S,T=(a+=1,!e[a++]),C=(z&&(r=Va(S)*b+_,o=Ha(S)*w+x),N=v=U=W=R=O=P=L=A=D=I=k=C=void 0,_),k=x,I=b,D=w,A=S,L=M,P=T,O=Ea,R=za,W=ne,U=ie;if((v=Math.abs(A-L))%Ta<1e-4&&1e-4<v)O[0]=C-I,O[1]=k-D,R[0]=C+I,R[1]=k+D;else{Ca[0]=Ma(A)*I+C,Ca[1]=Sa(A)*D+k,ka[0]=Ma(L)*I+C,ka[1]=Sa(L)*D+k,W(O,Ca,ka),U(R,Ca,ka),(A%=Ta)<0&&(A+=Ta),(L%=Ta)<0&&(L+=Ta),L<A&&!P?L+=Ta:A<L&&P&&(A+=Ta),P&&(v=L,L=A,A=v);for(var N=0;N<L;N+=Math.PI/2)A<N&&(Ia[0]=Ma(N)*I+C,Ia[1]=Sa(N)*D+k,W(O,Ia,O),U(R,Ia,R))}n=Va(M)*b+_,i=Ha(M)*w+x;break;case Y.R:Da(r=n=e[a++],o=i=e[a++],r+e[a++],o+e[a++],Ea,za);break;case Y.Z:n=r,i=o}ne(Ra,Ra,Ea),ie(Na,Na,za)}return 0===a&&(Ra[0]=Ra[1]=Na[0]=Na[1]=0),new X(Ra[0],Ra[1],Na[0]-Ra[0],Na[1]-Ra[1])},a.prototype._calculateLength=function(){for(var t=this.data,e=this._len,n=this._ux,i=this._uy,r=0,o=0,a=0,s=0,l=(this._pathSegLen||(this._pathSegLen=[]),this._pathSegLen),u=0,h=0,c=0;c<e;){var p=t[c++],d=1===c,f=(d&&(a=r=t[c],s=o=t[c+1]),-1);switch(p){case Y.M:r=a=t[c++],o=s=t[c++];break;case Y.L:var g=t[c++],y=(_=t[c++])-o;(Ga(k=g-r)>n||Ga(y)>i||c===e-1)&&(f=Math.sqrt(k*k+y*y),r=g,o=_);break;case Y.C:var m=t[c++],v=t[c++],g=t[c++],_=t[c++],x=t[c++],b=t[c++],f=function(t,e,n,i,r,o,a,s,l){for(var u=t,h=e,c=0,p=1/l,d=1;d<=l;d++){var f=d*p,g=En(t,n,r,a,f),f=En(e,i,o,s,f),y=g-u,m=f-h;c+=Math.sqrt(y*y+m*m),u=g,h=f}return c}(r,o,m,v,g,_,x,b,10),r=x,o=b;break;case Y.Q:f=function(t,e,n,i,r,o,a){for(var s=t,l=e,u=0,h=1/a,c=1;c<=a;c++){var p=c*h,d=Gn(t,n,r,p),p=Gn(e,i,o,p),f=d-s,g=p-l;u+=Math.sqrt(f*f+g*g),s=d,l=p}return u}(r,o,m=t[c++],v=t[c++],g=t[c++],_=t[c++],10),r=g,o=_;break;case Y.A:var x=t[c++],b=t[c++],w=t[c++],S=t[c++],M=t[c++],T=t[c++],C=T+M;c+=1,d&&(a=Va(M)*w+x,s=Ha(M)*S+b),f=Fa(w,S)*Ba(Ua,Math.abs(T)),r=Va(C)*w+x,o=Ha(C)*S+b;break;case Y.R:a=r=t[c++],s=o=t[c++];f=2*t[c++]+2*t[c++];break;case Y.Z:var k=a-r,y=s-o;f=Math.sqrt(k*k+y*y),r=a,o=s}0<=f&&(u+=l[h++]=f)}return this._pathLen=u},a.prototype.rebuildPath=function(t,e){var n,i,r,o,a,s,l,u,h=this.data,E=this._ux,z=this._uy,B=this._len,c=e<1,p=0,d=0,f=0;if(!c||(this._pathSegLen||this._calculateLength(),a=this._pathSegLen,s=e*this._pathLen))t:for(var g=0;g<B;){var y=h[g++],F=1===g;switch(F&&(n=r=h[g],i=o=h[g+1]),y!==Y.L&&0<f&&(t.lineTo(l,u),f=0),y){case Y.M:n=r=h[g++],i=o=h[g++],t.moveTo(r,o);break;case Y.L:var m=h[g++],v=h[g++],_=Ga(m-r),x=Ga(v-o);if(E<_||z<x){if(c){if(s<p+(N=a[d++])){var b=(s-p)/N;t.lineTo(r*(1-b)+m*b,o*(1-b)+v*b);break t}p+=N}t.lineTo(m,v),r=m,o=v,f=0}else{_=_*_+x*x;f<_&&(l=m,u=v,f=_)}break;case Y.C:var w=h[g++],S=h[g++],M=h[g++],T=h[g++],x=h[g++],_=h[g++];if(c){if(s<p+(N=a[d++])){Vn(r,w,M,x,b=(s-p)/N,Pa),Vn(o,S,T,_,b,Oa),t.bezierCurveTo(Pa[1],Oa[1],Pa[2],Oa[2],Pa[3],Oa[3]);break t}p+=N}t.bezierCurveTo(w,S,M,T,x,_),r=x,o=_;break;case Y.Q:w=h[g++],S=h[g++],M=h[g++],T=h[g++];if(c){if(s<p+(N=a[d++])){Xn(r,w,M,b=(s-p)/N,Pa),Xn(o,S,T,b,Oa),t.quadraticCurveTo(Pa[1],Oa[1],Pa[2],Oa[2]);break t}p+=N}t.quadraticCurveTo(w,S,M,T),r=M,o=T;break;case Y.A:var C=h[g++],k=h[g++],I=h[g++],D=h[g++],A=h[g++],L=h[g++],P=h[g++],V=!h[g++],H=D<I?I:D,O=.001<Ga(I-D),R=A+L,G=!1;if(c&&(s<p+(N=a[d++])&&(R=A+L*(s-p)/N,G=!0),p+=N),O&&t.ellipse?t.ellipse(C,k,I,D,P,A,R,V):t.arc(C,k,H,A,R,V),G)break t;F&&(n=Va(A)*I+C,i=Ha(A)*D+k),r=Va(R)*I+C,o=Ha(R)*D+k;break;case Y.R:n=r=h[g],i=o=h[g+1],m=h[g++],v=h[g++];var N,L=h[g++],O=h[g++];if(c){if(s<p+(N=a[d++])){P=s-p;t.moveTo(m,v),t.lineTo(m+Ba(P,L),v),0<(P-=L)&&t.lineTo(m+L,v+Ba(P,O)),0<(P-=O)&&t.lineTo(m+Fa(L-P,0),v+O),0<(P-=L)&&t.lineTo(m,v+Fa(O-P,0));break t}p+=N}t.rect(m,v,L,O);break;case Y.Z:if(c){if(s<p+(N=a[d++])){b=(s-p)/N;t.lineTo(r*(1-b)+n*b,o*(1-b)+i*b);break t}p+=N}t.closePath(),r=n,o=i}}},a.prototype.clone=function(){var t=new a,e=this.data;return t.data=e.slice?e.slice():Array.prototype.slice.call(e),t._len=this._len,t},a.CMD=Y,a.initDefaultProps=((uu=a.prototype)._saveData=!0,uu._ux=0,uu._uy=0,uu._pendingPtDist=0,void(uu._version=0));var Za=a;function a(t){this.dpr=1,this._xi=0,this._yi=0,this._x0=0,this._y0=0,this._len=0,t&&(this._saveData=!1),this._saveData&&(this.data=[])}function ja(t,e,n,i,r,o,a){var s;if(0!==r)return s=0,!(e+(r=r)<a&&i+r<a||a<e-r&&a<i-r||t+r<o&&n+r<o||o<t-r&&o<n-r)&&(t===n?Math.abs(o-t)<=r/2:(o=(s=(e-i)/(t-n))*o-a+(t*i-n*e)/(t-n))*o/(s*s+1)<=r/2*r/2)}var Ka=2*Math.PI;function $a(t){return(t%=Ka)<0&&(t+=Ka),t}var Qa=2*Math.PI;function Ja(t,e,n,i,r,o){return e<o&&i<o||o<e&&o<i||i===e?0:(n=(o=(o-e)/(i-e))*(n-t)+t)===r?1/0:r<n?1!=o&&0!=o?i<e?1:-1:i<e?.5:-.5:0}var ts=Za.CMD,es=2*Math.PI,ns=1e-4;var is=[-1,-1,-1],rs=[-1,-1];function os(t,e,n,i,r,o,a,s,l,u){if(e<u&&i<u&&o<u&&s<u||u<e&&u<i&&u<o&&u<s)return 0;var h=Bn(e,i,o,s,u,is);if(0===h)return 0;for(var c,p=0,d=-1,f=void 0,g=void 0,y=0;y<h;y++){var m=is[y],v=0===m||1===m?.5:1;En(t,n,r,a,m)<l||(d<0&&(d=Fn(e,i,o,s,rs),rs[1]<rs[0]&&1<d&&(c=void 0,c=rs[0],rs[0]=rs[1],rs[1]=c),f=En(e,i,o,s,rs[0]),1<d)&&(g=En(e,i,o,s,rs[1])),2===d?m<rs[0]?p+=f<e?v:-v:m<rs[1]?p+=g<f?v:-v:p+=s<g?v:-v:m<rs[0]?p+=f<e?v:-v:p+=s<f?v:-v)}return p}function as(t,e,n,i,r,o,a,s){if(e<s&&i<s&&o<s||s<e&&s<i&&s<o)return 0;c=is,h=(l=e)-2*(u=i)+(h=o),u=2*(u-l),l-=s=s,s=0,Rn(h)?Nn(u)&&0<=(p=-l/u)&&p<=1&&(c[s++]=p):Rn(l=u*u-4*h*l)?0<=(p=-u/(2*h))&&p<=1&&(c[s++]=p):0<l&&(d=(-u-(l=kn(l)))/(2*h),0<=(p=(-u+l)/(2*h))&&p<=1&&(c[s++]=p),0<=d)&&d<=1&&(c[s++]=d);var l,u,h,c,p,d,f=s;if(0===f)return 0;var g=Un(e,i,o);if(0<=g&&g<=1){for(var y=0,m=Gn(e,i,o,g),v=0;v<f;v++){var _=0===is[v]||1===is[v]?.5:1;Gn(t,n,r,is[v])<a||(is[v]<g?y+=m<e?_:-_:y+=o<m?_:-_)}return y}return _=0===is[0]||1===is[0]?.5:1,Gn(t,n,r,is[0])<a?0:o<e?_:-_}function ss(t,e,n,i,r){for(var o,a=t.data,s=t.len(),l=0,u=0,h=0,c=0,p=0,d=0;d<s;){var f=a[d++],g=1===d;switch(f===ts.M&&1<d&&(n||(l+=Ja(u,h,c,p,i,r))),g&&(c=u=a[d],p=h=a[d+1]),f){case ts.M:u=c=a[d++],h=p=a[d++];break;case ts.L:if(n){if(ja(u,h,a[d],a[d+1],e,i,r))return!0}else l+=Ja(u,h,a[d],a[d+1],i,r)||0;u=a[d++],h=a[d++];break;case ts.C:if(n){if(function(t,e,n,i,r,o,a,s,l,u,h){if(0!==l)return!(e+(l=l)<h&&i+l<h&&o+l<h&&s+l<h||h<e-l&&h<i-l&&h<o-l&&h<s-l||t+l<u&&n+l<u&&r+l<u&&a+l<u||u<t-l&&u<n-l&&u<r-l&&u<a-l)&&Hn(t,e,n,i,r,o,a,s,u,h,null)<=l/2}(u,h,a[d++],a[d++],a[d++],a[d++],a[d],a[d+1],e,i,r))return!0}else l+=os(u,h,a[d++],a[d++],a[d++],a[d++],a[d],a[d+1],i,r)||0;u=a[d++],h=a[d++];break;case ts.Q:if(n){if(function(t,e,n,i,r,o,a,s,l){if(0!==a)return!(e+(a=a)<l&&i+a<l&&o+a<l||l<e-a&&l<i-a&&l<o-a||t+a<s&&n+a<s&&r+a<s||s<t-a&&s<n-a&&s<r-a)&&Yn(t,e,n,i,r,o,s,l,null)<=a/2}(u,h,a[d++],a[d++],a[d],a[d+1],e,i,r))return!0}else l+=as(u,h,a[d++],a[d++],a[d],a[d+1],i,r)||0;u=a[d++],h=a[d++];break;case ts.A:var y=a[d++],m=a[d++],v=a[d++],_=a[d++],x=a[d++],b=a[d++],w=(d+=1,!!(1-a[d++])),S=Math.cos(x)*v+y,M=Math.sin(x)*_+m,T=(g?(c=S,p=M):l+=Ja(u,h,S,M,i,r),(i-y)*_/v+y);if(n){if(function(t,e,n,i,r,o,a,s,l){if(0!==a)return a=a,s-=t,l-=e,!(n<(t=Math.sqrt(s*s+l*l))-a||t+a<n)&&(Math.abs(i-r)%Qa<1e-4||((r=o?(e=i,i=$a(r),$a(e)):(i=$a(i),$a(r)))<i&&(r+=Qa),(t=Math.atan2(l,s))<0&&(t+=Qa),i<=t&&t<=r)||i<=t+Qa&&t+Qa<=r)}(y,m,_,x,x+b,w,e,T,r))return!0}else l+=function(t,e,n,i,r,o,a,s){if(n<(s-=e)||s<-n)return 0;var e=Math.sqrt(n*n-s*s);if(is[0]=-e,is[1]=e,(n=Math.abs(i-r))<1e-4)return 0;if(es-1e-4<=n)return r=es,h=o?1:-1,a>=is[i=0]+t&&a<=is[1]+t?h:0;r<i&&(e=i,i=r,r=e),i<0&&(i+=es,r+=es);for(var l=0,u=0;u<2;u++){var h,c=is[u];a<c+t&&(h=o?1:-1,i<=(c=(c=Math.atan2(s,c))<0?es+c:c)&&c<=r||i<=c+es&&c+es<=r)&&(l+=h=c>Math.PI/2&&c<1.5*Math.PI?-h:h)}return l}(y,m,_,x,x+b,w,T,r);u=Math.cos(x+b)*v+y,h=Math.sin(x+b)*_+m;break;case ts.R:c=u=a[d++],p=h=a[d++];if(S=c+a[d++],M=p+a[d++],n){if(ja(c,p,S,p,e,i,r)||ja(S,p,S,M,e,i,r)||ja(S,M,c,M,e,i,r)||ja(c,M,c,p,e,i,r))return!0}else l=(l+=Ja(S,p,S,M,i,r))+Ja(c,M,c,p,i,r);break;case ts.Z:if(n){if(ja(u,h,c,p,e,i,r))return!0}else l+=Ja(u,h,c,p,i,r);u=c,h=p}}return n||(t=h,o=p,Math.abs(t-o)<ns)||(l+=Ja(u,h,c,p,i,r)||0),0!==l}var ls,us=z({fill:"#000",stroke:null,strokePercent:1,fillOpacity:1,strokeOpacity:1,lineDashOffset:0,lineWidth:1,lineCap:"butt",miterLimit:10,strokeNoScale:!1,strokeFirst:!1},ga),hs={style:z({fill:!0,stroke:!0,strokePercent:!0,fillOpacity:!0,strokeOpacity:!0,lineDashOffset:!0,lineWidth:!0,miterLimit:!0},ya.style)},cs=vr.concat(["invisible","culling","z","z2","zlevel","parent"]),j=(u(s,ls=n),s.prototype.update=function(){var e=this,t=(ls.prototype.update.call(this),this.style);if(t.decal){var n,i=this._decalEl=this._decalEl||new s,r=(i.buildPath===s.prototype.buildPath&&(i.buildPath=function(t){e.buildPath(t,e.shape)}),i.silent=!0,i.style);for(n in t)r[n]!==t[n]&&(r[n]=t[n]);r.fill=t.fill?t.decal:null,r.decal=null,r.shadowColor=null,t.strokeFirst&&(r.stroke=null);for(var o=0;o<cs.length;++o)i[cs[o]]=this[cs[o]];i.__dirty|=mn}else this._decalEl&&(this._decalEl=null)},s.prototype.getDecalElement=function(){return this._decalEl},s.prototype._init=function(t){var e=I(t),n=(this.shape=this.getDefaultShape(),this.getDefaultStyle());n&&this.useStyle(n);for(var i=0;i<e.length;i++){var r=e[i],o=t[r];"style"===r?this.style?P(this.style,o):this.useStyle(o):"shape"===r?P(this.shape,o):ls.prototype.attrKV.call(this,r,o)}this.style||this.useStyle({})},s.prototype.getDefaultStyle=function(){return null},s.prototype.getDefaultShape=function(){return{}},s.prototype.canBeInsideText=function(){return this.hasFill()},s.prototype.getInsideTextFill=function(){var t,e=this.style.fill;if("none"!==e){if(V(e))return.5<(t=bi(e,0))?lr:.2<t?"#eee":ur;if(e)return ur}return lr},s.prototype.getInsideTextStroke=function(t){var e=this.style.fill;if(V(e)){var n=this.__zr;if(!(!n||!n.isDarkMode())==bi(t,0)<.4)return e}},s.prototype.buildPath=function(t,e,n){},s.prototype.pathUpdated=function(){this.__dirty&=~vn},s.prototype.getUpdatedPathProxy=function(t){return this.path||this.createPathProxy(),this.path.beginPath(),this.buildPath(this.path,this.shape,t),this.path},s.prototype.createPathProxy=function(){this.path=new Za(!1)},s.prototype.hasStroke=function(){var t=this.style,e=t.stroke;return!(null==e||"none"===e||!(0<t.lineWidth))},s.prototype.hasFill=function(){var t=this.style.fill;return null!=t&&"none"!==t},s.prototype.getBoundingRect=function(){var t,e,n=this._rect,i=this.style,r=!n;return r&&(t=!1,this.path||(t=!0,this.createPathProxy()),e=this.path,(t||this.__dirty&vn)&&(e.beginPath(),this.buildPath(e,this.shape,!1),this.pathUpdated()),n=e.getBoundingRect()),this._rect=n,this.hasStroke()&&this.path&&0<this.path.len()?(t=this._rectStroke||(this._rectStroke=n.clone()),(this.__dirty||r)&&(t.copy(n),e=i.strokeNoScale?this.getLineScale():1,r=i.lineWidth,this.hasFill()||(i=this.strokeContainThreshold,r=Math.max(r,null==i?4:i)),1e-10<e)&&(t.width+=r/e,t.height+=r/e,t.x-=r/e/2,t.y-=r/e/2),t):n},s.prototype.contain=function(t,e){var n=this.transformCoordToLocal(t,e),i=this.getBoundingRect(),r=this.style;if(t=n[0],e=n[1],i.contain(t,e)){n=this.path;if(this.hasStroke()){i=r.lineWidth,r=r.strokeNoScale?this.getLineScale():1;if(1e-10<r&&(this.hasFill()||(i=Math.max(i,this.strokeContainThreshold)),ss(n,i/r,!0,t,e)))return!0}if(this.hasFill())return ss(n,0,!1,t,e)}return!1},s.prototype.dirtyShape=function(){this.__dirty|=vn,this._rect&&(this._rect=null),this._decalEl&&this._decalEl.dirtyShape(),this.markRedraw()},s.prototype.dirty=function(){this.dirtyStyle(),this.dirtyShape()},s.prototype.animateShape=function(t){return this.animate("shape",t)},s.prototype.updateDuringAnimation=function(t){"style"===t?this.dirtyStyle():"shape"===t?this.dirtyShape():this.markRedraw()},s.prototype.attrKV=function(t,e){"shape"===t?this.setShape(e):ls.prototype.attrKV.call(this,t,e)},s.prototype.setShape=function(t,e){var n=(n=this.shape)||(this.shape={});return"string"==typeof t?n[t]=e:P(n,t),this.dirtyShape(),this},s.prototype.shapeChanged=function(){return!!(this.__dirty&vn)},s.prototype.createStyle=function(t){return Et(us,t)},s.prototype._innerSaveToNormal=function(t){ls.prototype._innerSaveToNormal.call(this,t);var e=this._normalState;t.shape&&!e.shape&&(e.shape=P({},this.shape))},s.prototype._applyStateObj=function(t,e,n,i,r,o){ls.prototype._applyStateObj.call(this,t,e,n,i,r,o);var a,s=!(e&&i);if(e&&e.shape?r?i?a=e.shape:(a=P({},n.shape),P(a,e.shape)):(a=P({},(i?this:n).shape),P(a,e.shape)):s&&(a=n.shape),a)if(r){this.shape=P({},this.shape);for(var l={},u=I(a),h=0;h<u.length;h++){var c=u[h];"object"==typeof a[c]?this.shape[c]=a[c]:l[c]=a[c]}this._transitionState(t,{shape:l},o)}else this.shape=a,this.dirtyShape()},s.prototype._mergeStates=function(t){for(var e,n=ls.prototype._mergeStates.call(this,t),i=0;i<t.length;i++){var r=t[i];r.shape&&this._mergeStyle(e=e||{},r.shape)}return e&&(n.shape=e),n},s.prototype.getAnimationStyleProps=function(){return hs},s.prototype.isZeroArea=function(){return!1},s.extend=function(n){u(r,i=s),r.prototype.getDefaultStyle=function(){return y(n.style)},r.prototype.getDefaultShape=function(){return y(n.shape)};var i,t,e=r;function r(t){var e=i.call(this,t)||this;return n.init&&n.init.call(e,t),e}for(t in n)"function"==typeof n[t]&&(e.prototype[t]=n[t]);return e},s.initDefaultProps=((uu=s.prototype).type="path",uu.strokeContainThreshold=5,uu.segmentIgnoreThreshold=0,uu.subPixelOptimize=!1,uu.autoBatch=!1,void(uu.__dirty=2|mn|vn)),s);function s(t){return ls.call(this,t)||this}var ps,ds=z({strokeFirst:!0,font:K,x:0,y:0,textAlign:"left",textBaseline:"top",miterLimit:2},us),fs=(u(gs,ps=n),gs.prototype.hasStroke=function(){var t=this.style,e=t.stroke;return null!=e&&"none"!==e&&0<t.lineWidth},gs.prototype.hasFill=function(){var t=this.style.fill;return null!=t&&"none"!==t},gs.prototype.createStyle=function(t){return Et(ds,t)},gs.prototype.setBoundingRect=function(t){this._rect=t},gs.prototype.getBoundingRect=function(){var t,e=this.style;return this._rect||(null!=(t=e.text)?t+="":t="",(t=wr(t,e.font,e.textAlign,e.textBaseline)).x+=e.x||0,t.y+=e.y||0,this.hasStroke()&&(e=e.lineWidth,t.x-=e/2,t.y-=e/2,t.width+=e,t.height+=e),this._rect=t),this._rect},gs.initDefaultProps=void(gs.prototype.dirtyRectTolerance=10),gs);function gs(){return null!==ps&&ps.apply(this,arguments)||this}fs.prototype.type="tspan";var ys=z({x:0,y:0},ga),ms={style:z({x:!0,y:!0,width:!0,height:!0,sx:!0,sy:!0,sWidth:!0,sHeight:!0},ya.style)};u(xs,vs=n),xs.prototype.createStyle=function(t){return Et(ys,t)},xs.prototype._getSize=function(t){var e,n=this.style,i=n[t];return null!=i?i:(i=(i=n.image)&&"string"!=typeof i&&i.width&&i.height?n.image:this.__image)?null==(e=n[n="width"===t?"height":"width"])?i[t]:i[t]/i[n]*e:0},xs.prototype.getWidth=function(){return this._getSize("width")},xs.prototype.getHeight=function(){return this._getSize("height")},xs.prototype.getAnimationStyleProps=function(){return ms},xs.prototype.getBoundingRect=function(){var t=this.style;return this._rect||(this._rect=new X(t.x||0,t.y||0,this.getWidth(),this.getHeight())),this._rect};var vs,_s=xs;function xs(){return null!==vs&&vs.apply(this,arguments)||this}_s.prototype.type="image";var bs=Math.round;function ws(t,e,n){var i,r,o;if(e)return i=e.x1,r=e.x2,o=e.y1,e=e.y2,t.x1=i,t.x2=r,t.y1=o,t.y2=e,(n=n&&n.lineWidth)&&(bs(2*i)===bs(2*r)&&(t.x1=t.x2=Ss(i,n,!0)),bs(2*o)===bs(2*e))&&(t.y1=t.y2=Ss(o,n,!0)),t}function Ss(t,e,n){var i;return e?((i=bs(2*t))+bs(e))%2==0?i/2:(i+(n?1:-1))/2:t}var Ms,Ts=function(){this.x=0,this.y=0,this.width=0,this.height=0},Cs={},ks=(u(Is,Ms=j),Is.prototype.getDefaultShape=function(){return new Ts},Is.prototype.buildPath=function(t,e){var n,i,r,o,a,s,l,u,h,c,p,d,f,g;this.subPixelOptimize?(n=(a=function(t,e,n){var i,r,o;if(e)return i=e.x,r=e.y,o=e.width,e=e.height,t.x=i,t.y=r,t.width=o,t.height=e,(n=n&&n.lineWidth)&&(t.x=Ss(i,n,!0),t.y=Ss(r,n,!0),t.width=Math.max(Ss(i+o,n,!1)-t.x,0===o?0:1),t.height=Math.max(Ss(r+e,n,!1)-t.y,0===e?0:1)),t}(Cs,e,this.style)).x,i=a.y,r=a.width,o=a.height,a.r=e.r,e=a):(n=e.x,i=e.y,r=e.width,o=e.height),e.r?(a=t,p=(e=e).x,d=e.y,f=e.width,g=e.height,e=e.r,f<0&&(p+=f,f=-f),g<0&&(d+=g,g=-g),"number"==typeof e?s=l=u=h=e:e instanceof Array?1===e.length?s=l=u=h=e[0]:2===e.length?(s=u=e[0],l=h=e[1]):3===e.length?(s=e[0],l=h=e[1],u=e[2]):(s=e[0],l=e[1],u=e[2],h=e[3]):s=l=u=h=0,f<s+l&&(s*=f/(c=s+l),l*=f/c),f<u+h&&(u*=f/(c=u+h),h*=f/c),g<l+u&&(l*=g/(c=l+u),u*=g/c),g<s+h&&(s*=g/(c=s+h),h*=g/c),a.moveTo(p+s,d),a.lineTo(p+f-l,d),0!==l&&a.arc(p+f-l,d+l,l,-Math.PI/2,0),a.lineTo(p+f,d+g-u),0!==u&&a.arc(p+f-u,d+g-u,u,0,Math.PI/2),a.lineTo(p+h,d+g),0!==h&&a.arc(p+h,d+g-h,h,Math.PI/2,Math.PI),a.lineTo(p,d+s),0!==s&&a.arc(p+s,d+s,s,Math.PI,1.5*Math.PI)):t.rect(n,i,r,o)},Is.prototype.isZeroArea=function(){return!this.shape.width||!this.shape.height},Is);function Is(t){return Ms.call(this,t)||this}ks.prototype.type="rect";var Ds,As={fill:"#000"},Ls={style:z({fill:!0,stroke:!0,fillOpacity:!0,strokeOpacity:!0,lineWidth:!0,fontSize:!0,lineHeight:!0,width:!0,height:!0,textShadowColor:!0,textShadowBlur:!0,textShadowOffsetX:!0,textShadowOffsetY:!0,backgroundColor:!0,padding:!0,borderColor:!0,borderWidth:!0,borderRadius:!0},ya.style)},Ps=(u(Os,Ds=n),Os.prototype.childrenRef=function(){return this._children},Os.prototype.update=function(){Ds.prototype.update.call(this),this.styleChanged()&&this._updateSubTexts();for(var t=0;t<this._children.length;t++){var e=this._children[t];e.zlevel=this.zlevel,e.z=this.z,e.z2=this.z2,e.culling=this.culling,e.cursor=this.cursor,e.invisible=this.invisible}},Os.prototype.updateTransform=function(){var t=this.innerTransformable;t?(t.updateTransform(),t.transform&&(this.transform=t.transform)):Ds.prototype.updateTransform.call(this)},Os.prototype.getLocalTransform=function(t){var e=this.innerTransformable;return e?e.getLocalTransform(t):Ds.prototype.getLocalTransform.call(this,t)},Os.prototype.getComputedTransform=function(){return this.__hostTarget&&(this.__hostTarget.getComputedTransform(),this.__hostTarget.updateInnerText(!0)),Ds.prototype.getComputedTransform.call(this)},Os.prototype._updateSubTexts=function(){var t;this._childCursor=0,Bs(t=this.style),O(t.rich,Bs),this.style.rich?this._updateRichTexts():this._updatePlainTexts(),this._children.length=this._childCursor,this.styleUpdated()},Os.prototype.addSelfToZr=function(t){Ds.prototype.addSelfToZr.call(this,t);for(var e=0;e<this._children.length;e++)this._children[e].__zr=t},Os.prototype.removeSelfFromZr=function(t){Ds.prototype.removeSelfFromZr.call(this,t);for(var e=0;e<this._children.length;e++)this._children[e].__zr=null},Os.prototype.getBoundingRect=function(){if(this.styleChanged()&&this._updateSubTexts(),!this._rect){for(var t=new X(0,0,0,0),e=this._children,n=[],i=null,r=0;r<e.length;r++){var o=e[r],a=o.getBoundingRect(),o=o.getLocalTransform(n);o?(t.copy(a),t.applyTransform(o),(i=i||t.clone()).union(t)):(i=i||a.clone()).union(a)}this._rect=i||t}return this._rect},Os.prototype.setDefaultTextStyle=function(t){this._defaultStyle=t||As},Os.prototype.setTextContent=function(t){},Os.prototype._mergeStyle=function(t,e){var n,i;return e&&(n=e.rich,i=t.rich||n&&{},P(t,e),n&&i?(this._mergeRich(i,n),t.rich=i):i&&(t.rich=i)),t},Os.prototype._mergeRich=function(t,e){for(var n=I(e),i=0;i<n.length;i++){var r=n[i];t[r]=t[r]||{},P(t[r],e[r])}},Os.prototype.getAnimationStyleProps=function(){return Ls},Os.prototype._getOrCreateChild=function(t){var e=this._children[this._childCursor];return e&&e instanceof t||(e=new t),(this._children[this._childCursor++]=e).__zr=this.__zr,e.parent=this,e},Os.prototype._updatePlainTexts=function(){for(var t,e=this.style,n=e.font||K,i=e.padding,r=function(t,e){null!=t&&(t+="");var n,i=e.overflow,r=e.padding,o=e.font,a="truncate"===i,s=Tr(o),l=N(e.lineHeight,s),u=!!e.backgroundColor,h="truncate"===e.lineOverflow,c=e.width,i=(n=null==c||"break"!==i&&"breakAll"!==i?t?t.split("\n"):[]:t?pa(t,e.font,c,"breakAll"===i,0).lines:[]).length*l,p=N(e.height,i);if(p<i&&h&&(h=Math.floor(p/l),n=n.slice(0,h)),t&&a&&null!=c)for(var d=ra(c,o,e.ellipsis,{minChar:e.truncateMinChar,placeholder:e.placeholder}),f=0;f<n.length;f++)n[f]=oa(n[f],d);for(var h=p,g=0,f=0;f<n.length;f++)g=Math.max(xr(n[f],o),g);return null==c&&(c=g),t=g,r&&(h+=r[0]+r[2],t+=r[1]+r[3],c+=r[1]+r[3]),{lines:n,height:p,outerWidth:t=u?c:t,outerHeight:h,lineHeight:l,calculatedLineHeight:s,contentWidth:g,contentHeight:i,width:c}}(Gs(e),e),o=Ws(e),a=!!e.backgroundColor,s=r.outerHeight,l=r.outerWidth,u=r.contentWidth,h=r.lines,c=r.lineHeight,p=this._defaultStyle,d=e.x||0,f=e.y||0,g=e.align||p.align||"left",y=e.verticalAlign||p.verticalAlign||"top",m=d,v=Mr(f,r.contentHeight,y),_=((o||i)&&(t=Sr(d,l,g),f=Mr(f,s,y),o)&&this._renderBackground(e,e,t,f,l,s),v+=c/2,i&&(m=Hs(d,g,i),"top"===y?v+=i[0]:"bottom"===y&&(v-=i[2])),0),o=!1,x=(Vs(("fill"in e?e:(o=!0,p)).fill)),b=(Fs("stroke"in e?e.stroke:a||p.autoStroke&&!o?null:(_=2,p.stroke))),w=0<e.textShadowBlur,S=null!=e.width&&("truncate"===e.overflow||"break"===e.overflow||"breakAll"===e.overflow),M=r.calculatedLineHeight,T=0;T<h.length;T++){var C=this._getOrCreateChild(fs),k=C.createStyle();C.useStyle(k),k.text=h[T],k.x=m,k.y=v,g&&(k.textAlign=g),k.textBaseline="middle",k.opacity=e.opacity,k.strokeFirst=!0,w&&(k.shadowBlur=e.textShadowBlur||0,k.shadowColor=e.textShadowColor||"transparent",k.shadowOffsetX=e.textShadowOffsetX||0,k.shadowOffsetY=e.textShadowOffsetY||0),k.stroke=b,k.fill=x,b&&(k.lineWidth=e.lineWidth||_,k.lineDash=e.lineDash,k.lineDashOffset=e.lineDashOffset||0),k.font=n,zs(k,e),v+=c,S&&C.setBoundingRect(new X(Sr(k.x,e.width,k.textAlign),Mr(k.y,M,k.textBaseline),u,M))}},Os.prototype._updateRichTexts=function(){for(var t=this.style,e=ua(Gs(t),t),n=e.width,i=e.outerWidth,r=e.outerHeight,o=t.padding,a=t.x||0,s=t.y||0,l=this._defaultStyle,u=t.align||l.align,l=t.verticalAlign||l.verticalAlign,a=Sr(a,i,u),u=Mr(s,r,l),h=a,c=u,p=(o&&(h+=o[3],c+=o[0]),h+n),d=(Ws(t)&&this._renderBackground(t,t,a,u,i,r),!!t.backgroundColor),f=0;f<e.lines.length;f++){for(var g=e.lines[f],y=g.tokens,m=y.length,v=g.lineHeight,_=g.width,x=0,b=h,w=p,S=m-1,M=void 0;x<m&&(!(M=y[x]).align||"left"===M.align);)this._placeToken(M,t,v,c,b,"left",d),_-=M.width,b+=M.width,x++;for(;0<=S&&"right"===(M=y[S]).align;)this._placeToken(M,t,v,c,w,"right",d),_-=M.width,w-=M.width,S--;for(b+=(n-(b-h)-(p-w)-_)/2;x<=S;)M=y[x],this._placeToken(M,t,v,c,b+M.width/2,"center",d),b+=M.width,x++;c+=v}},Os.prototype._placeToken=function(t,e,n,i,r,o,a){var s=e.rich[t.styleName]||{},l=(s.text=t.text,t.verticalAlign),u=i+n/2;"top"===l?u=i+t.height/2:"bottom"===l&&(u=i+n-t.height/2);!t.isLineHolder&&Ws(s)&&this._renderBackground(s,e,"right"===o?r-t.width:"center"===o?r-t.width/2:r,u-t.height/2,t.width,t.height);var l=!!s.backgroundColor,i=t.textPadding,n=(i&&(r=Hs(r,o,i),u-=t.height/2-i[0]-t.innerHeight/2),this._getOrCreateChild(fs)),i=n.createStyle(),h=(n.useStyle(i),this._defaultStyle),c=!1,p=0,d=Vs(("fill"in s?s:"fill"in e?e:(c=!0,h)).fill),l=Fs("stroke"in s?s.stroke:"stroke"in e?e.stroke:l||a||h.autoStroke&&!c?null:(p=2,h.stroke)),a=0<s.textShadowBlur||0<e.textShadowBlur,c=(i.text=t.text,i.x=r,i.y=u,a&&(i.shadowBlur=s.textShadowBlur||e.textShadowBlur||0,i.shadowColor=s.textShadowColor||e.textShadowColor||"transparent",i.shadowOffsetX=s.textShadowOffsetX||e.textShadowOffsetX||0,i.shadowOffsetY=s.textShadowOffsetY||e.textShadowOffsetY||0),i.textAlign=o,i.textBaseline="middle",i.font=t.font||K,i.opacity=wt(s.opacity,e.opacity,1),zs(i,s),l&&(i.lineWidth=wt(s.lineWidth,e.lineWidth,p),i.lineDash=N(s.lineDash,e.lineDash),i.lineDashOffset=e.lineDashOffset||0,i.stroke=l),d&&(i.fill=d),t.contentWidth),h=t.contentHeight;n.setBoundingRect(new X(Sr(i.x,c,i.textAlign),Mr(i.y,h,i.textBaseline),c,h))},Os.prototype._renderBackground=function(t,e,n,i,r,o){var a,s,l,u,h=t.backgroundColor,c=t.borderWidth,p=t.borderColor,d=h&&h.image,f=h&&!d,g=t.borderRadius,y=this,g=((f||t.lineHeight||c&&p)&&((a=this._getOrCreateChild(ks)).useStyle(a.createStyle()),a.style.fill=null,(l=a.shape).x=n,l.y=i,l.width=r,l.height=o,l.r=g,a.dirtyShape()),f?((u=a.style).fill=h||null,u.fillOpacity=N(t.fillOpacity,1)):d&&((s=this._getOrCreateChild(_s)).onload=function(){y.dirtyStyle()},(l=s.style).image=h.image,l.x=n,l.y=i,l.width=r,l.height=o),c&&p&&((u=a.style).lineWidth=c,u.stroke=p,u.strokeOpacity=N(t.strokeOpacity,1),u.lineDash=t.borderDash,u.lineDashOffset=t.borderDashOffset||0,a.strokeContainThreshold=0,a.hasFill())&&a.hasStroke()&&(u.strokeFirst=!0,u.lineWidth*=2),(a||s).style);g.shadowBlur=t.shadowBlur||0,g.shadowColor=t.shadowColor||"transparent",g.shadowOffsetX=t.shadowOffsetX||0,g.shadowOffsetY=t.shadowOffsetY||0,g.opacity=wt(t.opacity,e.opacity,1)},Os.makeFont=function(t){var e,n="";return(n=null!=(e=t).fontSize||e.fontFamily||e.fontWeight?[t.fontStyle,t.fontWeight,"string"!=typeof(e=t.fontSize)||-1===e.indexOf("px")&&-1===e.indexOf("rem")&&-1===e.indexOf("em")?isNaN(+e)?"12px":e+"px":e,t.fontFamily||"sans-serif"].join(" "):n)&&Ct(n)||t.textFont||t.font},Os);function Os(t){var e=Ds.call(this)||this;return e.type="text",e._children=[],e._defaultStyle=As,e.attr(t),e}var Rs={left:!0,right:1,center:1},Ns={top:1,bottom:1,middle:1},Es=["fontStyle","fontWeight","fontSize","fontFamily"];function zs(t,e){for(var n=0;n<Es.length;n++){var i=Es[n],r=e[i];null!=r&&(t[i]=r)}}function Bs(t){var e;t&&(t.font=Ps.makeFont(t),e=t.align,t.align=null==(e="middle"===e?"center":e)||Rs[e]?e:"left",e=t.verticalAlign,t.verticalAlign=null==(e="center"===e?"middle":e)||Ns[e]?e:"top",t.padding)&&(t.padding=Mt(t.padding))}function Fs(t,e){return null==t||e<=0||"transparent"===t||"none"===t?null:t.image||t.colorStops?"#000":t}function Vs(t){return null==t||"none"===t?null:t.image||t.colorStops?"#000":t}function Hs(t,e,n){return"right"===e?t-n[1]:"center"===e?t+n[3]/2-n[1]/2:t+n[3]}function Gs(t){t=t.text;return null!=t&&(t+=""),t}function Ws(t){return!!(t.backgroundColor||t.lineHeight||t.borderWidth&&t.borderColor)}var Us=Ao(),Xs=1,Ys={},qs=Ao(),Zs=Ao(),js=0,Ks=1,$s=2,Qs=["emphasis","blur","select"],Js=["normal","emphasis","blur","select"],tl="highlight",el="downplay",nl="select",il="unselect",rl="toggleSelect";function ol(t){return null!=t&&"none"!==t}function al(t,e,n){t.onHoverStateChange&&(t.hoverState||0)!==n&&t.onHoverStateChange(e),t.hoverState=n}function sl(t){al(t,"emphasis",$s)}function ll(t){t.hoverState===$s&&al(t,"normal",js)}function ul(t){al(t,"blur",Ks)}function hl(t){t.hoverState===Ks&&al(t,"normal",js)}function cl(t){t.selected=!0}function pl(t){t.selected=!1}function dl(t,e,n){e(t,n)}function fl(t,e,n){dl(t,e,n),t.isGroup&&t.traverse(function(t){dl(t,e,n)})}function gl(t,e){switch(e){case"emphasis":t.hoverState=$s;break;case"normal":t.hoverState=js;break;case"blur":t.hoverState=Ks;break;case"select":t.selected=!0}}function yl(t,e,n){var i=0<=k(t.currentStates,e),r=t.style.opacity,t=i?null:function(t,e,n,i){for(var r=t.style,o={},a=0;a<e.length;a++){var s=e[a],l=r[s];o[s]=null==l?i&&i[s]:l}for(a=0;a<t.animators.length;a++){var u=t.animators[a];u.__fromStateTransition&&u.__fromStateTransition.indexOf(n)<0&&"style"===u.targetName&&u.saveTo(o,e)}return o}(t,["opacity"],e,{opacity:1}),e=(n=n||{}).style||{};return null==e.opacity&&(n=P({},n),e=P({opacity:i?r:.1*t.opacity},e),n.style=e),n}function ml(t,e){var n,i,r,o,a,s=this.states[t];if(this.style){if("emphasis"===t)return n=this,i=s,e=(e=e)&&0<=k(e,"select"),a=!1,n instanceof j&&(r=qs(n),o=e&&r.selectFill||r.normalFill,e=e&&r.selectStroke||r.normalStroke,ol(o)||ol(e))&&("inherit"===(r=(i=i||{}).style||{}).fill?(a=!0,i=P({},i),(r=P({},r)).fill=o):!ol(r.fill)&&ol(o)?(a=!0,i=P({},i),(r=P({},r)).fill=Si(o)):!ol(r.stroke)&&ol(e)&&(a||(i=P({},i),r=P({},r)),r.stroke=Si(e)),i.style=r),i&&null==i.z2&&(a||(i=P({},i)),o=n.z2EmphasisLift,i.z2=n.z2+(null!=o?o:10)),i;if("blur"===t)return yl(this,t,s);if("select"===t)return e=this,(r=s)&&null==r.z2&&(r=P({},r),a=e.z2SelectLift,r.z2=e.z2+(null!=a?a:9)),r}return s}function vl(t){t.stateProxy=ml;var e=t.getTextContent(),t=t.getTextGuideLine();e&&(e.stateProxy=ml),t&&(t.stateProxy=ml)}function _l(t,e){kl(t,e)||t.__highByOuter||fl(t,sl)}function xl(t,e){kl(t,e)||t.__highByOuter||fl(t,ll)}function bl(t,e){t.__highByOuter|=1<<(e||0),fl(t,sl)}function wl(t,e){(t.__highByOuter&=~(1<<(e||0)))||fl(t,ll)}function Sl(t){fl(t,ul)}function Ml(t){fl(t,hl)}function Tl(t){fl(t,cl)}function Cl(t){fl(t,pl)}function kl(t,e){return t.__highDownSilentOnTouch&&e.zrByTouch}function Il(r){var e=r.getModel(),o=[],a=[];e.eachComponent(function(t,e){var n=Zs(e),t="series"===t,i=t?r.getViewOfSeriesModel(e):r.getViewOfComponentModel(e);t||a.push(i),n.isBlured&&(i.group.traverse(function(t){hl(t)}),t)&&o.push(e),n.isBlured=!1}),O(a,function(t){t&&t.toggleBlurSeries&&t.toggleBlurSeries(o,!1,e)})}function Dl(t,o,a,s){var l,u,h,n=s.getModel();function c(t,e){for(var n=0;n<e.length;n++){var i=t.getItemGraphicEl(e[n]);i&&Ml(i)}}a=a||"coordinateSystem",null!=t&&o&&"none"!==o&&(l=n.getSeriesByIndex(t),(u=l.coordinateSystem)&&u.master&&(u=u.master),h=[],n.eachSeries(function(t){var e=l===t,n=t.coordinateSystem,n=(n=n&&n.master?n.master:n)&&u?n===u:e;if(!("series"===a&&!e||"coordinateSystem"===a&&!n||"series"===o&&e)){if(s.getViewOfSeriesModel(t).group.traverse(function(t){t.__highByOuter&&e&&"self"===o||ul(t)}),st(o))c(t.getData(),o);else if(R(o))for(var i=I(o),r=0;r<i.length;r++)c(t.getData(i[r]),o[i[r]]);h.push(t),Zs(t).isBlured=!0}}),n.eachComponent(function(t,e){"series"!==t&&(t=s.getViewOfComponentModel(e))&&t.toggleBlurSeries&&t.toggleBlurSeries(h,!0,n)}))}function Al(t,e,n){var i;null!=t&&null!=e&&(t=n.getModel().getComponent(t,e))&&(Zs(t).isBlured=!0,i=n.getViewOfComponentModel(t))&&i.focusBlurEnabled&&i.group.traverse(function(t){ul(t)})}function Ll(t,e,n,i){var r={focusSelf:!1,dispatchers:null};if(null==t||"series"===t||null==e||null==n)return r;t=i.getModel().getComponent(t,e);if(!t)return r;e=i.getViewOfComponentModel(t);if(!e||!e.findHighDownDispatchers)return r;for(var o,a=e.findHighDownDispatchers(n),s=0;s<a.length;s++)if("self"===Us(a[s]).focus){o=!0;break}return{focusSelf:o,dispatchers:a}}function Pl(i){O(i.getAllData(),function(t){var e=t.data,n=t.type;e.eachItemGraphicEl(function(t,e){(i.isSelected(e,n)?Tl:Cl)(t)})})}function Ol(t,e,n){Bl(t,!0),fl(t,vl);t=Us(t),null!=e?(t.focus=e,t.blurScope=n):t.focus&&(t.focus=null)}function Rl(t,e,n,i){i?Bl(t,!1):Ol(t,e,n)}var Nl=["emphasis","blur","select"],El={itemStyle:"getItemStyle",lineStyle:"getLineStyle",areaStyle:"getAreaStyle"};function zl(t,e,n,i){n=n||"itemStyle";for(var r=0;r<Nl.length;r++){var o=Nl[r],a=e.getModel([o,n]);t.ensureState(o).style=i?i(a):a[El[n]]()}}function Bl(t,e){var e=!1===e,n=t;t.highDownSilentOnTouch&&(n.__highDownSilentOnTouch=t.highDownSilentOnTouch),e&&!n.__highDownDispatcher||(n.__highByOuter=n.__highByOuter||0,n.__highDownDispatcher=!e)}function Fl(t){return!(!t||!t.__highDownDispatcher)}function Vl(t){t=t.type;return t===nl||t===il||t===rl}function Hl(t){t=t.type;return t===tl||t===el}var Gl=Za.CMD,Wl=[[],[],[]],Ul=Math.sqrt,Xl=Math.atan2;var Yl=Math.sqrt,ql=Math.sin,Zl=Math.cos,jl=Math.PI;function Kl(t){return Math.sqrt(t[0]*t[0]+t[1]*t[1])}function $l(t,e){return(t[0]*e[0]+t[1]*e[1])/(Kl(t)*Kl(e))}function Ql(t,e){return(t[0]*e[1]<t[1]*e[0]?-1:1)*Math.acos($l(t,e))}function Jl(t,e,n,i,r,o,a,s,l,u,h){var l=l*(jl/180),c=Zl(l)*(t-n)/2+ql(l)*(e-i)/2,p=-1*ql(l)*(t-n)/2+Zl(l)*(e-i)/2,d=c*c/(a*a)+p*p/(s*s),d=(1<d&&(a*=Yl(d),s*=Yl(d)),(r===o?-1:1)*Yl((a*a*(s*s)-a*a*(p*p)-s*s*(c*c))/(a*a*(p*p)+s*s*(c*c)))||0),r=d*a*p/s,d=d*-s*c/a,t=(t+n)/2+Zl(l)*r-ql(l)*d,n=(e+i)/2+ql(l)*r+Zl(l)*d,e=Ql([1,0],[(c-r)/a,(p-d)/s]),i=[(c-r)/a,(p-d)/s],c=[(-1*c-r)/a,(-1*p-d)/s],r=Ql(i,c);$l(i,c)<=-1&&(r=jl),(r=1<=$l(i,c)?0:r)<0&&(p=Math.round(r/jl*1e6)/1e6,r=2*jl+p%2*jl),h.addData(u,t,n,a,s,e,r,l,o)}var tu=/([mlvhzcqtsa])([^mlvhzcqtsa]*)/gi,eu=/-?([0-9]*\.)?[0-9]+([eE]-?[0-9]+)?/g;u(ru,nu=j),ru.prototype.applyTransform=function(t){};var nu,iu=ru;function ru(){return null!==nu&&nu.apply(this,arguments)||this}function ou(t){return null!=t.setData}function au(t,e){var S=function(t){var e=new Za;if(t){var n,i=0,r=0,o=i,a=r,s=Za.CMD,l=t.match(tu);if(l){for(var u=0;u<l.length;u++){for(var h=l[u],c=h.charAt(0),p=void 0,d=h.match(eu)||[],f=d.length,g=0;g<f;g++)d[g]=parseFloat(d[g]);for(var y=0;y<f;){var m=void 0,v=void 0,_=void 0,x=void 0,b=void 0,w=void 0,S=void 0,M=i,T=r,C=void 0,k=void 0;switch(c){case"l":i+=d[y++],r+=d[y++],p=s.L,e.addData(p,i,r);break;case"L":i=d[y++],r=d[y++],p=s.L,e.addData(p,i,r);break;case"m":i+=d[y++],r+=d[y++],p=s.M,e.addData(p,i,r),o=i,a=r,c="l";break;case"M":i=d[y++],r=d[y++],p=s.M,e.addData(p,i,r),o=i,a=r,c="L";break;case"h":i+=d[y++],p=s.L,e.addData(p,i,r);break;case"H":i=d[y++],p=s.L,e.addData(p,i,r);break;case"v":r+=d[y++],p=s.L,e.addData(p,i,r);break;case"V":r=d[y++],p=s.L,e.addData(p,i,r);break;case"C":p=s.C,e.addData(p,d[y++],d[y++],d[y++],d[y++],d[y++],d[y++]),i=d[y-2],r=d[y-1];break;case"c":p=s.C,e.addData(p,d[y++]+i,d[y++]+r,d[y++]+i,d[y++]+r,d[y++]+i,d[y++]+r),i+=d[y-2],r+=d[y-1];break;case"S":m=i,v=r,C=e.len(),k=e.data,n===s.C&&(m+=i-k[C-4],v+=r-k[C-3]),p=s.C,M=d[y++],T=d[y++],i=d[y++],r=d[y++],e.addData(p,m,v,M,T,i,r);break;case"s":m=i,v=r,C=e.len(),k=e.data,n===s.C&&(m+=i-k[C-4],v+=r-k[C-3]),p=s.C,M=i+d[y++],T=r+d[y++],i+=d[y++],r+=d[y++],e.addData(p,m,v,M,T,i,r);break;case"Q":M=d[y++],T=d[y++],i=d[y++],r=d[y++],p=s.Q,e.addData(p,M,T,i,r);break;case"q":M=d[y++]+i,T=d[y++]+r,i+=d[y++],r+=d[y++],p=s.Q,e.addData(p,M,T,i,r);break;case"T":m=i,v=r,C=e.len(),k=e.data,n===s.Q&&(m+=i-k[C-4],v+=r-k[C-3]),i=d[y++],r=d[y++],p=s.Q,e.addData(p,m,v,i,r);break;case"t":m=i,v=r,C=e.len(),k=e.data,n===s.Q&&(m+=i-k[C-4],v+=r-k[C-3]),i+=d[y++],r+=d[y++],p=s.Q,e.addData(p,m,v,i,r);break;case"A":_=d[y++],x=d[y++],b=d[y++],w=d[y++],S=d[y++],Jl(M=i,T=r,i=d[y++],r=d[y++],w,S,_,x,b,p=s.A,e);break;case"a":_=d[y++],x=d[y++],b=d[y++],w=d[y++],S=d[y++],Jl(M=i,T=r,i+=d[y++],r+=d[y++],w,S,_,x,b,p=s.A,e)}}"z"!==c&&"Z"!==c||(p=s.Z,e.addData(p),i=o,r=a),n=p}e.toStatic()}}return e}(t),t=P({},e);return t.buildPath=function(t){var e;ou(t)?(t.setData(S.data),(e=t.getContext())&&t.rebuildPath(e,1)):S.rebuildPath(e=t,1)},t.applyTransform=function(t){var e=S,n=t;if(n){for(var i,r,o,a,s=e.data,l=e.len(),u=Gl.M,h=Gl.C,c=Gl.L,p=Gl.R,d=Gl.A,f=Gl.Q,g=0,y=0;g<l;){switch(i=s[g++],y=g,r=0,i){case u:case c:r=1;break;case h:r=3;break;case f:r=2;break;case d:var m=n[4],v=n[5],_=Ul(n[0]*n[0]+n[1]*n[1]),x=Ul(n[2]*n[2]+n[3]*n[3]),b=Xl(-n[1]/x,n[0]/_);s[g]*=_,s[g++]+=m,s[g]*=x,s[g++]+=v,s[g++]*=_,s[g++]*=x,s[g++]+=b,s[g++]+=b,y=g+=2;break;case p:a[0]=s[g++],a[1]=s[g++],ee(a,a,n),s[y++]=a[0],s[y++]=a[1],a[0]+=s[g++],a[1]+=s[g++],ee(a,a,n),s[y++]=a[0],s[y++]=a[1]}for(o=0;o<r;o++){var w=Wl[o];w[0]=s[g++],w[1]=s[g++],ee(w,w,n),s[y++]=w[0],s[y++]=w[1]}}e.increaseVersion()}this.dirtyShape()},t}var su,lu=function(){this.cx=0,this.cy=0,this.r=0},uu=(u(hu,su=j),hu.prototype.getDefaultShape=function(){return new lu},hu.prototype.buildPath=function(t,e){t.moveTo(e.cx+e.r,e.cy),t.arc(e.cx,e.cy,e.r,0,2*Math.PI)},hu);function hu(t){return su.call(this,t)||this}uu.prototype.type="circle";var cu,pu=function(){this.cx=0,this.cy=0,this.rx=0,this.ry=0},du=(u(fu,cu=j),fu.prototype.getDefaultShape=function(){return new pu},fu.prototype.buildPath=function(t,e){var n=e.cx,i=e.cy,r=e.rx,e=e.ry,o=.5522848*r,a=.5522848*e;t.moveTo(n-r,i),t.bezierCurveTo(n-r,i-a,n-o,i-e,n,i-e),t.bezierCurveTo(n+o,i-e,n+r,i-a,n+r,i),t.bezierCurveTo(n+r,i+a,n+o,i+e,n,i+e),t.bezierCurveTo(n-o,i+e,n-r,i+a,n-r,i),t.closePath()},fu);function fu(t){return cu.call(this,t)||this}du.prototype.type="ellipse";var gu=Math.PI,yu=2*gu,mu=Math.sin,vu=Math.cos,_u=Math.acos,xu=Math.atan2,bu=Math.abs,wu=Math.sqrt,Su=Math.max,Mu=Math.min,Tu=1e-4;function Cu(t,e,n,i,r,o,a){var s=t-n,l=e-i,a=(a?o:-o)/wu(s*s+l*l),l=a*l,a=-a*s,s=t+l,t=e+a,e=n+l,n=i+a,i=(s+e)/2,u=(t+n)/2,h=e-s,c=n-t,p=h*h+c*c,o=r-o,s=s*n-e*t,n=(c<0?-1:1)*wu(Su(0,o*o*p-s*s)),e=(s*c-h*n)/p,t=(-s*h-c*n)/p,d=(s*c+h*n)/p,s=(-s*h+c*n)/p,h=e-i,c=t-u,n=d-i,p=s-u;return n*n+p*p<h*h+c*c&&(e=d,t=s),{cx:e,cy:t,x0:-l,y0:-a,x1:e*(r/o-1),y1:t*(r/o-1)}}function ku(t,e){var n,i,r,o,a,s,l,u,h,c,p,d,f,g,y,m,v,_,x,b,w,S,M,T,C,k,I,D,A,L,P=Su(e.r,0),O=Su(e.r0||0,0),R=0<P;(R||0<O)&&(R||(P=O,O=0),P<O&&(R=P,P=O,O=R),R=e.startAngle,n=e.endAngle,isNaN(R)||isNaN(n)||(i=e.cx,r=e.cy,o=!!e.clockwise,m=bu(n-R),Tu<(a=yu<m&&m%yu)&&(m=a),Tu<P?yu-Tu<m?(t.moveTo(i+P*vu(R),r+P*mu(R)),t.arc(i,r,P,R,n,!o),Tu<O&&(t.moveTo(i+O*vu(n),r+O*mu(n)),t.arc(i,r,O,n,R,o))):(S=w=b=x=_=v=c=h=k=C=T=M=u=l=s=a=void 0,p=P*vu(R),d=P*mu(R),f=O*vu(n),g=O*mu(n),(y=Tu<m)&&((e=e.cornerRadius)&&(a=(e=function(t){if(F(t)){var e=t.length;if(!e)return t;e=1===e?[t[0],t[0],0,0]:2===e?[t[0],t[0],t[1],t[1]]:3===e?t.concat(t[2]):t}else e=[t,t,t,t];return e}(e))[0],s=e[1],l=e[2],u=e[3]),e=bu(P-O)/2,M=Mu(e,l),T=Mu(e,u),C=Mu(e,a),k=Mu(e,s),v=h=Su(M,T),_=c=Su(C,k),Tu<h||Tu<c)&&(x=P*vu(n),b=P*mu(n),w=O*vu(R),S=O*mu(R),m<gu)&&(e=function(t,e,n,i,r,o,a,s){var l=(s=s-o)*(n=n-t)-(a=a-r)*(i=i-e);if(!(l*l<Tu))return[t+(l=(a*(e-o)-s*(t-r))/l)*n,e+l*i]}(p,d,w,S,x,b,f,g))&&(M=p-e[0],T=d-e[1],C=x-e[0],k=b-e[1],m=1/mu(_u((M*C+T*k)/(wu(M*M+T*T)*wu(C*C+k*k)))/2),M=wu(e[0]*e[0]+e[1]*e[1]),v=Mu(h,(P-M)/(1+m)),_=Mu(c,(O-M)/(m-1))),y?Tu<v?(I=Mu(l,v),D=Mu(u,v),A=Cu(w,S,p,d,P,I,o),L=Cu(x,b,f,g,P,D,o),t.moveTo(i+A.cx+A.x0,r+A.cy+A.y0),v<h&&I===D?t.arc(i+A.cx,r+A.cy,v,xu(A.y0,A.x0),xu(L.y0,L.x0),!o):(0<I&&t.arc(i+A.cx,r+A.cy,I,xu(A.y0,A.x0),xu(A.y1,A.x1),!o),t.arc(i,r,P,xu(A.cy+A.y1,A.cx+A.x1),xu(L.cy+L.y1,L.cx+L.x1),!o),0<D&&t.arc(i+L.cx,r+L.cy,D,xu(L.y1,L.x1),xu(L.y0,L.x0),!o))):(t.moveTo(i+p,r+d),t.arc(i,r,P,R,n,!o)):t.moveTo(i+p,r+d),Tu<O&&y?Tu<_?(I=Mu(a,_),A=Cu(f,g,x,b,O,-(D=Mu(s,_)),o),L=Cu(p,d,w,S,O,-I,o),t.lineTo(i+A.cx+A.x0,r+A.cy+A.y0),_<c&&I===D?t.arc(i+A.cx,r+A.cy,_,xu(A.y0,A.x0),xu(L.y0,L.x0),!o):(0<D&&t.arc(i+A.cx,r+A.cy,D,xu(A.y0,A.x0),xu(A.y1,A.x1),!o),t.arc(i,r,O,xu(A.cy+A.y1,A.cx+A.x1),xu(L.cy+L.y1,L.cx+L.x1),o),0<I&&t.arc(i+L.cx,r+L.cy,I,xu(L.y1,L.x1),xu(L.y0,L.x0),!o))):(t.lineTo(i+f,r+g),t.arc(i,r,O,n,R,o)):t.lineTo(i+f,r+g)):t.moveTo(i,r),t.closePath()))}var Iu,Du=function(){this.cx=0,this.cy=0,this.r0=0,this.r=0,this.startAngle=0,this.endAngle=2*Math.PI,this.clockwise=!0,this.cornerRadius=0},Au=(u(Lu,Iu=j),Lu.prototype.getDefaultShape=function(){return new Du},Lu.prototype.buildPath=function(t,e){ku(t,e)},Lu.prototype.isZeroArea=function(){return this.shape.startAngle===this.shape.endAngle||this.shape.r===this.shape.r0},Lu);function Lu(t){return Iu.call(this,t)||this}Au.prototype.type="sector";var Pu,Ou=function(){this.cx=0,this.cy=0,this.r=0,this.r0=0},Ru=(u(Nu,Pu=j),Nu.prototype.getDefaultShape=function(){return new Ou},Nu.prototype.buildPath=function(t,e){var n=e.cx,i=e.cy,r=2*Math.PI;t.moveTo(n+e.r,i),t.arc(n,i,e.r,0,r,!1),t.moveTo(n+e.r0,i),t.arc(n,i,e.r0,0,r,!0)},Nu);function Nu(t){return Pu.call(this,t)||this}function Eu(t,e,n){var i=e.smooth,r=e.points;if(r&&2<=r.length){if(i)for(var o=function(t,e,n,i){var r,o,a=[],s=[],l=[],u=[];if(i){for(var h=[1/0,1/0],c=[-1/0,-1/0],p=0,d=t.length;p<d;p++)ne(h,h,t[p]),ie(c,c,t[p]);ne(h,h,i[0]),ie(c,c,i[1])}for(p=0,d=t.length;p<d;p++){var f=t[p];if(n)r=t[p?p-1:d-1],o=t[(p+1)%d];else{if(0===p||p===d-1){a.push(Wt(t[p]));continue}r=t[p-1],o=t[p+1]}Xt(s,o,r),Zt(s,s,e);var g=Kt(f,r),y=Kt(f,o),m=g+y,m=(0!==m&&(g/=m,y/=m),Zt(l,s,-g),Zt(u,s,y),Ut([],f,l)),g=Ut([],f,u);i&&(ie(m,m,h),ne(m,m,c),ie(g,g,h),ne(g,g,c)),a.push(m),a.push(g)}return n&&a.push(a.shift()),a}(r,i,n,e.smoothConstraint),a=(t.moveTo(r[0][0],r[0][1]),r.length),s=0;s<(n?a:a-1);s++){var l=o[2*s],u=o[2*s+1],h=r[(s+1)%a];t.bezierCurveTo(l[0],l[1],u[0],u[1],h[0],h[1])}else{t.moveTo(r[0][0],r[0][1]);for(var s=1,c=r.length;s<c;s++)t.lineTo(r[s][0],r[s][1])}n&&t.closePath()}}Ru.prototype.type="ring";var zu,Bu=function(){this.points=null,this.smooth=0,this.smoothConstraint=null},Fu=(u(Vu,zu=j),Vu.prototype.getDefaultShape=function(){return new Bu},Vu.prototype.buildPath=function(t,e){Eu(t,e,!0)},Vu);function Vu(t){return zu.call(this,t)||this}Fu.prototype.type="polygon";var Hu,Gu=function(){this.points=null,this.percent=1,this.smooth=0,this.smoothConstraint=null},Wu=(u(Uu,Hu=j),Uu.prototype.getDefaultStyle=function(){return{stroke:"#000",fill:null}},Uu.prototype.getDefaultShape=function(){return new Gu},Uu.prototype.buildPath=function(t,e){Eu(t,e,!1)},Uu);function Uu(t){return Hu.call(this,t)||this}Wu.prototype.type="polyline";var Xu,Yu={},qu=function(){this.x1=0,this.y1=0,this.x2=0,this.y2=0,this.percent=1},Zu=(u(ju,Xu=j),ju.prototype.getDefaultStyle=function(){return{stroke:"#000",fill:null}},ju.prototype.getDefaultShape=function(){return new qu},ju.prototype.buildPath=function(t,e){o=(this.subPixelOptimize?(n=(o=ws(Yu,e,this.style)).x1,i=o.y1,r=o.x2,o):(n=e.x1,i=e.y1,r=e.x2,e)).y2;var n,i,r,o,e=e.percent;0!==e&&(t.moveTo(n,i),e<1&&(r=n*(1-e)+r*e,o=i*(1-e)+o*e),t.lineTo(r,o))},ju.prototype.pointAt=function(t){var e=this.shape;return[e.x1*(1-t)+e.x2*t,e.y1*(1-t)+e.y2*t]},ju);function ju(t){return Xu.call(this,t)||this}Zu.prototype.type="line";var Ku=[],$u=function(){this.x1=0,this.y1=0,this.x2=0,this.y2=0,this.cpx1=0,this.cpy1=0,this.percent=1};function Qu(t,e,n){var i=t.cpx2,r=t.cpy2;return null!=i||null!=r?[(n?zn:En)(t.x1,t.cpx1,t.cpx2,t.x2,e),(n?zn:En)(t.y1,t.cpy1,t.cpy2,t.y2,e)]:[(n?Wn:Gn)(t.x1,t.cpx1,t.x2,e),(n?Wn:Gn)(t.y1,t.cpy1,t.y2,e)]}u(eh,Ju=j),eh.prototype.getDefaultStyle=function(){return{stroke:"#000",fill:null}},eh.prototype.getDefaultShape=function(){return new $u},eh.prototype.buildPath=function(t,e){var n=e.x1,i=e.y1,r=e.x2,o=e.y2,a=e.cpx1,s=e.cpy1,l=e.cpx2,u=e.cpy2,e=e.percent;0!==e&&(t.moveTo(n,i),null==l||null==u?(e<1&&(Xn(n,a,r,e,Ku),a=Ku[1],r=Ku[2],Xn(i,s,o,e,Ku),s=Ku[1],o=Ku[2]),t.quadraticCurveTo(a,s,r,o)):(e<1&&(Vn(n,a,l,r,e,Ku),a=Ku[1],l=Ku[2],r=Ku[3],Vn(i,s,u,o,e,Ku),s=Ku[1],u=Ku[2],o=Ku[3]),t.bezierCurveTo(a,s,l,u,r,o)))},eh.prototype.pointAt=function(t){return Qu(this.shape,t,!1)},eh.prototype.tangentAt=function(t){t=Qu(this.shape,t,!0);return jt(t,t)};var Ju,th=eh;function eh(t){return Ju.call(this,t)||this}th.prototype.type="bezier-curve";var nh,ih=function(){this.cx=0,this.cy=0,this.r=0,this.startAngle=0,this.endAngle=2*Math.PI,this.clockwise=!0},rh=(u(oh,nh=j),oh.prototype.getDefaultStyle=function(){return{stroke:"#000",fill:null}},oh.prototype.getDefaultShape=function(){return new ih},oh.prototype.buildPath=function(t,e){var n=e.cx,i=e.cy,r=Math.max(e.r,0),o=e.startAngle,a=e.endAngle,e=e.clockwise,s=Math.cos(o),l=Math.sin(o);t.moveTo(s*r+n,l*r+i),t.arc(n,i,r,o,a,!e)},oh);function oh(t){return nh.call(this,t)||this}rh.prototype.type="arc";u(lh,ah=j),lh.prototype._updatePathDirty=function(){for(var t=this.shape.paths,e=this.shapeChanged(),n=0;n<t.length;n++)e=e||t[n].shapeChanged();e&&this.dirtyShape()},lh.prototype.beforeBrush=function(){this._updatePathDirty();for(var t=this.shape.paths||[],e=this.getGlobalScale(),n=0;n<t.length;n++)t[n].path||t[n].createPathProxy(),t[n].path.setScale(e[0],e[1],t[n].segmentIgnoreThreshold)},lh.prototype.buildPath=function(t,e){for(var n=e.paths||[],i=0;i<n.length;i++)n[i].buildPath(t,n[i].shape,!0)},lh.prototype.afterBrush=function(){for(var t=this.shape.paths||[],e=0;e<t.length;e++)t[e].pathUpdated()},lh.prototype.getBoundingRect=function(){return this._updatePathDirty.call(this),j.prototype.getBoundingRect.call(this)};var ah,sh=lh;function lh(){var t=null!==ah&&ah.apply(this,arguments)||this;return t.type="compound",t}hh.prototype.addColorStop=function(t,e){this.colorStops.push({offset:t,color:e})};var uh=hh;function hh(t){this.colorStops=t||[]}u(dh,ch=uh);var ch,ph=dh;function dh(t,e,n,i,r,o){r=ch.call(this,r)||this;return r.x=null==t?0:t,r.y=null==e?0:e,r.x2=null==n?1:n,r.y2=null==i?0:i,r.type="linear",r.global=o||!1,r}u(gh,fh=uh);var fh,uh=gh;function gh(t,e,n,i,r){i=fh.call(this,i)||this;return i.x=null==t?.5:t,i.y=null==e?.5:e,i.r=null==n?.5:n,i.type="radial",i.global=r||!1,i}var yh=[0,0],mh=[0,0],vh=new M,_h=new M,xh=(bh.prototype.fromBoundingRect=function(t,e){var n=this._corners,i=this._axes,r=t.x,o=t.y,a=r+t.width,t=o+t.height;if(n[0].set(r,o),n[1].set(a,o),n[2].set(a,t),n[3].set(r,t),e)for(var s=0;s<4;s++)n[s].transform(e);M.sub(i[0],n[1],n[0]),M.sub(i[1],n[3],n[0]),i[0].normalize(),i[1].normalize();for(s=0;s<2;s++)this._origin[s]=i[s].dot(n[0])},bh.prototype.intersect=function(t,e){var n=!0,i=!e;return vh.set(1/0,1/0),_h.set(0,0),!this._intersectCheckOneSide(this,t,vh,_h,i,1)&&(n=!1,i)||!this._intersectCheckOneSide(t,this,vh,_h,i,-1)&&(n=!1,i)||i||M.copy(e,n?vh:_h),n},bh.prototype._intersectCheckOneSide=function(t,e,n,i,r,o){for(var a=!0,s=0;s<2;s++){var l=this._axes[s];if(this._getProjMinMaxOnAxis(s,t._corners,yh),this._getProjMinMaxOnAxis(s,e._corners,mh),yh[1]<mh[0]||mh[1]<yh[0]){if(a=!1,r)return a;var u=Math.abs(mh[0]-yh[1]),h=Math.abs(yh[0]-mh[1]);Math.min(u,h)>i.len()&&(u<h?M.scale(i,l,-u*o):M.scale(i,l,h*o))}else n&&(u=Math.abs(mh[0]-yh[1]),h=Math.abs(yh[0]-mh[1]),Math.min(u,h)<n.len())&&(u<h?M.scale(n,l,u*o):M.scale(n,l,-h*o))}return a},bh.prototype._getProjMinMaxOnAxis=function(t,e,n){for(var i=this._axes[t],r=this._origin,o=e[0].dot(i)+r[t],a=o,s=o,l=1;l<e.length;l++)var u=e[l].dot(i)+r[t],a=Math.min(u,a),s=Math.max(u,s);n[0]=a,n[1]=s},bh);function bh(t,e){this._corners=[],this._axes=[],this._origin=[0,0];for(var n=0;n<4;n++)this._corners[n]=new M;for(n=0;n<2;n++)this._axes[n]=new M;t&&this.fromBoundingRect(t,e)}var wh,Sh=[],n=(u(Mh,wh=n),Mh.prototype.traverse=function(t,e){t.call(e,this)},Mh.prototype.useStyle=function(){this.style={}},Mh.prototype.getCursor=function(){return this._cursor},Mh.prototype.innerAfterBrush=function(){this._cursor=this._displayables.length},Mh.prototype.clearDisplaybles=function(){this._displayables=[],this._temporaryDisplayables=[],this._cursor=0,this.markRedraw(),this.notClear=!1},Mh.prototype.clearTemporalDisplayables=function(){this._temporaryDisplayables=[]},Mh.prototype.addDisplayable=function(t,e){(e?this._temporaryDisplayables:this._displayables).push(t),this.markRedraw()},Mh.prototype.addDisplayables=function(t,e){e=e||!1;for(var n=0;n<t.length;n++)this.addDisplayable(t[n],e)},Mh.prototype.getDisplayables=function(){return this._displayables},Mh.prototype.getTemporalDisplayables=function(){return this._temporaryDisplayables},Mh.prototype.eachPendingDisplayable=function(t){for(var e=this._cursor;e<this._displayables.length;e++)t&&t(this._displayables[e]);for(e=0;e<this._temporaryDisplayables.length;e++)t&&t(this._temporaryDisplayables[e])},Mh.prototype.update=function(){this.updateTransform();for(var t=this._cursor;t<this._displayables.length;t++)(e=this._displayables[t]).parent=this,e.update(),e.parent=null;for(var e,t=0;t<this._temporaryDisplayables.length;t++)(e=this._temporaryDisplayables[t]).parent=this,e.update(),e.parent=null},Mh.prototype.getBoundingRect=function(){if(!this._rect){for(var t=new X(1/0,1/0,-1/0,-1/0),e=0;e<this._displayables.length;e++){var n=this._displayables[e],i=n.getBoundingRect().clone();n.needLocalTransform()&&i.applyTransform(n.getLocalTransform(Sh)),t.union(i)}this._rect=t}return this._rect},Mh.prototype.contain=function(t,e){var n=this.transformCoordToLocal(t,e);if(this.getBoundingRect().contain(n[0],n[1]))for(var i=0;i<this._displayables.length;i++)if(this._displayables[i].contain(t,e))return!0;return!1},Mh);function Mh(){var t=null!==wh&&wh.apply(this,arguments)||this;return t.notClear=!0,t.incremental=!0,t._displayables=[],t._temporaryDisplayables=[],t._cursor=0,t}var Th=Ao();function Ch(t,e,n,i,r,o,a){var s,l,u,h,c,p,d=!1,f=(D(r)?(a=o,o=r,r=null):R(r)&&(o=r.cb,a=r.during,d=r.isFrom,l=r.removeOpt,r=r.dataIndex),"leave"===t),g=(f||e.stopAnimation("leave"),p=t,s=r,l=f?l||{}:null,i=(g=i)&&i.getAnimationDelayParams?i.getAnimationDelayParams(e,r):null,g&&g.ecModel&&(u=(u=g.ecModel.getUpdatePayload())&&u.animation),p="update"===p,g&&g.isAnimationEnabled()?(c=h=r=void 0,c=l?(r=N(l.duration,200),h=N(l.easing,"cubicOut"),0):(r=g.getShallow(p?"animationDurationUpdate":"animationDuration"),h=g.getShallow(p?"animationEasingUpdate":"animationEasing"),g.getShallow(p?"animationDelayUpdate":"animationDelay")),D(c=u&&(null!=u.duration&&(r=u.duration),null!=u.easing&&(h=u.easing),null!=u.delay)?u.delay:c)&&(c=c(s,i)),{duration:(r=D(r)?r(s):r)||0,delay:c,easing:h}):null);g&&0<g.duration?(p={duration:g.duration,delay:g.delay||0,easing:g.easing,done:o,force:!!o||!!a,setToFinal:!f,scope:t,during:a},d?e.animateFrom(n,p):e.animateTo(n,p)):(e.stopAnimation(),d||e.attr(n),a&&a(1),o&&o())}function kh(t,e,n,i,r,o){Ch("update",t,e,n,i,r,o)}function Ih(t,e,n,i,r,o){Ch("enter",t,e,n,i,r,o)}function Dh(t){if(!t.__zr)return 1;for(var e=0;e<t.animators.length;e++)if("leave"===t.animators[e].scope)return 1}function Ah(t,e,n,i,r,o){Dh(t)||Ch("leave",t,e,n,i,r,o)}var Lh=Math.max,Ph=Math.min,Oh={};var Rh=function(t,e){var n,i=au(t,e);function r(t){t=n.call(this,t)||this;return t.applyTransform=i.applyTransform,t.buildPath=i.buildPath,t}return u(r,n=iu),r};function Nh(t,e){Oh[t]=e}function Eh(t,e,n,i){t=new iu(au(t,e));return n&&("center"===i&&(n=Bh(n,t.getBoundingRect())),Vh(t,n)),t}function zh(t,e,n){var i=new _s({style:{image:t,x:e.x,y:e.y,width:e.width,height:e.height},onload:function(t){"center"===n&&(t={width:t.width,height:t.height},i.setStyle(Bh(e,t)))}});return i}function Bh(t,e){var e=e.width/e.height,n=t.height*e,e=n<=t.width?t.height:(n=t.width)/e;return{x:t.x+t.width/2-n/2,y:t.y+t.height/2-e/2,width:n,height:e}}function Fh(t,e){for(var n=[],i=t.length,r=0;r<i;r++){var o=t[r];n.push(o.getUpdatedPathProxy(!0))}return(e=new j(e)).createPathProxy(),e.buildPath=function(t){var e;ou(t)&&(t.appendPath(n),e=t.getContext())&&t.rebuildPath(e,1)},e}function Vh(t,e){t.applyTransform&&(e=t.getBoundingRect().calculateTransform(e),t.applyTransform(e))}function Hh(t,e){ws(t,t,{lineWidth:e})}function Gh(t){return!t.isGroup}function Wh(t,e,n){var e=P({rectHover:!0},e),i=e.style={strokeNoScale:!0};if(n=n||{x:-1,y:-1,width:2,height:2},t)return 0===t.indexOf("image://")?(i.image=t.slice(8),z(i,n),new _s(e)):Eh(t.replace("path://",""),e,n,"center")}function Uh(t,e){var n;(n=t.isGroup?e(t):n)||t.traverse(e)}function Xh(t,e){if(t)if(F(t))for(var n=0;n<t.length;n++)Uh(t[n],e);else Uh(t,e)}Nh("circle",uu),Nh("ellipse",du),Nh("sector",Au),Nh("ring",Ru),Nh("polygon",Fu),Nh("polyline",Wu),Nh("rect",ks),Nh("line",Zu),Nh("bezierCurve",th),Nh("arc",rh);var Yh={};function qh(t,e){for(var n=0;n<Qs.length;n++){var i=Qs[n],r=e[i],i=t.ensureState(i);i.style=i.style||{},i.style.text=r}var o=t.currentStates.slice();t.clearStates(!0),t.setStyle({text:e.normal}),t.useStates(o,!0)}function Zh(t,e,n){for(var i,r=t.labelFetcher,o=t.labelDataIndex,a=t.labelDimIndex,s=e.normal,l={normal:i=null==(i=r?r.getFormattedLabel(o,"normal",null,a,s&&s.get("formatter"),null!=n?{interpolatedValue:n}:null):i)?D(t.defaultText)?t.defaultText(o,t,n):t.defaultText:i},u=0;u<Qs.length;u++){var h=Qs[u],c=e[h];l[h]=N(r?r.getFormattedLabel(o,h,null,a,c&&c.get("formatter")):null,i)}return l}function jh(t,e,n,i){n=n||Yh;for(var r=t instanceof Ps,o=!1,a=0;a<Js.length;a++)if((p=e[Js[a]])&&p.getShallow("show")){o=!0;break}var s=r?t:t.getTextContent();if(o){r||(s||(s=new Ps,t.setTextContent(s)),t.stateProxy&&(s.stateProxy=t.stateProxy));var l=Zh(n,e),u=e.normal,h=!!u.getShallow("show"),c=$h(u,i&&i.normal,n,!1,!r);c.text=l.normal,r||t.setTextConfig(Qh(u,n,!1));for(a=0;a<Qs.length;a++){var p,d,f,g=Qs[a];(p=e[g])&&(d=s.ensureState(g),(f=!!N(p.getShallow("show"),h))!=h&&(d.ignore=!f),d.style=$h(p,i&&i[g],n,!0,!r),d.style.text=l[g],r||(t.ensureState(g).textConfig=Qh(p,n,!0)))}s.silent=!!u.getShallow("silent"),null!=s.style.x&&(c.x=s.style.x),null!=s.style.y&&(c.y=s.style.y),s.ignore=!h,s.useStyle(c),s.dirty(),n.enableTextSetter&&(ic(s).setLabelText=function(t){t=Zh(n,e,t);qh(s,t)})}else s&&(s.ignore=!0);t.dirty()}function Kh(t,e){for(var n={normal:t.getModel(e=e||"label")},i=0;i<Qs.length;i++){var r=Qs[i];n[r]=t.getModel([r,e])}return n}function $h(t,e,n,i,r){var o,a={},s=a,l=t,u=n,h=i,c=r;u=u||Yh;var p,t=l.ecModel,d=t&&t.option.textStyle,f=function(t){var e;for(;t&&t!==t.ecModel;){var n=(t.option||Yh).rich;if(n){e=e||{};for(var i=I(n),r=0;r<i.length;r++){var o=i[r];e[o]=1}}t=t.parentModel}return e}(l);if(f)for(var g in p={},f)f.hasOwnProperty(g)&&(o=l.getModel(["rich",g]),nc(p[g]={},o,d,u,h,c,!1,!0));return p&&(s.rich=p),(t=l.get("overflow"))&&(s.overflow=t),null!=(t=l.get("minMargin"))&&(s.margin=t),nc(s,l,d,u,h,c,!0,!1),e&&P(a,e),a}function Qh(t,e,n){e=e||{};var i={},r=t.getShallow("rotate"),o=N(t.getShallow("distance"),n?null:5),a=t.getShallow("offset"),n=t.getShallow("position")||(n?null:"inside");return null!=(n="outside"===n?e.defaultOutsidePosition||"top":n)&&(i.position=n),null!=a&&(i.offset=a),null!=r&&(r*=Math.PI/180,i.rotation=r),null!=o&&(i.distance=o),i.outsideFill="inherit"===t.get("color")?e.inheritColor||null:"auto",i}var Jh=["fontStyle","fontWeight","fontSize","fontFamily","textShadowColor","textShadowBlur","textShadowOffsetX","textShadowOffsetY"],tc=["align","lineHeight","width","height","tag","verticalAlign","ellipsis"],ec=["padding","borderWidth","borderRadius","borderDashOffset","backgroundColor","borderColor","shadowColor","shadowBlur","shadowOffsetX","shadowOffsetY"];function nc(t,e,n,i,r,o,a,s){n=!r&&n||Yh;var l=i&&i.inheritColor,u=e.getShallow("color"),h=e.getShallow("textBorderColor"),c=N(e.getShallow("opacity"),n.opacity),u=("inherit"!==u&&"auto"!==u||(u=l||null),"inherit"!==h&&"auto"!==h||(h=l||null),o||(u=u||n.color,h=h||n.textBorderColor),null!=u&&(t.fill=u),null!=h&&(t.stroke=h),N(e.getShallow("textBorderWidth"),n.textBorderWidth)),h=(null!=u&&(t.lineWidth=u),N(e.getShallow("textBorderType"),n.textBorderType)),u=(null!=h&&(t.lineDash=h),N(e.getShallow("textBorderDashOffset"),n.textBorderDashOffset));null!=u&&(t.lineDashOffset=u),null!=(c=r||null!=c||s?c:i&&i.defaultOpacity)&&(t.opacity=c),r||o||null==t.fill&&i.inheritColor&&(t.fill=i.inheritColor);for(var p=0;p<Jh.length;p++){var d=Jh[p];null!=(f=N(e.getShallow(d),n[d]))&&(t[d]=f)}for(var p=0;p<tc.length;p++){d=tc[p];null!=(f=e.getShallow(d))&&(t[d]=f)}if(null==t.verticalAlign&&null!=(h=e.getShallow("baseline"))&&(t.verticalAlign=h),!a||!i.disableBox){for(p=0;p<ec.length;p++){var f,d=ec[p];null!=(f=e.getShallow(d))&&(t[d]=f)}u=e.getShallow("borderType");null!=u&&(t.borderDash=u),"auto"!==t.backgroundColor&&"inherit"!==t.backgroundColor||!l||(t.backgroundColor=l),"auto"!==t.borderColor&&"inherit"!==t.borderColor||!l||(t.borderColor=l)}}var ic=Ao();function rc(n,i,r,t,o){var a,s,l,u=ic(n);u.valueAnimation&&u.prevValue!==u.value&&(a=u.defaultInterpolatedText,s=N(u.interpolatedValue,u.prevValue),l=u.value,n.percent=0,(null==u.prevValue?Ih:kh)(n,{percent:1},t,i,null,function(t){var e=zo(r,u.precision,s,l,t),t=(u.interpolatedValue=1===t?null:e,Zh({labelDataIndex:i,labelFetcher:o,defaultText:a?a(e):e+""},u.statesModels,e));qh(n,t)}))}var oc=["textStyle","color"],ac=["fontStyle","fontWeight","fontSize","fontFamily","padding","lineHeight","rich","width","height","overflow"],sc=new Ps,lc=(uc.prototype.getTextColor=function(t){var e=this.ecModel;return this.getShallow("color")||(!t&&e?e.get(oc):null)},uc.prototype.getFont=function(){return t={fontStyle:this.getShallow("fontStyle"),fontWeight:this.getShallow("fontWeight"),fontSize:this.getShallow("fontSize"),fontFamily:this.getShallow("fontFamily")},e=(e=this.ecModel)&&e.getModel("textStyle"),Ct([t.fontStyle||e&&e.getShallow("fontStyle")||"",t.fontWeight||e&&e.getShallow("fontWeight")||"",(t.fontSize||e&&e.getShallow("fontSize")||12)+"px",t.fontFamily||e&&e.getShallow("fontFamily")||"sans-serif"].join(" "));var t,e},uc.prototype.getTextRect=function(t){for(var e={text:t,verticalAlign:this.getShallow("verticalAlign")||this.getShallow("baseline")},n=0;n<ac.length;n++)e[ac[n]]=this.getShallow(ac[n]);return sc.useStyle(e),sc.update(),sc.getBoundingRect()},uc);function uc(){}var hc=[["lineWidth","width"],["stroke","color"],["opacity"],["shadowBlur"],["shadowOffsetX"],["shadowOffsetY"],["shadowColor"],["lineDash","type"],["lineDashOffset","dashOffset"],["lineCap","cap"],["lineJoin","join"],["miterLimit"]],cc=Zo(hc),pc=(dc.prototype.getLineStyle=function(t){return cc(this,t)},dc);function dc(){}var fc=[["fill","color"],["stroke","borderColor"],["lineWidth","borderWidth"],["opacity"],["shadowBlur"],["shadowOffsetX"],["shadowOffsetY"],["shadowColor"],["lineDash","borderType"],["lineDashOffset","borderDashOffset"],["lineCap","borderCap"],["lineJoin","borderJoin"],["miterLimit","borderMiterLimit"]],gc=Zo(fc),yc=(mc.prototype.getItemStyle=function(t,e){return gc(this,t,e)},mc);function mc(){}xc.prototype.init=function(t,e,n){},xc.prototype.mergeOption=function(t,e){d(this.option,t,!0)},xc.prototype.get=function(t,e){return null==t?this.option:this._doGet(this.parsePath(t),!e&&this.parentModel)},xc.prototype.getShallow=function(t,e){var n=this.option,n=null==n?n:n[t];return null!=n||e||(e=this.parentModel)&&(n=e.getShallow(t)),n},xc.prototype.getModel=function(t,e){var n=null!=t,t=n?this.parsePath(t):null;return new xc(n?this._doGet(t):this.option,e=e||this.parentModel&&this.parentModel.getModel(this.resolveParentPath(t)),this.ecModel)},xc.prototype.isEmpty=function(){return null==this.option},xc.prototype.restoreData=function(){},xc.prototype.clone=function(){return new this.constructor(y(this.option))},xc.prototype.parsePath=function(t){return"string"==typeof t?t.split("."):t},xc.prototype.resolveParentPath=function(t){return t},xc.prototype.isAnimationEnabled=function(){if(!p.node&&this.option)return null!=this.option.animation?!!this.option.animation:this.parentModel?this.parentModel.isAnimationEnabled():void 0},xc.prototype._doGet=function(t,e){var n=this.option;if(t){for(var i=0;i<t.length&&(!t[i]||null!=(n=n&&"object"==typeof n?n[t[i]]:null));i++);null==n&&e&&(n=e._doGet(this.resolveParentPath(t),e.parentModel))}return n};var vc,_c=xc;function xc(t,e,n){this.parentModel=e,this.ecModel=n,this.option=t}Go(_c),Nc=_c,vc=["__\0is_clz",Uo++].join("_"),Nc.prototype[vc]=!0,Nc.isInstance=function(t){return!(!t||!t[vc])},at(_c,pc),at(_c,yc),at(_c,Ko),at(_c,lc);var bc=Math.round(10*Math.random());function wc(t){return[t||"",bc++].join("_")}var Sc="ZH",Mc="EN",Tc=Mc,Cc={},kc={},Ic=p.domSupported&&-1<(document.documentElement.lang||navigator.language||navigator.browserLanguage||Tc).toUpperCase().indexOf(Sc)?Sc:Tc;function Dc(t,e){t=t.toUpperCase(),kc[t]=new _c(e),Cc[t]=e}Dc(Mc,{time:{month:["January","February","March","April","May","June","July","August","September","October","November","December"],monthAbbr:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],dayOfWeek:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],dayOfWeekAbbr:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"]},legend:{selector:{all:"All",inverse:"Inv"}},toolbox:{brush:{title:{rect:"Box Select",polygon:"Lasso Select",lineX:"Horizontally Select",lineY:"Vertically Select",keep:"Keep Selections",clear:"Clear Selections"}},dataView:{title:"Data View",lang:["Data View","Close","Refresh"]},dataZoom:{title:{zoom:"Zoom",back:"Zoom Reset"}},magicType:{title:{line:"Switch to Line Chart",bar:"Switch to Bar Chart",stack:"Stack",tiled:"Tile"}},restore:{title:"Restore"},saveAsImage:{title:"Save as Image",lang:["Right Click to Save Image"]}},series:{typeNames:{pie:"Pie chart",bar:"Bar chart",line:"Line chart",scatter:"Scatter plot",effectScatter:"Ripple scatter plot",radar:"Radar chart",tree:"Tree",treemap:"Treemap",boxplot:"Boxplot",candlestick:"Candlestick",k:"K line chart",heatmap:"Heat map",map:"Map",parallel:"Parallel coordinate map",lines:"Line graph",graph:"Relationship graph",sankey:"Sankey diagram",funnel:"Funnel chart",gauge:"Gauge",pictorialBar:"Pictorial bar",themeRiver:"Theme River Map",sunburst:"Sunburst",custom:"Custom chart",chart:"Chart"}},aria:{general:{withTitle:'This is a chart about "{title}"',withoutTitle:"This is a chart"},series:{single:{prefix:"",withName:" with type {seriesType} named {seriesName}.",withoutName:" with type {seriesType}."},multiple:{prefix:". It consists of {seriesCount} series count.",withName:" The {seriesId} series is a {seriesType} representing {seriesName}.",withoutName:" The {seriesId} series is a {seriesType}.",separator:{middle:"",end:""}}},data:{allData:"The data is as follows: ",partialData:"The first {displayCnt} items are: ",withName:"the data for {name} is {value}",withoutName:"{value}",separator:{middle:", ",end:". "}}}}),Dc(Sc,{time:{month:["一月","二月","三月","四月","五月","六月","七月","八月","九月","十月","十一月","十二月"],monthAbbr:["1月","2月","3月","4月","5月","6月","7月","8月","9月","10月","11月","12月"],dayOfWeek:["星期日","星期一","星期二","星期三","星期四","星期五","星期六"],dayOfWeekAbbr:["日","一","二","三","四","五","六"]},legend:{selector:{all:"全选",inverse:"反选"}},toolbox:{brush:{title:{rect:"矩形选择",polygon:"圈选",lineX:"横向选择",lineY:"纵向选择",keep:"保持选择",clear:"清除选择"}},dataView:{title:"数据视图",lang:["数据视图","关闭","刷新"]},dataZoom:{title:{zoom:"区域缩放",back:"区域缩放还原"}},magicType:{title:{line:"切换为折线图",bar:"切换为柱状图",stack:"切换为堆叠",tiled:"切换为平铺"}},restore:{title:"还原"},saveAsImage:{title:"保存为图片",lang:["右键另存为图片"]}},series:{typeNames:{pie:"饼图",bar:"柱状图",line:"折线图",scatter:"散点图",effectScatter:"涟漪散点图",radar:"雷达图",tree:"树图",treemap:"矩形树图",boxplot:"箱型图",candlestick:"K线图",k:"K线图",heatmap:"热力图",map:"地图",parallel:"平行坐标图",lines:"线图",graph:"关系图",sankey:"桑基图",funnel:"漏斗图",gauge:"仪表盘图",pictorialBar:"象形柱图",themeRiver:"主题河流图",sunburst:"旭日图",custom:"自定义图表",chart:"图表"}},aria:{general:{withTitle:"这是一个关于“{title}”的图表。",withoutTitle:"这是一个图表，"},series:{single:{prefix:"",withName:"图表类型是{seriesType}，表示{seriesName}。",withoutName:"图表类型是{seriesType}。"},multiple:{prefix:"它由{seriesCount}个图表系列组成。",withName:"第{seriesId}个系列是一个表示{seriesName}的{seriesType}，",withoutName:"第{seriesId}个系列是一个{seriesType}，",separator:{middle:"；",end:"。"}}},data:{allData:"其数据是——",partialData:"其中，前{displayCnt}项是——",withName:"{name}的数据是{value}",withoutName:"{value}",separator:{middle:"，",end:""}}}});var Ac=1e3,Lc=60*Ac,Pc=60*Lc,Oc=24*Pc,Uo=365*Oc,Rc={year:"{yyyy}",month:"{MMM}",day:"{d}",hour:"{HH}:{mm}",minute:"{HH}:{mm}",second:"{HH}:{mm}:{ss}",millisecond:"{HH}:{mm}:{ss} {SSS}",none:"{yyyy}-{MM}-{dd} {HH}:{mm}:{ss} {SSS}"},Nc="{yyyy}-{MM}-{dd}",Ec={year:"{yyyy}",month:"{yyyy}-{MM}",day:Nc,hour:Nc+" "+Rc.hour,minute:Nc+" "+Rc.minute,second:Nc+" "+Rc.second,millisecond:Rc.none},zc=["year","month","day","hour","minute","second","millisecond"],Bc=["year","half-year","quarter","month","week","half-week","day","half-day","quarter-day","hour","minute","second","millisecond"];function Fc(t,e){return"0000".substr(0,e-(t+="").length)+t}function Vc(t){switch(t){case"half-year":case"quarter":return"month";case"week":case"half-week":return"day";case"half-day":case"quarter-day":return"hour";default:return t}}function Hc(t,e,n,i){var t=lo(t),r=t[Uc(n)](),o=t[Xc(n)]()+1,a=Math.floor((o-1)/3)+1,s=t[Yc(n)](),l=t["get"+(n?"UTC":"")+"Day"](),u=t[qc(n)](),h=(u-1)%12+1,c=t[Zc(n)](),p=t[jc(n)](),t=t[Kc(n)](),n=12<=u?"pm":"am",d=n.toUpperCase(),i=(i instanceof _c?i:kc[i||Ic]||kc[Tc]).getModel("time"),f=i.get("month"),g=i.get("monthAbbr"),y=i.get("dayOfWeek"),i=i.get("dayOfWeekAbbr");return(e||"").replace(/{a}/g,n+"").replace(/{A}/g,d+"").replace(/{yyyy}/g,r+"").replace(/{yy}/g,Fc(r%100+"",2)).replace(/{Q}/g,a+"").replace(/{MMMM}/g,f[o-1]).replace(/{MMM}/g,g[o-1]).replace(/{MM}/g,Fc(o,2)).replace(/{M}/g,o+"").replace(/{dd}/g,Fc(s,2)).replace(/{d}/g,s+"").replace(/{eeee}/g,y[l]).replace(/{ee}/g,i[l]).replace(/{e}/g,l+"").replace(/{HH}/g,Fc(u,2)).replace(/{H}/g,u+"").replace(/{hh}/g,Fc(h+"",2)).replace(/{h}/g,h+"").replace(/{mm}/g,Fc(c,2)).replace(/{m}/g,c+"").replace(/{ss}/g,Fc(p,2)).replace(/{s}/g,p+"").replace(/{SSS}/g,Fc(t,3)).replace(/{S}/g,t+"")}function Gc(t,e){var t=lo(t),n=t[Xc(e)]()+1,i=t[Yc(e)](),r=t[qc(e)](),o=t[Zc(e)](),a=t[jc(e)](),t=0===t[Kc(e)](),e=t&&0===a,a=e&&0===o,o=a&&0===r,r=o&&1===i;return r&&1===n?"year":r?"month":o?"day":a?"hour":e?"minute":t?"second":"millisecond"}function Wc(t,e,n){var i=dt(t)?lo(t):t;switch(e=e||Gc(t,n)){case"year":return i[Uc(n)]();case"half-year":return 6<=i[Xc(n)]()?1:0;case"quarter":return Math.floor((i[Xc(n)]()+1)/4);case"month":return i[Xc(n)]();case"day":return i[Yc(n)]();case"half-day":return i[qc(n)]()/24;case"hour":return i[qc(n)]();case"minute":return i[Zc(n)]();case"second":return i[jc(n)]();case"millisecond":return i[Kc(n)]()}}function Uc(t){return t?"getUTCFullYear":"getFullYear"}function Xc(t){return t?"getUTCMonth":"getMonth"}function Yc(t){return t?"getUTCDate":"getDate"}function qc(t){return t?"getUTCHours":"getHours"}function Zc(t){return t?"getUTCMinutes":"getMinutes"}function jc(t){return t?"getUTCSeconds":"getSeconds"}function Kc(t){return t?"getUTCMilliseconds":"getMilliseconds"}function $c(t){return t?"setUTCMonth":"setMonth"}function Qc(t){return t?"setUTCDate":"setDate"}function Jc(t){return t?"setUTCHours":"setHours"}function tp(t){return t?"setUTCMinutes":"setMinutes"}function ep(t){return t?"setUTCSeconds":"setSeconds"}function np(t){return t?"setUTCMilliseconds":"setMilliseconds"}function ip(t){var e;return fo(t)?(e=(t+"").split("."))[0].replace(/(\d{1,3})(?=(?:\d{3})+(?!\d))/g,"$1,")+(1<e.length?"."+e[1]:""):V(t)?t:"-"}function rp(t,e){return"{"+t+(null==e?"":e)+"}"}var op=Mt,ap=["a","b","c","d","e","f","g"];function sp(t,e,n){var i=(e=F(e)?e:[e]).length;if(!i)return"";for(var r=e[0].$vars||[],o=0;o<r.length;o++){var a=ap[o];t=t.replace(rp(a),rp(a,0))}for(var s=0;s<i;s++)for(var l=0;l<r.length;l++){var u=e[s][r[l]];t=t.replace(rp(ap[l],s),n?ve(u):u)}return t}function lp(t,e){return e=e||"transparent",V(t)?t:R(t)&&t.colorStops&&(t.colorStops[0]||{}).color||e}var up=O,hp=["left","right","top","bottom","width","height"],cp=[["width","left","right"],["height","top","bottom"]];function pp(a,s,l,u,h){var c=0,p=0,d=(null==u&&(u=1/0),null==h&&(h=1/0),0);s.eachChild(function(t,e){var n,i,r,o=t.getBoundingRect(),e=s.childAt(e+1),e=e&&e.getBoundingRect();d="horizontal"===a?(i=o.width+(e?-e.x+o.x:0),u<(n=c+i)||t.newline?(c=0,n=i,p+=d+l,o.height):Math.max(d,o.height)):(i=o.height+(e?-e.y+o.y:0),h<(r=p+i)||t.newline?(c+=d+l,p=0,r=i,o.width):Math.max(d,o.width)),t.newline||(t.x=c,t.y=p,t.markRedraw(),"horizontal"===a?c=n+l:p=r+l)})}function dp(t,e,n){n=op(n||0);var i=e.width,r=e.height,o=Jr(t.left,i),a=Jr(t.top,r),e=Jr(t.right,i),s=Jr(t.bottom,r),l=Jr(t.width,i),u=Jr(t.height,r),h=n[2]+n[0],c=n[1]+n[3],p=t.aspect;switch(isNaN(l)&&(l=i-e-c-o),isNaN(u)&&(u=r-s-h-a),null!=p&&(isNaN(l)&&isNaN(u)&&(i/r<p?l=.8*i:u=.8*r),isNaN(l)&&(l=p*u),isNaN(u))&&(u=l/p),isNaN(o)&&(o=i-e-l-c),isNaN(a)&&(a=r-s-u-h),t.left||t.right){case"center":o=i/2-l/2-n[3];break;case"right":o=i-l-c}switch(t.top||t.bottom){case"middle":case"center":a=r/2-u/2-n[0];break;case"bottom":a=r-u-h}o=o||0,a=a||0,isNaN(l)&&(l=i-c-o-(e||0)),isNaN(u)&&(u=r-h-a-(s||0));p=new X(o+n[3],a+n[0],l,u);return p.margin=n,p}function fp(t){t=t.layoutMode||t.constructor.layoutMode;return R(t)?t:t?{type:t}:null}function gp(l,u,t){var h=t&&t.ignoreSize,t=(F(h)||(h=[h,h]),n(cp[0],0)),e=n(cp[1],1);function n(t,e){var n={},i=0,r={},o=0;if(up(t,function(t){r[t]=l[t]}),up(t,function(t){c(u,t)&&(n[t]=r[t]=u[t]),p(n,t)&&i++,p(r,t)&&o++}),h[e])p(u,t[1])?r[t[2]]=null:p(u,t[2])&&(r[t[1]]=null);else if(2!==o&&i){if(!(2<=i))for(var a=0;a<t.length;a++){var s=t[a];if(!c(n,s)&&c(l,s)){n[s]=l[s];break}}return n}return r}function c(t,e){return t.hasOwnProperty(e)}function p(t,e){return null!=t[e]&&"auto"!==t[e]}function i(t,e,n){up(t,function(t){e[t]=n[t]})}i(cp[0],l,t),i(cp[1],l,e)}function yp(t){return e={},(n=t)&&e&&up(hp,function(t){n.hasOwnProperty(t)&&(e[t]=n[t])}),e;var e,n}ct(pp,"vertical"),ct(pp,"horizontal");var mp,vp,_p,xp,bp=Ao(),g=(u(wp,mp=_c),wp.prototype.init=function(t,e,n){this.mergeDefaultAndTheme(t,n)},wp.prototype.mergeDefaultAndTheme=function(t,e){var n=fp(this),i=n?yp(t):{};d(t,e.getTheme().get(this.mainType)),d(t,this.getDefaultOption()),n&&gp(t,i,n)},wp.prototype.mergeOption=function(t,e){d(this.option,t,!0);var n=fp(this);n&&gp(this.option,t,n)},wp.prototype.optionUpdated=function(t,e){},wp.prototype.getDefaultOption=function(){var t=this.constructor;if(!(e=t)||!e[Vo])return t.defaultOption;var e=bp(this);if(!e.defaultOption){for(var n=[],i=t;i;){var r=i.prototype.defaultOption;r&&n.push(r),i=i.superClass}for(var o={},a=n.length-1;0<=a;a--)o=d(o,n[a],!0);e.defaultOption=o}return e.defaultOption},wp.prototype.getReferringComponents=function(t,e){var n=t+"Id";return No(this.ecModel,t,{index:this.get(t+"Index",!0),id:this.get(n,!0)},e)},wp.prototype.getBoxLayoutParams=function(){return{left:this.get("left"),top:this.get("top"),right:this.get("right"),bottom:this.get("bottom"),width:this.get("width"),height:this.get("height")}},wp.prototype.getZLevelKey=function(){return""},wp.prototype.setZLevel=function(t){this.option.zlevel=t},wp.protoInitialize=((pc=wp.prototype).type="component",pc.id="",pc.name="",pc.mainType="",pc.subType="",void(pc.componentIndex=0)),wp);function wp(t,e,n){t=mp.call(this,t,e,n)||this;return t.uid=wc("ec_cpt_model"),t}function Sp(t,e){return t[e]||(t[e]={predecessor:[],successor:[]}),t[e]}Wo(g,_c),qo(g),_p={},(vp=g).registerSubTypeDefaulter=function(t,e){t=Ho(t);_p[t.main]=e},vp.determineSubType=function(t,e){var n,i=e.type;return i||(n=Ho(t).main,vp.hasSubTypes(t)&&_p[n]&&(i=_p[n](e))),i},xp=function(t){var e=[];O(g.getClassesByMainType(t),function(t){e=e.concat(t.dependencies||t.prototype.dependencies||[])}),e=B(e,function(t){return Ho(t).main}),"dataset"!==t&&k(e,"dataset")<=0&&e.unshift("dataset");return e},g.topologicalTravel=function(t,e,n,i){if(t.length){a={},s=[],O(o=e,function(n){var e,i,r=Sp(a,n),t=r.originalDeps=xp(n),t=(e=o,i=[],O(t,function(t){0<=k(e,t)&&i.push(t)}),i);r.entryCount=t.length,0===r.entryCount&&s.push(n),O(t,function(t){k(r.predecessor,t)<0&&r.predecessor.push(t);var e=Sp(a,t);k(e.successor,t)<0&&e.successor.push(n)})});var o,a,s,e={graph:a,noEntryList:s},r=e.graph,l=e.noEntryList,u={};for(O(t,function(t){u[t]=!0});l.length;){var h=l.pop(),c=r[h],p=!!u[h];p&&(n.call(i,h,c.originalDeps.slice()),delete u[h]),O(c.successor,p?f:d)}O(u,function(){throw new Error("")})}function d(t){r[t].entryCount--,0===r[t].entryCount&&l.push(t)}function f(t){u[t]=!0,d(t)}};var yc="",Ko=("undefined"!=typeof navigator&&(yc=navigator.platform||""),"rgba(0, 0, 0, 0.2)"),Mp={darkMode:"auto",colorBy:"series",color:["#5470c6","#91cc75","#fac858","#ee6666","#73c0de","#3ba272","#fc8452","#9a60b4","#ea7ccc"],gradientColor:["#f6efa6","#d88273","#bf444c"],aria:{decal:{decals:[{color:Ko,dashArrayX:[1,0],dashArrayY:[2,5],symbolSize:1,rotation:Math.PI/6},{color:Ko,symbol:"circle",dashArrayX:[[8,8],[0,8,8,0]],dashArrayY:[6,0],symbolSize:.8},{color:Ko,dashArrayX:[1,0],dashArrayY:[4,3],rotation:-Math.PI/4},{color:Ko,dashArrayX:[[6,6],[0,6,6,0]],dashArrayY:[6,0]},{color:Ko,dashArrayX:[[1,0],[1,6]],dashArrayY:[1,0,6,0],rotation:Math.PI/4},{color:Ko,symbol:"triangle",dashArrayX:[[9,9],[0,9,9,0]],dashArrayY:[7,2],symbolSize:.75}]}},textStyle:{fontFamily:yc.match(/^Win/)?"Microsoft YaHei":"sans-serif",fontSize:12,fontStyle:"normal",fontWeight:"normal"},blendMode:null,stateAnimation:{duration:300,easing:"cubicOut"},animation:"auto",animationDuration:1e3,animationDurationUpdate:500,animationEasing:"cubicInOut",animationEasingUpdate:"cubicInOut",animationThreshold:2e3,progressiveThreshold:3e3,progressive:400,hoverLayerThreshold:3e3,useUTC:!1},Tp=E(["tooltip","label","itemName","itemId","itemGroupId","itemChildGroupId","seriesName"]),Cp="original",kp="arrayRows",Ip="objectRows",Dp="keyedColumns",Ap="typedArray",Lp="unknown",Pp="column",Op="row",Rp={Must:1,Might:2,Not:3},Np=Ao();function Ep(n,t,e){var r,o,a,i,s,l={},u=zp(t);return u&&n&&(r=[],o=[],t=t.ecModel,t=Np(t).datasetMap,u=u.uid+"_"+e.seriesLayoutBy,O(n=n.slice(),function(t,e){t=R(t)?t:n[e]={name:t};"ordinal"===t.type&&null==a&&(a=e,i=c(t)),l[t.name]=[]}),s=t.get(u)||t.set(u,{categoryWayDim:i,valueWayDim:0}),O(n,function(t,e){var n,i=t.name,t=c(t);null==a?(n=s.valueWayDim,h(l[i],n,t),h(o,n,t),s.valueWayDim+=t):a===e?(h(l[i],0,t),h(r,0,t)):(n=s.categoryWayDim,h(l[i],n,t),h(o,n,t),s.categoryWayDim+=t)}),r.length&&(l.itemName=r),o.length)&&(l.seriesName=o),l;function h(t,e,n){for(var i=0;i<n;i++)t.push(e+i)}function c(t){t=t.dimsDef;return t?t.length:1}}function zp(t){if(!t.get("data",!0))return No(t.ecModel,"dataset",{index:t.get("datasetIndex",!0),id:t.get("datasetId",!0)},Ro).models[0]}function Bp(t,e){var n,i,r,o=t.data,a=t.sourceFormat,s=t.seriesLayoutBy,l=t.dimensionsDefine,u=t.startIndex,h=e;if(!gt(o)){if(l&&(R(l=l[h])?(i=l.name,r=l.type):V(l)&&(i=l)),null!=r)return"ordinal"===r?Rp.Must:Rp.Not;if(a===kp){var c=o;if(s===Op){for(var p=c[h],d=0;d<(p||[]).length&&d<5;d++)if(null!=(n=_(p[u+d])))return n}else for(d=0;d<c.length&&d<5;d++){var f=c[u+d];if(f&&null!=(n=_(f[h])))return n}}else if(a===Ip){var g=o;if(!i)return Rp.Not;for(d=0;d<g.length&&d<5;d++)if((m=g[d])&&null!=(n=_(m[i])))return n}else if(a===Dp){l=o;if(!i)return Rp.Not;if(!(p=l[i])||gt(p))return Rp.Not;for(d=0;d<p.length&&d<5;d++)if(null!=(n=_(p[d])))return n}else if(a===Cp)for(var y=o,d=0;d<y.length&&d<5;d++){var m,v=bo(m=y[d]);if(!F(v))return Rp.Not;if(null!=(n=_(v[h])))return n}}return Rp.Not;function _(t){var e=V(t);return null!=t&&Number.isFinite(Number(t))&&""!==t?e?Rp.Might:Rp.Not:e&&"-"!==t?Rp.Must:void 0}}var Fp=E();var Vp,Hp,Gp,Wp=Ao(),Up=(Ao(),Xp.prototype.getColorFromPalette=function(t,e,n){var i=vo(this.get("color",!0)),r=this.get("colorLayer",!0),o=this,a=Wp;return a=a(e=e||o),o=a.paletteIdx||0,(e=a.paletteNameMap=a.paletteNameMap||{}).hasOwnProperty(t)?e[t]:(r=(r=n==null||!r?i:Yp(r,n))||i)&&r.length?(n=r[o],t&&(e[t]=n),a.paletteIdx=(o+1)%r.length,n):void 0},Xp.prototype.clearColorPalette=function(){var t,e;(e=Wp)(t=this).paletteIdx=0,e(t).paletteNameMap={}},Xp);function Xp(){}function Yp(t,e){for(var n=t.length,i=0;i<n;i++)if(t[i].length>e)return t[i];return t[n-1]}var qp,Zp="\0_ec_inner",jp=(u(l,qp=_c),l.prototype.init=function(t,e,n,i,r,o){i=i||{},this.option=null,this._theme=new _c(i),this._locale=new _c(r),this._optionManager=o},l.prototype.setOption=function(t,e,n){e=Qp(e);this._optionManager.setOption(t,n,e),this._resetOption(null,e)},l.prototype.resetOption=function(t,e){return this._resetOption(t,Qp(e))},l.prototype._resetOption=function(t,e){var n,i=!1,r=this._optionManager;return t&&"recreate"!==t||(n=r.mountOption("recreate"===t),this.option&&"recreate"!==t?(this.restoreData(),this._mergeOption(n,e)):Gp(this,n),i=!0),"timeline"!==t&&"media"!==t||this.restoreData(),t&&"recreate"!==t&&"timeline"!==t||(n=r.getTimelineOption(this))&&(i=!0,this._mergeOption(n,e)),t&&"recreate"!==t&&"media"!==t||(n=r.getMediaOption(this)).length&&O(n,function(t){i=!0,this._mergeOption(t,e)},this),i},l.prototype.mergeOption=function(t){this._mergeOption(t,null)},l.prototype._mergeOption=function(i,t){var r=this.option,h=this._componentsMap,c=this._componentsCount,n=[],o=E(),p=t&&t.replaceMergeMainTypeMap;Np(this).datasetMap=E(),O(i,function(t,e){null!=t&&(g.hasClass(e)?e&&(n.push(e),o.set(e,!0)):r[e]=null==r[e]?y(t):d(r[e],t,!0))}),p&&p.each(function(t,e){g.hasClass(e)&&!o.get(e)&&(n.push(e),o.set(e,!0))}),g.topologicalTravel(n,g.getAllClassMainTypes(),function(o){var a,t=function(t,e,n){return(e=(e=Fp.get(e))&&e(t))?n.concat(e):n}(this,o,vo(i[o])),e=h.get(o),n=e?p&&p.get(o)?"replaceMerge":"normalMerge":"replaceAll",e=wo(e,t,n),s=(Io(e,o,g),r[o]=null,h.set(o,null),c.set(o,0),[]),l=[],u=0;O(e,function(t,e){var n=t.existing,i=t.newOption;if(i){var r=g.getClass(o,t.keyInfo.subType,!("series"===o));if(!r)return;if("tooltip"===o){if(a)return;a=!0}n&&n.constructor===r?(n.name=t.keyInfo.name,n.mergeOption(i,this),n.optionUpdated(i,!1)):(e=P({componentIndex:e},t.keyInfo),P(n=new r(i,this,this,e),e),t.brandNew&&(n.__requireNewView=!0),n.init(i,this,this),n.optionUpdated(null,!0))}else n&&(n.mergeOption({},this),n.optionUpdated({},!1));n?(s.push(n.option),l.push(n),u++):(s.push(void 0),l.push(void 0))},this),r[o]=s,h.set(o,l),c.set(o,u),"series"===o&&Vp(this)},this),this._seriesIndices||Vp(this)},l.prototype.getOption=function(){var a=y(this.option);return O(a,function(t,e){if(g.hasClass(e)){for(var n=vo(t),i=n.length,r=!1,o=i-1;0<=o;o--)n[o]&&!ko(n[o])?r=!0:(n[o]=null,r||i--);n.length=i,a[e]=n}}),delete a[Zp],a},l.prototype.getTheme=function(){return this._theme},l.prototype.getLocaleModel=function(){return this._locale},l.prototype.setUpdatePayload=function(t){this._payload=t},l.prototype.getUpdatePayload=function(){return this._payload},l.prototype.getComponent=function(t,e){var n=this._componentsMap.get(t);if(n){t=n[e||0];if(t)return t;if(null==e)for(var i=0;i<n.length;i++)if(n[i])return n[i]}},l.prototype.queryComponents=function(t){var e,n,i,r,o,a=t.mainType;return a&&(e=t.index,n=t.id,i=t.name,r=this._componentsMap.get(a))&&r.length?(null!=e?(o=[],O(vo(e),function(t){r[t]&&o.push(r[t])})):o=null!=n?Kp("id",n,r):null!=i?Kp("name",i,r):ut(r,function(t){return!!t}),$p(o,t)):[]},l.prototype.findComponents=function(t){var e,n=t.query,i=t.mainType,r=(r=i+"Index",o=i+"Id",e=i+"Name",!(n=n)||null==n[r]&&null==n[o]&&null==n[e]?null:{mainType:i,index:n[r],id:n[o],name:n[e]}),o=r?this.queryComponents(r):ut(this._componentsMap.get(i),function(t){return!!t});return n=$p(o,t),t.filter?ut(n,t.filter):n},l.prototype.eachComponent=function(t,e,n){var i=this._componentsMap;if(D(t)){var r=e,o=t;i.each(function(t,e){for(var n=0;t&&n<t.length;n++){var i=t[n];i&&o.call(r,e,i,i.componentIndex)}})}else for(var a=V(t)?i.get(t):R(t)?this.findComponents(t):null,s=0;a&&s<a.length;s++){var l=a[s];l&&e.call(n,l,l.componentIndex)}},l.prototype.getSeriesByName=function(t){var e=To(t,null);return ut(this._componentsMap.get("series"),function(t){return!!t&&null!=e&&t.name===e})},l.prototype.getSeriesByIndex=function(t){return this._componentsMap.get("series")[t]},l.prototype.getSeriesByType=function(e){return ut(this._componentsMap.get("series"),function(t){return!!t&&t.subType===e})},l.prototype.getSeries=function(){return ut(this._componentsMap.get("series"),function(t){return!!t})},l.prototype.getSeriesCount=function(){return this._componentsCount.get("series")},l.prototype.eachSeries=function(n,i){Hp(this),O(this._seriesIndices,function(t){var e=this._componentsMap.get("series")[t];n.call(i,e,t)},this)},l.prototype.eachRawSeries=function(e,n){O(this._componentsMap.get("series"),function(t){t&&e.call(n,t,t.componentIndex)})},l.prototype.eachSeriesByType=function(n,i,r){Hp(this),O(this._seriesIndices,function(t){var e=this._componentsMap.get("series")[t];e.subType===n&&i.call(r,e,t)},this)},l.prototype.eachRawSeriesByType=function(t,e,n){return O(this.getSeriesByType(t),e,n)},l.prototype.isSeriesFiltered=function(t){return Hp(this),null==this._seriesIndicesMap.get(t.componentIndex)},l.prototype.getCurrentSeriesIndices=function(){return(this._seriesIndices||[]).slice()},l.prototype.filterSeries=function(n,i){Hp(this);var r=[];O(this._seriesIndices,function(t){var e=this._componentsMap.get("series")[t];n.call(i,e,t)&&r.push(t)},this),this._seriesIndices=r,this._seriesIndicesMap=E(r)},l.prototype.restoreData=function(n){Vp(this);var t=this._componentsMap,i=[];t.each(function(t,e){g.hasClass(e)&&i.push(e)}),g.topologicalTravel(i,g.getAllClassMainTypes(),function(e){O(t.get(e),function(t){!t||"series"===e&&function(t,e){{var n,i;if(e)return n=e.seriesIndex,i=e.seriesId,e=e.seriesName,null!=n&&t.componentIndex!==n||null!=i&&t.id!==i||null!=e&&t.name!==e}}(t,n)||t.restoreData()})})},l.internalField=(Vp=function(t){var e=t._seriesIndices=[];O(t._componentsMap.get("series"),function(t){t&&e.push(t.componentIndex)}),t._seriesIndicesMap=E(e)},Hp=function(t){},void(Gp=function(t,e){t.option={},t.option[Zp]=1,t._componentsMap=E({series:[]}),t._componentsCount=E();var n,i,r=e.aria;R(r)&&null==r.enabled&&(r.enabled=!0),n=e,r=t._theme.option,i=n.color&&!n.colorLayer,O(r,function(t,e){"colorLayer"===e&&i||g.hasClass(e)||("object"==typeof t?n[e]=n[e]?d(n[e],t,!1):y(t):null==n[e]&&(n[e]=t))}),d(e,Mp,!1),t._mergeOption(e,null)})),l);function l(){return null!==qp&&qp.apply(this,arguments)||this}function Kp(e,t,n){var i,r;return F(t)?(i=E(),O(t,function(t){null!=t&&null!=To(t,null)&&i.set(t,!0)}),ut(n,function(t){return t&&i.get(t[e])})):(r=To(t,null),ut(n,function(t){return t&&null!=r&&t[e]===r}))}function $p(t,e){return e.hasOwnProperty("subType")?ut(t,function(t){return t&&t.subType===e.subType}):t}function Qp(t){var e=E();return t&&O(vo(t.replaceMerge),function(t){e.set(t,!0)}),{replaceMergeMainTypeMap:e}}at(jp,Up);function Jp(e){O(td,function(t){this[t]=ht(e[t],e)},this)}var td=["getDom","getZr","getWidth","getHeight","getDevicePixelRatio","dispatchAction","isSSR","isDisposed","on","off","getDataURL","getConnectedDataURL","getOption","getId","updateLabelLayout"],ed={},nd=(id.prototype.create=function(n,i){var r=[];O(ed,function(t,e){t=t.create(n,i);r=r.concat(t||[])}),this._coordinateSystems=r},id.prototype.update=function(e,n){O(this._coordinateSystems,function(t){t.update&&t.update(e,n)})},id.prototype.getCoordinateSystems=function(){return this._coordinateSystems.slice()},id.register=function(t,e){ed[t]=e},id.get=function(t){return ed[t]},id);function id(){this._coordinateSystems=[]}var rd=/^(min|max)?(.+)$/,od=(ad.prototype.setOption=function(t,e,n){t&&(O(vo(t.series),function(t){t&&t.data&&gt(t.data)&&It(t.data)}),O(vo(t.dataset),function(t){t&&t.source&&gt(t.source)&&It(t.source)})),t=y(t);var i=this._optionBackup,t=function(t,n,i){var e,r,o=[],a=t.baseOption,s=t.timeline,l=t.options,u=t.media,h=!!t.media,c=!!(l||s||a&&a.timeline);a?(r=a).timeline||(r.timeline=s):((c||h)&&(t.options=t.media=null),r=t);h&&F(u)&&O(u,function(t){t&&t.option&&(t.query?o.push(t):e=e||t)});function p(e){O(n,function(t){t(e,i)})}return p(r),O(l,p),O(o,function(t){return p(t.option)}),{baseOption:r,timelineOptions:l||[],mediaDefault:e,mediaList:o}}(t,e,!i);this._newBaseOption=t.baseOption,i?(t.timelineOptions.length&&(i.timelineOptions=t.timelineOptions),t.mediaList.length&&(i.mediaList=t.mediaList),t.mediaDefault&&(i.mediaDefault=t.mediaDefault)):this._optionBackup=t},ad.prototype.mountOption=function(t){var e=this._optionBackup;return this._timelineOptions=e.timelineOptions,this._mediaList=e.mediaList,this._mediaDefault=e.mediaDefault,this._currentMediaIndices=[],y(t?e.baseOption:this._newBaseOption)},ad.prototype.getTimelineOption=function(t){var e,n=this._timelineOptions;return e=n.length&&(t=t.getComponent("timeline"))?y(n[t.getCurrentIndex()]):e},ad.prototype.getMediaOption=function(t){var e=this._api.getWidth(),n=this._api.getHeight(),i=this._mediaList,r=this._mediaDefault,o=[],a=[];if(i.length||r){for(var s,l,u=0,h=i.length;u<h;u++)!function(t,e,n){var i={width:e,height:n,aspectratio:e/n},r=!0;return O(t,function(t,e){var n,e=e.match(rd);e&&e[1]&&e[2]&&(n=e[1],e=e[2].toLowerCase(),e=i[e],t=t,("min"===(n=n)?t<=e:"max"===n?e<=t:e===t)||(r=!1))}),r}(i[u].query,e,n)||o.push(u);(o=!o.length&&r?[-1]:o).length&&(s=o,l=this._currentMediaIndices,s.join(",")!==l.join(","))&&(a=B(o,function(t){return y((-1===t?r:i[t]).option)})),this._currentMediaIndices=o}return a},ad);function ad(t){this._timelineOptions=[],this._mediaList=[],this._currentMediaIndices=[],this._api=t}var sd=O,ld=R,ud=["areaStyle","lineStyle","nodeStyle","linkStyle","chordStyle","label","labelLine"];function hd(t){var e=t&&t.itemStyle;if(e)for(var n=0,i=ud.length;n<i;n++){var r=ud[n],o=e.normal,a=e.emphasis;o&&o[r]&&(t[r]=t[r]||{},t[r].normal?d(t[r].normal,o[r]):t[r].normal=o[r],o[r]=null),a&&a[r]&&(t[r]=t[r]||{},t[r].emphasis?d(t[r].emphasis,a[r]):t[r].emphasis=a[r],a[r]=null)}}function cd(t,e,n){var i,r;t&&t[e]&&(t[e].normal||t[e].emphasis)&&(i=t[e].normal,r=t[e].emphasis,i&&(n?(t[e].normal=t[e].emphasis=null,z(t[e],i)):t[e]=i),r)&&(t.emphasis=t.emphasis||{},(t.emphasis[e]=r).focus&&(t.emphasis.focus=r.focus),r.blurScope)&&(t.emphasis.blurScope=r.blurScope)}function pd(t){cd(t,"itemStyle"),cd(t,"lineStyle"),cd(t,"areaStyle"),cd(t,"label"),cd(t,"labelLine"),cd(t,"upperLabel"),cd(t,"edgeLabel")}function dd(t,e){var n=ld(t)&&t[e],i=ld(n)&&n.textStyle;if(i)for(var r=0,o=xo.length;r<o;r++){var a=xo[r];i.hasOwnProperty(a)&&(n[a]=i[a])}}function fd(t){t&&(pd(t),dd(t,"label"),t.emphasis)&&dd(t.emphasis,"label")}function gd(t){return F(t)?t:t?[t]:[]}function yd(t){return(F(t)?t[0]:t)||{}}function md(e,t){sd(gd(e.series),function(t){if(ld(t))if(ld(t)){hd(t),pd(t),dd(t,"label"),dd(t,"upperLabel"),dd(t,"edgeLabel"),t.emphasis&&(dd(t.emphasis,"label"),dd(t.emphasis,"upperLabel"),dd(t.emphasis,"edgeLabel"));var e=t.markPoint,n=(e&&(hd(e),fd(e)),t.markLine),i=(n&&(hd(n),fd(n)),t.markArea),r=(i&&fd(i),t.data);if("graph"===t.type){var r=r||t.nodes,o=t.links||t.edges;if(o&&!gt(o))for(var a=0;a<o.length;a++)fd(o[a]);O(t.categories,function(t){pd(t)})}if(r&&!gt(r))for(a=0;a<r.length;a++)fd(r[a]);if((e=t.markPoint)&&e.data)for(var s=e.data,a=0;a<s.length;a++)fd(s[a]);if((n=t.markLine)&&n.data)for(var l=n.data,a=0;a<l.length;a++)F(l[a])?(fd(l[a][0]),fd(l[a][1])):fd(l[a]);"gauge"===t.type?(dd(t,"axisLabel"),dd(t,"title"),dd(t,"detail")):"treemap"===t.type?(cd(t.breadcrumb,"itemStyle"),O(t.levels,function(t){pd(t)})):"tree"===t.type&&pd(t.leaves)}});var n=["xAxis","yAxis","radiusAxis","angleAxis","singleAxis","parallelAxis","radar"];t&&n.push("valueAxis","categoryAxis","logAxis","timeAxis"),sd(n,function(t){sd(gd(e[t]),function(t){t&&(dd(t,"axisLabel"),dd(t.axisPointer,"label"))})}),sd(gd(e.parallel),function(t){t=t&&t.parallelAxisDefault;dd(t,"axisLabel"),dd(t&&t.axisPointer,"label")}),sd(gd(e.calendar),function(t){cd(t,"itemStyle"),dd(t,"dayLabel"),dd(t,"monthLabel"),dd(t,"yearLabel")}),sd(gd(e.radar),function(t){dd(t,"name"),t.name&&null==t.axisName&&(t.axisName=t.name,delete t.name),null!=t.nameGap&&null==t.axisNameGap&&(t.axisNameGap=t.nameGap,delete t.nameGap)}),sd(gd(e.geo),function(t){ld(t)&&(fd(t),sd(gd(t.regions),function(t){fd(t)}))}),sd(gd(e.timeline),function(t){fd(t),cd(t,"label"),cd(t,"itemStyle"),cd(t,"controlStyle",!0);t=t.data;F(t)&&O(t,function(t){R(t)&&(cd(t,"label"),cd(t,"itemStyle"))})}),sd(gd(e.toolbox),function(t){cd(t,"iconStyle"),sd(t.feature,function(t){cd(t,"iconStyle")})}),dd(yd(e.axisPointer),"label"),dd(yd(e.tooltip).axisPointer,"label")}function vd(e){e&&O(_d,function(t){t[0]in e&&!(t[1]in e)&&(e[t[1]]=e[t[0]])})}var _d=[["x","left"],["y","top"],["x2","right"],["y2","bottom"]],xd=["grid","geo","parallel","legend","toolbox","title","visualMap","dataZoom","timeline"],bd=[["borderRadius","barBorderRadius"],["borderColor","barBorderColor"],["borderWidth","barBorderWidth"]];function wd(t){var e=t&&t.itemStyle;if(e)for(var n=0;n<bd.length;n++){var i=bd[n][1],r=bd[n][0];null!=e[i]&&(e[r]=e[i])}}function Sd(t){t&&"edge"===t.alignTo&&null!=t.margin&&null==t.edgeDistance&&(t.edgeDistance=t.margin)}function Md(t){t&&t.downplay&&!t.blur&&(t.blur=t.downplay)}function Td(e,t){md(e,t),e.series=vo(e.series),O(e.series,function(t){if(R(t)){var e,n=t.type;if("line"===n)null!=t.clipOverflow&&(t.clip=t.clipOverflow);else if("pie"===n||"gauge"===n){if(null!=t.clockWise&&(t.clockwise=t.clockWise),Sd(t.label),(e=t.data)&&!gt(e))for(var i=0;i<e.length;i++)Sd(e[i]);null!=t.hoverOffset&&(t.emphasis=t.emphasis||{},t.emphasis.scaleSize=null)&&(t.emphasis.scaleSize=t.hoverOffset)}else if("gauge"===n){var r=function(t,e){for(var n=e.split(","),i=t,r=0;r<n.length&&null!=(i=i&&i[n[r]]);r++);return i}(t,"pointer.color");if(null!=r){var o=t;var a="itemStyle.color";var s=void 0;for(var l,u=a.split(","),h=o,c=0;c<u.length-1;c++)null==h[l=u[c]]&&(h[l]={}),h=h[l];!s&&null!=h[u[c]]||(h[u[c]]=r)}}else if("bar"===n){if(wd(t),wd(t.backgroundStyle),wd(t.emphasis),(e=t.data)&&!gt(e))for(i=0;i<e.length;i++)"object"==typeof e[i]&&(wd(e[i]),wd(e[i]&&e[i].emphasis))}else"sunburst"===n?((a=t.highlightPolicy)&&(t.emphasis=t.emphasis||{},t.emphasis.focus||(t.emphasis.focus=a)),Md(t),function t(e,n){if(e)for(var i=0;i<e.length;i++)n(e[i]),e[i]&&t(e[i].children,n)}(t.data,Md)):"graph"===n||"sankey"===n?(o=t)&&null!=o.focusNodeAdjacency&&(o.emphasis=o.emphasis||{},null==o.emphasis.focus)&&(o.emphasis.focus="adjacency"):"map"===n&&(t.mapType&&!t.map&&(t.map=t.mapType),t.mapLocation)&&z(t,t.mapLocation);null!=t.hoverAnimation&&(t.emphasis=t.emphasis||{},t.emphasis)&&null==t.emphasis.scale&&(t.emphasis.scale=t.hoverAnimation),vd(t)}}),e.dataRange&&(e.visualMap=e.dataRange),O(xd,function(t){t=e[t];t&&O(t=F(t)?t:[t],function(t){vd(t)})})}function Cd(_){O(_,function(p,d){var f=[],g=[NaN,NaN],t=[p.stackResultDimension,p.stackedOverDimension],y=p.data,m=p.isStackedByIndex,v=p.seriesModel.get("stackStrategy")||"samesign";y.modify(t,function(t,e,n){var i,r,o=y.get(p.stackedDimension,n);if(isNaN(o))return g;m?r=y.getRawIndex(n):i=y.get(p.stackedByDimension,n);for(var a,s,l,u=NaN,h=d-1;0<=h;h--){var c=_[h];if(0<=(r=m?r:c.data.rawIndexOf(c.stackedByDimension,i))){c=c.data.getByRawIndex(c.stackResultDimension,r);if("all"===v||"positive"===v&&0<c||"negative"===v&&c<0||"samesign"===v&&0<=o&&0<c||"samesign"===v&&o<=0&&c<0){a=o,s=c,l=void 0,l=Math.max(no(a),no(s)),a+=s,o=$r<l?a:to(a,l),u=c;break}}}return f[0]=o,f[1]=u,f})})}var kd,Id,Dd=function(t){this.data=t.data||(t.sourceFormat===Dp?{}:[]),this.sourceFormat=t.sourceFormat||Lp,this.seriesLayoutBy=t.seriesLayoutBy||Pp,this.startIndex=t.startIndex||0,this.dimensionsDetectedCount=t.dimensionsDetectedCount,this.metaRawOption=t.metaRawOption;var e=this.dimensionsDefine=t.dimensionsDefine;if(e)for(var n=0;n<e.length;n++){var i=e[n];null==i.type&&Bp(this,n)===Rp.Must&&(i.type="ordinal")}};function Ad(t){return t instanceof Dd}function Ld(t,e,n){n=n||Od(t);var i=e.seriesLayoutBy,r=function(t,e,n,i,r){var o,a;if(!t)return{dimensionsDefine:Rd(r),startIndex:a,dimensionsDetectedCount:o};{var s;e===kp?(s=t,"auto"===i||null==i?Nd(function(t){null!=t&&"-"!==t&&(V(t)?null==a&&(a=1):a=0)},n,s,10):a=dt(i)?i:i?1:0,r||1!==a||(r=[],Nd(function(t,e){r[e]=null!=t?t+"":""},n,s,1/0)),o=r?r.length:n===Op?s.length:s[0]?s[0].length:null):e===Ip?r=r||function(t){var e,n=0;for(;n<t.length&&!(e=t[n++]););if(e)return I(e)}(t):e===Dp?r||(r=[],O(t,function(t,e){r.push(e)})):e===Cp&&(i=bo(t[0]),o=F(i)&&i.length||1)}return{startIndex:a,dimensionsDefine:Rd(r),dimensionsDetectedCount:o}}(t,n,i,e.sourceHeader,e.dimensions);return new Dd({data:t,sourceFormat:n,seriesLayoutBy:i,dimensionsDefine:r.dimensionsDefine,startIndex:r.startIndex,dimensionsDetectedCount:r.dimensionsDetectedCount,metaRawOption:y(e)})}function Pd(t){return new Dd({data:t,sourceFormat:gt(t)?Ap:Cp})}function Od(t){var e=Lp;if(gt(t))e=Ap;else if(F(t)){0===t.length&&(e=kp);for(var n=0,i=t.length;n<i;n++){var r=t[n];if(null!=r){if(F(r)||gt(r)){e=kp;break}if(R(r)){e=Ip;break}}}}else if(R(t))for(var o in t)if(Bt(t,o)&&st(t[o])){e=Dp;break}return e}function Rd(t){var i;if(t)return i=E(),B(t,function(t,e){var n,t={name:(t=R(t)?t:{name:t}).name,displayName:t.displayName,type:t.type};return null!=t.name&&(t.name+="",null==t.displayName&&(t.displayName=t.name),(n=i.get(t.name))?t.name+="-"+n.count++:i.set(t.name,{count:1})),t})}function Nd(t,e,n,i){if(e===Op)for(var r=0;r<n.length&&r<i;r++)t(n[r]?n[r][0]:null,r);else for(var o=n[0]||[],r=0;r<o.length&&r<i;r++)t(o[r],r)}function Ed(t){t=t.sourceFormat;return t===Ip||t===Dp}Hd.prototype.getSource=function(){return this._source},Hd.prototype.count=function(){return 0},Hd.prototype.getItem=function(t,e){},Hd.prototype.appendData=function(t){},Hd.prototype.clean=function(){},Hd.protoInitialize=((lc=Hd.prototype).pure=!1,void(lc.persistent=!0)),Hd.internalField=(Id=function(t,e,n){var i,r=n.sourceFormat,o=n.seriesLayoutBy,a=n.startIndex,n=n.dimensionsDefine;P(t,kd[Qd(r,o)]),r===Ap?(t.getItem=zd,t.count=Fd,t.fillStorage=Bd):(i=Xd(r,o),t.getItem=ht(i,null,e,a,n),i=Zd(r,o),t.count=ht(i,null,e,a,n))},zd=function(t,e){t-=this._offset,e=e||[];for(var n=this._data,i=this._dimSize,r=i*t,o=0;o<i;o++)e[o]=n[r+o];return e},Bd=function(t,e,n,i){for(var r=this._data,o=this._dimSize,a=0;a<o;a++){for(var s=i[a],l=null==s[0]?1/0:s[0],u=null==s[1]?-1/0:s[1],h=e-t,c=n[a],p=0;p<h;p++){var d=r[p*o+a];(c[t+p]=d)<l&&(l=d),u<d&&(u=d)}s[0]=l,s[1]=u}},Fd=function(){return this._data?this._data.length/this._dimSize:0},(lc={})[kp+"_"+Pp]={pure:!0,appendData:Gd},lc[kp+"_"+Op]={pure:!0,appendData:function(){throw new Error('Do not support appendData when set seriesLayoutBy: "row".')}},lc[Ip]={pure:!0,appendData:Gd},lc[Dp]={pure:!0,appendData:function(t){var r=this._data;O(t,function(t,e){for(var n=r[e]||(r[e]=[]),i=0;i<(t||[]).length;i++)n.push(t[i])})}},lc[Cp]={appendData:Gd},lc[Ap]={persistent:!1,pure:!0,appendData:function(t){this._data=t},clean:function(){this._offset+=this.count(),this._data=null}},void(kd=lc));var zd,Bd,Fd,Vd=Hd;function Hd(t,e){var t=Ad(t)?t:Pd(t),n=(this._source=t,this._data=t.data);t.sourceFormat===Ap&&(this._offset=0,this._dimSize=e,this._data=n),Id(this,n,t)}function Gd(t){for(var e=0;e<t.length;e++)this._data.push(t[e])}function Wd(t,e,n,i){return t[i]}(Nc={})[kp+"_"+Pp]=function(t,e,n,i){return t[i+e]},Nc[kp+"_"+Op]=function(t,e,n,i,r){i+=e;for(var o=r||[],a=t,s=0;s<a.length;s++){var l=a[s];o[s]=l?l[i]:null}return o},Nc[Ip]=Wd,Nc[Dp]=function(t,e,n,i,r){for(var o=r||[],a=0;a<n.length;a++){var s=t[n[a].name];o[a]=s?s[i]:null}return o},Nc[Cp]=Wd;var Ud=Nc;function Xd(t,e){return Ud[Qd(t,e)]}function Yd(t,e,n){return t.length}(pc={})[kp+"_"+Pp]=function(t,e,n){return Math.max(0,t.length-e)},pc[kp+"_"+Op]=function(t,e,n){t=t[0];return t?Math.max(0,t.length-e):0},pc[Ip]=Yd,pc[Dp]=function(t,e,n){t=t[n[0].name];return t?t.length:0},pc[Cp]=Yd;var qd=pc;function Zd(t,e){return qd[Qd(t,e)]}function jd(t,e,n){return t[e]}(Ko={})[kp]=jd,Ko[Ip]=function(t,e,n){return t[n]},Ko[Dp]=jd,Ko[Cp]=function(t,e,n){t=bo(t);return t instanceof Array?t[e]:t},Ko[Ap]=jd;var Kd=Ko;function $d(t){return Kd[t]}function Qd(t,e){return t===kp?t+"_"+e:t}function Jd(t,e,n){if(t){var i,r,e=t.getRawDataItem(e);if(null!=e)return i=(r=t.getStore()).getSource().sourceFormat,null!=n?(t=t.getDimensionIndex(n),n=r.getDimensionProperty(t),$d(i)(e,t,n)):(r=e,i===Cp?bo(e):r)}}var tf=/\{@(.+?)\}/g,ef=(nf.prototype.getDataParams=function(t,e){var n=this.getData(e),i=this.getRawValue(t,e),r=n.getRawIndex(t),o=n.getName(t),a=n.getRawDataItem(t),s=n.getItemVisual(t,"style"),t=s&&s[n.getItemVisual(t,"drawType")||"fill"],s=s&&s.stroke,l=this.mainType,u="series"===l,n=n.userOutput&&n.userOutput.get();return{componentType:l,componentSubType:this.subType,componentIndex:this.componentIndex,seriesType:u?this.subType:null,seriesIndex:this.seriesIndex,seriesId:u?this.id:null,seriesName:u?this.name:null,name:o,dataIndex:r,data:a,dataType:e,value:i,color:t,borderColor:s,dimensionNames:n?n.fullDimensions:null,encode:n?n.encode:null,$vars:["seriesName","name","value"]}},nf.prototype.getFormattedLabel=function(i,t,e,n,r,o){t=t||"normal";var a=this.getData(e),e=this.getDataParams(i,e);return o&&(e.value=o.interpolatedValue),null!=n&&F(e.value)&&(e.value=e.value[n]),D(r=r||a.getItemModel(i).get("normal"===t?["label","formatter"]:[t,"label","formatter"]))?(e.status=t,e.dimensionIndex=n,r(e)):V(r)?sp(r,e).replace(tf,function(t,e){var n=e.length,n=("["===e.charAt(0)&&"]"===e.charAt(n-1)&&(e=+e.slice(1,n-1)),Jd(a,i,e));return null!=(n=o&&F(o.interpolatedValue)&&0<=(e=a.getDimensionIndex(e))?o.interpolatedValue[e]:n)?n+"":""}):void 0},nf.prototype.getRawValue=function(t,e){return Jd(this.getData(e),t)},nf.prototype.formatTooltip=function(t,e,n){},nf);function nf(){}function rf(t){return new of(t)}af.prototype.perform=function(t){var e,n,i=this._upstream,r=t&&t.skip,o=(this._dirty&&i&&((o=this.context).data=o.outputData=i.context.outputData),this.__pipeline&&(this.__pipeline.currentTask=this),this._plan&&!r&&(e=this._plan(this.context)),u(this._modBy)),a=this._modDataCount||0,s=u(t&&t.modBy),l=t&&t.modDataCount||0;function u(t){return t=1<=t?t:1}o===s&&a===l||(e="reset"),!this._dirty&&"reset"!==e||(this._dirty=!1,n=this._doReset(r)),this._modBy=s,this._modDataCount=l;o=t&&t.step;if(this._dueEnd=i?i._outputDueEnd:this._count?this._count(this.context):1/0,this._progress){var h=this._dueIndex,c=Math.min(null!=o?this._dueIndex+o:1/0,this._dueEnd);if(!r&&(n||h<c)){var p=this._progress;if(F(p))for(var d=0;d<p.length;d++)this._doProgress(p[d],h,c,s,l);else this._doProgress(p,h,c,s,l)}this._dueIndex=c;a=null!=this._settedOutputEnd?this._settedOutputEnd:c;this._outputDueEnd=a}else this._dueIndex=this._outputDueEnd=null!=this._settedOutputEnd?this._settedOutputEnd:this._dueEnd;return this.unfinished()},af.prototype.dirty=function(){this._dirty=!0,this._onDirty&&this._onDirty(this.context)},af.prototype._doProgress=function(t,e,n,i,r){df.reset(e,n,i,r),this._callingProgress=t,this._callingProgress({start:e,end:n,count:n-e,next:df.next},this.context)},af.prototype._doReset=function(t){this._dueIndex=this._outputDueEnd=this._dueEnd=0,this._settedOutputEnd=null,!t&&this._reset&&((e=this._reset(this.context))&&e.progress&&(n=e.forceFirstProgress,e=e.progress),F(e))&&!e.length&&(e=null),this._progress=e,this._modBy=this._modDataCount=null;var e,n,t=this._downstream;return t&&t.dirty(),n},af.prototype.unfinished=function(){return this._progress&&this._dueIndex<this._dueEnd},af.prototype.pipe=function(t){this._downstream===t&&!this._dirty||((this._downstream=t)._upstream=this,t.dirty())},af.prototype.dispose=function(){this._disposed||(this._upstream&&(this._upstream._downstream=null),this._downstream&&(this._downstream._upstream=null),this._dirty=!1,this._disposed=!0)},af.prototype.getUpstream=function(){return this._upstream},af.prototype.getDownstream=function(){return this._downstream},af.prototype.setOutputEnd=function(t){this._outputDueEnd=this._settedOutputEnd=t};var of=af;function af(t){this._reset=(t=t||{}).reset,this._plan=t.plan,this._count=t.count,this._onDirty=t.onDirty,this._dirty=!0}var sf,lf,uf,hf,cf,pf,df=pf={reset:function(t,e,n,i){lf=t,sf=e,uf=n,hf=i,cf=Math.ceil(hf/uf),pf.next=1<uf&&0<hf?gf:ff}};function ff(){return lf<sf?lf++:null}function gf(){var t=lf%cf*uf+Math.ceil(lf/cf),t=sf<=lf?null:t<hf?t:lf;return lf++,t}function yf(t,e){e=e&&e.type;return"ordinal"===e?t:null==(t="time"!==e||dt(t)||null==t||"-"===t?t:+lo(t))||""===t?NaN:Number(t)}var mf=E({number:function(t){return parseFloat(t)},time:function(t){return+lo(t)},trim:function(t){return V(t)?Ct(t):t}});function vf(t){return mf.get(t)}var _f={lt:function(t,e){return t<e},lte:function(t,e){return t<=e},gt:function(t,e){return e<t},gte:function(t,e){return e<=t}},xf=(bf.prototype.evaluate=function(t){return dt(t)?this._opFn(t,this._rvalFloat):this._opFn(po(t),this._rvalFloat)},bf);function bf(t,e){dt(e)||f(""),this._opFn=_f[t],this._rvalFloat=po(e)}Sf.prototype.evaluate=function(t,e){var n=dt(t)?t:po(t),i=dt(e)?e:po(e),r=isNaN(n),o=isNaN(i);return r&&(n=this._incomparable),o&&(i=this._incomparable),r&&o&&(r=V(t),o=V(e),r&&(n=o?t:0),o)&&(i=r?e:0),n<i?this._resultLT:i<n?-this._resultLT:0};var wf=Sf;function Sf(t,e){t="desc"===t;this._resultLT=t?1:-1,this._incomparable="min"===(e=null==e?t?"min":"max":e)?-1/0:1/0}Tf.prototype.evaluate=function(t){var e,n=t===this._rval;return n||(e=typeof t)===this._rvalTypeof||"number"!=e&&"number"!==this._rvalTypeof||(n=po(t)===this._rvalFloat),this._isEQ?n:!n};var Mf=Tf;function Tf(t,e){this._rval=e,this._isEQ=t,this._rvalTypeof=typeof e,this._rvalFloat=po(e)}kf.prototype.getRawData=function(){throw new Error("not supported")},kf.prototype.getRawDataItem=function(t){throw new Error("not supported")},kf.prototype.cloneRawData=function(){},kf.prototype.getDimensionInfo=function(t){},kf.prototype.cloneAllDimensionInfo=function(){},kf.prototype.count=function(){},kf.prototype.retrieveValue=function(t,e){},kf.prototype.retrieveValueFromItem=function(t,e){},kf.prototype.convertValue=yf;var Cf=kf;function kf(){}function If(t){return Rf(t.sourceFormat)||f(""),t.data}function Df(t){var e=t.sourceFormat,n=t.data;if(Rf(e)||f(""),e===kp){for(var i=[],r=0,o=n.length;r<o;r++)i.push(n[r].slice());return i}if(e===Ip){for(i=[],r=0,o=n.length;r<o;r++)i.push(P({},n[r]));return i}}function Af(t,e,n){if(null!=n)return dt(n)||!isNaN(n)&&!Bt(e,n)?t[n]:Bt(e,n)?e[n]:void 0}function Lf(t){return y(t)}var Pf=E();function Of(t,e){var n=vo(t),t=n.length;t||f("");for(var i=0,r=t;i<r;i++)e=function(t,i){i.length||f("");R(t)||f("");var e=t.type,d=Pf.get(e);d||f("");e=B(i,function(t){var e=t,t=d,n=new Cf,i=e.data,r=n.sourceFormat=e.sourceFormat,o=e.startIndex,a=(e.seriesLayoutBy!==Pp&&f(""),[]),s={};if(h=e.dimensionsDefine)O(h,function(t,e){var n=t.name,e={index:e,name:n,displayName:t.displayName};a.push(e),null!=n&&(Bt(s,n)&&f(""),s[n]=e)});else for(var l=0;l<e.dimensionsDetectedCount;l++)a.push({index:l});var u=Xd(r,Pp),h=(t.__isBuiltIn&&(n.getRawDataItem=function(t){return u(i,o,a,t)},n.getRawData=ht(If,null,e)),n.cloneRawData=ht(Df,null,e),Zd(r,Pp)),c=(n.count=ht(h,null,i,o,a),$d(r)),p=(n.retrieveValue=function(t,e){t=u(i,o,a,t);return p(t,e)},n.retrieveValueFromItem=function(t,e){var n;return null!=t&&(n=a[e])?c(t,e,n.name):void 0});return n.getDimensionInfo=ht(Af,null,a,s),n.cloneAllDimensionInfo=ht(Lf,null,a),n});return B(vo(d.transform({upstream:e[0],upstreamList:e,config:y(t.config)})),function(t,e){R(t)||f(""),t.data||f("");Rf(Od(t.data))||f("");var n=i[0],e=n&&0===e&&!t.dimensions?((e=n.startIndex)&&(t.data=n.data.slice(0,e).concat(t.data)),{seriesLayoutBy:Pp,sourceHeader:e,dimensions:n.metaRawOption.dimensions}):{seriesLayoutBy:Pp,sourceHeader:0,dimensions:t.dimensions};return Ld(t.data,e,null)})}(n[i],e),i!==r-1&&(e.length=Math.max(e.length,1));return e}function Rf(t){return t===kp||t===Ip}var Nf,yc="undefined",Ef=typeof Uint32Array==yc?Array:Uint32Array,zf=typeof Uint16Array==yc?Array:Uint16Array,Bf=typeof Int32Array==yc?Array:Int32Array,lc=typeof Float64Array==yc?Array:Float64Array,Ff={float:lc,int:Bf,ordinal:Array,number:Array,time:lc};function Vf(t){return 65535<t?Ef:zf}function Hf(){return[1/0,-1/0]}function Gf(t,e,n,i,r){n=Ff[n||"float"];if(r){var o=t[e],a=o&&o.length;if(a!==i){for(var s=new n(i),l=0;l<a;l++)s[l]=o[l];t[e]=s}}else t[e]=new n(i)}h.prototype.initData=function(t,e,n){this._provider=t,this._chunks=[],this._indices=null,this.getRawIndex=this._getRawIdxIdentity;var i=t.getSource(),r=this.defaultDimValueGetter=Nf[i.sourceFormat];this._dimValueGetter=n||r,this._rawExtent=[],Ed(i),this._dimensions=B(e,function(t){return{type:t.type,property:t.property}}),this._initDataFromProvider(0,t.count())},h.prototype.getProvider=function(){return this._provider},h.prototype.getSource=function(){return this._provider.getSource()},h.prototype.ensureCalculationDimension=function(t,e){var n=this._calcDimNameToIdx,i=this._dimensions,r=n.get(t);if(null!=r){if(i[r].type===e)return r}else r=i.length;return i[r]={type:e},n.set(t,r),this._chunks[r]=new Ff[e||"float"](this._rawCount),this._rawExtent[r]=Hf(),r},h.prototype.collectOrdinalMeta=function(t,e){for(var n=this._chunks[t],i=this._dimensions[t],r=this._rawExtent,o=i.ordinalOffset||0,a=n.length,s=(0===o&&(r[t]=Hf()),r[t]),l=o;l<a;l++){var u=n[l]=e.parseAndCollect(n[l]);isNaN(u)||(s[0]=Math.min(u,s[0]),s[1]=Math.max(u,s[1]))}i.ordinalMeta=e,i.ordinalOffset=a,i.type="ordinal"},h.prototype.getOrdinalMeta=function(t){return this._dimensions[t].ordinalMeta},h.prototype.getDimensionProperty=function(t){t=this._dimensions[t];return t&&t.property},h.prototype.appendData=function(t){var e=this._provider,n=this.count(),t=(e.appendData(t),e.count());return e.persistent||(t+=n),n<t&&this._initDataFromProvider(n,t,!0),[n,t]},h.prototype.appendValues=function(t,e){for(var n=this._chunks,i=this._dimensions,r=i.length,o=this._rawExtent,a=this.count(),s=a+Math.max(t.length,e||0),l=0;l<r;l++)Gf(n,l,(d=i[l]).type,s,!0);for(var u=[],h=a;h<s;h++)for(var c=h-a,p=0;p<r;p++){var d=i[p],f=Nf.arrayRows.call(this,t[c]||u,d.property,c,p),g=(n[p][h]=f,o[p]);f<g[0]&&(g[0]=f),f>g[1]&&(g[1]=f)}return{start:a,end:this._rawCount=this._count=s}},h.prototype._initDataFromProvider=function(t,e,n){for(var i=this._provider,r=this._chunks,o=this._dimensions,a=o.length,s=this._rawExtent,l=B(o,function(t){return t.property}),u=0;u<a;u++){var h=o[u];s[u]||(s[u]=Hf()),Gf(r,u,h.type,e,n)}if(i.fillStorage)i.fillStorage(t,e,r,s);else for(var c=[],p=t;p<e;p++)for(var c=i.getItem(p,c),d=0;d<a;d++){var f=r[d],g=this._dimValueGetter(c,l[d],p,d),f=(f[p]=g,s[d]);g<f[0]&&(f[0]=g),g>f[1]&&(f[1]=g)}!i.persistent&&i.clean&&i.clean(),this._rawCount=this._count=e,this._extent=[]},h.prototype.count=function(){return this._count},h.prototype.get=function(t,e){return 0<=e&&e<this._count&&(t=this._chunks[t])?t[this.getRawIndex(e)]:NaN},h.prototype.getValues=function(t,e){var n=[],i=[];if(null==e){e=t,t=[];for(var r=0;r<this._dimensions.length;r++)i.push(r)}else i=t;for(var r=0,o=i.length;r<o;r++)n.push(this.get(i[r],e));return n},h.prototype.getByRawIndex=function(t,e){return 0<=e&&e<this._rawCount&&(t=this._chunks[t])?t[e]:NaN},h.prototype.getSum=function(t){var e=0;if(this._chunks[t])for(var n=0,i=this.count();n<i;n++){var r=this.get(t,n);isNaN(r)||(e+=r)}return e},h.prototype.getMedian=function(t){var e=[],t=(this.each([t],function(t){isNaN(t)||e.push(t)}),e.sort(function(t,e){return t-e})),n=this.count();return 0===n?0:n%2==1?t[(n-1)/2]:(t[n/2]+t[n/2-1])/2},h.prototype.indexOfRawIndex=function(t){if(!(t>=this._rawCount||t<0)){if(!this._indices)return t;var e=this._indices,n=e[t];if(null!=n&&n<this._count&&n===t)return t;for(var i=0,r=this._count-1;i<=r;){var o=(i+r)/2|0;if(e[o]<t)i=1+o;else{if(!(e[o]>t))return o;r=o-1}}}return-1},h.prototype.indicesOfNearest=function(t,e,n){var i=this._chunks[t],r=[];if(i){null==n&&(n=1/0);for(var o=1/0,a=-1,s=0,l=0,u=this.count();l<u;l++){var h=e-i[this.getRawIndex(l)],c=Math.abs(h);c<=n&&((c<o||c===o&&0<=h&&a<0)&&(o=c,a=h,s=0),h===a)&&(r[s++]=l)}r.length=s}return r},h.prototype.getIndices=function(){var t=this._indices;if(t){var e=t.constructor,n=this._count;if(e===Array)for(var i=new e(n),r=0;r<n;r++)i[r]=t[r];else i=new e(t.buffer,0,n)}else{i=new(e=Vf(this._rawCount))(this.count());for(r=0;r<i.length;r++)i[r]=r}return i},h.prototype.filter=function(t,e){if(!this._count)return this;for(var n=this.clone(),i=n.count(),r=new(Vf(n._rawCount))(i),o=[],a=t.length,s=0,l=t[0],u=n._chunks,h=0;h<i;h++){var c=void 0,p=n.getRawIndex(h);if(0===a)c=e(h);else if(1===a)c=e(u[l][p],h);else{for(var d=0;d<a;d++)o[d]=u[t[d]][p];o[d]=h,c=e.apply(null,o)}c&&(r[s++]=p)}return s<i&&(n._indices=r),n._count=s,n._extent=[],n._updateGetRawIdx(),n},h.prototype.selectRange=function(t){var e=this.clone(),n=e._count;if(!n)return this;var i=I(t),r=i.length;if(!r)return this;var o=e.count(),a=new(Vf(e._rawCount))(o),s=0,l=i[0],u=t[l][0],h=t[l][1],c=e._chunks,l=!1;if(!e._indices){var p=0;if(1===r){for(var d=c[i[0]],f=0;f<n;f++)(u<=(v=d[f])&&v<=h||isNaN(v))&&(a[s++]=p),p++;l=!0}else if(2===r){for(var d=c[i[0]],g=c[i[1]],y=t[i[1]][0],m=t[i[1]][1],f=0;f<n;f++){var v=d[f],_=g[f];(u<=v&&v<=h||isNaN(v))&&(y<=_&&_<=m||isNaN(_))&&(a[s++]=p),p++}l=!0}}if(!l)if(1===r)for(f=0;f<o;f++){var x=e.getRawIndex(f);(u<=(v=c[i[0]][x])&&v<=h||isNaN(v))&&(a[s++]=x)}else for(f=0;f<o;f++){for(var b=!0,x=e.getRawIndex(f),w=0;w<r;w++){var S=i[w];((v=c[S][x])<t[S][0]||v>t[S][1])&&(b=!1)}b&&(a[s++]=e.getRawIndex(f))}return s<o&&(e._indices=a),e._count=s,e._extent=[],e._updateGetRawIdx(),e},h.prototype.map=function(t,e){var n=this.clone(t);return this._updateDims(n,t,e),n},h.prototype.modify=function(t,e){this._updateDims(this,t,e)},h.prototype._updateDims=function(t,e,n){for(var i=t._chunks,r=[],o=e.length,a=t.count(),s=[],l=t._rawExtent,u=0;u<e.length;u++)l[e[u]]=Hf();for(var h=0;h<a;h++){for(var c=t.getRawIndex(h),p=0;p<o;p++)s[p]=i[e[p]][c];s[o]=h;var d=n&&n.apply(null,s);if(null!=d){"object"!=typeof d&&(r[0]=d,d=r);for(u=0;u<d.length;u++){var f=e[u],g=d[u],y=l[f],f=i[f];f&&(f[c]=g),g<y[0]&&(y[0]=g),g>y[1]&&(y[1]=g)}}}},h.prototype.lttbDownSample=function(t,e){var n,i=this.clone([t],!0),r=i._chunks[t],o=this.count(),a=0,s=Math.floor(1/e),l=this.getRawIndex(0),u=new(Vf(this._rawCount))(Math.min(2*(Math.ceil(o/s)+2),o));u[a++]=l;for(var h=1;h<o-1;h+=s){for(var c=Math.min(h+s,o-1),p=Math.min(h+2*s,o),d=(p+c)/2,f=0,g=c;g<p;g++){var y=r[M=this.getRawIndex(g)];isNaN(y)||(f+=y)}f/=p-c;for(var c=h,m=Math.min(h+s,o),v=h-1,_=r[l],x=-1,b=c,w=-1,S=0,g=c;g<m;g++){var M,y=r[M=this.getRawIndex(g)];isNaN(y)?(S++,w<0&&(w=M)):x<(n=Math.abs((v-d)*(y-_)-(v-g)*(f-_)))&&(x=n,b=M)}0<S&&S<m-c&&(u[a++]=Math.min(w,b),b=Math.max(w,b)),l=u[a++]=b}return u[a++]=this.getRawIndex(o-1),i._count=a,i._indices=u,i.getRawIndex=this._getRawIdx,i},h.prototype.downSample=function(t,e,n,i){for(var r=this.clone([t],!0),o=r._chunks,a=[],s=Math.floor(1/e),l=o[t],u=this.count(),h=r._rawExtent[t]=Hf(),c=new(Vf(this._rawCount))(Math.ceil(u/s)),p=0,d=0;d<u;d+=s){u-d<s&&(a.length=s=u-d);for(var f=0;f<s;f++){var g=this.getRawIndex(d+f);a[f]=l[g]}var y=n(a),m=this.getRawIndex(Math.min(d+i(a,y)||0,u-1));(l[m]=y)<h[0]&&(h[0]=y),y>h[1]&&(h[1]=y),c[p++]=m}return r._count=p,r._indices=c,r._updateGetRawIdx(),r},h.prototype.each=function(t,e){if(this._count)for(var n=t.length,i=this._chunks,r=0,o=this.count();r<o;r++){var a=this.getRawIndex(r);switch(n){case 0:e(r);break;case 1:e(i[t[0]][a],r);break;case 2:e(i[t[0]][a],i[t[1]][a],r);break;default:for(var s=0,l=[];s<n;s++)l[s]=i[t[s]][a];l[s]=r,e.apply(null,l)}}},h.prototype.getDataExtent=function(t){var e=this._chunks[t],n=Hf();if(!e)return n;var i=this.count();if(!this._indices)return this._rawExtent[t].slice();if(r=this._extent[t])return r.slice();for(var r,o=(r=n)[0],a=r[1],s=0;s<i;s++){var l=e[this.getRawIndex(s)];l<o&&(o=l),a<l&&(a=l)}return this._extent[t]=r=[o,a]},h.prototype.getRawDataItem=function(t){var e=this.getRawIndex(t);if(this._provider.persistent)return this._provider.getItem(e);for(var n=[],i=this._chunks,r=0;r<i.length;r++)n.push(i[r][e]);return n},h.prototype.clone=function(t,e){var n,i,r=new h,o=this._chunks,a=t&&lt(t,function(t,e){return t[e]=!0,t},{});if(a)for(var s=0;s<o.length;s++)r._chunks[s]=a[s]?(n=o[s],i=void 0,(i=n.constructor)===Array?n.slice():new i(n)):o[s];else r._chunks=o;return this._copyCommonProps(r),e||(r._indices=this._cloneIndices()),r._updateGetRawIdx(),r},h.prototype._copyCommonProps=function(t){t._count=this._count,t._rawCount=this._rawCount,t._provider=this._provider,t._dimensions=this._dimensions,t._extent=y(this._extent),t._rawExtent=y(this._rawExtent)},h.prototype._cloneIndices=function(){if(this._indices){var t=this._indices.constructor,e=void 0;if(t===Array)for(var n=this._indices.length,e=new t(n),i=0;i<n;i++)e[i]=this._indices[i];else e=new t(this._indices);return e}return null},h.prototype._getRawIdxIdentity=function(t){return t},h.prototype._getRawIdx=function(t){return t<this._count&&0<=t?this._indices[t]:-1},h.prototype._updateGetRawIdx=function(){this.getRawIndex=this._indices?this._getRawIdx:this._getRawIdxIdentity},h.internalField=void(Nf={arrayRows:Uf,objectRows:function(t,e,n,i){return yf(t[e],this._dimensions[i])},keyedColumns:Uf,original:function(t,e,n,i){t=t&&(null==t.value?t:t.value);return yf(t instanceof Array?t[i]:t,this._dimensions[i])},typedArray:function(t,e,n,i){return t[i]}});var Wf=h;function h(){this._chunks=[],this._rawExtent=[],this._extent=[],this._count=0,this._rawCount=0,this._calcDimNameToIdx=E()}function Uf(t,e,n,i){return yf(t[i],this._dimensions[i])}Yf.prototype.dirty=function(){this._setLocalSource([],[]),this._storeList=[],this._dirty=!0},Yf.prototype._setLocalSource=function(t,e){this._sourceList=t,this._upstreamSignList=e,this._versionSignBase++,9e10<this._versionSignBase&&(this._versionSignBase=0)},Yf.prototype._getVersionSign=function(){return this._sourceHost.uid+"_"+this._versionSignBase},Yf.prototype.prepareSource=function(){this._isDirty()&&(this._createSource(),this._dirty=!1)},Yf.prototype._createSource=function(){this._setLocalSource([],[]);var t,e,n,i,r,o,a,s=this._sourceHost,l=this._getUpstreamSourceManagers(),u=!!l.length;Zf(s)?(i=s,r=t=o=void 0,e=u?((e=l[0]).prepareSource(),o=(r=e.getSource()).data,t=r.sourceFormat,[e._getVersionSign()]):(t=gt(o=i.get("data",!0))?Ap:Cp,[]),i=this._getSourceMetaRawOption()||{},r=r&&r.metaRawOption||{},a=N(i.seriesLayoutBy,r.seriesLayoutBy)||null,n=N(i.sourceHeader,r.sourceHeader),i=N(i.dimensions,r.dimensions),r=a!==r.seriesLayoutBy||!!n!=!!r.sourceHeader||i?[Ld(o,{seriesLayoutBy:a,sourceHeader:n,dimensions:i},t)]:[]):(o=s,e=u?(r=(a=this._applyTransform(l)).sourceList,a.upstreamSignList):(r=[Ld(o.get("source",!0),this._getSourceMetaRawOption(),null)],[])),this._setLocalSource(r,e)},Yf.prototype._applyTransform=function(t){var e,n=this._sourceHost,i=n.get("transform",!0),r=n.get("fromTransformResult",!0),o=(null!=r&&1!==t.length&&jf(""),[]),a=[];return O(t,function(t){t.prepareSource();var e=t.getSource(r||0);null==r||e||jf(""),o.push(e),a.push(t._getVersionSign())}),i?e=Of(i,o,n.componentIndex):null!=r&&(e=[new Dd({data:(t=o[0]).data,sourceFormat:t.sourceFormat,seriesLayoutBy:t.seriesLayoutBy,dimensionsDefine:y(t.dimensionsDefine),startIndex:t.startIndex,dimensionsDetectedCount:t.dimensionsDetectedCount})]),{sourceList:e,upstreamSignList:a}},Yf.prototype._isDirty=function(){if(this._dirty)return!0;for(var t=this._getUpstreamSourceManagers(),e=0;e<t.length;e++){var n=t[e];if(n._isDirty()||this._upstreamSignList[e]!==n._getVersionSign())return!0}},Yf.prototype.getSource=function(t){var e=this._sourceList[t=t||0];return e||(e=this._getUpstreamSourceManagers())[0]&&e[0].getSource(t)},Yf.prototype.getSharedDataStore=function(t){var e=t.makeStoreSchema();return this._innerGetDataStore(e.dimensions,t.source,e.hash)},Yf.prototype._innerGetDataStore=function(t,e,n){var i,r=this._storeList,o=r[0],r=(o=o||(r[0]={}))[n];return r||(i=this._getUpstreamSourceManagers()[0],Zf(this._sourceHost)&&i?r=i._innerGetDataStore(t,e,n):(r=new Wf).initData(new Vd(e,t.length),t),o[n]=r),r},Yf.prototype._getUpstreamSourceManagers=function(){var t,e=this._sourceHost;return Zf(e)?(t=zp(e))?[t.getSourceManager()]:[]:B((t=e).get("transform",!0)||t.get("fromTransformResult",!0)?No(t.ecModel,"dataset",{index:t.get("fromDatasetIndex",!0),id:t.get("fromDatasetId",!0)},Ro).models:[],function(t){return t.getSourceManager()})},Yf.prototype._getSourceMetaRawOption=function(){var t,e,n,i=this._sourceHost;return Zf(i)?(t=i.get("seriesLayoutBy",!0),e=i.get("sourceHeader",!0),n=i.get("dimensions",!0)):this._getUpstreamSourceManagers().length||(t=(i=i).get("seriesLayoutBy",!0),e=i.get("sourceHeader",!0),n=i.get("dimensions",!0)),{seriesLayoutBy:t,sourceHeader:e,dimensions:n}};var Xf=Yf;function Yf(t){this._sourceList=[],this._storeList=[],this._upstreamSignList=[],this._versionSignBase=0,this._dirty=!0,this._sourceHost=t}function qf(t){t.option.transform&&It(t.option.transform)}function Zf(t){return"series"===t.mainType}function jf(t){throw new Error(t)}function Kf(t,e){return e.type=t,e}function $f(t){var e,n,i,r,o,a,s,l,u,h,c,p=t.series,d=t.dataIndex,t=t.multipleSeries,f=p.getData(),g=f.mapDimensionsAll("defaultedTooltip"),y=g.length,m=p.getRawValue(d),v=F(m),_=(_=d,lp((b=p).getData().getItemVisual(_,"style")[b.visualDrawType]));function x(t,e){e=s.getDimensionInfo(e);e&&!1!==e.otherDims.tooltip&&(l?c.push(Kf("nameValue",{markerType:"subItem",markerColor:a,name:e.displayName,value:t,valueType:e.type})):(u.push(t),h.push(e.type)))}1<y||v&&!y?(b=m,r=d,o=g,a=_,s=p.getData(),l=lt(b,function(t,e,n){n=s.getDimensionInfo(n);return t||n&&!1!==n.tooltip&&null!=n.displayName},!1),u=[],h=[],c=[],o.length?O(o,function(t){x(Jd(s,r,t),t)}):O(b,x),e=(o={inlineValues:u,inlineValueTypes:h,blocks:c}).inlineValueTypes,n=o.blocks,i=(o=o.inlineValues)[0]):y?(b=f.getDimensionInfo(g[0]),i=o=Jd(f,d,g[0]),e=b.type):i=o=v?m[0]:m;var y=Co(p),g=y&&p.name||"",b=f.getName(d),v=t?g:b;return Kf("section",{header:g,noHeader:t||!y,sortParam:i,blocks:[Kf("nameValue",{markerType:"item",markerColor:_,name:v,noName:!Ct(v),value:o,valueType:e,dataIndex:d})].concat(n||[])})}var Qf=Ao();function Jf(t,e){return t.getName(e)||t.getId(e)}u(c,tg=g),c.prototype.init=function(t,e,n){this.seriesIndex=this.componentIndex,this.dataTask=rf({count:ig,reset:rg}),this.dataTask.context={model:this},this.mergeDefaultAndTheme(t,n);(Qf(this).sourceManager=new Xf(this)).prepareSource();t=this.getInitialData(t,n);ag(t,this),this.dataTask.context.data=t,Qf(this).dataBeforeProcessed=t,ng(this),this._initSelectedMapFromData(t)},c.prototype.mergeDefaultAndTheme=function(t,e){var n=fp(this),i=n?yp(t):{},r=this.subType;g.hasClass(r),d(t,e.getTheme().get(this.subType)),d(t,this.getDefaultOption()),_o(t,"label",["show"]),this.fillDataTextStyle(t.data),n&&gp(t,i,n)},c.prototype.mergeOption=function(t,e){t=d(this.option,t,!0),this.fillDataTextStyle(t.data);var n=fp(this),n=(n&&gp(this.option,t,n),Qf(this).sourceManager),n=(n.dirty(),n.prepareSource(),this.getInitialData(t,e));ag(n,this),this.dataTask.dirty(),this.dataTask.context.data=n,Qf(this).dataBeforeProcessed=n,ng(this),this._initSelectedMapFromData(n)},c.prototype.fillDataTextStyle=function(t){if(t&&!gt(t))for(var e=["show"],n=0;n<t.length;n++)t[n]&&t[n].label&&_o(t[n],"label",e)},c.prototype.getInitialData=function(t,e){},c.prototype.appendData=function(t){this.getRawData().appendData(t.data)},c.prototype.getData=function(t){var e=lg(this);return e?(e=e.context.data,null!=t&&e.getLinkedData?e.getLinkedData(t):e):Qf(this).data},c.prototype.getAllData=function(){var t=this.getData();return t&&t.getLinkedDataAll?t.getLinkedDataAll():[{data:t}]},c.prototype.setData=function(t){var e,n=lg(this);n&&((e=n.context).outputData=t,n!==this.dataTask)&&(e.data=t),Qf(this).data=t},c.prototype.getEncode=function(){var t=this.get("encode",!0);if(t)return E(t)},c.prototype.getSourceManager=function(){return Qf(this).sourceManager},c.prototype.getSource=function(){return this.getSourceManager().getSource()},c.prototype.getRawData=function(){return Qf(this).dataBeforeProcessed},c.prototype.getColorBy=function(){return this.get("colorBy")||"series"},c.prototype.isColorBySeries=function(){return"series"===this.getColorBy()},c.prototype.getBaseAxis=function(){var t=this.coordinateSystem;return t&&t.getBaseAxis&&t.getBaseAxis()},c.prototype.formatTooltip=function(t,e,n){return $f({series:this,dataIndex:t,multipleSeries:e})},c.prototype.isAnimationEnabled=function(){var t=this.ecModel;return!!(!p.node||t&&t.ssr)&&!!(t=(t=this.getShallow("animation"))&&this.getData().count()>this.getShallow("animationThreshold")?!1:t)},c.prototype.restoreData=function(){this.dataTask.dirty()},c.prototype.getColorFromPalette=function(t,e,n){var i=this.ecModel;return Up.prototype.getColorFromPalette.call(this,t,e,n)||i.getColorFromPalette(t,e,n)},c.prototype.coordDimToDataDim=function(t){return this.getRawData().mapDimensionsAll(t)},c.prototype.getProgressive=function(){return this.get("progressive")},c.prototype.getProgressiveThreshold=function(){return this.get("progressiveThreshold")},c.prototype.select=function(t,e){this._innerSelect(this.getData(e),t)},c.prototype.unselect=function(t,e){var n=this.option.selectedMap;if(n){var i=this.option.selectedMode,r=this.getData(e);if("series"===i||"all"===n)this.option.selectedMap={},this._selectedDataIndicesMap={};else for(var o=0;o<t.length;o++){var a=Jf(r,t[o]);n[a]=!1,this._selectedDataIndicesMap[a]=-1}}},c.prototype.toggleSelect=function(t,e){for(var n=[],i=0;i<t.length;i++)n[0]=t[i],this.isSelected(t[i],e)?this.unselect(n,e):this.select(n,e)},c.prototype.getSelectedDataIndices=function(){if("all"===this.option.selectedMap)return[].slice.call(this.getData().getIndices());for(var t=this._selectedDataIndicesMap,e=I(t),n=[],i=0;i<e.length;i++){var r=t[e[i]];0<=r&&n.push(r)}return n},c.prototype.isSelected=function(t,e){var n=this.option.selectedMap;return!!n&&(e=this.getData(e),"all"===n||n[Jf(e,t)])&&!e.getItemModel(t).get(["select","disabled"])},c.prototype.isUniversalTransitionEnabled=function(){var t;return!!this.__universalTransitionEnabled||!!(t=this.option.universalTransition)&&(!0===t||t&&t.enabled)},c.prototype._innerSelect=function(t,e){var n=this.option,i=n.selectedMode,r=e.length;if(i&&r)if("series"===i)n.selectedMap="all";else if("multiple"===i){R(n.selectedMap)||(n.selectedMap={});for(var o=n.selectedMap,a=0;a<r;a++){var s,l=e[a];o[s=Jf(t,l)]=!0,this._selectedDataIndicesMap[s]=t.getRawIndex(l)}}else"single"!==i&&!0!==i||(s=Jf(t,i=e[r-1]),n.selectedMap=((n={})[s]=!0,n),this._selectedDataIndicesMap=((n={})[s]=t.getRawIndex(i),n))},c.prototype._initSelectedMapFromData=function(n){var i;this.option.selectedMap||(i=[],n.hasItemOption&&n.each(function(t){var e=n.getRawDataItem(t);e&&e.selected&&i.push(t)}),0<i.length&&this._innerSelect(n,i))},c.registerClass=function(t){return g.registerClass(t)},c.protoInitialize=((Nc=c.prototype).type="series.__base__",Nc.seriesIndex=0,Nc.ignoreStyleOnData=!1,Nc.hasSymbolVisual=!1,Nc.defaultSymbol="circle",Nc.visualStyleAccessPath="itemStyle",void(Nc.visualDrawType="fill"));var tg,eg=c;function c(){var t=null!==tg&&tg.apply(this,arguments)||this;return t._selectedDataIndicesMap={},t}function ng(t){var e,n,i=t.name;Co(t)||(t.name=(t=(e=(t=t).getRawData()).mapDimensionsAll("seriesName"),n=[],O(t,function(t){t=e.getDimensionInfo(t);t.displayName&&n.push(t.displayName)}),n.join(" ")||i))}function ig(t){return t.model.getRawData().count()}function rg(t){t=t.model;return t.setData(t.getRawData().cloneShallow()),og}function og(t,e){e.outputData&&t.end>e.outputData.count()&&e.model.getRawData().cloneShallow(e.outputData)}function ag(e,n){O(Nt(e.CHANGABLE_METHODS,e.DOWNSAMPLE_METHODS),function(t){e.wrapMethod(t,ct(sg,n))})}function sg(t,e){t=lg(t);return t&&t.setOutputEnd((e||this).count()),e}function lg(t){var e,n=(t.ecModel||{}).scheduler,n=n&&n.getPipeline(t.uid);if(n)return(n=n.currentTask)&&(e=n.agentStubMap)?e.get(t.uid):n}at(eg,ef),at(eg,Up),Wo(eg,g);hg.prototype.init=function(t,e){},hg.prototype.render=function(t,e,n,i){},hg.prototype.dispose=function(t,e){},hg.prototype.updateView=function(t,e,n,i){},hg.prototype.updateLayout=function(t,e,n,i){},hg.prototype.updateVisual=function(t,e,n,i){},hg.prototype.toggleBlurSeries=function(t,e,n){},hg.prototype.eachRendered=function(t){var e=this.group;e&&e.traverse(t)};var ug=hg;function hg(){this.group=new Vr,this.uid=wc("viewComponent")}function cg(){var o=Ao();return function(t){var e=o(t),t=t.pipelineContext,n=!!e.large,i=!!e.progressiveRender,r=e.large=!(!t||!t.large),e=e.progressiveRender=!(!t||!t.progressiveRender);return!(n==r&&i==e)&&"reset"}}Go(ug),qo(ug);var pg=Ao(),dg=cg(),fg=(gg.prototype.init=function(t,e){},gg.prototype.render=function(t,e,n,i){},gg.prototype.highlight=function(t,e,n,i){t=t.getData(i&&i.dataType);t&&mg(t,i,"emphasis")},gg.prototype.downplay=function(t,e,n,i){t=t.getData(i&&i.dataType);t&&mg(t,i,"normal")},gg.prototype.remove=function(t,e){this.group.removeAll()},gg.prototype.dispose=function(t,e){},gg.prototype.updateView=function(t,e,n,i){this.render(t,e,n,i)},gg.prototype.updateLayout=function(t,e,n,i){this.render(t,e,n,i)},gg.prototype.updateVisual=function(t,e,n,i){this.render(t,e,n,i)},gg.prototype.eachRendered=function(t){Xh(this.group,t)},gg.markUpdateMethod=function(t,e){pg(t).updateMethod=e},gg.protoInitialize=void(gg.prototype.type="chart"),gg);function gg(){this.group=new Vr,this.uid=wc("viewChart"),this.renderTask=rf({plan:vg,reset:_g}),this.renderTask.context={view:this}}function yg(t,e,n){t&&Fl(t)&&("emphasis"===e?bl:wl)(t,n)}function mg(e,t,n){var i,r=Do(e,t),o=t&&null!=t.highlightKey?(t=t.highlightKey,i=null==(i=Ys[t])&&Xs<=32?Ys[t]=Xs++:i):null;null!=r?O(vo(r),function(t){yg(e.getItemGraphicEl(t),n,o)}):e.eachItemGraphicEl(function(t){yg(t,n,o)})}function vg(t){return dg(t.model)}function _g(t){var e=t.model,n=t.ecModel,i=t.api,r=t.payload,o=e.pipelineContext.progressiveRender,t=t.view,a=r&&pg(r).updateMethod,o=o?"incrementalPrepareRender":a&&t[a]?a:"render";return"render"!==o&&t[o](e,n,i,r),xg[o]}Go(fg),qo(fg);var xg={incrementalPrepareRender:{progress:function(t,e){e.view.incrementalRender(t,e.model,e.ecModel,e.api,e.payload)}},render:{forceFirstProgress:!0,progress:function(t,e){e.view.render(e.model,e.ecModel,e.api,e.payload)}}};function bg(t,r,o){var a,s,l,u,h,c=0,p=0,d=null;function f(){p=(new Date).getTime(),d=null,t.apply(l,u||[])}r=r||0;function e(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];a=(new Date).getTime(),l=this,u=t;var n=h||r,i=h||o;h=null,s=a-(i?c:p)-n,clearTimeout(d),i?d=setTimeout(f,n):0<=s?f():d=setTimeout(f,-s),c=a}return e.clear=function(){d&&(clearTimeout(d),d=null)},e.debounceNextCall=function(t){h=t},e}var wg=Ao(),Sg={itemStyle:Zo(fc,!0),lineStyle:Zo(hc,!0)},Mg={lineStyle:"stroke",itemStyle:"fill"};function Tg(t,e){t=t.visualStyleMapper||Sg[e];return t||(console.warn("Unknown style type '"+e+"'."),Sg.itemStyle)}function Cg(t,e){t=t.visualDrawType||Mg[e];return t||(console.warn("Unknown style type '"+e+"'."),"fill")}var pc={createOnAllSeries:!0,performRawSeries:!0,reset:function(r,t){var e=r.getData(),n=r.visualStyleAccessPath||"itemStyle",i=r.getModel(n),o=Tg(r,n)(i),i=i.getShallow("decal"),a=(i&&(e.setVisual("decal",i),i.dirty=!0),Cg(r,n)),i=o[a],s=D(i)?i:null,n="auto"===o.fill||"auto"===o.stroke;if(o[a]&&!s&&!n||(i=r.getColorFromPalette(r.name,null,t.getSeriesCount()),o[a]||(o[a]=i,e.setVisual("colorFromPalette",!0)),o.fill="auto"===o.fill||D(o.fill)?i:o.fill,o.stroke="auto"===o.stroke||D(o.stroke)?i:o.stroke),e.setVisual("style",o),e.setVisual("drawType",a),!t.isSeriesFiltered(r)&&s)return e.setVisual("colorFromPalette",!1),{dataEach:function(t,e){var n=r.getDataParams(e),i=P({},o);i[a]=s(n),t.setItemVisual(e,"style",i)}}}},kg=new _c,Ko={createOnAllSeries:!0,performRawSeries:!0,reset:function(t,e){var i,r,o;if(!t.ignoreStyleOnData&&!e.isSeriesFiltered(t))return e=t.getData(),i=t.visualStyleAccessPath||"itemStyle",r=Tg(t,i),o=e.getVisual("drawType"),{dataEach:e.hasItemOption?function(t,e){var n=t.getRawDataItem(e);n&&n[i]&&(kg.option=n[i],n=r(kg),P(t.ensureUniqueItemVisual(e,"style"),n),kg.option.decal&&(t.setItemVisual(e,"decal",kg.option.decal),kg.option.decal.dirty=!0),o in n)&&t.setItemVisual(e,"colorFromPalette",!1)}:null}}},yc={performRawSeries:!0,overallReset:function(e){var i=E();e.eachSeries(function(t){var e,n=t.getColorBy();t.isColorBySeries()||(n=t.type+"-"+n,(e=i.get(n))||i.set(n,e={}),wg(t).scope=e)}),e.eachSeries(function(i){var r,o,a,s,t,l;i.isColorBySeries()||e.isSeriesFiltered(i)||(r=i.getRawData(),o={},a=i.getData(),s=wg(i).scope,t=i.visualStyleAccessPath||"itemStyle",l=Cg(i,t),a.each(function(t){var e=a.getRawIndex(t);o[e]=t}),r.each(function(t){var e,n=o[t];a.getItemVisual(n,"colorFromPalette")&&(n=a.ensureUniqueItemVisual(n,"style"),t=r.getName(t)||t+"",e=r.count(),n[l]=i.getColorFromPalette(t,s,e))}))})}},Ig=Math.PI;Ag.prototype.restoreData=function(t,e){t.restoreData(e),this._stageTaskMap.each(function(t){t=t.overallTask;t&&t.dirty()})},Ag.prototype.getPerformArgs=function(t,e){var n,i;if(t.__pipeline)return i=(n=this._pipelineMap.get(t.__pipeline.id)).context,{step:e=!e&&n.progressiveEnabled&&(!i||i.progressiveRender)&&t.__idxInPipeline>n.blockIndex?n.step:null,modBy:null!=(t=i&&i.modDataCount)?Math.ceil(t/e):null,modDataCount:t}},Ag.prototype.getPipeline=function(t){return this._pipelineMap.get(t)},Ag.prototype.updateStreamModes=function(t,e){var n=this._pipelineMap.get(t.uid),i=t.getData().count(),e=n.progressiveEnabled&&e.incrementalPrepareRender&&i>=n.threshold,r=t.get("large")&&i>=t.get("largeThreshold"),i="mod"===t.get("progressiveChunkMode")?i:null;t.pipelineContext=n.context={progressiveRender:e,modDataCount:i,large:r}},Ag.prototype.restorePipelines=function(t){var i=this,r=i._pipelineMap=E();t.eachSeries(function(t){var e=t.getProgressive(),n=t.uid;r.set(n,{id:n,head:null,tail:null,threshold:t.getProgressiveThreshold(),progressiveEnabled:e&&!(t.preventIncremental&&t.preventIncremental()),blockIndex:-1,step:Math.round(e||700),count:0}),i._pipe(t,t.dataTask)})},Ag.prototype.prepareStageTasks=function(){var n=this._stageTaskMap,i=this.api.getModel(),r=this.api;O(this._allHandlers,function(t){var e=n.get(t.uid)||n.set(t.uid,{});Tt(!(t.reset&&t.overallReset),""),t.reset&&this._createSeriesStageTask(t,e,i,r),t.overallReset&&this._createOverallStageTask(t,e,i,r)},this)},Ag.prototype.prepareView=function(t,e,n,i){var r=t.renderTask,o=r.context;o.model=e,o.ecModel=n,o.api=i,r.__block=!t.incrementalPrepareRender,this._pipe(e,r)},Ag.prototype.performDataProcessorTasks=function(t,e){this._performStageTasks(this._dataProcessorHandlers,t,e,{block:!0})},Ag.prototype.performVisualTasks=function(t,e,n){this._performStageTasks(this._visualHandlers,t,e,n)},Ag.prototype._performStageTasks=function(t,s,l,u){u=u||{};var h=!1,c=this;function p(t,e){return t.setDirty&&(!t.dirtyMap||t.dirtyMap.get(e.__pipeline.id))}O(t,function(i,t){var e,n,r,o,a;u.visualType&&u.visualType!==i.visualType||(e=(n=c._stageTaskMap.get(i.uid)).seriesTaskMap,(n=n.overallTask)?((o=n.agentStubMap).each(function(t){p(u,t)&&(t.dirty(),r=!0)}),r&&n.dirty(),c.updatePayload(n,l),a=c.getPerformArgs(n,u.block),o.each(function(t){t.perform(a)}),n.perform(a)&&(h=!0)):e&&e.each(function(t,e){p(u,t)&&t.dirty();var n=c.getPerformArgs(t,u.block);n.skip=!i.performRawSeries&&s.isSeriesFiltered(t.context.model),c.updatePayload(t,l),t.perform(n)&&(h=!0)}))}),this.unfinished=h||this.unfinished},Ag.prototype.performSeriesTasks=function(t){var e;t.eachSeries(function(t){e=t.dataTask.perform()||e}),this.unfinished=e||this.unfinished},Ag.prototype.plan=function(){this._pipelineMap.each(function(t){var e=t.tail;do{if(e.__block){t.blockIndex=e.__idxInPipeline;break}}while(e=e.getUpstream())})},Ag.prototype.updatePayload=function(t,e){"remain"!==e&&(t.context.payload=e)},Ag.prototype._createSeriesStageTask=function(n,t,i,r){var o=this,a=t.seriesTaskMap,s=t.seriesTaskMap=E(),t=n.seriesType,e=n.getTargetSeries;function l(t){var e=t.uid,e=s.set(e,a&&a.get(e)||rf({plan:Ng,reset:Eg,count:Fg}));e.context={model:t,ecModel:i,api:r,useClearVisual:n.isVisual&&!n.isLayout,plan:n.plan,reset:n.reset,scheduler:o},o._pipe(t,e)}n.createOnAllSeries?i.eachRawSeries(l):t?i.eachRawSeriesByType(t,l):e&&e(i,r).each(l)},Ag.prototype._createOverallStageTask=function(t,e,n,i){var r=this,o=e.overallTask=e.overallTask||rf({reset:Lg}),a=(o.context={ecModel:n,api:i,overallReset:t.overallReset,scheduler:r},o.agentStubMap),s=o.agentStubMap=E(),e=t.seriesType,l=t.getTargetSeries,u=!0,h=!1;function c(t){var e=t.uid,e=s.set(e,a&&a.get(e)||(h=!0,rf({reset:Pg,onDirty:Rg})));e.context={model:t,overallProgress:u},e.agent=o,e.__block=u,r._pipe(t,e)}Tt(!t.createOnAllSeries,""),e?n.eachRawSeriesByType(e,c):l?l(n,i).each(c):(u=!1,O(n.getSeries(),c)),h&&o.dirty()},Ag.prototype._pipe=function(t,e){t=t.uid,t=this._pipelineMap.get(t);t.head||(t.head=e),t.tail&&t.tail.pipe(e),(t.tail=e).__idxInPipeline=t.count++,e.__pipeline=t},Ag.wrapStageHandler=function(t,e){return(t=D(t)?{overallReset:t,seriesType:function(t){Vg=null;try{t(Hg,Gg)}catch(t){}return Vg}(t)}:t).uid=wc("stageHandler"),e&&(t.visualType=e),t};var Dg=Ag;function Ag(t,e,n,i){this._stageTaskMap=E(),this.ecInstance=t,this.api=e,n=this._dataProcessorHandlers=n.slice(),i=this._visualHandlers=i.slice(),this._allHandlers=n.concat(i)}function Lg(t){t.overallReset(t.ecModel,t.api,t.payload)}function Pg(t){return t.overallProgress&&Og}function Og(){this.agent.dirty(),this.getDownstream().dirty()}function Rg(){this.agent&&this.agent.dirty()}function Ng(t){return t.plan?t.plan(t.model,t.ecModel,t.api,t.payload):null}function Eg(t){t.useClearVisual&&t.data.clearAllVisual();t=t.resetDefines=vo(t.reset(t.model,t.ecModel,t.api,t.payload));return 1<t.length?B(t,function(t,e){return Bg(e)}):zg}var zg=Bg(0);function Bg(o){return function(t,e){var n=e.data,i=e.resetDefines[o];if(i&&i.dataEach)for(var r=t.start;r<t.end;r++)i.dataEach(n,r);else i&&i.progress&&i.progress(t,n)}}function Fg(t){return t.data.count()}var Vg,Hg={},Gg={};function Wg(t,e){for(var n in e.prototype)t[n]=Ft}Wg(Hg,jp),Wg(Gg,Jp),Hg.eachSeriesByType=Hg.eachRawSeriesByType=function(t){Vg=t},Hg.eachComponent=function(t){"series"===t.mainType&&t.subType&&(Vg=t.subType)};function Ug(){return{axisLine:{lineStyle:{color:Xg}},splitLine:{lineStyle:{color:"#484753"}},splitArea:{areaStyle:{color:["rgba(255,255,255,0.02)","rgba(255,255,255,0.05)"]}},minorSplitLine:{lineStyle:{color:"#20203B"}}}}var lc=["#37A2DA","#32C5E9","#67E0E3","#9FE6B8","#FFDB5C","#ff9f7f","#fb7293","#E062AE","#E690D1","#e7bcf3","#9d96f5","#8378EA","#96BFFF"],Nc={color:lc,colorLayer:[["#37A2DA","#ffd85c","#fd7b5f"],["#37A2DA","#67E0E3","#FFDB5C","#ff9f7f","#E062AE","#9d96f5"],["#37A2DA","#32C5E9","#9FE6B8","#FFDB5C","#ff9f7f","#fb7293","#e7bcf3","#8378EA","#96BFFF"],lc]},Xg="#B9B8CE",fc="#100C2A",hc=["#4992ff","#7cffb2","#fddd60","#ff6e76","#58d9f9","#05c091","#ff8a45","#8d48e3","#dd79ff"],lc={darkMode:!0,color:hc,backgroundColor:fc,axisPointer:{lineStyle:{color:"#817f91"},crossStyle:{color:"#817f91"},label:{color:"#fff"}},legend:{textStyle:{color:Xg}},textStyle:{color:Xg},title:{textStyle:{color:"#EEF1FA"},subtextStyle:{color:"#B9B8CE"}},toolbox:{iconStyle:{borderColor:Xg}},dataZoom:{borderColor:"#71708A",textStyle:{color:Xg},brushStyle:{color:"rgba(135,163,206,0.3)"},handleStyle:{color:"#353450",borderColor:"#C5CBE3"},moveHandleStyle:{color:"#B0B6C3",opacity:.3},fillerColor:"rgba(135,163,206,0.2)",emphasis:{handleStyle:{borderColor:"#91B7F2",color:"#4D587D"},moveHandleStyle:{color:"#636D9A",opacity:.7}},dataBackground:{lineStyle:{color:"#71708A",width:1},areaStyle:{color:"#71708A"}},selectedDataBackground:{lineStyle:{color:"#87A3CE"},areaStyle:{color:"#87A3CE"}}},visualMap:{textStyle:{color:Xg}},timeline:{lineStyle:{color:Xg},label:{color:Xg},controlStyle:{color:Xg,borderColor:Xg}},calendar:{itemStyle:{color:fc},dayLabel:{color:Xg},monthLabel:{color:Xg},yearLabel:{color:Xg}},timeAxis:Ug(),logAxis:Ug(),valueAxis:Ug(),categoryAxis:Ug(),line:{symbol:"circle"},graph:{color:hc},gauge:{title:{color:Xg},axisLine:{lineStyle:{color:[[1,"rgba(207,212,219,0.2)"]]}},axisLabel:{color:Xg},detail:{color:"#EEF1FA"}},candlestick:{itemStyle:{color:"#f64e56",color0:"#54ea92",borderColor:"#f64e56",borderColor0:"#54ea92"}}},Yg=(lc.categoryAxis.splitLine.show=!1,qg.prototype.normalizeQuery=function(t){var e,a,s,l={},u={},h={};return V(t)?(e=Ho(t),l.mainType=e.main||null,l.subType=e.sub||null):(a=["Index","Name","Id"],s={name:1,dataIndex:1,dataType:1},O(t,function(t,e){for(var n=!1,i=0;i<a.length;i++){var r=a[i],o=e.lastIndexOf(r);0<o&&o===e.length-r.length&&"data"!==(o=e.slice(0,o))&&(l.mainType=o,l[r.toLowerCase()]=t,n=!0)}s.hasOwnProperty(e)&&(u[e]=t,n=!0),n||(h[e]=t)})),{cptQuery:l,dataQuery:u,otherQuery:h}},qg.prototype.filter=function(t,e){var n,i,r,o,a,s=this.eventInfo;return!s||(n=s.targetEl,i=s.packedEvent,r=s.model,s=s.view,!r)||!s||(o=e.cptQuery,a=e.dataQuery,l(o,r,"mainType")&&l(o,r,"subType")&&l(o,r,"index","componentIndex")&&l(o,r,"name")&&l(o,r,"id")&&l(a,i,"name")&&l(a,i,"dataIndex")&&l(a,i,"dataType")&&(!s.filterForExposedEvent||s.filterForExposedEvent(t,e.otherQuery,n,i)));function l(t,e,n,i){return null==t[n]||e[i||n]===t[n]}},qg.prototype.afterTrigger=function(){this.eventInfo=null},qg);function qg(){}var Zg=["symbol","symbolSize","symbolRotate","symbolOffset"],jg=Zg.concat(["symbolKeepAspect"]),fc={createOnAllSeries:!0,performRawSeries:!0,reset:function(a,t){var e=a.getData();if(a.legendIcon&&e.setVisual("legendIcon",a.legendIcon),a.hasSymbolVisual){for(var s,n={},l={},i=!1,r=0;r<Zg.length;r++){var o=Zg[r],u=a.get(o);D(u)?(i=!0,l[o]=u):n[o]=u}if(n.symbol=n.symbol||a.defaultSymbol,e.setVisual(P({legendIcon:a.legendIcon||n.symbol,symbolKeepAspect:a.get("symbolKeepAspect")},n)),!t.isSeriesFiltered(a))return s=I(l),{dataEach:i?function(t,e){for(var n=a.getRawValue(e),i=a.getDataParams(e),r=0;r<s.length;r++){var o=s[r];t.setItemVisual(e,o,l[o](n,i))}}:null}}}},hc={createOnAllSeries:!0,performRawSeries:!0,reset:function(t,e){if(t.hasSymbolVisual&&!e.isSeriesFiltered(t))return{dataEach:t.getData().hasItemOption?function(t,e){for(var n=t.getItemModel(e),i=0;i<jg.length;i++){var r=jg[i],o=n.getShallow(r,!0);null!=o&&t.setItemVisual(e,r,o)}}:null}}};function Kg(t,e){switch(e){case"color":return t.getVisual("style")[t.getVisual("drawType")];case"opacity":return t.getVisual("style").opacity;case"symbol":case"symbolSize":case"liftZ":return t.getVisual(e)}}function $g(t,e,s,n,l){var u=t+e;s.isSilent(u)||n.eachComponent({mainType:"series",subType:"pie"},function(t){for(var e,n,i=t.seriesIndex,r=t.option.selectedMap,o=l.selected,a=0;a<o.length;a++)o[a].seriesIndex===i&&(n=Do(e=t.getData(),l.fromActionPayload),s.trigger(u,{type:u,seriesId:t.id,name:F(n)?e.getName(n[0]):e.getName(n),selected:V(r)?r:P({},r)}))})}function Qg(t,e,n){for(var i;t&&(!e(t)||(i=t,!n));)t=t.__hostTarget||t.parent;return i}var Jg=Math.round(9*Math.random()),ty="function"==typeof Object.defineProperty,ey=(ny.prototype.get=function(t){return this._guard(t)[this._id]},ny.prototype.set=function(t,e){t=this._guard(t);return ty?Object.defineProperty(t,this._id,{value:e,enumerable:!1,configurable:!0}):t[this._id]=e,this},ny.prototype.delete=function(t){return!!this.has(t)&&(delete this._guard(t)[this._id],!0)},ny.prototype.has=function(t){return!!this._guard(t)[this._id]},ny.prototype._guard=function(t){if(t!==Object(t))throw TypeError("Value of WeakMap is not a non-null object.");return t},ny);function ny(){this._id="__ec_inner_"+Jg++}var iy=j.extend({type:"triangle",shape:{cx:0,cy:0,width:0,height:0},buildPath:function(t,e){var n=e.cx,i=e.cy,r=e.width/2,e=e.height/2;t.moveTo(n,i-e),t.lineTo(n+r,i+e),t.lineTo(n-r,i+e),t.closePath()}}),ry=j.extend({type:"diamond",shape:{cx:0,cy:0,width:0,height:0},buildPath:function(t,e){var n=e.cx,i=e.cy,r=e.width/2,e=e.height/2;t.moveTo(n,i-e),t.lineTo(n+r,i),t.lineTo(n,i+e),t.lineTo(n-r,i),t.closePath()}}),oy=j.extend({type:"pin",shape:{x:0,y:0,width:0,height:0},buildPath:function(t,e){var n=e.x,i=e.y,r=e.width/5*3,e=Math.max(r,e.height),r=r/2,o=r*r/(e-r),e=i-e+r+o,a=Math.asin(o/r),s=Math.cos(a)*r,l=Math.sin(a),u=Math.cos(a),h=.6*r,c=.7*r;t.moveTo(n-s,e+o),t.arc(n,e,r,Math.PI-a,2*Math.PI+a),t.bezierCurveTo(n+s-l*h,e+o+u*h,n,i-c,n,i),t.bezierCurveTo(n,i-c,n-s+l*h,e+o+u*h,n-s,e+o),t.closePath()}}),ay=j.extend({type:"arrow",shape:{x:0,y:0,width:0,height:0},buildPath:function(t,e){var n=e.height,i=e.width,r=e.x,e=e.y,i=i/3*2;t.moveTo(r,e),t.lineTo(r+i,e+n),t.lineTo(r,e+n/4*3),t.lineTo(r-i,e+n),t.lineTo(r,e),t.closePath()}}),sy={line:function(t,e,n,i,r){r.x1=t,r.y1=e+i/2,r.x2=t+n,r.y2=e+i/2},rect:function(t,e,n,i,r){r.x=t,r.y=e,r.width=n,r.height=i},roundRect:function(t,e,n,i,r){r.x=t,r.y=e,r.width=n,r.height=i,r.r=Math.min(n,i)/4},square:function(t,e,n,i,r){n=Math.min(n,i);r.x=t,r.y=e,r.width=n,r.height=n},circle:function(t,e,n,i,r){r.cx=t+n/2,r.cy=e+i/2,r.r=Math.min(n,i)/2},diamond:function(t,e,n,i,r){r.cx=t+n/2,r.cy=e+i/2,r.width=n,r.height=i},pin:function(t,e,n,i,r){r.x=t+n/2,r.y=e+i/2,r.width=n,r.height=i},arrow:function(t,e,n,i,r){r.x=t+n/2,r.y=e+i/2,r.width=n,r.height=i},triangle:function(t,e,n,i,r){r.cx=t+n/2,r.cy=e+i/2,r.width=n,r.height=i}},ly={},uy=(O({line:Zu,rect:ks,roundRect:ks,square:ks,circle:uu,diamond:ry,pin:oy,arrow:ay,triangle:iy},function(t,e){ly[e]=new t}),j.extend({type:"symbol",shape:{symbolType:"",x:0,y:0,width:0,height:0},calculateTextPosition:function(t,e,n){var t=kr(t,e,n),i=this.shape;return i&&"pin"===i.symbolType&&"inside"===e.position&&(t.y=n.y+.4*n.height),t},buildPath:function(t,e,n){var i,r=e.symbolType;"none"!==r&&(i=(i=ly[r])||ly[r="rect"],sy[r](e.x,e.y,e.width,e.height,i.shape),i.buildPath(t,i.shape,n))}}));function hy(t,e){var n;"image"!==this.type&&(n=this.style,this.__isEmptyBrush?(n.stroke=t,n.fill=e||"#fff",n.lineWidth=2):"line"===this.shape.symbolType?n.stroke=t:n.fill=t,this.markRedraw())}function cy(t,e,n,i,r,o,a){var s=0===t.indexOf("empty");return(a=0===(t=s?t.substr(5,1).toLowerCase()+t.substr(6):t).indexOf("image://")?zh(t.slice(8),new X(e,n,i,r),a?"center":"cover"):0===t.indexOf("path://")?Eh(t.slice(7),{},new X(e,n,i,r),a?"center":"cover"):new uy({shape:{symbolType:t,x:e,y:n,width:i,height:r}})).__isEmptyBrush=s,a.setColor=hy,o&&a.setColor(o),a}function py(t){return[(t=F(t)?t:[+t,+t])[0]||0,t[1]||0]}function dy(t,e){if(null!=t)return[Jr((t=F(t)?t:[t,t])[0],e[0])||0,Jr(N(t[1],t[0]),e[1])||0]}function fy(t){return isFinite(t)}function gy(t,e,n){for(var i,r,o,a,s,l,u,h,c,p="radial"===e.type?(i=t,r=e,a=(o=n).width,s=o.height,l=Math.min(a,s),u=null==r.x?.5:r.x,h=null==r.y?.5:r.y,c=null==r.r?.5:r.r,r.global||(u=u*a+o.x,h=h*s+o.y,c*=l),u=fy(u)?u:.5,h=fy(h)?h:.5,c=0<=c&&fy(c)?c:.5,i.createRadialGradient(u,h,0,u,h,c)):(r=t,a=n,o=null==(s=e).x?0:s.x,l=null==s.x2?1:s.x2,i=null==s.y?0:s.y,u=null==s.y2?0:s.y2,s.global||(o=o*a.width+a.x,l=l*a.width+a.x,i=i*a.height+a.y,u=u*a.height+a.y),o=fy(o)?o:0,l=fy(l)?l:1,i=fy(i)?i:0,u=fy(u)?u:0,r.createLinearGradient(o,i,l,u)),d=e.colorStops,f=0;f<d.length;f++)p.addColorStop(d[f].offset,d[f].color);return p}function yy(t){return parseInt(t,10)}function my(t,e,n){var i=["width","height"][e],r=["clientWidth","clientHeight"][e],o=["paddingLeft","paddingTop"][e],e=["paddingRight","paddingBottom"][e];return null!=n[i]&&"auto"!==n[i]?parseFloat(n[i]):(n=document.defaultView.getComputedStyle(t),(t[r]||yy(n[i])||yy(t.style[i]))-(yy(n[o])||0)-(yy(n[e])||0)|0)}function vy(t){var e,n=t.style,i=n.lineDash&&0<n.lineWidth&&(r=n.lineDash,i=n.lineWidth,r&&"solid"!==r&&0<i?"dashed"===r?[4*i,2*i]:"dotted"===r?[i]:dt(r)?[r]:F(r)?r:null:null),r=n.lineDashOffset;return i&&(e=n.strokeNoScale&&t.getLineScale?t.getLineScale():1)&&1!==e&&(i=B(i,function(t){return t/e}),r/=e),[i,r]}var _y=new Za(!0);function xy(t){var e=t.stroke;return!(null==e||"none"===e||!(0<t.lineWidth))}function by(t){return"string"==typeof t&&"none"!==t}function wy(t){t=t.fill;return null!=t&&"none"!==t}function Sy(t,e){var n;null!=e.fillOpacity&&1!==e.fillOpacity?(n=t.globalAlpha,t.globalAlpha=e.fillOpacity*e.opacity,t.fill(),t.globalAlpha=n):t.fill()}function My(t,e){var n;null!=e.strokeOpacity&&1!==e.strokeOpacity?(n=t.globalAlpha,t.globalAlpha=e.strokeOpacity*e.opacity,t.stroke(),t.globalAlpha=n):t.stroke()}function Ty(t,e,n){var n=Jo(e.image,e.__image,n);if(ea(n))return t=t.createPattern(n,e.repeat||"repeat"),"function"==typeof DOMMatrix&&t&&t.setTransform&&((n=new DOMMatrix).translateSelf(e.x||0,e.y||0),n.rotateSelf(0,0,(e.rotation||0)*Vt),n.scaleSelf(e.scaleX||1,e.scaleY||1),t.setTransform(n)),t}var Cy=["shadowBlur","shadowOffsetX","shadowOffsetY"],ky=[["lineCap","butt"],["lineJoin","miter"],["miterLimit",10]];function Iy(t,e,n,i,r){var o,a=!1;if(!i&&e===(n=n||{}))return!1;!i&&e.opacity===n.opacity||(Ny(t,r),a=!0,o=Math.max(Math.min(e.opacity,1),0),t.globalAlpha=isNaN(o)?ga.opacity:o),!i&&e.blend===n.blend||(a||(Ny(t,r),a=!0),t.globalCompositeOperation=e.blend||ga.blend);for(var s=0;s<Cy.length;s++){var l=Cy[s];!i&&e[l]===n[l]||(a||(Ny(t,r),a=!0),t[l]=t.dpr*(e[l]||0))}return!i&&e.shadowColor===n.shadowColor||(a||(Ny(t,r),a=!0),t.shadowColor=e.shadowColor||ga.shadowColor),a}function Dy(t,e,n,i,r){var o=Ey(e,r.inHover),a=i?null:n&&Ey(n,r.inHover)||{};if(o!==a){var s=Iy(t,o,a,i,r);(i||o.fill!==a.fill)&&(s||(Ny(t,r),s=!0),by(o.fill))&&(t.fillStyle=o.fill),(i||o.stroke!==a.stroke)&&(s||(Ny(t,r),s=!0),by(o.stroke))&&(t.strokeStyle=o.stroke),!i&&o.opacity===a.opacity||(s||(Ny(t,r),s=!0),t.globalAlpha=null==o.opacity?1:o.opacity),e.hasStroke()&&(n=o.lineWidth/(o.strokeNoScale&&e.getLineScale?e.getLineScale():1),t.lineWidth!==n)&&(s||(Ny(t,r),s=!0),t.lineWidth=n);for(var l=0;l<ky.length;l++){var u=ky[l],h=u[0];!i&&o[h]===a[h]||(s||(Ny(t,r),s=!0),t[h]=o[h]||u[1])}}}function Ay(t,e){var e=e.transform,n=t.dpr||1;e?t.setTransform(n*e[0],n*e[1],n*e[2],n*e[3],n*e[4],n*e[5]):t.setTransform(n,0,0,n,0,0)}var Ly=1,Py=2,Oy=3,Ry=4;function Ny(t,e){e.batchFill&&t.fill(),e.batchStroke&&t.stroke(),e.batchFill="",e.batchStroke=""}function Ey(t,e){return e&&t.__hoverStyle||t.style}function zy(t,e){By(t,e,{inHover:!1,viewWidth:0,viewHeight:0},!0)}function By(t,e,n,E){var i=e.transform;if(e.shouldBePainted(n.viewWidth,n.viewHeight,!1,!1)){var r=e.__clipPaths,o=n.prevElClipPaths,a=!1,s=!1;if(!o||function(t,e){if(t!==e&&(t||e)){if(!t||!e||t.length!==e.length)return 1;for(var n=0;n<t.length;n++)if(t[n]!==e[n])return 1}}(r,o)){if(o&&o.length&&(Ny(t,n),t.restore(),s=a=!0,n.prevElClipPaths=null,n.allClipped=!1,n.prevEl=null),r&&r.length){Ny(t,n),t.save();for(var z=r,l=t,o=n,B=!1,F=0;F<z.length;F++){var V=z[F],B=B||V.isZeroArea();Ay(l,V),l.beginPath(),V.buildPath(l,V.shape),l.clip()}o.allClipped=B,a=!0}n.prevElClipPaths=r}if(n.allClipped)e.__isRendered=!1;else{e.beforeBrush&&e.beforeBrush(),e.innerBeforeBrush();var u,h,c,p,d,f,g,y,m,v,_,x,b,H,w,S,M,T,C,k,I,D,A,o=n.prevEl,L=(o||(s=a=!0),e instanceof j&&e.autoBatch&&(r=e.style,L=wy(r),u=xy(r),!(r.lineDash||!(+L^+u)||L&&"string"!=typeof r.fill||u&&"string"!=typeof r.stroke||r.strokePercent<1||r.strokeOpacity<1||r.fillOpacity<1))),a=(a||(u=i,r=o.transform,u&&r?u[0]!==r[0]||u[1]!==r[1]||u[2]!==r[2]||u[3]!==r[3]||u[4]!==r[4]||u[5]!==r[5]:u||r)?(Ny(t,n),Ay(t,e)):L||Ny(t,n),Ey(e,n.inHover));if(e instanceof j)n.lastDrawType!==Ly&&(s=!0,n.lastDrawType=Ly),Dy(t,e,o,s,n),L&&(n.batchFill||n.batchStroke)||t.beginPath(),i=t,r=e,R=L,w=xy(p=a),S=wy(p),M=p.strokePercent,T=M<1,C=!r.path,r.silent&&!T||!C||r.createPathProxy(),k=r.path||_y,I=r.__dirty,R||(d=p.fill,A=p.stroke,f=S&&!!d.colorStops,g=w&&!!A.colorStops,y=S&&!!d.image,m=w&&!!A.image,D=b=x=_=v=void 0,(f||g)&&(D=r.getBoundingRect()),f&&(v=I?gy(i,d,D):r.__canvasFillGradient,r.__canvasFillGradient=v),g&&(_=I?gy(i,A,D):r.__canvasStrokeGradient,r.__canvasStrokeGradient=_),y&&(x=I||!r.__canvasFillPattern?Ty(i,d,r):r.__canvasFillPattern,r.__canvasFillPattern=x),m&&(b=I||!r.__canvasStrokePattern?Ty(i,A,r):r.__canvasStrokePattern,r.__canvasStrokePattern=x),f?i.fillStyle=v:y&&(x?i.fillStyle=x:S=!1),g?i.strokeStyle=_:m&&(b?i.strokeStyle=b:w=!1)),D=r.getGlobalScale(),k.setScale(D[0],D[1],r.segmentIgnoreThreshold),i.setLineDash&&p.lineDash&&(H=(d=vy(r))[0],O=d[1]),A=!0,(C||I&vn)&&(k.setDPR(i.dpr),T?k.setContext(null):(k.setContext(i),A=!1),k.reset(),r.buildPath(k,r.shape,R),k.toStatic(),r.pathUpdated()),A&&k.rebuildPath(i,T?M:1),H&&(i.setLineDash(H),i.lineDashOffset=O),R||(p.strokeFirst?(w&&My(i,p),S&&Sy(i,p)):(S&&Sy(i,p),w&&My(i,p))),H&&i.setLineDash([]),L&&(n.batchFill=a.fill||"",n.batchStroke=a.stroke||"");else if(e instanceof fs)n.lastDrawType!==Oy&&(s=!0,n.lastDrawType=Oy),Dy(t,e,o,s,n),f=t,v=e,null!=(x=(y=a).text)&&(x+=""),x&&(f.font=y.font||K,f.textAlign=y.textAlign,f.textBaseline=y.textBaseline,_=g=void 0,f.setLineDash&&y.lineDash&&(g=(v=vy(v))[0],_=v[1]),g&&(f.setLineDash(g),f.lineDashOffset=_),y.strokeFirst?(xy(y)&&f.strokeText(x,y.x,y.y),wy(y)&&f.fillText(x,y.x,y.y)):(wy(y)&&f.fillText(x,y.x,y.y),xy(y)&&f.strokeText(x,y.x,y.y)),g)&&f.setLineDash([]);else if(e instanceof _s)n.lastDrawType!==Py&&(s=!0,n.lastDrawType=Py),m=o,b=s,Iy(t,Ey(e,(D=n).inHover),m&&Ey(m,D.inHover),b,D),d=t,C=a,(r=(I=e).__image=Jo(C.image,I.__image,I,I.onload))&&ea(r)&&(A=C.x||0,k=C.y||0,T=I.getWidth(),I=I.getHeight(),M=r.width/r.height,null==T&&null!=I?T=I*M:null==I&&null!=T?I=T/M:null==T&&null==I&&(T=r.width,I=r.height),C.sWidth&&C.sHeight?(h=C.sx||0,c=C.sy||0,d.drawImage(r,h,c,C.sWidth,C.sHeight,A,k,T,I)):C.sx&&C.sy?(h=C.sx,c=C.sy,d.drawImage(r,h,c,T-h,I-c,A,k,T,I)):d.drawImage(r,A,k,T,I));else if(e.getTemporalDisplayables){n.lastDrawType!==Ry&&(s=!0,n.lastDrawType=Ry);var P,G,W=t,O=e,R=n,U=O.getDisplayables(),X=O.getTemporalDisplayables(),Y=(W.save(),{prevElClipPaths:null,prevEl:null,allClipped:!1,viewWidth:R.viewWidth,viewHeight:R.viewHeight,inHover:R.inHover});for(P=O.getCursor(),G=U.length;P<G;P++)(N=U[P]).beforeBrush&&N.beforeBrush(),N.innerBeforeBrush(),By(W,N,Y,P===G-1),N.innerAfterBrush(),N.afterBrush&&N.afterBrush(),Y.prevEl=N;for(var N,q=0,Z=X.length;q<Z;q++)(N=X[q]).beforeBrush&&N.beforeBrush(),N.innerBeforeBrush(),By(W,N,Y,q===Z-1),N.innerAfterBrush(),N.afterBrush&&N.afterBrush(),Y.prevEl=N;O.clearTemporalDisplayables(),O.notClear=!0,W.restore()}L&&E&&Ny(t,n),e.innerAfterBrush(),e.afterBrush&&e.afterBrush(),(n.prevEl=e).__dirty=0,e.__isRendered=!0}}else e.__dirty&=~mn,e.__isRendered=!1}var Fy=new ey,Vy=new ti(100),Hy=["symbol","symbolSize","symbolKeepAspect","color","backgroundColor","dashArrayX","dashArrayY","maxTileWidth","maxTileHeight"];function Gy(t,e){if("none"===t)return null;var a=e.getDevicePixelRatio(),s=e.getZr(),l="svg"===s.painter.type,e=(t.dirty&&Fy.delete(t),Fy.get(t));if(e)return e;for(var n,u=z(t,{symbol:"rect",symbolSize:1,symbolKeepAspect:!0,color:"rgba(0, 0, 0, 0.2)",backgroundColor:null,dashArrayX:5,dashArrayY:5,rotation:0,maxTileWidth:512,maxTileHeight:512}),e=("none"===u.backgroundColor&&(u.backgroundColor=null),{repeat:"repeat"}),i=e,r=[a],o=!0,h=0;h<Hy.length;++h){var c=u[Hy[h]];if(null!=c&&!F(c)&&!V(c)&&!dt(c)&&"boolean"!=typeof c){o=!1;break}r.push(c)}o&&(n=r.join(",")+(l?"-svg":""),v=Vy.get(n))&&(l?i.svgElement=v:i.image=v);var p,d=function t(e){if(!e||0===e.length)return[[0,0]];if(dt(e))return[[o=Math.ceil(e),o]];var n=!0;for(var i=0;i<e.length;++i)if(!dt(e[i])){n=!1;break}if(n)return t([e]);var r=[];for(i=0;i<e.length;++i){var o;dt(e[i])?(o=Math.ceil(e[i]),r.push([o,o])):(o=B(e[i],function(t){return Math.ceil(t)})).length%2==1?r.push(o.concat(o)):r.push(o)}return r}(u.dashArrayX),f=function(t){if(!t||"object"==typeof t&&0===t.length)return[0,0];if(dt(t))return[e=Math.ceil(t),e];var e=B(t,function(t){return Math.ceil(t)});return t.length%2?e.concat(e):e}(u.dashArrayY),g=function t(e){if(!e||0===e.length)return[["rect"]];if(V(e))return[[e]];var n=!0;for(var i=0;i<e.length;++i)if(!V(e[i])){n=!1;break}if(n)return t([e]);var r=[];for(i=0;i<e.length;++i)V(e[i])?r.push([e[i]]):r.push(e[i]);return r}(u.symbol),y=function(t){return B(t,Wy)}(d),m=Wy(f),v=!l&&H.createCanvas(),_=l&&{tag:"g",attrs:{},key:"dcl",children:[]},x=function(){for(var t=1,e=0,n=y.length;e<n;++e)t=go(t,y[e]);for(var i=1,e=0,n=g.length;e<n;++e)i=go(i,g[e].length);t*=i;var r=m*y.length*g.length;return{width:Math.max(1,Math.min(t,u.maxTileWidth)),height:Math.max(1,Math.min(r,u.maxTileHeight))}}();v&&(v.width=x.width*a,v.height=x.height*a,p=v.getContext("2d")),p&&(p.clearRect(0,0,v.width,v.height),u.backgroundColor)&&(p.fillStyle=u.backgroundColor,p.fillRect(0,0,v.width,v.height));for(var b=0,w=0;w<f.length;++w)b+=f[w];if(!(b<=0))for(var S=-m,M=0,T=0,C=0;S<x.height;){if(M%2==0){for(var k=T/2%g.length,I=0,D=0,A=0;I<2*x.width;){for(var L,P,O,R,N,E=0,w=0;w<d[C].length;++w)E+=d[C][w];if(E<=0)break;D%2==0&&(P=.5*(1-u.symbolSize),L=I+d[C][D]*P,P=S+f[M]*P,O=d[C][D]*u.symbolSize,R=f[M]*u.symbolSize,N=A/2%g[k].length,function(t,e,n,i,r){var o=l?1:a,r=cy(r,t*o,e*o,n*o,i*o,u.color,u.symbolKeepAspect);l?(t=s.painter.renderOneToVNode(r))&&_.children.push(t):zy(p,r)}(L,P,O,R,g[k][N])),I+=d[C][D],++A,++D===d[C].length&&(D=0)}++C===d.length&&(C=0)}S+=f[M],++T,++M===f.length&&(M=0)}return o&&Vy.put(n,v||_),i.image=v,i.svgElement=_,i.svgWidth=x.width,i.svgHeight=x.height,e.rotation=u.rotation,e.scaleX=e.scaleY=l?1:1/a,Fy.set(t,e),t.dirty=!1,e}function Wy(t){for(var e=0,n=0;n<t.length;++n)e+=t[n];return t.length%2==1?2*e:e}var Uy=new le,Xy={};var ry={PROCESSOR:{FILTER:1e3,SERIES_FILTER:800,STATISTIC:5e3},VISUAL:{LAYOUT:1e3,PROGRESSIVE_LAYOUT:1100,GLOBAL:2e3,CHART:3e3,POST_CHART_LAYOUT:4600,COMPONENT:4e3,BRUSH:5e3,CHART_ITEM:4500,ARIA:6e3,DECAL:7e3}},Yy="__flagInMainProcess",qy="__pendingUpdate",Zy="__needsUpdateStatus",jy=/^[a-zA-Z0-9_]+$/,Ky="__connectUpdateStatus";function $y(n){return function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];if(!this.isDisposed())return Jy(this,n,t);this.id}}function Qy(n){return function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];return Jy(this,n,t)}}function Jy(t,e,n){return n[0]=n[0]&&n[0].toLowerCase(),le.prototype[e].apply(t,n)}u(nm,tm=le);var tm,em=nm;function nm(){return null!==tm&&tm.apply(this,arguments)||this}var im,rm,om,am,sm,lm,um,hm,cm,pm,dm,fm,gm,ym,mm,vm,_m,xm,bm,oy=em.prototype,wm=(oy.on=Qy("on"),oy.off=Qy("off"),u(m,bm=le),m.prototype._onframe=function(){if(!this._disposed){xm(this);var t=this._scheduler;if(this[qy]){var e=this[qy].silent;this[Yy]=!0;try{im(this),am.update.call(this,null,this[qy].updateParams)}catch(t){throw this[Yy]=!1,this[qy]=null,t}this._zr.flush(),this[Yy]=!1,this[qy]=null,hm.call(this,e),cm.call(this,e)}else if(t.unfinished){var n=1,i=this._model,r=this._api;t.unfinished=!1;do{var o=+new Date}while(t.performSeriesTasks(i),t.performDataProcessorTasks(i),lm(this,i),t.performVisualTasks(i),ym(this,this._model,r,"remain",{}),0<(n-=+new Date-o)&&t.unfinished);t.unfinished||this._zr.flush()}}},m.prototype.getDom=function(){return this._dom},m.prototype.getId=function(){return this.id},m.prototype.getZr=function(){return this._zr},m.prototype.isSSR=function(){return this._ssr},m.prototype.setOption=function(t,e,n){if(!this[Yy])if(this._disposed)this.id;else{R(e)&&(n=e.lazyUpdate,i=e.silent,r=e.replaceMerge,o=e.transition,e=e.notMerge),this[Yy]=!0,this._model&&!e||(e=new od(this._api),a=this._theme,(s=this._model=new jp).scheduler=this._scheduler,s.ssr=this._ssr,s.init(null,null,null,a,this._locale,e)),this._model.setOption(t,{replaceMerge:r},Pm);var i,r,o,a,s={seriesTransition:o,optionChanged:!0};if(n)this[qy]={silent:i,updateParams:s},this[Yy]=!1,this.getZr().wakeUp();else{try{im(this),am.update.call(this,null,s)}catch(t){throw this[qy]=null,this[Yy]=!1,t}this._ssr||this._zr.flush(),this[qy]=null,this[Yy]=!1,hm.call(this,i),cm.call(this,i)}}},m.prototype.setTheme=function(){},m.prototype.getModel=function(){return this._model},m.prototype.getOption=function(){return this._model&&this._model.getOption()},m.prototype.getWidth=function(){return this._zr.getWidth()},m.prototype.getHeight=function(){return this._zr.getHeight()},m.prototype.getDevicePixelRatio=function(){return this._zr.painter.dpr||p.hasGlobalWindow&&window.devicePixelRatio||1},m.prototype.getRenderedCanvas=function(t){return this.renderToCanvas(t)},m.prototype.renderToCanvas=function(t){return this._zr.painter.getRenderedCanvas({backgroundColor:(t=t||{}).backgroundColor||this._model.get("backgroundColor"),pixelRatio:t.pixelRatio||this.getDevicePixelRatio()})},m.prototype.renderToSVGString=function(t){return this._zr.painter.renderToString({useViewBox:(t=t||{}).useViewBox})},m.prototype.getSvgDataURL=function(){var t;if(p.svgSupported)return O((t=this._zr).storage.getDisplayList(),function(t){t.stopAnimation(null,!0)}),t.painter.toDataURL()},m.prototype.getDataURL=function(t){var e,n,i,r;if(!this._disposed)return r=(t=t||{}).excludeComponents,e=this._model,n=[],i=this,O(r,function(t){e.eachComponent({mainType:t},function(t){t=i._componentsMap[t.__viewId];t.group.ignore||(n.push(t),t.group.ignore=!0)})}),r="svg"===this._zr.painter.getType()?this.getSvgDataURL():this.renderToCanvas(t).toDataURL("image/"+(t&&t.type||"png")),O(n,function(t){t.group.ignore=!1}),r;this.id},m.prototype.getConnectedDataURL=function(i){var r,o,a,s,l,u,h,c,p,e,t,n,d,f,g;if(!this._disposed)return r="svg"===i.type,o=this.group,a=Math.min,s=Math.max,zm[o]?(u=l=1/0,c=h=-1/0,p=[],e=i&&i.pixelRatio||this.getDevicePixelRatio(),O(Em,function(t,e){var n;t.group===o&&(n=r?t.getZr().painter.getSvgDom().innerHTML:t.renderToCanvas(y(i)),t=t.getDom().getBoundingClientRect(),l=a(t.left,l),u=a(t.top,u),h=s(t.right,h),c=s(t.bottom,c),p.push({dom:n,left:t.left,top:t.top}))}),t=(h*=e)-(l*=e),n=(c*=e)-(u*=e),d=H.createCanvas(),(f=qr(d,{renderer:r?"svg":"canvas"})).resize({width:t,height:n}),r?(g="",O(p,function(t){var e=t.left-l,n=t.top-u;g+='<g transform="translate('+e+","+n+')">'+t.dom+"</g>"}),f.painter.getSvgRoot().innerHTML=g,i.connectedBackgroundColor&&f.painter.setBackgroundColor(i.connectedBackgroundColor),f.refreshImmediately(),f.painter.toDataURL()):(i.connectedBackgroundColor&&f.add(new ks({shape:{x:0,y:0,width:t,height:n},style:{fill:i.connectedBackgroundColor}})),O(p,function(t){t=new _s({style:{x:t.left*e-l,y:t.top*e-u,image:t.dom}});f.add(t)}),f.refreshImmediately(),d.toDataURL("image/"+(i&&i.type||"png")))):this.getDataURL(i);this.id},m.prototype.convertToPixel=function(t,e){return sm(this,"convertToPixel",t,e)},m.prototype.convertFromPixel=function(t,e){return sm(this,"convertFromPixel",t,e)},m.prototype.containPixel=function(t,i){var r;if(!this._disposed)return O(Po(this._model,t),function(t,n){0<=n.indexOf("Models")&&O(t,function(t){var e=t.coordinateSystem;e&&e.containPoint?r=r||!!e.containPoint(i):"seriesModels"===n&&(e=this._chartsMap[t.__viewId])&&e.containPoint&&(r=r||e.containPoint(i,t))},this)},this),!!r;this.id},m.prototype.getVisual=function(t,e){var t=Po(this._model,t,{defaultMainType:"series"}),n=t.seriesModel.getData(),t=t.hasOwnProperty("dataIndexInside")?t.dataIndexInside:t.hasOwnProperty("dataIndex")?n.indexOfRawIndex(t.dataIndex):null;if(null==t)return Kg(n,e);var i=n,r=t,o=e;switch(o){case"color":return i.getItemVisual(r,"style")[i.getVisual("drawType")];case"opacity":return i.getItemVisual(r,"style").opacity;case"symbol":case"symbolSize":case"liftZ":return i.getItemVisual(r,o)}},m.prototype.getViewOfComponentModel=function(t){return this._componentsMap[t.__viewId]},m.prototype.getViewOfSeriesModel=function(t){return this._chartsMap[t.__viewId]},m.prototype._initEvents=function(){var t,n,i,s=this;O(Im,function(a){function t(t){var n,e,i,r=s.getModel(),o=t.target;"globalout"===a?n={}:o&&Qg(o,function(t){var e,t=Us(t);return t&&null!=t.dataIndex?(e=t.dataModel||r.getSeriesByIndex(t.seriesIndex),n=e&&e.getDataParams(t.dataIndex,t.dataType,o)||{},1):t.eventData&&(n=P({},t.eventData),1)},!0),n&&(e=n.componentType,i=n.componentIndex,"markLine"!==e&&"markPoint"!==e&&"markArea"!==e||(e="series",i=n.seriesIndex),i=(e=e&&null!=i&&r.getComponent(e,i))&&s["series"===e.mainType?"_chartsMap":"_componentsMap"][e.__viewId],n.event=t,n.type=a,s._$eventProcessor.eventInfo={targetEl:o,packedEvent:n,model:e,view:i},s.trigger(a,n))}t.zrEventfulCallAtLast=!0,s._zr.on(a,t,s)}),O(Am,function(t,e){s._messageCenter.on(e,function(t){this.trigger(e,t)},s)}),O(["selectchanged"],function(e){s._messageCenter.on(e,function(t){this.trigger(e,t)},s)}),t=this._messageCenter,i=(n=this)._api,t.on("selectchanged",function(t){var e=i.getModel();t.isFromClick?($g("map","selectchanged",n,e,t),$g("pie","selectchanged",n,e,t)):"select"===t.fromAction?($g("map","selected",n,e,t),$g("pie","selected",n,e,t)):"unselect"===t.fromAction&&($g("map","unselected",n,e,t),$g("pie","unselected",n,e,t))})},m.prototype.isDisposed=function(){return this._disposed},m.prototype.clear=function(){this._disposed?this.id:this.setOption({series:[]},!0)},m.prototype.dispose=function(){var t,e,n;this._disposed?this.id:(this._disposed=!0,this.getDom()&&Eo(this.getDom(),Vm,""),e=(t=this)._api,n=t._model,O(t._componentsViews,function(t){t.dispose(n,e)}),O(t._chartsViews,function(t){t.dispose(n,e)}),t._zr.dispose(),t._dom=t._model=t._chartsMap=t._componentsMap=t._chartsViews=t._componentsViews=t._scheduler=t._api=t._zr=t._throttledZrFlush=t._theme=t._coordSysMgr=t._messageCenter=null,delete Em[t.id])},m.prototype.resize=function(t){if(!this[Yy])if(this._disposed)this.id;else{this._zr.resize(t);var e=this._model;if(this._loadingFX&&this._loadingFX.resize(),e){var e=e.resetOption("media"),n=t&&t.silent;this[qy]&&(null==n&&(n=this[qy].silent),e=!0,this[qy]=null),this[Yy]=!0;try{e&&im(this),am.update.call(this,{type:"resize",animation:P({duration:0},t&&t.animation)})}catch(t){throw this[Yy]=!1,t}this[Yy]=!1,hm.call(this,n),cm.call(this,n)}}},m.prototype.showLoading=function(t,e){this._disposed?this.id:(R(t)&&(e=t,t=""),t=t||"default",this.hideLoading(),Nm[t]&&(t=Nm[t](this._api,e),e=this._zr,this._loadingFX=t,e.add(t)))},m.prototype.hideLoading=function(){this._disposed?this.id:(this._loadingFX&&this._zr.remove(this._loadingFX),this._loadingFX=null)},m.prototype.makeActionFromEvent=function(t){var e=P({},t);return e.type=Am[t.type],e},m.prototype.dispatchAction=function(t,e){var n;this._disposed?this.id:(R(e)||(e={silent:!!e}),Dm[t.type]&&this._model&&(this[Yy]?this._pendingActions.push(t):(n=e.silent,um.call(this,t,n),(t=e.flush)?this._zr.flush():!1!==t&&p.browser.weChat&&this._throttledZrFlush(),hm.call(this,n),cm.call(this,n))))},m.prototype.updateLabelLayout=function(){Uy.trigger("series:layoutlabels",this._model,this._api,{updatedSeries:[]})},m.prototype.appendData=function(t){var e;this._disposed?this.id:(e=t.seriesIndex,this.getModel().getSeriesByIndex(e).appendData(t),this._scheduler.unfinished=!0,this.getZr().wakeUp())},m.internalField=(im=function(t){var e=t._scheduler;e.restorePipelines(t._model),e.prepareStageTasks(),rm(t,!0),rm(t,!1),e.plan()},rm=function(t,r){for(var o=t._model,a=t._scheduler,s=r?t._componentsViews:t._chartsViews,l=r?t._componentsMap:t._chartsMap,u=t._zr,h=t._api,e=0;e<s.length;e++)s[e].__alive=!1;function n(t){var e,n=t.__requireNewView,i=(t.__requireNewView=!1,"_ec_"+t.id+"_"+t.type),n=!n&&l[i];n||(e=Ho(t.type),(n=new(r?ug.getClass(e.main,e.sub):fg.getClass(e.sub))).init(o,h),l[i]=n,s.push(n),u.add(n.group)),t.__viewId=n.__id=i,n.__alive=!0,n.__model=t,n.group.__ecComponentInfo={mainType:t.mainType,index:t.componentIndex},r||a.prepareView(n,t,o,h)}r?o.eachComponent(function(t,e){"series"!==t&&n(e)}):o.eachSeries(n);for(e=0;e<s.length;){var i=s[e];i.__alive?e++:(r||i.renderTask.dispose(),u.remove(i.group),i.dispose(o,h),s.splice(e,1),l[i.__id]===i&&delete l[i.__id],i.__id=i.group.__ecComponentInfo=null)}},om=function(c,e,p,n,t){var i,d,r=c._model;function o(t){t&&t.__alive&&t[e]&&t[e](t.__model,r,c._api,p)}r.setUpdatePayload(p),n?((i={})[n+"Id"]=p[n+"Id"],i[n+"Index"]=p[n+"Index"],i[n+"Name"]=p[n+"Name"],i={mainType:n,query:i},t&&(i.subType=t),null!=(t=p.excludeSeriesId)&&(d=E(),O(vo(t),function(t){t=To(t,null);null!=t&&d.set(t,!0)})),r&&r.eachComponent(i,function(t){var e,n,i=d&&null!=d.get(t.id);if(!i)if(Hl(p))if(t instanceof eg){if(p.type===tl&&!p.notBlur&&!t.get(["emphasis","disabled"])){var i=t,r=p,o=c._api,a=i.seriesIndex,s=i.getData(r.dataType);if(s){var r=(F(r=Do(s,r))?r[0]:r)||0,l=s.getItemGraphicEl(r);if(!l)for(var u=s.count(),h=0;!l&&h<u;)l=s.getItemGraphicEl(h++);l?Dl(a,(r=Us(l)).focus,r.blurScope,o):(r=i.get(["emphasis","focus"]),i=i.get(["emphasis","blurScope"]),null!=r&&Dl(a,r,i,o))}}}else{a=Ll(t.mainType,t.componentIndex,p.name,c._api),r=a.focusSelf,i=a.dispatchers;p.type===tl&&r&&!p.notBlur&&Al(t.mainType,t.componentIndex,c._api),i&&O(i,function(t){(p.type===tl?bl:wl)(t)})}else Vl(p)&&t instanceof eg&&(o=t,i=p,c._api,Vl(i)&&(e=i.dataType,F(n=Do(o.getData(e),i))||(n=[n]),o[i.type===rl?"toggleSelect":i.type===nl?"select":"unselect"](n,e)),Pl(t),_m(c))},c),r&&r.eachComponent(i,function(t){d&&null!=d.get(t.id)||o(c["series"===n?"_chartsMap":"_componentsMap"][t.__viewId])},c)):O([].concat(c._componentsViews).concat(c._chartsViews),o)},am={prepareAndUpdate:function(t){im(this),am.update.call(this,t,{optionChanged:null!=t.newOption})},update:function(t,e){var n=this._model,i=this._api,r=this._zr,o=this._coordSysMgr,a=this._scheduler;n&&(n.setUpdatePayload(t),a.restoreData(n,t),a.performSeriesTasks(n),o.create(n,i),a.performDataProcessorTasks(n,t),lm(this,n),o.update(n,i),Sm(n),a.performVisualTasks(n,t),fm(this,n,i,t,e),o=n.get("backgroundColor")||"transparent",a=n.get("darkMode"),r.setBackgroundColor(o),null!=a&&"auto"!==a&&r.setDarkMode(a),Uy.trigger("afterupdate",n,i))},updateTransform:function(n){var i,r,o=this,a=this._model,s=this._api;a&&(a.setUpdatePayload(n),i=[],a.eachComponent(function(t,e){"series"!==t&&(t=o.getViewOfComponentModel(e))&&t.__alive&&(!t.updateTransform||(e=t.updateTransform(e,a,s,n))&&e.update)&&i.push(t)}),r=E(),a.eachSeries(function(t){var e=o._chartsMap[t.__viewId];(!e.updateTransform||(e=e.updateTransform(t,a,s,n))&&e.update)&&r.set(t.uid,1)}),Sm(a),this._scheduler.performVisualTasks(a,n,{setDirty:!0,dirtyMap:r}),ym(this,a,s,n,{},r),Uy.trigger("afterupdate",a,s))},updateView:function(t){var e=this._model;e&&(e.setUpdatePayload(t),fg.markUpdateMethod(t,"updateView"),Sm(e),this._scheduler.performVisualTasks(e,t,{setDirty:!0}),fm(this,e,this._api,t,{}),Uy.trigger("afterupdate",e,this._api))},updateVisual:function(n){var i=this,r=this._model;r&&(r.setUpdatePayload(n),r.eachSeries(function(t){t.getData().clearAllVisual()}),fg.markUpdateMethod(n,"updateVisual"),Sm(r),this._scheduler.performVisualTasks(r,n,{visualType:"visual",setDirty:!0}),r.eachComponent(function(t,e){"series"!==t&&(t=i.getViewOfComponentModel(e))&&t.__alive&&t.updateVisual(e,r,i._api,n)}),r.eachSeries(function(t){i._chartsMap[t.__viewId].updateVisual(t,r,i._api,n)}),Uy.trigger("afterupdate",r,this._api))},updateLayout:function(t){am.update.call(this,t)}},sm=function(t,e,n,i){if(t._disposed)t.id;else for(var r=t._model,o=t._coordSysMgr.getCoordinateSystems(),a=Po(r,n),s=0;s<o.length;s++){var l=o[s];if(l[e]&&null!=(l=l[e](r,a,i)))return l}},lm=function(t,e){var n=t._chartsMap,i=t._scheduler;e.eachSeries(function(t){i.updateStreamModes(t,n[t.__viewId])})},um=function(i,t){var r,o,a=this,e=this.getModel(),n=i.type,s=i.escapeConnect,l=Dm[n],u=l.actionInfo,h=(u.update||"update").split(":"),c=h.pop(),p=null!=h[0]&&Ho(h[0]),h=(this[Yy]=!0,[i]),d=!1,f=(i.batch&&(d=!0,h=B(i.batch,function(t){return(t=z(P({},t),i)).batch=null,t})),[]),g=Vl(i),y=Hl(i);if(y&&Il(this._api),O(h,function(t){var e,n;(r=(r=l.action(t,a._model,a._api))||P({},t)).type=u.event||r.type,f.push(r),y?(e=(n=Oo(i)).queryOptionMap,n=n.mainTypeSpecified?e.keys()[0]:"series",om(a,c,t,n),_m(a)):g?(om(a,c,t,"series"),_m(a)):p&&om(a,c,t,p.main,p.sub)}),"none"!==c&&!y&&!g&&!p)try{this[qy]?(im(this),am.update.call(this,i),this[qy]=null):am[c].call(this,i)}catch(t){throw this[Yy]=!1,t}r=d?{type:u.event||n,escapeConnect:s,batch:f}:f[0],this[Yy]=!1,t||((h=this._messageCenter).trigger(r.type,r),g&&(d={type:"selectchanged",escapeConnect:s,selected:(o=[],e.eachSeries(function(n){O(n.getAllData(),function(t){t.data;var t=t.type,e=n.getSelectedDataIndices();0<e.length&&(e={dataIndex:e,seriesIndex:n.seriesIndex},null!=t&&(e.dataType=t),o.push(e))})}),o),isFromClick:i.isFromClick||!1,fromAction:i.type,fromActionPayload:i},h.trigger(d.type,d)))},hm=function(t){for(var e=this._pendingActions;e.length;){var n=e.shift();um.call(this,n,t)}},cm=function(t){t||this.trigger("updated")},pm=function(e,n){e.on("rendered",function(t){n.trigger("rendered",t),!e.animation.isFinished()||n[qy]||n._scheduler.unfinished||n._pendingActions.length||n.trigger("finished")})},dm=function(t,a){t.on("mouseover",function(t){var e,n,i,r,o=Qg(t.target,Fl);o&&(o=o,e=t,t=a._api,n=Us(o),i=(r=Ll(n.componentMainType,n.componentIndex,n.componentHighDownName,t)).dispatchers,r=r.focusSelf,i?(r&&Al(n.componentMainType,n.componentIndex,t),O(i,function(t){return _l(t,e)})):(Dl(n.seriesIndex,n.focus,n.blurScope,t),"self"===n.focus&&Al(n.componentMainType,n.componentIndex,t),_l(o,e)),_m(a))}).on("mouseout",function(t){var e,n,i=Qg(t.target,Fl);i&&(i=i,e=t,Il(t=a._api),(n=Ll((n=Us(i)).componentMainType,n.componentIndex,n.componentHighDownName,t).dispatchers)?O(n,function(t){return xl(t,e)}):xl(i,e),_m(a))}).on("click",function(t){var e,t=Qg(t.target,function(t){return null!=Us(t).dataIndex},!0);t&&(e=t.selected?"unselect":"select",t=Us(t),a._api.dispatchAction({type:e,dataType:t.dataType,dataIndexInside:t.dataIndex,seriesIndex:t.seriesIndex,isFromClick:!0}))})},fm=function(t,e,n,i,r){var o,a,s,l,u,h,c;u=[],c=!(h=[]),(o=e).eachComponent(function(t,e){var n=e.get("zlevel")||0,i=e.get("z")||0,r=e.getZLevelKey();c=c||!!r,("series"===t?h:u).push({zlevel:n,z:i,idx:e.componentIndex,type:t,key:r})}),c&&(yn(a=u.concat(h),function(t,e){return t.zlevel===e.zlevel?t.z-e.z:t.zlevel-e.zlevel}),O(a,function(t){var e=o.getComponent(t.type,t.idx),n=t.zlevel,t=t.key;null!=s&&(n=Math.max(s,n)),t?(n===s&&t!==l&&n++,l=t):l&&(n===s&&n++,l=""),s=n,e.setZLevel(n)})),gm(t,e,n,i,r),O(t._chartsViews,function(t){t.__alive=!1}),ym(t,e,n,i,r),O(t._chartsViews,function(t){t.__alive||t.remove(e,n)})},gm=function(t,n,i,r,e,o){O(o||t._componentsViews,function(t){var e=t.__model;Cm(0,t),t.render(e,n,i,r),Tm(e,t),km(e,t)})},ym=function(r,t,e,o,n,a){var i,s,l,u,h=r._scheduler,c=(n=P(n||{},{updatedSeries:t.getSeries()}),Uy.trigger("series:beforeupdate",t,e,n),!1);t.eachSeries(function(t){var e,n=r._chartsMap[t.__viewId],i=(n.__alive=!0,n.renderTask);h.updatePayload(i,o),Cm(0,n),a&&a.get(t.uid)&&i.dirty(),i.perform(h.getPerformArgs(i))&&(c=!0),n.group.silent=!!t.get("silent"),i=n,e=t.get("blendMode")||null,i.eachRendered(function(t){t.isGroup||(t.style.blend=e)}),Pl(t)}),h.unfinished=c||h.unfinished,Uy.trigger("series:layoutlabels",t,e,n),Uy.trigger("series:transition",t,e,n),t.eachSeries(function(t){var e=r._chartsMap[t.__viewId];Tm(t,e),km(t,e)}),s=t,l=(i=r)._zr.storage,u=0,l.traverse(function(t){t.isGroup||u++}),u>s.get("hoverLayerThreshold")&&!p.node&&!p.worker&&s.eachSeries(function(t){t.preventUsingHoverLayer||(t=i._chartsMap[t.__viewId]).__alive&&t.eachRendered(function(t){t.states.emphasis&&(t.states.emphasis.hoverLayer=!0)})}),Uy.trigger("series:afterupdate",t,e,n)},_m=function(t){t[Zy]=!0,t.getZr().wakeUp()},xm=function(t){t[Zy]&&(t.getZr().storage.traverse(function(t){Dh(t)||Mm(t)}),t[Zy]=!1)},mm=function(n){return u(t,e=Jp),t.prototype.getCoordinateSystems=function(){return n._coordSysMgr.getCoordinateSystems()},t.prototype.getComponentByElement=function(t){for(;t;){var e=t.__ecComponentInfo;if(null!=e)return n._model.getComponent(e.mainType,e.index);t=t.parent}},t.prototype.enterEmphasis=function(t,e){bl(t,e),_m(n)},t.prototype.leaveEmphasis=function(t,e){wl(t,e),_m(n)},t.prototype.enterBlur=function(t){Sl(t),_m(n)},t.prototype.leaveBlur=function(t){Ml(t),_m(n)},t.prototype.enterSelect=function(t){Tl(t),_m(n)},t.prototype.leaveSelect=function(t){Cl(t),_m(n)},t.prototype.getModel=function(){return n.getModel()},t.prototype.getViewOfComponentModel=function(t){return n.getViewOfComponentModel(t)},t.prototype.getViewOfSeriesModel=function(t){return n.getViewOfSeriesModel(t)},new t(n);function t(){return null!==e&&e.apply(this,arguments)||this}var e},void(vm=function(i){function r(t,e){for(var n=0;n<t.length;n++)t[n][Ky]=e}O(Am,function(t,e){i._messageCenter.on(e,function(t){var e,n;!zm[i.group]||0===i[Ky]||t&&t.escapeConnect||(e=i.makeActionFromEvent(t),n=[],O(Em,function(t){t!==i&&t.group===i.group&&n.push(t)}),r(n,0),O(n,function(t){1!==t[Ky]&&t.dispatchAction(e)}),r(n,2))})})})),m);function m(t,e,n){var i=bm.call(this,new Yg)||this,t=(i._chartsViews=[],i._chartsMap={},i._componentsViews=[],i._componentsMap={},i._pendingActions=[],n=n||{},V(e)&&(e=Rm[e]),i._dom=t,n.ssr&&jr(function(t){var e,t=Us(t),n=t.dataIndex;if(null!=n)return(e=E()).set("series_index",t.seriesIndex),e.set("data_index",n),t.ssrType&&e.set("ssr_type",t.ssrType),e}),i._zr=qr(t,{renderer:n.renderer||"canvas",devicePixelRatio:n.devicePixelRatio,width:n.width,height:n.height,ssr:n.ssr,useDirtyRect:N(n.useDirtyRect,!1),useCoarsePointer:N(n.useCoarsePointer,"auto"),pointerSize:n.pointerSize})),n=(i._ssr=n.ssr,i._throttledZrFlush=bg(ht(t.flush,t),17),(e=y(e))&&Td(e,!0),i._theme=e,i._locale=V(e=n.locale||Ic)?(n=Cc[e.toUpperCase()]||{},e===Sc||e===Mc?y(n):d(y(n),y(Cc[Tc]),!1)):d(y(e),y(Cc[Tc]),!1),i._coordSysMgr=new nd,i._api=mm(i));function r(t,e){return t.__prio-e.__prio}return yn(Om,r),yn(Lm,r),i._scheduler=new Dg(i,n,Lm,Om),i._messageCenter=new em,i._initEvents(),i.resize=ht(i.resize,i),t.animation.on("frame",i._onframe,i),pm(t,i),dm(t,i),It(i),i}function Sm(t){t.clearColorPalette(),t.eachSeries(function(t){t.clearColorPalette()})}function Mm(t){for(var e=[],n=t.currentStates,i=0;i<n.length;i++){var r=n[i];"emphasis"!==r&&"blur"!==r&&"select"!==r&&e.push(r)}t.selected&&t.states.select&&e.push("select"),t.hoverState===$s&&t.states.emphasis?e.push("emphasis"):t.hoverState===Ks&&t.states.blur&&e.push("blur"),t.useStates(e)}function Tm(t,e){var n,i;t.preventAutoZ||(n=t.get("z")||0,i=t.get("zlevel")||0,e.eachRendered(function(t){return function t(e,n,i,r){var o=e.getTextContent();var a=e.getTextGuideLine();var s=e.isGroup;if(s)for(var l=e.childrenRef(),u=0;u<l.length;u++)r=Math.max(t(l[u],n,i,r),r);else e.z=n,e.zlevel=i,r=Math.max(e.z2,r);o&&(o.z=n,o.zlevel=i,isFinite(r))&&(o.z2=r+2);a&&(s=e.textGuideLineConfig,a.z=n,a.zlevel=i,isFinite(r))&&(a.z2=r+(s&&s.showAbove?1:-1));return r}(t,n,i,-1/0),!0}))}function Cm(t,e){e.eachRendered(function(t){var e,n;Dh(t)||(e=t.getTextContent(),n=t.getTextGuideLine(),t.stateTransition&&(t.stateTransition=null),e&&e.stateTransition&&(e.stateTransition=null),n&&n.stateTransition&&(n.stateTransition=null),t.hasState()?(t.prevStates=t.currentStates,t.clearStates()):t.prevStates&&(t.prevStates=null))})}function km(t,e){var n=t.getModel("stateAnimation"),r=t.isAnimationEnabled(),t=n.get("duration"),o=0<t?{duration:t,delay:n.get("delay"),easing:n.get("easing")}:null;e.eachRendered(function(t){var e,n,i;t.states&&t.states.emphasis&&(Dh(t)||(t instanceof j&&((i=qs(n=t)).normalFill=n.style.fill,i.normalStroke=n.style.stroke,n=n.states.select||{},i.selectFill=n.style&&n.style.fill||null,i.selectStroke=n.style&&n.style.stroke||null),t.__dirty&&(i=t.prevStates)&&t.useStates(i),r&&(t.stateTransition=o,n=t.getTextContent(),e=t.getTextGuideLine(),n&&(n.stateTransition=o),e)&&(e.stateTransition=o),t.__dirty&&Mm(t)))})}var ay=wm.prototype,Im=(ay.on=$y("on"),ay.off=$y("off"),ay.one=function(i,r,t){var o=this;this.on.call(this,i,function t(){for(var e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];r&&r.apply&&r.apply(this,e),o.off(i,t)},t)},["click","dblclick","mouseover","mouseout","mousemove","mousedown","mouseup","globalout","contextmenu"]);var Dm={},Am={},Lm=[],Pm=[],Om=[],Rm={},Nm={},Em={},zm={},Bm=+new Date,Fm=+new Date,Vm="_echarts_instance_";function Hm(t){zm[t]=!1}iy=Hm;function Gm(t){return Em[e=Vm,(t=t).getAttribute?t.getAttribute(e):t[e]];var e}function Wm(t,e){Rm[t]=e}function Um(t){k(Pm,t)<0&&Pm.push(t)}function Xm(t,e){t0(Lm,t,e,2e3)}function Ym(t){Zm("afterinit",t)}function qm(t){Zm("afterupdate",t)}function Zm(t,e){Uy.on(t,e)}function jm(t,e,n){D(e)&&(n=e,e="");var i=R(t)?t.type:[t,t={event:e}][0];t.event=(t.event||i).toLowerCase(),e=t.event,Am[e]||(Tt(jy.test(i)&&jy.test(e)),Dm[i]||(Dm[i]={action:n,actionInfo:t}),Am[e]=i)}function Km(t,e){nd.register(t,e)}function $m(t,e){t0(Om,t,e,1e3,"layout")}function Qm(t,e){t0(Om,t,e,3e3,"visual")}var Jm=[];function t0(t,e,n,i,r){(D(e)||R(e))&&(n=e,e=i),0<=k(Jm,n)||(Jm.push(n),(i=Dg.wrapStageHandler(n,r)).__prio=e,i.__raw=n,t.push(i))}function e0(t,e){Nm[t]=e}function n0(t,e,n){var i=Xy.registerMap;i&&i(t,e,n)}function i0(t){var e=(t=y(t)).type,n=(e||f(""),e.split(":")),i=(2!==n.length&&f(""),!1);"echarts"===n[0]&&(e=n[1],i=!0),t.__isBuiltIn=i,Pf.set(e,t)}Qm(2e3,pc),Qm(4500,Ko),Qm(4500,yc),Qm(2e3,fc),Qm(4500,hc),Qm(7e3,function(e,i){e.eachRawSeries(function(t){var n;!e.isSeriesFiltered(t)&&((n=t.getData()).hasItemVisual()&&n.each(function(t){var e=n.getItemVisual(t,"decal");e&&(n.ensureUniqueItemVisual(t,"style").decal=Gy(e,i))}),t=n.getVisual("decal"))&&(n.getVisual("style").decal=Gy(t,i))})}),Um(Td),Xm(900,function(t){var i=E();t.eachSeries(function(t){var e,n=t.get("stack");n&&(n=i.get(n)||i.set(n,[]),(t={stackResultDimension:(e=t.getData()).getCalculationInfo("stackResultDimension"),stackedOverDimension:e.getCalculationInfo("stackedOverDimension"),stackedDimension:e.getCalculationInfo("stackedDimension"),stackedByDimension:e.getCalculationInfo("stackedByDimension"),isStackedByIndex:e.getCalculationInfo("isStackedByIndex"),data:e,seriesModel:t}).stackedDimension)&&(t.isStackedByIndex||t.stackedByDimension)&&(n.length&&e.setCalculationInfo("stackedOnSeries",n[n.length-1].seriesModel),n.push(t))}),i.each(Cd)}),e0("default",function(i,r){z(r=r||{},{text:"loading",textColor:"#000",fontSize:12,fontWeight:"normal",fontStyle:"normal",fontFamily:"sans-serif",maskColor:"rgba(255, 255, 255, 0.8)",showSpinner:!0,color:"#5470c6",spinnerRadius:10,lineWidth:5,zlevel:0});var o,t=new Vr,a=new ks({style:{fill:r.maskColor},zlevel:r.zlevel,z:1e4}),s=(t.add(a),new Ps({style:{text:r.text,fill:r.textColor,fontSize:r.fontSize,fontWeight:r.fontWeight,fontStyle:r.fontStyle,fontFamily:r.fontFamily},zlevel:r.zlevel,z:10001})),l=new ks({style:{fill:"none"},textContent:s,textConfig:{position:"right",distance:10},zlevel:r.zlevel,z:10001});return t.add(l),r.showSpinner&&((o=new rh({shape:{startAngle:-Ig/2,endAngle:-Ig/2+.1,r:r.spinnerRadius},style:{stroke:r.color,lineCap:"round",lineWidth:r.lineWidth},zlevel:r.zlevel,z:10001})).animateShape(!0).when(1e3,{endAngle:3*Ig/2}).start("circularInOut"),o.animateShape(!0).when(1e3,{startAngle:3*Ig/2}).delay(300).start("circularInOut"),t.add(o)),t.resize=function(){var t=s.getBoundingRect().width,e=r.showSpinner?r.spinnerRadius:0,t=(i.getWidth()-2*e-(r.showSpinner&&t?10:0)-t)/2-(r.showSpinner&&t?0:5+t/2)+(r.showSpinner?0:t/2)+(t?0:e),n=i.getHeight()/2;r.showSpinner&&o.setShape({cx:t,cy:n}),l.setShape({x:t-e,y:n-e,width:2*e,height:2*e}),a.setShape({x:0,y:0,width:i.getWidth(),height:i.getHeight()})},t.resize(),t}),jm({type:tl,event:tl,update:tl},Ft),jm({type:el,event:el,update:el},Ft),jm({type:nl,event:nl,update:nl},Ft),jm({type:il,event:il,update:il},Ft),jm({type:rl,event:rl,update:rl},Ft),Wm("light",Nc),Wm("dark",lc);function r0(t){return null==t?0:t.length||1}function o0(t){return t}s0.prototype.add=function(t){return this._add=t,this},s0.prototype.update=function(t){return this._update=t,this},s0.prototype.updateManyToOne=function(t){return this._updateManyToOne=t,this},s0.prototype.updateOneToMany=function(t){return this._updateOneToMany=t,this},s0.prototype.updateManyToMany=function(t){return this._updateManyToMany=t,this},s0.prototype.remove=function(t){return this._remove=t,this},s0.prototype.execute=function(){this[this._diffModeMultiple?"_executeMultiple":"_executeOneToOne"]()},s0.prototype._executeOneToOne=function(){var t=this._old,e=this._new,n={},i=new Array(t.length),r=new Array(e.length);this._initIndexMap(t,null,i,"_oldKeyGetter"),this._initIndexMap(e,n,r,"_newKeyGetter");for(var o=0;o<t.length;o++){var a,s=i[o],l=n[s],u=r0(l);1<u?(a=l.shift(),1===l.length&&(n[s]=l[0]),this._update&&this._update(a,o)):1===u?(n[s]=null,this._update&&this._update(l,o)):this._remove&&this._remove(o)}this._performRestAdd(r,n)},s0.prototype._executeMultiple=function(){var t=this._old,e=this._new,n={},i={},r=[],o=[];this._initIndexMap(t,n,r,"_oldKeyGetter"),this._initIndexMap(e,i,o,"_newKeyGetter");for(var a=0;a<r.length;a++){var s=r[a],l=n[s],u=i[s],h=r0(l),c=r0(u);if(1<h&&1===c)this._updateManyToOne&&this._updateManyToOne(u,l),i[s]=null;else if(1===h&&1<c)this._updateOneToMany&&this._updateOneToMany(u,l),i[s]=null;else if(1===h&&1===c)this._update&&this._update(u,l),i[s]=null;else if(1<h&&1<c)this._updateManyToMany&&this._updateManyToMany(u,l),i[s]=null;else if(1<h)for(var p=0;p<h;p++)this._remove&&this._remove(l[p]);else this._remove&&this._remove(l)}this._performRestAdd(o,i)},s0.prototype._performRestAdd=function(t,e){for(var n=0;n<t.length;n++){var i=t[n],r=e[i],o=r0(r);if(1<o)for(var a=0;a<o;a++)this._add&&this._add(r[a]);else 1===o&&this._add&&this._add(r);e[i]=null}},s0.prototype._initIndexMap=function(t,e,n,i){for(var r=this._diffModeMultiple,o=0;o<t.length;o++){var a,s,l="_ec_"+this[i](t[o],o);r||(n[o]=l),e&&(0===(s=r0(a=e[l]))?(e[l]=o,r&&n.push(l)):1===s?e[l]=[a,o]:a.push(o))}};var a0=s0;function s0(t,e,n,i,r,o){this._old=t,this._new=e,this._oldKeyGetter=n||o0,this._newKeyGetter=i||o0,this.context=r,this._diffModeMultiple="multiple"===o}u0.prototype.get=function(){return{fullDimensions:this._getFullDimensionNames(),encode:this._encode}},u0.prototype._getFullDimensionNames=function(){return this._cachedDimNames||(this._cachedDimNames=this._schema?this._schema.makeOutputDimensionNames():[]),this._cachedDimNames};var l0=u0;function u0(t,e){this._encode=t,this._schema=e}function h0(o,t){var e={},a=e.encode={},s=E(),l=[],u=[],h={},i=(O(o.dimensions,function(t){var e,n,i=o.getDimensionInfo(t),r=i.coordDim;r&&(e=i.coordDimIndex,c0(a,r)[e]=t,i.isExtraCoord||(s.set(r,1),"ordinal"!==(n=i.type)&&"time"!==n&&(l[0]=t),c0(h,r)[e]=o.getDimensionIndex(i.name)),i.defaultTooltip)&&u.push(t),Tp.each(function(t,e){var n=c0(a,e),e=i.otherDims[e];null!=e&&!1!==e&&(n[e]=i.name)})}),[]),r={},n=(s.each(function(t,e){var n=a[e];r[e]=n[0],i=i.concat(n)}),e.dataDimsOnCoord=i,e.dataDimIndicesOnCoord=B(i,function(t){return o.getDimensionInfo(t).storeDimIndex}),e.encodeFirstDimNotExtra=r,a.label),n=(n&&n.length&&(l=n.slice()),a.tooltip);return n&&n.length?u=n.slice():u.length||(u=l.slice()),a.defaultedLabel=l,a.defaultedTooltip=u,e.userOutput=new l0(h,t),e}function c0(t,e){return t.hasOwnProperty(e)||(t[e]=[]),t[e]}var p0=function(t){this.otherDims={},null!=t&&P(this,t)},d0=Ao(),f0={float:"f",int:"i",ordinal:"o",number:"n",time:"t"},g0=(y0.prototype.isDimensionOmitted=function(){return this._dimOmitted},y0.prototype._updateDimOmitted=function(t){(this._dimOmitted=t)&&!this._dimNameMap&&(this._dimNameMap=_0(this.source))},y0.prototype.getSourceDimensionIndex=function(t){return N(this._dimNameMap.get(t),-1)},y0.prototype.getSourceDimension=function(t){var e=this.source.dimensionsDefine;if(e)return e[t]},y0.prototype.makeStoreSchema=function(){for(var t=this._fullDimCount,e=Ed(this.source),n=!(30<t),i="",r=[],o=0,a=0;o<t;o++){var s,l=void 0,u=void 0,h=void 0,c=this.dimensions[a];c&&c.storeDimIndex===o?(l=e?c.name:null,u=c.type,h=c.ordinalMeta,a++):(s=this.getSourceDimension(o))&&(l=e?s.name:null,u=s.type),r.push({property:l,type:u,ordinalMeta:h}),!e||null==l||c&&c.isCalculationCoord||(i+=n?l.replace(/\`/g,"`1").replace(/\$/g,"`2"):l),i=i+"$"+(f0[u]||"f"),h&&(i+=h.uid),i+="$"}var p=this.source;return{dimensions:r,hash:[p.seriesLayoutBy,p.startIndex,i].join("$$")}},y0.prototype.makeOutputDimensionNames=function(){for(var t=[],e=0,n=0;e<this._fullDimCount;e++){var i=void 0,r=this.dimensions[n];r&&r.storeDimIndex===e?(r.isCalculationCoord||(i=r.name),n++):(r=this.getSourceDimension(e))&&(i=r.name),t.push(i)}return t},y0.prototype.appendCalculationDimension=function(t){this.dimensions.push(t),t.isCalculationCoord=!0,this._fullDimCount++,this._updateDimOmitted(!0)},y0);function y0(t){this.dimensions=t.dimensions,this._dimOmitted=t.dimensionOmitted,this.source=t.source,this._fullDimCount=t.fullDimensionCount,this._updateDimOmitted(t.dimensionOmitted)}function m0(t){return t instanceof g0}function v0(t){for(var e=E(),n=0;n<(t||[]).length;n++){var i=t[n],i=R(i)?i.name:i;null!=i&&null==e.get(i)&&e.set(i,n)}return e}function _0(t){var e=d0(t);return e.dimNameMap||(e.dimNameMap=v0(t.dimensionsDefine))}var x0,b0,w0,S0,M0,T0,C0,k0=R,I0=B,D0="undefined"==typeof Int32Array?Array:Int32Array,A0=["hasItemOption","_nameList","_idList","_invertedIndicesMap","_dimSummary","userOutput","_rawData","_dimValueGetter","_nameDimIdx","_idDimIdx","_nameRepeatCount"],L0=["_approximateExtent"],P0=(v.prototype.getDimension=function(t){var e;return null==(e=this._recognizeDimIndex(t))?t:(e=t,this._dimOmitted?null!=(t=this._dimIdxToName.get(e))?t:(t=this._schema.getSourceDimension(e))?t.name:void 0:this.dimensions[e])},v.prototype.getDimensionIndex=function(t){var e=this._recognizeDimIndex(t);return null!=e?e:null==t?-1:(e=this._getDimInfo(t))?e.storeDimIndex:this._dimOmitted?this._schema.getSourceDimensionIndex(t):-1},v.prototype._recognizeDimIndex=function(t){if(dt(t)||null!=t&&!isNaN(t)&&!this._getDimInfo(t)&&(!this._dimOmitted||this._schema.getSourceDimensionIndex(t)<0))return+t},v.prototype._getStoreDimIndex=function(t){return this.getDimensionIndex(t)},v.prototype.getDimensionInfo=function(t){return this._getDimInfo(this.getDimension(t))},v.prototype._initGetDimensionInfo=function(t){var e=this._dimInfos;this._getDimInfo=t?function(t){return e.hasOwnProperty(t)?e[t]:void 0}:function(t){return e[t]}},v.prototype.getDimensionsOnCoord=function(){return this._dimSummary.dataDimsOnCoord.slice()},v.prototype.mapDimension=function(t,e){var n=this._dimSummary;return null==e?n.encodeFirstDimNotExtra[t]:(n=n.encode[t])?n[e]:null},v.prototype.mapDimensionsAll=function(t){return(this._dimSummary.encode[t]||[]).slice()},v.prototype.getStore=function(){return this._store},v.prototype.initData=function(t,e,n){var i,r,o=this;(i=t instanceof Wf?t:i)||(r=this.dimensions,t=Ad(t)||st(t)?new Vd(t,r.length):t,i=new Wf,r=I0(r,function(t){return{type:o._dimInfos[t].type,property:t}}),i.initData(t,r,n)),this._store=i,this._nameList=(e||[]).slice(),this._idList=[],this._nameRepeatCount={},this._doInit(0,i.count()),this._dimSummary=h0(this,this._schema),this.userOutput=this._dimSummary.userOutput},v.prototype.appendData=function(t){t=this._store.appendData(t);this._doInit(t[0],t[1])},v.prototype.appendValues=function(t,e){var t=this._store.appendValues(t,e.length),n=t.start,i=t.end,r=this._shouldMakeIdFromName();if(this._updateOrdinalMeta(),e)for(var o=n;o<i;o++)this._nameList[o]=e[o-n],r&&C0(this,o)},v.prototype._updateOrdinalMeta=function(){for(var t=this._store,e=this.dimensions,n=0;n<e.length;n++){var i=this._dimInfos[e[n]];i.ordinalMeta&&t.collectOrdinalMeta(i.storeDimIndex,i.ordinalMeta)}},v.prototype._shouldMakeIdFromName=function(){var t=this._store.getProvider();return null==this._idDimIdx&&t.getSource().sourceFormat!==Ap&&!t.fillStorage},v.prototype._doInit=function(t,e){if(!(e<=t)){var n=this._store.getProvider(),i=(this._updateOrdinalMeta(),this._nameList),r=this._idList;if(n.getSource().sourceFormat===Cp&&!n.pure)for(var o=[],a=t;a<e;a++){var s,l=n.getItem(a,o);this.hasItemOption||!R(s=l)||s instanceof Array||(this.hasItemOption=!0),l&&(s=l.name,null==i[a]&&null!=s&&(i[a]=To(s,null)),l=l.id,null==r[a])&&null!=l&&(r[a]=To(l,null))}if(this._shouldMakeIdFromName())for(a=t;a<e;a++)C0(this,a);x0(this)}},v.prototype.getApproximateExtent=function(t){return this._approximateExtent[t]||this._store.getDataExtent(this._getStoreDimIndex(t))},v.prototype.setApproximateExtent=function(t,e){e=this.getDimension(e),this._approximateExtent[e]=t.slice()},v.prototype.getCalculationInfo=function(t){return this._calculationInfo[t]},v.prototype.setCalculationInfo=function(t,e){k0(t)?P(this._calculationInfo,t):this._calculationInfo[t]=e},v.prototype.getName=function(t){var t=this.getRawIndex(t),e=this._nameList[t];return e=null==(e=null==e&&null!=this._nameDimIdx?w0(this,this._nameDimIdx,t):e)?"":e},v.prototype._getCategory=function(t,e){e=this._store.get(t,e),t=this._store.getOrdinalMeta(t);return t?t.categories[e]:e},v.prototype.getId=function(t){return b0(this,this.getRawIndex(t))},v.prototype.count=function(){return this._store.count()},v.prototype.get=function(t,e){var n=this._store,t=this._dimInfos[t];if(t)return n.get(t.storeDimIndex,e)},v.prototype.getByRawIndex=function(t,e){var n=this._store,t=this._dimInfos[t];if(t)return n.getByRawIndex(t.storeDimIndex,e)},v.prototype.getIndices=function(){return this._store.getIndices()},v.prototype.getDataExtent=function(t){return this._store.getDataExtent(this._getStoreDimIndex(t))},v.prototype.getSum=function(t){return this._store.getSum(this._getStoreDimIndex(t))},v.prototype.getMedian=function(t){return this._store.getMedian(this._getStoreDimIndex(t))},v.prototype.getValues=function(t,e){var n=this,i=this._store;return F(t)?i.getValues(I0(t,function(t){return n._getStoreDimIndex(t)}),e):i.getValues(t)},v.prototype.hasValue=function(t){for(var e=this._dimSummary.dataDimIndicesOnCoord,n=0,i=e.length;n<i;n++)if(isNaN(this._store.get(e[n],t)))return!1;return!0},v.prototype.indexOfName=function(t){for(var e=0,n=this._store.count();e<n;e++)if(this.getName(e)===t)return e;return-1},v.prototype.getRawIndex=function(t){return this._store.getRawIndex(t)},v.prototype.indexOfRawIndex=function(t){return this._store.indexOfRawIndex(t)},v.prototype.rawIndexOf=function(t,e){t=(t&&this._invertedIndicesMap[t])[e];return null==t||isNaN(t)?-1:t},v.prototype.indicesOfNearest=function(t,e,n){return this._store.indicesOfNearest(this._getStoreDimIndex(t),e,n)},v.prototype.each=function(t,e,n){D(t)&&(n=e,e=t,t=[]);n=n||this,t=I0(S0(t),this._getStoreDimIndex,this);this._store.each(t,n?ht(e,n):e)},v.prototype.filterSelf=function(t,e,n){D(t)&&(n=e,e=t,t=[]);n=n||this,t=I0(S0(t),this._getStoreDimIndex,this);return this._store=this._store.filter(t,n?ht(e,n):e),this},v.prototype.selectRange=function(n){var i=this,r={};return O(I(n),function(t){var e=i._getStoreDimIndex(t);r[e]=n[t]}),this._store=this._store.selectRange(r),this},v.prototype.mapArray=function(t,e,n){D(t)&&(n=e,e=t,t=[]);var i=[];return this.each(t,function(){i.push(e&&e.apply(this,arguments))},n=n||this),i},v.prototype.map=function(t,e,n,i){n=n||i||this,i=I0(S0(t),this._getStoreDimIndex,this),t=T0(this);return t._store=this._store.map(i,n?ht(e,n):e),t},v.prototype.modify=function(t,e,n,i){n=n||i||this,i=I0(S0(t),this._getStoreDimIndex,this);this._store.modify(i,n?ht(e,n):e)},v.prototype.downSample=function(t,e,n,i){var r=T0(this);return r._store=this._store.downSample(this._getStoreDimIndex(t),e,n,i),r},v.prototype.lttbDownSample=function(t,e){var n=T0(this);return n._store=this._store.lttbDownSample(this._getStoreDimIndex(t),e),n},v.prototype.getRawDataItem=function(t){return this._store.getRawDataItem(t)},v.prototype.getItemModel=function(t){var e=this.hostModel,t=this.getRawDataItem(t);return new _c(t,e,e&&e.ecModel)},v.prototype.diff=function(e){var n=this;return new a0(e?e.getStore().getIndices():[],this.getStore().getIndices(),function(t){return b0(e,t)},function(t){return b0(n,t)})},v.prototype.getVisual=function(t){var e=this._visual;return e&&e[t]},v.prototype.setVisual=function(t,e){this._visual=this._visual||{},k0(t)?P(this._visual,t):this._visual[t]=e},v.prototype.getItemVisual=function(t,e){t=this._itemVisuals[t],t=t&&t[e];return null==t?this.getVisual(e):t},v.prototype.hasItemVisual=function(){return 0<this._itemVisuals.length},v.prototype.ensureUniqueItemVisual=function(t,e){var n=this._itemVisuals,i=n[t],n=(i=i||(n[t]={}))[e];return null==n&&(F(n=this.getVisual(e))?n=n.slice():k0(n)&&(n=P({},n)),i[e]=n),n},v.prototype.setItemVisual=function(t,e,n){var i=this._itemVisuals[t]||{};this._itemVisuals[t]=i,k0(e)?P(i,e):i[e]=n},v.prototype.clearAllVisual=function(){this._visual={},this._itemVisuals=[]},v.prototype.setLayout=function(t,e){k0(t)?P(this._layout,t):this._layout[t]=e},v.prototype.getLayout=function(t){return this._layout[t]},v.prototype.getItemLayout=function(t){return this._itemLayouts[t]},v.prototype.setItemLayout=function(t,e,n){this._itemLayouts[t]=n?P(this._itemLayouts[t]||{},e):e},v.prototype.clearItemLayouts=function(){this._itemLayouts.length=0},v.prototype.setItemGraphicEl=function(t,e){var n,i,r,o,a=this.hostModel&&this.hostModel.seriesIndex;n=a,i=this.dataType,r=t,(a=e)&&((o=Us(a)).dataIndex=r,o.dataType=i,o.seriesIndex=n,o.ssrType="chart","group"===a.type)&&a.traverse(function(t){t=Us(t);t.seriesIndex=n,t.dataIndex=r,t.dataType=i,t.ssrType="chart"}),this._graphicEls[t]=e},v.prototype.getItemGraphicEl=function(t){return this._graphicEls[t]},v.prototype.eachItemGraphicEl=function(n,i){O(this._graphicEls,function(t,e){t&&n&&n.call(i,t,e)})},v.prototype.cloneShallow=function(t){return t=t||new v(this._schema||I0(this.dimensions,this._getDimInfo,this),this.hostModel),M0(t,this),t._store=this._store,t},v.prototype.wrapMethod=function(t,e){var n=this[t];D(n)&&(this.__wrappedMethods=this.__wrappedMethods||[],this.__wrappedMethods.push(t),this[t]=function(){var t=n.apply(this,arguments);return e.apply(this,[t].concat(St(arguments)))})},v.internalField=(x0=function(a){var s=a._invertedIndicesMap;O(s,function(t,e){var n=a._dimInfos[e],i=n.ordinalMeta,r=a._store;if(i){t=s[e]=new D0(i.categories.length);for(var o=0;o<t.length;o++)t[o]=-1;for(o=0;o<r.count();o++)t[r.get(n.storeDimIndex,o)]=o}})},w0=function(t,e,n){return To(t._getCategory(e,n),null)},b0=function(t,e){var n=t._idList[e];return n=null==(n=null==n&&null!=t._idDimIdx?w0(t,t._idDimIdx,e):n)?"e\0\0"+e:n},S0=function(t){return t=F(t)?t:null!=t?[t]:[]},T0=function(t){var e=new v(t._schema||I0(t.dimensions,t._getDimInfo,t),t.hostModel);return M0(e,t),e},M0=function(e,n){O(A0.concat(n.__wrappedMethods||[]),function(t){n.hasOwnProperty(t)&&(e[t]=n[t])}),e.__wrappedMethods=n.__wrappedMethods,O(L0,function(t){e[t]=y(n[t])}),e._calculationInfo=P({},n._calculationInfo)},void(C0=function(t,e){var n=t._nameList,i=t._idList,r=t._nameDimIdx,o=t._idDimIdx,a=n[e],s=i[e];null==a&&null!=r&&(n[e]=a=w0(t,r,e)),null==s&&null!=o&&(i[e]=s=w0(t,o,e)),null==s&&null!=a&&(s=a,1<(r=(n=t._nameRepeatCount)[a]=(n[a]||0)+1)&&(s+="__ec__"+r),i[e]=s)})),v);function v(t,e){this.type="list",this._dimOmitted=!1,this._nameList=[],this._idList=[],this._visual={},this._layout={},this._itemVisuals=[],this._itemLayouts=[],this._graphicEls=[],this._approximateExtent={},this._calculationInfo={},this.hasItemOption=!1,this.TRANSFERABLE_METHODS=["cloneShallow","downSample","lttbDownSample","map"],this.CHANGABLE_METHODS=["filterSelf","selectRange"];for(var n,i,r=!(this.DOWNSAMPLE_METHODS=["downSample","lttbDownSample"]),o=(m0(t)?(n=t.dimensions,this._dimOmitted=t.isDimensionOmitted(),this._schema=t):(r=!0,n=t),n=n||["x","y"],{}),a=[],s={},l=!1,u={},h=0;h<n.length;h++){var c=n[h],c=V(c)?new p0({name:c}):c instanceof p0?c:new p0(c),p=c.name,d=(c.type=c.type||"float",c.coordDim||(c.coordDim=p,c.coordDimIndex=0),c.otherDims=c.otherDims||{});a.push(p),null!=u[p]&&(l=!0),(o[p]=c).createInvertedIndices&&(s[p]=[]),0===d.itemName&&(this._nameDimIdx=h),0===d.itemId&&(this._idDimIdx=h),r&&(c.storeDimIndex=h)}this.dimensions=a,this._dimInfos=o,this._initGetDimensionInfo(l),this.hostModel=e,this._invertedIndicesMap=s,this._dimOmitted&&(i=this._dimIdxToName=E(),O(a,function(t){i.set(o[t].storeDimIndex,t)}))}function O0(t,e){Ad(t)||(t=Pd(t));for(var n,i,r=(e=e||{}).coordDimensions||[],o=e.dimensionsDefine||t.dimensionsDefine||[],a=E(),s=[],l=(u=t,n=r,p=e.dimensionsCount,i=Math.max(u.dimensionsDetectedCount||1,n.length,o.length,p||0),O(n,function(t){R(t)&&(t=t.dimsDef)&&(i=Math.max(i,t.length))}),i),u=e.canOmitUnusedDimensions&&30<l,h=o===t.dimensionsDefine,c=h?_0(t):v0(o),p=e.encodeDefine,d=E(p=!p&&e.encodeDefaulter?e.encodeDefaulter(t,l):p),f=new Bf(l),g=0;g<f.length;g++)f[g]=-1;function y(t){var e,n,i,r=f[t];return r<0?(e=R(e=o[t])?e:{name:e},n=new p0,null!=(i=e.name)&&null!=c.get(i)&&(n.name=n.displayName=i),null!=e.type&&(n.type=e.type),null!=e.displayName&&(n.displayName=e.displayName),f[t]=s.length,n.storeDimIndex=t,s.push(n),n):s[r]}if(!u)for(g=0;g<l;g++)y(g);d.each(function(t,n){var i,t=vo(t).slice();1===t.length&&!V(t[0])&&t[0]<0?d.set(n,!1):(i=d.set(n,[]),O(t,function(t,e){t=V(t)?c.get(t):t;null!=t&&t<l&&v(y(i[e]=t),n,e)}))});var m=0;function v(t,e,n){null!=Tp.get(e)?t.otherDims[e]=n:(t.coordDim=e,t.coordDimIndex=n,a.set(e,!0))}O(r,function(t){V(t)?(o=t,r={}):(o=(r=t).name,t=r.ordinalMeta,r.ordinalMeta=null,(r=P({},r)).ordinalMeta=t,n=r.dimsDef,i=r.otherDims,r.name=r.coordDim=r.coordDimIndex=r.dimsDef=r.otherDims=null);var n,i,r,o,e=d.get(o);if(!1!==e){if(!(e=vo(e)).length)for(var a=0;a<(n&&n.length||1);a++){for(;m<l&&null!=y(m).coordDim;)m++;m<l&&e.push(m++)}O(e,function(t,e){t=y(t);h&&null!=r.type&&(t.type=r.type),v(z(t,r),o,e),null==t.name&&n&&(R(e=n[e])||(e={name:e}),t.name=t.displayName=e.name,t.defaultTooltip=e.defaultTooltip),i&&z(t.otherDims,i)})}});var _=e.generateCoord,x=null!=(b=e.generateCoordCount),b=_?b||1:0,w=_||"value";function S(t){null==t.name&&(t.name=t.coordDim)}if(u)O(s,function(t){S(t)}),s.sort(function(t,e){return t.storeDimIndex-e.storeDimIndex});else for(var M=0;M<l;M++){var T=y(M);null==T.coordDim&&(T.coordDim=function(t,e,n){if(n||e.hasKey(t)){for(var i=0;e.hasKey(t+i);)i++;t+=i}return e.set(t,!0),t}(w,a,x),T.coordDimIndex=0,(!_||b<=0)&&(T.isExtraCoord=!0),b--),S(T),null!=T.type||Bp(t,M)!==Rp.Must&&(!T.isExtraCoord||null==T.otherDims.itemName&&null==T.otherDims.seriesName)||(T.type="ordinal")}for(var C=s,k=E(),I=0;I<C.length;I++){var D=C[I],A=D.name,L=k.get(A)||0;0<L&&(D.name=A+(L-1)),L++,k.set(A,L)}return new g0({source:t,dimensions:s,fullDimensionCount:l,dimensionOmitted:u})}var R0=function(t){this.coordSysDims=[],this.axisMap=E(),this.categoryAxisMap=E(),this.coordSysName=t};var N0={cartesian2d:function(t,e,n,i){var r=t.getReferringComponents("xAxis",Ro).models[0],t=t.getReferringComponents("yAxis",Ro).models[0];e.coordSysDims=["x","y"],n.set("x",r),n.set("y",t),E0(r)&&(i.set("x",r),e.firstCategoryDimIndex=0),E0(t)&&(i.set("y",t),null==e.firstCategoryDimIndex)&&(e.firstCategoryDimIndex=1)},singleAxis:function(t,e,n,i){t=t.getReferringComponents("singleAxis",Ro).models[0];e.coordSysDims=["single"],n.set("single",t),E0(t)&&(i.set("single",t),e.firstCategoryDimIndex=0)},polar:function(t,e,n,i){var t=t.getReferringComponents("polar",Ro).models[0],r=t.findAxisModel("radiusAxis"),t=t.findAxisModel("angleAxis");e.coordSysDims=["radius","angle"],n.set("radius",r),n.set("angle",t),E0(r)&&(i.set("radius",r),e.firstCategoryDimIndex=0),E0(t)&&(i.set("angle",t),null==e.firstCategoryDimIndex)&&(e.firstCategoryDimIndex=1)},geo:function(t,e,n,i){e.coordSysDims=["lng","lat"]},parallel:function(t,i,r,o){var a=t.ecModel,t=a.getComponent("parallel",t.get("parallelIndex")),s=i.coordSysDims=t.dimensions.slice();O(t.parallelAxisIndex,function(t,e){var t=a.getComponent("parallelAxis",t),n=s[e];r.set(n,t),E0(t)&&(o.set(n,t),null==i.firstCategoryDimIndex)&&(i.firstCategoryDimIndex=e)})}};function E0(t){return"category"===t.get("type")}function z0(t,e,n){var i,r,o,a,s,l,u,h,c,p=(n=n||{}).byIndex,d=n.stackedCoordDimension,f=(m0(e.schema)?(r=e.schema,i=r.dimensions,o=e.store):i=e,!(!t||!t.get("stack")));return O(i,function(t,e){V(t)&&(i[e]=t={name:t}),f&&!t.isExtraCoord&&(p||a||!t.ordinalMeta||(a=t),s||"ordinal"===t.type||"time"===t.type||d&&d!==t.coordDim||(s=t))}),!s||p||a||(p=!0),s&&(l="__\0ecstackresult_"+t.id,u="__\0ecstackedover_"+t.id,a&&(a.createInvertedIndices=!0),h=s.coordDim,n=s.type,c=0,O(i,function(t){t.coordDim===h&&c++}),e={name:l,coordDim:h,coordDimIndex:c,type:n,isExtraCoord:!0,isCalculationCoord:!0,storeDimIndex:i.length},t={name:u,coordDim:u,coordDimIndex:c+1,type:n,isExtraCoord:!0,isCalculationCoord:!0,storeDimIndex:i.length+1},r?(o&&(e.storeDimIndex=o.ensureCalculationDimension(u,n),t.storeDimIndex=o.ensureCalculationDimension(l,n)),r.appendCalculationDimension(e),r.appendCalculationDimension(t)):(i.push(e),i.push(t))),{stackedDimension:s&&s.name,stackedByDimension:a&&a.name,isStackedByIndex:p,stackedOverDimension:u,stackResultDimension:l}}function B0(t,e){return!!e&&e===t.getCalculationInfo("stackedDimension")}function F0(t,e){return B0(t,e)?t.getCalculationInfo("stackResultDimension"):e}function V0(t,e,n){n=n||{};var i,r,o,a,s,l,u=e.getSourceManager(),h=!1,t=(t?(h=!0,i=Pd(t)):h=(i=u.getSource()).sourceFormat===Cp,function(t){var e=t.get("coordinateSystem"),n=new R0(e);if(e=N0[e])return e(t,n,n.axisMap,n.categoryAxisMap),n}(e)),c=(r=t,c=(c=e).get("coordinateSystem"),c=nd.get(c),p=(p=r&&r.coordSysDims?B(r.coordSysDims,function(t){var e={name:t},t=r.axisMap.get(t);return t&&(t=t.get("type"),e.type="category"===(t=t)?"ordinal":"time"===t?"time":"float"),e}):p)||c&&(c.getDimensionsInfo?c.getDimensionsInfo():c.dimensions.slice())||["x","y"]),p=n.useEncodeDefaulter,p=D(p)?p:p?ct(Ep,c,e):null,c={coordDimensions:c,generateCoord:n.generateCoord,encodeDefine:e.getEncode(),encodeDefaulter:p,canOmitUnusedDimensions:!h},p=O0(i,c),d=(c=p.dimensions,o=n.createInvertedIndices,(a=t)&&O(c,function(t,e){var n=t.coordDim,n=a.categoryAxisMap.get(n);n&&(null==s&&(s=e),t.ordinalMeta=n.getOrdinalMeta(),o)&&(t.createInvertedIndices=!0),null!=t.otherDims.itemName&&(l=!0)}),l||null==s||(c[s].otherDims.itemName=0),s),n=h?null:u.getSharedDataStore(p),t=z0(e,{schema:p,store:n}),c=new P0(p,e),p=(c.setCalculationInfo(t),null==d||(u=i).sourceFormat!==Cp||F(bo(function(t){var e=0;for(;e<t.length&&null==t[e];)e++;return t[e]}(u.data||[])))?null:function(t,e,n,i){return i===d?n:this.defaultDimValueGetter(t,e,n,i)});return c.hasItemOption=!1,c.initData(h?i:n,null,p),c}G0.prototype.getSetting=function(t){return this._setting[t]},G0.prototype.unionExtent=function(t){var e=this._extent;t[0]<e[0]&&(e[0]=t[0]),t[1]>e[1]&&(e[1]=t[1])},G0.prototype.unionExtentFromData=function(t,e){this.unionExtent(t.getApproximateExtent(e))},G0.prototype.getExtent=function(){return this._extent.slice()},G0.prototype.setExtent=function(t,e){var n=this._extent;isNaN(t)||(n[0]=t),isNaN(e)||(n[1]=e)},G0.prototype.isInExtentRange=function(t){return this._extent[0]<=t&&this._extent[1]>=t},G0.prototype.isBlank=function(){return this._isBlank},G0.prototype.setBlank=function(t){this._isBlank=t};var H0=G0;function G0(t){this._setting=t||{},this._extent=[1/0,-1/0]}qo(H0);var W0=0,U0=(X0.createByAxisModel=function(t){var t=t.option,e=t.data,e=e&&B(e,Y0);return new X0({categories:e,needCollect:!e,deduplication:!1!==t.dedplication})},X0.prototype.getOrdinal=function(t){return this._getOrCreateMap().get(t)},X0.prototype.parseAndCollect=function(t){var e,n,i=this._needCollect;return V(t)||i?(i&&!this._deduplication?(n=this.categories.length,this.categories[n]=t):null==(n=(e=this._getOrCreateMap()).get(t))&&(i?(n=this.categories.length,this.categories[n]=t,e.set(t,n)):n=NaN),n):t},X0.prototype._getOrCreateMap=function(){return this._map||(this._map=E(this.categories))},X0);function X0(t){this.categories=t.categories||[],this._needCollect=t.needCollect,this._deduplication=t.deduplication,this.uid=++W0}function Y0(t){return R(t)&&null!=t.value?t.value:t+""}function q0(t){return"interval"===t.type||"log"===t.type}function Z0(t,e,n,i){var r={},o=t[1]-t[0],o=r.interval=co(o/e,!0),e=(null!=n&&o<n&&(o=r.interval=n),null!=i&&i<o&&(o=r.interval=i),r.intervalPrecision=K0(o)),n=r.niceTickExtent=[to(Math.ceil(t[0]/o)*o,e),to(Math.floor(t[1]/o)*o,e)];return i=n,o=t,isFinite(i[0])||(i[0]=o[0]),isFinite(i[1])||(i[1]=o[1]),$0(i,0,o),$0(i,1,o),i[0]>i[1]&&(i[0]=i[1]),r}function j0(t){var e=Math.pow(10,ho(t)),t=t/e;return t?2===t?t=3:3===t?t=5:t*=2:t=1,to(t*e)}function K0(t){return no(t)+2}function $0(t,e,n){t[e]=Math.max(Math.min(t[e],n[1]),n[0])}function Q0(t,e){return t>=e[0]&&t<=e[1]}function J0(t,e){return e[1]===e[0]?.5:(t-e[0])/(e[1]-e[0])}function tv(t,e){return t*(e[1]-e[0])+e[0]}u(iv,ev=H0),iv.prototype.parse=function(t){return null==t?NaN:V(t)?this._ordinalMeta.getOrdinal(t):Math.round(t)},iv.prototype.contain=function(t){return Q0(t=this.parse(t),this._extent)&&null!=this._ordinalMeta.categories[t]},iv.prototype.normalize=function(t){return J0(t=this._getTickNumber(this.parse(t)),this._extent)},iv.prototype.scale=function(t){return t=Math.round(tv(t,this._extent)),this.getRawOrdinalNumber(t)},iv.prototype.getTicks=function(){for(var t=[],e=this._extent,n=e[0];n<=e[1];)t.push({value:n}),n++;return t},iv.prototype.getMinorTicks=function(t){},iv.prototype.setSortInfo=function(t){if(null==t)this._ordinalNumbersByTick=this._ticksByOrdinalNumber=null;else{for(var e=t.ordinalNumbers,n=this._ordinalNumbersByTick=[],i=this._ticksByOrdinalNumber=[],r=0,o=this._ordinalMeta.categories.length,a=Math.min(o,e.length);r<a;++r){var s=e[r];i[n[r]=s]=r}for(var l=0;r<o;++r){for(;null!=i[l];)l++;n.push(l),i[l]=r}}},iv.prototype._getTickNumber=function(t){var e=this._ticksByOrdinalNumber;return e&&0<=t&&t<e.length?e[t]:t},iv.prototype.getRawOrdinalNumber=function(t){var e=this._ordinalNumbersByTick;return e&&0<=t&&t<e.length?e[t]:t},iv.prototype.getLabel=function(t){if(!this.isBlank())return t=this.getRawOrdinalNumber(t.value),null==(t=this._ordinalMeta.categories[t])?"":t+""},iv.prototype.count=function(){return this._extent[1]-this._extent[0]+1},iv.prototype.unionExtentFromData=function(t,e){this.unionExtent(t.getApproximateExtent(e))},iv.prototype.isInExtentRange=function(t){return t=this._getTickNumber(t),this._extent[0]<=t&&this._extent[1]>=t},iv.prototype.getOrdinalMeta=function(){return this._ordinalMeta},iv.prototype.calcNiceTicks=function(){},iv.prototype.calcNiceExtent=function(){},iv.type="ordinal";var ev,nv=iv;function iv(t){var t=ev.call(this,t)||this,e=(t.type="ordinal",t.getSetting("ordinalMeta"));return F(e=e||new U0({}))&&(e=new U0({categories:B(e,function(t){return R(t)?t.value:t})})),t._ordinalMeta=e,t._extent=t.getSetting("extent")||[0,e.categories.length-1],t}H0.registerClass(nv);var rv,ov=to,av=(u(sv,rv=H0),sv.prototype.parse=function(t){return t},sv.prototype.contain=function(t){return Q0(t,this._extent)},sv.prototype.normalize=function(t){return J0(t,this._extent)},sv.prototype.scale=function(t){return tv(t,this._extent)},sv.prototype.setExtent=function(t,e){var n=this._extent;isNaN(t)||(n[0]=parseFloat(t)),isNaN(e)||(n[1]=parseFloat(e))},sv.prototype.unionExtent=function(t){var e=this._extent;t[0]<e[0]&&(e[0]=t[0]),t[1]>e[1]&&(e[1]=t[1]),this.setExtent(e[0],e[1])},sv.prototype.getInterval=function(){return this._interval},sv.prototype.setInterval=function(t){this._interval=t,this._niceExtent=this._extent.slice(),this._intervalPrecision=K0(t)},sv.prototype.getTicks=function(t){var e=this._interval,n=this._extent,i=this._niceExtent,r=this._intervalPrecision,o=[];if(e){n[0]<i[0]&&o.push(t?{value:ov(i[0]-e,r)}:{value:n[0]});for(var a=i[0];a<=i[1]&&(o.push({value:a}),(a=ov(a+e,r))!==o[o.length-1].value);)if(1e4<o.length)return[];var s=o.length?o[o.length-1].value:i[1];n[1]>s&&o.push(t?{value:ov(s+e,r)}:{value:n[1]})}return o},sv.prototype.getMinorTicks=function(t){for(var e=this.getTicks(!0),n=[],i=this.getExtent(),r=1;r<e.length;r++){for(var o=e[r],a=e[r-1],s=0,l=[],u=(o.value-a.value)/t;s<t-1;){var h=ov(a.value+(s+1)*u);h>i[0]&&h<i[1]&&l.push(h),s++}n.push(l)}return n},sv.prototype.getLabel=function(t,e){return null==t?"":(null==(e=e&&e.precision)?e=no(t.value)||0:"auto"===e&&(e=this._intervalPrecision),ip(ov(t.value,e,!0)))},sv.prototype.calcNiceTicks=function(t,e,n){t=t||5;var i=this._extent,r=i[1]-i[0];isFinite(r)&&(r<0&&i.reverse(),r=Z0(i,t,e,n),this._intervalPrecision=r.intervalPrecision,this._interval=r.interval,this._niceExtent=r.niceTickExtent)},sv.prototype.calcNiceExtent=function(t){var e=this._extent,n=(e[0]===e[1]&&(0!==e[0]?(n=Math.abs(e[0]),t.fixMax||(e[1]+=n/2),e[0]-=n/2):e[1]=1),e[1]-e[0]),n=(isFinite(n)||(e[0]=0,e[1]=1),this.calcNiceTicks(t.splitNumber,t.minInterval,t.maxInterval),this._interval);t.fixMin||(e[0]=ov(Math.floor(e[0]/n)*n)),t.fixMax||(e[1]=ov(Math.ceil(e[1]/n)*n))},sv.prototype.setNiceExtent=function(t,e){this._niceExtent=[t,e]},sv.type="interval",sv);function sv(){var t=null!==rv&&rv.apply(this,arguments)||this;return t.type="interval",t._interval=0,t._intervalPrecision=2,t}H0.registerClass(av);var lv="undefined"!=typeof Float32Array,uv=lv?Float32Array:Array;function hv(t){return F(t)?lv?new Float32Array(t):t:new uv(t)}function cv(t){return t.get("stack")||"__ec_stack_"+t.seriesIndex}function pv(t){return t.dim+t.index}function dv(t,e){var n=[];return e.eachSeriesByType(t,function(t){var e;(e=t).coordinateSystem&&"cartesian2d"===e.coordinateSystem.type&&n.push(t)}),n}function fv(t){var a,d,u=function(t){var e,l={},n=(O(t,function(t){var e=t.coordinateSystem.getBaseAxis();if("time"===e.type||"value"===e.type)for(var t=t.getData(),n=e.dim+"_"+e.index,i=t.getDimensionIndex(t.mapDimension(e.dim)),r=t.getStore(),o=0,a=r.count();o<a;++o){var s=r.get(i,o);l[n]?l[n].push(s):l[n]=[s]}}),{});for(e in l)if(l.hasOwnProperty(e)){var i=l[e];if(i){i.sort(function(t,e){return t-e});for(var r=null,o=1;o<i.length;++o){var a=i[o]-i[o-1];0<a&&(r=null===r?a:Math.min(r,a))}n[e]=r}}return n}(t),h=[];return O(t,function(t){var e,n,i=t.coordinateSystem.getBaseAxis(),r=i.getExtent(),o=(e="category"===i.type?i.getBandWidth():"value"===i.type||"time"===i.type?(e=i.dim+"_"+i.index,e=u[e],o=Math.abs(r[1]-r[0]),n=i.scale.getExtent(),n=Math.abs(n[1]-n[0]),e?o/n*e:o):(n=t.getData(),Math.abs(r[1]-r[0])/n.count()),Jr(t.get("barWidth"),e)),r=Jr(t.get("barMaxWidth"),e),a=Jr(t.get("barMinWidth")||((n=t).pipelineContext&&n.pipelineContext.large?.5:1),e),s=t.get("barGap"),l=t.get("barCategoryGap");h.push({bandWidth:e,barWidth:o,barMaxWidth:r,barMinWidth:a,barGap:s,barCategoryGap:l,axisKey:pv(i),stackId:cv(t)})}),a={},O(h,function(t,e){var n=t.axisKey,i=t.bandWidth,i=a[n]||{bandWidth:i,remainedWidth:i,autoWidthCount:0,categoryGap:null,gap:"20%",stacks:{}},r=i.stacks,n=(a[n]=i,t.stackId),o=(r[n]||i.autoWidthCount++,r[n]=r[n]||{width:0,maxWidth:0},t.barWidth),o=(o&&!r[n].width&&(r[n].width=o,o=Math.min(i.remainedWidth,o),i.remainedWidth-=o),t.barMaxWidth),o=(o&&(r[n].maxWidth=o),t.barMinWidth),r=(o&&(r[n].minWidth=o),t.barGap),n=(null!=r&&(i.gap=r),t.barCategoryGap);null!=n&&(i.categoryGap=n)}),d={},O(a,function(t,n){d[n]={};var i,e=t.stacks,r=t.bandWidth,o=t.categoryGap,a=(null==o&&(a=I(e).length,o=Math.max(35-4*a,15)+"%"),Jr(o,r)),s=Jr(t.gap,1),l=t.remainedWidth,u=t.autoWidthCount,h=(l-a)/(u+(u-1)*s),h=Math.max(h,0),c=(O(e,function(t){var e,n=t.maxWidth,i=t.minWidth;t.width?(e=t.width,n&&(e=Math.min(e,n)),i&&(e=Math.max(e,i)),t.width=e,l-=e+s*e,u--):(e=h,n&&n<e&&(e=Math.min(n,l)),(e=i&&e<i?i:e)!==h&&(t.width=e,l-=e+s*e,u--))}),h=(l-a)/(u+(u-1)*s),h=Math.max(h,0),0),p=(O(e,function(t,e){t.width||(t.width=h),c+=(i=t).width*(1+s)}),i&&(c-=i.width*s),-c/2);O(e,function(t,e){d[n][e]=d[n][e]||{bandWidth:r,offset:p,width:t.width},p+=t.width*(1+s)})}),d}u(mv,gv=av),mv.prototype.getLabel=function(t){var e=this.getSetting("useUTC");return Hc(t.value,Ec[function(t){switch(t){case"year":case"month":return"day";case"millisecond":return"millisecond";default:return"second"}}(Vc(this._minLevelUnit))]||Ec.second,e,this.getSetting("locale"))},mv.prototype.getFormattedLabel=function(t,e,n){var i=this.getSetting("useUTC"),r=this.getSetting("locale"),o=null;if(V(n))o=n;else if(D(n))o=n(t.value,e,{level:t.level});else{var a=P({},Rc);if(0<t.level)for(var s=0;s<zc.length;++s)a[zc[s]]="{primary|"+a[zc[s]]+"}";var l=n?!1===n.inherit?n:z(n,a):a,u=Gc(t.value,i);if(l[u])o=l[u];else if(l.inherit){for(s=Bc.indexOf(u)-1;0<=s;--s)if(l[u]){o=l[u];break}o=o||a.none}F(o)&&(e=null==t.level?0:0<=t.level?t.level:o.length+t.level,o=o[e=Math.min(e,o.length-1)])}return Hc(new Date(t.value),o,i,r)},mv.prototype.getTicks=function(){var t=this._interval,e=this._extent,n=[];return t&&(n.push({value:e[0],level:0}),t=this.getSetting("useUTC"),t=function(t,w,S,M){var e=Bc,n=0;function i(t,e,n){var i=[],r=!e.length;if(!function(t,e,n,i){function r(t){return Wc(c,t,i)===Wc(p,t,i)}function o(){return r("year")}function a(){return o()&&r("month")}function s(){return a()&&r("day")}function l(){return s()&&r("hour")}function u(){return l()&&r("minute")}function h(){return u()&&r("second")}var c=lo(e),p=lo(n);switch(t){case"year":return o();case"month":return a();case"day":return s();case"hour":return l();case"minute":return u();case"second":return h();case"millisecond":return h()&&r("millisecond")}}(Vc(t),M[0],M[1],S)){r&&(e=[{value:function(t,e,n){var i=new Date(t);switch(Vc(e)){case"year":case"month":i[$c(n)](0);case"day":i[Qc(n)](1);case"hour":i[Jc(n)](0);case"minute":i[tp(n)](0);case"second":i[ep(n)](0),i[np(n)](0)}return i.getTime()}(new Date(M[0]),t,S)},{value:M[1]}]);for(var o,a,s=0;s<e.length-1;s++){var l=e[s].value,u=e[s+1].value;if(l!==u){var h=void 0,c=void 0,p=void 0;switch(t){case"year":h=Math.max(1,Math.round(w/Oc/365)),c=Uc(S),p=S?"setUTCFullYear":"setFullYear";break;case"half-year":case"quarter":case"month":a=w,h=6<(a/=30*Oc)?6:3<a?3:2<a?2:1,c=Xc(S),p=$c(S);break;case"week":case"half-week":case"day":a=w,h=16<(a/=Oc)?16:7.5<a?7:3.5<a?4:1.5<a?2:1,c=Yc(S),p=Qc(S),0;break;case"half-day":case"quarter-day":case"hour":o=w,h=12<(o/=Pc)?12:6<o?6:3.5<o?4:2<o?2:1,c=qc(S),p=Jc(S);break;case"minute":h=_v(w,!0),c=Zc(S),p=tp(S);break;case"second":h=_v(w,!1),c=jc(S),p=ep(S);break;case"millisecond":h=co(w,!0),c=Kc(S),p=np(S)}b=x=_=v=m=y=g=f=d=void 0;for(var d=h,f=l,g=u,y=c,m=p,v=i,_=new Date(f),x=f,b=_[y]();x<g&&x<=M[1];)v.push({value:x}),_[m](b+=d),x=_.getTime();v.push({value:x,notAdd:!0}),"year"===t&&1<n.length&&0===s&&n.unshift({value:n[0].value-h})}}for(s=0;s<i.length;s++)n.push(i[s])}}for(var r=[],o=[],a=0,s=0,l=0;l<e.length&&n++<1e4;++l){var u=Vc(e[l]);if(function(t){return t===Vc(t)}(e[l])){i(e[l],r[r.length-1]||[],o);var h=e[l+1]?Vc(e[l+1]):null;if(u!==h){if(o.length){s=a,o.sort(function(t,e){return t.value-e.value});for(var c=[],p=0;p<o.length;++p){var d=o[p].value;0!==p&&o[p-1].value===d||(c.push(o[p]),d>=M[0]&&d<=M[1]&&a++)}u=(M[1]-M[0])/w;if(1.5*u<a&&u/1.5<s)break;if(r.push(c),u<a||t===e[l])break}o=[]}}}for(var f=ut(B(r,function(t){return ut(t,function(t){return t.value>=M[0]&&t.value<=M[1]&&!t.notAdd})}),function(t){return 0<t.length}),g=[],y=f.length-1,l=0;l<f.length;++l)for(var m=f[l],v=0;v<m.length;++v)g.push({value:m[v].value,level:y-l});g.sort(function(t,e){return t.value-e.value});for(var _=[],l=0;l<g.length;++l)0!==l&&g[l].value===g[l-1].value||_.push(g[l]);return _}(this._minLevelUnit,this._approxInterval,t,e),(n=n.concat(t)).push({value:e[1],level:0})),n},mv.prototype.calcNiceExtent=function(t){var e,n=this._extent;n[0]===n[1]&&(n[0]-=Oc,n[1]+=Oc),n[1]===-1/0&&n[0]===1/0&&(e=new Date,n[1]=+new Date(e.getFullYear(),e.getMonth(),e.getDate()),n[0]=n[1]-Oc),this.calcNiceTicks(t.splitNumber,t.minInterval,t.maxInterval)},mv.prototype.calcNiceTicks=function(t,e,n){var i=this._extent,i=i[1]-i[0],i=(this._approxInterval=i/(t=t||10),null!=e&&this._approxInterval<e&&(this._approxInterval=e),null!=n&&this._approxInterval>n&&(this._approxInterval=n),vv.length),t=Math.min(function(t,e,n,i){for(;n<i;){var r=n+i>>>1;t[r][1]<e?n=1+r:i=r}return n}(vv,this._approxInterval,0,i),i-1);this._interval=vv[t][1],this._minLevelUnit=vv[Math.max(t-1,0)][0]},mv.prototype.parse=function(t){return dt(t)?t:+lo(t)},mv.prototype.contain=function(t){return Q0(this.parse(t),this._extent)},mv.prototype.normalize=function(t){return J0(this.parse(t),this._extent)},mv.prototype.scale=function(t){return tv(t,this._extent)},mv.type="time";var gv,yv=mv;function mv(t){t=gv.call(this,t)||this;return t.type="time",t}var vv=[["second",Ac],["minute",Lc],["hour",Pc],["quarter-day",6*Pc],["half-day",12*Pc],["day",1.2*Oc],["half-week",3.5*Oc],["week",7*Oc],["month",31*Oc],["quarter",95*Oc],["half-year",Uo/2],["year",Uo]];function _v(t,e){return 30<(t/=e?Lc:Ac)?30:20<t?20:15<t?15:10<t?10:5<t?5:2<t?2:1}H0.registerClass(yv);var xv,bv=H0.prototype,wv=av.prototype,Sv=to,Mv=Math.floor,Tv=Math.ceil,Cv=Math.pow,kv=Math.log,Iv=(u(Dv,xv=H0),Dv.prototype.getTicks=function(t){var e=this._originalScale,n=this._extent,i=e.getExtent();return B(wv.getTicks.call(this,t),function(t){var t=t.value,e=to(Cv(this.base,t)),e=t===n[0]&&this._fixMin?Av(e,i[0]):e;return{value:t===n[1]&&this._fixMax?Av(e,i[1]):e}},this)},Dv.prototype.setExtent=function(t,e){var n=kv(this.base);t=kv(Math.max(0,t))/n,e=kv(Math.max(0,e))/n,wv.setExtent.call(this,t,e)},Dv.prototype.getExtent=function(){var t=this.base,e=bv.getExtent.call(this);e[0]=Cv(t,e[0]),e[1]=Cv(t,e[1]);t=this._originalScale.getExtent();return this._fixMin&&(e[0]=Av(e[0],t[0])),this._fixMax&&(e[1]=Av(e[1],t[1])),e},Dv.prototype.unionExtent=function(t){this._originalScale.unionExtent(t);var e=this.base;t[0]=kv(t[0])/kv(e),t[1]=kv(t[1])/kv(e),bv.unionExtent.call(this,t)},Dv.prototype.unionExtentFromData=function(t,e){this.unionExtent(t.getApproximateExtent(e))},Dv.prototype.calcNiceTicks=function(t){t=t||10;var e=this._extent,n=e[1]-e[0];if(!(n==1/0||n<=0)){var i=uo(n);for(t/n*i<=.5&&(i*=10);!isNaN(i)&&Math.abs(i)<1&&0<Math.abs(i);)i*=10;t=[to(Tv(e[0]/i)*i),to(Mv(e[1]/i)*i)];this._interval=i,this._niceExtent=t}},Dv.prototype.calcNiceExtent=function(t){wv.calcNiceExtent.call(this,t),this._fixMin=t.fixMin,this._fixMax=t.fixMax},Dv.prototype.parse=function(t){return t},Dv.prototype.contain=function(t){return Q0(t=kv(t)/kv(this.base),this._extent)},Dv.prototype.normalize=function(t){return J0(t=kv(t)/kv(this.base),this._extent)},Dv.prototype.scale=function(t){return t=tv(t,this._extent),Cv(this.base,t)},Dv.type="log",Dv);function Dv(){var t=null!==xv&&xv.apply(this,arguments)||this;return t.type="log",t.base=10,t._originalScale=new av,t._interval=0,t}ey=Iv.prototype;function Av(t,e){return Sv(t,no(e))}ey.getMinorTicks=wv.getMinorTicks,ey.getLabel=wv.getLabel,H0.registerClass(Iv);Pv.prototype._prepareParams=function(t,e,n){n[1]<n[0]&&(n=[NaN,NaN]),this._dataMin=n[0],this._dataMax=n[1];var i=this._isOrdinal="ordinal"===t.type,r=(this._needCrossZero="interval"===t.type&&e.getNeedCrossZero&&e.getNeedCrossZero(),e.get("min",!0)),r=(null==r&&(r=e.get("startValue",!0)),this._modelMinRaw=r),r=(D(r)?this._modelMinNum=Nv(t,r({min:n[0],max:n[1]})):"dataMin"!==r&&(this._modelMinNum=Nv(t,r)),this._modelMaxRaw=e.get("max",!0));D(r)?this._modelMaxNum=Nv(t,r({min:n[0],max:n[1]})):"dataMax"!==r&&(this._modelMaxNum=Nv(t,r)),i?this._axisDataLen=e.getCategories().length:"boolean"==typeof(t=F(n=e.get("boundaryGap"))?n:[n||0,n||0])[0]||"boolean"==typeof t[1]?this._boundaryGapInner=[0,0]:this._boundaryGapInner=[Cr(t[0],1),Cr(t[1],1)]},Pv.prototype.calculate=function(){var t=this._isOrdinal,e=this._dataMin,n=this._dataMax,i=this._axisDataLen,r=this._boundaryGapInner,o=t?null:n-e||Math.abs(e),a="dataMin"===this._modelMinRaw?e:this._modelMinNum,s="dataMax"===this._modelMaxRaw?n:this._modelMaxNum,l=null!=a,u=null!=s,e=(null==a&&(a=t?i?0:NaN:e-r[0]*o),null==s&&(s=t?i?i-1:NaN:n+r[1]*o),null!=a&&isFinite(a)||(a=NaN),null!=s&&isFinite(s)||(s=NaN),xt(a)||xt(s)||t&&!i),n=(this._needCrossZero&&(a=0<a&&0<s&&!l?0:a)<0&&s<0&&!u&&(s=0),this._determinedMin),r=this._determinedMax;return null!=n&&(a=n,l=!0),null!=r&&(s=r,u=!0),{min:a,max:s,minFixed:l,maxFixed:u,isBlank:e}},Pv.prototype.modifyDataMinMax=function(t,e){this[Rv[t]]=e},Pv.prototype.setDeterminedMinMax=function(t,e){this[Ov[t]]=e},Pv.prototype.freeze=function(){this.frozen=!0};var Lv=Pv;function Pv(t,e,n){this._prepareParams(t,e,n)}var Ov={min:"_determinedMin",max:"_determinedMax"},Rv={min:"_dataMin",max:"_dataMax"};function Nv(t,e){return null==e?null:xt(e)?NaN:t.parse(e)}function Ev(t,e){var n,i,r,o,a,s=t.type,l=(l=e,u=(h=t).getExtent(),(c=h.rawExtentInfo)||(c=new Lv(h,l,u),h.rawExtentInfo=c),c.calculate()),u=(t.setBlank(l.isBlank),l.min),h=l.max,c=e.ecModel;return c&&"time"===s&&(t=dv("bar",c),n=!1,O(t,function(t){n=n||t.getBaseAxis()===e.axis}),n)&&(s=fv(t),c=u,t=h,s=s,a=(a=(i=e).axis.getExtent())[1]-a[0],void 0!==(s=function(t,e,n){if(t&&e)return null!=(t=t[pv(e)])&&null!=n?t[cv(n)]:t}(s,i.axis))&&(r=1/0,O(s,function(t){r=Math.min(t.offset,r)}),o=-1/0,O(s,function(t){o=Math.max(t.offset+t.width,o)}),r=Math.abs(r),o=Math.abs(o),t+=o/(i=r+o)*(a=(s=t-c)/(1-(r+o)/a)-s),c-=r/i*a),u=(s={min:c,max:t}).min,h=s.max),{extent:[u,h],fixMin:l.minFixed,fixMax:l.maxFixed}}function zv(t,e){var n=Ev(t,e),i=n.extent,r=e.get("splitNumber"),o=(t instanceof Iv&&(t.base=e.get("logBase")),t.type),a=e.get("interval"),o="interval"===o||"time"===o;t.setExtent(i[0],i[1]),t.calcNiceExtent({splitNumber:r,fixMin:n.fixMin,fixMax:n.fixMax,minInterval:o?e.get("minInterval"):null,maxInterval:o?e.get("maxInterval"):null}),null!=a&&t.setInterval&&t.setInterval(a)}function Bv(t,e){if(e=e||t.get("type"))switch(e){case"category":return new nv({ordinalMeta:t.getOrdinalMeta?t.getOrdinalMeta():t.getCategories(),extent:[1/0,-1/0]});case"time":return new yv({locale:t.ecModel.getLocaleModel(),useUTC:t.ecModel.get("useUTC")});default:return new(H0.getClass(e)||av)}}function Fv(r){var o,e,n,t=r.getLabelModel().get("formatter"),a="category"===r.type?r.scale.getExtent()[0]:null;return"time"===r.scale.type?(n=t,function(t,e){return r.scale.getFormattedLabel(t,e,n)}):V(t)?(e=t,function(t){t=r.scale.getLabel(t);return e.replace("{value}",null!=t?t:"")}):D(t)?(o=t,function(t,e){return null!=a&&(e=t.value-a),o((i=t,"category"===(n=r).type?n.scale.getLabel(i):i.value),e,null!=t.level?{level:t.level}:null);var n,i}):function(t){return r.scale.getLabel(t)}}function Vv(t){var e=t.model,n=t.scale;if(e.get(["axisLabel","show"])&&!n.isBlank()){var i,r,o=n.getExtent(),a=n instanceof nv?n.count():(i=n.getTicks()).length,s=t.getLabelModel(),l=Fv(t),u=1;40<a&&(u=Math.ceil(a/40));for(var h,c,p,d=0;d<a;d+=u){var f=l(i?i[d]:{value:o[0]+d},d),f=s.getTextRect(f),g=(f=f,h=s.get("rotate")||0,c=p=g=c=void 0,h=h*Math.PI/180,c=f.width,g=f.height,p=c*Math.abs(Math.cos(h))+Math.abs(g*Math.sin(h)),c=c*Math.abs(Math.sin(h))+Math.abs(g*Math.cos(h)),new X(f.x,f.y,p,c));r?r.union(g):r=g}return r}}function Hv(t){t=t.get("interval");return null==t?"auto":t}function Gv(t){return"category"===t.type&&0===Hv(t.getLabelModel())}Uv.prototype.getNeedCrossZero=function(){return!this.option.scale},Uv.prototype.getCoordSysModel=function(){};var Wv=Uv;function Uv(){}var oy=Object.freeze({__proto__:null,createDimensions:function(t,e){return O0(t,e).dimensions},createList:function(t){return V0(null,t)},createScale:function(t,e){var n=e;return(e=Bv(n=e instanceof _c?n:new _c(e))).setExtent(t[0],t[1]),zv(e,n),e},createSymbol:cy,createTextStyle:function(t,e){return $h(t,null,null,"normal"!==(e=e||{}).state)},dataStack:{isDimensionStacked:B0,enableDataStack:z0,getStackedDimension:F0},enableHoverEmphasis:Ol,getECData:Us,getLayoutRect:dp,mixinAxisModelCommonMethods:function(t){at(t,Wv)}}),Xv=[],Yv={registerPreprocessor:Um,registerProcessor:Xm,registerPostInit:Ym,registerPostUpdate:qm,registerUpdateLifecycle:Zm,registerAction:jm,registerCoordinateSystem:Km,registerLayout:$m,registerVisual:Qm,registerTransform:i0,registerLoading:e0,registerMap:n0,registerImpl:function(t,e){Xy[t]=e},PRIORITY:ry,ComponentModel:g,ComponentView:ug,SeriesModel:eg,ChartView:fg,registerComponentModel:function(t){g.registerClass(t)},registerComponentView:function(t){ug.registerClass(t)},registerSeriesModel:function(t){eg.registerClass(t)},registerChartView:function(t){fg.registerClass(t)},registerSubTypeDefaulter:function(t,e){g.registerSubTypeDefaulter(t,e)},registerPainter:function(t,e){Zr(t,e)}};function qv(t){F(t)?O(t,function(t){qv(t)}):0<=k(Xv,t)||(Xv.push(t),(t=D(t)?{install:t}:t).install(Yv))}var Zv=1e-8;function jv(t,e){return Math.abs(t-e)<Zv}function Kv(t,e,n){var i=0,r=t[0];if(r){for(var o=1;o<t.length;o++){var a=t[o];i+=Ja(r[0],r[1],a[0],a[1],e,n),r=a}var s=t[0];return jv(r[0],s[0])&&jv(r[1],s[1])||(i+=Ja(r[0],r[1],s[0],s[1],e,n)),0!==i}}var $v=[];function Qv(t,e){for(var n=0;n<t.length;n++)ee(t[n],t[n],e)}function Jv(t,e,n,i){for(var r=0;r<t.length;r++){var o=t[r];(o=i?i.project(o):o)&&isFinite(o[0])&&isFinite(o[1])&&(ne(e,e,o),ie(n,n,o))}}t_.prototype.setCenter=function(t){this._center=t},t_.prototype.getCenter=function(){return this._center||(this._center=this.calcCenter())};ay=t_;function t_(t){this.name=t}var e_,n_,i_=function(t,e){this.type="polygon",this.exterior=t,this.interiors=e},r_=function(t){this.type="linestring",this.points=t},o_=(u(a_,e_=ay),a_.prototype.calcCenter=function(){for(var t,e=this.geometries,n=0,i=0;i<e.length;i++){var r=e[i],o=r.exterior,o=o&&o.length;n<o&&(t=r,n=o)}if(t){for(var a=t.exterior,s=0,l=0,u=0,h=a.length,c=a[h-1][0],p=a[h-1][1],d=0;d<h;d++){var f=a[d][0],g=a[d][1],y=c*g-f*p;s+=y,l+=(c+f)*y,u+=(p+g)*y,c=f,p=g}return s?[l/s/3,u/s/3,s]:[a[0][0]||0,a[0][1]||0]}var m=this.getBoundingRect();return[m.x+m.width/2,m.y+m.height/2]},a_.prototype.getBoundingRect=function(e){var n,i,t=this._rect;return t&&!e||(n=[1/0,1/0],i=[-1/0,-1/0],O(this.geometries,function(t){"polygon"===t.type?Jv(t.exterior,n,i,e):O(t.points,function(t){Jv(t,n,i,e)})}),isFinite(n[0])&&isFinite(n[1])&&isFinite(i[0])&&isFinite(i[1])||(n[0]=n[1]=i[0]=i[1]=0),t=new X(n[0],n[1],i[0]-n[0],i[1]-n[1]),e)||(this._rect=t),t},a_.prototype.contain=function(t){var e=this.getBoundingRect(),n=this.geometries;if(e.contain(t[0],t[1]))t:for(var i=0,r=n.length;i<r;i++){var o=n[i];if("polygon"===o.type){var a=o.exterior,s=o.interiors;if(Kv(a,t[0],t[1])){for(var l=0;l<(s?s.length:0);l++)if(Kv(s[l],t[0],t[1]))continue t;return!0}}}return!1},a_.prototype.transformTo=function(t,e,n,i){for(var r=this.getBoundingRect(),o=r.width/r.height,o=(n?i=i||n/o:n=o*i,new X(t,e,n,i)),a=r.calculateTransform(o),s=this.geometries,l=0;l<s.length;l++){var u=s[l];"polygon"===u.type?(Qv(u.exterior,a),O(u.interiors,function(t){Qv(t,a)})):O(u.points,function(t){Qv(t,a)})}(r=this._rect).copy(o),this._center=[r.x+r.width/2,r.y+r.height/2]},a_.prototype.cloneShallow=function(t){t=new a_(t=null==t?this.name:t,this.geometries,this._center);return t._rect=this._rect,t.transformTo=null,t},a_);function a_(t,e,n){t=e_.call(this,t)||this;return t.type="geoJSON",t.geometries=e,t._center=n&&[n[0],n[1]],t}function s_(t,e){t=n_.call(this,t)||this;return t.type="geoSVG",t._elOnlyForCalculate=e,t}function l_(t,e,n){for(var i=0;i<t.length;i++)t[i]=u_(t[i],e[i],n)}function u_(t,e,n){for(var i=[],r=e[0],o=e[1],a=0;a<t.length;a+=2){var s=(s=t.charCodeAt(a)-64)>>1^-(1&s),l=(l=t.charCodeAt(a+1)-64)>>1^-(1&l);i.push([(r=s+=r)/n,(o=l+=o)/n])}return i}function h_(t,o){var e,n,r;return B(ut((t=(e=t).UTF8Encoding?(null==(r=(n=e).UTF8Scale)&&(r=1024),O(n.features,function(t){var e=t.geometry,n=e.encodeOffsets,i=e.coordinates;if(n)switch(e.type){case"LineString":e.coordinates=u_(i,n,r);break;case"Polygon":case"MultiLineString":l_(i,n,r);break;case"MultiPolygon":O(i,function(t,e){return l_(t,n[e],r)})}}),n.UTF8Encoding=!1,n):e).features,function(t){return t.geometry&&t.properties&&0<t.geometry.coordinates.length}),function(t){var e=t.properties,n=t.geometry,i=[];switch(n.type){case"Polygon":var r=n.coordinates;i.push(new i_(r[0],r.slice(1)));break;case"MultiPolygon":O(n.coordinates,function(t){t[0]&&i.push(new i_(t[0],t.slice(1)))});break;case"LineString":i.push(new r_([n.coordinates]));break;case"MultiLineString":i.push(new r_(n.coordinates))}t=new o_(e[o||"name"],i,e.cp);return t.properties=e,t})}u(s_,n_=ay),s_.prototype.calcCenter=function(){for(var t=this._elOnlyForCalculate,e=t.getBoundingRect(),e=[e.x+e.width/2,e.y+e.height/2],n=Pe($v),i=t;i&&!i.isGeoSVGGraphicRoot;)Re(n,i.getLocalTransform(),n),i=i.parent;return Be(n,n),ee(e,e,n),e};var pc=Object.freeze({__proto__:null,MAX_SAFE_INTEGER:9007199254740991,asc:eo,getPercentWithPrecision:function(t,e,n){return t[e]&&function(t,e){var n=lt(t,function(t,e){return t+(isNaN(e)?0:e)},0);if(0===n)return[];var i=Math.pow(10,e),e=B(t,function(t){return(isNaN(t)?0:t)/n*i*100}),r=100*i,o=B(e,function(t){return Math.floor(t)}),a=lt(o,function(t,e){return t+e},0),s=B(e,function(t,e){return t-o[e]});for(;a<r;){for(var l=Number.NEGATIVE_INFINITY,u=null,h=0,c=s.length;h<c;++h)s[h]>l&&(l=s[h],u=h);++o[u],s[u]=0,++a}return B(o,function(t){return t/i})}(t,n)[e]||0},getPixelPrecision:ro,getPrecision:no,getPrecisionSafe:io,isNumeric:fo,isRadianAroundZero:ao,linearMap:Qr,nice:co,numericToNumber:po,parseDate:lo,quantile:function(t,e){var e=(t.length-1)*e+1,n=Math.floor(e),i=+t[n-1];return(e=e-n)?i+e*(t[n]-i):i},quantity:uo,quantityExponent:ho,reformIntervals:function(t){t.sort(function(t,e){return function t(e,n,i){return e.interval[i]<n.interval[i]||e.interval[i]===n.interval[i]&&(e.close[i]-n.close[i]==(i?-1:1)||!i&&t(e,n,1))}(t,e,0)?-1:1});for(var e=-1/0,n=1,i=0;i<t.length;){for(var r=t[i].interval,o=t[i].close,a=0;a<2;a++)r[a]<=e&&(r[a]=e,o[a]=a?1:1-n),e=r[a],n=o[a];r[0]===r[1]&&o[0]*o[1]!=1?t.splice(i,1):i++}return t},remRadian:oo,round:to}),Ko=Object.freeze({__proto__:null,format:Hc,parse:lo}),yc=Object.freeze({__proto__:null,Arc:rh,BezierCurve:th,BoundingRect:X,Circle:uu,CompoundPath:sh,Ellipse:du,Group:Vr,Image:_s,IncrementalDisplayable:n,Line:Zu,LinearGradient:ph,Polygon:Fu,Polyline:Wu,RadialGradient:uh,Rect:ks,Ring:Ru,Sector:Au,Text:Ps,clipPointsByRect:function(t,n){return B(t,function(t){var e=t[0],e=Lh(e,n.x),t=(e=Ph(e,n.x+n.width),t[1]),t=Lh(t,n.y);return[e,Ph(t,n.y+n.height)]})},clipRectByRect:function(t,e){var n=Lh(t.x,e.x),i=Ph(t.x+t.width,e.x+e.width),r=Lh(t.y,e.y),t=Ph(t.y+t.height,e.y+e.height);if(n<=i&&r<=t)return{x:n,y:r,width:i-n,height:t-r}},createIcon:Wh,extendPath:function(t,e){return Rh(t,e)},extendShape:function(t){return j.extend(t)},getShapeClass:function(t){if(Oh.hasOwnProperty(t))return Oh[t]},getTransform:function(t,e){for(var n=Pe([]);t&&t!==e;)Re(n,t.getLocalTransform(),n),t=t.parent;return n},initProps:Ih,makeImage:zh,makePath:Eh,mergePath:Fh,registerShape:Nh,resizePath:Vh,updateProps:kh}),fc=Object.freeze({__proto__:null,addCommas:ip,capitalFirst:function(t){return t&&t.charAt(0).toUpperCase()+t.substr(1)},encodeHTML:ve,formatTime:function(t,e,n){"week"!==t&&"month"!==t&&"quarter"!==t&&"half-year"!==t&&"year"!==t||(t="MM-dd\nyyyy");var i=(e=lo(e))[(n=n?"getUTC":"get")+"FullYear"](),r=e[n+"Month"]()+1,o=e[n+"Date"](),a=e[n+"Hours"](),s=e[n+"Minutes"](),l=e[n+"Seconds"](),e=e[n+"Milliseconds"]();return t=t.replace("MM",Fc(r,2)).replace("M",r).replace("yyyy",i).replace("yy",Fc(i%100+"",2)).replace("dd",Fc(o,2)).replace("d",o).replace("hh",Fc(a,2)).replace("h",a).replace("mm",Fc(s,2)).replace("m",s).replace("ss",Fc(l,2)).replace("s",l).replace("SSS",Fc(e,3))},formatTpl:sp,getTextRect:function(t,e,n,i,r,o,a,s){return new Ps({style:{text:t,font:e,align:n,verticalAlign:i,padding:r,rich:o,overflow:a?"truncate":null,lineHeight:s}}).getBoundingRect()},getTooltipMarker:function(t,e){var n=(t=V(t)?{color:t,extraCssText:e}:t||{}).color,i=t.type,r=(e=t.extraCssText,t.renderMode||"html");return n?"html"===r?"subItem"===i?'<span style="display:inline-block;vertical-align:middle;margin-right:8px;margin-left:3px;border-radius:4px;width:4px;height:4px;background-color:'+ve(n)+";"+(e||"")+'"></span>':'<span style="display:inline-block;margin-right:4px;border-radius:10px;width:10px;height:10px;background-color:'+ve(n)+";"+(e||"")+'"></span>':{renderMode:r,content:"{"+(t.markerId||"markerX")+"|}  ",style:"subItem"===i?{width:4,height:4,borderRadius:2,backgroundColor:n}:{width:10,height:10,borderRadius:5,backgroundColor:n}}:""},normalizeCssArray:op,toCamelCase:function(t,e){return t=(t||"").toLowerCase().replace(/-(.)/g,function(t,e){return e.toUpperCase()}),t=e?t&&t.charAt(0).toUpperCase()+t.slice(1):t},truncateText:ia}),hc=Object.freeze({__proto__:null,bind:ht,clone:y,curry:ct,defaults:z,each:O,extend:P,filter:ut,indexOf:k,inherits:ot,isArray:F,isFunction:D,isObject:R,isString:V,map:B,merge:d,reduce:lt}),c_=Ao();function p_(e,t){t=B(t,function(t){return e.scale.parse(t)});return"time"===e.type&&0<t.length&&(t.sort(),t.unshift(t[0]),t.push(t[t.length-1])),t}function d_(n){var i,r,o,t,e,a=n.getLabelModel().get("customValues");return a?(i=Fv(n),{labels:p_(n,a).map(function(t){var e={value:t};return{formattedLabel:i(e),rawLabel:n.scale.getLabel(e),tickValue:t}})}):"category"===n.type?(t=(a=n).getLabelModel(),e=g_(a,t),!t.get("show")||a.scale.isBlank()?{labels:[],labelCategoryInterval:e.labelCategoryInterval}:e):(t=(r=n).scale.getTicks(),o=Fv(r),{labels:B(t,function(t,e){return{level:t.level,formattedLabel:o(t,e),rawLabel:r.scale.getLabel(t),tickValue:t.value}})})}function f_(t,e){var n,i,r,o,a,s=t.getTickModel().get("customValues");return s?{ticks:p_(t,s)}:"category"===t.type?(s=e,r=y_(e=t,"ticks"),o=Hv(s),(a=m_(r,o))||(s.get("show")&&!e.scale.isBlank()||(n=[]),n=D(o)?x_(e,o,!0):"auto"===o?(a=g_(e,e.getLabelModel()),i=a.labelCategoryInterval,B(a.labels,function(t){return t.tickValue})):__(e,i=o,!0),v_(r,o,{ticks:n,tickCategoryInterval:i}))):{ticks:B(t.scale.getTicks(),function(t){return t.value})}}function g_(t,e){var n,i=y_(t,"labels"),e=Hv(e),r=m_(i,e);return r||v_(i,e,{labels:D(e)?x_(t,e):__(t,n="auto"===e?null!=(i=c_(r=t).autoInterval)?i:c_(r).autoInterval=r.calculateCategoryInterval():e),labelCategoryInterval:n})}function y_(t,e){return c_(t)[e]||(c_(t)[e]=[])}function m_(t,e){for(var n=0;n<t.length;n++)if(t[n].key===e)return t[n].value}function v_(t,e,n){return t.push({key:e,value:n}),n}function __(t,e,n){for(var i=Fv(t),r=t.scale,o=r.getExtent(),a=t.getLabelModel(),s=[],l=Math.max((e||0)+1,1),e=o[0],u=r.count(),u=(0!==e&&1<l&&2<u/l&&(e=Math.round(Math.ceil(e/l)*l)),Gv(t)),t=a.get("showMinLabel")||u,a=a.get("showMaxLabel")||u,h=(t&&e!==o[0]&&c(o[0]),e);h<=o[1];h+=l)c(h);function c(t){var e={value:t};s.push(n?t:{formattedLabel:i(e),rawLabel:r.getLabel(e),tickValue:t})}return a&&h-l!==o[1]&&c(o[1]),s}function x_(t,i,r){var o=t.scale,a=Fv(t),s=[];return O(o.getTicks(),function(t){var e=o.getLabel(t),n=t.value;i(t.value,e)&&s.push(r?n:{formattedLabel:a(t),rawLabel:e,tickValue:n})}),s}var b_=[0,1],Nc=(w_.prototype.contain=function(t){var e=this._extent,n=Math.min(e[0],e[1]),e=Math.max(e[0],e[1]);return n<=t&&t<=e},w_.prototype.containData=function(t){return this.scale.contain(t)},w_.prototype.getExtent=function(){return this._extent.slice()},w_.prototype.getPixelPrecision=function(t){return ro(t||this.scale.getExtent(),this._extent)},w_.prototype.setExtent=function(t,e){var n=this._extent;n[0]=t,n[1]=e},w_.prototype.dataToCoord=function(t,e){var n=this._extent,i=this.scale;return t=i.normalize(t),this.onBand&&"ordinal"===i.type&&S_(n=n.slice(),i.count()),Qr(t,b_,n,e)},w_.prototype.coordToData=function(t,e){var n=this._extent,i=this.scale,i=(this.onBand&&"ordinal"===i.type&&S_(n=n.slice(),i.count()),Qr(t,n,b_,e));return this.scale.scale(i)},w_.prototype.pointToData=function(t,e){},w_.prototype.getTicksCoords=function(t){var e,n,i,r,o,a,s,l=(t=t||{}).tickModel||this.getTickModel(),u=B(f_(this,l).ticks,function(t){return{coord:this.dataToCoord("ordinal"===this.scale.type?this.scale.getRawOrdinalNumber(t):t),tickValue:t}},this),l=l.get("alignWithLabel");function h(t,e){return t=to(t),e=to(e),a?e<t:t<e}return e=this,n=u,l=l,t=t.clamp,s=n.length,e.onBand&&!l&&s&&(l=e.getExtent(),1===s?(n[0].coord=l[0],i=n[1]={coord:l[1]}):(o=n[s-1].tickValue-n[0].tickValue,r=(n[s-1].coord-n[0].coord)/o,O(n,function(t){t.coord-=r/2}),e=1+(o=e.scale.getExtent())[1]-n[s-1].tickValue,i={coord:n[s-1].coord+r*e},n.push(i)),a=l[0]>l[1],h(n[0].coord,l[0])&&(t?n[0].coord=l[0]:n.shift()),t&&h(l[0],n[0].coord)&&n.unshift({coord:l[0]}),h(l[1],i.coord)&&(t?i.coord=l[1]:n.pop()),t)&&h(i.coord,l[1])&&n.push({coord:l[1]}),u},w_.prototype.getMinorTicksCoords=function(){var t;return"ordinal"===this.scale.type?[]:(t=this.model.getModel("minorTick").get("splitNumber"),B(this.scale.getMinorTicks(t=0<t&&t<100?t:5),function(t){return B(t,function(t){return{coord:this.dataToCoord(t),tickValue:t}},this)},this))},w_.prototype.getViewLabels=function(){return d_(this).labels},w_.prototype.getLabelModel=function(){return this.model.getModel("axisLabel")},w_.prototype.getTickModel=function(){return this.model.getModel("axisTick")},w_.prototype.getBandWidth=function(){var t=this._extent,e=this.scale.getExtent(),e=e[1]-e[0]+(this.onBand?1:0),t=(0===e&&(e=1),Math.abs(t[1]-t[0]));return Math.abs(t)/e},w_.prototype.calculateCategoryInterval=function(){r=(n=d=this).getLabelModel();var t={axisRotate:n.getRotate?n.getRotate():n.isHorizontal&&!n.isHorizontal()?90:0,labelRotate:r.get("rotate")||0,font:r.getFont()},e=Fv(d),n=(t.axisRotate-t.labelRotate)/180*Math.PI,i=(r=d.scale).getExtent(),r=r.count();if(i[1]-i[0]<1)return 0;for(var o=1,a=(40<r&&(o=Math.max(1,Math.floor(r/40))),i[0]),s=d.dataToCoord(a+1)-d.dataToCoord(a),l=Math.abs(s*Math.cos(n)),s=Math.abs(s*Math.sin(n)),u=0,h=0;a<=i[1];a+=o)var c=1.3*(p=wr(e({value:a}),t.font,"center","top")).width,p=1.3*p.height,u=Math.max(u,c,7),h=Math.max(h,p,7);var n=u/l,l=h/s,s=(isNaN(n)&&(n=1/0),isNaN(l)&&(l=1/0),Math.max(0,Math.floor(Math.min(n,l)))),n=c_(d.model),l=d.getExtent(),d=n.lastAutoInterval,f=n.lastTickCount;return null!=d&&null!=f&&Math.abs(d-s)<=1&&Math.abs(f-r)<=1&&s<d&&n.axisExtent0===l[0]&&n.axisExtent1===l[1]?s=d:(n.lastTickCount=r,n.lastAutoInterval=s,n.axisExtent0=l[0],n.axisExtent1=l[1]),s},w_);function w_(t,e,n){this.onBand=!1,this.inverse=!1,this.dim=t,this.scale=e,this._extent=n||[0,0]}function S_(t,e){e=(t[1]-t[0])/e/2;t[0]+=e,t[1]-=e}var M_=2*Math.PI,T_=Za.CMD,C_=["top","right","bottom","left"];function k_(t,e,n,i,r,o,a,s){var l=r-t,u=o-e,n=n-t,i=i-e,h=Math.sqrt(n*n+i*i),l=(l*(n/=h)+u*(i/=h))/h,u=(s&&(l=Math.min(Math.max(l,0),1)),a[0]=t+(l*=h)*n),s=a[1]=e+l*i;return Math.sqrt((u-r)*(u-r)+(s-o)*(s-o))}function I_(t,e,n,i,r,o,a){n<0&&(t+=n,n=-n),i<0&&(e+=i,i=-i);n=t+n,i=e+i,t=a[0]=Math.min(Math.max(r,t),n),n=a[1]=Math.min(Math.max(o,e),i);return Math.sqrt((t-r)*(t-r)+(n-o)*(n-o))}var D_=[];function A_(t,e,n){for(var i,r,o,a,s,l,u,h,c,p=0,d=0,f=0,g=0,y=1/0,m=e.data,v=t.x,_=t.y,x=0;x<m.length;){var b=m[x++],w=(1===x&&(f=p=m[x],g=d=m[x+1]),y);switch(b){case T_.M:p=f=m[x++],d=g=m[x++];break;case T_.L:w=k_(p,d,m[x],m[x+1],v,_,D_,!0),p=m[x++],d=m[x++];break;case T_.C:w=Hn(p,d,m[x++],m[x++],m[x++],m[x++],m[x],m[x+1],v,_,D_),p=m[x++],d=m[x++];break;case T_.Q:w=Yn(p,d,m[x++],m[x++],m[x],m[x+1],v,_,D_),p=m[x++],d=m[x++];break;case T_.A:var S=m[x++],M=m[x++],T=m[x++],C=m[x++],k=m[x++],I=m[x++],D=(x+=1,!!(1-m[x++])),A=Math.cos(k)*T+S,L=Math.sin(k)*C+M;x<=1&&(f=A,g=L),L=(A=k)+I,D=D,a=(v-S)*(o=C)/T+S,s=_,l=D_,c=h=u=void 0,a-=i=S,s-=r=M,u=Math.sqrt(a*a+s*s),h=(a/=u)*o+i,c=(s/=u)*o+r,w=Math.abs(A-L)%M_<1e-4||((L=D?(D=A,A=$a(L),$a(D)):(A=$a(A),$a(L)))<A&&(L+=M_),(D=Math.atan2(s,a))<0&&(D+=M_),A<=D&&D<=L)||A<=D+M_&&D+M_<=L?(l[0]=h,l[1]=c,u-o):(c=((D=o*Math.cos(A)+i)-a)*(D-a)+((h=o*Math.sin(A)+r)-s)*(h-s))<(i=((u=o*Math.cos(L)+i)-a)*(u-a)+((A=o*Math.sin(L)+r)-s)*(A-s))?(l[0]=D,l[1]=h,Math.sqrt(c)):(l[0]=u,l[1]=A,Math.sqrt(i)),p=Math.cos(k+I)*T+S,d=Math.sin(k+I)*C+M;break;case T_.R:w=I_(f=p=m[x++],g=d=m[x++],m[x++],m[x++],v,_,D_);break;case T_.Z:w=k_(p,d,f,g,v,_,D_,!0),p=f,d=g}w<y&&(y=w,n.set(D_[0],D_[1]))}return y}var L_=new M,P_=new M,O_=new M,R_=new M,N_=new M;function E_(t,e){if(t){var n=t.getTextGuideLine(),i=t.getTextContent();if(i&&n){var r=t.textGuideLineConfig||{},o=[[0,0],[0,0],[0,0]],a=r.candidates||C_,s=i.getBoundingRect().clone(),l=(s.applyTransform(i.getComputedTransform()),1/0),u=r.anchor,h=t.getComputedTransform(),c=h&&Be([],h),p=e.get("length2")||0;u&&O_.copy(u);for(var d,f,g=0;g<a.length;g++){var y=a[g],m=(S=w=b=x=_=v=m=void 0,y),v=0,_=s,x=L_,b=R_,w=_.width,S=_.height;switch(m){case"top":x.set(_.x+w/2,_.y-v),b.set(0,-1);break;case"bottom":x.set(_.x+w/2,_.y+S+v),b.set(0,1);break;case"left":x.set(_.x-v,_.y+S/2),b.set(-1,0);break;case"right":x.set(_.x+w+v,_.y+S/2),b.set(1,0)}M.scaleAndAdd(P_,L_,R_,p),P_.transform(c);y=t.getBoundingRect(),y=u?u.distance(P_):t instanceof j?A_(P_,t.path,O_):(m=O_,d=I_((d=y).x,y.y,y.width,y.height,P_.x,P_.y,D_),m.set(D_[0],D_[1]),d);y<l&&(l=y,P_.transform(h),O_.transform(h),O_.toArray(o[0]),P_.toArray(o[1]),L_.toArray(o[2]))}i=o,(r=e.get("minTurnAngle"))<=180&&0<r&&(r=r/180*Math.PI,L_.fromArray(i[0]),P_.fromArray(i[1]),O_.fromArray(i[2]),M.sub(R_,L_,P_),M.sub(N_,O_,P_),e=R_.len(),f=N_.len(),e<.001||f<.001||(R_.scale(1/e),N_.scale(1/f),e=R_.dot(N_),Math.cos(r)<e&&(f=k_(P_.x,P_.y,O_.x,O_.y,L_.x,L_.y,z_,!1),B_.fromArray(z_),B_.scaleAndAdd(N_,f/Math.tan(Math.PI-r)),e=O_.x!==P_.x?(B_.x-P_.x)/(O_.x-P_.x):(B_.y-P_.y)/(O_.y-P_.y),isNaN(e)||(e<0?M.copy(B_,P_):1<e&&M.copy(B_,O_),B_.toArray(i[1]))))),n.setShape({points:o})}}}var z_=[],B_=new M;function F_(t,e,n,i){var r="normal"===n,n=r?t:t.ensureState(n),e=(n.ignore=e,i.get("smooth")),e=(e&&!0===e&&(e=.3),n.shape=n.shape||{},0<e&&(n.shape.smooth=e),i.getModel("lineStyle").getLineStyle());r?t.useStyle(e):n.style=e}function V_(t,e){var n=e.smooth,i=e.points;if(i)if(t.moveTo(i[0][0],i[0][1]),0<n&&3<=i.length){var e=$t(i[0],i[1]),r=$t(i[1],i[2]);e&&r?(n=Math.min(e,r)*n,e=te([],i[1],i[0],n/e),n=te([],i[1],i[2],n/r),r=te([],e,n,.5),t.bezierCurveTo(e[0],e[1],e[0],e[1],r[0],r[1]),t.bezierCurveTo(n[0],n[1],n[0],n[1],i[2][0],i[2][1])):(t.lineTo(i[1][0],i[1][1]),t.lineTo(i[2][0],i[2][1]))}else for(var o=1;o<i.length;o++)t.lineTo(i[o][0],i[o][1])}function H_(t){for(var e=[],n=0;n<t.length;n++){var i,r,o,a,s,l,u=t[n];u.defaultAttr.ignore||(r=(i=u.label).getComputedTransform(),o=i.getBoundingRect(),a=!r||r[1]<1e-5&&r[2]<1e-5,l=i.style.margin||0,(s=o.clone()).applyTransform(r),s.x-=l/2,s.y-=l/2,s.width+=l,s.height+=l,l=a?new xh(o,r):null,e.push({label:i,labelLine:u.labelLine,rect:s,localRect:o,obb:l,priority:u.priority,defaultAttr:u.defaultAttr,layoutOption:u.computedLayoutOption,axisAligned:a,transform:r}))}return e}function G_(s,l,u,t,e,n){var h=s.length;if(!(h<2)){s.sort(function(t,e){return t.rect[l]-e.rect[l]});for(var i=0,o=!1,r=0,a=0;a<h;a++){var c,p=s[a],d=p.rect;(c=d[l]-i)<0&&(d[l]-=c,p.label[l]-=c,o=!0),r+=Math.max(-c,0),i=d[l]+d[u]}0<r&&n&&x(-r/h,0,h);var f,g,y=s[0],m=s[h-1];return v(),f<0&&b(-f,.8),g<0&&b(g,.8),v(),_(f,g,1),_(g,f,-1),v(),f<0&&w(-f),g<0&&w(g),o}function v(){f=y.rect[l]-t,g=e-m.rect[l]-m.rect[u]}function _(t,e,n){t<0&&(0<(e=Math.min(e,-t))?(x(e*n,0,h),(e=e+t)<0&&b(-e*n,1)):b(-t*n,1))}function x(t,e,n){0!==t&&(o=!0);for(var i=e;i<n;i++){var r=s[i];r.rect[l]+=t,r.label[l]+=t}}function b(t,e){for(var n=[],i=0,r=1;r<h;r++){var o=s[r-1].rect,o=Math.max(s[r].rect[l]-o[l]-o[u],0);n.push(o),i+=o}if(i){var a=Math.min(Math.abs(t)/i,e);if(0<t)for(r=0;r<h-1;r++)x(n[r]*a,0,r+1);else for(r=h-1;0<r;r--)x(-(n[r-1]*a),r,h)}}function w(t){for(var e=t<0?-1:1,n=(t=Math.abs(t),Math.ceil(t/(h-1))),i=0;i<h-1;i++)if(0<e?x(n,0,i+1):x(-n,h-i-1,h),(t-=n)<=0)return}}function W_(t){var e=[],n=(t.sort(function(t,e){return e.priority-t.priority}),new X(0,0,0,0));function i(t){var e;t.ignore||null==(e=t.ensureState("emphasis")).ignore&&(e.ignore=!1),t.ignore=!0}for(var r=0;r<t.length;r++){for(var o=t[r],a=o.axisAligned,s=o.localRect,l=o.transform,u=o.label,h=o.labelLine,c=(n.copy(o.rect),n.width-=.1,n.height-=.1,n.x+=.05,n.y+=.05,o.obb),p=!1,d=0;d<e.length;d++){var f=e[d];if(n.intersect(f.rect)){if(a&&f.axisAligned){p=!0;break}if(f.obb||(f.obb=new xh(f.localRect,f.transform)),(c=c||new xh(s,l)).intersect(f.obb)){p=!0;break}}}p?(i(u),h&&i(h)):(u.attr("ignore",o.defaultAttr.ignore),h&&h.attr("ignore",o.defaultAttr.labelGuideIgnore),e.push(o))}}function U_(t,e){var n=t.label,e=e&&e.getTextGuideLine();return{dataIndex:t.dataIndex,dataType:t.dataType,seriesIndex:t.seriesModel.seriesIndex,text:t.label.style.text,rect:t.hostRect,labelRect:t.rect,align:n.style.align,verticalAlign:n.style.verticalAlign,labelLinePoints:function(t){if(t){for(var e=[],n=0;n<t.length;n++)e.push(t[n].slice());return e}}(e&&e.shape.points)}}var X_=["align","verticalAlign","width","height","fontSize"],Y_=new yr,q_=Ao(),Z_=Ao();function j_(t,e,n){for(var i=0;i<n.length;i++){var r=n[i];null!=e[r]&&(t[r]=e[r])}}var K_=["x","y","rotation"],$_=(Q_.prototype.clearLabels=function(){this._labelList=[],this._chartViewList=[]},Q_.prototype._addLabel=function(t,e,n,i,r){var o,a=i.style,s=i.__hostTarget.textConfig||{},l=i.getComputedTransform(),u=i.getBoundingRect().plain(),l=(X.applyTransform(u,u,l),l?Y_.setLocalTransform(l):(Y_.x=Y_.y=Y_.rotation=Y_.originX=Y_.originY=0,Y_.scaleX=Y_.scaleY=1),Y_.rotation=$a(Y_.rotation),i.__hostTarget),h=(l&&(o=l.getBoundingRect().plain(),h=l.getComputedTransform(),X.applyTransform(o,o,h)),o&&l.getTextGuideLine());this._labelList.push({label:i,labelLine:h,seriesModel:n,dataIndex:t,dataType:e,layoutOption:r,computedLayoutOption:null,rect:u,hostRect:o,priority:o?o.width*o.height:0,defaultAttr:{ignore:i.ignore,labelGuideIgnore:h&&h.ignore,x:Y_.x,y:Y_.y,scaleX:Y_.scaleX,scaleY:Y_.scaleY,rotation:Y_.rotation,style:{x:a.x,y:a.y,align:a.align,verticalAlign:a.verticalAlign,width:a.width,height:a.height,fontSize:a.fontSize},cursor:i.cursor,attachedPos:s.position,attachedRot:s.rotation}})},Q_.prototype.addLabelsOfSeries=function(t){var n=this,i=(this._chartViewList.push(t),t.__model),r=i.get("labelLayout");(D(r)||I(r).length)&&t.group.traverse(function(t){if(t.ignore)return!0;var e=t.getTextContent(),t=Us(t);e&&!e.disableLabelLayout&&n._addLabel(t.dataIndex,t.dataType,i,e,r)})},Q_.prototype.updateLayoutConfig=function(t){var e=t.getWidth(),n=t.getHeight();for(var i=0;i<this._labelList.length;i++){var r=this._labelList[i],o=r.label,a=o.__hostTarget,s=r.defaultAttr,l=void 0,l=D(r.layoutOption)?r.layoutOption(U_(r,a)):r.layoutOption,u=(r.computedLayoutOption=l=l||{},Math.PI/180),h=(a&&a.setTextConfig({local:!1,position:null!=l.x||null!=l.y?null:s.attachedPos,rotation:null!=l.rotate?l.rotate*u:s.attachedRot,offset:[l.dx||0,l.dy||0]}),!1);null!=l.x?(o.x=Jr(l.x,e),o.setStyle("x",0),h=!0):(o.x=s.x,o.setStyle("x",s.style.x)),null!=l.y?(o.y=Jr(l.y,n),o.setStyle("y",0),h=!0):(o.y=s.y,o.setStyle("y",s.style.y)),l.labelLinePoints&&(c=a.getTextGuideLine())&&(c.setShape({points:l.labelLinePoints}),h=!1),q_(o).needsUpdateLabelLine=h,o.rotation=null!=l.rotate?l.rotate*u:s.rotation,o.scaleX=s.scaleX,o.scaleY=s.scaleY;for(var c,p=0;p<X_.length;p++){var d=X_[p];o.setStyle(d,(null!=l[d]?l:s.style)[d])}l.draggable?(o.draggable=!0,o.cursor="move",a&&(c=r.seriesModel,null!=r.dataIndex&&(c=r.seriesModel.getData(r.dataType).getItemModel(r.dataIndex)),o.on("drag",function(t,e){return function(){E_(t,e)}}(a,c.getModel("labelLine"))))):(o.off("drag"),o.cursor=s.cursor)}},Q_.prototype.layout=function(t){var e,n,i=t.getWidth(),t=t.getHeight(),r=H_(this._labelList),o=ut(r,function(t){return"shiftX"===t.layoutOption.moveOverlap}),a=ut(r,function(t){return"shiftY"===t.layoutOption.moveOverlap});G_(o,"x","width",0,i,e),G_(a,"y","height",0,t,n),W_(ut(r,function(t){return t.layoutOption.hideOverlap}))},Q_.prototype.processLabelsOverall=function(){var a=this;O(this._chartViewList,function(t){var i=t.__model,r=t.ignoreLabelLineUpdate,o=i.isAnimationEnabled();t.group.traverse(function(t){if(t.ignore&&!t.forceLabelAnimation)return!0;var e=!r,n=t.getTextContent();(e=!e&&n?q_(n).needsUpdateLabelLine:e)&&a._updateLabelLine(t,i),o&&a._animateLabels(t,i)})})},Q_.prototype._updateLabelLine=function(t,e){var n=t.getTextContent(),i=Us(t),r=i.dataIndex;if(n&&null!=r){var n=e.getData(i.dataType),e=n.getItemModel(r),i={},r=n.getItemVisual(r,"style"),r=(r&&(n=n.getVisual("drawType"),i.stroke=r[n]),e.getModel("labelLine")),o=t,a=function(t,e){for(var n={normal:t.getModel(e=e||"labelLine")},i=0;i<Qs.length;i++){var r=Qs[i];n[r]=t.getModel([r,e])}return n}(e),n=i,s=o.getTextGuideLine(),l=o.getTextContent();if(l){for(var e=a.normal,u=e.get("show"),h=l.ignore,c=0;c<Js.length;c++){var p,d=Js[c],f=a[d],g="normal"===d;f&&(p=f.get("show"),(g?h:N(l.states[d]&&l.states[d].ignore,h))||!N(p,u)?((p=g?s:s&&s.states[d])&&(p.ignore=!0),s&&F_(s,!0,d,f)):(s||(s=new Wu,o.setTextGuideLine(s),g||!h&&u||F_(s,!0,"normal",a.normal),o.stateProxy&&(s.stateProxy=o.stateProxy)),F_(s,!1,d,f)))}s&&(z(s.style,n),s.style.fill=null,n=e.get("showAbove"),(o.textGuideLineConfig=o.textGuideLineConfig||{}).showAbove=n||!1,s.buildPath=V_)}else s&&o.removeTextGuideLine();E_(t,r)}},Q_.prototype._animateLabels=function(t,e){var n,i,r,o,a,s=t.getTextContent(),l=t.getTextGuideLine();!s||!t.forceLabelAnimation&&(s.ignore||s.invisible||t.disableLabelAnimation||Dh(t))||(o=(r=q_(s)).oldLayout,n=(i=Us(t)).dataIndex,a={x:s.x,y:s.y,rotation:s.rotation},i=e.getData(i.dataType),o?(s.attr(o),(t=t.prevStates)&&(0<=k(t,"select")&&s.attr(r.oldLayoutSelect),0<=k(t,"emphasis"))&&s.attr(r.oldLayoutEmphasis),kh(s,a,e,n)):(s.attr(a),ic(s).valueAnimation||(t=N(s.style.opacity,1),s.style.opacity=0,Ih(s,{style:{opacity:t}},e,n))),r.oldLayout=a,s.states.select&&(j_(t=r.oldLayoutSelect={},a,K_),j_(t,s.states.select,K_)),s.states.emphasis&&(j_(t=r.oldLayoutEmphasis={},a,K_),j_(t,s.states.emphasis,K_)),rc(s,n,i,e,e)),!l||l.ignore||l.invisible||(o=(r=Z_(l)).oldLayout,a={points:l.shape.points},o?(l.attr({shape:o}),kh(l,{shape:a},e)):(l.setShape(a),l.style.strokePercent=0,Ih(l,{style:{strokePercent:1}},e)),r.oldLayout=a)},Q_);function Q_(){this._labelList=[],this._chartViewList=[]}var J_=Ao();function t1(t){t.registerUpdateLifecycle("series:beforeupdate",function(t,e,n){(J_(e).labelManager||(J_(e).labelManager=new $_)).clearLabels()}),t.registerUpdateLifecycle("series:layoutlabels",function(t,e,n){var i=J_(e).labelManager;n.updatedSeries.forEach(function(t){i.addLabelsOfSeries(e.getViewOfSeriesModel(t))}),i.updateLayoutConfig(e),i.layout(e),i.processLabelsOverall()})}function e1(t,e,n){var i=H.createCanvas(),r=e.getWidth(),e=e.getHeight(),o=i.style;return o&&(o.position="absolute",o.left="0",o.top="0",o.width=r+"px",o.height=e+"px",i.setAttribute("data-zr-dom-id",t)),i.width=r*n,i.height=e*n,i}qv(t1);u(r1,n1=le),r1.prototype.getElementCount=function(){return this.__endIndex-this.__startIndex},r1.prototype.afterBrush=function(){this.__prevStartIndex=this.__startIndex,this.__prevEndIndex=this.__endIndex},r1.prototype.initContext=function(){this.ctx=this.dom.getContext("2d"),this.ctx.dpr=this.dpr},r1.prototype.setUnpainted=function(){this.__firstTimePaint=!0},r1.prototype.createBackBuffer=function(){var t=this.dpr;this.domBack=e1("back-"+this.id,this.painter,t),this.ctxBack=this.domBack.getContext("2d"),1!==t&&this.ctxBack.scale(t,t)},r1.prototype.createRepaintRects=function(t,e,n,i){if(this.__firstTimePaint)return this.__firstTimePaint=!1,null;var l=[],u=this.maxRepaintRectCount,h=!1,c=new X(0,0,0,0);function r(t){if(t.isFinite()&&!t.isZero())if(0===l.length)(e=new X(0,0,0,0)).copy(t),l.push(e);else{for(var e,n=!1,i=1/0,r=0,o=0;o<l.length;++o){var a=l[o];if(a.intersect(t)){var s=new X(0,0,0,0);s.copy(a),s.union(t),l[o]=s,n=!0;break}h&&(c.copy(t),c.union(a),s=t.width*t.height,a=a.width*a.height,(a=c.width*c.height-s-a)<i)&&(i=a,r=o)}h&&(l[r].union(t),n=!0),n||((e=new X(0,0,0,0)).copy(t),l.push(e)),h=h||l.length>=u}}for(var o,a=this.__startIndex;a<this.__endIndex;++a)(s=t[a])&&(d=s.shouldBePainted(n,i,!0,!0),(p=s.__isRendered&&(s.__dirty&mn||!d)?s.getPrevPaintRect():null)&&r(p),o=d&&(s.__dirty&mn||!s.__isRendered)?s.getPaintRect():null)&&r(o);for(a=this.__prevStartIndex;a<this.__prevEndIndex;++a){var s,p,d=(s=e[a])&&s.shouldBePainted(n,i,!0,!0);!s||d&&s.__zr||!s.__isRendered||(p=s.getPrevPaintRect())&&r(p)}do{for(var f=!1,a=0;a<l.length;)if(l[a].isZero())l.splice(a,1);else{for(var g=a+1;g<l.length;)l[a].intersect(l[g])?(f=!0,l[a].union(l[g]),l.splice(g,1)):g++;a++}}while(f);return this._paintRects=l},r1.prototype.debugGetPaintRects=function(){return(this._paintRects||[]).slice()},r1.prototype.resize=function(t,e){var n=this.dpr,i=this.dom,r=i.style,o=this.domBack;r&&(r.width=t+"px",r.height=e+"px"),i.width=t*n,i.height=e*n,o&&(o.width=t*n,o.height=e*n,1!==n)&&this.ctxBack.scale(n,n)},r1.prototype.clear=function(t,o,e){var n=this.dom,a=this.ctx,i=n.width,r=n.height,s=(o=o||this.clearColor,this.motionBlur&&!t),l=this.lastFrameAlpha,u=this.dpr,h=this,c=(s&&(this.domBack||this.createBackBuffer(),this.ctxBack.globalCompositeOperation="copy",this.ctxBack.drawImage(n,0,0,i/u,r/u)),this.domBack);function p(t,e,n,i){var r;a.clearRect(t,e,n,i),o&&"transparent"!==o&&(r=void 0,mt(o)?(r=(o.global||o.__width===n&&o.__height===i)&&o.__canvasGradient||gy(a,o,{x:0,y:0,width:n,height:i}),o.__canvasGradient=r,o.__width=n,o.__height=i):vt(o)&&(o.scaleX=o.scaleX||u,o.scaleY=o.scaleY||u,r=Ty(a,o,{dirty:function(){h.setUnpainted(),h.painter.refresh()}})),a.save(),a.fillStyle=r||o,a.fillRect(t,e,n,i),a.restore()),s&&(a.save(),a.globalAlpha=l,a.drawImage(c,t,e,n,i),a.restore())}!e||s?p(0,0,i,r):e.length&&O(e,function(t){p(t.x*u,t.y*u,t.width*u,t.height*u)})};var n1,i1=r1;function r1(t,e,n){var i,r=n1.call(this)||this,t=(r.motionBlur=!1,r.lastFrameAlpha=.7,r.dpr=1,r.virtual=!1,r.config={},r.incremental=!1,r.zlevel=0,r.maxRepaintRectCount=5,r.__dirty=!0,r.__firstTimePaint=!0,r.__used=!1,r.__drawIndex=0,r.__startIndex=0,r.__endIndex=0,r.__prevStartIndex=null,r.__prevEndIndex=null,n=n||sr,"string"==typeof t?i=e1(t,e,n):R(t)&&(t=(i=t).id),r.id=t,(r.dom=i).style);return t&&(zt(i),i.onselectstart=function(){return!1},t.padding="0",t.margin="0",t.borderWidth="0"),r.painter=e,r.dpr=n,r}var o1=314159;_.prototype.getType=function(){return"canvas"},_.prototype.isSingleCanvas=function(){return this._singleCanvas},_.prototype.getViewportRoot=function(){return this._domRoot},_.prototype.getViewportRootOffset=function(){var t=this.getViewportRoot();if(t)return{offsetLeft:t.offsetLeft||0,offsetTop:t.offsetTop||0}},_.prototype.refresh=function(t){var e=this.storage.getDisplayList(!0),n=this._prevDisplayList,i=this._zlevelList;this._redrawId=Math.random(),this._paintList(e,n,t,this._redrawId);for(var r=0;r<i.length;r++){var o,a=i[r],a=this._layers[a];!a.__builtin__&&a.refresh&&(o=0===r?this._backgroundColor:null,a.refresh(o))}return this._opts.useDirtyRect&&(this._prevDisplayList=e.slice()),this},_.prototype.refreshHover=function(){this._paintHoverList(this.storage.getDisplayList(!1))},_.prototype._paintHoverList=function(t){var e=t.length,n=this._hoverlayer;if(n&&n.clear(),e){for(var i,r={inHover:!0,viewWidth:this._width,viewHeight:this._height},o=0;o<e;o++){var a=t[o];a.__inHover&&(n=n||(this._hoverlayer=this.getLayer(1e5)),i||(i=n.ctx).save(),By(i,a,r,o===e-1))}i&&i.restore()}},_.prototype.getHoverLayer=function(){return this.getLayer(1e5)},_.prototype.paintOne=function(t,e){zy(t,e)},_.prototype._paintList=function(t,e,n,i){var r,o,a;this._redrawId===i&&(n=n||!1,this._updateLayerStatus(t),r=(o=this._doPaintList(t,e,n)).finished,o=o.needsRefreshHover,this._needsManuallyCompositing&&this._compositeManually(),o&&this._paintHoverList(t),r?this.eachLayer(function(t){t.afterBrush&&t.afterBrush()}):(a=this,Mn(function(){a._paintList(t,e,n,i)})))},_.prototype._compositeManually=function(){var e=this.getLayer(o1).ctx,n=this._domRoot.width,i=this._domRoot.height;e.clearRect(0,0,n,i),this.eachBuiltinLayer(function(t){t.virtual&&e.drawImage(t.dom,0,0,n,i)})},_.prototype._doPaintList=function(d,f,g){for(var y=this,m=[],v=this._opts.useDirtyRect,t=0;t<this._zlevelList.length;t++){var e=this._zlevelList[t],e=this._layers[e];e.__builtin__&&e!==this._hoverlayer&&(e.__dirty||g)&&m.push(e)}for(var _=!0,x=!1,n=function(t){function e(t){var e={inHover:!1,allClipped:!1,prevEl:null,viewWidth:y._width,viewHeight:y._height};for(i=s;i<r.__endIndex;i++){var n=d[i];if(n.__inHover&&(x=!0),y._doPaintEl(n,r,v,t,e,i===r.__endIndex-1),l)if(15<Date.now()-u)break}e.prevElClipPaths&&o.restore()}var n,i,r=m[t],o=r.ctx,a=v&&r.createRepaintRects(d,f,b._width,b._height),s=g?r.__startIndex:r.__drawIndex,l=!g&&r.incremental&&Date.now,u=l&&Date.now(),t=r.zlevel===b._zlevelList[0]?b._backgroundColor:null;r.__startIndex!==r.__endIndex&&(s!==r.__startIndex||(n=d[s]).incremental&&n.notClear&&!g)||r.clear(!1,t,a),-1===s&&(console.error("For some unknown reason. drawIndex is -1"),s=r.__startIndex);if(a)if(0===a.length)i=r.__endIndex;else for(var h=b.dpr,c=0;c<a.length;++c){var p=a[c];o.save(),o.beginPath(),o.rect(p.x*h,p.y*h,p.width*h,p.height*h),o.clip(),e(p),o.restore()}else o.save(),e(),o.restore();r.__drawIndex=i,r.__drawIndex<r.__endIndex&&(_=!1)},b=this,i=0;i<m.length;i++)n(i);return p.wxa&&O(this._layers,function(t){t&&t.ctx&&t.ctx.draw&&t.ctx.draw()}),{finished:_,needsRefreshHover:x}},_.prototype._doPaintEl=function(t,e,n,i,r,o){e=e.ctx;n?(n=t.getPaintRect(),(!i||n&&n.intersect(i))&&(By(e,t,r,o),t.setPrevPaintRect(n))):By(e,t,r,o)},_.prototype.getLayer=function(t,e){this._singleCanvas&&!this._needsManuallyCompositing&&(t=o1);var n=this._layers[t];return n||((n=new i1("zr_"+t,this,this.dpr)).zlevel=t,n.__builtin__=!0,this._layerConfig[t]?d(n,this._layerConfig[t],!0):this._layerConfig[t-.01]&&d(n,this._layerConfig[t-.01],!0),e&&(n.virtual=e),this.insertLayer(t,n),n.initContext()),n},_.prototype.insertLayer=function(t,e){var n,i=this._layers,r=this._zlevelList,o=r.length,a=this._domRoot,s=null,l=-1;if(!i[t]&&(n=e)&&(n.__builtin__||"function"==typeof n.resize&&"function"==typeof n.refresh)){if(0<o&&t>r[0]){for(l=0;l<o-1&&!(r[l]<t&&r[l+1]>t);l++);s=i[r[l]]}r.splice(l+1,0,t),(i[t]=e).virtual||(s?(n=s.dom).nextSibling?a.insertBefore(e.dom,n.nextSibling):a.appendChild(e.dom):a.firstChild?a.insertBefore(e.dom,a.firstChild):a.appendChild(e.dom)),e.painter||(e.painter=this)}},_.prototype.eachLayer=function(t,e){for(var n=this._zlevelList,i=0;i<n.length;i++){var r=n[i];t.call(e,this._layers[r],r)}},_.prototype.eachBuiltinLayer=function(t,e){for(var n=this._zlevelList,i=0;i<n.length;i++){var r=n[i],o=this._layers[r];o.__builtin__&&t.call(e,o,r)}},_.prototype.eachOtherLayer=function(t,e){for(var n=this._zlevelList,i=0;i<n.length;i++){var r=n[i],o=this._layers[r];o.__builtin__||t.call(e,o,r)}},_.prototype.getLayers=function(){return this._layers},_.prototype._updateLayerStatus=function(t){function e(t){r&&(r.__endIndex!==t&&(r.__dirty=!0),r.__endIndex=t)}if(this.eachBuiltinLayer(function(t,e){t.__dirty=t.__used=!1}),this._singleCanvas)for(var n=1;n<t.length;n++)if((s=t[n]).zlevel!==t[n-1].zlevel||s.incremental){this._needsManuallyCompositing=!0;break}for(var i,r=null,o=0,a=0;a<t.length;a++){var s,l=(s=t[a]).zlevel,u=void 0;i!==l&&(i=l,o=0),s.incremental?((u=this.getLayer(l+.001,this._needsManuallyCompositing)).incremental=!0,o=1):u=this.getLayer(l+(0<o?.01:0),this._needsManuallyCompositing),u.__builtin__||it("ZLevel "+l+" has been used by unkown layer "+u.id),u!==r&&(u.__used=!0,u.__startIndex!==a&&(u.__dirty=!0),u.__startIndex=a,u.incremental?u.__drawIndex=-1:u.__drawIndex=a,e(a),r=u),s.__dirty&mn&&!s.__inHover&&(u.__dirty=!0,u.incremental)&&u.__drawIndex<0&&(u.__drawIndex=a)}e(a),this.eachBuiltinLayer(function(t,e){!t.__used&&0<t.getElementCount()&&(t.__dirty=!0,t.__startIndex=t.__endIndex=t.__drawIndex=0),t.__dirty&&t.__drawIndex<0&&(t.__drawIndex=t.__startIndex)})},_.prototype.clear=function(){return this.eachBuiltinLayer(this._clearLayer),this},_.prototype._clearLayer=function(t){t.clear()},_.prototype.setBackgroundColor=function(t){this._backgroundColor=t,O(this._layers,function(t){t.setUnpainted()})},_.prototype.configLayer=function(t,e){if(e){var n=this._layerConfig;n[t]?d(n[t],e,!0):n[t]=e;for(var i=0;i<this._zlevelList.length;i++){var r=this._zlevelList[i];r!==t&&r!==t+.01||d(this._layers[r],n[t],!0)}}},_.prototype.delLayer=function(t){var e=this._layers,n=this._zlevelList,i=e[t];i&&(i.dom.parentNode.removeChild(i.dom),delete e[t],n.splice(k(n,t),1))},_.prototype.resize=function(t,e){if(this._domRoot.style){var n=this._domRoot,i=(n.style.display="none",this._opts),r=this.root;if(null!=t&&(i.width=t),null!=e&&(i.height=e),t=my(r,0,i),e=my(r,1,i),n.style.display="",this._width!==t||e!==this._height){for(var o in n.style.width=t+"px",n.style.height=e+"px",this._layers)this._layers.hasOwnProperty(o)&&this._layers[o].resize(t,e);this.refresh(!0)}this._width=t,this._height=e}else{if(null==t||null==e)return;this._width=t,this._height=e,this.getLayer(o1).resize(t,e)}return this},_.prototype.clearLayer=function(t){t=this._layers[t];t&&t.clear()},_.prototype.dispose=function(){this.root.innerHTML="",this.root=this.storage=this._domRoot=this._layers=null},_.prototype.getRenderedCanvas=function(t){if(this._singleCanvas&&!this._compositeManually)return this._layers[o1].dom;var e=new i1("image",this,(t=t||{}).pixelRatio||this.dpr),n=(e.initContext(),e.clear(!1,t.backgroundColor||this._backgroundColor),e.ctx);if(t.pixelRatio<=this.dpr){this.refresh();var i=e.dom.width,r=e.dom.height;this.eachLayer(function(t){t.__builtin__?n.drawImage(t.dom,0,0,i,r):t.renderToCanvas&&(n.save(),t.renderToCanvas(n),n.restore())})}else for(var o={inHover:!1,viewWidth:this._width,viewHeight:this._height},a=this.storage.getDisplayList(!0),s=0,l=a.length;s<l;s++){var u=a[s];By(n,u,o,s===l-1)}return e.dom},_.prototype.getWidth=function(){return this._width},_.prototype.getHeight=function(){return this._height};var a1=_;function _(t,e,n,i){this.type="canvas",this._zlevelList=[],this._prevDisplayList=[],this._layers={},this._layerConfig={},this._needsManuallyCompositing=!1,this.type="canvas";var r=!t.nodeName||"CANVAS"===t.nodeName.toUpperCase();this._opts=n=P({},n||{}),this.dpr=n.devicePixelRatio||sr,this._singleCanvas=r;(this.root=t).style&&(zt(t),t.innerHTML=""),this.storage=e;var o,a,e=this._zlevelList,s=(this._prevDisplayList=[],this._layers);r?(o=(r=t).width,a=r.height,null!=n.width&&(o=n.width),null!=n.height&&(a=n.height),this.dpr=n.devicePixelRatio||1,r.width=o*this.dpr,r.height=a*this.dpr,this._width=o,this._height=a,(o=new i1(r,this,this.dpr)).__builtin__=!0,o.initContext(),(s[o1]=o).zlevel=o1,e.push(o1),this._domRoot=t):(this._width=my(t,0,n),this._height=my(t,1,n),o=this._domRoot=(a=this._width,r=this._height,(s=document.createElement("div")).style.cssText=["position:relative","width:"+a+"px","height:"+r+"px","padding:0","margin:0","border-width:0"].join(";")+";",s),t.appendChild(o))}u(u1,s1=g),u1.prototype.init=function(t,e,n){s1.prototype.init.call(this,t,e,n),this._sourceManager=new Xf(this),qf(this)},u1.prototype.mergeOption=function(t,e){s1.prototype.mergeOption.call(this,t,e),qf(this)},u1.prototype.optionUpdated=function(){this._sourceManager.dirty()},u1.prototype.getSourceManager=function(){return this._sourceManager},u1.type="dataset",u1.defaultOption={seriesLayoutBy:Pp};var s1,l1=u1;function u1(){var t=null!==s1&&s1.apply(this,arguments)||this;return t.type="dataset",t}u(p1,h1=ug),p1.type="dataset";var h1,c1=p1;function p1(){var t=null!==h1&&h1.apply(this,arguments)||this;return t.type="dataset",t}function d1(t){t.registerComponentModel(l1),t.registerComponentView(c1)}qv([function(t){t.registerPainter("canvas",a1)},d1]),qv(t1);u(y1,f1=eg),y1.prototype.getInitialData=function(t){return V0(null,this,{useEncodeDefaulter:!0})},y1.prototype.getLegendIcon=function(t){var e=new Vr,n=cy("line",0,t.itemHeight/2,t.itemWidth,0,t.lineStyle.stroke,!1),n=(e.add(n),n.setStyle(t.lineStyle),this.getData().getVisual("symbol")),i=this.getData().getVisual("symbolRotate"),n="none"===n?"circle":n,r=.8*t.itemHeight,r=cy(n,(t.itemWidth-r)/2,(t.itemHeight-r)/2,r,r,t.itemStyle.fill),i=(e.add(r),r.setStyle(t.itemStyle),"inherit"===t.iconRotate?i:t.iconRotate||0);return r.rotation=i*Math.PI/180,r.setOrigin([t.itemWidth/2,t.itemHeight/2]),-1<n.indexOf("empty")&&(r.style.stroke=r.style.fill,r.style.fill="#fff",r.style.lineWidth=2),e},y1.type="series.line",y1.dependencies=["grid","polar"],y1.defaultOption={z:3,coordinateSystem:"cartesian2d",legendHoverLink:!0,clip:!0,label:{position:"top"},endLabel:{show:!1,valueAnimation:!0,distance:8},lineStyle:{width:2,type:"solid"},emphasis:{scale:!0},step:!1,smooth:!1,smoothMonotone:null,symbol:"emptyCircle",symbolSize:4,symbolRotate:null,showSymbol:!0,showAllSymbol:"auto",connectNulls:!1,sampling:"none",animationEasing:"linear",progressive:0,hoverLayerThreshold:1/0,universalTransition:{divideShape:"clone"},triggerLineEvent:!1};var f1,g1=y1;function y1(){var t=null!==f1&&f1.apply(this,arguments)||this;return t.type=y1.type,t.hasSymbolVisual=!0,t}function m1(t,e){var n,i=t.mapDimensionsAll("defaultedLabel"),r=i.length;if(1===r)return null!=(n=Jd(t,e,i[0]))?n+"":null;if(r){for(var o=[],a=0;a<i.length;a++)o.push(Jd(t,e,i[a]));return o.join(" ")}}u(x1,v1=Vr),x1.prototype._createSymbol=function(t,e,n,i,r){this.removeAll();r=cy(t,-1,-1,2,2,null,r);r.attr({z2:100,culling:!0,scaleX:i[0]/2,scaleY:i[1]/2}),r.drift=b1,this._symbolType=t,this.add(r)},x1.prototype.stopSymbolAnimation=function(t){this.childAt(0).stopAnimation(null,t)},x1.prototype.getSymbolType=function(){return this._symbolType},x1.prototype.getSymbolPath=function(){return this.childAt(0)},x1.prototype.highlight=function(){bl(this.childAt(0))},x1.prototype.downplay=function(){wl(this.childAt(0))},x1.prototype.setZ=function(t,e){var n=this.childAt(0);n.zlevel=t,n.z=e},x1.prototype.setDraggable=function(t,e){var n=this.childAt(0);n.draggable=t,n.cursor=!e&&t?"move":n.cursor},x1.prototype.updateData=function(t,e,n,i){this.silent=!1;var r,o,a,s=t.getItemVisual(e,"symbol")||"circle",l=t.hostModel,u=x1.getSymbolSize(t,e),h=s!==this._symbolType,c=i&&i.disableAnimation;h?(r=t.getItemVisual(e,"symbolKeepAspect"),this._createSymbol(s,t,e,u,r)):((o=this.childAt(0)).silent=!1,a={scaleX:u[0]/2,scaleY:u[1]/2},c?o.attr(a):kh(o,a,l,e),Th(s=o).oldStyle=s.style),this._updateCommon(t,e,u,n,i),h&&(o=this.childAt(0),c||(a={scaleX:this._sizeX,scaleY:this._sizeY,style:{opacity:o.style.opacity}},o.scaleX=o.scaleY=0,o.style.opacity=0,Ih(o,a,l,e))),c&&this.childAt(0).stopAnimation("leave")},x1.prototype._updateCommon=function(e,t,n,i,r){var o,a,s,l,u,h,c,p,d=this.childAt(0),f=e.hostModel,g=(i&&(o=i.emphasisItemStyle,s=i.blurItemStyle,a=i.selectItemStyle,l=i.focus,u=i.blurScope,c=i.labelStatesModels,p=i.hoverScale,y=i.cursorStyle,h=i.emphasisDisabled),i&&!e.hasItemOption||(o=(g=(i=i&&i.itemModel?i.itemModel:e.getItemModel(t)).getModel("emphasis")).getModel("itemStyle").getItemStyle(),a=i.getModel(["select","itemStyle"]).getItemStyle(),s=i.getModel(["blur","itemStyle"]).getItemStyle(),l=g.get("focus"),u=g.get("blurScope"),h=g.get("disabled"),c=Kh(i),p=g.getShallow("scale"),y=i.getShallow("cursor")),e.getItemVisual(t,"symbolRotate")),i=(d.attr("rotation",(g||0)*Math.PI/180||0),dy(e.getItemVisual(t,"symbolOffset"),n)),g=(i&&(d.x=i[0],d.y=i[1]),y&&d.attr("cursor",y),e.getItemVisual(t,"style")),i=g.fill,y=(d instanceof _s?(y=d.style,d.useStyle(P({image:y.image,x:y.x,y:y.y,width:y.width,height:y.height},g))):(d.__isEmptyBrush?d.useStyle(P({},g)):d.useStyle(g),d.style.decal=null,d.setColor(i,r&&r.symbolInnerColor),d.style.strokeNoScale=!0),e.getItemVisual(t,"liftZ")),m=this._z2,v=(null!=y?null==m&&(this._z2=d.z2,d.z2+=y):null!=m&&(d.z2=m,this._z2=null),r&&r.useNameLabel);jh(d,c,{labelFetcher:f,labelDataIndex:t,defaultText:function(t){return v?e.getName(t):m1(e,t)},inheritColor:i,defaultOpacity:g.opacity}),this._sizeX=n[0]/2,this._sizeY=n[1]/2;y=d.ensureState("emphasis"),y.style=o,d.ensureState("select").style=a,d.ensureState("blur").style=s,m=null==p||!0===p?Math.max(1.1,3/this._sizeY):isFinite(p)&&0<p?+p:1;y.scaleX=this._sizeX*m,y.scaleY=this._sizeY*m,this.setSymbolScale(1),Rl(this,l,u,h)},x1.prototype.setSymbolScale=function(t){this.scaleX=this.scaleY=t},x1.prototype.fadeOut=function(t,e,n){var i=this.childAt(0),r=Us(this).dataIndex,o=n&&n.animation;this.silent=i.silent=!0,n&&n.fadeLabel?(n=i.getTextContent())&&Ah(n,{style:{opacity:0}},e,{dataIndex:r,removeOpt:o,cb:function(){i.removeTextContent()}}):i.removeTextContent(),Ah(i,{style:{opacity:0},scaleX:0,scaleY:0},e,{dataIndex:r,cb:t,removeOpt:o})},x1.getSymbolSize=function(t,e){return py(t.getItemVisual(e,"symbolSize"))};var v1,_1=x1;function x1(t,e,n,i){var r=v1.call(this)||this;return r.updateData(t,e,n,i),r}function b1(t,e){this.parent.drift(t,e)}function w1(t,e,n,i){return e&&!isNaN(e[0])&&!isNaN(e[1])&&(!i.isIgnore||!i.isIgnore(n))&&(!i.clipShape||i.clipShape.contain(e[0],e[1]))&&"none"!==t.getItemVisual(n,"symbol")}function S1(t){return(t=null==t||R(t)?t:{isIgnore:t})||{}}function M1(t){var t=t.hostModel,e=t.getModel("emphasis");return{emphasisItemStyle:e.getModel("itemStyle").getItemStyle(),blurItemStyle:t.getModel(["blur","itemStyle"]).getItemStyle(),selectItemStyle:t.getModel(["select","itemStyle"]).getItemStyle(),focus:e.get("focus"),blurScope:e.get("blurScope"),emphasisDisabled:e.get("disabled"),hoverScale:e.get("scale"),labelStatesModels:Kh(t),cursorStyle:t.get("cursor")}}C1.prototype.updateData=function(o,a){this._progressiveEls=null,a=S1(a);var s=this.group,l=o.hostModel,u=this._data,h=this._SymbolCtor,c=a.disableAnimation,p=M1(o),d={disableAnimation:c},f=a.getSymbolPoint||function(t){return o.getItemLayout(t)};u||s.removeAll(),o.diff(u).add(function(t){var e,n=f(t);w1(o,n,t,a)&&((e=new h(o,t,p,d)).setPosition(n),o.setItemGraphicEl(t,e),s.add(e))}).update(function(t,e){var n,i,e=u.getItemGraphicEl(e),r=f(t);w1(o,r,t,a)?(n=o.getItemVisual(t,"symbol")||"circle",i=e&&e.getSymbolType&&e.getSymbolType(),!e||i&&i!==n?(s.remove(e),(e=new h(o,t,p,d)).setPosition(r)):(e.updateData(o,t,p,d),i={x:r[0],y:r[1]},c?e.attr(i):kh(e,i,l)),s.add(e),o.setItemGraphicEl(t,e)):s.remove(e)}).remove(function(t){var e=u.getItemGraphicEl(t);e&&e.fadeOut(function(){s.remove(e)},l)}).execute(),this._getSymbolPoint=f,this._data=o},C1.prototype.updateLayout=function(){var n=this,t=this._data;t&&t.eachItemGraphicEl(function(t,e){e=n._getSymbolPoint(e);t.setPosition(e),t.markRedraw()})},C1.prototype.incrementalPrepareUpdate=function(t){this._seriesScope=M1(t),this._data=null,this.group.removeAll()},C1.prototype.incrementalUpdate=function(t,e,n){function i(t){t.isGroup||(t.incremental=!0,t.ensureState("emphasis").hoverLayer=!0)}this._progressiveEls=[],n=S1(n);for(var r=t.start;r<t.end;r++){var o,a=e.getItemLayout(r);w1(e,a,r,n)&&((o=new this._SymbolCtor(e,r,this._seriesScope)).traverse(i),o.setPosition(a),this.group.add(o),e.setItemGraphicEl(r,o),this._progressiveEls.push(o))}},C1.prototype.eachRendered=function(t){Xh(this._progressiveEls||this.group,t)},C1.prototype.remove=function(t){var e=this.group,n=this._data;n&&t?n.eachItemGraphicEl(function(t){t.fadeOut(function(){e.remove(t)},n.hostModel)}):e.removeAll()};var T1=C1;function C1(t){this.group=new Vr,this._SymbolCtor=t||_1}function k1(t,e,n){var i=t.getBaseAxis(),r=t.getOtherAxis(i),n=function(t,e){var n=0,t=t.scale.getExtent();"start"===e?n=t[0]:"end"===e?n=t[1]:dt(e)&&!isNaN(e)?n=e:0<t[0]?n=t[0]:t[1]<0&&(n=t[1]);return n}(r,n),i=i.dim,r=r.dim,o=e.mapDimension(r),a=e.mapDimension(i),s="x"===r||"radius"===r?1:0,t=B(t.dimensions,function(t){return e.mapDimension(t)}),l=!1,u=e.getCalculationInfo("stackResultDimension");return B0(e,t[0])&&(l=!0,t[0]=u),B0(e,t[1])&&(l=!0,t[1]=u),{dataDimsForPoint:t,valueStart:n,valueAxisDim:r,baseAxisDim:i,stacked:!!l,valueDim:o,baseDim:a,baseDataOffset:s,stackedOverDimension:e.getCalculationInfo("stackedOverDimension")}}function I1(t,e,n,i){var r=NaN,o=(t.stacked&&(r=n.get(n.getCalculationInfo("stackedOverDimension"),i)),isNaN(r)&&(r=t.valueStart),t.baseDataOffset),a=[];return a[o]=n.get(t.baseDim,i),a[1-o]=r,e.dataToPoint(a)}var D1=Math.min,A1=Math.max;function L1(t,e){return isNaN(t)||isNaN(e)}function P1(t,e,n,i,r,o,a,s,l){for(var u,h,c,p,d=n,f=0;f<i;f++){var g=e[2*d],y=e[2*d+1];if(r<=d||d<0)break;if(L1(g,y)){if(l){d+=o;continue}break}if(d===n)t[0<o?"moveTo":"lineTo"](g,y),c=g,p=y;else{var m=g-u,v=y-h;if(m*m+v*v<.5){d+=o;continue}if(0<a){for(var _=d+o,x=e[2*_],b=e[2*_+1];x===g&&b===y&&f<i;)f++,d+=o,x=e[2*(_+=o)],b=e[2*_+1],g=e[2*d],y=e[2*d+1];var w=f+1;if(l)for(;L1(x,b)&&w<i;)w++,x=e[2*(_+=o)],b=e[2*_+1];var S,M,T,C,k,I,D,A,L,m=0,v=0,P=void 0,O=void 0;i<=w||L1(x,b)?(D=g,A=y):(m=x-u,v=b-h,S=g-u,M=x-g,T=y-h,C=b-y,I=k=void 0,O="x"===s?(D=g-(L=0<m?1:-1)*(k=Math.abs(S))*a,A=y,P=g+L*(I=Math.abs(M))*a,y):"y"===s?(A=y-(L=0<v?1:-1)*(k=Math.abs(T))*a,P=D=g,y+L*(I=Math.abs(C))*a):(k=Math.sqrt(S*S+T*T),D=g-m*a*(1-(S=(I=Math.sqrt(M*M+C*C))/(I+k))),A=y-v*a*(1-S),O=y+v*a*S,P=D1(P=g+m*a*S,A1(x,g)),O=D1(O,A1(b,y)),P=A1(P,D1(x,g)),A=y-(v=(O=A1(O,D1(b,y)))-y)*k/I,D=D1(D=g-(m=P-g)*k/I,A1(u,g)),A=D1(A,A1(h,y)),P=g+(m=g-(D=A1(D,D1(u,g))))*I/k,y+(v=y-(A=A1(A,D1(h,y))))*I/k)),t.bezierCurveTo(c,p,D,A,g,y),c=P,p=O}else t.lineTo(g,y)}u=g,h=y,d+=o}return f}var O1,R1=function(){this.smooth=0,this.smoothConstraint=!0},N1=(u(E1,O1=j),E1.prototype.getDefaultStyle=function(){return{stroke:"#000",fill:null}},E1.prototype.getDefaultShape=function(){return new R1},E1.prototype.buildPath=function(t,e){var n=e.points,i=0,r=n.length/2;if(e.connectNulls){for(;0<r&&L1(n[2*r-2],n[2*r-1]);r--);for(;i<r&&L1(n[2*i],n[2*i+1]);i++);}for(;i<r;)i+=P1(t,n,i,r,r,1,e.smooth,e.smoothMonotone,e.connectNulls)+1},E1.prototype.getPointOn=function(t,e){this.path||(this.createPathProxy(),this.buildPath(this.path,this.shape));for(var n,i,r=this.path.data,o=Za.CMD,a="x"===e,s=[],l=0;l<r.length;){var u=void 0,h=void 0;switch(r[l++]){case o.M:n=r[l++],i=r[l++];break;case o.L:var c,u=r[l++],h=r[l++];if((c=a?(t-n)/(u-n):(t-i)/(h-i))<=1&&0<=c)return v=a?(h-i)*c+i:(u-n)*c+n,a?[t,v]:[v,t];n=u,i=h;break;case o.C:u=r[l++],h=r[l++];var p=r[l++],d=r[l++],f=r[l++],g=r[l++],y=a?Bn(n,u,p,f,t,s):Bn(i,h,d,g,t,s);if(0<y)for(var m=0;m<y;m++){var v,_=s[m];if(_<=1&&0<=_)return v=a?En(i,h,d,g,_):En(n,u,p,f,_),a?[t,v]:[v,t]}n=f,i=g}}},E1);function E1(t){t=O1.call(this,t)||this;return t.type="ec-polyline",t}u(F1,z1=R1);var z1,B1=F1;function F1(){return null!==z1&&z1.apply(this,arguments)||this}u(G1,V1=j),G1.prototype.getDefaultShape=function(){return new B1},G1.prototype.buildPath=function(t,e){var n=e.points,i=e.stackedOnPoints,r=0,o=n.length/2,a=e.smoothMonotone;if(e.connectNulls){for(;0<o&&L1(n[2*o-2],n[2*o-1]);o--);for(;r<o&&L1(n[2*r],n[2*r+1]);r++);}for(;r<o;){var s=P1(t,n,r,o,o,1,e.smooth,a,e.connectNulls);P1(t,i,r+s-1,s,o,-1,e.stackedOnSmooth,a,e.connectNulls),r+=s+1,t.closePath()}};var V1,H1=G1;function G1(t){t=V1.call(this,t)||this;return t.type="ec-polygon",t}function W1(t,e){return t.type===e}function U1(t,e){if(t.length===e.length){for(var n=0;n<t.length;n++)if(t[n]!==e[n])return;return 1}}function X1(t){for(var e=1/0,n=1/0,i=-1/0,r=-1/0,o=0;o<t.length;){var a=t[o++],s=t[o++];isNaN(a)||(e=Math.min(a,e),i=Math.max(a,i)),isNaN(s)||(n=Math.min(s,n),r=Math.max(s,r))}return[[e,n],[i,r]]}function Y1(t,e){var t=X1(t),n=t[0],t=t[1],e=X1(e),i=e[0],e=e[1];return Math.max(Math.abs(n[0]-i[0]),Math.abs(n[1]-i[1]),Math.abs(t[0]-e[0]),Math.abs(t[1]-e[1]))}function q1(t){return dt(t)?t:t?.5:0}function Z1(t,e,n,i){var e=e.getBaseAxis(),r="x"===e.dim||"radius"===e.dim?0:1,o=[],a=0,s=[],l=[],u=[],h=[];if(i){for(a=0;a<t.length;a+=2)isNaN(t[a])||isNaN(t[a+1])||h.push(t[a],t[a+1]);t=h}for(a=0;a<t.length-2;a+=2)switch(u[0]=t[a+2],u[1]=t[a+3],l[0]=t[a],l[1]=t[a+1],o.push(l[0],l[1]),n){case"end":s[r]=u[r],s[1-r]=l[1-r],o.push(s[0],s[1]);break;case"middle":var c=[];s[r]=c[r]=(l[r]+u[r])/2,s[1-r]=l[1-r],c[1-r]=u[1-r],o.push(s[0],s[1]),o.push(c[0],c[1]);break;default:s[r]=l[r],s[1-r]=u[1-r],o.push(s[0],s[1])}return o.push(t[a++],t[a++]),o}function j1(t,e,n){var i=t.getVisual("visualMeta");if(i&&i.length&&t.count()&&"cartesian2d"===e.type){for(var r,o=i.length-1;0<=o;o--){var a,s=t.getDimensionInfo(i[o].dimension);if("x"===(a=s&&s.coordDim)||"y"===a){r=i[o];break}}if(r){var l=e.getAxis(a),e=B(r.stops,function(t){return{coord:l.toGlobalCoord(l.dataToCoord(t.value)),color:t.color}}),u=e.length,h=r.outerColors.slice(),n=(u&&e[0].coord>e[u-1].coord&&(e.reverse(),h.reverse()),function(t,e){var n,i,r=[],o=t.length;function a(t,e,n){var i=t.coord;return{coord:n,color:_i((n-i)/(e.coord-i),[t.color,e.color])}}for(var s=0;s<o;s++){var l=t[s],u=l.coord;if(u<0)n=l;else{if(e<u){i?r.push(a(i,l,e)):n&&r.push(a(n,l,0),a(n,l,e));break}n&&(r.push(a(n,l,0)),n=null),r.push(l),i=l}}return r}(e,"x"===a?n.getWidth():n.getHeight())),c=n.length;if(!c&&u)return e[0].coord<0?h[1]||e[u-1].color:h[0]||e[0].color;var p=n[0].coord-10,u=n[c-1].coord+10,d=u-p;if(d<.001)return"transparent";O(n,function(t){t.offset=(t.coord-p)/d}),n.push({offset:c?n[c-1].offset:.5,color:h[1]||"transparent"}),n.unshift({offset:c?n[0].offset:.5,color:h[0]||"transparent"});e=new ph(0,0,0,0,n,!0);return e[a]=p,e[a+"2"]=u,e}}}function K1(t,e,n){var t=t.get("showAllSymbol"),i="auto"===t;if(!t||i){var r,o,a=n.getAxesByScale("ordinal")[0];if(a)if(!i||!function(t,e){for(var n=t.getExtent(),i=Math.abs(n[1]-n[0])/t.scale.count(),r=(isNaN(i)&&(i=0),e.count()),o=Math.max(1,Math.round(r/5)),a=0;a<r;a+=o)if(1.5*_1.getSymbolSize(e,a)[t.isHorizontal()?1:0]>i)return;return 1}(a,e))return r=e.mapDimension(a.dim),o={},O(a.getViewLabels(),function(t){t=a.scale.getRawOrdinalNumber(t.tickValue);o[t]=1}),function(t){return!o.hasOwnProperty(e.get(r,t))}}}function $1(t){for(var e,n,i=t.length/2;0<i&&(e=t[2*i-2],n=t[2*i-1],isNaN(e)||isNaN(n));i--);return i-1}function Q1(t,e){return[t[2*e],t[2*e+1]]}function J1(t){if(t.get(["endLabel","show"]))return 1;for(var e=0;e<Qs.length;e++)if(t.get([Qs[e],"endLabel","show"]))return 1}function tx(n,i,e,t){var r,o,a,s,l,u,h,c,p,d,f,g,y,m,v,_,x;return W1(i,"cartesian2d")?(r=t.getModel("endLabel"),o=r.get("valueAnimation"),a=t.getData(),s={lastFrameIndex:0},l=J1(t)?function(t,e){n._endLabelOnDuring(t,e,a,s,o,r,i)}:null,u=i.getBaseAxis().isHorizontal(),c=e,p=t,d=function(){var t=n._endLabel;t&&e&&null!=s.originalX&&t.attr({x:s.originalX,y:s.originalY})},f=l,g=(v=(h=i).getArea()).x,y=v.y,m=v.width,v=v.height,_=p.get(["lineStyle","width"])||2,g-=_/2,y-=_/2,m+=_,v+=_,m=Math.ceil(m),g!==Math.floor(g)&&(g=Math.floor(g),m++),x=new ks({shape:{x:g,y:y,width:m,height:v}}),c&&(c=(_=h.getBaseAxis()).isHorizontal(),h=_.inverse,c?(h&&(x.shape.x+=m),x.shape.width=0):(h||(x.shape.y+=v),x.shape.height=0),_=D(f)?function(t){f(t,x)}:null,Ih(x,{shape:{width:m,height:v,x:g,y:y}},p,null,d,_)),c=x,t.get("clip",!0)||(h=c.shape,m=Math.max(h.width,h.height),u?(h.y-=m,h.height+=2*m):(h.x-=m,h.width+=2*m)),l&&l(1,c),c):(v=e,g=t,p=(y=i).getArea(),d=to(p.r0,1),_=to(p.r,1),u=new Au({shape:{cx:to(y.cx,1),cy:to(y.cy,1),r0:d,r:_,startAngle:p.startAngle,endAngle:p.endAngle,clockwise:p.clockwise}}),v&&("angle"===y.getBaseAxis().dim?u.shape.endAngle=p.startAngle:u.shape.r=d,Ih(u,{shape:{endAngle:p.endAngle,r:_}},g)),u)}u(ix,ex=fg),ix.prototype.init=function(){var t=new Vr,e=new T1;this.group.add(e.group),this._symbolDraw=e,this._lineGroup=t},ix.prototype.render=function(t,e,n){function i(t){o._changePolyState(t)}var r,o=this,a=t.coordinateSystem,s=this.group,l=t.getData(),u=t.getModel("lineStyle"),h=t.getModel("areaStyle"),c=l.getLayout("points")||[],p="polar"===a.type,d=this._coordSys,f=this._symbolDraw,g=this._polyline,y=this._polygon,m=this._lineGroup,e=!e.ssr&&t.get("animation"),v=!h.isEmpty(),_=h.get("origin"),x=k1(a,l,_),x=v&&function(t,e,n){if(!n.valueDim)return[];for(var i=e.count(),r=hv(2*i),o=0;o<i;o++){var a=I1(n,t,e,o);r[2*o]=a[0],r[2*o+1]=a[1]}return r}(a,l,x),b=t.get("showSymbol"),w=t.get("connectNulls"),S=b&&!p&&K1(t,l,a),M=this._data,T=(M&&M.eachItemGraphicEl(function(t,e){t.__temp&&(s.remove(t),M.setItemGraphicEl(e,null))}),b||f.remove(),s.add(m),!p&&t.get("step")),C=(a&&a.getArea&&t.get("clip",!0)&&(null!=(r=a.getArea()).width?(r.x-=.1,r.y-=.1,r.width+=.2,r.height+=.2):r.r0&&(r.r0-=.5,r.r+=.5)),this._clipShapeForSymbol=r,j1(l,a,n)||l.getVisual("style")[l.getVisual("drawType")]),d=(g&&d.type===a.type&&T===this._step?(v&&!y?y=this._newPolygon(c,x):y&&!v&&(m.remove(y),y=this._polygon=null),p||this._initOrUpdateEndLabel(t,a,lp(C)),(d=m.getClipPath())?Ih(d,{shape:tx(this,a,!1,t).shape},t):m.setClipPath(tx(this,a,!0,t)),b&&f.updateData(l,{isIgnore:S,clipShape:r,disableAnimation:!0,getSymbolPoint:function(t){return[c[2*t],c[2*t+1]]}}),U1(this._stackedOnPoints,x)&&U1(this._points,c)||(e?this._doUpdateAnimation(l,x,a,n,T,_,w):(T&&(c=Z1(c,a,T,w),x=x&&Z1(x,a,T,w)),g.setShape({points:c}),y&&y.setShape({points:c,stackedOnPoints:x})))):(b&&f.updateData(l,{isIgnore:S,clipShape:r,disableAnimation:!0,getSymbolPoint:function(t){return[c[2*t],c[2*t+1]]}}),e&&this._initSymbolLabelAnimation(l,a,r),T&&(c=Z1(c,a,T,w),x=x&&Z1(x,a,T,w)),g=this._newPolyline(c),v?y=this._newPolygon(c,x):y&&(m.remove(y),y=this._polygon=null),p||this._initOrUpdateEndLabel(t,a,lp(C)),m.setClipPath(tx(this,a,!0,t))),t.getModel("emphasis")),n=d.get("focus"),b=d.get("blurScope"),f=d.get("disabled"),S=(g.useStyle(z(u.getLineStyle(),{fill:"none",stroke:C,lineJoin:"bevel"})),zl(g,t,"lineStyle"),0<g.style.lineWidth&&"bolder"===t.get(["emphasis","lineStyle","width"])&&(g.getState("emphasis").style.lineWidth=+g.style.lineWidth+1),Us(g).seriesIndex=t.seriesIndex,Rl(g,n,b,f),q1(t.get("smooth"))),e=t.get("smoothMonotone");g.setShape({smooth:S,smoothMonotone:e,connectNulls:w}),y&&(r=l.getCalculationInfo("stackedOnSeries"),v=0,y.useStyle(z(h.getAreaStyle(),{fill:C,opacity:.7,lineJoin:"bevel",decal:l.getVisual("style").decal})),r&&(v=q1(r.get("smooth"))),y.setShape({smooth:S,stackedOnSmooth:v,smoothMonotone:e,connectNulls:w}),zl(y,t,"areaStyle"),Us(y).seriesIndex=t.seriesIndex,Rl(y,n,b,f));l.eachItemGraphicEl(function(t){t&&(t.onHoverStateChange=i)}),this._polyline.onHoverStateChange=i,this._data=l,this._coordSys=a,this._stackedOnPoints=x,this._points=c,this._step=T,this._valueOrigin=_,t.get("triggerLineEvent")&&(this.packEventData(t,g),y)&&this.packEventData(t,y)},ix.prototype.packEventData=function(t,e){Us(e).eventData={componentType:"series",componentSubType:"line",componentIndex:t.componentIndex,seriesIndex:t.seriesIndex,seriesName:t.name,seriesType:"line"}},ix.prototype.highlight=function(t,e,n,i){var r=t.getData(),o=Do(r,i);if(this._changePolyState("emphasis"),!(o instanceof Array)&&null!=o&&0<=o){var a=r.getLayout("points");if(!(l=r.getItemGraphicEl(o))){var s=a[2*o],a=a[2*o+1];if(isNaN(s)||isNaN(a))return;if(this._clipShapeForSymbol&&!this._clipShapeForSymbol.contain(s,a))return;var l,u=t.get("zlevel")||0,h=t.get("z")||0,s=((l=new _1(r,o)).x=s,l.y=a,l.setZ(u,h),l.getSymbolPath().getTextContent());s&&(s.zlevel=u,s.z=h,s.z2=this._polyline.z2+1),l.__temp=!0,r.setItemGraphicEl(o,l),l.stopSymbolAnimation(!0),this.group.add(l)}l.highlight()}else fg.prototype.highlight.call(this,t,e,n,i)},ix.prototype.downplay=function(t,e,n,i){var r,o=t.getData(),a=Do(o,i);this._changePolyState("normal"),null!=a&&0<=a?(r=o.getItemGraphicEl(a))&&(r.__temp?(o.setItemGraphicEl(a,null),this.group.remove(r)):r.downplay()):fg.prototype.downplay.call(this,t,e,n,i)},ix.prototype._changePolyState=function(t){var e=this._polygon;gl(this._polyline,t),e&&gl(e,t)},ix.prototype._newPolyline=function(t){var e=this._polyline;return e&&this._lineGroup.remove(e),e=new N1({shape:{points:t},segmentIgnoreThreshold:2,z2:10}),this._lineGroup.add(e),this._polyline=e},ix.prototype._newPolygon=function(t,e){var n=this._polygon;return n&&this._lineGroup.remove(n),n=new H1({shape:{points:t,stackedOnPoints:e},segmentIgnoreThreshold:2}),this._lineGroup.add(n),this._polygon=n},ix.prototype._initSymbolLabelAnimation=function(t,l,u){var h,c,e=l.getBaseAxis(),p=e.inverse,e=("cartesian2d"===l.type?(h=e.isHorizontal(),c=!1):"polar"===l.type&&(h="angle"===e.dim,c=!0),t.hostModel),d=e.get("animationDuration"),f=(D(d)&&(d=d(null)),e.get("animationDelay")||0),g=D(f)?f(null):f;t.eachItemGraphicEl(function(t,e){var n,i,r,o,a,s=t;s&&(o=[t.x,t.y],a=i=n=void 0,u&&(a=c?(r=u,o=l.pointToCoord(o),h?(n=r.startAngle,i=r.endAngle,-o[1]/180*Math.PI):(n=r.r0,i=r.r,o[0])):h?(n=u.x,i=u.x+u.width,t.x):(n=u.y+u.height,i=u.y,t.y)),r=i===n?0:(a-n)/(i-n),p&&(r=1-r),o=D(f)?f(e):d*r+g,a=(t=s.getSymbolPath()).getTextContent(),s.attr({scaleX:0,scaleY:0}),s.animateTo({scaleX:1,scaleY:1},{duration:200,setToFinal:!0,delay:o}),a&&a.animateFrom({style:{opacity:0}},{duration:300,delay:o}),t.disableLabelAnimation=!0)})},ix.prototype._initOrUpdateEndLabel=function(t,e,n){var u,i,r,o=t.getModel("endLabel");J1(t)?(u=t.getData(),i=this._polyline,(r=u.getLayout("points"))?(this._endLabel||((this._endLabel=new Ps({z2:200})).ignoreClip=!0,i.setTextContent(this._endLabel),i.disableLabelAnimation=!0),0<=(r=$1(r))&&(jh(i,Kh(t,"endLabel"),{inheritColor:n,labelFetcher:t,labelDataIndex:r,defaultText:function(t,e,n){if(null==n)return m1(u,t);var i=u,r=n,o=i.mapDimensionsAll("defaultedLabel");if(!F(r))return r+"";for(var a=[],s=0;s<o.length;s++){var l=i.getDimensionIndex(o[s]);0<=l&&a.push(r[l])}return a.join(" ")},enableTextSetter:!0},(n=o,r=(t=(t=e).getBaseAxis()).isHorizontal(),t=t.inverse,o=r?t?"right":"left":"center",r=r?"middle":t?"top":"bottom",{normal:{align:n.get("align")||o,verticalAlign:n.get("verticalAlign")||r}})),i.textConfig.position=null)):(i.removeTextContent(),this._endLabel=null)):this._endLabel&&(this._polyline.removeTextContent(),this._endLabel=null)},ix.prototype._endLabelOnDuring=function(t,e,n,i,r,o,a){var s,l,u,h,c,p,d,f,g,y,m=this._endLabel,v=this._polyline;m&&(t<1&&null==i.originalX&&(i.originalX=m.x,i.originalY=m.y),s=n.getLayout("points"),g=(l=n.hostModel).get("connectNulls"),u=o.get("precision"),o=o.get("distance")||0,c=(a=a.getBaseAxis()).isHorizontal(),a=a.inverse,e=e.shape,h=(c?o:0)*(a?-1:1),o=(c?0:-o)*(a?-1:1),d=void 0,1<=(f=(p=(c=function(t,e,n){for(var i,r,o=t.length/2,a="x"===n?0:1,s=0,l=-1,u=0;u<o;u++)if(r=t[2*u+a],!isNaN(r)&&!isNaN(t[2*u+1-a])){if(0!==u){if(i<=e&&e<=r||e<=i&&r<=e){l=u;break}s=u}i=r}return{range:[s,l],t:(e-i)/(r-i)}}(s,a=a?c?e.x:e.y+e.height:c?e.x+e.width:e.y,e=c?"x":"y")).range)[1]-p[0])?(1<f&&!g?(y=Q1(s,p[0]),m.attr({x:y[0]+h,y:y[1]+o}),r&&(d=l.getRawValue(p[0]))):((y=v.getPointOn(a,e))&&m.attr({x:y[0]+h,y:y[1]+o}),f=l.getRawValue(p[0]),g=l.getRawValue(p[1]),r&&(d=zo(n,u,f,g,c.t))),i.lastFrameIndex=p[0]):(y=Q1(s,v=1===t||0<i.lastFrameIndex?p[0]:0),r&&(d=l.getRawValue(v)),m.attr({x:y[0]+h,y:y[1]+o})),r)&&"function"==typeof(a=ic(m)).setLabelText&&a.setLabelText(d)},ix.prototype._doUpdateAnimation=function(t,e,n,i,r,o,a){var s=this._polyline,l=this._polygon,u=t.hostModel,e=function(t,e,n,i,r,o){a=[],e.diff(t).add(function(t){a.push({cmd:"+",idx:t})}).update(function(t,e){a.push({cmd:"=",idx:e,idx1:t})}).remove(function(t){a.push({cmd:"-",idx:t})}).execute();for(var a,s=a,l=[],u=[],h=[],c=[],p=[],d=[],f=[],g=k1(r,e,o),y=t.getLayout("points")||[],m=e.getLayout("points")||[],v=0;v<s.length;v++){var _=s[v],x=!0,b=void 0;switch(_.cmd){case"=":var w=2*_.idx,b=2*_.idx1,S=y[w],M=y[1+w],T=m[b],C=m[b+1];(isNaN(S)||isNaN(M))&&(S=T,M=C),l.push(S,M),u.push(T,C),h.push(n[w],n[1+w]),c.push(i[b],i[b+1]),f.push(e.getRawIndex(_.idx1));break;case"+":S=_.idx,M=g.dataDimsForPoint,T=r.dataToPoint([e.get(M[0],S),e.get(M[1],S)]),C=(b=2*S,l.push(T[0],T[1]),u.push(m[b],m[b+1]),I1(g,r,e,S));h.push(C[0],C[1]),c.push(i[b],i[b+1]),f.push(e.getRawIndex(S));break;case"-":x=!1}x&&(p.push(_),d.push(d.length))}d.sort(function(t,e){return f[t]-f[e]});for(var k=hv(o=l.length),I=hv(o),D=hv(o),A=hv(o),L=[],v=0;v<d.length;v++){var P=d[v],O=2*v,R=2*P;k[O]=l[R],k[1+O]=l[1+R],I[O]=u[R],I[1+O]=u[1+R],D[O]=h[R],D[1+O]=h[1+R],A[O]=c[R],A[1+O]=c[1+R],L[v]=p[P]}return{current:k,next:I,stackedOnCurrent:D,stackedOnNext:A,status:L}}(this._data,t,this._stackedOnPoints,e,this._coordSys,this._valueOrigin),h=e.current,c=e.stackedOnCurrent,p=e.next,d=e.stackedOnNext;if(r&&(h=Z1(e.current,n,r,a),c=Z1(e.stackedOnCurrent,n,r,a),p=Z1(e.next,n,r,a),d=Z1(e.stackedOnNext,n,r,a)),3e3<Y1(h,p)||l&&3e3<Y1(c,d))s.stopAnimation(),s.setShape({points:p}),l&&(l.stopAnimation(),l.setShape({points:p,stackedOnPoints:d}));else{s.shape.__points=e.current,s.shape.points=h;for(var f,n={shape:{points:p}},g=(e.current!==h&&(n.shape.__points=e.next),s.stopAnimation(),kh(s,n,u),l&&(l.setShape({points:h,stackedOnPoints:c}),l.stopAnimation(),kh(l,{shape:{stackedOnPoints:d}},u),s.shape.points!==l.shape.points)&&(l.shape.points=s.shape.points),[]),y=e.status,m=0;m<y.length;m++)"="===y[m].cmd&&(f=t.getItemGraphicEl(y[m].idx1))&&g.push({el:f,ptIdx:m});s.animators&&s.animators.length&&s.animators[0].during(function(){l&&l.dirtyShape();for(var t=s.shape.__points,e=0;e<g.length;e++){var n=g[e].el,i=2*g[e].ptIdx;n.x=t[i],n.y=t[1+i],n.markRedraw()}})}},ix.prototype.remove=function(t){var n=this.group,i=this._data;this._lineGroup.removeAll(),this._symbolDraw.remove(!0),i&&i.eachItemGraphicEl(function(t,e){t.__temp&&(n.remove(t),i.setItemGraphicEl(e,null))}),this._polyline=this._polygon=this._coordSys=this._points=this._stackedOnPoints=this._endLabel=this._data=null},ix.type="line";var ex,nx=ix;function ix(){return null!==ex&&ex.apply(this,arguments)||this}var rx={average:function(t){for(var e=0,n=0,i=0;i<t.length;i++)isNaN(t[i])||(e+=t[i],n++);return 0===n?NaN:e/n},sum:function(t){for(var e=0,n=0;n<t.length;n++)e+=t[n]||0;return e},max:function(t){for(var e=-1/0,n=0;n<t.length;n++)t[n]>e&&(e=t[n]);return isFinite(e)?e:NaN},min:function(t){for(var e=1/0,n=0;n<t.length;n++)t[n]<e&&(e=t[n]);return isFinite(e)?e:NaN},minmax:function(t){for(var e=-1/0,n=-1/0,i=0;i<t.length;i++){var r=t[i],o=Math.abs(r);e<o&&(e=o,n=r)}return isFinite(n)?n:NaN},nearest:function(t){return t[0]}},ox=function(t){return Math.round(t.length/2)};qv(function(t){var i;t.registerChartView(nx),t.registerSeriesModel(g1),t.registerLayout((i=!0,{seriesType:"line",plan:cg(),reset:function(t){var h,e,c,p,d,n=t.getData(),f=t.coordinateSystem,t=t.pipelineContext,g=i||t.large;if(f)return t=B(f.dimensions,function(t){return n.mapDimension(t)}).slice(0,2),h=t.length,e=n.getCalculationInfo("stackResultDimension"),B0(n,t[0])&&(t[0]=e),B0(n,t[1])&&(t[1]=e),c=n.getStore(),p=n.getDimensionIndex(t[0]),d=n.getDimensionIndex(t[1]),h&&{progress:function(t,e){for(var n=t.end-t.start,i=g&&hv(n*h),r=[],o=[],a=t.start,s=0;a<t.end;a++){var l,u=void 0;u=1===h?(l=c.get(p,a),f.dataToPoint(l,null,o)):(r[0]=c.get(p,a),r[1]=c.get(d,a),f.dataToPoint(r,null,o)),g?(i[s++]=u[0],i[s++]=u[1]):e.setItemLayout(a,u.slice())}g&&e.setLayout("points",i)}}}})),t.registerVisual({seriesType:"line",reset:function(t){var e=t.getData(),t=t.getModel("lineStyle").getLineStyle();t&&!t.stroke&&(t.stroke=e.getVisual("style").fill),e.setVisual("legendLineStyle",t)}}),t.registerProcessor(t.PRIORITY.PROCESSOR.STATISTIC,{seriesType:"line",reset:function(t,e,n){var i,r=t.getData(),o=t.get("sampling"),a=t.coordinateSystem,s=r.count();10<s&&"cartesian2d"===a.type&&o&&(i=a.getBaseAxis(),a=a.getOtherAxis(i),i=i.getExtent(),n=n.getDevicePixelRatio(),i=Math.abs(i[1]-i[0])*(n||1),n=Math.round(s/i),isFinite(n))&&1<n&&("lttb"===o&&t.setData(r.lttbDownSample(r.mapDimension(a.dim),1/n)),s=void 0,V(o)?s=rx[o]:D(o)&&(s=o),s)&&t.setData(r.downSample(r.mapDimension(a.dim),1/n,s,ox))}})});u(lx,ax=g),lx.type="grid",lx.dependencies=["xAxis","yAxis"],lx.layoutMode="box",lx.defaultOption={show:!1,z:0,left:"10%",top:60,right:"10%",bottom:70,containLabel:!1,backgroundColor:"rgba(0,0,0,0)",borderWidth:1,borderColor:"#ccc"};var ax,sx=lx;function lx(){return null!==ax&&ax.apply(this,arguments)||this}u(cx,ux=g),cx.prototype.getCoordSysModel=function(){return this.getReferringComponents("grid",Ro).models[0]},cx.type="cartesian2dAxis";var ux,hx=cx;function cx(){return null!==ux&&ux.apply(this,arguments)||this}at(hx,Wv);var lc={show:!0,z:0,inverse:!1,name:"",nameLocation:"end",nameRotate:null,nameTruncate:{maxWidth:null,ellipsis:"...",placeholder:"."},nameTextStyle:{},nameGap:15,silent:!1,triggerEvent:!1,tooltip:{show:!1},axisPointer:{},axisLine:{show:!0,onZero:!0,onZeroAxisIndex:null,lineStyle:{color:"#6E7079",width:1,type:"solid"},symbol:["none","none"],symbolSize:[10,15]},axisTick:{show:!0,inside:!1,length:5,lineStyle:{width:1}},axisLabel:{show:!0,inside:!1,rotate:0,showMinLabel:null,showMaxLabel:null,margin:8,fontSize:12},splitLine:{show:!0,lineStyle:{color:["#E0E6F1"],width:1,type:"solid"}},splitArea:{show:!1,areaStyle:{color:["rgba(250,250,250,0.2)","rgba(210,219,238,0.2)"]}}},Uo=d({boundaryGap:!0,deduplication:null,splitLine:{show:!1},axisTick:{alignWithLabel:!1,interval:"auto"},axisLabel:{interval:"auto"}},lc),ey=d({boundaryGap:[0,0],axisLine:{show:"auto"},axisTick:{show:"auto"},splitNumber:5,minorTick:{show:!1,splitNumber:5,length:3,lineStyle:{}},minorSplitLine:{show:!1,lineStyle:{color:"#F4F7FD",width:1}}},lc),px={category:Uo,value:ey,time:d({splitNumber:6,axisLabel:{showMinLabel:!1,showMaxLabel:!1,rich:{primary:{fontWeight:"bold"}}},splitLine:{show:!1}},ey),log:z({logBase:10},ey)},dx={value:1,category:1,time:1,log:1};function fx(o,a,s,l){O(dx,function(t,r){var e,n=d(d({},px[r],!0),l,!0),n=(u(i,e=s),i.prototype.mergeDefaultAndTheme=function(t,e){var n=fp(this),i=n?yp(t):{};d(t,e.getTheme().get(r+"Axis")),d(t,this.getDefaultOption()),t.type=gx(t),n&&gp(t,i,n)},i.prototype.optionUpdated=function(){"category"===this.option.type&&(this.__ordinalMeta=U0.createByAxisModel(this))},i.prototype.getCategories=function(t){var e=this.option;if("category"===e.type)return t?e.data:this.__ordinalMeta.categories},i.prototype.getOrdinalMeta=function(){return this.__ordinalMeta},i.type=a+"Axis."+r,i.defaultOption=n,i);function i(){var t=null!==e&&e.apply(this,arguments)||this;return t.type=a+"Axis."+r,t}o.registerComponentModel(n)}),o.registerSubTypeDefaulter(a+"Axis",gx)}function gx(t){return t.type||(t.data?"category":"value")}function yx(t){this.type="cartesian",this._dimList=[],this._axes={},this.name=t||""}yx.prototype.getAxis=function(t){return this._axes[t]},yx.prototype.getAxes=function(){return B(this._dimList,function(t){return this._axes[t]},this)},yx.prototype.getAxesByScale=function(e){return e=e.toLowerCase(),ut(this.getAxes(),function(t){return t.scale.type===e})},yx.prototype.addAxis=function(t){var e=t.dim;this._axes[e]=t,this._dimList.push(e)};var mx=["x","y"];function vx(t){return"interval"===t.type||"time"===t.type}u(bx,_x=yx),bx.prototype.calcAffineTransform=function(){this._transform=this._invTransform=null;var t,e,n,i,r=this.getAxis("x").scale,o=this.getAxis("y").scale;vx(r)&&vx(o)&&(r=r.getExtent(),o=o.getExtent(),i=this.dataToPoint([r[0],o[0]]),e=this.dataToPoint([r[1],o[1]]),t=r[1]-r[0],n=o[1]-o[0],t)&&n&&(t=(e[0]-i[0])/t,e=(e[1]-i[1])/n,n=i[0]-r[0]*t,r=i[1]-o[0]*e,i=this._transform=[t,0,0,e,n,r],this._invTransform=Be([],i))},bx.prototype.getBaseAxis=function(){return this.getAxesByScale("ordinal")[0]||this.getAxesByScale("time")[0]||this.getAxis("x")},bx.prototype.containPoint=function(t){var e=this.getAxis("x"),n=this.getAxis("y");return e.contain(e.toLocalCoord(t[0]))&&n.contain(n.toLocalCoord(t[1]))},bx.prototype.containData=function(t){return this.getAxis("x").containData(t[0])&&this.getAxis("y").containData(t[1])},bx.prototype.containZone=function(t,e){var t=this.dataToPoint(t),e=this.dataToPoint(e),n=this.getArea(),e=new X(t[0],t[1],e[0]-t[0],e[1]-t[1]);return n.intersect(e)},bx.prototype.dataToPoint=function(t,e,n){n=n||[];var i,r=t[0],o=t[1];return this._transform&&null!=r&&isFinite(r)&&null!=o&&isFinite(o)?ee(n,t,this._transform):(t=this.getAxis("x"),i=this.getAxis("y"),n[0]=t.toGlobalCoord(t.dataToCoord(r,e)),n[1]=i.toGlobalCoord(i.dataToCoord(o,e)),n)},bx.prototype.clampData=function(t,e){var n=this.getAxis("x").scale,i=this.getAxis("y").scale,r=n.getExtent(),o=i.getExtent(),n=n.parse(t[0]),i=i.parse(t[1]);return(e=e||[])[0]=Math.min(Math.max(Math.min(r[0],r[1]),n),Math.max(r[0],r[1])),e[1]=Math.min(Math.max(Math.min(o[0],o[1]),i),Math.max(o[0],o[1])),e},bx.prototype.pointToData=function(t,e){var n,i,r=[];return this._invTransform?ee(r,t,this._invTransform):(n=this.getAxis("x"),i=this.getAxis("y"),r[0]=n.coordToData(n.toLocalCoord(t[0]),e),r[1]=i.coordToData(i.toLocalCoord(t[1]),e),r)},bx.prototype.getOtherAxis=function(t){return this.getAxis("x"===t.dim?"y":"x")},bx.prototype.getArea=function(t){t=t||0;var e=this.getAxis("x").getGlobalExtent(),n=this.getAxis("y").getGlobalExtent(),i=Math.min(e[0],e[1])-t,r=Math.min(n[0],n[1])-t,e=Math.max(e[0],e[1])-i+t,n=Math.max(n[0],n[1])-r+t;return new X(i,r,e,n)};var _x,xx=bx;function bx(){var t=null!==_x&&_x.apply(this,arguments)||this;return t.type="cartesian2d",t.dimensions=mx,t}u(Tx,Sx=Nc),Tx.prototype.isHorizontal=function(){var t=this.position;return"top"===t||"bottom"===t},Tx.prototype.getGlobalExtent=function(t){var e=this.getExtent();return e[0]=this.toGlobalCoord(e[0]),e[1]=this.toGlobalCoord(e[1]),t&&e[0]>e[1]&&e.reverse(),e},Tx.prototype.pointToData=function(t,e){return this.coordToData(this.toLocalCoord(t["x"===this.dim?0:1]),e)},Tx.prototype.setCategorySortInfo=function(t){if("category"!==this.type)return!1;this.model.option.categorySortInfo=t,this.scale.setSortInfo(t)};var Sx,Mx=Tx;function Tx(t,e,n,i,r){t=Sx.call(this,t,e,n)||this;return t.index=0,t.type=i||"value",t.position=r||"bottom",t}function Cx(t){return"cartesian2d"===t.get("coordinateSystem")}function kx(i){var r={xAxisModel:null,yAxisModel:null};return O(r,function(t,e){var n=e.replace(/Model$/,""),n=i.getReferringComponents(n,Ro).models[0];r[e]=n}),r}var Ix=Math.log;Ax.prototype.getRect=function(){return this._rect},Ax.prototype.update=function(t,e){var n=this._axesMap;function i(t){var d,e=I(t),n=e.length;if(n){for(var i=[],r=n-1;0<=r;r--){var o=t[+e[r]],a=o.model,s=o.scale;q0(s)&&a.get("alignTicks")&&null==a.get("interval")?i.push(o):(zv(s,a),q0(s)&&(d=o))}i.length&&(d||zv((d=i.pop()).scale,d.model),O(i,function(t){var e=t.scale,t=t.model,n=d.scale,i=av.prototype,r=i.getTicks.call(n),o=i.getTicks.call(n,!0),a=r.length-1,n=i.getInterval.call(n),s=(t=Ev(e,t)).extent,l=t.fixMin,t=t.fixMax,u=("log"===e.type&&(u=Ix(e.base),s=[Ix(s[0])/u,Ix(s[1])/u]),e.setExtent(s[0],s[1]),e.calcNiceExtent({splitNumber:a,fixMin:l,fixMax:t}),i.getExtent.call(e)),h=(l&&(s[0]=u[0]),t&&(s[1]=u[1]),i.getInterval.call(e)),c=s[0],p=s[1];if(l&&t)h=(p-c)/a;else if(l)for(p=s[0]+h*a;p<s[1]&&isFinite(p)&&isFinite(s[1]);)h=j0(h),p=s[0]+h*a;else if(t)for(c=s[1]-h*a;c>s[0]&&isFinite(c)&&isFinite(s[0]);)h=j0(h),c=s[1]-h*a;else{u=(h=a<e.getTicks().length-1?j0(h):h)*a;(c=to((p=Math.ceil(s[1]/h)*h)-u))<0&&0<=s[0]?(c=0,p=to(u)):0<p&&s[1]<=0&&(p=0,c=-to(u))}l=(r[0].value-o[0].value)/n,t=(r[a].value-o[a].value)/n,i.setExtent.call(e,c+h*l,p+h*t),i.setInterval.call(e,h),(l||t)&&i.setNiceExtent.call(e,c+h,p-h)}))}}this._updateScale(t,this.model),i(n.x),i(n.y);var r={};O(n.x,function(t){Px(n,"y",t,r)}),O(n.y,function(t){Px(n,"x",t,r)}),this.resize(this.model,e)},Ax.prototype.resize=function(t,e,n){var i=t.getBoxLayoutParams(),n=!n&&t.get("containLabel"),a=dp(i,{width:e.getWidth(),height:e.getHeight()}),r=(this._rect=a,this._axesList);function o(){O(r,function(t){var e,n,i=t.isHorizontal(),r=i?[0,a.width]:[0,a.height],o=t.inverse?1:0;t.setExtent(r[o],r[1-o]),r=t,e=i?a.x:a.y,o=r.getExtent(),n=o[0]+o[1],r.toGlobalCoord="x"===r.dim?function(t){return t+e}:function(t){return n-t+e},r.toLocalCoord="x"===r.dim?function(t){return t-e}:function(t){return n-t+e}})}o(),n&&(O(r,function(t){var e,n,i;t.model.get(["axisLabel","inside"])||(e=Vv(t))&&(n=t.isHorizontal()?"height":"width",i=t.model.get(["axisLabel","margin"]),a[n]-=e[n]+i,"top"===t.position?a.y+=e.height+i:"left"===t.position&&(a.x+=e.width+i))}),o()),O(this._coordsList,function(t){t.calcAffineTransform()})},Ax.prototype.getAxis=function(t,e){t=this._axesMap[t];if(null!=t)return t[e||0]},Ax.prototype.getAxes=function(){return this._axesList.slice()},Ax.prototype.getCartesian=function(t,e){if(null!=t&&null!=e)return this._coordsMap["x"+t+"y"+e];R(t)&&(e=t.yAxisIndex,t=t.xAxisIndex);for(var n=0,i=this._coordsList;n<i.length;n++)if(i[n].getAxis("x").index===t||i[n].getAxis("y").index===e)return i[n]},Ax.prototype.getCartesians=function(){return this._coordsList.slice()},Ax.prototype.convertToPixel=function(t,e,n){e=this._findConvertTarget(e);return e.cartesian?e.cartesian.dataToPoint(n):e.axis?e.axis.toGlobalCoord(e.axis.dataToCoord(n)):null},Ax.prototype.convertFromPixel=function(t,e,n){e=this._findConvertTarget(e);return e.cartesian?e.cartesian.pointToData(n):e.axis?e.axis.coordToData(e.axis.toLocalCoord(n)):null},Ax.prototype._findConvertTarget=function(t){var e,n,i=t.seriesModel,r=t.xAxisModel||i&&i.getReferringComponents("xAxis",Ro).models[0],o=t.yAxisModel||i&&i.getReferringComponents("yAxis",Ro).models[0],t=t.gridModel,a=this._coordsList;return i?k(a,e=i.coordinateSystem)<0&&(e=null):r&&o?e=this.getCartesian(r.componentIndex,o.componentIndex):r?n=this.getAxis("x",r.componentIndex):o?n=this.getAxis("y",o.componentIndex):t&&t.coordinateSystem===this&&(e=this._coordsList[0]),{cartesian:e,axis:n}},Ax.prototype.containPoint=function(t){var e=this._coordsList[0];if(e)return e.containPoint(t)},Ax.prototype._initCartesian=function(o,t,e){var a=this,s=this,l={left:!1,right:!1,top:!1,bottom:!1},u={x:{},y:{}},h={x:0,y:0};function n(r){return function(t,e){var n,i;Lx(t,o)&&(n=t.get("position"),"x"===r?"top"!==n&&"bottom"!==n&&(n=l.bottom?"top":"bottom"):"left"!==n&&"right"!==n&&(n=l.left?"right":"left"),l[n]=!0,i="category"===(n=new Mx(r,Bv(t),[0,0],t.get("type"),n)).type,n.onBand=i&&t.get("boundaryGap"),n.inverse=t.get("inverse"),(t.axis=n).model=t,n.grid=s,n.index=e,s._axesList.push(n),u[r][e]=n,h[r]++)}}t.eachComponent("xAxis",n("x"),this),t.eachComponent("yAxis",n("y"),this),h.x&&h.y?O((this._axesMap=u).x,function(i,r){O(u.y,function(t,e){var e="x"+r+"y"+e,n=new xx(e);n.master=a,n.model=o,a._coordsMap[e]=n,a._coordsList.push(n),n.addAxis(i),n.addAxis(t)})}):(this._axesMap={},this._axesList=[])},Ax.prototype._updateScale=function(t,i){function r(e,n){var i,t,r;O((i=e,t=n.dim,r={},O(i.mapDimensionsAll(t),function(t){r[F0(i,t)]=!0}),I(r)),function(t){n.scale.unionExtentFromData(e,t)})}O(this._axesList,function(t){var e;t.scale.setExtent(1/0,-1/0),"category"===t.type&&(e=t.model.get("categorySortInfo"),t.scale.setSortInfo(e))}),t.eachSeries(function(t){var e,n;Cx(t)&&(n=(e=kx(t)).xAxisModel,e=e.yAxisModel,Lx(n,i))&&Lx(e,i)&&(n=this.getCartesian(n.componentIndex,e.componentIndex),e=t.getData(),t=n.getAxis("x"),n=n.getAxis("y"),r(e,t),r(e,n))},this)},Ax.prototype.getTooltipAxes=function(n){var i=[],r=[];return O(this.getCartesians(),function(t){var e=null!=n&&"auto"!==n?t.getAxis(n):t.getBaseAxis(),t=t.getOtherAxis(e);k(i,e)<0&&i.push(e),k(r,t)<0&&r.push(t)}),{baseAxes:i,otherAxes:r}},Ax.create=function(i,r){var o=[];return i.eachComponent("grid",function(t,e){var n=new Ax(t,i,r);n.name="grid_"+e,n.resize(t,r,!0),t.coordinateSystem=n,o.push(n)}),i.eachSeries(function(t){var e,n,i;Cx(t)&&(e=(n=kx(t)).xAxisModel,n=n.yAxisModel,i=e.getCoordSysModel().coordinateSystem,t.coordinateSystem=i.getCartesian(e.componentIndex,n.componentIndex))}),o},Ax.dimensions=mx;var Dx=Ax;function Ax(t,e,n){this.type="grid",this._coordsMap={},this._coordsList=[],this._axesMap={},this._axesList=[],this.axisPointerEnabled=!0,this.dimensions=mx,this._initCartesian(t,e,n),this.model=t}function Lx(t,e){return t.getCoordSysModel()===e}function Px(t,e,n,i){n.getAxesOnZeroOf=function(){return r?[r]:[]};var r,o=t[e],t=n.model,e=t.get(["axisLine","onZero"]),n=t.get(["axisLine","onZeroAxisIndex"]);if(e){if(null!=n)Ox(o[n])&&(r=o[n]);else for(var a in o)if(o.hasOwnProperty(a)&&Ox(o[a])&&!i[s(o[a])]){r=o[a];break}r&&(i[s(r)]=!0)}function s(t){return t.dim+"_"+t.index}}function Ox(t){return t&&"category"!==t.type&&"time"!==t.type&&(e=(t=(t=t).scale.getExtent())[0],t=t[1],!(0<e&&0<t||e<0&&t<0));var e}var Rx=Math.PI,Nx=(Ex.prototype.hasBuilder=function(t){return!!zx[t]},Ex.prototype.add=function(t){zx[t](this.opt,this.axisModel,this.group,this._transformGroup)},Ex.prototype.getGroup=function(){return this.group},Ex.innerTextLayout=function(t,e,n){var i,e=oo(e-t),t=ao(e)?(i=0<n?"top":"bottom","center"):ao(e-Rx)?(i=0<n?"bottom":"top","center"):(i="middle",0<e&&e<Rx?0<n?"right":"left":0<n?"left":"right");return{rotation:e,textAlign:t,textVerticalAlign:i}},Ex.makeAxisEventDataBase=function(t){var e={componentType:t.mainType,componentIndex:t.componentIndex};return e[t.mainType+"Index"]=t.componentIndex,e},Ex.isLabelSilent=function(t){var e=t.get("tooltip");return t.get("silent")||!(t.get("triggerEvent")||e&&e.show)},Ex);function Ex(t,e){this.group=new Vr,this.opt=e,this.axisModel=t,z(e,{labelOffset:0,nameDirection:1,tickDirection:1,labelDirection:1,silent:!0,handleAutoShown:function(){return!0}});t=new Vr({x:e.position[0],y:e.position[1],rotation:e.rotation});t.updateTransform(),this._transformGroup=t}var zx={axisLine:function(i,t,r,e){var o,a,s,l,u,h,c,n=t.get(["axisLine","show"]);(n="auto"===n&&i.handleAutoShown?i.handleAutoShown("axisLine"):n)&&(n=t.axis.getExtent(),e=e.transform,o=[n[0],0],a=[n[1],0],s=a[0]<o[0],e&&(ee(o,o,e),ee(a,a,e)),l=P({lineCap:"round"},t.getModel(["axisLine","lineStyle"]).getLineStyle()),Hh((n=new Zu({shape:{x1:o[0],y1:o[1],x2:a[0],y2:a[1]},style:l,strokeContainThreshold:i.strokeContainThreshold||5,silent:!0,z2:1})).shape,n.style.lineWidth),n.anid="line",r.add(n),null!=(u=t.get(["axisLine","symbol"])))&&(e=t.get(["axisLine","symbolSize"]),V(u)&&(u=[u,u]),(V(e)||dt(e))&&(e=[e,e]),n=dy(t.get(["axisLine","symbolOffset"])||0,e),h=e[0],c=e[1],O([{rotate:i.rotation+Math.PI/2,offset:n[0],r:0},{rotate:i.rotation-Math.PI/2,offset:n[1],r:Math.sqrt((o[0]-a[0])*(o[0]-a[0])+(o[1]-a[1])*(o[1]-a[1]))}],function(t,e){var n;"none"!==u[e]&&null!=u[e]&&(e=cy(u[e],-h/2,-c/2,h,c,l.stroke,!0),n=t.r+t.offset,e.attr({rotation:t.rotate,x:(t=s?a:o)[0]+n*Math.cos(i.rotation),y:t[1]-n*Math.sin(i.rotation),silent:!0,z2:11}),r.add(e))}))},axisTickLabel:function(t,e,n,i){var r,o,a,s,l,u=function(t,e,n,i){var r=n.axis,o=n.getModel("axisTick"),a=o.get("show");"auto"===a&&i.handleAutoShown&&(a=i.handleAutoShown("axisTick"));if(a&&!r.scale.isBlank()){for(var a=o.getModel("lineStyle"),i=i.tickDirection*o.get("length"),s=Hx(r.getTicksCoords(),e.transform,i,z(a.getLineStyle(),{stroke:n.get(["axisLine","lineStyle","color"])}),"ticks"),l=0;l<s.length;l++)t.add(s[l]);return s}}(n,i,e,t),h=function(f,g,y,m){var v,_,x,b,w,S,M,T,C=y.axis,t=bt(m.axisLabelShow,y.get(["axisLabel","show"]));if(t&&!C.scale.isBlank())return v=y.getModel("axisLabel"),_=v.get("margin"),x=C.getViewLabels(),t=(bt(m.labelRotate,v.get("rotate"))||0)*Rx/180,b=Nx.innerTextLayout(m.rotation,t,m.labelDirection),w=y.getCategories&&y.getCategories(!0),S=[],M=Nx.isLabelSilent(y),T=y.get("triggerEvent"),O(x,function(t,e){var n="ordinal"===C.scale.type?C.scale.getRawOrdinalNumber(t.tickValue):t.tickValue,i=t.formattedLabel,r=t.rawLabel,o=v,a=(o=w&&w[n]&&R(a=w[n])&&a.textStyle?new _c(a.textStyle,v,y.ecModel):o).getTextColor()||y.get(["axisLine","lineStyle","color"]),s=C.dataToCoord(n),l=o.getShallow("align",!0)||b.textAlign,u=N(o.getShallow("alignMinLabel",!0),l),h=N(o.getShallow("alignMaxLabel",!0),l),c=o.getShallow("verticalAlign",!0)||o.getShallow("baseline",!0)||b.textVerticalAlign,p=N(o.getShallow("verticalAlignMinLabel",!0),c),d=N(o.getShallow("verticalAlignMaxLabel",!0),c),s=new Ps({x:s,y:m.labelOffset+m.labelDirection*_,rotation:b.rotation,silent:M,z2:10+(t.level||0),style:$h(o,{text:i,align:0===e?u:e===x.length-1?h:l,verticalAlign:0===e?p:e===x.length-1?d:c,fill:D(a)?a("category"===C.type?r:"value"===C.type?n+"":n,e):a})});s.anid="label_"+n,T&&((t=Nx.makeAxisEventDataBase(y)).targetType="axisLabel",t.value=r,t.tickIndex=e,"category"===C.type&&(t.dataIndex=n),Us(s).eventData=t),g.add(s),s.updateTransform(),S.push(s),f.add(s),s.decomposeTransform()}),S}(n,i,e,t),c=(o=h,u=u,Gv((r=e).axis)||(d=r.get(["axisLabel","showMinLabel"]),r=r.get(["axisLabel","showMaxLabel"]),u=u||[],y=(o=o||[])[0],f=o[1],a=o[o.length-1],o=o[o.length-2],s=u[0],g=u[1],l=u[u.length-1],u=u[u.length-2],!1===d?(Bx(y),Bx(s)):Fx(y,f)&&(d?(Bx(f),Bx(g)):(Bx(y),Bx(s))),!1===r?(Bx(a),Bx(l)):Fx(o,a)&&(r?(Bx(o),Bx(u)):(Bx(a),Bx(l)))),n),p=i,d=e,f=t.tickDirection,g=d.axis,y=d.getModel("minorTick");if(y.get("show")&&!g.scale.isBlank()){var m=g.getMinorTicksCoords();if(m.length)for(var g=y.getModel("lineStyle"),v=f*y.get("length"),_=z(g.getLineStyle(),z(d.getModel("axisTick").getLineStyle(),{stroke:d.get(["axisLine","lineStyle","color"])})),x=0;x<m.length;x++)for(var b=Hx(m[x],p.transform,v,_,"minorticks_"+x),w=0;w<b.length;w++)c.add(b[w])}e.get(["axisLabel","hideOverlap"])&&W_(H_(B(h,function(t){return{label:t,priority:t.z2,defaultAttr:{ignore:t.ignore}}})))},axisName:function(t,e,n,i){var r,o,a,s,l,u,h,c,p,d,f=bt(t.axisName,e.get("name"));f&&(a=e.get("nameLocation"),u=t.nameDirection,r=e.getModel("nameTextStyle"),d=e.get("nameGap")||0,l=(h=e.axis.getExtent())[0]>h[1]?-1:1,l=["start"===a?h[0]-l*d:"end"===a?h[1]+l*d:(h[0]+h[1])/2,Vx(a)?t.labelOffset+u*d:0],null!=(d=e.get("nameRotate"))&&(d=d*Rx/180),Vx(a)?o=Nx.innerTextLayout(t.rotation,null!=d?d:t.rotation,u):(o=function(t,e,n,i){var r,n=oo(n-t),t=i[0]>i[1],i="start"===e&&!t||"start"!==e&&t;e=ao(n-Rx/2)?(r=i?"bottom":"top","center"):ao(n-1.5*Rx)?(r=i?"top":"bottom","center"):(r="middle",n<1.5*Rx&&Rx/2<n?i?"left":"right":i?"right":"left");return{rotation:n,textAlign:e,textVerticalAlign:r}}(t.rotation,a,d||0,h),null!=(s=t.axisNameAvailableWidth)&&(s=Math.abs(s/Math.sin(o.rotation)),isFinite(s)||(s=null))),u=r.getFont(),d=(a=e.get("nameTruncate",!0)||{}).ellipsis,h=bt(t.nameTruncateMaxWidth,a.maxWidth,s),t=new Ps({x:l[0],y:l[1],rotation:o.rotation,silent:Nx.isLabelSilent(e),style:$h(r,{text:f,font:u,overflow:"truncate",width:h,ellipsis:d,fill:r.getTextColor()||e.get(["axisLine","lineStyle","color"]),align:r.get("align")||o.textAlign,verticalAlign:r.get("verticalAlign")||o.textVerticalAlign}),z2:1}),s=(a={el:t,componentModel:e,itemName:f}).itemTooltipOption,l=a.componentModel,u=a.itemName,s=V(s)?{formatter:s}:s,h=l.mainType,l=l.componentIndex,(c={componentType:h,name:u,$vars:["name"]})[h+"Index"]=l,(p=a.formatterParamsExtra)&&O(I(p),function(t){Bt(c,t)||(c[t]=p[t],c.$vars.push(t))}),(a=Us(a.el)).componentMainType=h,a.componentIndex=l,a.tooltipConfig={name:u,option:z({content:u,encodeHTMLContent:!0,formatterParams:c},s)},t.__fullText=f,t.anid="name",e.get("triggerEvent")&&((d=Nx.makeAxisEventDataBase(e)).targetType="axisName",d.name=f,Us(t).eventData=d),i.add(t),t.updateTransform(),n.add(t),t.decomposeTransform())}};function Bx(t){t&&(t.ignore=!0)}function Fx(t,e){var n,i=t&&t.getBoundingRect().clone(),r=e&&e.getBoundingRect().clone();if(i&&r)return Ee(n=Pe([]),n,-t.rotation),i.applyTransform(Re([],n,t.getLocalTransform())),r.applyTransform(Re([],n,e.getLocalTransform())),i.intersect(r)}function Vx(t){return"middle"===t||"center"===t}function Hx(t,e,n,i,r){for(var o=[],a=[],s=[],l=0;l<t.length;l++){var u=t[l].coord,u=(a[0]=u,s[a[1]=0]=u,s[1]=n,e&&(ee(a,a,e),ee(s,s,e)),new Zu({shape:{x1:a[0],y1:a[1],x2:s[0],y2:s[1]},style:i,z2:2,autoBatch:!0,silent:!0}));Hh(u.shape,u.style.lineWidth),u.anid=r+"_"+t[l].tickValue,o.push(u)}return o}function Gx(t){var e=(t.ecModel.getComponent("axisPointer")||{}).coordSysAxesInfo;return e&&e.axesInfo[t.type+"||"+t.id]}var Wx,Ux={},ay=(u(Xx,Wx=ug),Xx.prototype.render=function(t,e,n,i){var r,o,a,s,l,u;this.axisPointerClass&&(r=Gx(r=t))&&(l=r.axisPointerModel,o=r.axis.scale,a=l.option,u=l.get("status"),null!=(s=l.get("value"))&&(s=o.parse(s)),l=!!l.get(["handle","show"]),null==u&&(a.status=l?"show":"hide"),(u=o.getExtent().slice())[0]>u[1]&&u.reverse(),(s=null==s||s>u[1]?u[1]:s)<u[0]&&(s=u[0]),a.value=s,l)&&(a.status=r.axis.scale.isBlank()?"hide":"show"),Wx.prototype.render.apply(this,arguments),this._doUpdateAxisPointerClass(t,n,!0)},Xx.prototype.updateAxisPointer=function(t,e,n,i){this._doUpdateAxisPointerClass(t,n,!1)},Xx.prototype.remove=function(t,e){var n=this._axisPointer;n&&n.remove(e)},Xx.prototype.dispose=function(t,e){this._disposeAxisPointer(e),Wx.prototype.dispose.apply(this,arguments)},Xx.prototype._doUpdateAxisPointerClass=function(t,e,n){var i,r=Xx.getAxisPointerClass(this.axisPointerClass);r&&((i=(i=Gx(i=t))&&i.axisPointerModel)?(this._axisPointer||(this._axisPointer=new r)).render(t,i,e,n):this._disposeAxisPointer(e))},Xx.prototype._disposeAxisPointer=function(t){this._axisPointer&&this._axisPointer.dispose(t),this._axisPointer=null},Xx.registerAxisPointerClass=function(t,e){Ux[t]=e},Xx.getAxisPointerClass=function(t){return t&&Ux[t]},Xx.type="axis",Xx);function Xx(){var t=null!==Wx&&Wx.apply(this,arguments)||this;return t.type=Xx.type,t}var Yx=Ao();var qx,Zx=["axisLine","axisTickLabel","axisName"],jx=["splitArea","splitLine","minorSplitLine"],uu=(u(Kx,qx=ay),Kx.prototype.render=function(i,t,e,n){this.group.removeAll();var r,o,a,s,l,u,h,c,p,d,f,g,y,m,v,_=this._axisGroup;function x(t){var e={x:t.x,y:t.y,rotation:t.rotation};return null!=t.shape&&(e.shape=P({},t.shape)),e}this._axisGroup=new Vr,this.group.add(this._axisGroup),i.get("show")&&(r=i.getCoordSysModel(),o=i,a=a||{},d=(d=r).coordinateSystem,f=o.axis,s={},g=f.getAxesOnZeroOf()[0],l=f.position,u=g?"onZero":l,f=f.dim,d=[(d=d.getRect()).x,d.x+d.width,d.y,d.y+d.height],h={left:0,right:1,top:0,bottom:1,onZero:2},c=o.get("offset")||0,c="x"===f?[d[2]-c,d[3]+c]:[d[0]-c,d[1]+c],g&&(p=g.toGlobalCoord(g.dataToCoord(0)),c[h.onZero]=Math.max(Math.min(p,c[1]),c[0])),s.position=["y"===f?c[h[u]]:d[0],"x"===f?c[h[u]]:d[3]],s.rotation=Math.PI/2*("x"===f?0:1),s.labelDirection=s.tickDirection=s.nameDirection={top:-1,bottom:1,left:-1,right:1}[l],s.labelOffset=g?c[h[l]]-c[h.onZero]:0,o.get(["axisTick","inside"])&&(s.tickDirection=-s.tickDirection),bt(a.labelInside,o.get(["axisLabel","inside"]))&&(s.labelDirection=-s.labelDirection),p=o.get(["axisLabel","rotate"]),s.labelRotate="top"===u?-p:p,s.z2=1,d=new Nx(i,P({handleAutoShown:function(t){for(var e=r.coordinateSystem.getCartesians(),n=0;n<e.length;n++)if(q0(e[n].getOtherAxis(i.axis).scale))return!0;return!1}},s)),O(Zx,d.add,d),this._axisGroup.add(d.getGroup()),O(jx,function(t){i.get([t,"show"])&&Qx[t](this,this._axisGroup,i,r)},this),n&&"changeAxisOrder"===n.type&&n.isInitSort||(f=_,g=this._axisGroup,y=i,f&&g&&(v={},f.traverse(function(t){Gh(t)&&t.anid&&(v[t.anid]=t)}),m=v,g.traverse(function(t){var e,n;Gh(t)&&t.anid&&(e=m[t.anid])&&(n=x(t),t.attr(x(e)),kh(t,n,y,Us(t).dataIndex))}))),qx.prototype.render.call(this,i,t,e,n))},Kx.prototype.remove=function(){Yx(this).splitAreaColors=null},Kx.type="cartesianAxis",Kx);function Kx(){var t=null!==qx&&qx.apply(this,arguments)||this;return t.type=Kx.type,t.axisPointerClass="CartesianAxisPointer",t}var $x,Qx={splitLine:function(t,e,n,i){var r=n.axis;if(!r.scale.isBlank())for(var n=n.getModel("splitLine"),o=n.getModel("lineStyle"),a=F(a=o.get("color"))?a:[a],s=i.coordinateSystem.getRect(),l=r.isHorizontal(),u=0,h=r.getTicksCoords({tickModel:n}),c=[],p=[],d=o.getLineStyle(),f=0;f<h.length;f++){var g=r.toGlobalCoord(h[f].coord),g=(l?(c[0]=g,c[1]=s.y,p[0]=g,p[1]=s.y+s.height):(c[0]=s.x,c[1]=g,p[0]=s.x+s.width,p[1]=g),u++%a.length),y=h[f].tickValue,y=new Zu({anid:null!=y?"line_"+h[f].tickValue:null,autoBatch:!0,shape:{x1:c[0],y1:c[1],x2:p[0],y2:p[1]},style:z({stroke:a[g]},d),silent:!0});Hh(y.shape,d.lineWidth),e.add(y)}},minorSplitLine:function(t,e,n,i){var r=n.axis,n=n.getModel("minorSplitLine").getModel("lineStyle"),o=i.coordinateSystem.getRect(),a=r.isHorizontal(),s=r.getMinorTicksCoords();if(s.length)for(var l=[],u=[],h=n.getLineStyle(),c=0;c<s.length;c++)for(var p=0;p<s[c].length;p++){var d=r.toGlobalCoord(s[c][p].coord),d=(a?(l[0]=d,l[1]=o.y,u[0]=d,u[1]=o.y+o.height):(l[0]=o.x,l[1]=d,u[0]=o.x+o.width,u[1]=d),new Zu({anid:"minor_line_"+s[c][p].tickValue,autoBatch:!0,shape:{x1:l[0],y1:l[1],x2:u[0],y2:u[1]},style:h,silent:!0}));Hh(d.shape,h.lineWidth),e.add(d)}},splitArea:function(t,e,n,i){var r=e,e=i,o=(i=n).axis;if(!o.scale.isBlank()){var i=i.getModel("splitArea"),n=i.getModel("areaStyle"),a=n.get("color"),s=e.coordinateSystem.getRect(),l=o.getTicksCoords({tickModel:i,clamp:!0});if(l.length){var u=a.length,h=Yx(t).splitAreaColors,c=E(),p=0;if(h)for(var d=0;d<l.length;d++){var f=h.get(l[d].tickValue);if(null!=f){p=(f+(u-1)*d)%u;break}}for(var g=o.toGlobalCoord(l[0].coord),y=n.getAreaStyle(),a=F(a)?a:[a],d=1;d<l.length;d++){var m=o.toGlobalCoord(l[d].coord),v=void 0,_=void 0,x=void 0,b=void 0,g=o.isHorizontal()?(v=g,_=s.y,b=s.height,v+(x=m-v)):(v=s.x,_=g,x=s.width,_+(b=m-_)),m=l[d-1].tickValue;null!=m&&c.set(m,p),r.add(new ks({anid:null!=m?"area_"+m:null,shape:{x:v,y:_,width:x,height:b},style:z({fill:a[p]},y),autoBatch:!0,silent:!0})),p=(p+1)%u}Yx(t).splitAreaColors=c}}}},Jx=(u(tb,$x=uu),tb.type="xAxis",tb);function tb(){var t=null!==$x&&$x.apply(this,arguments)||this;return t.type=tb.type,t}u(ib,eb=uu),ib.type="yAxis";var eb,nb=ib;function ib(){var t=null!==eb&&eb.apply(this,arguments)||this;return t.type=Jx.type,t}u(ab,rb=ug),ab.prototype.render=function(t,e){this.group.removeAll(),t.get("show")&&this.group.add(new ks({shape:t.coordinateSystem.getRect(),style:z({fill:t.get("backgroundColor")},t.getItemStyle()),silent:!0,z2:-1}))},ab.type="grid";var rb,ob=ab;function ab(){var t=null!==rb&&rb.apply(this,arguments)||this;return t.type="grid",t}var sb={offset:0};function lb(t){_o(t,"label",["show"])}qv(function(t){t.registerComponentView(ob),t.registerComponentModel(sx),t.registerCoordinateSystem("cartesian2d",Dx),fx(t,"x",hx,sb),fx(t,"y",hx,sb),t.registerComponentView(Jx),t.registerComponentView(nb),t.registerPreprocessor(function(t){t.xAxis&&t.yAxis&&!t.grid&&(t.grid={})})});var ub,hb=Ao(),cb=(u(pb,ub=g),pb.prototype.init=function(t,e,n){this.mergeDefaultAndTheme(t,n),this._mergeOption(t,n,!1,!0)},pb.prototype.isAnimationEnabled=function(){var t;return!p.node&&(t=this.__hostSeries,this.getShallow("animation"))&&t&&t.isAnimationEnabled()},pb.prototype.mergeOption=function(t,e){this._mergeOption(t,e,!1,!1)},pb.prototype._mergeOption=function(t,i,e,r){var o=this.mainType;e||i.eachSeries(function(t){var e=t.get(this.mainType,!0),n=hb(t)[o];e&&e.data?(n?n._mergeOption(e,i,!0):(r&&lb(e),O(e.data,function(t){t instanceof Array?(lb(t[0]),lb(t[1])):lb(t)}),P(n=this.createMarkerModelFromSeries(e,this,i),{mainType:this.mainType,seriesIndex:t.seriesIndex,name:t.name,createdBySelf:!0}),n.__hostSeries=t),hb(t)[o]=n):hb(t)[o]=null},this)},pb.prototype.formatTooltip=function(t,e,n){var i=this.getData(),r=this.getRawValue(t),i=i.getName(t);return Kf("section",{header:this.name,blocks:[Kf("nameValue",{name:i,value:r,noName:!i,noValue:null==r})]})},pb.prototype.getData=function(){return this._data},pb.prototype.setData=function(t){this._data=t},pb.prototype.getDataParams=function(t,e){t=ef.prototype.getDataParams.call(this,t,e),e=this.__hostSeries;return e&&(t.seriesId=e.id,t.seriesName=e.name,t.seriesType=e.subType),t},pb.getMarkerModelFromSeries=function(t,e){return hb(t)[e]},pb.type="marker",pb.dependencies=["series","grid","polar","geo"],pb);function pb(){var t=null!==ub&&ub.apply(this,arguments)||this;return t.type=pb.type,t.createdBySelf=!1,t}at(cb,ef.prototype);u(gb,db=cb),gb.prototype.createMarkerModelFromSeries=function(t,e,n){return new gb(t,e,n)},gb.type="markLine",gb.defaultOption={z:5,symbol:["circle","arrow"],symbolSize:[8,16],symbolOffset:0,precision:2,tooltip:{trigger:"item"},label:{show:!0,position:"end",distance:5},lineStyle:{type:"dashed"},emphasis:{label:{show:!0},lineStyle:{width:3}},animationEasing:"linear"};var db,fb=gb;function gb(){var t=null!==db&&db.apply(this,arguments)||this;return t.type=gb.type,t}function yb(t,e,n,i,r,o){var a=[],s=B0(e,i)?e.getCalculationInfo("stackResultDimension"):i,t=bb(e,s,t),t=e.indicesOfNearest(s,t)[0],r=(a[r]=e.get(n,t),a[o]=e.get(s,t),e.get(i,t)),n=no(e.get(i,t));return 0<=(n=Math.min(n,20))&&(a[o]=+a[o].toFixed(n)),[a,r]}var mb={min:ct(yb,"min"),max:ct(yb,"max"),average:ct(yb,"average"),median:ct(yb,"median")};function vb(t,e){if(e){var n,i=t.getData(),r=t.coordinateSystem,o=r&&r.dimensions;if(n=e,(isNaN(parseFloat(n.x))||isNaN(parseFloat(n.y)))&&!F(e.coord)&&F(o)&&(n=_b(e,i,r,t),(e=y(e)).type&&mb[e.type]&&n.baseAxis&&n.valueAxis?(r=k(o,n.baseAxis.dim),t=k(o,n.valueAxis.dim),n=mb[e.type](i,n.baseDataDim,n.valueDataDim,r,t),e.coord=n[0],e.value=n[1]):e.coord=[null!=e.xAxis?e.xAxis:e.radiusAxis,null!=e.yAxis?e.yAxis:e.angleAxis]),null!=e.coord&&F(o))for(var a=e.coord,s=0;s<2;s++)mb[a[s]]&&(a[s]=bb(i,i.mapDimension(o[s]),a[s]));else e.coord=[];return e}}function _b(t,e,n,i){var r={};return null!=t.valueIndex||null!=t.valueDim?(r.valueDataDim=null!=t.valueIndex?e.getDimension(t.valueIndex):t.valueDim,r.valueAxis=n.getAxis(function(t,e){t=t.getData().getDimensionInfo(e);return t&&t.coordDim}(i,r.valueDataDim)),r.baseAxis=n.getOtherAxis(r.valueAxis),r.baseDataDim=e.mapDimension(r.baseAxis.dim)):(r.baseAxis=i.getBaseAxis(),r.valueAxis=n.getOtherAxis(r.baseAxis),r.baseDataDim=e.mapDimension(r.baseAxis.dim),r.valueDataDim=e.mapDimension(r.valueAxis.dim)),r}function xb(t,e){return!(t&&t.containData&&e.coord&&(n=e,isNaN(parseFloat(n.x)))&&isNaN(parseFloat(n.y)))||t.containData(e.coord);var n}function bb(t,e,n){var i,r;return"average"===n?(r=i=0,t.each(e,function(t,e){isNaN(t)||(i+=t,r++)}),i/r):"median"===n?t.getMedian(e):t.getDataExtent(e)["max"===n?1:0]}var wb,Sb=Zu.prototype,Mb=th.prototype,Tb=function(){this.x1=0,this.y1=0,this.x2=0,this.y2=0,this.percent=1};function Cb(){return null!==wb&&wb.apply(this,arguments)||this}function kb(t){return isNaN(+t.cpx1)||isNaN(+t.cpy1)}u(Cb,wb=Tb);u(Ab,Ib=j),Ab.prototype.getDefaultStyle=function(){return{stroke:"#000",fill:null}},Ab.prototype.getDefaultShape=function(){return new Tb},Ab.prototype.buildPath=function(t,e){(kb(e)?Sb:Mb).buildPath.call(this,t,e)},Ab.prototype.pointAt=function(t){return(kb(this.shape)?Sb:Mb).pointAt.call(this,t)},Ab.prototype.tangentAt=function(t){var e=this.shape,e=kb(e)?[e.x2-e.x1,e.y2-e.y1]:Mb.tangentAt.call(this,t);return jt(e,e)};var Ib,Db=Ab;function Ab(t){t=Ib.call(this,t)||this;return t.type="ec-line",t}var Lb=["fromSymbol","toSymbol"];function Pb(t){return"_"+t+"Type"}function Ob(t,e,n){var i,r,o,a=e.getItemVisual(n,t);return a&&"none"!==a?(i=e.getItemVisual(n,t+"Size"),r=e.getItemVisual(n,t+"Rotate"),o=e.getItemVisual(n,t+"Offset"),e=e.getItemVisual(n,t+"KeepAspect"),a+(n=py(i))+dy(o||0,n)+(r||"")+(e||"")):a}function Rb(t,e,n){var i,r,o,a=e.getItemVisual(n,t);if(a&&"none"!==a)return o=e.getItemVisual(n,t+"Size"),i=e.getItemVisual(n,t+"Rotate"),r=e.getItemVisual(n,t+"Offset"),e=e.getItemVisual(n,t+"KeepAspect"),r=dy(r||0,n=py(o)),(o=cy(a,-n[0]/2+r[0],-n[1]/2+r[1],n[0],n[1],null,e)).__specifiedRotation=null==i||isNaN(i)?void 0:+i*Math.PI/180||0,o.name=t,o}function Nb(t,e){t.x1=e[0][0],t.y1=e[0][1],t.x2=e[1][0],t.y2=e[1][1],t.percent=1;e=e[2];e?(t.cpx1=e[0],t.cpy1=e[1]):(t.cpx1=NaN,t.cpy1=NaN)}u(Bb,Eb=Vr),Bb.prototype._createLine=function(n,i,t){var e,r=n.hostModel,o=n.getItemLayout(i),o=(o=o,Nb((e=new Db({name:"line",subPixelOptimize:!0})).shape,o),e);o.shape.percent=0,Ih(o,{shape:{percent:1}},r,i),this.add(o),O(Lb,function(t){var e=Rb(t,n,i);this.add(e),this[Pb(t)]=Ob(t,n,i)},this),this._updateCommonStl(n,i,t)},Bb.prototype.updateData=function(i,r,t){var e=i.hostModel,n=this.childOfName("line"),o=i.getItemLayout(r),a={shape:{}};Nb(a.shape,o),kh(n,a,e,r),O(Lb,function(t){var e=Ob(t,i,r),n=Pb(t);this[n]!==e&&(this.remove(this.childOfName(t)),t=Rb(t,i,r),this.add(t)),this[n]=e},this),this._updateCommonStl(i,r,t)},Bb.prototype.getLinePath=function(){return this.childAt(0)},Bb.prototype._updateCommonStl=function(n,t,e){var i=n.hostModel,o=this.childOfName("line"),r=e&&e.emphasisLineStyle,a=e&&e.blurLineStyle,s=e&&e.selectLineStyle,l=e&&e.labelStatesModels,u=e&&e.emphasisDisabled,h=e&&e.focus,c=e&&e.blurScope,p=(e&&!n.hasItemOption||(r=(f=(e=n.getItemModel(t)).getModel("emphasis")).getModel("lineStyle").getLineStyle(),a=e.getModel(["blur","lineStyle"]).getLineStyle(),s=e.getModel(["select","lineStyle"]).getLineStyle(),u=f.get("disabled"),h=f.get("focus"),c=f.get("blurScope"),l=Kh(e)),n.getItemVisual(t,"style")),d=p.stroke,f=(o.useStyle(p),o.style.fill=null,o.style.strokeNoScale=!0,o.ensureState("emphasis").style=r,o.ensureState("blur").style=a,o.ensureState("select").style=s,O(Lb,function(t){var e=this.childOfName(t);if(e){e.setColor(d),e.style.opacity=p.opacity;for(var n=0;n<Qs.length;n++){var i=Qs[n],r=o.getState(i);r&&(r=r.style||{},i=(i=e.ensureState(i)).style||(i.style={}),null!=r.stroke&&(i[e.__isEmptyBrush?"stroke":"fill"]=r.stroke),null!=r.opacity)&&(i.opacity=r.opacity)}e.markRedraw()}},this),i.getRawValue(t)),e=(jh(this,l,{labelDataIndex:t,labelFetcher:{getFormattedLabel:function(t,e){return i.getFormattedLabel(t,e,n.dataType)}},inheritColor:d||"#000",defaultOpacity:p.opacity,defaultText:(null==f?n.getName(t):isFinite(f)?to(f):f)+""}),this.getTextContent());e&&(r=l.normal,e.__align=e.style.align,e.__verticalAlign=e.style.verticalAlign,e.__position=r.get("position")||"middle",F(a=r.get("distance"))||(a=[a,a]),e.__labelDistance=a),this.setTextConfig({position:null,local:!0,inside:!1}),Rl(this,h,c,u)},Bb.prototype.highlight=function(){bl(this)},Bb.prototype.downplay=function(){wl(this)},Bb.prototype.updateLayout=function(t,e){this.setLinePoints(t.getItemLayout(e))},Bb.prototype.setLinePoints=function(t){var e=this.childOfName("line");Nb(e.shape,t),e.dirty()},Bb.prototype.beforeUpdate=function(){var t=this.childOfName("fromSymbol"),e=this.childOfName("toSymbol"),n=this.getTextContent();if(t||e||n&&!n.ignore){for(var i=1,r=this.parent;r;)r.scaleX&&(i/=r.scaleX),r=r.parent;var o=this.childOfName("line");if(this.__dirty||o.__dirty){var a=o.shape.percent,s=o.pointAt(0),l=o.pointAt(a),u=Xt([],l,s);if(jt(u,u),t&&(t.setPosition(s),v(t,0),t.scaleX=t.scaleY=i*a,t.markRedraw()),e&&(e.setPosition(l),v(e,1),e.scaleX=e.scaleY=i*a,e.markRedraw()),n&&!n.ignore){n.x=n.y=0;var h=void(n.originX=n.originY=0),c=void 0,t=n.__labelDistance,p=t[0]*i,d=t[1]*i,e=a/2,f=o.tangentAt(e),t=[f[1],-f[0]],g=o.pointAt(e),y=(0<t[1]&&(t[0]=-t[0],t[1]=-t[1]),f[0]<0?-1:1),m=("start"!==n.__position&&"end"!==n.__position&&(a=-Math.atan2(f[1],f[0]),l[0]<s[0]&&(a=Math.PI+a),n.rotation=a),void 0);switch(n.__position){case"insideStartTop":case"insideMiddleTop":case"insideEndTop":case"middle":m=-d,c="bottom";break;case"insideStartBottom":case"insideMiddleBottom":case"insideEndBottom":m=d,c="top";break;default:m=0,c="middle"}switch(n.__position){case"end":n.x=u[0]*p+l[0],n.y=u[1]*d+l[1],h=.8<u[0]?"left":u[0]<-.8?"right":"center",c=.8<u[1]?"top":u[1]<-.8?"bottom":"middle";break;case"start":n.x=-u[0]*p+s[0],n.y=-u[1]*d+s[1],h=.8<u[0]?"right":u[0]<-.8?"left":"center",c=.8<u[1]?"bottom":u[1]<-.8?"top":"middle";break;case"insideStartTop":case"insideStart":case"insideStartBottom":n.x=p*y+s[0],n.y=s[1]+m,h=f[0]<0?"right":"left",n.originX=-p*y,n.originY=-m;break;case"insideMiddleTop":case"insideMiddle":case"insideMiddleBottom":case"middle":n.x=g[0],n.y=g[1]+m,h="center",n.originY=-m;break;case"insideEndTop":case"insideEnd":case"insideEndBottom":n.x=-p*y+l[0],n.y=l[1]+m,h=0<=f[0]?"right":"left",n.originX=p*y,n.originY=-m}n.scaleX=n.scaleY=i,n.setStyle({verticalAlign:n.__verticalAlign||c,align:n.__align||h})}}}function v(t,e){var n,i=t.__specifiedRotation;null==i?(n=o.tangentAt(e),t.attr("rotation",(1===e?-1:1)*Math.PI/2-Math.atan2(n[1],n[0]))):t.attr("rotation",i)}};var Eb,zb=Bb;function Bb(t,e,n){var i=Eb.call(this)||this;return i._createLine(t,e,n),i}Vb.prototype.updateData=function(n){var i=this,e=(this._progressiveEls=null,this.group),r=this._lineData,o=(this._lineData=n,r||e.removeAll(),Hb(n));n.diff(r).add(function(t){i._doAdd(n,t,o)}).update(function(t,e){i._doUpdate(r,n,e,t,o)}).remove(function(t){e.remove(r.getItemGraphicEl(t))}).execute()},Vb.prototype.updateLayout=function(){var n=this._lineData;n&&n.eachItemGraphicEl(function(t,e){t.updateLayout(n,e)},this)},Vb.prototype.incrementalPrepareUpdate=function(t){this._seriesScope=Hb(t),this._lineData=null,this.group.removeAll()},Vb.prototype.incrementalUpdate=function(t,e){function n(t){var e;t.isGroup||(e=t).animators&&0<e.animators.length||(t.incremental=!0,t.ensureState("emphasis").hoverLayer=!0)}this._progressiveEls=[];for(var i,r=t.start;r<t.end;r++)Wb(e.getItemLayout(r))&&((i=new this._LineCtor(e,r,this._seriesScope)).traverse(n),this.group.add(i),e.setItemGraphicEl(r,i),this._progressiveEls.push(i))},Vb.prototype.remove=function(){this.group.removeAll()},Vb.prototype.eachRendered=function(t){Xh(this._progressiveEls||this.group,t)},Vb.prototype._doAdd=function(t,e,n){Wb(t.getItemLayout(e))&&(n=new this._LineCtor(t,e,n),t.setItemGraphicEl(e,n),this.group.add(n))},Vb.prototype._doUpdate=function(t,e,n,i,r){t=t.getItemGraphicEl(n);Wb(e.getItemLayout(i))?(t?t.updateData(e,i,r):t=new this._LineCtor(e,i,r),e.setItemGraphicEl(i,t),this.group.add(t)):this.group.remove(t)};var Fb=Vb;function Vb(t){this.group=new Vr,this._LineCtor=t||zb}function Hb(t){var t=t.hostModel,e=t.getModel("emphasis");return{lineStyle:t.getModel("lineStyle").getLineStyle(),emphasisLineStyle:e.getModel(["lineStyle"]).getLineStyle(),blurLineStyle:t.getModel(["blur","lineStyle"]).getLineStyle(),selectLineStyle:t.getModel(["select","lineStyle"]).getLineStyle(),emphasisDisabled:e.get("disabled"),blurScope:e.get("blurScope"),focus:e.get("focus"),labelStatesModels:Kh(t)}}function Gb(t){return isNaN(t[0])||isNaN(t[1])}function Wb(t){return t&&!Gb(t[0])&&!Gb(t[1])}var Ub,Xb=Ao(),sh=(u(Yb,Ub=ug),Yb.prototype.init=function(){this.markerGroupMap=E()},Yb.prototype.render=function(t,n,i){var r=this,e=this.markerGroupMap;e.each(function(t){Xb(t).keep=!1}),n.eachSeries(function(t){var e=cb.getMarkerModelFromSeries(t,r.type);e&&r.renderSeries(t,e,n,i)}),e.each(function(t){Xb(t).keep||r.group.remove(t.group)})},Yb.prototype.markKeep=function(t){Xb(t).keep=!0},Yb.prototype.toggleBlurSeries=function(t,e){var n=this;O(t,function(t){t=cb.getMarkerModelFromSeries(t,n.type);t&&t.getData().eachItemGraphicEl(function(t){t&&(e?Sl:Ml)(t)})})},Yb.type="marker",Yb);function Yb(){var t=null!==Ub&&Ub.apply(this,arguments)||this;return t.type=Yb.type,t}function qb(t,e,n,i){var r,o,a,s,l=t.getData();return(e=[vb(t,(n=F(i)?i:"min"===(r=i.type)||"max"===r||"average"===r||"median"===r||null!=i.xAxis||null!=i.yAxis?(o=s=void 0,o=null!=i.yAxis||null!=i.xAxis?(s=e.getAxis(null!=i.yAxis?"y":"x"),bt(i.yAxis,i.xAxis)):(s=(e=_b(i,l,e,t)).valueAxis,bb(l,F0(l,e.valueDataDim),r)),e=1-(l="x"===s.dim?0:1),s={coord:[]},(a=y(i)).type=null,a.coord=[],a.coord[e]=-1/0,s.coord[e]=1/0,0<=(e=n.get("precision"))&&dt(o)&&(o=+o.toFixed(Math.min(e,20))),a.coord[l]=s.coord[l]=o,[a,s,{type:r,valueIndex:i.valueIndex,value:o}]):[])[0]),vb(t,n[1]),P({},n[2])])[2].type=e[2].type||null,d(e[2],e[0]),d(e[2],e[1]),e}var Zb=Ao();function jb(t){return!isNaN(t)&&!isFinite(t)}function Kb(t,e,n,i){var r=1-t,o=i.dimensions[t];return jb(e[r])&&jb(n[r])&&e[t]===n[t]&&i.getAxis(o).containData(e[t])}function $b(t,e){if("cartesian2d"===t.type){var n=e[0].coord,i=e[1].coord;if(n&&i&&(Kb(1,n,i,t)||Kb(0,n,i,t)))return!0}return xb(t,e[0])&&xb(t,e[1])}function Qb(t,e,n,i,r){var o,a,s=i.coordinateSystem,l=t.getItemModel(e),u=Jr(l.get("x"),r.getWidth()),l=Jr(l.get("y"),r.getHeight());isNaN(u)||isNaN(l)?(o=i.getMarkerPosition?i.getMarkerPosition(t.getValues(t.dimensions,e)):(a=s.dimensions,r=t.get(a[0],e),i=t.get(a[1],e),s.dataToPoint([r,i])),W1(s,"cartesian2d")&&(r=s.getAxis("x"),i=s.getAxis("y"),a=s.dimensions,jb(t.get(a[0],e))?o[0]=r.toGlobalCoord(r.getExtent()[n?0:1]):jb(t.get(a[1],e))&&(o[1]=i.toGlobalCoord(i.getExtent()[n?0:1]))),isNaN(u)||(o[0]=u),isNaN(l)||(o[1]=l)):o=[u,l],t.setItemLayout(e,o)}u(ew,Jb=sh),ew.prototype.updateTransform=function(t,e,o){e.eachSeries(function(e){var n,i,r,t=cb.getMarkerModelFromSeries(e,"markLine");t&&(n=t.getData(),i=Zb(t).from,r=Zb(t).to,i.each(function(t){Qb(i,t,!0,e,o),Qb(r,t,!1,e,o)}),n.each(function(t){n.setItemLayout(t,[i.getItemLayout(t),r.getItemLayout(t)])}),this.markerGroupMap.get(e.id).updateLayout())},this)},ew.prototype.renderSeries=function(o,e,t,a){var n=o.coordinateSystem,i=o.id,s=o.getData(),r=this.markerGroupMap,r=r.get(i)||r.set(i,new Fb),i=(this.group.add(r.group),function(t,e,n){var i;i=t?B(t&&t.dimensions,function(t){return P(P({},e.getData().getDimensionInfo(e.getData().mapDimension(t))||{}),{name:t,ordinalMeta:null})}):[{name:"value",type:"float"}];var r=new P0(i,n),o=new P0(i,n),a=new P0([],n),n=B(n.get("data"),ct(qb,e,t,n));t&&(n=ut(n,ct($b,t)));t=function(t,r){return t?function(t,e,n,i){return yf(i<2?t.coord&&t.coord[i]:t.value,r[i])}:function(t,e,n,i){return yf(t.value,r[i])}}(!!t,i);return r.initData(B(n,function(t){return t[0]}),null,t),o.initData(B(n,function(t){return t[1]}),null,t),a.initData(B(n,function(t){return t[2]})),a.hasItemOption=!0,{from:r,to:o,line:a}}(n,o,e)),l=i.from,u=i.to,h=i.line,c=(Zb(e).from=l,Zb(e).to=u,e.setData(h),e.get("symbol")),p=e.get("symbolSize"),d=e.get("symbolRotate"),f=e.get("symbolOffset");function g(t,e,n){var i=t.getItemModel(e),r=(Qb(t,e,n,o,a),i.getModel("itemStyle").getItemStyle());null==r.fill&&(r.fill=Kg(s,"color")),t.setItemVisual(e,{symbolKeepAspect:i.get("symbolKeepAspect"),symbolOffset:N(i.get("symbolOffset",!0),f[n?0:1]),symbolRotate:N(i.get("symbolRotate",!0),d[n?0:1]),symbolSize:N(i.get("symbolSize"),p[n?0:1]),symbol:N(i.get("symbol",!0),c[n?0:1]),style:r})}F(c)||(c=[c,c]),F(p)||(p=[p,p]),F(d)||(d=[d,d]),F(f)||(f=[f,f]),i.from.each(function(t){g(l,t,!0),g(u,t,!1)}),h.each(function(t){var e=h.getItemModel(t).getModel("lineStyle").getLineStyle();h.setItemLayout(t,[l.getItemLayout(t),u.getItemLayout(t)]),null==e.stroke&&(e.stroke=l.getItemVisual(t,"style").fill),h.setItemVisual(t,{fromSymbolKeepAspect:l.getItemVisual(t,"symbolKeepAspect"),fromSymbolOffset:l.getItemVisual(t,"symbolOffset"),fromSymbolRotate:l.getItemVisual(t,"symbolRotate"),fromSymbolSize:l.getItemVisual(t,"symbolSize"),fromSymbol:l.getItemVisual(t,"symbol"),toSymbolKeepAspect:u.getItemVisual(t,"symbolKeepAspect"),toSymbolOffset:u.getItemVisual(t,"symbolOffset"),toSymbolRotate:u.getItemVisual(t,"symbolRotate"),toSymbolSize:u.getItemVisual(t,"symbolSize"),toSymbol:u.getItemVisual(t,"symbol"),style:e})}),r.updateData(h),i.line.eachItemGraphicEl(function(t){Us(t).dataModel=e,t.traverse(function(t){Us(t).dataModel=e})}),this.markKeep(r),r.group.silent=e.get("silent")||o.get("silent")},ew.type="markLine";var Jb,tw=ew;function ew(){var t=null!==Jb&&Jb.apply(this,arguments)||this;return t.type=ew.type,t}qv(function(t){t.registerComponentModel(fb),t.registerComponentView(tw),t.registerPreprocessor(function(t){!function(t,e){if(t)for(var n=F(t)?t:[t],i=0;i<n.length;i++)if(n[i]&&n[i][e])return 1}(t.series,"markLine")||(t.markLine=t.markLine||{})})});u(iw,nw=g),iw.prototype.init=function(t,e,n){this.mergeDefaultAndTheme(t,n),this._initData()},iw.prototype.mergeOption=function(t){nw.prototype.mergeOption.apply(this,arguments),this._initData()},iw.prototype.setCurrentIndex=function(t){null==t&&(t=this.option.currentIndex);var e=this._data.count();this.option.loop?t=(t%e+e)%e:(t=e<=t?e-1:t)<0&&(t=0),this.option.currentIndex=t},iw.prototype.getCurrentIndex=function(){return this.option.currentIndex},iw.prototype.isIndexMax=function(){return this.getCurrentIndex()>=this._data.count()-1},iw.prototype.setPlayState=function(t){this.option.autoPlay=!!t},iw.prototype.getPlayState=function(){return!!this.option.autoPlay},iw.prototype._initData=function(){var r,t=this.option,e=t.data||[],t=t.axisType,o=this._names=[],e=("category"===t?(r=[],O(e,function(t,e){var n,i=To(bo(t),"");R(t)?(n=y(t)).value=e:n=e,r.push(n),o.push(i)})):r=e,{category:"ordinal",time:"time",value:"number"}[t]||"number");(this._data=new P0([{name:"value",type:e}],this)).initData(r,o)},iw.prototype.getData=function(){return this._data},iw.prototype.getCategories=function(){if("category"===this.get("axisType"))return this._names.slice()},iw.type="timeline",iw.defaultOption={z:4,show:!0,axisType:"time",realtime:!0,left:"20%",top:null,right:"20%",bottom:0,width:null,height:40,padding:5,controlPosition:"left",autoPlay:!1,rewind:!1,loop:!0,playInterval:2e3,currentIndex:0,itemStyle:{},label:{color:"#000"},data:[]};var nw,du=iw;function iw(){var t=null!==nw&&nw.apply(this,arguments)||this;return t.type=iw.type,t.layoutMode="box",t}u(aw,rw=du),aw.type="timeline.slider",aw.defaultOption=(n={backgroundColor:"rgba(0,0,0,0)",borderColor:"#ccc",borderWidth:0,orient:"horizontal",inverse:!1,tooltip:{trigger:"item"},symbol:"circle",symbolSize:12,lineStyle:{show:!0,width:2,color:"#DAE1F5"},label:{position:"auto",show:!0,interval:"auto",rotate:0,color:"#A4B1D7"},itemStyle:{color:"#A4B1D7",borderWidth:1},checkpointStyle:{symbol:"circle",symbolSize:15,color:"#316bf3",borderColor:"#fff",borderWidth:2,shadowBlur:2,shadowOffsetX:1,shadowOffsetY:1,shadowColor:"rgba(0, 0, 0, 0.3)",animation:!0,animationDuration:300,animationEasing:"quinticInOut"},controlStyle:{show:!0,showPlayBtn:!0,showPrevBtn:!0,showNextBtn:!0,itemSize:24,itemGap:12,position:"left",playIcon:"path://M31.6,53C17.5,53,6,41.5,6,27.4S17.5,1.8,31.6,1.8C45.7,1.8,57.2,13.3,57.2,27.4S45.7,53,31.6,53z M31.6,3.3 C18.4,3.3,7.5,14.1,7.5,27.4c0,13.3,10.8,24.1,24.1,24.1C44.9,51.5,55.7,40.7,55.7,27.4C55.7,14.1,44.9,3.3,31.6,3.3z M24.9,21.3 c0-2.2,1.6-3.1,3.5-2l10.5,6.1c1.899,1.1,1.899,2.9,0,4l-10.5,6.1c-1.9,1.1-3.5,0.2-3.5-2V21.3z",stopIcon:"path://M30.9,53.2C16.8,53.2,5.3,41.7,5.3,27.6S16.8,2,30.9,2C45,2,56.4,13.5,56.4,27.6S45,53.2,30.9,53.2z M30.9,3.5C17.6,3.5,6.8,14.4,6.8,27.6c0,13.3,10.8,24.1,24.101,24.1C44.2,51.7,55,40.9,55,27.6C54.9,14.4,44.1,3.5,30.9,3.5z M36.9,35.8c0,0.601-0.4,1-0.9,1h-1.3c-0.5,0-0.9-0.399-0.9-1V19.5c0-0.6,0.4-1,0.9-1H36c0.5,0,0.9,0.4,0.9,1V35.8z M27.8,35.8 c0,0.601-0.4,1-0.9,1h-1.3c-0.5,0-0.9-0.399-0.9-1V19.5c0-0.6,0.4-1,0.9-1H27c0.5,0,0.9,0.4,0.9,1L27.8,35.8L27.8,35.8z",nextIcon:"M2,18.5A1.52,1.52,0,0,1,.92,18a1.49,1.49,0,0,1,0-2.12L7.81,9.36,1,3.11A1.5,1.5,0,1,1,3,.89l8,7.34a1.48,1.48,0,0,1,.49,1.09,1.51,1.51,0,0,1-.46,1.1L3,18.08A1.5,1.5,0,0,1,2,18.5Z",prevIcon:"M10,.5A1.52,1.52,0,0,1,11.08,1a1.49,1.49,0,0,1,0,2.12L4.19,9.64,11,15.89a1.5,1.5,0,1,1-2,2.22L1,10.77A1.48,1.48,0,0,1,.5,9.68,1.51,1.51,0,0,1,1,8.58L9,.92A1.5,1.5,0,0,1,10,.5Z",prevBtnSize:18,nextBtnSize:18,color:"#A4B1D7",borderColor:"#A4B1D7",borderWidth:1},emphasis:{label:{show:!0,color:"#6f778d"},itemStyle:{color:"#316BF3"},controlStyle:{color:"#316BF3",borderColor:"#316BF3",borderWidth:2}},progress:{lineStyle:{color:"#316BF3"},itemStyle:{color:"#316BF3"},label:{color:"#6f778d"}},data:[]},d(d({},du.defaultOption,!0),n,!0));var rw,ow=aw;function aw(){var t=null!==rw&&rw.apply(this,arguments)||this;return t.type=aw.type,t}at(ow,ef.prototype);u(lw,sw=ug),lw.type="timeline";var sw,Fu=lw;function lw(){var t=null!==sw&&sw.apply(this,arguments)||this;return t.type=lw.type,t}u(cw,uw=Nc),cw.prototype.getLabelModel=function(){return this.model.getModel("label")},cw.prototype.isHorizontal=function(){return"horizontal"===this.model.get("orient")};var uw,hw=cw;function cw(t,e,n,i){t=uw.call(this,t,e,n)||this;return t.type=i||"value",t}var pw,dw=Math.PI,fw=Ao(),gw=(u(yw,pw=Fu),yw.prototype.init=function(t,e){this.api=e},yw.prototype.render=function(e,t,n){var i,r,o;this.model=e,this.api=n,this.ecModel=t,this.group.removeAll(),e.get("show",!0)&&(i=this._layout(e,n),r=this._createGroup("_mainGroup"),t=this._createGroup("_labelGroup"),o=this._axis=this._createAxis(i,e),e.formatTooltip=function(t){return Kf("nameValue",{noName:!0,value:o.scale.getLabel({value:t})})},O(["AxisLine","AxisTick","Control","CurrentPointer"],function(t){this["_render"+t](i,r,o,e)},this),this._renderAxisLabel(i,t,o,e),this._position(i,e)),this._doPlayStop(),this._updateTicksStatus()},yw.prototype.remove=function(){this._clearTimer(),this.group.removeAll()},yw.prototype.dispose=function(){this._clearTimer()},yw.prototype._layout=function(t,e){var n,i,r,o=t.get(["label","position"]),a=t.get("orient"),s=(s=e,dp((l=t).getBoxLayoutParams(),{width:s.getWidth(),height:s.getHeight()},l.get("padding"))),l=null==o||"auto"===o?"horizontal"===a?s.y+s.height/2<e.getHeight()/2?"-":"+":s.x+s.width/2<e.getWidth()/2?"+":"-":V(o)?{horizontal:{top:"-",bottom:"+"},vertical:{left:"-",right:"+"}}[a][o]:o,e={horizontal:"center",vertical:0<=l||"+"===l?"left":"right"},o={horizontal:0<=l||"+"===l?"top":"bottom",vertical:"middle"},u={horizontal:0,vertical:dw/2},h="vertical"===a?s.height:s.width,c=t.getModel("controlStyle"),p=c.get("show",!0),d=p?c.get("itemSize"):0,f=p?c.get("itemGap"):0,g=d+f,y=(t.get(["label","rotate"])||0)*dw/180,m=c.get("position",!0),v=p&&c.get("showPlayBtn",!0),_=p&&c.get("showPrevBtn",!0),p=p&&c.get("showNextBtn",!0),c=0,x=h,m=("left"===m||"bottom"===m?(v&&(n=[0,0],c+=g),_&&(i=[c,0],c+=g)):(v&&(n=[x-d,0],x-=g),_&&(i=[0,0],c+=g)),p&&(r=[x-d,0],x-=g),[c,x]);return t.get("inverse")&&m.reverse(),{viewRect:s,mainLength:h,orient:a,rotation:u[a],labelRotation:y,labelPosOpt:l,labelAlign:t.get(["label","align"])||e[a],labelBaseline:t.get(["label","verticalAlign"])||t.get(["label","baseline"])||o[a],playPosition:n,prevBtnPosition:i,nextBtnPosition:r,axisExtent:m,controlSize:d,controlGap:f}},yw.prototype._position=function(t,e){var n,i=this._mainGroup,r=this._labelGroup,o=t.viewRect,a=("vertical"===t.orient&&(Ne(u=Le(),u,[-(s=o.x),-(l=o.y+o.height)]),Ee(u,u,-dw/2),Ne(u,u,[s,l]),(o=o.clone()).applyTransform(u)),p(o)),s=p(i.getBoundingRect()),l=p(r.getBoundingRect()),u=[i.x,i.y],o=[r.x,r.y],h=(o[0]=u[0]=a[0][0],t.labelPosOpt);function c(t){t.originX=a[0][0]-t.x,t.originY=a[1][0]-t.y}function p(t){return[[t.x,t.x+t.width],[t.y,t.y+t.height]]}function d(t,e,n,i,r){t[i]+=n[i][r]-e[i][r]}null==h||V(h)?(d(u,s,a,1,n="+"===h?0:1),d(o,l,a,1,1-n)):(d(u,s,a,1,n=0<=h?0:1),o[1]=u[1]+h),i.setPosition(u),r.setPosition(o),i.rotation=r.rotation=t.rotation,c(i),c(r)},yw.prototype._createAxis=function(t,e){var n=e.getData(),i=e.get("axisType"),r=function(t,e){if(e=e||t.get("type"))switch(e){case"category":return new nv({ordinalMeta:t.getCategories(),extent:[1/0,-1/0]});case"time":return new yv({locale:t.ecModel.getLocaleModel(),useUTC:t.ecModel.get("useUTC")});default:return new av}}(e,i),o=(r.getTicks=function(){return n.mapArray(["value"],function(t){return{value:t}})},n.getDataExtent("value")),o=(r.setExtent(o[0],o[1]),r.calcNiceTicks(),new hw("value",r,t.axisExtent,i));return o.model=e,o},yw.prototype._createGroup=function(t){t=this[t]=new Vr;return this.group.add(t),t},yw.prototype._renderAxisLine=function(t,e,n,i){var r,n=n.getExtent();i.get(["lineStyle","show"])&&(r=new Zu({shape:{x1:n[0],y1:0,x2:n[1],y2:0},style:P({lineCap:"round"},i.getModel("lineStyle").getLineStyle()),silent:!0,z2:1}),e.add(r),n=this._progressLine=new Zu({shape:{x1:n[0],x2:this._currentPointer?this._currentPointer.x:n[0],y1:0,y2:0},style:z({lineCap:"round",lineWidth:r.style.lineWidth},i.getModel(["progress","lineStyle"]).getLineStyle()),silent:!0,z2:1}),e.add(n))},yw.prototype._renderAxisTick=function(t,a,s,l){var u=this,h=l.getData(),e=s.scale.getTicks();this._tickSymbols=[],O(e,function(t){var e=s.dataToCoord(t.value),n=h.getItemModel(t.value),i=n.getModel("itemStyle"),r=n.getModel(["emphasis","itemStyle"]),o=n.getModel(["progress","itemStyle"]),e={x:e,y:0,onclick:ht(u._changeTimeline,u,t.value)},i=mw(n,i,a,e),e=(i.ensureState("emphasis").style=r.getItemStyle(),i.ensureState("progress").style=o.getItemStyle(),Ol(i),Us(i));n.get("tooltip")?(e.dataIndex=t.value,e.dataModel=l):e.dataIndex=e.dataModel=null,u._tickSymbols.push(i)})},yw.prototype._renderAxisLabel=function(a,s,l,t){var u,h=this;l.getLabelModel().get("show")&&(u=t.getData(),t=l.getViewLabels(),this._tickLabels=[],O(t,function(t){var e=t.tickValue,n=u.getItemModel(e),i=n.getModel("label"),r=n.getModel(["emphasis","label"]),n=n.getModel(["progress","label"]),o=l.dataToCoord(t.tickValue),o=new Ps({x:o,y:0,rotation:a.labelRotation-a.rotation,onclick:ht(h._changeTimeline,h,e),silent:!1,style:$h(i,{text:t.formattedLabel,align:a.labelAlign,verticalAlign:a.labelBaseline})});o.ensureState("emphasis").style=$h(r),o.ensureState("progress").style=$h(n),s.add(o),Ol(o),fw(o).dataIndex=e,h._tickLabels.push(o)}))},yw.prototype._renderControl=function(t,o,e,a){var s=t.controlSize,l=t.rotation,u=a.getModel("controlStyle").getItemStyle(),h=a.getModel(["emphasis","controlStyle"]).getItemStyle(),n=a.getPlayState(),i=a.get("inverse",!0);function r(t,e,n,i){var r;t&&(r=Cr(N(a.get(["controlStyle",e+"BtnSize"]),s),s),(e=function(t,e,n,i){var r=i.style,t=Wh(t.get(["controlStyle",e]),i||{},new X(n[0],n[1],n[2],n[3]));r&&t.setStyle(r);return t}(a,e+"Icon",[0,-r/2,r,r],{x:t[0],y:t[1],originX:s/2,originY:0,rotation:i?-l:0,rectHover:!0,style:u,onclick:n})).ensureState("emphasis").style=h,o.add(e),Ol(e))}r(t.nextBtnPosition,"next",ht(this._changeTimeline,this,i?"-":"+")),r(t.prevBtnPosition,"prev",ht(this._changeTimeline,this,i?"+":"-")),r(t.playPosition,n?"stop":"play",ht(this._handlePlayClick,this,!n),!0)},yw.prototype._renderCurrentPointer=function(t,e,n,i){var r=i.getData(),o=i.getCurrentIndex(),r=r.getItemModel(o).getModel("checkpointStyle"),a=this;this._currentPointer=mw(r,r,this._mainGroup,{},this._currentPointer,{onCreate:function(t){t.draggable=!0,t.drift=ht(a._handlePointerDrag,a),t.ondragend=ht(a._handlePointerDragend,a),vw(t,a._progressLine,o,n,i,!0)},onUpdate:function(t){vw(t,a._progressLine,o,n,i)}})},yw.prototype._handlePlayClick=function(t){this._clearTimer(),this.api.dispatchAction({type:"timelinePlayChange",playState:t,from:this.uid})},yw.prototype._handlePointerDrag=function(t,e,n){this._clearTimer(),this._pointerChangeTimeline([n.offsetX,n.offsetY])},yw.prototype._handlePointerDragend=function(t){this._pointerChangeTimeline([t.offsetX,t.offsetY],!0)},yw.prototype._pointerChangeTimeline=function(t,e){var t=this._toAxisCoord(t)[0],n=eo(this._axis.getExtent().slice()),n=((t=t>n[1]?n[1]:t)<n[0]&&(t=n[0]),this._currentPointer.x=t,this._currentPointer.markRedraw(),this._progressLine),n=(n&&(n.shape.x2=t,n.dirty()),this._findNearestTick(t)),t=this.model;(e||n!==t.getCurrentIndex()&&t.get("realtime"))&&this._changeTimeline(n)},yw.prototype._doPlayStop=function(){var e=this;this._clearTimer(),this.model.getPlayState()&&(this._timer=setTimeout(function(){var t=e.model;e._changeTimeline(t.getCurrentIndex()+(t.get("rewind",!0)?-1:1))},this.model.get("playInterval")))},yw.prototype._toAxisCoord=function(t){var e,n=this._mainGroup.getLocalTransform();return t=t,e=!0,(n=n)&&!st(n)&&(n=yr.getLocalTransform(n)),ee([],t,n=e?Be([],n):n)},yw.prototype._findNearestTick=function(n){var i,t=this.model.getData(),r=1/0,o=this._axis;return t.each(["value"],function(t,e){t=o.dataToCoord(t),t=Math.abs(t-n);t<r&&(r=t,i=e)}),i},yw.prototype._clearTimer=function(){this._timer&&(clearTimeout(this._timer),this._timer=null)},yw.prototype._changeTimeline=function(t){var e=this.model.getCurrentIndex();"+"===t?t=e+1:"-"===t&&(t=e-1),this.api.dispatchAction({type:"timelineChange",currentIndex:t,from:this.uid})},yw.prototype._updateTicksStatus=function(){var t=this.model.getCurrentIndex(),e=this._tickSymbols,n=this._tickLabels;if(e)for(var i=0;i<e.length;i++)e&&e[i]&&e[i].toggleState("progress",i<t);if(n)for(i=0;i<n.length;i++)n&&n[i]&&n[i].toggleState("progress",fw(n[i]).dataIndex<=t)},yw.type="timeline.slider",yw);function yw(){var t=null!==pw&&pw.apply(this,arguments)||this;return t.type=yw.type,t}function mw(t,e,n,i,r,o){var a=e.get("color"),a=(r?(r.setColor(a),n.add(r),o&&o.onUpdate(r)):((r=cy(t.get("symbol"),-1,-1,2,2,a)).setStyle("strokeNoScale",!0),n.add(r),o&&o.onCreate(r)),e.getItemStyle(["color"])),n=(r.setStyle(a),i=d({rectHover:!0,z2:100},i,!0),py(t.get("symbolSize"))),o=(i.scaleX=n[0]/2,i.scaleY=n[1]/2,dy(t.get("symbolOffset"),n)),e=(o&&(i.x=(i.x||0)+o[0],i.y=(i.y||0)+o[1]),t.get("symbolRotate"));return i.rotation=(e||0)*Math.PI/180||0,r.attr(i),r.updateTransform(),r}function vw(t,e,n,i,r,o){var a;t.dragging||(a=r.getModel("checkpointStyle"),i=i.dataToCoord(r.getData().get("value",n)),o||!a.get("animation",!0)?(t.attr({x:i,y:0}),e&&e.attr({shape:{x2:i}})):(r={duration:a.get("animationDuration",!0),easing:a.get("animationEasing",!0)},t.stopAnimation(null,!0),t.animateTo({x:i,y:0},r),e&&e.animateTo({shape:{x2:i}},r)))}function _w(t){t=t&&t.timeline;O(t=F(t)?t:t?[t]:[],function(t){var e,n;t&&(e=(t=t).type,(n={number:"value",time:"time"})[e]&&(t.axisType=n[e],delete t.type),xw(t),bw(t,"controlPosition")&&(bw(n=t.controlStyle||(t.controlStyle={}),"position")||(n.position=t.controlPosition),"none"!==n.position||bw(n,"show")||(n.show=!1,delete n.position),delete t.controlPosition),O(t.data||[],function(t){R(t)&&!F(t)&&(!bw(t,"value")&&bw(t,"name")&&(t.value=t.name),xw(t))}))})}function xw(t){var e=t.itemStyle||(t.itemStyle={}),e=e.emphasis||(e.emphasis={}),t=t.label||t.label||{},n=t.normal||(t.normal={}),i={normal:1,emphasis:1};O(t,function(t,e){i[e]||bw(n,e)||(n[e]=t)}),e.label&&!bw(t,"emphasis")&&(t.emphasis=e.label,delete e.label)}function bw(t,e){return t.hasOwnProperty(e)}qv(function(t){var e;t.registerComponentModel(ow),t.registerComponentView(gw),t.registerSubTypeDefaulter("timeline",function(){return"slider"}),(e=t).registerAction({type:"timelineChange",event:"timelineChanged",update:"prepareAndUpdate"},function(t,e,n){var i=e.getComponent("timeline");return i&&null!=t.currentIndex&&(i.setCurrentIndex(t.currentIndex),!i.get("loop",!0))&&i.isIndexMax()&&i.getPlayState()&&(i.setPlayState(!1),n.dispatchAction({type:"timelinePlayChange",playState:!1,from:t.from})),e.resetOption("timeline",{replaceMerge:i.get("replaceMerge",!0)}),z({currentIndex:i.option.currentIndex},t)}),e.registerAction({type:"timelinePlayChange",event:"timelinePlayChanged",update:"update"},function(t,e){e=e.getComponent("timeline");e&&null!=t.playState&&e.setPlayState(t.playState)}),t.registerPreprocessor(_w)}),qv(d1);var ww={value:"eq","<":"lt","<=":"lte",">":"gt",">=":"gte","=":"eq","!=":"ne","<>":"ne"},Sw=(Mw.prototype.evaluate=function(t){var e=typeof t;return V(e)?this._condVal.test(t):!!dt(e)&&this._condVal.test(t+"")},Mw);function Mw(t){null==(this._condVal=V(t)?new RegExp(t):_t(t)?t:null)&&f("")}Cw.prototype.evaluate=function(){return this.value};var Tw=Cw;function Cw(){}Iw.prototype.evaluate=function(){for(var t=this.children,e=0;e<t.length;e++)if(!t[e].evaluate())return!1;return!0};var kw=Iw;function Iw(){}Aw.prototype.evaluate=function(){for(var t=this.children,e=0;e<t.length;e++)if(t[e].evaluate())return!0;return!1};var Dw=Aw;function Aw(){}Pw.prototype.evaluate=function(){return!this.child.evaluate()};var Lw=Pw;function Pw(){}Rw.prototype.evaluate=function(){for(var t=!!this.valueParser,e=(0,this.getValue)(this.valueGetterParam),n=t?this.valueParser(e):null,i=0;i<this.subCondList.length;i++)if(!this.subCondList[i].evaluate(t?n:e))return!1;return!0};var Ow=Rw;function Rw(){}function Nw(t,e){if(!0===t||!1===t)return(n=new Tw).value=t,n;var n;if(zw(t)||f(""),t.and)return Ew("and",t,e);if(t.or)return Ew("or",t,e);if(t.not)return n=e,zw(o=(o=t).not)||f(""),(l=new Lw).child=Nw(o,n),l.child||f(""),l;for(var i=t,r=e,o=r.prepareGetValue(i),a=[],s=I(i),l=i.parser,u=l?vf(l):null,h=0;h<s.length;h++){var c,p=s[h];"parser"===p||r.valueGetterAttrMap.get(p)||(c=Bt(ww,p)?ww[p]:p,p=i[p],p=u?u(p):p,(c=function(t,e){return"eq"===t||"ne"===t?new Mf("eq"===t,e):Bt(_f,t)?new xf(t,e):null}(c,p)||"reg"===c&&new Sw(p))||f(""),a.push(c))}return a.length||f(""),(l=new Ow).valueGetterParam=o,l.valueParser=u,l.getValue=r.getValue,l.subCondList=a,l}function Ew(t,e,n){e=e[t],F(e)||f(""),e.length||f(""),t=new("and"===t?kw:Dw);return t.children=B(e,function(t){return Nw(t,n)}),t.children.length||f(""),t}function zw(t){return R(t)&&!st(t)}Fw.prototype.evaluate=function(){return this._cond.evaluate()};var Bw=Fw;function Fw(t,e){this._cond=Nw(t,e)}var Vw={type:"echarts:filter",transform:function(t){for(var e,n,i=t.upstream,r=(t=t.config,n={valueGetterAttrMap:E({dimension:!0}),prepareGetValue:function(t){var e=t.dimension,t=(Bt(t,"dimension")||f(""),i.getDimensionInfo(e));return t||f(""),{dimIdx:t.index}},getValue:function(t){return i.retrieveValueFromItem(e,t.dimIdx)}},new Bw(t,n)),o=[],a=0,s=i.count();a<s;a++)e=i.getRawDataItem(a),r.evaluate()&&o.push(e);return{data:o}}},Hw={type:"echarts:sort",transform:function(t){for(var a=t.upstream,t=t.config,t=vo(t),s=(t.length||f(""),[]),t=(O(t,function(t){var e=t.dimension,n=t.order,i=t.parser,t=t.incomparable,e=(null==e&&f(""),"asc"!==n&&"desc"!==n&&f(""),t&&"min"!==t&&"max"!==t&&f(""),"asc"!==n&&"desc"!==n&&f(""),a.getDimensionInfo(e)),r=(e||f(""),i?vf(i):null);i&&!r&&f(""),s.push({dimIdx:e.index,parser:r,comparator:new wf(n,t)})}),a.sourceFormat),e=(t!==kp&&t!==Ip&&f(""),[]),n=0,i=a.count();n<i;n++)e.push(a.getRawDataItem(n));return e.sort(function(t,e){for(var n=0;n<s.length;n++){var i=s[n],r=a.retrieveValueFromItem(t,i.dimIdx),o=a.retrieveValueFromItem(e,i.dimIdx),i=(i.parser&&(r=i.parser(r),o=i.parser(o)),i.comparator.evaluate(r,o));if(0!==i)return i}return 0}),{data:e}}};qv(function(t){t.registerTransform(Vw),t.registerTransform(Hw)}),t.Axis=Nc,t.ChartView=fg,t.ComponentModel=g,t.ComponentView=ug,t.List=P0,t.Model=_c,t.PRIORITY=ry,t.SeriesModel=eg,t.color=vi,t.connect=function(e){var t;return F(e)&&(t=e,e=null,O(t,function(t){null!=t.group&&(e=t.group)}),e=e||"g_"+Fm++,O(t,function(t){t.group=e})),zm[e]=!0,e},t.dataTool={},t.dependencies={zrender:"5.6.0"},t.disConnect=iy,t.disconnect=Hm,t.dispose=function(t){V(t)?t=Em[t]:t instanceof wm||(t=Gm(t)),t instanceof wm&&!t.isDisposed()&&t.dispose()},t.env=p,t.extendChartView=function(t){return t=fg.extend(t),fg.registerClass(t),t},t.extendComponentModel=function(t){return t=g.extend(t),g.registerClass(t),t},t.extendComponentView=function(t){return t=ug.extend(t),ug.registerClass(t),t},t.extendSeriesModel=function(t){return t=eg.extend(t),eg.registerClass(t),t},t.format=fc,t.getCoordinateSystemDimensions=function(t){if(t=nd.get(t))return t.getDimensionsInfo?t.getDimensionsInfo():t.dimensions.slice()},t.getInstanceByDom=Gm,t.getInstanceById=function(t){return Em[t]},t.getMap=function(t){var e=Xy.getMap;return e&&e(t)},t.graphic=yc,t.helper=oy,t.init=function(t,e,n){var i=!(n&&n.ssr);if(i){var r=Gm(t);if(r)return r}return(r=new wm(t,e,n)).id="ec_"+Bm++,Em[r.id]=r,i&&Eo(t,Vm,r.id),vm(r),Uy.trigger("afterinit",r),r},t.innerDrawElementOnCanvas=zy,t.matrix=Fe,t.number=pc,t.parseGeoJSON=h_,t.parseGeoJson=h_,t.registerAction=jm,t.registerCoordinateSystem=Km,t.registerLayout=$m,t.registerLoading=e0,t.registerLocale=Dc,t.registerMap=n0,t.registerPostInit=Ym,t.registerPostUpdate=qm,t.registerPreprocessor=Um,t.registerProcessor=Xm,t.registerTheme=Wm,t.registerTransform=i0,t.registerUpdateLifecycle=Zm,t.registerVisual=Qm,t.setCanvasCreator=function(t){C({createCanvas:t})},t.setPlatformAPI=C,t.throttle=bg,t.time=Ko,t.use=qv,t.util=hc,t.vector=re,t.version="5.5.1",t.zrUtil=Ht,t.zrender=Kr});