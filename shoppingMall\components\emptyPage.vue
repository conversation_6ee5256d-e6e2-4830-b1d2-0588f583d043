<template>
  <view class="pt-140">
    <!-- #ifdef MP-WEIXIN -->
    <u-empty mode="car" textSize="24" width="200" height="200" :icon="imgHost + 'alading/correcting/no_data.png'" :text="emptyText || '暂无记录'"></u-empty>
    <!-- #endif -->
    <!-- #ifdef APP-PLUS -->
    <u-empty mode="car" textSize="18" width="100" height="100" :icon="imgHost + 'alading/correcting/no_data.png'" :text="emptyText || '暂无记录'"></u-empty>
    <!-- #endif -->
  </view>
</template>

<script>
  export default {
    props: ['emptyText'],
    data() {
      return {
        imgHost: getApp().globalData.imgsomeHost
      };
    },
    methods: {}
  };
</script>

<style></style>
