<template>
  <view class="contain">
    <view class="title">
      <view style="flex: 3; text-align: center">复习关卡</view>
      <view style="flex: 3; text-align: center">日期</view>
      <view style="flex: 3; text-align: center">查看详情</view>
    </view>

    <view class="items" :style="{ height: useHeight + 'rpx' }">
      <view v-if="list.length">
        <view class="item" v-for="item in list" :key="item.id">
          <view style="flex: 3; text-align: center">{{ item.checkpointName }}</view>
          <view style="flex: 3; text-align: center">{{ item.actualReviewTime.slice(0, 11) }}</view>
          <view style="flex: 3; text-align: center">
            <image @click="goReport(item)" src="https://document.dxznjy.com/applet/newimages/xiangqing.png" style="width: 32rpx; height: 32rpx"></image>
          </view>
        </view>
      </view>
      <view v-else class="emtp">
        <image src="https://document.dxznjy.com/course/445806b8053f4246b0897dca73f40629.png" style="width: 122rpx; height: 122rpx" mode=""></image>
        <view class="emptyText">暂无数据</view>
      </view>
    </view>
  </view>
</template>

<script>
  export default {
    data() {
      return {
        useHeight: 0,
        studentCode: '',
        merchantCode: '',
        list: []
      };
    },
    onLoad(options) {
      uni.getStorageSync('token', options.token);
      this.studentCode = options.studentCode;
      this.merchantCode = options.merchantCode;
    },
    onReady() {
      uni.getSystemInfo({
        success: (res) => {
          // 可使用窗口高度，将px转换rpx
          this.useHeight = res.windowHeight * (750 / res.windowWidth) - 200;
        }
      });
    },
    mounted() {
      this.init();
    },
    methods: {
      async init() {
        let { data } = await this.$httpUser.get('znyy/superReadReview/getReviewRoundList?status=1&studentCode=' + this.studentCode + '&merchantCode=' + this.merchantCode);
        if (data.success) {
          this.list = data.data;
        }
      },
      goReport(e) {
        uni.navigateTo({
          url: `/ReadForget/readReport?studentCode=${this.studentCode}&merchantCode=${this.merchantCode}&forgettingId=${e.id}&type=2`
        });
      }
    }
  };
</script>

<style lang="scss" scoped>
  .contain {
    margin: 20rpx;
    background-color: #fff;
    border-radius: 15rpx;
    overflow: hidden;
    .title {
      display: flex;
      align-items: center;
      height: 108rpx;
      font-size: 28rpx;
      font-weight: bold;
    }
    .items {
      overflow-y: auto;
    }
    .emtp {
      margin-top: 22rpx;
      border-radius: 20rpx 20rpx 0rpx 0rpx;
      background-color: #fff;
      flex-direction: column;
      display: flex;
      height: 512rpx;
      justify-content: center;
      align-items: center;
      .emptyText {
        margin-top: 40rpx;
        font-size: 34rpx;
        color: #bababa;
        text-align: center;
      }
    }
    .item {
      display: flex;
      align-items: center;
      font-size: 26rpx;
      height: 90rpx;
      border-top: 2rpx solid #e2e3e7;
    }
  }
</style>
