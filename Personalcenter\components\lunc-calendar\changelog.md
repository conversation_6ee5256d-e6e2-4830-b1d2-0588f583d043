## 1.0.8（2022-12-30）
修改 readme.md 说明文件
## 1.0.7（2022-12-30）
1. 修改 是否显示按钮属性 shouChangeBtn ，改为 showChangeBtn；
2. 解决在收起状态下，点击“今”不会触发 dayChange 和 monthChange 事件；
3. 解决 不显示农历的同时也不显示标记问题，修改后可只显示标记；
## 1.0.6（2022-12-09）
添加属性 shrinkState ，默认显示周数据(收起)还是月数据(展开)
## 1.0.5（2022-11-25）
1. 解决 iOS系统下日期格式只识别"/"问题；
2. 添加 删除标记和添加标记两个方法；删除之前的设置标记方法setSignList()；
3. 添加收起和展开状态改变事件；
## 1.0.4（2022-11-24）
1. 添加可收缩按钮，收缩后显示一个星期的日期，展开显示一个月的日期；
2. 添加setSignList方法，可动态添加修改删除 标记事件；
## 1.0.3（2022-10-25）
添加signList的监听，可动态修改signList值
## 1.0.0（2021-09-23）
初次提交
