import { httpUser } from './luch-request/indexUser.js'; // 全局挂载引入

export default {
  getNotReadNews: function () {
    return new Promise((resolve, reject) => {
      let mobile = uni.getStorageSync('phone');
      httpUser
        .get('zx/message/notice/status/num?status=0&mobile=' + mobile)
        .then((res) => {
          console.log(res, '未读消息数量');
          uni.setStorageSync('notReadNews', Number(res.data.data));
          if (Number(res.data.data) > 0) {
            console.log('有未读消息');
            uni.setTabBarItem({
              index: 3,
              text: '我的',
              iconPath: '/static/tab/tab_mine_read.png',
              selectedIconPath: '/static/tab/tab_mine_active_read.png'
            });
          } else {
            uni.setTabBarItem({
              index: 3,
              text: '我的',
              iconPath: '/static/tab/tab_mine.png',
              selectedIconPath: '/static/tab/tab_mine_active.png'
            });
          }
          resolve();
        })
        .catch((err) => {
          console.error(err);
          reject(err);
        });
    });
  }
};
