<template>
	<view class="problem_detail_style">
		<view class="bg-ff">
			<scroll-view class="scroll_view_content" v-if="answerInfo.length > 0" @scrolltolower="scrolltolower"  @scroll="scroll"  :scroll-top="scrollTop" 
				:show-scrollbar="false"  bounces  :throttle="false" scroll-with-animation  scroll-anchoring  scroll-y enhanced>
			<view class="flex-x-s flex-y-s problem_top_content">
				<view class="positionRelative" style="width: 82rpx;height: 82rpx;" >
					<image class="radius-all"  style="width: 82rpx;height: 82rpx;" :src="problemInfo.headPhoto?problemInfo.headPhoto:avaUrl"></image>
					<image class="positionAbsolute wen_icon_css" src="https://document.dxznjy.com/course/b5cf5b0b3f2b493cb1998fe2eca8f823.png"></image>
				</view>
				<view class="ml-15">
					<view class="c-33 f-28 lh-40">{{problemInfo.userName}}</view>
					<view  class="c-55 f-28 lh-42 content_css">{{problemInfo.content}}</view>
				</view>
			</view>
			<view   class="da_content_css plr-32">
				<view v-for="(info,index) in answerInfo" @click="getFouseItem(info)"  :key="index">
					<view class="flex-x-s flex-self-s da_item_css bg-ff p-24">
						<view class="positionRelative" style="width: 82rpx;height: 82rpx;" >
							<image class="radius-all"  style="width: 82rpx;height: 82rpx;" :src="info.headPhoto?info.headPhoto:avaUrl"></image>
							<image class="positionAbsolute da_icon_css" src="https://document.dxznjy.com/course/3fbe7257487d4fdc9be292d520e0e0c3.png"></image>
						</view>
						<view class="ml-15">
							<view class="c-33 f-28 lh-40 flex-a-c flex-x-b">
								<view>
									{{info.userName}}
								</view>
							</view>
							<view class="c-55 f-28 lh-42 content_css">{{info.content}}</view>
						</view>
					</view>
				</view>
			</view>
			<!-- <view class="input_center_css bg-ff">
				<input type="text" :focus="shoFoucs" placeholder="我要回答这个问题......">
			</view> -->
			</scroll-view>
			<view class="no_empty_page" v-if="answerInfo.length==0">
				<emptyPage emptyText="还没有人回答哦~快来回答吧"></emptyPage>
			</view>
			<view class="bg-ff input_data_css"  >
					<input type="text" v-model="content"  placeholder="我要回答这个问题......">
					<view @click="problemRelease" class="fabu_btn f-28 c-ff">发布</view>
				</view>
		</view>
		<growthPopup ref="growthPopupRefs"></growthPopup>
	</view>
</template>

<script>
const {
		$showMsg,
		$http
	} = require("@/util/methods.js")
	import emptyPage from "../components/emptyPage.vue"
	import growthPopup from "../components/growthPopup.vue"
	export default {
		components:{growthPopup,emptyPage},
		data() {
			return {
				shoFoucs:false,
				keyUpHeight:-200,
				answerInfo:[],
				content:'',
				identityType:uni.getStorageSync('identityType'),
				avaUrl: 'https://document.dxznjy.com/dxSelect/home_avaUrl.png',
				screenWindow:0,
				problemInfo:{},
				infoLists:{},
				scrollTopNum:0,
				scrollTop:0,
				page:1,
			}
		},
		onLoad(e) {
			let that =this
			this.problemInfo=JSON.parse(uni.getStorageSync('problemInfo'))
			this.getComment()
			uni.getSystemInfo({
				success: function (res) {
					that.screenWindow=res.screenHeight-res.windowHeight
				}
			});
		},
		methods: {
			getFouseItem(info){
				this.shoFoucs=true
				uni.onKeyboardHeightChange(res => {
					if(res.height>0){
						this.keyUpHeight=res.height-this.screenWindow/2
					}else{
						this.keyUpHeight=-200
					}
					if(res.duration>0&&res.height==0){
						this.shoFoucs=false
						this.keyUpHeight=-200
					}
				})
			},
			scrolltolower(){
				console.log('-------------==============================------------')
				if (this.page * 10 >= this.infoLists.totalItems) {
					return false;
				}
				this.getComment(true, ++this.page);
			},
			scroll(e){
				this.scrollTopNum=e.detail.scrollTop
			},
			async getComment(isPage,page){
				let _this = this
				const res = await $http({
					url: 'zx/wap/CultureCircle/getComment',
					data: {
						topicId:this.problemInfo.id,
						pageNum:page||1,
						pageSize:10,
					}
				})
				if(res){
					this.infoLists = res.data;
					if(isPage==-1){
						_this.scrollTop=this.scrollTopNum
						this.$nextTick(() =>{
							 _this.scrollTop = 0
						})
					}
					if (isPage&&isPage!=-1) {
						this.answerInfo = [...this.answerInfo, ...res.data.data];
					} else {
						this.answerInfo = res.data.data || [];
					}
				}
			},
			async getGrowthValue(text){
				if(this.identityType!=4){
					return
				}
				const res = await $http({
					url: 'zx/wap/CultureCircle/getGrowthValue?eventType='+text+'&userId='+uni.getStorageSync('user_id'),
					method: 'POST',
					data: {}
				})
				if(res){
					if(Number(res.data)){
						this.$refs.growthPopupRefs.open(res.data)
					}
				}
			},
			async problemRelease(){
				if(!this.content){
					$showMsg('请输入您的回答内容');
					return
				}
				this.auditContent()
			},
			async auditContent(){
				let _this = this
				const res = await $http({
					url: 'zx/common/auditContent',
					method: 'POST',
					showLoading:true,
					data: {
						content:this.content,
						scene :2,
					}
				})
				if(res){
					if(res.data=='pass'){
						let _this = this
						const res = await $http({
							url: 'zx/wap/CultureCircle/comment',
							showLoading:true,
							method: 'POST',
							data: {
								content:this.content,
								topicId:this.problemInfo.id,
								replyType:1,
								userId:uni.getStorageSync('user_id')?uni.getStorageSync('user_id'):''
							}
						})
						if(res){
							this.content=''
							_this.page=1
							_this.getComment(-1)
							_this.getGrowthValue('ANSWER')
							this.getCultureCircle()
						}
					}else if(res.data=='risk'){
						$showMsg('您回复的内容有风险');
					}
				}
			},
			async getCultureCircle(){
			    const res = await $http({
			    	url: 'zx/wap/badge/gain?userId='+uni.getStorageSync('user_id')+'&eventType=ANSWER',
					showLoading:true,
					method: 'POST',
			    	data: {}
			    })
				if(res){
					console.log(res)
				}
			},
		}
	}
</script>

<style lang="scss" scoped>
	.problem_top_content{
		padding:0 56rpx;
		padding-top: 40rpx;
	}
	.problem_detail_style{
		height: 100vh;
		overflow: hidden;
	}
	.input_data_css{
		position: absolute;
		left:0;
		bottom:0rpx;
		background-color: #fff;
		width: 750rpx;
		display: flex;
		justify-content: flex-start;
		z-index: 999;
		padding-bottom: 60rpx;
		input{
			width: 600rpx;
			height: 130rpx;
			line-height: 130rpx;
			color:#BEBEBE;
			padding-left: 32rpx;
			font-size: 28rpx;
		}
		.fabu_btn{
			width: 160rpx;
			height: 72rpx;
			background-color: #339378;
			border-radius: 36rpx;
			text-align: center;
			line-height: 72rpx;
			margin-top: 32rpx;
			margin-right: 32rpx;
		}
	}
	.no_empty_page{
		height: calc(100vh - 210rpx);
	}
	.scroll_view_content{
		height: calc(100vh - 210rpx);
	}
	.da_content_css{
		background-color: #F8F8F8;
		border-radius: 40rpx 40rpx 0rpx 0rpx;
		padding-top: 32rpx;
		margin-top: 34rpx;
		padding-bottom: 60rpx;
		width:686rpx;
		.content_css{
			word-break:break-all;
		}
		.da_item_css{
			border-radius: 16rpx;
			margin-bottom: 24rpx;
			padding:24rpx;
		}
		.more_css{
			display: flex;
			justify-content: space-between;
			width: 100%;
			color:#28A781;
			margin-top: 32rpx;
			.more_icon_css{
				width: 15rpx;
				height: 20rpx;
			}
		}
		position: relative;
		.input_center_css{
			width: 686rpx;
			position: absolute;
			left:32rpx;
			bottom:20rpx;
			border-radius: 16rpx;
			input{
				height: 80rpx;
				line-height: 80rpx;
				color:#BEBEBE;
				font-size: 28rpx;
				padding-left: 26rpx;
			}
		}
	}
	.release_image{
		width: 32rpx;
		height: 32rpx;
	}
	.right_css_style{
		display: flex;
		justify-content: flex-start;
	}
.da_icon_css,.wen_icon_css{
	width: 40rpx;
	height: 40rpx;
	bottom:0;
	right:0;
}
</style>
