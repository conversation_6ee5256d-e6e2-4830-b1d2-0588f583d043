<template>
  <view class="videoBox">
    <view class="container" @scroll="handleScroll">
      <view class="video-list">
        <view class="video-card" v-for="(video, index) in videos" :key="index" @click="goVideoPlay(index)">
          <image class="video-image" :src="video.image" mode="aspectFill" />
          <image class="videoUnlock" src="https://document.dxznjy.com/dxSelect/496b00d6-584d-4b90-b64d-536341e7d39d.png" mode="widthFix" v-if="video.isUnlock == 0" />
          <view class="video-title">{{ video.videoName }}</view>
        </view>
      </view>
      <view v-if="!hasMore" class="no-more">没有更多数据了</view>
    </view>
  </view>
</template>

<script>
  export default {
    data() {
      return {
        videos: [],
        studentCode: '',
        nodeLevel: '',
        nodeId: '',
        pageSize: 10,
        pageNum: 1,
        loading: false, // 加载状态
        hasMore: true // 是否还有更多数据
      };
    },
    onLoad(option) {
      console.log('视频列表token', option.token);
      console.log('视频列表 baseurl', uni.getStorageSync('baseUrl'));
      if (option.token) {
        this.$handleTokenFormNative(option); // 从app获取token
      }
      this.studentCode = option.studentCode;
      this.nodeLevel = option.nodeLevel;
      this.nodeId = option.nodeId;
      this.getVideList();
    },
    methods: {
      getVideList(loadMore = false) {
        if (!this.hasMore || this.loading) return;

        this.loading = true;
        uni.showLoading({ title: '加载中' });
        // const id = '1367202768627843072';
        this.$httpUser
          .get('dyf/math/wap/video/queryVideosByNodeIdPage', {
            nodeLevel: this.nodeLevel,
            nodeId: this.nodeId,
            studentCode: this.studentCode,
            pageSize: this.pageSize,
            pageNum: this.pageNum
          })
          .then((res) => {
            uni.hideLoading();

            const newVideos = res.data.data.data || [];
            // 创建一个数组来存储所有请求的 Promise
            const requestPromises = newVideos.map((item, index) => {
              const vid = item.vid;
              return this.$httpUser
                .get(`media/web/video/details/${vid}`)
                .then((response) => {
                  if (response && response.data) {
                    item.image = response.data.data.firstImg;
                  }
                })
                .catch((error) => {
                  console.error('获取视频详情失败:', error);
                });
            });

            // 使用 Promise.all 等待所有请求完成
            Promise.all(requestPromises).then(() => {
              if (newVideos.length > 0) {
                this.videos = loadMore ? [...this.videos, ...newVideos] : newVideos;
                this.pageNum += 1;
              } else {
                this.hasMore = false;
              }
              this.loading = false;
            });
          })
          .catch((err) => {
            console.error('请求失败:', err);
            uni.showToast({ title: '加载失败', icon: 'none' });
            this.loading = false;
            uni.hideLoading();
          });
      },
      handleScroll(e) {
        const { scrollHeight, scrollTop, clientHeight } = e.detail;
        const bottom = scrollHeight - scrollTop === clientHeight;

        if (bottom) {
          this.getVideList(true); // 调用分页加载
        }
      },
      onPullDownRefresh() {
        this.pageNum = 1;
        this.hasMore = true;
        this.getVideList();
      },
      goVideoPlay(index) {
        if (this.videos[index].isUnlock == 0) return uni.showToast({ title: '视频暂未解锁', icon: 'none' });
        uni.navigateTo({
          url: `./knowVideoPlay?index=${index}&videoInfo=` + JSON.stringify(this.videos)
        });
      }
    }
  };
</script>

<style scoped>
  .videoBox {
    background-color: #fff;
    height: 100vh;
  }
  .container {
    padding: 16px;
  }

  .video-list {
    display: flex;
    flex-wrap: wrap; /* 允许换行 */
    gap: 16px; /* 卡片之间的间距 */
  }

  .video-card {
    width: calc(50% - 8px); /* 每行两个卡片，减去间距的一半 */
    background-color: #fff;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    position: relative;
  }

  .video-image {
    width: 336rpx;
    height: 189rpx;
    object-fit: cover; /* 确保图片比例 */
  }
  .videoUnlock {
    width: 60rpx;
    position: absolute;
    top: 35%;
    left: 50%;
    transform: translate(-50%, -50%);
  }

  .video-title {
    padding: 8px;
    font-size: 14px;
    color: #333;
    text-align: center;
  }
  .no-more {
    text-align: center;
    padding: 15px;
    color: #999;
    font-size: 14px;
  }
</style>
