<template>
  <!-- 题目 -->
  <view class="questionBox pr-40 pl-40 mt-10">
    <text>{{ currentIndex }}.{{ questionText }}</text>
    <view v-if="type == 0">
      <view
        v-for="(item, index) in superReadOptionDtoList"
        :key="item.id"
		class="optionAnswer"
		:class="{
		  optionRightAnswer: currentAnswerData.optionSelectedStatus[index] && currentAnswerData.isAnswerCorrectMap && currentAnswerData.id && item.optionIsAnswer,
		  optionErrorAnswer:
		    currentAnswerData.optionSelectedStatus[index] &&
		    !currentAnswerData.isAnswerCorrectMap &&
		    currentAnswerData.id &&
		    (item.optionIsAnswer === 1 ? item.optionIsAnswer : !item.optionIsAnswer),
		  selectedOption: item.isSelected && currentSelect[index] && !currentAnswerData.id
		}"
        @click="optionItem(item, index)"
      >
        {{ item.choiceOption }}.{{ item.content }}
      </view>
    </view>
     
    <view v-if="type == 1">
      <view
        class="optionAnswer"
        :class="{
          optionRightAnswer: item.isSelected && item.optionIsAnswer,
          optionErrorAnswer: item.isSelected && (item.optionIsAnswer === 1 ? item.optionIsAnswer : !item.optionIsAnswer)
        }"
        v-for="(item, index) in superReadOptionDtoList"
        :key="item.id"
        @click="optionItem(item, index)"
      >
        <text>{{ item.choiceOption }}.{{ item.content }}</text>
      </view>
    </view>
    <!-- 解析与正确答案 -->
    <view class="mt-80 f-28" v-if="type == 0">
      <view class="flex-e mb-20">
        <text class="iconBox" v-if="!currentAnswerData.id" @click="viewAnswer">查看答案</text>
      </view>
      <view v-if="currentAnswerData.id">
        <view class="mb-10">
          正确答案：
          <text class="t-w">{{ correctAnswer }}</text>
        </view>
        <text>解析： {{ analysis }}</text>
      </view>
    </view>
    <view class="mt-80 f-28" v-if="type == 1">
      <view class="flex-e mb-20">
        <!-- <text class="iconBox" v-if="!currentAnswerData.id" @click="viewAnswer">查看答案</text> -->
      </view>
      <view>
        <view class="mb-10">
          正确答案：
          <text class="t-w">{{ correctAnswer }}</text>
        </view>
        <text>解析： {{ analysis }}</text>
      </view>
    </view>
  </view>
</template>

<script>
  export default {
    props: {
      questionList: Array,
      currentQuestionIndex: Number,
      type: String
    },
    data() {
      return {
        optionList: [],

        currentIndex: 1,
        questionText: '', // 题目
        superReadOptionDtoList: [], // 选项
        analysis: '', // 解析
        correctAnswer: '', // 正确答案
        selectedOption: null, // 选中的答案
        isAnswerCorrect: '', // 输入答案颜色区别
        choiceQuestionType: '', // 判断题型
        selectedOptionArray: [], // 选中的答案数组
        currentSelect: {},
        currentAnswerData: { id: null, isViewAnswer: this.currentAnswerData?.isViewAnswer ?? false, optionSelectedStatus: {}, isAnswerCorrectMap: {} }
      };
    },
    mounted() {
      this.initQuestionData();
    },
    watch: {
      currentQuestionIndex(newVal) {
        this.initQuestionData(newVal);
      },
      type(newVal) {
        (this.selectedOption = ''), (this.currentAnswerData.isViewAnswer = !this.currentAnswerData.isViewAnswer);
        const question = this.questionList[this.currentQuestionIndex];
        if (question && question.studentAnswer) {
          this.superReadOptionDtoList.forEach((item, index) => {
            item.isSelected = false;
          });
        }
        this.initQuestionData(this.currentQuestionIndex, newVal);
        if (newVal == 0) {
          this.currentAnswerData = { id: null, isViewAnswer: false, optionSelectedStatus: {}, isAnswerCorrectMap: {} };
        }
      }
    },

    methods: {
      initQuestionData(index = this.currentQuestionIndex, type = this.type) {
        // 获取存储的答案数据列表
        const storedAnswerDataList = uni.getStorageSync('answerDataList');

        // 检查是否存在存储数据且不为空数组
        if (storedAnswerDataList && storedAnswerDataList.length > 0) {
          // 遍历列表查找当前题目索引对应的答案数据
          this.currentAnswerData = storedAnswerDataList.find((item) => item.index === index);
          if (this.currentAnswerData?.isViewAnswer) {
            this.selectedOption = this.currentAnswerData.studentAnswer;
            // 获取并处理本地存储的选项选中状态
            const optionSelectedStatus = this.currentAnswerData.optionSelectedStatus;
            if (optionSelectedStatus) {
              this.superReadOptionDtoList.forEach((item, index) => {
                item.isSelected = optionSelectedStatus[index];
              });
            }
            this.superReadOptionDtoList.forEach((item, index) => {
              item.isSelected = false;
            });
          } else {
            this.currentAnswerData = { id: null, isViewAnswer: false, optionSelectedStatus: {}, isAnswerCorrectMap: {} };
            this.selectedOption = '';
          }
        }
        // 根据题目类型进行初始化
        if (type == 1) {
          this.currentAnswerData.isViewAnswer = true;
        } else if (type == 0 && this.currentAnswerData.id) {
          this.currentAnswerData.isViewAnswer = true;
        } else {
          this.currentAnswerData.isViewAnswer = false;
        }
        this.selectedOptionArray = [];
        const questionInfo = this.questionList[index]?.questionInfo || {};
        this.selectedOption = this.type == 1 ? this.questionList[index].studentAnswer : '';
        this.optionList = questionInfo;
        this.choiceQuestionType = this.optionList.choiceQuestionType === 0 ? '单选题' : '多选题';
        this.currentIndex = index + 1;
        this.questionText = this.optionList.questionText.replace(/#/g, '_');
        this.superReadOptionDtoList = this.optionList.superReadOptionDtoList;
        this.analysis = this.optionList.analysis;
        this.correctAnswer = this.optionList.correctAnswer;

        // 判断题目是否已做，并设置已选中的选项
        if (this.questionList[index].studentAnswer) {
          this.superReadOptionDtoList.forEach((item) => {
            if (this.choiceQuestionType === '单选题') {
              item.isSelected = item.choiceOption === this.selectedOption;
            } else {
              // 如果是多选题，将 selectedOption 解析成数组后判断
              const selectedOptions = this.selectedOption ? this.selectedOption.split(',') : [];
              item.isSelected = selectedOptions.includes(item.choiceOption);
            }
          });
        }
      },

      optionItem(item, index) {
        if (this.currentAnswerData.isViewAnswer) return;
        this.currentSelect[index] = true;
        // 单选题	
        if (!this.optionList.choiceQuestionType) {
          this.selectedOption = item.choiceOption;
          // 如果已经选中则取消选中
          if (item.isSelected) {
            // item.isSelected = false;
			this.$set(this.superReadOptionDtoList[index], 'isSelected', false);
            this.selectedOption = null;
            this.currentAnswerData.optionSelectedStatus[index] = false;
          } else {
            this.superReadOptionDtoList.forEach((opt, idx) => {
              opt.isSelected = false; // 清除选中状态
              this.currentAnswerData.optionSelectedStatus[idx] = false; // 确保选项状态同步
            });
            // item.isSelected = true;
			this.$set(this.superReadOptionDtoList[index], 'isSelected', true);
            this.currentAnswerData.optionSelectedStatus[index] = true;
            this.isAnswerCorrect = this.correctAnswer === this.selectedOption;
          }
        } else {
          // 多选题
          if (item.isSelected) {
            // 已选中则取消
            // item.isSelected = false;
			this.$set(this.superReadOptionDtoList[index], 'isSelected', false);
            this.currentAnswerData.optionSelectedStatus[index] = false;
            this.selectedOptionArray = this.selectedOptionArray.filter((opt) => opt.choiceOption !== item.choiceOption);
          } else {
            // 未选中则加入已选
            this.selectedOptionArray.push(item);
            // item.isSelected = true;
			this.$set(this.superReadOptionDtoList[index], 'isSelected', true);
            // 更新本地存储的选项选中状态
            this.currentAnswerData.optionSelectedStatus[index] = true;
            // 检查多选的正确性
            let sortedVal = Array.from(new Set(this.selectedOptionArray.map((opt) => opt.choiceOption))).sort();
            this.selectedOption = sortedVal.join(',');
            this.isAnswerCorrect = this.correctAnswer === this.selectedOption;
          }
        }
		this.$forceUpdate();
      },
      viewAnswer() {
        let currentIndex = this.currentQuestionIndex;
        if (this.selectedOption && !this.currentAnswerData.id) {
          const newAnswerData = {
            id: this.optionList.id,
            studentAnswer: this.selectedOption,
            index: currentIndex,
            optionSelectedStatus: this.currentAnswerData.optionSelectedStatus,
            isAnswerCorrectMap: this.isAnswerCorrect,
            isViewAnswer: true
          };
          this.currentAnswerData = newAnswerData;
          // 获取当前存储的答案数据列表
          const storedAnswerDataList = uni.getStorageSync('answerDataList');
          // 将新答案数据添加到列表中
          storedAnswerDataList.push(newAnswerData);
          // 更新本地存储的答案数据列表
          uni.setStorageSync('answerDataList', storedAnswerDataList);
          this.$emit('answerSubmitted', { id: this.optionList.id, studentAnswer: this.selectedOption, index: currentIndex });
          // 获取当前存储的 answerView 对象
          let answerViewMap = uni.getStorageSync('answerViewMap') || {};
          // 更新当前索引和下一个索引对应的 answerView 值
          answerViewMap[currentIndex] = true;
          answerViewMap[currentIndex + 1] = true;
          // 更新本地存储的 answerView 对象
          uni.setStorageSync('answerViewMap', answerViewMap);
        } else {
          uni.showToast({
            title: '请选择答案~',
            icon: 'none'
          });
        }
      }
    }
  };
</script>
<style>
  page {
    background-color: #fff;
  }
</style>
<style lang="scss" scoped>
  .iconBox {
    border: 2rpx solid #339378;
    border-radius: 8rpx;
    font-size: 24rpx;
    color: #339378;
    padding: 6rpx;
  }

  .questionBox {
    background-color: rgba(255, 255, 255, 1);
    position: relative;
    display: flex;
    flex-direction: column;
  }

  /* 答案公共样式 */
  .optionBase {
    border-radius: 16rpx;
    display: flex;
    flex-direction: column;
    flex-wrap: wrap;
    padding: 26rpx 30rpx;
    margin-top: 30rpx;
    font-size: 28rpx;
  }

  /* 错误答案样式 */
  .optionErrorAnswer {
    @extend .optionBase;
    background-color: rgba(255, 172, 129, 0.1);
    border: 1px solid rgba(255, 175, 133, 1);
    color: rgba(255, 172, 128, 1);
  }

  /* 正确答案样式 */
  .optionRightAnswer {
    @extend .optionBase;
    background-color: rgba(148, 230, 199, 0.15);
    border: 1px solid rgba(148, 230, 199, 1);
    color: rgba(66, 138, 111, 1);
  }
  .selectedOption {
    @extend .optionBase;
    background-color: #f8ffe7; // 选中背景色
    border: 1px solid #d2ea96; // 选中边框
  }

  /* 普通答案样式 */
  .optionAnswer {
    @extend .optionBase;
    border: 1px solid rgba(239, 239, 240, 1);
  }
</style>
