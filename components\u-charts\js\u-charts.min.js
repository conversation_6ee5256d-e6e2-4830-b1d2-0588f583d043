'use strict';var config={yAxisWidth:15,yAxisSplit:5,xAxisHeight:15,xAxisLineHeight:15,legendHeight:15,yAxisTitleWidth:15,padding:12,pixelRatio:1,rotate:!1,columePadding:3,fontSize:13,dataPointShape:["circle","circle","circle","circle"],colors:["#1890ff","#2fc25b","#facc14","#f04864","#8543e0","#90ed7d"],pieChartLinePadding:15,pieChartTextPadding:5,xAxisTextPadding:3,titleColor:"#333333",titleFontSize:20,subtitleColor:"#999999",subtitleFontSize:15,toolTipPadding:3,toolTipBackground:"#000000",toolTipOpacity:.7,toolTipLineHeight:20,radarGridCount:3,radarLabelTextMargin:15,gaugeLabelTextMargin:15};function assign(e){if(null==e)throw new TypeError("Cannot convert undefined or null to object");for(var t,i=Object(e),o=1;o<arguments.length;o++)if(t=arguments[o],null!=t)for(var a in t)Object.prototype.hasOwnProperty.call(t,a)&&(i[a]=t[a]);return i}var util={toFixed:function(e,t){return t=t||2,this.isFloat(e)&&(e=e.toFixed(t)),e},isFloat:function(e){return 0!=e%1},approximatelyEqual:function(e,t){return 1e-10>Math.abs(e-t)},isSameSign:function(e,t){var i=Math.abs;return i(e)===e&&i(t)===t||i(e)!==e&&i(t)!==t},isSameXCoordinateArea:function(e,t){return this.isSameSign(e.x,t.x)},isCollision:function(e,t){e.end={},e.end.x=e.start.x+e.width,e.end.y=e.start.y-e.height,t.end={},t.end.x=t.start.x+t.width,t.end.y=t.start.y-t.height;var i=t.start.x>e.end.x||t.end.x<e.start.x||t.end.y>e.start.y||t.start.y<e.end.y;return!i}};function hexToRgb(e,t){var i=/^#?([a-f\d])([a-f\d])([a-f\d])$/i,o=e.replace(i,function(e,t,i,o){return t+t+i+i+o+o}),a=/^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(o),n=parseInt(a[1],16),l=parseInt(a[2],16),s=parseInt(a[3],16);return"rgba("+n+","+l+","+s+","+t+")"}function findRange(e,t,i){if(isNaN(e))throw new Error("[wxCharts] unvalid series data!");i=i||10,t=t?t:"upper";for(var o=1;1>i;)i*=10,o*=10;for(e="upper"===t?Math.ceil(e*o):Math.floor(e*o);0!=e%i;)"upper"===t?e++:e--;return e/o}function calCandleMA(e,t,i,o){let a=[];for(let n,l=0;l<e.length;l++){n={data:[],name:t[l],color:i[l]};for(let t=0,i=o.length;t<i;t++){if(t<e[l]){n.data.push(null);continue}let i=0;for(let a=0;a<e[l];a++)i+=o[t-a][1];n.data.push(+(i/e[l]).toFixed(3))}a.push(n)}return a}function calValidDistance(e,t,i,o){var a=o.width-i.padding-t.xAxisPoints[0],n=t.eachSpacing*o.categories.length,l=e;return 0<=e?l=0:Math.abs(e)>=n-a&&(l=a-n),l}function isInAngleRange(e,t,i){function o(e){for(;0>e;)e+=2*a;for(;e>2*a;)e-=2*a;return e}var a=Math.PI;return e=o(e),t=o(t),i=o(i),t>i&&(i+=2*a,e<t&&(e+=2*a)),e>=t&&e<=i}function calRotateTranslate(e,t,i){var o=e,a=i-t,n=o+(i-a-o)/1.4142135623730951;n*=-1;return{transX:n,transY:(i-a)*(1.4142135623730951-1)-(i-a-o)/1.4142135623730951}}function createCurveControlPoints(e,t){function i(e,t){return!!(e[t-1]&&e[t+1])&&(e[t].y>=Math.max(e[t-1].y,e[t+1].y)||e[t].y<=Math.min(e[t-1].y,e[t+1].y))}var o=.2,a=.2,n=null,l=null,s=null,r=null;if(1>t?(n=e[0].x+(e[1].x-e[0].x)*o,l=e[0].y+(e[1].y-e[0].y)*o):(n=e[t].x+(e[t+1].x-e[t-1].x)*o,l=e[t].y+(e[t+1].y-e[t-1].y)*o),t>e.length-3){var d=e.length-1;s=e[d].x-(e[d].x-e[d-1].x)*a,r=e[d].y-(e[d].y-e[d-1].y)*a}else s=e[t+1].x-(e[t+2].x-e[t].x)*a,r=e[t+1].y-(e[t+2].y-e[t].y)*a;return i(e,t+1)&&(r=e[t+1].y),i(e,t)&&(l=e[t].y),{ctrA:{x:n,y:l},ctrB:{x:s,y:r}}}function convertCoordinateOrigin(e,t,i){return{x:i.x+e,y:i.y-t}}function avoidCollision(e,t){if(t)for(;util.isCollision(e,t);)0<e.start.x?e.start.y--:0>e.start.x?e.start.y++:0<e.start.y?e.start.y++:e.start.y--;return e}function fillSeriesColor(e,t){var i=0;return e.map(function(e){return e.color||(e.color=t.colors[i],i=(i+1)%t.colors.length),e})}function fillSeriesType(e,t){return e.map(function(e){return e.type||(e.type=t.type),e})}function getDataRange(e,t){var i=0,o=t-e;return i=1e4<=o?1e3:1e3<=o?100:100<=o?10:10<=o?5:1<=o?1:.1<=o?.1:.01,{minRange:findRange(e,"lower",i),maxRange:findRange(t,"upper",i)}}function measureText(e){var t=1<arguments.length&&arguments[1]!==void 0?arguments[1]:config.fontSize;e=e+"";var e=e.split(""),o=0;for(let t,a=0;a<e.length;a++)t=e[a],o+=/[a-zA-Z]/.test(t)?7:/[0-9]/.test(t)?5.5:/\./.test(t)?2.7:/-/.test(t)?3.25:/[\u4e00-\u9fa5]/.test(t)?10:/\(|\)/.test(t)?3.73:/\s/.test(t)?2.5:/%/.test(t)?8:10;return o*t/10}function dataCombine(e){return e.reduce(function(e,t){return(e.data?e.data:e).concat(t.data)},[])}function dataCombineStack(e){for(var t=Array(e[0].data.length),o=0;o<t.length;o++)t[o]=0;for(var a=0;a<e.length;a++)for(var o=0;o<t.length;o++)t[o]+=e[a].data[o];return e.reduce(function(e,i){return(e.data?e.data:e).concat(i.data).concat(t)},[])}function getTouches(t,i,o){let e,a;return t.clientX?i.rotate?(a=i.height-t.clientX*i.pixelRatio,e=(t.pageY-o.currentTarget.offsetTop-i.height/i.pixelRatio/2*(i.pixelRatio-1))*i.pixelRatio):(e=t.clientX*i.pixelRatio,a=(t.pageY-o.currentTarget.offsetTop-i.height/i.pixelRatio/2*(i.pixelRatio-1))*i.pixelRatio):i.rotate?(a=i.height-t.x*i.pixelRatio,e=t.y*i.pixelRatio):(e=t.x*i.pixelRatio,a=t.y*i.pixelRatio),{x:e,y:a}}function getSeriesDataItem(e,t){var i=[];for(let o,a=0;a<e.length;a++)if(o=e[a],null!==o.data[t]&&"undefined"!=typeof o.data[t]){let e={};e.color=o.color,e.type=o.type,e.style=o.style,e.shape=o.shape,e.disableLegend=o.disableLegend,e.name=o.name,e.data=o.format?o.format(o.data[t]):o.data[t],i.push(e)}return i}function getMaxTextListLength(e){var t=e.map(function(e){return measureText(e)});return Math.max.apply(null,t)}function getRadarCoordinateSeries(e){for(var t=Math.PI,o=[],a=0;a<e;a++)o.push(2*t/e*a);return o.map(function(e){return-1*e+t/2})}function getToolTipData(e,t,o,i){var a=4<arguments.length&&void 0!==arguments[4]?arguments[4]:{},n=e.map(function(e){return{text:a.format?a.format(e,i[o]):e.name+": "+e.data,color:e.color}}),l=[],s={x:0,y:0};for(let a,n=0;n<t.length;n++)a=t[n],"undefined"!=typeof a[o]&&null!==a[o]&&l.push(a[o]);for(let a,n=0;n<l.length;n++)a=l[n],s.x=Math.round(a.x),s.y+=a.y;return s.y/=l.length,{textList:n,offset:s}}function getMixToolTipData(e,t,o,i){var a=4<arguments.length&&void 0!==arguments[4]?arguments[4]:{},n=e.map(function(e){return{text:a.format?a.format(e,i[o]):e.name+": "+e.data,color:e.color,disableLegend:!!e.disableLegend}});n=n.filter(function(e){if(!0!==e.disableLegend)return e});var l=[],s={x:0,y:0};for(let a,n=0;n<t.length;n++)a=t[n],"undefined"!=typeof a[o]&&null!==a[o]&&l.push(a[o]);for(let a,n=0;n<l.length;n++)a=l[n],s.x=Math.round(a.x),s.y+=a.y;return s.y/=l.length,{textList:n,offset:s}}function getCandleToolTipData(e,t,o,a,i,n){6<arguments.length&&void 0!==arguments[6]?arguments[6]:{};let l=n.color.upFill,s=n.color.downFill,r=[l,l,s,l];var d=[];let h={text:i[a],color:null};d.push(h),t.map(function(t){0==a&&0>t.data[1]-t.data[0]?r[1]=s:(t.data[0]<e[a-1][1]&&(r[0]=s),t.data[1]<t.data[0]&&(r[1]=s),t.data[2]>e[a-1][1]&&(r[2]=l),t.data[3]<e[a-1][1]&&(r[3]=s));let i={text:"\u5F00\u76D8\uFF1A"+t.data[0],color:r[0]},o={text:"\u6536\u76D8\uFF1A"+t.data[1],color:r[1]},n={text:"\u6700\u4F4E\uFF1A"+t.data[2],color:r[2]},h={text:"\u6700\u9AD8\uFF1A"+t.data[3],color:r[3]};d.push(i,o,n,h)});var x=[],c={x:0,y:0};for(let l,s=0;s<o.length;s++)l=o[s],"undefined"!=typeof l[a]&&null!==l[a]&&x.push(l[a]);return c.x=Math.round(x[0][0].x),{textList:d,offset:c}}function findCurrentIndex(e,t,i,o){var a=4<arguments.length&&void 0!==arguments[4]?arguments[4]:0,n=-1;return isInExactChartArea(e,i,o)&&t.forEach(function(t,i){e.x+a>t&&(n=i)}),n}function isInExactChartArea(e,t,i){return e.x<t.width-i.padding&&e.x>i.padding+i.yAxisWidth+i.yAxisTitleWidth&&e.y>i.padding&&e.y<t.height-i.legendHeight-i.xAxisHeight-i.padding}function findRadarChartCurrentIndex(e,t,i){var o=Math.PI,a=2*o/i,n=-1;if(isInExactPieChartArea(e,t.center,t.radius)){var l=function(e){return 0>e&&(e+=2*o),e>2*o&&(e-=2*o),e},s=Math.atan2(t.center.y-e.y,e.x-t.center.x);s=-1*s,0>s&&(s+=2*o);var r=t.angleList.map(function(e){return e=l(-1*e),e});r.forEach(function(e,t){var i=l(e-a/2),r=l(e+a/2);r<i&&(r+=2*o),(s>=i&&s<=r||s+2*o>=i&&s+2*o<=r)&&(n=t)})}return n}function findPieChartCurrentIndex(e,t){var o=-1;if(isInExactPieChartArea(e,t.center,t.radius)){var a=Math.atan2(t.center.y-e.y,e.x-t.center.x);a=-a;for(var n,l=0,s=t.series.length;l<s;l++)if(n=t.series[l],isInAngleRange(a,n._start_,n._start_+2*n._proportion_*Math.PI)){o=l;break}}return o}function isInExactPieChartArea(e,t,i){var o=Math.pow;return o(e.x-t.x,2)+o(e.y-t.y,2)<=o(i,2)}function splitPoints(e){var t=[],i=[];return e.forEach(function(e){null===e?(i.length&&t.push(i),i=[]):i.push(e)}),i.length&&t.push(i),t}function calLegendData(e,t,i){if(!1===t.legend)return{legendList:[],legendHeight:0};var o=5*t.pixelRatio,a=8*t.pixelRatio,n=15*t.pixelRatio,l=[],s=0,r=[];for(let a=0;a<e.length;a++){let i=e[a],d=3*o+n+measureText(i.name||"undefined");s+d>t.width?(l.push(r),s=d,r=[i]):(s+=d,r.push(i))}return r.length&&l.push(r),{legendList:l,legendHeight:l.length*(i.fontSize+a)+o}}function calCategoriesData(e,t,i){var o={angle:0,xAxisHeight:i.xAxisHeight},a=getXAxisPoints(e,t,i),n=a.eachSpacing,l=e.map(function(e){return measureText(e)}),s=Math.max.apply(this,l);return!0==t.xAxis.rotateLabel&&s+2*i.xAxisTextPadding>n&&(o.angle=45*Math.PI/180,o.xAxisHeight=2*i.xAxisTextPadding+s*Math.sin(o.angle)),o}function getRadarDataPoints(e,t,i,o,a){var n=Math.max,l=5<arguments.length&&void 0!==arguments[5]?arguments[5]:1,s=a.extra.radar||{};s.max=s.max||0;var r=n(s.max,n.apply(null,dataCombine(o))),d=[];for(let n=0;n<o.length;n++){let a=o[n],s={};s.color=a.color,s.data=[],a.data.forEach(function(o,a){let n={};n.angle=e[a],n.proportion=o/r,n.position=convertCoordinateOrigin(i*n.proportion*l*Math.cos(n.angle),i*n.proportion*l*Math.sin(n.angle),t),s.data.push(n)}),d.push(s)}return d}function getPieDataPoints(e,t){var o=2<arguments.length&&arguments[2]!==void 0?arguments[2]:1,a=0,n=0;for(let o,n=0;n<e.length;n++)o=e[n],o.data=null===o.data?0:o.data,a+=o.data;for(let n,l=0;l<e.length;l++)n=e[l],n.data=null===n.data?0:n.data,n._proportion_=0===a?1/e.length*o:n.data/a*o,n._radius_=t;for(let o,a=0;a<e.length;a++)o=e[a],o._start_=n,n+=2*o._proportion_*Math.PI;return e}function getRoseDataPoints(e,t,o,a){var n=4<arguments.length&&arguments[4]!==void 0?arguments[4]:1,l=0,s=0,r=[];for(let n,s=0;s<e.length;s++)n=e[s],n.data=null===n.data?0:n.data,l+=n.data,r.push(n.data);var d=r.pop(),h=r.shift();for(let s,r=0;r<e.length;r++)s=e[r],s.data=null===s.data?0:s.data,s._proportion_=0===l||"area"==t?1/e.length*n:s.data/l*n,s._radius_=o+(a-o)*((s.data-d)/(h-d));for(let n,l=0;l<e.length;l++)n=e[l],n._start_=s,s+=2*n._proportion_*Math.PI;return e}function getArcbarDataPoints(e,t){var o=2<arguments.length&&arguments[2]!==void 0?arguments[2]:1;1==o&&(o=.999999);for(let a,n=0;n<e.length;n++){a=e[n],a.data=null===a.data?0:a.data;let i;i="default"==t.type?t.startAngle-t.endAngle+1:2,a._proportion_=i*a.data*o+t.startAngle,2<=a._proportion_&&(a._proportion_%=2)}return e}function getGaugeAxisPoints(e,t,o){let a=t;for(let n=0;n<e.length;n++)e[n].value=null===e[n].value?0:e[n].value,e[n]._startAngle_=a,e[n]._endAngle_=(t-o+1)*e[n].value+t,2<=e[n]._endAngle_&&(e[n]._endAngle_%=2),a=e[n]._endAngle_;return e}function getGaugeDataPoints(e,t,o){let a=3<arguments.length&&arguments[3]!==void 0?arguments[3]:1;for(let n,l=0;l<e.length;l++){if(n=e[l],n.data=null===n.data?0:n.data,"auto"==o.pointer.color){for(let e=0;e<t.length;e++)if(n.data<=t[e].value){n.color=t[e].color;break}}else n.color=o.pointer.color;let i=o.startAngle-o.endAngle+1;n._endAngle_=i*n.data+o.startAngle,n._oldAngle_=o.oldAngle,o.oldAngle<o.endAngle&&(n._oldAngle_+=2),n._proportion_=n.data>=o.oldData?(n._endAngle_-n._oldAngle_)*a+o.oldAngle:n._oldAngle_-(n._oldAngle_-n._endAngle_)*a,2<=n._proportion_&&(n._proportion_%=2)}return e}function getPieTextMaxLength(e){e=getPieDataPoints(e);let t=0;for(let o=0;o<e.length;o++){let i=e[o],a=i.format?i.format(+i._proportion_.toFixed(2)):util.toFixed(100*i._proportion_)+"%";t=Math.max(t,measureText(a))}return t}function fixColumeData(e,t,i,o,a,n){var l=Math.min;return e.map(function(e){return null===e?null:(e.width=(t-2*a.columePadding)/i,e.width=n.extra.column&&n.extra.column.width&&0<+n.extra.column.width?l(e.width,+n.extra.column.width):l(e.width,25),e.x+=(o+.5-i/2)*e.width,e)})}function fixColumeMeterData(e,t,i,o,a,n,l){var s=Math.min;return e.map(function(e){return null===e?null:(e.width=t-2*a.columePadding,e.width=n.extra.column&&n.extra.column.width&&0<+n.extra.column.width?s(e.width,+n.extra.column.width):s(e.width,25),0<o&&(e.width-=2*l),e)})}function fixColumeStackData(e,t,i,o,a,n){var l=Math.min;return e.map(function(e){return null===e?null:(e.width=t-2*a.columePadding,e.width=n.extra.column&&n.extra.column.width&&0<+n.extra.column.width?l(e.width,+n.extra.column.width):l(e.width,25),e)})}function getXAxisPoints(e,t,i){var o=i.yAxisWidth+i.yAxisTitleWidth,a=t.width-2*i.padding-o,n=t.enableScroll?Math.min(t.xAxis.itemCount,e.length):e.length,l=a/n,s=[],r=i.padding+o,d=t.width-i.padding;return e.forEach(function(e,t){s.push(r+t*l)}),!0===t.enableScroll?s.push(r+e.length*l):s.push(d),{xAxisPoints:s,startX:r,endX:d,eachSpacing:l}}function getCandleDataPoints(e,t,i,o,a,n,l){var s=Math.round,r=7<arguments.length&&void 0!==arguments[7]?arguments[7]:1,d=[],h=n.height-2*l.padding-l.xAxisHeight-l.legendHeight;return e.forEach(function(e,x){if(null===e)d.push(null);else{var c=[];e.forEach(function(e){var d={x:o[x]+s(a/2)},p=e.value||e,g=h*(p-t)/(i-t);g*=r,d.y=n.height-l.xAxisHeight-l.legendHeight-s(g)-l.padding,c.push(d)}),d.push(c)}}),d}function getDataPoints(e,t,i,o,a,n,l){var s=Math.round,r=7<arguments.length&&void 0!==arguments[7]?arguments[7]:1,d=[],h=n.height-2*l.padding-l.xAxisHeight-l.legendHeight;return e.forEach(function(e,x){if(null===e)d.push(null);else{var c={color:e.color,x:o[x]+s(a/2)},p=e;"object"==typeof e&&null!=e&&(p=e.value);var g=h*(p-t)/(i-t);g*=r,c.y=n.height-l.xAxisHeight-l.legendHeight-s(g)-l.padding,d.push(c)}}),d}function getStackDataPoints(e,t,i,o,a,n,l,s,r){var d=Math.round,h=9<arguments.length&&void 0!==arguments[9]?arguments[9]:1,x=[],c=n.height-2*l.padding-l.xAxisHeight-l.legendHeight;return e.forEach(function(e,p){if(null===e)x.push(null);else{var g={color:e.color,x:o[p]+d(a/2)};if(0<s){var y=0;for(let e=0;e<=s;e++)y+=r[e].data[p];var f=y-e,u=c*(y-t)/(i-t),m=c*(f-t)/(i-t)}else var y=e,u=c*(y-t)/(i-t),m=0;var A=m;u*=h,A*=h,g.y=n.height-l.xAxisHeight-l.legendHeight-d(u)-l.padding,g.y0=n.height-l.xAxisHeight-l.legendHeight-d(A)-l.padding,x.push(g)}}),x}function getYAxisTextList(e,t,o,a){var n,l=Math.min,s=Math.max;n="stack"==a?dataCombineStack(e):dataCombine(e);var r=[];n=n.filter(function(e){return"object"==typeof e&&null!==e?e.constructor==Array?null!==e:null!==e.value:null!==e}),n.map(e=>{"object"==typeof e?e.constructor==Array?e.map(e=>{r.push(e)}):r.push(e.value):r.push(e)});var d=0,h=0;if(0<r.length&&(d=l.apply(this,r),h=s.apply(this,r)),"number"==typeof t.yAxis.min&&(d=l(t.yAxis.min,d)),"number"==typeof t.yAxis.max&&(h=s(t.yAxis.max,h)),d===h){var x=h||10;h+=x}for(var c=getDataRange(d,h),p=c.minRange,g=c.maxRange,y=[],f=(g-p)/o.yAxisSplit,u=0;u<=o.yAxisSplit;u++)y.push(p+f*u);return y.reverse()}function calYAxisData(e,t,i){var o=assign({},t.extra.column||{type:""}),a=getYAxisTextList(e,t,i,o.type),n=i.yAxisWidth,l=a.map(function(e){return e=util.toFixed(e,2),e=t.yAxis.format?t.yAxis.format(+e):e,n=Math.max(n,measureText(e)+5),e});return!0===t.yAxis.disabled&&(n=0),{rangesFormat:l,ranges:a,yAxisWidth:n}}function calTooltipYAxisData(e,t,i,o){var a=getYAxisTextList(t,i,o),n=i.height-2*o.padding-o.xAxisHeight-o.legendHeight;let l=a[0],s=a[a.length-1],r=o.padding,d=o.padding+n,h=l-(l-s)*(e-r)/(d-r);return h=i.yAxis.format?i.yAxis.format(+h):h,h}function contextRotate(e,t){var i=Math.PI;!0===t.rotateLock?!0!==t._rotate_&&(e.translate(t.height,0),e.rotate(90*i/180),t._rotate_=!0):(e.translate(t.height,0),e.rotate(90*i/180))}function drawPointShape(e,t,i,o,a){o.beginPath(),o.setStrokeStyle("#ffffff"),o.setLineWidth(1*a.pixelRatio),o.setFillStyle(t),"diamond"===i?e.forEach(function(e){null!==e&&(o.moveTo(e.x,e.y-4.5),o.lineTo(e.x-4.5,e.y),o.lineTo(e.x,e.y****),o.lineTo(e.x****,e.y),o.lineTo(e.x,e.y-4.5))}):"circle"===i?e.forEach(function(e){null!==e&&(o.moveTo(e.x*****a.pixelRatio,e.y),o.arc(e.x,e.y,4*a.pixelRatio,0,2*Math.PI,!1))}):"rect"===i?e.forEach(function(e){null!==e&&(o.moveTo(e.x-3.5,e.y-3.5),o.rect(e.x-3.5,e.y-3.5,7,7))}):"triangle"==i&&e.forEach(function(e){null!==e&&(o.moveTo(e.x,e.y-4.5),o.lineTo(e.x-4.5,e.y****),o.lineTo(e.x****,e.y****),o.lineTo(e.x,e.y-4.5))}),o.closePath(),o.fill(),o.stroke()}function drawRingTitle(e,t,i){var o=e.title.fontSize||t.titleFontSize,a=e.subtitle.fontSize||t.subtitleFontSize,n=e.title.name||"",l=e.subtitle.name||"",s=e.title.color||t.titleColor,r=e.subtitle.color||t.subtitleColor,d=n?o:0,h=l?a:0,x=5;if(l){var c=measureText(l,a),p=(e.width-c)/2+(e.subtitle.offsetX||0),g=(e.height-t.legendHeight+a)/2+(e.subtitle.offsetY||0);n&&(g-=(d+x)/2),i.beginPath(),i.setFontSize(a),i.setFillStyle(r),i.fillText(l,p,g),i.closePath(),i.stroke()}if(n){var y=measureText(n,o),f=(e.width-y)/2+(e.title.offsetX||0),u=(e.height-t.legendHeight+o)/2+(e.title.offsetY||0);l&&(u+=(h+x)/2),i.beginPath(),i.setFontSize(o),i.setFillStyle(s),i.fillText(n,f,u),i.closePath(),i.stroke()}}function drawPointText(e,t,i,o){var a=t.data,n=t.textColor==null?"#666666":t.textColor;e.forEach(function(e,l){if(null!==e){o.beginPath(),o.setFontSize(i.fontSize),o.setFillStyle(n);var s=a[l];"object"==typeof a[l]&&null!==a[l]&&(s=a[l].value);var r=t.format?t.format(s):s;o.fillText(r,e.x-measureText(r)/2,e.y-2),o.closePath(),o.stroke()}})}function drawGaugeLabel(e,t,i,o,a,n){var l=Math.PI;t-=e.width/2+a.gaugeLabelTextMargin;let s=e.startAngle-e.endAngle+1,r=s/e.splitLine.splitNumber,d=e.endNumber-e.startNumber,h=d/e.splitLine.splitNumber,x=e.startAngle,c=e.startNumber;for(let s=0;s<e.splitLine.splitNumber+1;s++){var p={x:t*Math.cos(x*l),y:t*Math.sin(x*l),x:i.x-measureText(c)/2,y:i.y},g=p.x,y=p.y;n.beginPath(),n.setFontSize(a.fontSize),n.setFillStyle(e.labelColor||"#666666"),n.fillText(c,g,y+a.fontSize/2),n.closePath(),n.stroke(),x+=r,2<=x&&(x%=2),c+=h}}function drawRadarLabel(e,t,i,o,a,n){var l=o.extra.radar||{};t+=a.radarLabelTextMargin,e.forEach(function(e,s){var r={x:t*Math.cos(e),y:t*Math.sin(e)},d=convertCoordinateOrigin(r.x,r.y,i),h=d.x,x=d.y;util.approximatelyEqual(r.x,0)?h-=measureText(o.categories[s]||"")/2:0>r.x&&(h-=measureText(o.categories[s]||"")),n.beginPath(),n.setFontSize(a.fontSize),n.setFillStyle(l.labelColor||"#666666"),n.fillText(o.categories[s]||"",h,x+a.fontSize/2),n.closePath(),n.stroke()})}function drawPieText(e,t,o,a,i,n){var l=Math.cos,s=Math.sin,r=Math.min,d=Math.max,h=Math.PI,x=o.pieChartLinePadding,c=[],p=null,g=e.map(function(e){var t=2*h-(e._start_+2*h*e._proportion_/2),i=e.format?e.format(+e._proportion_.toFixed(2)):util.toFixed(100*e._proportion_)+"%",o=e.color,a=e._radius_;return{arc:t,text:i,color:o,radius:a}});for(let h=0;h<g.length;h++){let e=g[h],t=l(e.arc)*(e.radius+x),i=s(e.arc)*(e.radius+x),a=l(e.arc)*e.radius,n=s(e.arc)*e.radius,y=0<=t?t+o.pieChartTextPadding:t-o.pieChartTextPadding,f=i,u=measureText(e.text),m=f;p&&util.isSameXCoordinateArea(p.start,{x:y})&&(0<y?m=r(f,p.start.y):0>t?m=d(f,p.start.y):0<f?m=d(f,p.start.y):m=r(f,p.start.y)),0>y&&(y-=u);let A={lineStart:{x:a,y:n},lineEnd:{x:t,y:i},start:{x:y,y:m},width:u,height:o.fontSize,text:e.text,color:e.color};p=avoidCollision(A,p),c.push(p)}for(let l=0;l<c.length;l++){let e=c[l],i=convertCoordinateOrigin(e.lineStart.x,e.lineStart.y,n),s=convertCoordinateOrigin(e.lineEnd.x,e.lineEnd.y,n),r=convertCoordinateOrigin(e.start.x,e.start.y,n);a.setLineWidth(1*t.pixelRatio),a.setFontSize(o.fontSize),a.beginPath(),a.setStrokeStyle(e.color),a.setFillStyle(e.color),a.moveTo(i.x,i.y);let d=0>e.start.x?r.x+e.width:r.x,x=0>e.start.x?r.x-5:r.x+5;a.quadraticCurveTo(s.x,s.y,d,r.y),a.moveTo(i.x,i.y),a.stroke(),a.closePath(),a.beginPath(),a.moveTo(r.x+e.width,r.y),a.arc(d,r.y,2,0,2*h),a.closePath(),a.fill(),a.beginPath(),a.setFontSize(o.fontSize),a.setFillStyle("#666666"),a.fillText(e.text,x,r.y+3),a.closePath(),a.stroke(),a.closePath()}}function drawToolTipSplitLine(e,t,i,o){var a=t.extra.tooltip||{};a.gridType=null==a.gridType?"solid":a.gridType,a.dashLength=null==a.dashLength?4:a.dashLength;var n=i.padding,l=t.height-i.padding-i.xAxisHeight-i.legendHeight;if("dash"==a.gridType&&o.setLineDash([a.dashLength,a.dashLength]),o.beginPath(),o.setStrokeStyle(a.gridColor||"#cccccc"),o.setLineWidth(1*t.pixelRatio),o.moveTo(e,n),o.lineTo(e,l),o.closePath(),o.stroke(),o.setLineDash([]),a.xAxisLabel){let n=t.categories[t.tooltip.index];o.setFontSize(i.fontSize);let s=o.measureText(n).width,r=e-i.toolTipPadding-.5*s,d=l;o.beginPath(),o.setFillStyle(hexToRgb(a.labelBgColor||i.toolTipBackground,a.labelBgOpacity||i.toolTipOpacity)),o.setStrokeStyle(a.labelBgColor||i.toolTipBackground),o.setLineWidth(1*t.pixelRatio),o.rect(r,d,s+2*i.toolTipPadding,i.fontSize+2*i.toolTipPadding),o.closePath(),o.stroke(),o.fill(),o.beginPath(),o.setFontSize(i.fontSize),o.setFillStyle(a.labelFontColor||i.fontColor),o.fillText(n,r+2*i.toolTipPadding,d+i.toolTipPadding+i.fontSize),o.closePath(),o.stroke()}}function drawToolTipHorizentalLine(e,t,i,o){var a=e.extra.tooltip||{};a.gridType=null==a.gridType?"solid":a.gridType,a.dashLength=null==a.dashLength?4:a.dashLength;var n=t.padding+t.yAxisWidth+t.yAxisTitleWidth,l=e.width-t.padding;if("dash"==a.gridType&&i.setLineDash([a.dashLength,a.dashLength]),i.beginPath(),i.setStrokeStyle(a.gridColor||"#cccccc"),i.setLineWidth(1*e.pixelRatio),i.moveTo(n,e.tooltip.offset.y),i.lineTo(l,e.tooltip.offset.y),i.closePath(),i.stroke(),i.setLineDash([]),a.yAxisLabel){let l=calTooltipYAxisData(e.tooltip.offset.y,e.series,e,t,o);i.setFontSize(t.fontSize);let s=i.measureText(l).width,r=n-2*t.toolTipPadding-s,d=e.tooltip.offset.y;i.beginPath(),i.setFillStyle(hexToRgb(a.labelBgColor||t.toolTipBackground,a.labelBgOpacity||t.toolTipOpacity)),i.setStrokeStyle(a.labelBgColor||t.toolTipBackground),i.setLineWidth(1*e.pixelRatio),i.rect(r,d-.5*t.fontSize-t.toolTipPadding,s+2*t.toolTipPadding,t.fontSize+2*t.toolTipPadding),i.closePath(),i.stroke(),i.fill(),i.beginPath(),i.setFontSize(t.fontSize),i.setFillStyle(a.labelFontColor||t.fontColor),i.fillText(l,r+t.toolTipPadding,d+.5*t.fontSize),i.closePath(),i.stroke()}}function drawToolTipSplitArea(e,t,i,o,a){var n=t.extra.tooltip||{activeBgColor:"#000000",activeBgOpacity:.08};n.activeBgColor=n.activeBgColor?n.activeBgColor:"#000000",n.activeBgOpacity=n.activeBgOpacity?n.activeBgOpacity:.08;var l=i.padding,s=t.height-i.padding-i.xAxisHeight-i.legendHeight;o.beginPath(),o.setFillStyle(hexToRgb(n.activeBgColor,n.activeBgOpacity)),o.rect(e-a/2,l,a,s-l),o.closePath(),o.fill()}function drawToolTip(e,t,i,o,a){var n=Math.round,l=i.extra.tooltip||{bgColor:"#000000",bgOpacity:.7,fontColor:"#FFFFFF"};l.bgColor=l.bgColor?l.bgColor:"#000000",l.bgOpacity=l.bgOpacity?l.bgOpacity:.7,l.fontColor=l.fontColor?l.fontColor:"#FFFFFF";var s=4*i.pixelRatio,r=5*i.pixelRatio,d=8*i.pixelRatio,h=!1;("line"==i.type||"area"==i.type||"candle"==i.type||"mix"==i.type)&&drawToolTipSplitLine(i.tooltip.offset.x,i,o,a),t=assign({x:0,y:0},t),t.y-=8*i.pixelRatio;var x=e.map(function(e){return measureText(e.text)}),c=s+r+4*o.toolTipPadding+Math.max.apply(null,x),p=2*o.toolTipPadding+e.length*o.toolTipLineHeight;t.x-Math.abs(i._scrollDistance_)+d+c>i.width&&(h=!0),a.beginPath(),a.setFillStyle(hexToRgb(l.bgColor||o.toolTipBackground,l.bgOpacity||o.toolTipOpacity)),h?(a.moveTo(t.x,t.y+10*i.pixelRatio),a.lineTo(t.x-d,t.y+10*i.pixelRatio-5*i.pixelRatio),a.lineTo(t.x-d,t.y),a.lineTo(t.x-d-n(c),t.y),a.lineTo(t.x-d-n(c),t.y+p),a.lineTo(t.x-d,t.y+p),a.lineTo(t.x-d,t.y+10*i.pixelRatio+5*i.pixelRatio),a.lineTo(t.x,t.y+10*i.pixelRatio)):(a.moveTo(t.x,t.y+10*i.pixelRatio),a.lineTo(t.x+d,t.y+10*i.pixelRatio-5*i.pixelRatio),a.lineTo(t.x+d,t.y),a.lineTo(t.x+d+n(c),t.y),a.lineTo(t.x+d+n(c),t.y+p),a.lineTo(t.x+d,t.y+p),a.lineTo(t.x+d,t.y+10*i.pixelRatio+5*i.pixelRatio),a.lineTo(t.x,t.y+10*i.pixelRatio)),a.closePath(),a.fill(),e.forEach(function(e,i){if(null!==e.color){a.beginPath(),a.setFillStyle(e.color);var n=t.x+d+2*o.toolTipPadding,l=t.y+(o.toolTipLineHeight-o.fontSize)/2+o.toolTipLineHeight*i+o.toolTipPadding+1;h&&(n=t.x-c-d+2*o.toolTipPadding),a.fillRect(n,l,s,o.fontSize),a.closePath()}}),e.forEach(function(e,i){var n=t.x+d+2*o.toolTipPadding+s+r;h&&(n=t.x-c-d+2*o.toolTipPadding+ +s+r);var x=t.y+(o.toolTipLineHeight-o.fontSize)/2+o.toolTipLineHeight*i+o.toolTipPadding;a.beginPath(),a.setFontSize(o.fontSize),a.setFillStyle(l.fontColor),a.fillText(e.text,n,x+o.fontSize),a.closePath(),a.stroke()})}function drawYAxisTitle(e,t,i,o){var a=i.xAxisHeight+(t.height-i.xAxisHeight-measureText(e))/2;o.save(),o.beginPath(),o.setFontSize(i.fontSize),o.setFillStyle(t.yAxis.titleFontColor||"#333333"),o.translate(0,t.height),o.rotate(-90*Math.PI/180),o.fillText(e,a,i.padding+.5*i.fontSize),o.closePath(),o.stroke(),o.restore()}function drawColumnDataPoints(e,t,i,o){var a=4<arguments.length&&void 0!==arguments[4]?arguments[4]:1,n=t.extra.column||{type:{},meter:{}};n.type=null==n.type?"group":n.type,n.meter=n.meter||{},n.meter.border=null==n.meter.border?4:n.meter.border,n.meter.fillColor=null==n.meter.fillColor?"#FFFFFF":n.meter.fillColor;var l=calYAxisData(e,t,i),s=l.ranges,r=getXAxisPoints(t.categories,t,i),d=r.xAxisPoints,h=r.eachSpacing,x=s.pop(),c=s.shift(),p=[];return o.save(),t._scrollDistance_&&0!==t._scrollDistance_&&!0===t.enableScroll&&o.translate(t._scrollDistance_,0),t.tooltip&&t.tooltip.textList&&t.tooltip.textList.length&&1===a&&drawToolTipSplitArea(t.tooltip.offset.x,t,i,o,h),e.forEach(function(l,s){var r=l.data;switch(n.type){case"group":var g=getDataPoints(r,x,c,d,h,t,i,a),y=getStackDataPoints(r,x,c,d,h,t,i,s,e,a);p.push(y),g=fixColumeData(g,h,e.length,s,i,t),g.forEach(function(e){if(null!==e){o.beginPath(),o.setFillStyle(e.color||l.color);var a=e.x-e.width/2+1,n=t.height-e.y-i.padding-i.xAxisHeight-i.legendHeight;o.moveTo(a,e.y),o.fillRect(a,e.y,e.width-2,n),o.closePath(),o.fill()}});break;case"stack":var g=getStackDataPoints(r,x,c,d,h,t,i,s,e,a);p.push(g),g=fixColumeStackData(g,h,e.length,s,i,t,e),g.forEach(function(e){if(null!==e){o.beginPath(),o.setFillStyle(e.color||l.color);var a=e.x-e.width/2+1,n=t.height-e.y-i.padding-i.xAxisHeight-i.legendHeight,r=t.height-e.y0-i.padding-i.xAxisHeight-i.legendHeight;0<s&&(n-=r),o.moveTo(a,e.y),o.fillRect(a,e.y,e.width-2,n),o.closePath(),o.fill()}});break;case"meter":var g=getDataPoints(r,x,c,d,h,t,i,a);p.push(g),g=fixColumeMeterData(g,h,e.length,s,i,t,n.meter.border),0==s?g.forEach(function(e){if(null!==e){o.beginPath(),o.setFillStyle(n.meter.fillColor);var a=e.x-e.width/2+1,s=t.height-e.y-i.padding-i.xAxisHeight-i.legendHeight;o.moveTo(a,e.y),o.fillRect(a,e.y,e.width-2,s),o.closePath(),o.fill(),o.beginPath(),o.setStrokeStyle(l.color),o.setLineWidth(n.meter.border*t.pixelRatio),o.moveTo(a+.5*n.meter.border,e.y+s),o.lineTo(a+.5*n.meter.border,e.y+.5*n.meter.border),o.lineTo(a+e.width-n.meter.border,e.y+.5*n.meter.border),o.lineTo(a+e.width-n.meter.border,e.y+s),o.stroke()}}):g.forEach(function(e){if(null!==e){o.beginPath(),o.setFillStyle(e.color||l.color);var a=e.x-e.width/2+1,n=t.height-e.y-i.padding-i.xAxisHeight-i.legendHeight;o.moveTo(a,e.y),o.rect(a,e.y,e.width-2,n),o.closePath(),o.fill()}});}}),!1!==t.dataLabel&&1===a&&e.forEach(function(l,s){var r=l.data;switch(n.type){case"group":var p=getDataPoints(r,x,c,d,h,t,i,a);p=fixColumeData(p,h,e.length,s,i,t),drawPointText(p,l,i,o);break;case"stack":var p=getStackDataPoints(r,x,c,d,h,t,i,s,e,a);drawPointText(p,l,i,o);break;case"meter":var p=getDataPoints(r,x,c,d,h,t,i,a);drawPointText(p,l,i,o);}}),o.restore(),{xAxisPoints:d,calPoints:p,eachSpacing:h}}function drawCandleDataPoints(e,t,i,o,a){var n=5<arguments.length&&void 0!==arguments[5]?arguments[5]:1,l=i.extra.candle||{color:{},average:{}};l.color.upLine=l.color.upLine?l.color.upLine:"#f04864",l.color.upFill=l.color.upFill?l.color.upFill:"#f04864",l.color.downLine=l.color.downLine?l.color.downLine:"#2fc25b",l.color.downFill=l.color.downFill?l.color.downFill:"#2fc25b",l.average.show=!0===l.average.show,l.average.name=l.average.name?l.average.name:[],l.average.day=l.average.day?l.average.day:[],l.average.color=l.average.color?l.average.color:["#1890ff","#2fc25b","#facc14","#f04864","#8543e0","#90ed7d"],i.extra.candle=l;var s=calYAxisData(e,i,o),r=s.ranges,d=getXAxisPoints(i.categories,i,o),h=d.xAxisPoints,x=d.eachSpacing,c=r.pop(),p=r.shift(),g=[];return a.save(),i._scrollDistance_&&0!==i._scrollDistance_&&!0===i.enableScroll&&a.translate(i._scrollDistance_,0),l.average.show&&t.forEach(function(e){var t=e.data,l=getDataPoints(t,c,p,h,x,i,o,n),s=splitPoints(l);s.forEach(function(t){a.beginPath(),a.setStrokeStyle(e.color),a.setLineWidth(1),1===t.length?(a.moveTo(t[0].x,t[0].y),a.arc(t[0].x,t[0].y,1,0,2*Math.PI)):(a.moveTo(t[0].x,t[0].y),t.forEach(function(e,i){if(0<i){var o=createCurveControlPoints(t,i-1);a.bezierCurveTo(o.ctrA.x,o.ctrA.y,o.ctrB.x,o.ctrB.y,e.x,e.y)}}),a.moveTo(t[0].x,t[0].y)),a.closePath(),a.stroke()})}),e.forEach(function(e){var t=e.data,s=getCandleDataPoints(t,c,p,h,x,i,o,n);g.push(s);var r=splitPoints(s);r=r[0],r.forEach(function(e,o){a.beginPath(),0<t[o][1]-t[o][0]?(a.setStrokeStyle(l.color.upLine),a.setFillStyle(l.color.upFill),a.setLineWidth(1*i.pixelRatio),a.moveTo(e[3].x,e[3].y),a.lineTo(e[1].x,e[1].y),a.lineTo(e[1].x-x/4,e[1].y),a.lineTo(e[0].x-x/4,e[0].y),a.lineTo(e[0].x,e[0].y),a.lineTo(e[2].x,e[2].y),a.lineTo(e[0].x,e[0].y),a.lineTo(e[0].x+x/4,e[0].y),a.lineTo(e[1].x+x/4,e[1].y),a.lineTo(e[1].x,e[1].y),a.moveTo(e[3].x,e[3].y)):(a.setStrokeStyle(l.color.downLine),a.setFillStyle(l.color.downFill),a.setLineWidth(1*i.pixelRatio),a.moveTo(e[3].x,e[3].y),a.lineTo(e[0].x,e[0].y),a.lineTo(e[0].x-x/4,e[0].y),a.lineTo(e[1].x-x/4,e[1].y),a.lineTo(e[1].x,e[1].y),a.lineTo(e[2].x,e[2].y),a.lineTo(e[1].x,e[1].y),a.lineTo(e[1].x+x/4,e[1].y),a.lineTo(e[0].x+x/4,e[0].y),a.lineTo(e[0].x,e[0].y),a.moveTo(e[3].x,e[3].y)),a.closePath(),a.fill(),a.stroke()})}),a.restore(),{xAxisPoints:h,calPoints:g,eachSpacing:x}}function drawAreaDataPoints(e,t,i,o){var a=4<arguments.length&&void 0!==arguments[4]?arguments[4]:1,n=t.extra.area||{type:"straight",opacity:.5,addLine:!1,width:2};n.type=n.type?n.type:"straight",n.opacity=n.opacity?n.opacity:.2,n.addLine=!0==n.addLine,n.width=n.width?n.width:2;var l=calYAxisData(e,t,i),s=l.ranges,r=getXAxisPoints(t.categories,t,i),d=r.xAxisPoints,h=r.eachSpacing,x=s.pop(),c=s.shift(),p=t.height-i.padding-i.xAxisHeight-i.legendHeight,g=[];return o.save(),t._scrollDistance_&&0!==t._scrollDistance_&&!0===t.enableScroll&&o.translate(t._scrollDistance_,0),t.tooltip&&t.tooltip.textList&&t.tooltip.textList.length&&1===a&&drawToolTipSplitLine(t.tooltip.offset.x,t,i,o),e.forEach(function(e,l){let s=e.data,r=getDataPoints(s,x,c,d,h,t,i,a);g.push(r);let y=splitPoints(r);for(let a,s=0;s<y.length;s++){if(a=y[s],o.beginPath(),o.setStrokeStyle(hexToRgb(e.color,n.opacity)),o.setFillStyle(hexToRgb(e.color,n.opacity)),o.setLineWidth(n.width*t.pixelRatio),1<a.length){let e=a[0],t=a[a.length-1];o.moveTo(e.x,e.y),"curve"===n.type?a.forEach(function(e,t){if(0<t){let i=createCurveControlPoints(a,t-1);o.bezierCurveTo(i.ctrA.x,i.ctrA.y,i.ctrB.x,i.ctrB.y,e.x,e.y)}}):a.forEach(function(e,t){0<t&&o.lineTo(e.x,e.y)}),o.lineTo(t.x,p),o.lineTo(e.x,p),o.lineTo(e.x,e.y)}else{let e=a[0];o.moveTo(e.x-h/2,e.y),o.lineTo(e.x+h/2,e.y),o.lineTo(e.x+h/2,p),o.lineTo(e.x-h/2,p),o.moveTo(e.x-h/2,e.y)}o.closePath(),o.fill(),n.addLine&&(o.beginPath(),o.setStrokeStyle(e.color),o.setLineWidth(n.width*t.pixelRatio),1===a.length?(o.moveTo(a[0].x,a[0].y),o.arc(a[0].x,a[0].y,1,0,2*Math.PI)):(o.moveTo(a[0].x,a[0].y),"curve"===n.type?a.forEach(function(e,t){if(0<t){let i=createCurveControlPoints(a,t-1);o.bezierCurveTo(i.ctrA.x,i.ctrA.y,i.ctrB.x,i.ctrB.y,e.x,e.y)}}):a.forEach(function(e,t){0<t&&o.lineTo(e.x,e.y)}),o.moveTo(a[0].x,a[0].y)),o.closePath(),o.stroke())}if(!1!==t.dataPointShape){var f=i.dataPointShape[l%i.dataPointShape.length];drawPointShape(r,e.color,f,o,t)}}),!1!==t.dataLabel&&1===a&&e.forEach(function(e){var n=e.data,l=getDataPoints(n,x,c,d,h,t,i,a);drawPointText(l,e,i,o)}),o.restore(),{xAxisPoints:d,calPoints:g,eachSpacing:h}}function drawLineDataPoints(e,t,i,o){var a=4<arguments.length&&void 0!==arguments[4]?arguments[4]:1,n=t.extra.line||{type:"straight",width:2};n.type=n.type?n.type:"straight",n.width=n.width?n.width:2;var l=calYAxisData(e,t,i),s=l.ranges,r=getXAxisPoints(t.categories,t,i),d=r.xAxisPoints,h=r.eachSpacing,x=s.pop(),c=s.shift(),p=[];return o.save(),t._scrollDistance_&&0!==t._scrollDistance_&&!0===t.enableScroll&&o.translate(t._scrollDistance_,0),t.tooltip&&t.tooltip.textList&&t.tooltip.textList.length&&1===a&&drawToolTipSplitLine(t.tooltip.offset.x,t,i,o),e.forEach(function(e,l){var s=e.data,r=getDataPoints(s,x,c,d,h,t,i,a);p.push(r);var g=splitPoints(r);if(g.forEach(function(i){o.beginPath(),o.setStrokeStyle(e.color),o.setLineWidth(n.width*t.pixelRatio),1===i.length?(o.moveTo(i[0].x,i[0].y),o.arc(i[0].x,i[0].y,1,0,2*Math.PI)):(o.moveTo(i[0].x,i[0].y),"curve"===n.type?i.forEach(function(e,t){if(0<t){var a=createCurveControlPoints(i,t-1);o.bezierCurveTo(a.ctrA.x,a.ctrA.y,a.ctrB.x,a.ctrB.y,e.x,e.y)}}):i.forEach(function(e,t){0<t&&o.lineTo(e.x,e.y)}),o.moveTo(i[0].x,i[0].y)),o.closePath(),o.stroke()}),!1!==t.dataPointShape){var y=i.dataPointShape[l%i.dataPointShape.length];drawPointShape(r,e.color,y,o,t)}}),!1!==t.dataLabel&&1===a&&e.forEach(function(e){var n=e.data,l=getDataPoints(n,x,c,d,h,t,i,a);drawPointText(l,e,i,o)}),o.restore(),{xAxisPoints:d,calPoints:p,eachSpacing:h}}function drawMixDataPoints(e,t,i,o){var a=Math.PI,n=4<arguments.length&&void 0!==arguments[4]?arguments[4]:1,l=calYAxisData(e,t,i),s=l.ranges,r=getXAxisPoints(t.categories,t,i),d=r.xAxisPoints,h=r.eachSpacing,x=s.pop(),c=s.shift(),p=t.height-i.padding-i.xAxisHeight-i.legendHeight,g=[],y=0,f=0;if(e.forEach(function(e){"column"==e.type&&(f+=1)}),o.save(),t._scrollDistance_&&0!==t._scrollDistance_&&!0===t.enableScroll&&o.translate(t._scrollDistance_,0),t.tooltip&&t.tooltip.textList&&t.tooltip.textList.length&&1===n&&drawToolTipSplitLine(t.tooltip.offset.x,t,i,o),e.forEach(function(e,l){var s=e.data,r=getDataPoints(s,x,c,d,h,t,i,n);if(g.push(r),"column"==e.type&&(r=fixColumeData(r,h,f,y,i,t),r.forEach(function(a){if(null!==a){o.beginPath(),o.setFillStyle(a.color||e.color);var n=a.x-a.width/2+1,l=t.height-a.y-i.padding-i.xAxisHeight-i.legendHeight;o.moveTo(n,a.y),o.rect(n,a.y,a.width-2,l),o.closePath(),o.fill()}}),y+=1),"area"==e.type){let a=splitPoints(r);for(let n,l=0;l<a.length;l++){if(n=a[l],o.beginPath(),o.setStrokeStyle(e.color),o.setFillStyle(e.color),o.setGlobalAlpha(.2),o.setLineWidth(2*t.pixelRatio),1<n.length){var u=n[0];let t=n[n.length-1];o.moveTo(u.x,u.y),"curve"===e.style?n.forEach(function(e,t){if(0<t){var i=createCurveControlPoints(n,t-1);o.bezierCurveTo(i.ctrA.x,i.ctrA.y,i.ctrB.x,i.ctrB.y,e.x,e.y)}}):n.forEach(function(e,t){0<t&&o.lineTo(e.x,e.y)}),o.lineTo(t.x,p),o.lineTo(u.x,p),o.lineTo(u.x,u.y)}else{let e=n[0];o.moveTo(e.x-h/2,e.y),o.lineTo(e.x+h/2,e.y),o.lineTo(e.x+h/2,p),o.lineTo(e.x-h/2,p),o.moveTo(e.x-h/2,e.y)}o.closePath(),o.fill(),o.setGlobalAlpha(1)}}if("line"==e.type){var m=splitPoints(r);m.forEach(function(i){o.beginPath(),o.setStrokeStyle(e.color),o.setLineWidth(2*t.pixelRatio),1===i.length?(o.moveTo(i[0].x,i[0].y),o.arc(i[0].x,i[0].y,1,0,2*a)):(o.moveTo(i[0].x,i[0].y),"curve"==e.style?i.forEach(function(e,t){if(0<t){var a=createCurveControlPoints(i,t-1);o.bezierCurveTo(a.ctrA.x,a.ctrA.y,a.ctrB.x,a.ctrB.y,e.x,e.y)}}):i.forEach(function(e,t){0<t&&o.lineTo(e.x,e.y)}),o.moveTo(i[0].x,i[0].y)),o.closePath(),o.stroke()})}if("point"==e.type){var m=splitPoints(r);m.forEach(function(i){o.beginPath(),o.setStrokeStyle(e.color),o.setLineWidth(2*t.pixelRatio),o.moveTo(i[0].x,i[0].y),o.arc(i[0].x,i[0].y,1,0,2*a),o.closePath(),o.stroke()})}if(!1!==t.dataPointShape&&"column"!==e.type){var A=i.dataPointShape[l%i.dataPointShape.length];drawPointShape(r,e.color,A,o,t)}}),!1!==t.dataLabel&&1===n){var y=0;e.forEach(function(e){var a=e.data,l=getDataPoints(a,x,c,d,h,t,i,n);"column"===e.type?(l=fixColumeData(l,h,f,y,i,t),drawPointText(l,e,i,o),y+=1):drawPointText(l,e,i,o)})}return o.restore(),{xAxisPoints:d,calPoints:g,eachSpacing:h}}function drawToolTipBridge(e,t,i,o,a,n){var l=e.extra.tooltip||{};l.horizentalLine&&e.tooltip&&1===o&&("line"==e.type||"area"==e.type||"column"==e.type||"candle"==e.type||"mix"==e.type)&&drawToolTipHorizentalLine(e,t,i,a,n),i.save(),e._scrollDistance_&&0!==e._scrollDistance_&&!0===e.enableScroll&&i.translate(e._scrollDistance_,0),e.tooltip&&e.tooltip.textList&&e.tooltip.textList.length&&1===o&&drawToolTip(e.tooltip.textList,e.tooltip.offset,e,t,i,a,n),i.restore()}function drawXAxis(e,t,i,o){var a=Math.ceil,n=getXAxisPoints(e,t,i),l=n.xAxisPoints,s=n.startX,r=n.endX,d=n.eachSpacing,h=t.height-i.padding-i.xAxisHeight-i.legendHeight,x=i.padding;if(t.enableScroll&&t.xAxis.scrollShow){var c=t.height-i.padding-i.legendHeight+6*t.pixelRatio,p=r-s,g=d*(l.length-1),y=0;t._scrollDistance_&&(y=-t._scrollDistance_*p/g),o.beginPath(),o.setLineCap("round"),o.setLineWidth(6*t.pixelRatio),o.setStrokeStyle(t.xAxis.scrollBackgroundColor||"#EFEBEF"),o.moveTo(s,c),o.lineTo(r,c),o.stroke(),o.closePath(),o.beginPath(),o.setLineCap("round"),o.setLineWidth(6*t.pixelRatio),o.setStrokeStyle(t.xAxis.scrollColor||"#A6A6A6"),o.moveTo(s+y,c),o.lineTo(s+y+p*p/g,c),o.stroke(),o.setLineCap("butt"),o.closePath()}if(o.save(),t._scrollDistance_&&0!==t._scrollDistance_&&o.translate(t._scrollDistance_,0),o.beginPath(),o.setStrokeStyle(t.xAxis.gridColor||"#cccccc"),o.setLineCap("butt"),o.setLineWidth(1*t.pixelRatio),"dash"==t.xAxis.gridType&&o.setLineDash([t.xAxis.dashLength,t.xAxis.dashLength]),!0!==t.xAxis.disableGrid&&("calibration"===t.xAxis.type?l.forEach(function(e,i){0<i&&(o.moveTo(e-d/2,h),o.lineTo(e-d/2,h+4*t.pixelRatio))}):l.forEach(function(e){o.moveTo(e,h),o.lineTo(e,x)})),o.closePath(),o.stroke(),o.setLineDash([]),!0!==t.xAxis.disabled){let n=t.width-2*i.padding-i.yAxisWidth-i.yAxisTitleWidth,s=e.length;0===i._xAxisTextAngle_?t.xAxis.labelCount&&(s=t.xAxis.itemCount?a(e.length/t.xAxis.itemCount*t.xAxis.labelCount):t.xAxis.labelCount,s-=1):s=Math.min(e.length,a(n/i.fontSize/1.5));let r=a(e.length/s),x=[],c=e.length;for(let t=0;t<c;t++)0==t%r?x.push(e[t]):x.push("");x[c-1]=e[c-1],0===i._xAxisTextAngle_?x.forEach(function(e,a){var n=d/2-measureText(e)/2;o.beginPath(),o.setFontSize(i.fontSize),o.setFillStyle(t.xAxis.fontColor||"#666666"),o.fillText(e,l[a]+n,h+i.fontSize+5),o.closePath(),o.stroke()}):x.forEach(function(e,a){o.save(),o.beginPath(),o.setFontSize(i.fontSize),o.setFillStyle(t.xAxis.fontColor||"#666666");var n=measureText(e),s=calRotateTranslate(l[a]+d/2,h+i.fontSize/2+5,t.height),r=s.transX,x=s.transY;o.rotate(-1*i._xAxisTextAngle_),o.translate(r,x),o.fillText(e,l[a]+(d/2-n),h+i.fontSize+5),o.closePath(),o.stroke(),o.restore()})}o.restore()}function drawYAxisGrid(e,t,o,a){if(!0!==t.yAxis.disableGrid){for(var n=t.height-2*o.padding-o.xAxisHeight-o.legendHeight,l=Math.floor(n/o.yAxisSplit),s=o.yAxisWidth+o.yAxisTitleWidth,r=o.padding+s,d=getXAxisPoints(e,t,o),h=d.xAxisPoints,x=d.eachSpacing,c=x*(h.length-1),p=r+c,g=[],y=0;y<o.yAxisSplit;y++)g.push(o.padding+l*y);g.push(o.padding+l*o.yAxisSplit+2),a.save(),t._scrollDistance_&&0!==t._scrollDistance_&&a.translate(t._scrollDistance_,0),"dash"==t.yAxis.gridType&&a.setLineDash([t.yAxis.dashLength,t.yAxis.dashLength]),a.beginPath(),a.setStrokeStyle(t.yAxis.gridColor||"#cccccc"),a.setLineWidth(1*t.pixelRatio),g.forEach(function(e){a.moveTo(r,e),a.lineTo(p,e)}),a.closePath(),a.stroke(),a.setLineDash([]),a.restore()}}function drawYAxis(e,t,o,a){if(!0!==t.yAxis.disabled){var n=calYAxisData(e,t,o),l=n.rangesFormat,s=o.yAxisWidth+o.yAxisTitleWidth,r=t.height-2*o.padding-o.xAxisHeight-o.legendHeight,d=Math.floor(r/o.yAxisSplit),h=o.padding+s,x=t.width-o.padding,c=t.height-o.padding-o.xAxisHeight-o.legendHeight+o.xAxisTextPadding;a.beginPath(),a.setFillStyle(t.background||"#ffffff"),0>t._scrollDistance_&&a.fillRect(0,0,h,c+o.xAxisHeight),a.fillRect(x,0,t.width,c+o.xAxisHeight),a.closePath(),a.stroke();for(var p=[],g=0;g<=o.yAxisSplit;g++)p.push(o.padding+d*g);l.forEach(function(e,i){var n=p[i]?p[i]:c;a.beginPath(),a.setFontSize(o.fontSize),a.setFillStyle(t.yAxis.fontColor||"#666666"),a.fillText(e,o.padding+o.yAxisTitleWidth,n+o.fontSize/2),a.closePath(),a.stroke()}),t.yAxis.title&&drawYAxisTitle(t.yAxis.title,t,o,a)}}function drawLegend(e,t,o,a){var n=Math.PI;if(!1!==t.legend){var i=calLegendData(e,t,o),l=i.legendList,s=5*t.pixelRatio,r=10*t.pixelRatio,d=15*t.pixelRatio;l.forEach(function(e,i){var l=0;for(let t,o=0;o<e.length;o++)t=e[o],t.name=t.name||"undefined",l+=3*s+measureText(t.name)+d;var h=(t.width-l)/2+s,x=t.height-o.padding-o.legendHeight+i*(o.fontSize+r)+s+r;a.setFontSize(o.fontSize);for(let l,r=0;r<e.length;r++){switch(l=e[r],t.type){case"line":a.beginPath(),a.setLineWidth(1*t.pixelRatio),a.setStrokeStyle(l.color),a.setFillStyle(l.color),a.moveTo(h+7.5*t.pixelRatio,x+5*t.pixelRatio),a.arc(h+7.5*t.pixelRatio,x+5*t.pixelRatio,6*t.pixelRatio,0,2*n),a.closePath(),a.fill(),a.stroke();break;case"pie":a.beginPath(),a.setLineWidth(1*t.pixelRatio),a.setStrokeStyle(l.color),a.setFillStyle(l.color),a.moveTo(h+7.5*t.pixelRatio,x+5*t.pixelRatio),a.arc(h+7.5*t.pixelRatio,x+5*t.pixelRatio,6*t.pixelRatio,0,2*n),a.closePath(),a.fill(),a.stroke();break;case"ring":case"rose":a.beginPath(),a.setLineWidth(1*t.pixelRatio),a.setStrokeStyle(l.color),a.setFillStyle(l.color),a.moveTo(h+7.5*t.pixelRatio,x+5*t.pixelRatio),a.arc(h+7.5*t.pixelRatio,x+5*t.pixelRatio,6*t.pixelRatio,0,2*n),a.closePath(),a.fill(),a.stroke();break;case"gauge":break;case"arcbar":break;default:a.beginPath(),a.setLineWidth(1*t.pixelRatio),a.setStrokeStyle(l.color),a.setFillStyle(l.color),a.moveTo(h,x),a.fillRect(h,x,15*t.pixelRatio,10*t.pixelRatio),a.closePath(),a.fill(),a.stroke();}h+=s+d,a.beginPath(),a.setFontSize(o.fontSize),a.setFillStyle(t.extra.legendTextColor||"#333333"),a.fillText(l.name,h,x+6*t.pixelRatio+3*t.pixelRatio),a.closePath(),a.stroke(),h+=measureText(l.name)+2*s}})}}function drawPieDataPoints(e,t,o,a){var n=Math.PI,l=4<arguments.length&&void 0!==arguments[4]?arguments[4]:1,s=t.extra.pie||{},r={x:t.width/2,y:(t.height-o.legendHeight)/2},d=Math.min(r.x-o.pieChartLinePadding-o.pieChartTextPadding-o._pieTextMaxLength_,r.y-o.pieChartLinePadding-o.pieChartTextPadding);d-=t.dataLabel?10:2*o.padding,e=getPieDataPoints(e,d,l);var h=o.pieChartLinePadding/2;if(e=e.map(function(e){return e._start_+=(s.offsetAngle||0)*n/180,e}),e.forEach(function(e,i){t.tooltip&&t.tooltip.index==i&&(a.beginPath(),a.setFillStyle(hexToRgb(e.color,t.extra.pie.activeOpacity||.5)),a.moveTo(r.x,r.y),a.arc(r.x,r.y,e._radius_+h,e._start_,e._start_+2*e._proportion_*n),a.closePath(),a.fill()),a.beginPath(),a.setLineWidth(2*t.pixelRatio),a.lineJoin="round",a.setStrokeStyle("#ffffff"),a.setFillStyle(e.color),a.moveTo(r.x,r.y),a.arc(r.x,r.y,e._radius_,e._start_,e._start_+2*e._proportion_*n),a.closePath(),a.fill(),!0!==t.disablePieStroke&&a.stroke()}),"ring"===t.type){var x=.6*d;"number"==typeof t.extra.pie.ringWidth&&0<t.extra.pie.ringWidth&&(x=Math.max(0,d-t.extra.pie.ringWidth)),a.beginPath(),a.setFillStyle(t.background||"#ffffff"),a.moveTo(r.x,r.y),a.arc(r.x,r.y,x,0,2*n),a.closePath(),a.fill()}if(!1!==t.dataLabel&&1===l){for(var c=!1,p=0,g=e.length;p<g;p++)if(0<e[p].data){c=!0;break}c&&drawPieText(e,t,o,a,d,r)}return 1===l&&"ring"===t.type&&drawRingTitle(t,o,a),{center:r,radius:d,series:e}}function drawRoseDataPoints(e,t,o,a){var n=Math.PI,l=4<arguments.length&&void 0!==arguments[4]?arguments[4]:1,s=t.extra.rose||{};s.type=s.type||"area";var r={x:t.width/2,y:(t.height-o.legendHeight)/2},d=Math.min(r.x-o.pieChartLinePadding-o.pieChartTextPadding-o._pieTextMaxLength_,r.y-o.pieChartLinePadding-o.pieChartTextPadding);d-=t.dataLabel?10:2*o.padding;var h=s.minRadius||.5*d;e=getRoseDataPoints(e,s.type,h,d,l);var x=o.pieChartLinePadding/2;if(e=e.map(function(e){return e._start_+=(s.offsetAngle||0)*n/180,e}),e.forEach(function(e,i){t.tooltip&&t.tooltip.index==i&&(a.beginPath(),a.setFillStyle(hexToRgb(e.color,s.activeOpacity||.5)),a.moveTo(r.x,r.y),a.arc(r.x,r.y,x+e._radius_,e._start_,e._start_+2*e._proportion_*n),a.closePath(),a.fill()),a.beginPath(),a.setLineWidth(2*t.pixelRatio),a.lineJoin="round",a.setStrokeStyle("#ffffff"),a.setFillStyle(e.color),a.moveTo(r.x,r.y),a.arc(r.x,r.y,e._radius_,e._start_,e._start_+2*e._proportion_*n),a.closePath(),a.fill(),!0!==t.disablePieStroke&&a.stroke()}),!1!==t.dataLabel&&1===l){for(var c=!1,p=0,g=e.length;p<g;p++)if(0<e[p].data){c=!0;break}c&&drawPieText(e,t,o,a,d,r)}return{center:r,radius:d,series:e}}function drawArcbarDataPoints(e,t,i,o){var a=Math.PI,n=4<arguments.length&&void 0!==arguments[4]?arguments[4]:1,l=t.extra.arcbar||{};l.startAngle=l.startAngle?l.startAngle:.75,l.endAngle=l.endAngle?l.endAngle:.25,l.type=l.type?l.type:"default",e=getArcbarDataPoints(e,l,n);var s={x:t.width/2,y:t.height/2},r=Math.min(s.x,s.y);l.width="number"==typeof l.width&&0<l.width?l.width:12*t.pixelRatio,r-=i.padding+l.width/2,o.setLineWidth(l.width),o.setStrokeStyle(l.backgroundColor||"#E9E9E9"),o.setLineCap("round"),o.beginPath(),"default"==l.type?o.arc(s.x,s.y,r,l.startAngle*a,l.endAngle*a,!1):o.arc(s.x,s.y,r,0,2*a,!1),o.stroke();for(let n,d=0;d<e.length;d++)n=e[d],o.setLineWidth(l.width),o.setStrokeStyle(n.color),o.setLineCap("round"),o.beginPath(),o.arc(s.x,s.y,r,l.startAngle*a,n._proportion_*a,!1),o.stroke();return drawRingTitle(t,i,o),{center:s,radius:r,series:e}}function drawGaugeDataPoints(e,t,o,i,a){var n=Math.PI,l=5<arguments.length&&void 0!==arguments[5]?arguments[5]:1,s=o.extra.gauge||{};s.startAngle=s.startAngle?s.startAngle:.75,null==s.oldAngle&&(s.oldAngle=s.startAngle),null==s.oldData&&(s.oldData=0),s.endAngle=s.endAngle?s.endAngle:.25,e=getGaugeAxisPoints(e,s.startAngle,s.endAngle);var r={x:o.width/2,y:o.height/2},d=Math.min(r.x,r.y);s.width="number"==typeof s.width&&0<s.width?s.width:15*o.pixelRatio,d-=i.padding+s.width/2;var h=d-s.width;a.setLineWidth(s.width),a.setLineCap("butt");for(let l,s=0;s<e.length;s++)l=e[s],a.beginPath(),a.setStrokeStyle(l.color),a.arc(r.x,r.y,d,l._startAngle_*n,l._endAngle_*n,!1),a.stroke();a.save();let x=s.startAngle-s.endAngle+1;s.splitLine.fixRadius=s.splitLine.fixRadius?s.splitLine.fixRadius:0,s.splitLine.splitNumber=s.splitLine.splitNumber?s.splitLine.splitNumber:10,s.splitLine.width=s.splitLine.width?s.splitLine.width:15*o.pixelRatio,s.splitLine.color=s.splitLine.color?s.splitLine.color:"#FFFFFF",s.splitLine.childNumber=s.splitLine.childNumber?s.splitLine.childNumber:5,s.splitLine.childWidth=s.splitLine.childWidth?s.splitLine.childWidth:5*o.pixelRatio;let c=x/s.splitLine.splitNumber,p=x/s.splitLine.splitNumber/s.splitLine.childNumber,g=-d-.5*s.width-s.splitLine.fixRadius,y=-d-.5*s.width-s.splitLine.fixRadius+s.splitLine.width,f=-d-.5*s.width-s.splitLine.fixRadius+s.splitLine.childWidth;a.translate(r.x,r.y),a.rotate((s.startAngle-1)*n);for(let l=0;l<s.splitLine.splitNumber+1;l++)a.beginPath(),a.setStrokeStyle(s.splitLine.color),a.setLineWidth(2*o.pixelRatio),a.moveTo(g,0),a.lineTo(y,0),a.stroke(),a.rotate(c*n);a.restore(),a.save(),a.translate(r.x,r.y),a.rotate((s.startAngle-1)*n);for(let l=0;l<s.splitLine.splitNumber*s.splitLine.childNumber+1;l++)a.beginPath(),a.setStrokeStyle(s.splitLine.color),a.setLineWidth(1*o.pixelRatio),a.moveTo(g,0),a.lineTo(f,0),a.stroke(),a.rotate(p*n);a.restore(),s.pointer.width=s.pointer.width?s.pointer.width:15*o.pixelRatio,null==s.pointer.color||"auto"==s.pointer.color?"auto"==s.pointer.color:s.pointer.color==s.pointer.color,t=getGaugeDataPoints(t,e,s,l);for(let l,d=0;d<t.length;d++)l=t[d],a.save(),a.translate(r.x,r.y),a.rotate((l._proportion_-1)*n),a.beginPath(),a.setFillStyle(l.color),a.moveTo(s.pointer.width,0),a.lineTo(0,-s.pointer.width/2),a.lineTo(-h,0),a.lineTo(0,s.pointer.width/2),a.lineTo(s.pointer.width,0),a.closePath(),a.fill(),a.beginPath(),a.setFillStyle("#FFFFFF"),a.arc(0,0,s.pointer.width/6,0,2*n,!1),a.fill(),a.restore();return!1!==o.dataLabel&&drawGaugeLabel(s,d,r,o,i,a),drawRingTitle(o,i,a),1===l&&"gauge"===o.type&&(s.oldAngle=t[0]._proportion_,s.oldData=t[0].data),{center:r,radius:d,innerRadius:h,categories:e,totalAngle:x}}function drawRadarDataPoints(e,t,o,a){var n=Math.cos,l=Math.sin,s=4<arguments.length&&void 0!==arguments[4]?arguments[4]:1,r=t.extra.radar||{},d=getRadarCoordinateSeries(t.categories.length),h={x:t.width/2,y:(t.height-o.legendHeight)/2},x=Math.min(h.x-(getMaxTextListLength(t.categories)+o.radarLabelTextMargin),h.y-o.radarLabelTextMargin);x-=o.padding,a.beginPath(),a.setLineWidth(1*t.pixelRatio),a.setStrokeStyle(r.gridColor||"#cccccc"),d.forEach(function(e){var t=convertCoordinateOrigin(x*n(e),x*l(e),h);a.moveTo(h.x,h.y),a.lineTo(t.x,t.y)}),a.stroke(),a.closePath();for(var c=function(e){var i={};a.beginPath(),a.setLineWidth(1*t.pixelRatio),a.setStrokeStyle(r.gridColor||"#cccccc"),d.forEach(function(t,s){var r=convertCoordinateOrigin(x/o.radarGridCount*e*n(t),x/o.radarGridCount*e*l(t),h);0===s?(i=r,a.moveTo(r.x,r.y)):a.lineTo(r.x,r.y)}),a.lineTo(i.x,i.y),a.stroke(),a.closePath()},p=1;p<=o.radarGridCount;p++)c(p);var g=getRadarDataPoints(d,h,x,e,t,s);return g.forEach(function(e,i){if(a.beginPath(),a.setFillStyle(e.color),a.setGlobalAlpha(.3),e.data.forEach(function(e,t){0===t?a.moveTo(e.position.x,e.position.y):a.lineTo(e.position.x,e.position.y)}),a.closePath(),a.fill(),a.setGlobalAlpha(1),!1!==t.dataPointShape){var n=o.dataPointShape[i%o.dataPointShape.length],l=e.data.map(function(e){return e.position});drawPointShape(l,e.color,n,a,t)}}),drawRadarLabel(d,x,h,t,o,a),{center:h,radius:x,angleList:d}}function drawCanvas(e,t){t.draw()}var Timing={easeIn:function(e){return Math.pow(e,3)},easeOut:function(e){return Math.pow(e-1,3)+1},easeInOut:function(e){var t=Math.pow;return 1>(e/=.5)?.5*t(e,3):.5*(t(e-2,3)+2)},linear:function(e){return e}};function Animation(e){this.isStop=!1,e.duration="undefined"==typeof e.duration?1e3:e.duration,e.timing=e.timing||"linear";var t=function(){return"undefined"==typeof requestAnimationFrame?"undefined"==typeof setTimeout?function(e){e(null)}:function(e,t){setTimeout(function(){var t=+new Date;e(t)},t)}:requestAnimationFrame}(),i=null,o=function(a){if(null===a||!0===this.isStop)return e.onProcess&&e.onProcess(1),void(e.onAnimationFinish&&e.onAnimationFinish());if(null===i&&(i=a),a-i<e.duration){var n=(a-i)/e.duration,l=Timing[e.timing];n=l(n),e.onProcess&&e.onProcess(n),t(o,17)}else e.onProcess&&e.onProcess(1),e.onAnimationFinish&&e.onAnimationFinish()};o=o.bind(this),t(o,17)}Animation.prototype.stop=function(){this.isStop=!0};function drawCharts(e,t,i,o){var a=this,n=t.series,l=t.categories;n=fillSeriesColor(n,i),n=fillSeriesType(n,t);let s=null;if("candle"==e){let e=assign({},t.extra.candle.average);e.show&&(s=calCandleMA(e.day,e.name,e.color,n[0].data),t.seriesMA=s)}var r=calLegendData(n,t,i),d=r.legendHeight;i.legendHeight=d;var h=calYAxisData(n,t,i),x=h.yAxisWidth;if(i.yAxisWidth=x,l&&l.length){var c=calCategoriesData(l,t,i),p=c.xAxisHeight,g=c.angle;i.xAxisHeight=p,i._xAxisTextAngle_=g}("pie"===e||"ring"===e||"rose"===e)&&(i._pieTextMaxLength_=!1===t.dataLabel?0:getPieTextMaxLength(n));var y=t.animation?1e3:0;this.animationInstance&&this.animationInstance.stop(),o.clearRect(0,0,t.width,t.height),"line"===e?this.animationInstance=new Animation({timing:"easeIn",duration:y,onProcess:function(e){t.rotate&&contextRotate(o,t),drawYAxisGrid(l,t,i,o),drawXAxis(l,t,i,o);var s=drawLineDataPoints(n,t,i,o,e),r=s.xAxisPoints,d=s.calPoints,h=s.eachSpacing;a.chartData.xAxisPoints=r,a.chartData.calPoints=d,a.chartData.eachSpacing=h,drawLegend(t.series,t,i,o),drawYAxis(n,t,i,o),drawToolTipBridge(t,i,o,e,h,r),drawCanvas(t,o)},onAnimationFinish:function(){a.event.trigger("renderComplete")}}):"mix"===e?this.animationInstance=new Animation({timing:"easeIn",duration:y,onProcess:function(e){t.rotate&&contextRotate(o,t),drawYAxisGrid(l,t,i,o),drawXAxis(l,t,i,o);var s=drawMixDataPoints(n,t,i,o,e),r=s.xAxisPoints,d=s.calPoints,h=s.eachSpacing;a.chartData.xAxisPoints=r,a.chartData.calPoints=d,a.chartData.eachSpacing=h,drawLegend(t.series,t,i,o),drawYAxis(n,t,i,o),drawToolTipBridge(t,i,o,e,h,r),drawCanvas(t,o)},onAnimationFinish:function(){a.event.trigger("renderComplete")}}):"column"===e?this.animationInstance=new Animation({timing:"easeIn",duration:y,onProcess:function(e){t.rotate&&contextRotate(o,t),drawYAxisGrid(l,t,i,o),drawXAxis(l,t,i,o);var s=drawColumnDataPoints(n,t,i,o,e),r=s.xAxisPoints,d=s.calPoints,h=s.eachSpacing;a.chartData.xAxisPoints=r,a.chartData.calPoints=d,a.chartData.eachSpacing=h,drawLegend(t.series,t,i,o),drawYAxis(n,t,i,o),drawToolTipBridge(t,i,o,e,h,r),drawCanvas(t,o)},onAnimationFinish:function(){a.event.trigger("renderComplete")}}):"area"===e?this.animationInstance=new Animation({timing:"easeIn",duration:y,onProcess:function(e){t.rotate&&contextRotate(o,t),drawYAxisGrid(l,t,i,o),drawXAxis(l,t,i,o);var s=drawAreaDataPoints(n,t,i,o,e),r=s.xAxisPoints,d=s.calPoints,h=s.eachSpacing;a.chartData.xAxisPoints=r,a.chartData.calPoints=d,a.chartData.eachSpacing=h,drawLegend(t.series,t,i,o),drawYAxis(n,t,i,o),drawToolTipBridge(t,i,o,e,h,r),drawCanvas(t,o)},onAnimationFinish:function(){a.event.trigger("renderComplete")}}):"ring"===e||"pie"===e?this.animationInstance=new Animation({timing:"easeInOut",duration:y,onProcess:function(e){t.rotate&&contextRotate(o,t),a.chartData.pieData=drawPieDataPoints(n,t,i,o,e),drawLegend(t.series,t,i,o),drawToolTipBridge(t,i,o,e),drawCanvas(t,o)},onAnimationFinish:function(){a.event.trigger("renderComplete")}}):"rose"===e?this.animationInstance=new Animation({timing:"easeInOut",duration:y,onProcess:function(e){t.rotate&&contextRotate(o,t),a.chartData.pieData=drawRoseDataPoints(n,t,i,o,e),drawLegend(t.series,t,i,o),drawToolTipBridge(t,i,o,e),drawCanvas(t,o)},onAnimationFinish:function(){a.event.trigger("renderComplete")}}):"radar"===e?this.animationInstance=new Animation({timing:"easeInOut",duration:y,onProcess:function(e){t.rotate&&contextRotate(o,t),a.chartData.radarData=drawRadarDataPoints(n,t,i,o,e),drawLegend(t.series,t,i,o),drawToolTipBridge(t,i,o,e),drawCanvas(t,o)},onAnimationFinish:function(){a.event.trigger("renderComplete")}}):"arcbar"===e?this.animationInstance=new Animation({timing:"easeInOut",duration:y,onProcess:function(e){t.rotate&&contextRotate(o,t),a.chartData.arcbarData=drawArcbarDataPoints(n,t,i,o,e),drawCanvas(t,o)},onAnimationFinish:function(){a.event.trigger("renderComplete")}}):"gauge"===e?this.animationInstance=new Animation({timing:"easeInOut",duration:y,onProcess:function(e){t.rotate&&contextRotate(o,t),a.chartData.gaugeData=drawGaugeDataPoints(l,n,t,i,o,e),drawCanvas(t,o)},onAnimationFinish:function(){a.event.trigger("renderComplete")}}):"candle"===e?this.animationInstance=new Animation({timing:"easeIn",duration:y,onProcess:function(e){t.rotate&&contextRotate(o,t),drawYAxisGrid(l,t,i,o),drawXAxis(l,t,i,o);var r=drawCandleDataPoints(n,s,t,i,o,e),d=r.xAxisPoints,h=r.calPoints,x=r.eachSpacing;a.chartData.xAxisPoints=d,a.chartData.calPoints=h,a.chartData.eachSpacing=x,s?drawLegend(s,t,i,o):drawLegend(t.series,t,i,o),drawYAxis(n,t,i,o),drawToolTipBridge(t,i,o,e,x,d),drawCanvas(t,o)},onAnimationFinish:function(){a.event.trigger("renderComplete")}}):void 0}function Event(){this.events={}}Event.prototype.addEventListener=function(e,t){this.events[e]=this.events[e]||[],this.events[e].push(t)},Event.prototype.trigger=function(){for(var e=arguments.length,t=Array(e),i=0;i<e;i++)t[i]=arguments[i];var o=t[0],a=t.slice(1);!this.events[o]||this.events[o].forEach(function(e){try{e.apply(null,a)}catch(t){console.error(t)}})};var Charts=function(e){e.fontSize=e.fontSize?e.fontSize*e.pixelRatio:13*e.pixelRatio,e.title=e.title||{},e.subtitle=e.subtitle||{},e.yAxis=e.yAxis||{},e.yAxis.gridType=e.yAxis.gridType?e.yAxis.gridType:"solid",e.yAxis.dashLength=e.yAxis.dashLength?e.yAxis.dashLength:4*e.pixelRatio,e.xAxis=e.xAxis||{},e.xAxis.rotateLabel=!!e.xAxis.rotateLabel,e.xAxis.type=e.xAxis.type?e.xAxis.type:"calibration",e.xAxis.gridType=e.xAxis.gridType?e.xAxis.gridType:"solid",e.xAxis.dashLength=e.xAxis.dashLength?e.xAxis.dashLength:4*e.pixelRatio,e.xAxis.scrollAlign=e.xAxis.scrollAlign?e.xAxis.scrollAlign:"left",e.extra=e.extra||{},e.legend=!1!==e.legend,e.rotate=!!e.rotate,e.animation=!1!==e.animation;var t=assign({},config);if(t.yAxisTitleWidth=!0!==e.yAxis.disabled&&e.yAxis.title?t.yAxisTitleWidth:0,("pie"==e.type||"ring"==e.type)&&(t.pieChartLinePadding=!1===e.dataLabel?0:e.extra.pie.lableWidth*e.pixelRatio||t.pieChartLinePadding*e.pixelRatio),t.pieChartTextPadding=!1===e.dataLabel?0:t.pieChartTextPadding*e.pixelRatio,t.yAxisSplit=e.yAxis.splitNumber?e.yAxis.splitNumber:config.yAxisSplit,t.rotate=e.rotate,e.rotate){let t=e.width,i=e.height;e.width=i,e.height=t}if(t.yAxisWidth=config.yAxisWidth*e.pixelRatio,t.xAxisHeight=config.xAxisHeight*e.pixelRatio,e.enableScroll&&e.xAxis.scrollShow&&(t.xAxisHeight+=6*e.pixelRatio),t.xAxisLineHeight=config.xAxisLineHeight*e.pixelRatio,t.legendHeight=config.legendHeight*e.pixelRatio,t.padding=config.padding*e.pixelRatio,t.fontSize=e.fontSize,t.titleFontSize=config.titleFontSize*e.pixelRatio,t.subtitleFontSize=config.subtitleFontSize*e.pixelRatio,t.toolTipPadding=config.toolTipPadding*e.pixelRatio,t.toolTipLineHeight=config.toolTipLineHeight*e.pixelRatio,t.columePadding=config.columePadding*e.pixelRatio,config.pixelRatio=e.pixelRatio,config.fontSize=e.fontSize,config.rotate=e.rotate,this.opts=e,this.config=t,e.$this=e.$this?e.$this:this,this.context=uni.createCanvasContext(e.canvasId,e.$this),this.chartData={},this.event=new Event,this.scrollOption={currentOffset:0,startTouchX:0,distance:0,lastMoveTime:0},e.enableScroll&&"right"==e.xAxis.scrollAlign){let i=calYAxisData(e.series,e,t),o=i.yAxisWidth;t.yAxisWidth=o;let a=0,n=getXAxisPoints(e.categories,e,t),l=n.xAxisPoints,s=n.startX,r=n.endX,d=n.eachSpacing,h=d*(l.length-1);a=r-s-h,this.scrollOption={currentOffset:a,startTouchX:a,distance:0,lastMoveTime:0},e._scrollDistance_=a}drawCharts.call(this,e.type,e,t,this.context)};Charts.prototype.updateData=function(){let e=0<arguments.length&&arguments[0]!==void 0?arguments[0]:{};this.opts.series=e.series||this.opts.series,this.opts.categories=e.categories||this.opts.categories;this.opts.type=e.type||this.opts.type;this.opts.extra=e.extra||this.opts.extra;this.opts.yAxis.format=e.unit?((v)=>{return v.toFixed(e.fixed||0)+(e.unit||'')}):this.opts.yAxis.format;let t=e.scrollPosition||"current";switch(t){case"current":this.opts._scrollDistance_=this.scrollOption.currentOffset;break;case"left":this.opts._scrollDistance_=0,this.scrollOption={currentOffset:0,startTouchX:0,distance:0,lastMoveTime:0};break;case"right":let e=calYAxisData(this.opts.series,this.opts,this.config),i=e.yAxisWidth;this.config.yAxisWidth=i;let o=0,a=getXAxisPoints(this.opts.categories,this.opts,this.config),n=a.xAxisPoints,l=a.startX,s=a.endX,r=a.eachSpacing,d=r*(n.length-1);o=s-l-d,this.scrollOption={currentOffset:o,startTouchX:o,distance:0,lastMoveTime:0},this.opts._scrollDistance_=o;}let i=e.animation==null?this.opts.animation:e.animation;this.opts.animation=i,this.opts.title=assign({},this.opts.title,e.title||{}),this.opts.subtitle=assign({},this.opts.subtitle,e.subtitle||{}),drawCharts.call(this,this.opts.type,this.opts,this.config,this.context)},Charts.prototype.zoom=function(){var e=Math.round,t=0<arguments.length&&void 0!==arguments[0]?arguments[0]:this.opts.xAxis.itemCount;if(!0!==this.opts.enableScroll)return void console.log("\u8BF7\u542F\u7528\u6EDA\u52A8\u6761\u540E\u4F7F\u7528\uFF01");let i=e(Math.abs(this.scrollOption.currentOffset)/this.chartData.eachSpacing)+e(this.opts.xAxis.itemCount/2);this.opts.animation=!1,this.opts.xAxis.itemCount=t.itemCount;let o=calYAxisData(this.opts.series,this.opts,this.config),a=o.yAxisWidth;this.config.yAxisWidth=a;let n=0,l=getXAxisPoints(this.opts.categories,this.opts,this.config),s=l.xAxisPoints,r=l.startX,d=l.endX,h=l.eachSpacing,x=d-r,c=x-h*(s.length-1);n=x/2-h*i,0<n&&(n=0),n<c&&(n=c),this.scrollOption={currentOffset:n,startTouchX:n,distance:0,lastMoveTime:0},this.opts._scrollDistance_=n,drawCharts.call(this,this.opts.type,this.opts,this.config,this.context)},Charts.prototype.stopAnimation=function(){this.animationInstance&&this.animationInstance.stop()},Charts.prototype.addEventListener=function(e,t){this.event.addEventListener(e,t)},Charts.prototype.getCurrentDataIndex=function(t){var e=t.mp.changedTouches[0]||t.changedTouches[0];if(e){var i=getTouches(e,this.opts,t);return"pie"===this.opts.type||"ring"===this.opts.type||"rose"===this.opts.type?findPieChartCurrentIndex({x:i.x,y:i.y},this.chartData.pieData):"radar"===this.opts.type?findRadarChartCurrentIndex({x:i.x,y:i.y},this.chartData.radarData,this.opts.categories.length):findCurrentIndex({x:i.x,y:i.y},this.chartData.xAxisPoints,this.opts,this.config,Math.abs(this.scrollOption.currentOffset))}return-1},Charts.prototype.showToolTip=function(t){var e=1<arguments.length&&arguments[1]!==void 0?arguments[1]:{},i=t.mp.changedTouches[0]||t.changedTouches[0],o=getTouches(i,this.opts,t);if("line"===this.opts.type||"area"===this.opts.type||"column"===this.opts.type){var a=this.getCurrentDataIndex(t),n=this.scrollOption.currentOffset,l=assign({},this.opts,{_scrollDistance_:n,animation:!1});if(-1<a){var s=getSeriesDataItem(this.opts.series,a);if(0!==s.length){var r=getToolTipData(s,this.chartData.calPoints,a,this.opts.categories,e),d=r.textList,h=r.offset;h.y=o.y,l.tooltip={textList:d,offset:h,option:e,index:a}}}drawCharts.call(this,l.type,l,this.config,this.context)}if("mix"===this.opts.type){var a=this.getCurrentDataIndex(t),n=this.scrollOption.currentOffset,l=assign({},this.opts,{_scrollDistance_:n,animation:!1});if(-1<a){var s=getSeriesDataItem(this.opts.series,a);if(0!==s.length){var x=getMixToolTipData(s,this.chartData.calPoints,a,this.opts.categories,e),d=x.textList,h=x.offset;h.y=o.y,l.tooltip={textList:d,offset:h,option:e,index:a}}}drawCharts.call(this,l.type,l,this.config,this.context)}if("candle"===this.opts.type){var a=this.getCurrentDataIndex(t),n=this.scrollOption.currentOffset,l=assign({},this.opts,{_scrollDistance_:n,animation:!1});if(-1<a){var s=getSeriesDataItem(this.opts.series,a);if(0!==s.length){var r=getCandleToolTipData(this.opts.series[0].data,s,this.chartData.calPoints,a,this.opts.categories,this.opts.extra.candle,e),d=r.textList,h=r.offset;h.y=o.y,l.tooltip={textList:d,offset:h,option:e,index:a}}}drawCharts.call(this,l.type,l,this.config,this.context)}if("pie"===this.opts.type||"ring"===this.opts.type||"rose"===this.opts.type){var a=this.getCurrentDataIndex(t),n=this.scrollOption.currentOffset,l=assign({},this.opts,{_scrollDistance_:n,animation:!1});if(-1<a){var s=this.opts.series[a],d=[{text:e.format?e.format(s):s.name+": "+s.data,color:s.color}],h={x:o.x,y:o.y};l.tooltip={textList:d,offset:h,option:e,index:a}}drawCharts.call(this,l.type,l,this.config,this.context)}if("radar"===this.opts.type){var a=this.getCurrentDataIndex(t),n=this.scrollOption.currentOffset,l=assign({},this.opts,{_scrollDistance_:n,animation:!1});if(-1<a){var s=getSeriesDataItem(this.opts.series,a);if(0!==s.length){var d=s.map(function(t){return{text:e.format?e.format(t):t.name+": "+t.data,color:t.color}}),h={x:o.x,y:o.y};l.tooltip={textList:d,offset:h,option:e,index:a}}}drawCharts.call(this,l.type,l,this.config,this.context)}},Charts.prototype.translate=function(e){this.scrollOption={currentOffset:e,startTouchX:e,distance:0,lastMoveTime:0};let t=assign({},this.opts,{_scrollDistance_:e,animation:!1});drawCharts.call(this,this.opts.type,t,this.config,this.context)},Charts.prototype.scrollStart=function(t){var e=t.mp.changedTouches[0]||t.changedTouches[0],i=getTouches(e,this.opts,t);e&&!0===this.opts.enableScroll&&(e.x?this.scrollOption.startTouchX=i.x:this.scrollOption.startTouchX=i.clientX)},Charts.prototype.scroll=function(t){0===this.scrollOption.lastMoveTime&&(this.scrollOption.lastMoveTime=Date.now());let e=this.opts.extra.touchMoveLimit||20,i=Date.now(),o=i-this.scrollOption.lastMoveTime;if(!(o<Math.floor(1e3/e))){this.scrollOption.lastMoveTime=i;var a=t.mp.changedTouches[0]||t.changedTouches[0],n=getTouches(a,this.opts,t);if(a&&!0===this.opts.enableScroll){var l=a.x?n.x-this.scrollOption.startTouchX:n.clientX-this.scrollOption.startTouchX;var s=this.scrollOption.currentOffset,r=calValidDistance(s+l,this.chartData,this.config,this.opts);this.scrollOption.distance=l=r-s;var d=assign({},this.opts,{_scrollDistance_:s+l,animation:!1});return drawCharts.call(this,d.type,d,this.config,this.context),s+l}}},Charts.prototype.scrollEnd=function(){if(!0===this.opts.enableScroll){var e=this.scrollOption,t=e.currentOffset,i=e.distance;this.scrollOption.currentOffset=t+i,this.scrollOption.distance=0}},module.exports=Charts;