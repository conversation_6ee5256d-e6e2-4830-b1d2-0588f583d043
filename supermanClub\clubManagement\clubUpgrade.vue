<template>
  <view class="partner p-30 f-28">
    <view class="bold">申请人信息:</view>
    <form id="#nform">
      <view class="partnerFlex" style="margin-top: 44rpx; margin-bottom: 100rpx">
        <view class="bold lineHiehgt">
          <text style="color: red">*</text>
          申请人姓名:
        </view>
        <view style="width: 60%">
          <input class="uni-input" name="input" placeholder="请输入姓名" maxlength="10" v-model="name" type="text" />
          <text style="font-size: 24rpx; color: #c6c6c6">请输入真实姓名，便于后续业务对接</text>
        </view>
      </view>
      <view class="partnerFlex">
        <view class="bold lineHiehgt">
          <text style="color: red">*</text>
          上级品牌编码:
        </view>
        <view style="width: 60%">
          <radio-group @change="checkClubCode">
            <view class="mb-20">
              <label class="mb-10">
                <radio value="1" checked="true" style="transform: scale(0.7)" />
                已有上级
              </label>
              <input class="uni-input" name="input" placeholder="请输入上级品牌编码" v-model="clubCode" style="flex: 1" :disabled="diasbleCode" />
              <text style="font-size: 24rpx; color: #c6c6c6">请输入上级品牌编码，你的申请将会由上级进行审批</text>
            </view>
            <view class="flex-dir-col">
              <label>
                <radio value="2" style="transform: scale(0.7)" />
                无
              </label>
              <text style="font-size: 24rpx; color: #c6c6c6">选择无，将会由鼎校甄选平台进行审批，平台将会在1-3个工作日内联系您。</text>
            </view>
          </radio-group>
        </view>
      </view>
    </form>
    <view class="submitButton">
      <button @tap.stop="submitInfo" class="f-28 c-ff">提交</button>
    </view>
  </view>
</template>

<script>
  const { $http } = require('@/util/methods.js');
  export default {
    data() {
      return {
        name: '',
        clubCode: '',
        shareId: '',
        checkedNone: 1,
        diasbleCode: false,
        app: 0
      };
    },
    onLoad(option) {
      if (option.token) {
        this.app = option.app;
        this.$handleTokenFormNative(option);
      }
      let a = uni.getStorageSync('club');
      if (a) {
        uni.switchTab({
          url: '/pages/home/<USER>/index'
        });
      }
      if (option != null) {
        this.shareId = option.userId;
      }
    },
    onUnload() {
      // #ifdef APP-PLUS
      if (this.app) {
        plus.runtime.quit();
      }
      // #endif
    },
    methods: {
      checkClubCode(value) {
        if (value.detail.value == 1) {
          this.checkedNone = 1;
          this.diasbleCode = false;
        } else {
          this.checkedNone = 2;
          this.clubCode = '';
          this.diasbleCode = true;
        }
      },
      async submitInfo() {
        if (this.name == '') {
          this.$util.alter('姓名不能为空');
          return false;
        }
        if (this.clubCode == '' && this.checkedNone == 1) {
          this.$util.alter('上级品牌编码不能为空');
          return false;
        }
        const res = await $http({
          url: 'zx/user/merchantApply',
          method: 'post',
          data: {
            realName: this.name,
            phone: uni.getStorageSync('phone') ? uni.getStorageSync('phone') : '',
            merchantType: 2,
            shareId: this.shareId,
            affiliationBrandCode: this.clubCode
          }
        });
        if (res) {
          uni.showToast({
            title: '已提交申请',
            icon: 'success'
          });
          uni.switchTab({
            url: '/pages/home/<USER>/index'
          });
        } else {
          /* uni.showToast({
						title:'上级编码有误',
					}) */
        }
      }
    }
  };
</script>

<style lang="scss" scoped>
  .partner {
    width: 100vw;
    height: 100vh;
    background-color: #fff;
    box-sizing: border-box;
  }

  .uni-input {
    height: 64rpx;
    padding-left: 32rpx;
    background-color: #f3f8fc;
  }

  .partnerFlex {
    display: flex;
    justify-content: space-between;
  }

  .lineHiehgt {
    line-height: 64rpx;
  }

  .submitButton {
    width: 686rpx;
    line-height: 74rpx;
    margin: 0 auto;
    background: #339378;
    border-radius: 38rpx;
    position: fixed;
    bottom: 32rpx;
  }
</style>
