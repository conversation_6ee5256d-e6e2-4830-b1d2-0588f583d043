<template>
  <view style="height: 100vh; background-color: #f5f8fa">
    <view class="detailTop">
      <view :class="detailsIndex === 0 ? 'details' : ''" @tap="clickCredit">积分获取明细</view>
      <view :class="detailsIndex === 1 ? 'details' : ''" @tap="clickdb">鼎币获取明细</view>
    </view>
    <view v-if="detailsIndex === 0">
      <view class="f-28 p-20" v-if="creditList.data && creditList.data.length > 0">
        <view class="pointsBox mb-20">
          积分获取金额：
          <view style="color: #339378; font-weight: 700">
            {{ userCredit }}
          </view>
        </view>
        <view class="radius-16 mb-20" v-for="(item, index) in creditList.data" :key="index">
          <view class="bg-ff p-10 sizing">
            <view class="flex-s lh-90" style="line-height: 90rpx; border-bottom: 2rpx solid #d0d0d0">
              <view class="type">
                类型:
                <view style="color: #339378; font-weight: 700; margin-left: 20rpx">{{ item.taskDesc }}</view>
              </view>
              <view style="color: #b1b1b1" class="f-24">
                {{ item.createdTime }}
              </view>
            </view>
            <view class="mtb-25" v-if="item.taskDesc != '签到'">被邀请人名称: {{ item.taskDetail.split('：')[2].split(',')[0] }}</view>
            <view class="" v-if="item.taskDesc != '签到'">手机号: {{ item.taskDetail.split('：')[3] }}</view>
            <view class="mtb-25">收益: {{ item.credit }}积分</view>
          </view>
        </view>
      </view>
      <view v-else class="curriculum_css_no pt-30 pb-55 f-28">
        <image class="curriculum_image" src="https://document.dxznjy.com/course/ac587707bf314badadb28a158852c77d.png"></image>
        <view class="c-66 f-24 mtb-25">暂无明细</view>
      </view>
      <view v-if="no_more && creditList.data != undefined && creditList.data.length > 0">
        <u-divider text="到底了"></u-divider>
      </view>
    </view>
    <view v-if="detailsIndex === 1">
      <view class="f-28 p-20" v-if="dbList.data && dbList.data.length > 0 && type == 1">
        <view class="pointsBox mb-20 flex-s">
          <view class="flex-a-c">
            鼎币金额：
            <view style="color: #339378; font-weight: 700">
              {{ userDingbi }}
            </view>
          </view>
        </view>
        <view class="radius-16 mb-20" v-for="(item, index) in dbList.data" :key="index">
          <view class="bg-ff p-10 sizing">
            <view class="flex-s lh-90" style="line-height: 90rpx; border-bottom: 2rpx solid #d0d0d0">
              <view class="type">
                类型:
                <view style="color: #339378; font-weight: 700; margin-left: 20rpx">
                  {{ item.coinSource }}
                </view>
              </view>
            </view>
            <view class="mtb-25">被推荐人名称: {{ item.beShareUerName }}</view>
            <view class="">被推荐人手机号: {{ item.beShareUerMobile }}</view>
            <view class="mtb-25">被推荐人下单总金额: {{ item.orderTotalAmount }}元</view>
            <view class="">{{ item.direction == 1 ? '退回' : '获得奖励' }} : {{ item.coinNumber }}鼎币</view>
            <view class="mtb-25">推荐完成时间: {{ item.createTime }}</view>
          </view>
        </view>
      </view>
      <view v-if="dbList.data.length == 0 && type == 1" class="curriculum_css_no pt-30 pb-55 f-28">
        <image class="curriculum_image" src="https://document.dxznjy.com/course/ac587707bf314badadb28a158852c77d.png"></image>
        <view class="c-66 f-24 mtb-25">暂无明细</view>
      </view>
      <view v-if="dbno_more && dbList.data != undefined && dbList.data.length > 0">
        <u-divider text="到底了"></u-divider>
      </view>
      <view class="curriculum_css_no pt-30 pb-55 f-28 t-c" v-if="type == 0">
        <image class="curriculum_image" src="https://document.dxznjy.com/course/ac587707bf314badadb28a158852c77d.png"></image>
        <view class="c-66 f-24 mtb-25">暂无明细</view>
        <view class="f-28 mt-24 mb-30" style="">成为会员，获得更多奖励</view>
        <!-- 	<view class="buttonMember">
					
				</view> -->
        <!-- 2024-11-6 紧急修改 购买超级会员修改成购买家长会员 隐藏 -->
        <view v-if="false" class="buttonMember" @click="toSuperMan">
          <!-- 开通会员 -->
        </view>
      </view>
    </view>
  </view>
</template>

<script>
  const { $http } = require('@/util/methods.js');
  export default {
    data() {
      return {
        detailsIndex: 0,
        page: 1,
        dbpage: 1,
        creditList: {},
        dbList: {
          data: []
        },
        no_more: false,
        dbno_more: false,
        tooltipInfoStatus: false,
        timerId: '',
        type: -1, //0家长 1会员
        userDingbi: uni.getStorageSync('userDingbi') ? uni.getStorageSync('userDingbi') : '',
        userCredit: uni.getStorageSync('userCredit') ? uni.getStorageSync('userCredit') : '',
        app: 0
      };
    },
    onHide() {},
    onLoad(option) {
      console.log(option);
      if (option.token) {
        this.app = option.app;
        this.$handleTokenFormNative(option);
        this.getMyData();
      }
      if (option != null) {
        this.type = option.type;
      }
    },
    onShow() {
      this.fetchdetails();
    },
    onReachBottom() {
      if (this.detailsIndex === 0) {
        if (this.page >= this.creditList.totalPage) {
          this.no_more = true;
          return false;
        }
        this.fetchdetails(true, ++this.page);
      }
      if (this.detailsIndex === 1) {
        if (this.dbpage >= this.dbList.totalPage) {
          this.dbno_more = true;
          return false;
        }
        this.fetchdbList(true, ++this.dbpage);
      }
    },
    onUnload() {
      // #ifdef APP-PLUS
      if (this.app) {
        plus.runtime.quit();
      }
      // #endif
    },
    methods: {
      async getMyData() {
        const res1 = await $http({ url: 'zx/wap/invite/getDingBi' });
        if (res1) {
          this.userDingbi = res1.data;
          uni.setStorageSync('userDingbi', this.userDingbi);
        }

        const res2 = await $http({
          url: `zx/wap/credit/getCreditCount`,
          method: 'GET',
          data: {
            userId: uni.getStorageSync('user_id')
          }
        });
        if (res2) {
          this.userCredit = res2.data;
          uni.setStorageSync('userCredit', this.userCredit);
        }
      },
      //获取明细
      async fetchdetails(isPage, page) {
        const res = await $http({
          url: 'zx/wap/credit/detail/page',
          data: {
            pageSize: 10,
            pageNum: 1,
            userId: uni.getStorageSync('user_id') ? uni.getStorageSync('user_id') : ''
          }
        });
        if (res) {
          if (isPage) {
            let old = this.creditList.data;
            this.creditList.data = [...old, ...res.data.data];
          } else {
            this.creditList = res.data;
          }
        }
      },
      async fetchdbList(isPage, page) {
        let parms = {
          pageNum: page || 1,
          pageSize: 10
        };
        const res = await $http({
          url: 'zx/wap/invite/getCoinFlowList',
          data: parms
        });
        if (res) {
          if (isPage) {
            let old = this.dbList.data;
            this.dbList.data = [...old, ...res.data.data];
          } else {
            this.dbList = res.data;
          }
        }
        console.log(this.dbList);
      },
      clickCredit() {
        this.detailsIndex = 0;
        this.fetchdetails();
      },
      clickdb() {
        this.detailsIndex = 1;
        this.fetchdbList();
      },
      toSuperMan() {
        uni.navigateTo({
          url: '/Personalcenter/my/nomyEquity'
        });
      }
    }
  };
</script>

<style lang="scss" scoped>
  .details {
    border-bottom: 4rpx solid #339378;
    font-weight: 700;
  }

  .pointsBox {
    height: 96rpx;
    background-color: #f9fcfe;
    display: flex;
    align-items: center;
  }

  .detailTop {
    margin: 20rpx 0;
    display: flex;
    justify-content: space-evenly;
  }

  .type {
    display: flex;
  }

  .bgf {
    width: 376rpx;
    height: 112rpx;
    margin: 0 auto;
    background: url('https://document.dxznjy.com/course/1e6afed624714ef4825a7dba609ccd58.png') no-repeat;
    background-size: 100%;
  }

  .curriculum_css_no {
    position: relative;
    width: 710rpx;
    margin: auto;
    margin-top: 400rpx;
    text-align: center;

    .curriculum_image {
      width: 122rpx;
      height: 114rpx;
      display: block;
      margin: 16rpx auto;
    }

    .curriculum_title {
      text-align: left;
    }
  }

  .typeStatus {
    width: 90rpx;
    line-height: 40rpx;
    background: rgba(255, 221, 167, 0.15);
    border-radius: 8rpx;
    border: 2rpx solid #ffdda7;
    color: #fd9b2a;
  }

  .tooltipClass {
    width: 320rpx;
    background: #fff;
    font-size: 24rpx;
    color: #afaeae;
    box-sizing: border-box;
    position: absolute;
    top: 10rpx;
    right: 40rpx;

    .sanjiao {
      width: 0;
      height: 0;
      border: 10rpx solid transparent;
      border-top: 10rpx solid #fff;
      position: absolute;
      bottom: -20rpx;
      right: 0;
    }
  }

  .buttonMember {
    background: url('https://document.dxznjy.com/course/21d5d6cd22c84fc2b9259cbeb7651a1f.png') no-repeat;
    background-size: 100%;
    width: 376rpx;
    height: 112rpx;
    margin: 0 auto;
  }
</style>
