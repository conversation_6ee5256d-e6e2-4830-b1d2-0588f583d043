# 甄选小程序

## 说明

鼎校甄选是鼎校推出的全新学习资源平台，坚持为用户甄选最好的学习产品，致力于为中国孩子提供全场景全龄段的学习解决方案。

## 安装和运行

### 前提条件
- [Node.js](https://nodejs.org/) (v16.20.2)
- npm 或 pnpm

### 安装
```bash
git clone https://codeup.aliyun.com/5ee323d293b16cdfea1276ae/dxznyy/parent_applet.git
cd parent_applet
# npm方式安装
npm i 
```

### 运行
`HbuilderX`运行到微信小程序模拟器


## 项目结构

```bash
parent_applet
├── .gitignore                                  # Git 忽略文件
├── .hbuilderx                                  # HbuilderX 配置文件
│   └── launch.json
├── .prettierignore                             # Prettier 忽略文件
├── App.vue                                     # app 根组件
├── Coursedetails                                      
│   ├── Career                                  
│   │   └── planning.vue                        # 规划报告
│   ├── centerList
│   │   └── videoDetails.vue                    # 课程详情
│   ├── components
│   │   ├── barrage                             # 弹幕组件
│   │   ├── courseCatalog.vue                   # 课程目录详情
│   │   ├── emptyPage.vue                       # 空页面
│   │   ├── growthPopup.vue                     # 成长值增长弹框组件
│   │   ├── problemItem.vue
│   │   ├── questionsAnswers.vue                
│   │   ├── releaseItem.vue                     
│   │   ├── suspensionBtn.vue                   # 我要上传悬浮按钮
│   │   ├── tipsContentPopup.vue                # 评论提示弹框组件     
│   │   ├── uni-calendar                        # 日历组件
│   │   ├── uni-forms                           # 表单组件
│   │   └── xuan-switch                         # 开关组件
│   ├── culturalType
│   │   ├── familyCulture.vue                   # 家庭文化
│   │   ├── palyVideo.vue                       # 家庭文化播放视频 
│   │   └── readCultural.vue                    # 读书文化
│   ├── feedback
│   │   ├── components
│   │   │   ├── review.vue                      
│   │   │   └── study.vue
│   │   ├── index.vue                           # 反馈
│   │   └── newIndex.vue                        # 学习反馈
│   ├── leave
│   │   └── leave.vue                           # 请假
│   ├── mealDetail.vue                          # 套餐详情
│   ├── my
│   │   ├── joggingCamp.vue                     # 陪跑营    
│   │   ├── joggingCampAct.vue                  # 陪跑营活动
│   │   ├── myEquity.vue                        # 超级会员权益中心
│   │   ├── parentEquity.vue                    # 家长会员权益中心
│   │   └── studentInfo.vue                     # 陪跑营报名信息
│   ├── pinYin
│   │   └── preschoolVideo.vue                  # 学前视频
│   ├── productDetils.vue                       # 商品详情页                
│   ├── share
│   │   ├── reviewshare.vue                     # 复习分享
│   │   ├── share.vue                           # 学习分享
│   │   └── trial.vue                           # 学习分享                  
│   └── study
│       ├── courseDetail.vue                    # 课程详情
│       └── downloadProfile.vue                 # 学习资料
├── InvitationGifts
│   ├── components
│   │   ├── helang-waterfall                    # 瀑布流组件
│   │   │   └── helang-waterfall.vue
│   │   ├── invitationDetai.vue                 # 邀请记录
│   │   ├── invitationPoster.vue                # 邀请海报
│   │   ├── uni-datetime-picker                 # 时间选择组件
│   │   └── uni-table                           # 表格组件
│   ├── index.vue                               # 邀请有礼
│   └── invitation.vue                          # 加入甄选福利群
├── Listen
│   ├── answer.vue                              # 答题
│   ├── answerAnalysis.vue                      # 答题分析
│   ├── components
│   │   └── my-audio.vue                        # 音频组件
│   ├── index.vue                               # 全能听力
│   ├── listenReport.vue                        # 查看报告
│   ├── originalTranslation.vue                 # 原文翻译
│   ├── previewListen.vue                       # 听力预览
│   └── reveiewAnswer.vue                       # 听力复习
├── PYFforget
│   ├── components
│   │   └── page-pagination                     # 分页组件
│   ├── dayLessonPreview.vue                    # 当日课程预览
│   ├── dcReviewCheck.vue                       # 单词复习检测
│   ├── forgetReview.vue                        
│   ├── lessonPreview.vue                       # 复习课程
│   ├── pastLessonPreview.vue                   # 往期课程预览
│   ├── pastReview.vue                          # 往期复习
│   ├── reviewReport.vue                        # 完成本次作业情况
│   ├── todayReview.vue                         # 今日复习
│   └── yfyReviewCheck.vue                      # 元辅音复习检测
├── Personalcenter
│   ├── Career
│   │   ├── enter.vue                           # 学业生涯规划
│   │   ├── index.vue                           # 初始问卷调查
│   │   ├── news.vue                            # 会员信息
│   │   └── planning.vue                        # 规划报告
│   ├── components
│   │   ├── endDialog
│   │   │   └── end-dialog.vue                  # 结束弹框
│   │   ├── lunc-calendar                       # 日历组件
│   │   ├── myp-one
│   │   │   └── myp-one.vue
│   │   ├── uni-bottom
│   │   │   └── uni-bottom.vue                  # 底部支付价格组件
│   │   ├── uni-data-checkbox                   # 复选组件
│   │   ├── uni-datetime-picker                 # 时间选择组件
│   │   └── uni-list                            # 列表组件
│   │       ├── uni-list.vue
│   │       ├── uni-refresh.vue
│   │       └── uni-refresh.wxs
│   ├── home
│   │   ├── info.vue                            # 个人信息
│   │   ├── page.vue                            # 个人中心
│   │   └── petName.vue                         # 昵称
│   ├── index
│   │   └── index.vue                           # 课程表
│   ├── interest
│   │   └── orderDetail.vue                     # 订单详情
│   ├── login
│   │   ├── login.vue                           # 登录
│   │   ├── phoneLogin.vue                      # 手机验证码登录
│   │   └── wxPhonePwdLogin.vue                 # 账号密码登录
│   ├── my
│   │   ├── collectionManage.vue                # 收藏管理
│   │   ├── course.vue                          # 产品中心
│   │   ├── customerServiceQRcode.vue           # 添加企业微信
│   │   ├── growthReport.vue                    # 成长报告
│   │   ├── integralRecord.vue                  # 积分记录
│   │   ├── meetingServiceQRcode.vue            # 会议企微二维码
│   │   ├── myCollection.vue                    # 我的收藏
│   │   ├── myCourse.vue                        # 我的课程
│   │   ├── myCourseList.vue                    # 我的课程
│   │   ├── myIntegral.vue                      # 积分中心
│   │   ├── myinvite.vue                        # 我的邀请
│   │   ├── mystudent.vue                       # 我的学员
│   │   ├── mystudentAdd.vue                    # 新增学员
│   │   ├── nomyEquity.vue                      # 超级会员开通权益
│   │   ├── parentVipEquity.vue                 # 家长会员开通权益
│   │   ├── partnerUnion.vue                    # 学习超人开通成功二维码
│   │   ├── pronunciationList.vue               # 我的发音
│   │   ├── pronunciationSettings.vue           # 发音设置
│   │   └── wordCheck
│   │       ├── wordCheck.vue                   # 词汇量检测
│   │       └── wordCheckReport.vue             # 词汇量测试报告
│   ├── password
│   │   └── PasswordChange.vue                  # 修改密码
│   ├── studyPrint
│   │   ├── studyContentPrint.vue               # 学习内容打印
│   │   ├── studyPrint.vue                      # 学习内容打印
│   │   └── studyZip.vue                        # 结业单词打印
│   ├── suggest
│   │   └── index.vue                           # 意见反馈
│   └── vip
│       ├── WithDetails.vue                     # 提现
│       ├── With_List.vue                       # 提现明细
│       ├── Withdrawal.vue                      # 提现信息
│       ├── apply.vue                           # 成为渠道商
│       ├── cashAdvance.vue                     # 提现
│       ├── commission.vue                      # 佣金记录
│       ├── customer.vue                        # 客户列表
│       ├── index.vue                           # 会员中心
│       ├── qd_apply.vue                        # 渠道商申请
│       └── wallet.vue                          # 提现
├── README.en.md
├── README.md
├── ReadForget
│   ├── ReviewBook.vue                          # 复习生词
│   ├── ReviewCheckPoint.vue                    # 复习关卡
│   ├── components
│   │   └── page-pagination                     # 分页组件
│   ├── forgetReview.vue                        # 拼音法抗遗忘
│   ├── index.vue                               # 新阅读理解抗遗忘
│   ├── pastForget.vue                          # 往期复习
│   ├── readReport.vue                          # 复习报告
│   └── todayForget.vue
├── Recharge
│   ├── Collection
│   │   └── Collection.vue                      # 收款
│   ├── Record
│   │   └── Record.vue                          # 充值记录
│   ├── components
│   │   ├── Sansnn-uQRCode                      # 收款码
│   │   ├── uni-datetime-picker                 # 时间选择组件
│   │   └── ys-paypass
│   │       └── ys-paypass.vue                  
│   ├── index.vue                               # 商户收款平台
│   ├── lessonDetails.vue                       # 学时详情
│   ├── onlineJoinTable
│   │   ├── coursePlan.vue                      # 课程规划
│   │   ├── onlineClass.vue                     # 上课信息对接表
│   │   └── onlineJoinTable.vue                 # 上课信息对接表
│   ├── paySuccess.vue                          # 支付成功
│   ├── payment
│   │   └── payment.vue                         # 付款
│   ├── reviewSchedule
│   │   ├── index.vue
│   │   └── reviewTableList.vue
│   └── successcopy.vue
├── Trialclass
│   ├── collectionProcess.vue                   # 红包领取流程
│   ├── components
│   │   ├── long-date                           # 时间选择组件
│   │   └── uni-data-checkbox                   # 多选组件
│   ├── index.vue                               # 试课单
│   ├── recommend.vue                           # 试课推荐
│   ├── result.vue                              # 试课报告
│   ├── shopnews.vue                            # 试课详情
│   └── trialreport.vue                         # 试课报告
├── _FolderWatchConfig.jscompress
├── aiIntelligentWords
│   └── wordsSelect
│       ├── aiLogging.vue                       # AI智阅
│       └── wordsSelect.vue                     # AI智阅
├── antiAmnesia
│   ├── antiForgetting
│   │   ├── grammarReport.vue                   # 语法复习报告
│   │   ├── historyReviewReport.vue             # 历史复习报告
│   │   └── reviewReport.vue                    # 今日复习报告
│   ├── components
│   │   ├── interesting-dialog                  
│   │   ├── interesting-head
│   │   └── page-pagination
│   └── review
│       ├── allWords.vue                        # 今日复习
│       ├── component
│       │   └── uni-tooltip                     # 提示框
│       ├── funReview.vue                       # 词库
│       ├── grammarPreview.vue                  # 语法预览
│       ├── grammarReview.vue                   # 复习语法
│       ├── handout.vue                         # 讲义列表
│       ├── history.vue                         # 往期复习
│       ├── index.vue                           # 复习
│       ├── list.vue                            # 复习单词
│       ├── report.vue                          # 复习报告
│       ├── studyFunReview.vue                  # 学习
│       └── wordPreview.vue                     # 单词预览
├── aptitudeTesting
│   ├── index.vue                               # 测评报告
│   └── startTest.vue                           # 天赋测评
├── clubApplication
│   ├── addPartner.vue                          # 新增合伙人
│   ├── addSuperbrands.vue                      # 新增品牌
│   ├── buyProcurement.vue                      # 采购邀请码
│   ├── clubList.vue                            # 俱乐部列表
│   ├── components
│   │   ├── piaoyi-cityPicker                   # 城市选择
│   │   ├── uni-datetime-picker                 # 时间选择组件
│   │   └── uni-table                           # 表格组件
│   ├── levelProcurement.vue                    # 采购单
│   └── partnerList.vue                         # 合伙人列表
├── common
│   ├── common.css                              # 公共css
│   ├── font.css                                # 字体
│   ├── interestChart.js                        # 统计图表
│   ├── playVoice.js                            # 播放音频
│   ├── superman.js                             # 接口
│   └── zwyCss.css                              # 公共css
├── components
│   ├── commonDialog.vue                        # 公共弹窗
│   ├── helang-waterfall                        # 瀑布流
│   │   └── helang-waterfall.vue
│   ├── mycurriculum.vue                        # 课程列表
│   ├── sharePopup.vue                          # 分享弹窗
│   ├── u-charts                                # 统计图表
│   ├── uni-badge                               # 标签
│   ├── uni-card                                # 卡片
│   ├── uni-list-item                           # 列表
│   ├── uni-load-more                           # 加载更多
│   ├── uni-nav-bar                             # 导航栏
│   ├── uni-rate                                # 评分
│   ├── uni-section                             # 标题
│   └── write_word.vue                          # 输入框
├── coupons
│   ├── CouponsList.vue                         # 优惠券列表
│   └── RedeemCode.vue                          # 兑换码弹框
├── errorBook
│   ├── components
│   │   ├── blankQuestion.vue                   # 填空题
│   │   ├── interesting-dialog
│   │   └── optionQuestion.vue                  # 单选题
│   ├── detailsPage.vue                         # 已做试题/未做试题
│   ├── doneCheckpoint.vue                      # 已做关卡/未做关卡
│   ├── doneTestQuestions.vue                   # 文章/题目
│   ├── index.vue                               # 错题本
│   ├── knowledgeReview.vue                     # 知识点复习
│   ├── questionPage.vue                        # 错题页
│   ├── topicPage.vue                           # 错题
│   └── topicPaged.vue                          # 错题
├── growth
│   └── centerList
│       ├── courseAssessment.vue                # 课程考核
│       ├── courseDetails.vue                   # 课程详情
│       ├── examRecords.vue                     # 考试记录
│       ├── fileView.vue                        # 文件查看
│       ├── growthCenter.vue                    # 成长中心
│       ├── viewTest.vue                        # 考试
│       └── viewTestPaper.vue                   # 考试记录
├── iconfont.css                                # iconfont
├── incomeDetails
│   └── index.vue                               # 收益明细
├── index.html                                 
├── index.js
├── index.scss
├── interestModule
│   ├── components
│   │   ├── cmd-progress    
│   │   │   └── cmd-progress.vue                # 进度条
│   │   ├── interesting-dialog
│   │   └── interesting-head
│   ├── interestHistory.vue                     # 历史记录
│   ├── interestingLlk.vue                      # 连连看
│   ├── interestingPpl.vue                      # 拼拼乐
│   ├── interestingSccg.vue                     # 识词冲关
│   ├── interestingTyby.vue                     # 听音辨意
│   ├── playbill.vue                            
│   ├── round.vue                               # 关卡报告
│   ├── searchPage.vue                          # 搜索页面
│   └── wordIdentifying.vue                     # 单词标识
├── main.js 
├── manifest.json                               # 小程序权限
├── meeting
│   ├── components
│   │   └── Sansnn-uQRCode
│   ├── meetH5.vue
│   ├── meetIndex.vue                           # 会议中心
│   ├── meetVerifyInfo.vue                      # 订单详情
│   ├── meetWeb.vue                             # h5
│   ├── meetingList.vue                         # 更多会议
│   └── verifyList.vue                          # 核销列表
├── memberCenter
│   ├── addProblem.vue                          # 去提问
│   ├── components
│   │   ├── emptyPage.vue                       # 空页面
│   │   ├── growthPopup.vue                     # 成长值弹窗
│   │   ├── problemItem.vue                     
│   │   ├── questionsAnswers.vue                
│   │   ├── releaseItem.vue
│   │   ├── suspensionBtn.vue
│   │   └── tipsContentPopup.vue
│   ├── culturalType
│   │   ├── familyPage                          
│   │   │   ├── addQuestions.vue                # 提问
│   │   │   └── teacherIntroduction.vue         # 家庭文化
│   │   ├── labourCulture.vue                   # 劳动文化
│   │   └── sportsCulture.vue                   # 运动文化
│   ├── detail
│   │   ├── growUpRecord.vue                    # 成长值获取明细
│   │   ├── problemDetail.vue                   # 问答详情
│   │   ├── rankingRecord.vue                   # 领取记录
│   │   └── releaseDetail.vue                   # 评论详情
│   ├── homepage.vue                            # 个人主页
│   ├── index.vue                               # 会员文化中心
│   ├── medalIndex.vue                          # 勋章
│   ├── rankingList.vue                         # 排行榜
│   └── releaseIndex.vue                        # 发布瞬间
├── package-lock.json                           
├── package.json                                # 项目依赖包版本信息
├── pages
│   ├── beingShared
│   │   └── index.vue                           # 加载页面
│   ├── feedback
│   │   └── share_feedback.vue                  # 反馈
│   ├── home
│   │   ├── components
│   │   │   └── tabDetail.vue                   # 学习服务组件
│   │   ├── index
│   │   │   └── index.vue                       # 我的页面
│   │   └── parent
│   │       └── index.vue                       # 学习页面
│   ├── index
│   │   ├── cart.vue
│   │   ├── index.vue                           # 首页
│   │   └── web.vue
│   ├── pages.json
│   └── selection
│       └── index.vue                           # 甄选页面
├── pages.json                                  # 页面配置
├── parentEnd
│   ├── components
│   │   ├── jp-charts
│   │   ├── lime-echart
│   │   └── qiun-data-charts
│   ├── dictation
│   │   ├── dictation.vue                       # 听写能力检测
│   │   ├── dictationReport.vue                 # 听写检测报告
│   │   └── writeTest.vue                       # 学习打印页面
│   ├── recharge
│   │   └── lessonDetails.vue                   # 学时详情
│   ├── report
│   │   ├── dictationSingleReport.vue           # 学习测试报告
│   │   ├── radar.vue                           # 雷达图
│   │   └── report.vue                          # 英语水平专业测评报告
│   └── vocabulary
│       └── vocabulary.vue                      # 词汇量报告
├── partnerApplication
│   ├── anticipatedIncome.vue                   # 课时预估收益
│   ├── codeProcurement.vue                     # 邀请码采购
│   ├── components
│   │   └── uni-table
│   ├── index.vue                               # 超级合伙人申请
│   ├── levelMember.vue                         # 会员
│   ├── levelPartners.vue                       # 下级合伙人
│   ├── myProcurement.vue                       # 我的采购
│   ├── partnerDetails.vue                      # 收益明细
│   ├── recomBrand.vue                          # 已推品牌
│   ├── recomClub.vue                           # 已推俱乐部
│   └── recomPartners.vue                       # 已推合伙人
├── prettier.config.js                          # 格式配置
├── shoppingMall
│   ├── components
│   │   └── emptyPage.vue                       # 空页面
│   ├── details.vue                             # 商品详情
│   ├── exchangeRecord.vue                      # 兑换记录
│   └── index.vue                               # 鼎币商城
├── signature
│   ├── components
│   │   ├── emptyPage.vue
│   │   └── uni-forms
│   └── contract
│       ├── cManageCheck.vue                    # 认证信息
│       ├── cManagement.vue                     # 合同管理
│       ├── cResult.vue                         # 合同结果
│       ├── confirmOrder.vue                    # 确认订单
│       ├── contractDetail.vue                  # 合同明细
│       ├── createContract.vue                  # 创建合同
│       ├── createDetail.vue                    # 确认订单
│       ├── manageSigning.vue                   # 合同签署
│       ├── orderDetails.vue                    # 订单详情
│       ├── orderRecord.vue                     # 订购记录
│       ├── qyCertification.vue                 # 企业认证
│       └── signingPage.vue                     # 合同签署
├── splitContent
│   ├── address
│   │   ├── add
│   │   │   └── add.vue                         # 新增地址
│   │   ├── edit
│   │   │   └── edit.vue                        # 编辑地址
│   │   └── list
│   │       └── list.vue                        # 地址列表
│   ├── authen
│   │   ├── Privacyagree.vue                    # 隐私协议
│   │   ├── authen.vue                          # 实名认证
│   │   └── useragree.vue                       # 用户服务协议
│   ├── channel
│   │   ├── Finance.vue                         # 财务管理
│   │   ├── customer.vue                        # 客户列表
│   │   ├── index.vue                           # 渠道商服务
│   │   ├── qd_apply.vue                        # 渠道商申请
│   │   └── qdsList.vue                         # 渠道商列表
│   ├── components
│   │   ├── uni-data-checkbox                   # 多选
│   │   ├── uni-datetime-picker                 # 时间选择
│   │   └── uni-file-picker                     # 文件选择
│   ├── meal
│   │   ├── logistics.vue                       # 物流详情
│   │   └── order.vue                           # 套餐订单中心
│   ├── officialAccount
│   │   ├── index.vue                           # 关注公众号
│   │   └── staffCard.vue                       # 添加专属学管师
│   ├── order
│   │   ├── evaluate.vue                        # 发布评价
│   │   ├── evaluateList.vue                    # 评论列表
│   │   ├── order.vue                           # 订单列表
│   │   ├── orderCancel.vue                     # 订单取消
│   │   ├── orderdetail.vue                     # 订单详情
│   │   ├── payCancel.vue                       # 支付取消
│   │   ├── paymentSuccess.vue                  # 支付成功
│   │   ├── refundDetails.vue                   # 已申请退款详情
│   │   ├── refundRequest.vue                   # 申请退款
│   │   └── viewComments.vue                    # 查看评价
│   ├── poster
│   │   ├── congratulations.vue                 # 喜报
│   │   ├── index.vue                           # 推荐海报
│   │   └── indexcopy.vue
│   ├── test.vue
│   └── writeoff                                
│       └── writeoff.vue                        # 订单核销
├── static
├── supermanClub
│   ├── applyDetails
│   │   └── index.vue                           # 申请详情
│   ├── clubApplication
│   │   ├── applicationWaiting.vue              # 俱乐部申请/升级
│   │   └── index.vue                           # 俱乐部申请
│   ├── clubManagement
│   │   ├── clubUpgrade.vue                     # 超级俱乐部申请
│   │   └── customerList.vue                    # 客户列表
│   ├── components
│   │   └── uni-table
│   ├── list.vue                                # 超人俱乐部列表
│   ├── owner
│   │   └── ownerChange.vue                     # 上级变更通知
│   ├── superman
│   │   ├── learnMore.vue                       # 了解更多
│   │   └── superman.vue                        # 超人
│   └── supermanSign
│       ├── news.vue                            # 消息
│       ├── processing.vue                      # 超人码待处理
│       ├── purchaseCodesList.vue               # 进货管理
│       ├── shipment.vue                        # 出货
│       ├── sign.vue                            # 超人码管理
│       ├── stockGoods.vue                      # 进货单
│       ├── stockprocessing.vue                 # 进货管理
│       ├── tabulation.vue                      # B3平推列表
│       └── treatHandleShipment.vue             # 待处理出货单
├── theme.scss
├── uni.scss
├── unpackage                                   # 编译产物
├── uni_modules
│   ├── lime-painter
│   ├── uni-data-select
│   ├── uni-icons
│   ├── uni-number-box
│   ├── uni-popup
│   ├── uni-scss
│   ├── uni-swiper-dot
│   └── uni-transition
└── util
    ├── common.css                              # 公共样式
    ├── config.js                               # 接口配置
    ├── graceChecker.js                         # 数据验证（表单验证）
    ├── luch-request
    │   ├── indexUser.js
    │   ├── request.js
    │   └── util.js
    ├── md5.js
    ├── methods
    │   ├── common.js
    │   ├── login.method.js
    │   ├── prompt.js
    │   ├── request.js
    │   └── upload.js
    ├── methods.js
    ├── publicVariable.js                       # 全局变量
    └── util.js                                 # 公共方法

```