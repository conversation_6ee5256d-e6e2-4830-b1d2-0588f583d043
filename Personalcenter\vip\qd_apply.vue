<template>
  <view>
    <view class="p-30">
      <block v-for="(item, index) in listS.list" :key="index">
        <view class="bg-ff p-30 radius-10 relative mb-20">
          <view class="status c-ff f-26" v-if="item.acceptStatus == 0">未受理</view>
          <view class="yisl" v-if="item.acceptStatus == 1">已受理</view>
          <view class="f-28 mb-20">申请人：{{ item.nickName }}</view>
          <view class="f-28 flex mb-20">
            <text>联系电话：{{ item.mobile }}</text>
            <text>申请等级：等级{{ item.merchantRank }}</text>
          </view>
          <view class="f-28 mb-20">
            申请理由：
            <text class="c-m" @tap="reason(item.applyReason)">点击查看</text>
          </view>
          <view class="t-r f-26 c-88">申请时间：{{ item.createdTime }}</view>
        </view>
      </block>
      <view v-if="listS.list.length == 0">
        <u-empty mode="history" icon="https://cdn.uviewui.com/uview/empty/list.png" text="暂无数据"></u-empty>
      </view>
      <view v-if="no_more && listS.list.length > 0">
        <u-divider text="到底了"></u-divider>
      </view>
    </view>
  </view>
</template>

<script>
  const { $http, $showSuccess } = require('@/util/methods.js');
  export default {
    data() {
      return {
        listS: {},
        page: 1,
        no_more: false
      };
    },
    onLoad() {},
    onShow() {
      this.list();
    },
    onReachBottom() {
      if (this.page >= this.listS.totalPage) {
        this.no_more = true;
        return false;
      }
      this.list(true, ++this.page);
    },
    methods: {
      async accept(e) {
        let _this = this;
        const res = await $http({
          url: 'zx/user/applyMerchantAccept',
          data: {
            applyId: e
          }
        });
        if (res) {
          $showSuccess(res.message);
          setTimeout(function () {
            _this.list();
          }, 2000);
        }
      },
      reason(e) {
        uni.showModal({
          title: '申请理由',
          content: e || '暂无',
          showCancel: false,
          success: function (res) {
            if (res.confirm) {
              console.log('用户点击确定');
            } else if (res.cancel) {
              console.log('用户点击取消');
            }
          }
        });
      },
      async list(isPage, page) {
        let _this = this;
        const res = await $http({
          url: 'zx/user/applyMerchantListInUserCenter',
          data: {
            page: page || 1
          }
        });
        if (res) {
          if (isPage) {
            let old = _this.listS.list;
            _this.listS.list = [...old, ...res.data.list];
          } else {
            _this.listS = res.data;
          }
        }
      }
    }
  };
</script>

<style>
  .status {
    position: absolute;
    top: 20rpx;
    right: 0;
    background-image: linear-gradient(to right, #159380, #0e6457);
    height: 50rpx;
    line-height: 50rpx;
    padding: 0 30rpx;
    border-radius: 30rpx 0 0 30rpx;
  }

  .yisl {
    position: absolute;
    top: 20rpx;
    right: 30rpx;
    color: #b3b3b3;
    font-size: 28rpx;
    line-height: 50rpx;
  }
</style>
