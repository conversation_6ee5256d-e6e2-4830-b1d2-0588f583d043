<template>
  <view>
    <view>
      <view class="m-30" v-if="list.length > 0">
        <view class="ptb-5 bg-ff radius-20 address_list_css">
          <block v-for="(item, index) in list" :key="index">
            <view class="address-header" @tap="select(item)">
              <view class="f-28 c-33 bold width_css">
                <!-- {{item.provinceName}}{{item.cityName}}{{item.districtName}} -->
                {{ item.address }}
              </view>
              <view class="f-28 c-33 mt-16">
                <text>{{ item.consigneeName }}</text>
                <text class="ml-20 disple_inline">{{ item.consigneePhone }}</text>
              </view>
              <view class="addRess_button">
                <image class="box-32" @tap.stop="editAddress(item.addressId)" src="https://document.dxznjy.com/course/572feb5d146e4ab1b1d8743041af2d96.png"></image>
                <image class="box-32" @tap.stop="removeAddress(item.addressId)" src="https://document.dxznjy.com/course/de798f49480d45688bc7591610343d11.png"></image>
              </view>
            </view>
            <!--<view class="flex flex-x-between p-30">
							<view class="address_left f-28">
							</view>
							<view class="address_right flex">
								<view class="edit c-99 f-28" @tap.stop="editAddress(item.addressId)">
									<text class="iconfont iconbianji mr-10"></text>
									<text>编辑</text>
								</view>
								<view class="delete ml-30 c-99 f-28" @tap.stop="removeAddress(item.addressId)">
									<text class="iconfont iconshanchu mr-10"></text>
									<text>删除</text>
								</view>
							</view>
						</view> -->
          </block>
        </view>
      </view>
      <view v-if="list.length == 0" class="empty-content">
        <u-empty mode="car" textSize="28rpx" width="150rpx" height="150rpx" :icon="imgHost + 'alading/correcting/no_data.png'" text="暂无地址"></u-empty>
      </view>
      <navigator class="confirm_btn m0 t-c c-ff addaddress" hover-class="none" url="../add/add">新建收货地址</navigator>
    </view>
  </view>
</template>

<script>
  const { $http } = require('@/util/methods.js');
  export default {
    data() {
      return {
        app: 0,
        list: [],
        from: null,
        addressId: '',
        imgHost: getApp().globalData.imgsomeHost,
        show: false // 判断是否选择地址后修改地址
      };
    },
    onLoad(e) {
      console.log('e', e);
      this.from = e.from;
      this.addressId = e.addressId;
      this.app = e.app;
      this.$handleTokenFormNative(e);
    },
    onShow() {
      this.getAddressList();
      uni.$off('editAddress');
      uni.$once('editAddress', (data) => {
        console.log(data);
        this.show = data.show;
      });
      // address
    },

    onUnload() {
      // #ifdef APP-PLUS
      if (this.app) {
        plus.runtime.quit();
      }
      // #endif
    },

    methods: {
      // 选择地址
      select(item) {
        console.log('========================================');
        if (this.from == 'goods') {
          uni.setStorageSync('address', item);
          let pages = getCurrentPages();
          console.log(pages);
          if (pages.length < 2) {
            return false;
          }
          let prevPage = pages[pages.length - 2];
          prevPage.needReGetOrder = true;
          uni.$emit('updateAddress', {
            plate: item
          });
          uni.navigateBack();
        }
      },
      // 编辑地址
      editAddress(id) {
        console.log(id);
        uni.navigateTo({
          url: '../edit/edit?address_id=' + id
        });
      },
      // 删除地址
      removeAddress(id) {
        let _this = this;
        uni.showModal({
          title: '提示',
          content: '您确定要移除当前收货地址吗?',
          success: async function (o) {
            if (o.confirm) {
              const resdata = await $http({
                url: 'zx/order/deleteOrderAddress/' + id
              });
              if (resdata) {
                _this.getAddressList();
              }
            }
          }
        });
      },
      async getAddressList() {
        let _this = this;
        const resdata = await $http({
          url: 'zx/order/userAddressList'
        });
        if (resdata) {
          // console.log(resdata)
          _this.list = resdata.data;
          if (_this.addressId != '' && _this.show == true) {
            let arr = _this.list.filter((ele) => ele.addressId == _this.addressId);
            console.log(arr[0]);
            this.getAddress(arr[0]);
          }
        }
      },
      getAddress(item) {
        if (this.from == 'goods') {
          uni.setStorageSync('address', item);
          let pages = getCurrentPages();
          console.log(pages);
          if (pages.length < 2) {
            return false;
          }
          let prevPage = pages[pages.length - 2];
          prevPage.needReGetOrder = true;
          uni.$emit('updateAddress', {
            plate: item
          });
        }
      }
    }
  };
</script>

<style lang="scss" scoped>
  .confirm_btn {
    margin-top: 15upx;
    height: 78upx;
    border-radius: 49upx;
    line-height: 78upx;
    width: 90%;
    position: fixed;
    left: 5%;
    bottom: 50upx;
    color: #fff;
    background-color: #339378;
    padding: 0;
  }
  .address_list_css {
    height: 100vh;
    overflow-y: scroll;
    padding-bottom: 200rpx;
  }
  .address-header {
    padding: 32rpx 0;
    border-bottom: 1rpx solid #f5f5f5;
    width: 628rpx;
    margin: 0 auto;
    position: relative;
    .addRess_button {
      position: absolute;
      right: 0;
      top: 68rpx;
      .box-32 {
        width: 32rpx;
        height: 32rpx;
        margin-left: 56rpx;
      }
    }
    .width_css {
      width: 490rpx;
    }
  }
  .address-header:last-child {
    border-bottom: none;
  }
  .disple_inline {
    display: inline-block;
  }

  .empty-content {
    height: calc(100vh - 140rpx);
    display: flex;
    align-items: center;
    justify-content: center;
  }
</style>
