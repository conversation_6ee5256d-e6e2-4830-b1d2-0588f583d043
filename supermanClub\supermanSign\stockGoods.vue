<!-- 进货单 -->
<template>
  <view class="plr-30">
    <view class="bg-ff plr-30 pb-40 radius-15 positionRelative" :style="{ height: useHeight + 'rpx' }">
      <view class="t-c">
        <image :src="imgHost + 'dxSelect/three/icon/jh-icon.png'" class="img_icon"></image>
      </view>
      <view class="pb-30 c-00 f-30">
        <view class="mt-40 c-66">
          超人码数量：
          <view class="mt-20 c-00">
            <input class="choose" v-model="quantity" type="number" placeholder="请输入" @blur="getNumber" placeholder-style="color:#999" />
          </view>
        </view>
      </view>

      <view class="tips" :style="{ height: svHeight + 'px' }">
        <button class="phone-btn" @click.once="confirm">确定</button>
      </view>
    </view>

    <!-- 无货弹窗 -->
    <uni-popup ref="tipDialog" type="center" :maskClick="false" :classBG="'white'">
      <view class="dialogBG">
        <view class="reviewCard_box positionRelative">
          <view class="cartoom_image">
            <image :src="dialog_iconUrl" mode="widthFix"></image>
          </view>
          <view class="review_close" @click="closeDialog">
            <uni-icons type="clear" size="28" color="#B1B1B1"></uni-icons>
          </view>
          <view class="reviewCard">
            <view class="reviewTitle bold">提示</view>

            <view class="dialogContent">您的上级俱乐部剩余超人码不足，是否确定提交进货单。</view>

            <view class="flex-s plr-30">
              <view class="common_btn_two common_btn_orange_active" @click="confirmStock">确定</view>
              <view class="common_btn_two common_btn_orange" @click="cancle">取消</view>
            </view>
          </view>
        </view>
      </view>
    </uni-popup>

    <!-- 进货成功 -->
    <uni-popup ref="popup" type="center" @change="changePopup">
      <view class="t-c bg-ff content">
        <u-icon name="checkmark-circle-fill" color="#2DC032" size="136"></u-icon>
        <view class="mt-30">进货申请成功，等待上级的处理！</view>
      </view>
    </uni-popup>
  </view>
</template>

<script>
  import Util from '@/util/util.js';
  const Supermanjs = require('@/common/superman.js');
  export default {
    data() {
      return {
        useHeight: 0, //除头部之外高度
        svHeight: 50,
        quantity: '', // 进货数量
        imgHost: getApp().globalData.imgsomeHost,
        dialog_iconUrl: Util.getCachedPic('https://document.dxznjy.com/dxSelect/dialog_icon.png', 'dialog_icon_path')
      };
    },

    onLoad(e) {},
    onReady() {
      let that = this;
      uni.getSystemInfo({
        //调用uni-app接口获取屏幕高度
        success(res) {
          // 可使用窗口高度，将px转换rpx
          let h = res.windowHeight * (750 / res.windowWidth);
          that.useHeight = h - 65;
        }
      });
    },

    onShow() {
      // this.$refs.popup.open()
    },

    methods: {
      getNumber(e) {
        console.log(e);
        this.quantity = e.detail.value;
      },

      // 确定进货按钮执行
      async confirm() {
        let _this = this;
        if (_this.quantity <= 0) {
          _this.$util.alter('超人码数量需要大于0哦');
          return;
        }
        // 判断上级超人码是否足够
        uni.showLoading();
        let isSufficient = await Supermanjs.ifEnoughCodeNumMerchant(this.quantity);
        uni.hideLoading();
        // 超人码数量足够
        if (isSufficient) {
          uni.showLoading();
          let isApplyOutSuccess = await Supermanjs.applyOutCode(this.quantity);
          uni.hideLoading();
          if (isApplyOutSuccess) {
            // 进货申请成功进入超人码管理界面
            // uni.redirectTo({
            // 	url:"/supermanClub/supermanSign/sign"
            // })
            this.$refs.popup.open();
            setTimeout(() => {
              this.$refs.popup.close();
            }, 1000);

            setTimeout(() => {
              uni.navigateBack();
            }, 500);
          } else {
            _this.$util.alter('进货失败，请稍后再试');
          }
        } else {
          // 超人码数量不够
          _this.$refs.tipDialog.open();
        }
      },

      // 取消
      cancle() {
        uni.redirectTo({
          url: '/supermanClub/supermanSign/sign'
        });
      },

      closeDialog() {
        this.$refs.tipDialog.close();
      },

      // 确定进货
      confirmStock() {
        this.CodeNumMerchant();
      }
    }
  };
</script>

<style lang="scss" scoped>
  .img_icon {
    width: 328rpx;
    height: 310rpx;
    margin: 120rpx auto;
  }

  /deep/.phone-btn {
    width: 586rpx;
    height: 90rpx;
    position: absolute;
    bottom: 40rpx;
    left: 54rpx;
    line-height: 90rpx;
    border-radius: 45rpx;
    font-size: 30rpx;
    color: #fff !important;
    background: linear-gradient(to bottom, #88cfba, #1d755c);
  }

  .choose {
    height: 90rpx;
    line-height: 90rpx;
    padding: 0 30rpx;
    border-radius: 45rpx;
    border: 1px solid #c8c8c8;
  }

  .dialogBG {
    width: 100%;
    height: 100%;
  }

  /* 21天结束复习弹窗样式 */
  .reviewCard_box {
    width: 670rpx;
    /* height: 560rpx; */
    position: relative;
  }

  .reviewCard_box image {
    width: 100%;
    height: 100%;
  }

  .reviewCard {
    width: 100%;
    height: 100%;
    background: #ffffff;
    border-radius: 24upx;
    padding: 30upx 30upx 60upx 30upx;
    box-sizing: border-box;
    position: relative;
  }

  .cartoom_image {
    width: 420rpx;
    position: absolute;
    top: -250rpx;
    left: 145rpx;
    z-index: -1;
  }

  .review_close {
    position: absolute;
    top: 20rpx;
    right: 20rpx;
    z-index: 1;
  }

  .reviewTitle {
    margin-top: 20rpx;
    width: 100%;
    text-align: center;
    font-size: 34upx;
    display: flex;
    justify-content: center;
  }
  .dialogContent {
    padding: 0 30upx;
    box-sizing: border-box;
    margin: 40upx 0 60upx 0;
    font-size: 32upx;
    line-height: 45upx;
    text-align: center;
  }

  // 短信
  .content {
    color: #333;
    border-radius: 15rpx;
    padding: 90rpx 20rpx;
    width: 600rpx;
  }

  /deep/.u-icon--right {
    justify-content: center;
  }
</style>
