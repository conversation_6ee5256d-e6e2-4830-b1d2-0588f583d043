<template>
	<page-meta :page-style="'overflow:'+(rollShow?'hidden':'visible')"></page-meta>
	<view class="plr-30">
		<form id="#nform">
			<view >
				<view class="p-30 bg-ff radius-15">
					<view class="bold f-32 mb-5 flex-a-c"><view class="vertical-line mr-30"></view>购买信息</view>
					<view class="information ptb-30 borderB">
						<view class="f-30"><text class="c-f0 mr-10 bold">*</text>超人俱乐部等级：</view>
						<view class="phone-input ptb-5 positionRelative" @click="changeData">
							<view class="icon_x">
								 <u-icon name="arrow-right" color="#999" size="32"></u-icon>
							</view>
							<uni-data-select ref="mySelect" :clear="false" class="uni-select c-99 w100"
								v-model="applyData.gradeLevel" :localdata="rangeGrade" @change="choose"></uni-data-select>
						</view>
					</view>
					<view class="information ptb-30 c-66 borderB">
						<view class="f-30"><text class="c-f0 mr-10">*</text>超人码：</view>
						<view class="phone-input" style="padding-left: 0;">
							{{applyData.codeNum || 0}}个超人码
						</view>
					</view>
					<!-- <view class="information ptb-20 borderB c-66">
						<view class="f-30 c-66"><text class="c-f0 mr-10">*</text>价格：</view>
						<view class="phone-input" style="padding-left: 0;">
							{{applyData.price || 0}}
						</view>
					</view> -->
					<view class="c-fea f-30 mt-30">同步开通学习超人权限</view>
				</view>
				
				<view class="positionRelative mt-30 p-30 bg-ff radius-15" :style="{height: useHeight+'rpx'}">
					<view class="f-32 bold flex-a-c"><view class="vertical-line mr-30"></view>申请人信息</view>
					<view class="flex-a-c ptb-30 borderB mt-20">
						<view class="f-30"><text class="c-f0 mr-10 bold">*</text>姓名：</view>
						<input :value="remark" @input="onInput" placeholder="请输入姓名" placeholder-style="color:#999;font-size:30rpx;"/>
					</view>
					<view class="c-fea f-30 mt-30">请输入真实姓名，便于后续业务对接</view>
					
					<button class="submit-to" @click="submitTo">提交</button>
				</view>
				
				
			</view>
			
			<!-- <view class="bg-ff radius-15 plr-20 ptb-30 mt-30 f-30 positionRelative" :style="{height: useHeight+'rpx'}">
				<view class="bold f-32">供应方信息</view>
				<view class="flex_x mt-40"><text class="c-f0 mr-10">*</text>是否有供应方
				    <view class="ml-20 flex_x">
				        <uni-data-checkbox v-model="applyData.isHaveSupplier" :localdata="range" selectedColor="#2E896F" @change="change"></uni-data-checkbox>
				    </view>
				</view>
				<button class="submit-to" @click="submitTo">提交</button>
			</view> -->
		</form>
		
		<uni-popup ref="codePopup" type="center" @change="changePopup">
		    <view>
		        <image class="cartoom_image" :src="dialog_iconUrl" mode="widthFix"></image>
		    </view>
		    <view class="review_close" @click="incoChange">
		        <uni-icons type="clear" size="26" color="#B1B1B1"></uni-icons>
		    </view>
		    <view class="f-30 ptb-50 positionRelative bg-ff radius-15 plr-80">
		        <view class="f-34 t-c bold mb-40">提交成功</view>
				<view class="t-c">
					<image class="imgs" :show-menu-by-longpress="true" mode="widthFix" :src="codeImg"></image>
				</view>
		        <view class="f-30 c-33 mt-40 lh-50 width500">
					申请提交成功，即将安排工作人员与您
					联系，请保持电话畅通。或者长按识别
                    二维码添加客户经理企微进行了解！
		        </view>
		    </view>
		</uni-popup>
	</view>
</template>

<script>
	const { $showError, $showMsg, $http, $navigationTo,} = require("@/util/methods.js")
	import Util from '@/util/util.js'
	const {
		httpUser
	} = require('@/util/luch-request/indexUser.js')
	export default {
		data() {
			return {
				allGradeLevel:[],
				applyData:{},
				chooseIndex:0,
				// 俱乐部等级
				rangeGrade: [],
				range: [
					{ text: '是', value: 0 }, 
					{ text: '否', value: 1 },
				],
				useHeight: 0,   //除头部之外高度
				rollShow:false, //禁止滚动穿透
				
				codeImg:"",     // 二维码
				shareId:"",		// 分享人id
				remark:"",      // 姓名
                
                dialog_iconUrl:Util.getCachedPic("https://document.dxznjy.com/dxSelect/dialog_icon.png","dialog_icon_path"),
			};
		},
		onReady() {
		    uni.getSystemInfo({
		        success: (res) => {
		            // 可使用窗口高度，将px转换rpx
		            let h = (res.windowHeight * (750 / res.windowWidth));
		            this.useHeight = h - 570;
		        }
		    })
		},
		onLoad(e) {
			// let invitationInfo = uni.getStorageSync('invitationInfo');
			// if(invitationInfo){
			// 	this.shareId = invitationInfo.userId;
			// }
			this.shareId = e.shareId;
			this.getAllLevel();
		},
		// onShow() {
		// },
		methods:{
			// 获取俱乐部等级
			async getAllLevel(){
				let _this = this;
				_this.applyData={};
				const res = await $http({
					url: 'zx/common/moisteningSettingList',
					method: 'get'
				})
				console.log(res);
				if(res){
					_this.allGradeLevel = res.data;
					res.data.forEach(item=>{
						let d = {
							text:item.gradeName,
							value:item.gradeLevel
						}
						_this.rangeGrade.push(d);
					})
					_this.applyData = _this.allGradeLevel[_this.chooseIndex];
				}
			},
			
			
			// 禁止滚动穿透
			changePopup(e) {
				this.rollShow = e.show
			},
			// 打开弹框
			open() {
			    this.$refs.codePopup.open();
			},
			//关闭弹窗
			incoChange() {
			    this.$refs.codePopup.close()
			},
			
			changeData(){
				this.$refs.mySelect.show=false;
			},
			
			// 选择俱乐部等级
			choose(e){
				this.applyData.grade = e;
				let _this = this;
				_this.allGradeLevel.forEach((item,index)=>{
					if(item.gradeLevel==e){
						_this.chooseIndex=index;
					}
				})
				_this.applyData = _this.allGradeLevel[_this.chooseIndex];
			},
			
			//是否用供应方
			change(e){
				console.log('e:',e);
				this.applyData.isHaveSupplier = e.detail.value;
			},
			
			// 提交
			async submitTo(){
				let _this = this;
				console.log(_this.applyData.isHaveSupplier)
				if (_this.remark=="") {
				    _this.$util.alter('请填写姓名');
					return
				}
				
				if (_this.remark.length>6) {
				    _this.$util.alter('姓名不可超过六个字符');
					return
				}
				
				let param = {
					auditGradeLevel:_this.applyData.gradeLevel,
					isHaveSupplier:_this.applyData.isHaveSupplier,
					shareId:_this.shareId,
					realName:_this.remark
				}
				const res = await $http({
					url: 'zx/merchant/applyMerchant',
					method: 'POST',
					data: param
				})
				uni.hideLoading();
				if(res){
					_this.getQrcode();
					_this.$refs.codePopup.open();
				}
			},
			//获取客服二维码
			async getQrcode(){
				let res = await $http({
					url: 'zx/common/getKfCodeByType',
					data:{
						type:1
					}
				})
				this.codeImg= res.data.codeUrl;
			},
			
			
			onInput(e) {
				console.log('11111111111111111')
				this.remark = e.detail.value;
				// if (e.detail.value.length > 0) {
				// 	this.showClearIcon = true;
				// } else {
				// 	this.showClearIcon = false;
				// }
			},
		}
	}
</script>

<style lang="scss">
.information {
        display: flex;
        align-items: center;
    }

    .phone-input {
		flex: 1;
        background: #fff;
        border-radius: 8rpx;
        height: 70rpx;
        font-size: 28rpx;
        color: #999;
        display: flex;
		align-items: center;
    }

    .uni-list-cell-db {
        background: #fff;
        border-radius: 8rpx;
        width: 100%;
        height: 70rpx;
        font-size: 28rpx;
        color: #999;
        display: flex;
        padding-left: 30rpx;
        align-items: center;
    }
   
    /deep/.submit-to {
		position: absolute;
		bottom: 40rpx;
		width: 91.4%;
        height: 90rpx;
        line-height: 90rpx;
        border-radius: 45rpx;
        font-size: 30rpx;
        color: #fff !important;
        background: linear-gradient(to bottom, #88CFBA, #1D755C);
    }

    /deep/.uni-select {
        border: none !important;
    }

    /deep/.uni-select--disabled {
        background-color: #fff;
    }

    /deep/.uni-stat__select {
        height: 60rpx !important;
    }

    .borderB {
        border-bottom: 1px solid #f2f2f2;
    }

    /deep/.uni-icons {
        color: #fff !important;
    }
	
	/deep/.uni-select__input-placeholder{
		color: #999 !important;
		font-size: 28rpx !important;
	}
	
	.icon_x{
		position: absolute;
		top: 28rpx;
		right: 10rpx;
		z-index: 1;
	}
	
	.flex_x {
	    display: flex;
	    align-items: center;
	}
	
	/deep/.checklist-box{
		margin-left: 50rpx !important;
	}
	/deep/.checklist-group{
		margin-top: 8rpx;
	}
	
	/deep/.uni-select__selector{
		border-radius: 0 !important;
		padding: 0 !important;
		width: 96% !important;
	}
	
	// /deep/.uni-select__selector-item{
	// 	padding: 0rpx 10rpx 0rpx 40rpx !important;
	// 	font-size: 30rpx !important;
	// 	line-height: 66rpx !important;
	// }
	
	/deep/.uni-select__selector-item:hover{
		color: #000 !important;
		font-weight: bold !important;
		background-color: #F4F4F4 !important;
	}
	
	
	// 弹出层
	.cartoom_image {
	    width: 420rpx;
	    position: absolute;
	    top: -250rpx;
	    left: 145rpx;
	    z-index: -1;
	}
	.review_close {
	    position: absolute;
	    top: 20rpx;
	    right: 20rpx;
	    z-index: 1;
	}
	
	.flex_s {
	    text-align: center;
	    vertical-align: middle;
	}
	
	.imgs{
		width: 340rpx;
		height: 340rpx;
		padding: 30rpx;
		border-radius: 16rpx;
		border: 1px solid #D9D9D9;
	}
	
	.width500{
		width: 500rpx;
	}
	
	/deep/.uniui-clear{
		color: #999 !important;
	}
	
	
	/deep/ .uni-select__selector{
		top: calc(100% + -5rpx) !important;
	}
	
	.vertical-line{
		width: 6rpx;
		height: 30rpx;
		background-color: #2E896F;
	}
</style>
