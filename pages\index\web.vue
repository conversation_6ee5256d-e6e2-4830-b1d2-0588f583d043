<template>
  <view>
    <web-view :webview-styles="webviewStyles" :src="tempUrl"></web-view>
  </view>
</template>

<script>
  export default {
    data() {
      return {
        tempUrl: '',
        webviewStyles: {
          progress: {
            color: '#FF3333'
          }
        }
      };
    },
    onLoad: function (option) {
      if (option.url != undefined) {
        console.log(option, '用户隐私协议');
        //#ifdef APP-PLUS
        this.tempUrl = decodeURIComponent(option.url);
        uni.setNavigationBarTitle({
          title: option.name
        });
        //#endif
        //#ifdef MP-WEIXIN
        this.tempUrl = decodeURIComponent(option.url);
        //#endif
        // #ifdef H5
        this.tempUrl = option.url;
        // #endif
      }
    },
    methods: {}
  };
</script>

<style></style>
