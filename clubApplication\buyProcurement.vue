<template>
	<view class="plr-35" style="box-sizing: border-box;">
		<view style="display: flex;justify-content: space-between;" class="mb-65 lh-42 mt-50">
			<view class="">
				订购内容:
			</view>
			<view class="">
				<radio-group @change="radioChange">
					<label class="radio">
						<radio value="2" checked="true" style="transform:scale(0.7)" />合伙人邀请码
					</label>
					<label class="radio" v-if="type=='2'">
						<radio value="1" style="transform:scale(0.7)" />俱乐部邀请码
					</label>
				</radio-group>
			</view>
		</view>
		<view style="display: flex;justify-content: space-between;" class="mb-65 lh-42">
			<view class="">
				订单单价:
			</view>
			<view class="">
				{{orderPrice}}
			</view>
		</view>
		<view style="display: flex;justify-content: space-between;" class="mb-65 lh-42">
			<view class="">
				购物数量:
			</view>
			<view class="">
					<uni-number-box @change="changeValue" />
			</view>
		</view>
		<view style="display: flex;justify-content: space-between;" class="mb-65 lh-42">
			<view class="">
				总金额:
			</view>
			<view class="">
				{{totalPrice}}
			</view>
		</view>
		<view class="flex-a-c flex-x-a buton">
		<view class="buyButton bg-ff endbutton" @tap="endBuy">
			取消
		</view>
		<view class="buyButton fetchbutton" @tap="fetchBuy">
			提交申请
		</view>
		</view>
	</view>
</template>

<script>
	const {
		$http,
	} = require("@/util/methods.js")
	export default{
		data(){
			return{
				procureContent:'',
				amount:'',
				procureNum:'',
				totalAmount:'',
				clubPurchasePrice:'',
				brandPurchasePrice:'',
				totalPrice:'',
				type:'',
				orderPrice:'',
				procureNum:1,procureContent:'partner_invitation_code',
				userCode:''
				
			}
		},
		onLoad(option) {
			if(option!=null){
				this.type=option.type
				this.userCode=option.userCode
			}
		},
		onShow() {
		this.fetchPrice()	
		},
		methods:{
			async fetchPrice(type){
				const res = await $http({
				    url: 'zxAdminCourse/web/commissionMeal/purchaseInfoByType',
					data:{
						invitationCodeType:type||2
					}
				})
				if(res){
					this.brandPurchasePrice=res.data.data.brandPurchasePrice
					this.clubPurchasePrice=res.data.data.clubPurchasePrice
					if(this.type=='1'){
						this.orderPrice=this.clubPurchasePrice
						this.totalPrice=this.orderPrice
					}
					if(this.type=='2'){
						this.orderPrice=this.brandPurchasePrice
						this.totalPrice=this.orderPrice
					}
				}
			},
			radioChange(val){
				if(val.detail.value=='2'){
					this.fetchPrice(2)	
					this.procureContent='partner_invitation_code'
				}
				if(val.detail.value=='1'){
					this.fetchPrice(1)	
					this.procureContent='club_invitation_code'
				}
			},
			changeValue(value){
				this.procureNum=value
				this.totalPrice= (value*this.orderPrice).toFixed(2)
			},
			endBuy(){
				uni.navigateBack()
			},
			async fetchBuy(){
				const res = await $http({
				    url: 'zxAdminCourse/web/invitation/applyCode',
					method: 'POST',
					data:{
						procureNum:this.procureNum,
						totalAmount:this.totalPrice,
						procureContent:this.procureContent,
						amount:this.orderPrice,
						userCode:this.userCode
					}
				})
				if(res){
					uni.showToast({
						title:'购买成功',
						icon:'success'
					})
					uni.navigateBack()
				}
			}
		}
	}
</script>

<style lang="scss" scoped>
	.buton{
		width: 100%;
		position: fixed;
		left: 0;
		bottom: 48rpx;
	}
	.buyButton{
		width: 40%;
		line-height: 74rpx;
		text-align: center;
		border-radius: 38rpx;
		border: 2rpx solid #339378;
	}
	.endbutton{
		color: #339378;
	}
	.fetchbutton{
		background-color: #339378;
		color: #FFF;
	}
</style>