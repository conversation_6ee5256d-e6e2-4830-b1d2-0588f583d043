import {httpUser} from '@/util/luch-request/indexUser.js' // 全局挂载引入

// 伦次报告学情走势(分值趋势)
function  turnGradeStudy(roundId) {
	return new Promise(function(resolve,reject){ 
		httpUser.get(`znyy/stats/review/getFunReviewStats?roundId=${roundId}`).then( (res) => {
			if(res.data.success){		
				resolve( res.data.data.data)
			}else{
				uni.showToast({
					title:result.data.message,
					icon:"none"
				})
				reject();
			}											
		})
	})
}

// 关卡报告分组正确率
const roundReport = (roundId,scheduleCode,level)=>{
	let showData;
	return new Promise(function(resolve,reject){
		httpUser.get(`znyy/stats/review/getLevelReport?roundId=${roundId}&scheduleCode=${scheduleCode}&level=${level}`).then((res) => {
			if(res.data.success){	
				showData = res.data.data.data;	
				resolve(showData)
			}else{
				uni.showToast({
					title:result.data.message,
					icon:"none"
				})
				reject();
			}											
		})
	})	
	
}

module.exports ={
	turnGradeStudy:turnGradeStudy,
	roundReport:roundReport
};