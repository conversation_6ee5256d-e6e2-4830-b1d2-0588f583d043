<template>
	<view class="paybottom" >
		<view class="payprice">{{payprice}}</view>
		<view class="confirmpay" @click="confirmpay">确认支付</view>
	</view>
</template>

<script>
	/**
	 * 支付价格固定底部
	 
	 */
	export default {
		name: 'UniBottom',
		props: {payprice:''},
		methods: {
			confirmpay() {
				this.$emit('confirmpay')
			}
		}
	}
</script>

<style scoped>
	.paybottom { width: 670upx;padding: 40upx; position: fixed;bottom: 0;background: #FFFFFF;font-size: 52upx;font-weight: bold; line-height: 86upx;display: flex;justify-content: space-between; }
	.payprice { color: #01c175; }
	.confirmpay { display: inline-block; padding: 0 80upx;height: 86upx;line-height: 86upx;color: #FFFFFF;font-size: 32upx;background:#23d589;border-radius: 40upx; }
	
</style>