<template>
  <view class="contaiter">
    <u-navbar title="往期复习" @leftClick="leftClick"></u-navbar>
    <view v-if="selectedType !== 'grammar'" class="top">
      <text class="timeFn">日期</text>
      <text class="wordsFn">复习单词数</text>
      <text>正确率</text>
      <text>查看详情</text>
    </view>
    <view class="topG" v-if="selectedType === 'grammar'">
      <text class="ml-30">日期</text>
      <text style="margin-left: 33%">复习讲义数</text>
      <text class="mr-20">查看详情</text>
    </view>
    <!-- 单词复习 -->
    <view v-if="selectedType !== 'grammar'" class="history-item" v-for="(item, index) in historylist" :key="index">
      <view class="list">
        <text class="list-timer">{{ item.studyData }}</text>
        <text>{{ item.wordCount }}</text>
        <text>{{ item.rate }}%</text>
        <image src="https://document.dxznjy.com/applet/newimages/xiangqing.png" mode="" @click="goUrl(item)" class="list-img"></image>
      </view>
    </view>
    <!-- 语法复习 -->
    <view v-if="selectedType === 'grammar'" class="flex-x-a" v-for="(item, index) in historyGralist" :key="index">
      <view class="list">
        <view class="flex-a-c listBorder">
          <view>{{ item.reviewDate }}</view>
        </view>
        <text class="t-r mr-60">{{ item.reviewNum }}</text>
        <image src="https://document.dxznjy.com/applet/newimages/xiangqing.png" mode="" @click="goGraUrl(item.id)" class="list-img"></image>
      </view>
    </view>
    <view class="zanwu" :status="loadingType">没有更多数据了~</view>
  </view>
</template>

<script>
  import uniLoadMore from '@/components/uni-load-more/uni-load-more.vue';
  export default {
    components: {
      uniLoadMore
    },
    data() {
      return {
        loadingType: 'more', //加载更多状态
        historylist: [],
        historyGralist: [],
        pageindex: 1,
        pageSize: 20,
        studentCode: '',
        selectedType: '',
        listData: [],
        antiForgetting: null,
        reviewType: 0
      };
    },
    methods: {
      leftClick() {
        console.log('leftClick', this.antiForgetting);
        // if(this.antiForgetting == 1){
        //   uni.navigateBack({
        //     delta: 1
        //   })
        // }
        //  else{
        uni.redirectTo({
          url: '/antiAmnesia/review/index?buttonclickName=' + '&buttonClick=' + encodeURIComponent(this.studentCode) + '&merchantCode=&deliverMerchant=' + '&selectedType=grammar'
        });
        // }
      },
      async loadMyMember(type = 'add', loading) {
        if (type === 'add') {
          if (this.loadingType === 'nomore') {
            return;
          }
          this.loadingType = 'loading';
        } else {
          this.loadingType = 'more';
        }
        if (this.selectedType === 'grammar') {
          await this.$httpUser
            .get('dyf/wap/applet/antiForgettingHistory', {
              studentCode: this.studentCode,
              // 学员 code 固定值 用于测试
              // studentCode: '6231217888',
              pageNum: this.pageindex,
              pageSize: this.pageSize
            })
            .then((result) => {
              if (result.data.data.data.length == 0) {
                this.loadingType = 'nodata';
              } else {
                if (result.data.data.data.length) {
                  this.historyGralist = [...this.historyGralist, ...result.data.data.data];
                }
                this.loadingType = this.pageindex >= result.data.data.totalPage ? 'nomore' : 'more';
              }
              if (type === 'refresh') {
                if (loading == 1) {
                  uni.hideLoading();
                } else {
                  uni.stopPullDownRefresh();
                  this.loadingType = 'nomore';
                }
              }
            });
        } else {
          var mindex = this.pageindex;
          // let result = await this.$httpUser.get(`znyy/review/query/student/word/review/${mindex}/${this.pageSize}/${this.studentCode}?type=${this.reviewType}`);
          let result = await this.$httpUser.get('znyy/review/query/student/word/review/' + mindex + '/' + this.pageSize + '/' + this.studentCode);
          if (result) {
            if (result.data.data.data.length == 0) {
              this.loadingType = 'nodata';
            } else {
              if (result.data.data.data.length) {
                this.historylist = this.historylist.concat(result.data.data.data);
              }
              this.loadingType = this.pageindex >= result.data.data.totalPage ? 'nomore' : 'more';
            }
            if (type === 'refresh') {
              if (loading == 1) {
                uni.hideLoading();
              } else {
                uni.stopPullDownRefresh();
                this.loadingType = 'nomore';
              }
            }
          }
        }

        if (type === 'refresh') {
          this.historylist = [];
          this.historyGralist = [];
        }
      },
      goUrl(item) {
        console.log(item, '单词');
        if (item.type == 0) {
          uni.redirectTo({
            url: `/antiAmnesia/antiForgetting/historyReviewReport?reviewId=${item.id}`
          });
        } else {
          uni.redirectTo({
            url: `/parentEnd/report/aiReviewReport?reviewId=${item.id}&studentCode=${this.studentCode}&history=1`
          });
        }
      },
      goGraUrl(id) {
        // console.log(id, '22')
        uni.redirectTo({
          url: `/antiAmnesia/antiForgetting/grammarReport?hanoutId=${id}`
        });
      }
    },
    onLoad(options) {
      console.log(options.antiForgetting, '1111111');
      this.studentCode = options.studentCode;
      // this.studentCode = '6231217888'
      this.selectedType = options.selectedType;
      this.antiForgetting = options.antiForgetting;
      this.reviewType = options.reviewType;
      this.loadMyMember();
    },

    onPageScroll(e) {
      if (e.scrollTop >= 0) {
        this.headerPosition = 'fixed';
      } else {
        this.headerPosition = 'absolute';
      }
    },
    onPullDownRefresh() {
      this.pageindex = 1;
      this.loadMyMember('refresh');
    },
    //加载更多
    onReachBottom() {
      this.pageindex++;
      this.loadMyMember();
    }
  };
</script>

<style>
  .contaiter {
    margin: 0 auto;
    padding-bottom: 100rpx;
    width: 690rpx;
    background: #ffffff;
    border-radius: 14rpx;
  }

  page {
    background: #f3f8fc !important;
  }

  .top {
    height: 100rpx;
    margin-top: 200rpx;
    display: flex;
    align-items: center;
    justify-content: space-around;
    font-size: 30rpx;
    border-bottom: 1px solid #e5e5e5;
    padding-left: 20rpx;
  }

  .topG {
    height: 100rpx;
    display: flex;
    margin-top: 200rpx;
    align-items: center;
    justify-content: space-between;
    font-size: 30rpx;
    border-bottom: 1px solid #e5e5e5;
    padding-left: 40rpx;
  }

  .list {
    display: flex;
    justify-content: space-around;
    align-items: center;
    font-size: 30rpx;
    border-bottom: 1px solid #e5e5e5;
    height: 100rpx;
    color: #666;
  }

  .list-img {
    width: 29rpx;
    height: 34rpx;
  }

  .list text {
    display: block;
    width: 25%;
    text-align: center;
  }

  .zanwu {
    margin: 0 auto;
    margin-top: 20rpx;
    margin-bottom: 20rpx;
    text-align: center;
    font-size: 28rpx;
    color: #b3b7ba;
  }

  .timeFn {
    width: 80rpx;
    margin-left: 40rpx;
  }

  .wordsFn {
    margin-left: 39rpx;
  }

  .list-timer {
    width: 182rpx !important;
  }

  .listBorder {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-flow: row wrap;
  }
</style>
