<template>
  <view
    class="drag-box"
    :style="{
      left: box.x + 'px',
      top: box.y + 'px',
      borderColor: isDragging ? '#4CAF50' : '#2196F3'
    }"
    @touchstart="onDragStart"
    @touchmove="onDragMove"
    @touchend="onDragEnd"
  >
    {{ value }}
  </view>
</template>

<script>
  export default {
    props: {
      value: {
        type: String,
        required: true
      },
      index: {
        type: Number,
        required: true
      },
      initialPosition: {
        type: Object,
        default: () => ({ x: 0, y: 0, width: 52, height: 47 })
      },
      isOK: {
        type: Boolean
      }
    },
    data() {
      return {
        box: { ...this.initialPosition },
        isDragging: false,
        startX: 0,
        startY: 0
      };
    },
    methods: {
      onDragStart(e) {
        this.isDragging = true;
        this.startX = e.touches[0].clientX;
        this.startY = e.touches[0].clientY;
      },
      onDragMove(e) {
        console.log('aaaa1111', this.isOK);
        if (!this.isDragging || this.isOK) return;
        const deltaX = e.touches[0].clientX - this.startX;
        const deltaY = e.touches[0].clientY - this.startY;
        this.box.x += deltaX;
        this.box.y += deltaY;
        this.startX = e.touches[0].clientX;
        this.startY = e.touches[0].clientY;

        // 限制边界
        this.box.x = Math.max(0, Math.min(this.box.x, this.windowWidth - this.box.width));
        this.box.y = Math.max(0, Math.min(this.box.y, this.windowHeight - this.box.height));
        this.$emit('drag-move', this.box, this.index);
      },
      onDragEnd() {
        this.isDragging = false;
        this.$emit('drag-end', this.box, this.index);
        setTimeout(() => {
          this.box = { ...this.initialPosition };
          this.$forceUpdate(); // Force update to re-render the component
        }, 200);
      },
      updatePosition(newPosition) {
        setTimeout(() => {
          this.box = { ...this.initialPosition };
        }, 200);
      }
    },
    mounted() {
      uni.getSystemInfo({
        success: (res) => {
          this.windowWidth = res.windowWidth;
          this.windowHeight = res.windowHeight * (750 / res.windowWidth) - 180;
        }
      });
    }
  };
</script>

<style scoped>
  .drag-box {
    position: absolute;
    height: 92rpx;
    width: 102rpx;
    box-sizing: border-box;
    text-align: center;
    line-height: 92rpx;
    border-radius: 24rpx;
    background-color: #ffffff;
    box-shadow: 0 16rpx 2rpx -2rpx rgba(97, 170, 244, 1);
    z-index: 2;
  }
</style>
