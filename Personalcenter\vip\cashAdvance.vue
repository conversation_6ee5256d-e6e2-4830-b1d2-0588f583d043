<template>
  <view class="plr-30">
    <view class="bg-ff radius-15 cash_content">
      <image v-if="applyList.verifyResult == 0" class="img" :src="imgHost + 'dxSelect/secondPhase/afoot.png'"></image>
      <image v-if="applyList.verifyResult == 2" class="img" :src="imgHost + 'dxSelect/secondPhase/error_icon.png'" />
      <view class="mt-12 f-32">{{ applyList.verifyResult == 0 ? '处理中' : '提现失败' }}</view>
    </view>
    <view class="bg-ff radius-15 mt-30 p-30 f-30 c-33" :style="{ height: useHeight + 'rpx' }">
      <view>提现金额：￥{{ applyList.withdrawAmount || '' }}</view>
      <view class="mt-30">提现手续费：¥{{ applyList.fee || '' }}</view>
      <view class="mt-30">银行卡号：{{ applyList.bankCard || '' }}</view>
      <view class="mt-30" v-if="applyList.verifyResult == 2">失败原因：{{ applyList.errorMessage || '' }}</view>
      <view class="btn" @click="goUrl">完成</view>
    </view>
  </view>
</template>

<script>
  const { $http } = require('@/util/methods.js');
  export default {
    data() {
      return {
        imgHost: getApp().globalData.imgsomeHost,
        useHeight: 0, //除头部之外高度
        withdrawId: '', // 提现详情id
        applyList: {} // 提现详情
      };
    },
    onLoad(e) {
      console.log(e);
      this.withdrawId = e.withdrawId;
    },
    onShow() {
      this.applyDetails();
    },

    onReady() {
      uni.getSystemInfo({
        success: (res) => {
          // 可使用窗口高度，将px转换rpx
          let h = res.windowHeight * (750 / res.windowWidth);
          this.useHeight = h - 470;
        }
      });
    },
    methods: {
      // 提现详情
      async applyDetails() {
        let _this = this;
        let res = await $http({
          url: 'zx/user/userWithdrawApplyDetail',
          data: {
            withdrawId: _this.withdrawId
          }
        });
        if (res) {
          console.log(res);
          _this.applyList = res.data;
          console.log(_this.applyDetails);
        }
      },

      goUrl() {
        uni.navigateBack();
      }
    }
  };
</script>

<style lang="scss" scoped>
  .cash_content {
    width: 100%;
    padding: 92rpx 0;
    text-align: center;
  }
  .img {
    width: 100rpx;
    height: 100rpx;
  }

  .btn {
    position: absolute;
    bottom: 60rpx;
    width: 630rpx;
    height: 90rpx;
    line-height: 90rpx;
    text-align: center;
    border-radius: 45rpx;
    margin: 30rpx auto 0 auto;
    background-image: linear-gradient(to bottom, #88cfba, #1d755c);
  }
</style>
