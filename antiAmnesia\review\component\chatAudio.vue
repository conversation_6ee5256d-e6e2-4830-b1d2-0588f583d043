<template>
  <view class="audioPlay" v-if="show">
    <view>
      <view class="mb-50" style="position: relative">
        <view style="position: absolute; left: 50%; transform: translateX(-50%); top: 30rpx">
          <text v-if="60 - times <= 10" class="c-66 f-28">{{ 60 - times }}'后停止录音</text>
          <!-- 语音音阶动画 -->
          <view v-else :class="['record-animate-box', { active: closeShow }]">
            <view class="voice-scale">
              <view :class="['item', { active: closeShow }]" v-for="(item, index) in 10" :key="index"></view>
            </view>
          </view>
        </view>
      </view>
      <view id="close" class="close">
        <image style="width: 110rpx; height: 110rpx" src="https://document.dxznjy.com/dxSelect/aiTraining/ai_icon_no_send.png" mode=""></image>
      </view>
      <view class="c-ff f-28 mb-20" style="text-align: center">
        <text>{{ closeShow ? '松开 取消' : '松开 发送' }}</text>
      </view>
      <view style="position: relative">
        <image
          class="audio-icon"
          :src="
            closeShow ? 'https://document.dxznjy.com/dxSelect/aiTraining/ai_icon_record_audio.png' : 'https://document.dxznjy.com/dxSelect/aiTraining/ai_icon_record_audio1.png'
          "
        ></image>
        <image
          :style="{ width: winSize.witdh + 'px' }"
          style="height: 240rpx"
          :src="closeShow ? 'https://document.dxznjy.com/dxSelect/aiTraining/ai_img_record_bg1.png' : 'https://document.dxznjy.com/dxSelect/aiTraining/ai_img_record_bg.png'"
        ></image>
      </view>
    </view>
  </view>
</template>
<!-- @submit：通知父组件，发送消息内容。
	audioXY：手指在屏幕中的位置信息。
	audioShow：是否弹出子组件
	audioTouchendShow：是否结束录制
	closeAudioShow：子组件通知父组件，结束录制，-->
<script>
  import Config from '@/util/config.js';
  // import sensors from 'sa-sdk-miniprogram';

  // 平台兼容的录音管理器
  let recorderManager = null;

  // #ifdef MP-WEIXIN || H5
  recorderManager = uni.getRecorderManager();
  // #endif

  // #ifdef APP-PLUS
  // App端使用plus.audio.getRecorder()
  // 在组件内动态创建
  // #endif

  const innerAudioContext = uni.createInnerAudioContext();
  innerAudioContext.autoplay = true;
  export default {
    name: 'chatAudio',
    props: {
      audioShow: {
        type: Boolean,
        default: true
      },
      audioXY: {
        type: Object
      },
      audioTouchendShow: {
        type: Boolean,
        default: false
      }
    },
    watch: {
      // audioShow：true录制界面显示，开始录音，false关闭。
      async audioShow(e) {
        if (e) {
          // #ifdef APP-PLUS
          // App端需要先检查录音权限
          try {
            await this.checkRecordPermission();
          } catch (error) {
            console.log('录音权限检查失败', error);
            this.$emit('closeAudioShow', 0);
            return;
          }
          // #endif

          // 开始录制
          this.status = 0;
          this.number = 1;
          this.show = true;

          // 使用统一的录音开始方法
          this.startRecord();

          // 计算录制时长，>=60 录制结束
          this.times = 1;
          this.timesInt = setInterval(() => {
            this.times++;
            if (this.times >= 60) {
              clearInterval(this.timesInt);
              this.closeShow = false;
              this.show = false;
              this.stopRecord();
              console.log('结束录制');
            }
          }, 1000);

          // 获取关闭按钮.close在屏幕中的位置信息
          this.$nextTick(() => {
            let close = uni.createSelectorQuery().in(this).select('#close');
            close
              .boundingClientRect((data) => {
                this.dom = data;
              })
              .exec();
          });
        } else {
          this.show = false;
          this.stopRecord();
        }
      },
      // 手指在屏幕中的位置，判断是否：录制中/删除
      audioXY(e) {
        let x = e.x;
        let y = e.y;
        let left = this.dom.left;
        let top = this.dom.top;
        if (x > left && x < left + this.dom.width && y > top && y < top + this.dom.height) {
          this.closeShow = true;
        } else {
          this.closeShow = false;
        }
      },
      // 手指结束触摸，通知父组件关闭弹窗，根据this.closeShow类型，判断是取消还是发送
      audioTouchendShow() {
        if (this.number == 1) {
          this.number++;
          if (this.closeShow) {
            console.log('取消录制');
            this.status = 0;
            clearInterval(this.timesInt);
            this.closeShow = false;
            this.stopRecord();
          } else {
            console.log('发送音频');
            clearInterval(this.timesInt);
            this.closeShow = false;
            this.status = 1;
            this.stopRecord();
          }
        }
        this.$emit('closeAudioShow', this.status);
      }
    },
    data() {
      return {
        baseUrl: uni.getStorageSync('baseUrl') || Config.DXHost,
        show: false, // 弹窗
        closeShow: false, // 正常/删除
        winSize: {},
        dom: {}, // 删除按钮位置
        times: 1, // 计时器
        timesInt: null, // 计时器
        status: 0, //0录制中，1录制结束。
        number: 1,
        // App端录音相关
        appRecorder: null, // App端录音对象
        isRecording: false, // 录音状态
        recordFilePath: '' // 录音文件路径
      };
    },
    mounted() {
      this.initRecorder();
      this.getSystemInfo();
    },
    methods: {
      // 初始化录音管理器
      initRecorder() {
        let that = this;

        // #ifdef MP-WEIXIN || H5
        // 小程序和H5端使用uni.getRecorderManager()
        if (recorderManager) {
          recorderManager.onStop((res) => {
            that.handleRecordStop(res);
          });
          recorderManager.onStart((res) => {
            console.log('录音开始', res);
          });
          recorderManager.onError((error) => {
            console.error('录音错误:', error);
            that.handleRecordError(error);
          });
        }
        // #endif

        // #ifdef APP-PLUS
        // App端使用plus.audio.getRecorder()
        try {
          this.appRecorder = plus.audio.getRecorder();
          if (this.appRecorder) {
            // 设置录音停止事件监听
            this.appRecorder.onStop = (res) => {
              console.log('App端录音停止:', res);
              that.recordFilePath = res;
              that.handleRecordStop({ tempFilePath: res });
            };
          }
        } catch (error) {
          console.error('App端录音初始化失败:', error);
        }
        // #endif
      },

      // 获取系统信息
      getSystemInfo() {
        uni.getSystemInfo({
          success: (res) => {
            this.winSize = {
              witdh: res.windowWidth,
              height: res.windowHeight
            };
          }
        });
      },
      // 检查录音权限（App端）
      // #ifdef APP-PLUS
      checkRecordPermission() {
        return new Promise((resolve, reject) => {
          // 判断平台
          if (uni.getSystemInfoSync().platform === 'android') {
            // Android平台权限检查
            plus.android.requestPermissions(
              ['android.permission.RECORD_AUDIO'],
              (result) => {
                if (result.granted && result.granted.length > 0) {
                  resolve(true);
                } else {
                  uni.showModal({
                    title: '权限申请',
                    content: '需要录音权限才能使用语音功能',
                    showCancel: false
                  });
                  reject(false);
                }
              },
              (error) => {
                console.log('权限申请失败：', error);
                reject(false);
              }
            );
          } else if (uni.getSystemInfoSync().platform === 'ios') {
            // iOS平台权限检查
            // iOS在录音时会自动弹出权限申请，这里直接resolve
            resolve(true);
          } else {
            // 其他平台直接通过
            resolve(true);
          }
        });
      },
      // #endif

      // 统一的录音开始方法
      startRecord() {
        console.log('开始录音');
        this.isRecording = true;
        this.recordFilePath = '';

        // #ifdef MP-WEIXIN || H5
        if (recorderManager) {
          recorderManager.start({
            format: 'mp3',
            sampleRate: 8000
          });
        }
        // #endif

        // #ifdef APP-PLUS
        if (this.appRecorder) {
          try {
            // App端录音参数
            const options = {
              filename: '_doc/audio/' + Date.now() + '.mp3',
              format: 'mp3',
              samplerate: '8000'
            };
            this.appRecorder.record(options);
          } catch (error) {
            console.error('App端录音启动失败:', error);
            this.handleRecordError(error);
          }
        }
        // #endif
      },

      // 统一的录音停止方法
      stopRecord() {
        console.log('停止录音');
        this.isRecording = false;

        // #ifdef MP-WEIXIN || H5
        if (recorderManager) {
          recorderManager.stop();
        }
        // #endif

        // #ifdef APP-PLUS
        if (this.appRecorder) {
          try {
            // App端停止录音，会触发onStop事件
            this.appRecorder.stop();
          } catch (error) {
            console.error('App端录音停止失败:', error);
            this.handleRecordError(error);
          }
        }
        // #endif
      },

      // 处理录音停止事件
      handleRecordStop(res) {
        let fileEmit = '';
        const tempFilePath = res.tempFilePath || res;

        if (this.status == 1) {
          // #ifdef MP-WEIXIN
          // 微信小程序使用wx.getFileSystemManager()
          wx.getFileSystemManager().saveFile({
            tempFilePath: tempFilePath,
            success: (res) => {
              const savedFilePath = res.savedFilePath;
              fileEmit = savedFilePath;
              this.uploadAudioFile(savedFilePath, fileEmit);
            },
            fail: (err) => {
              console.error('保存文件失败：', err);
              uni.showToast({
                title: '保存文件失败',
                icon: 'none'
              });
            }
          });
          // #endif

          // #ifdef APP-PLUS || H5
          // App端和H5端直接使用临时文件路径上传
          fileEmit = tempFilePath;
          this.uploadAudioFile(tempFilePath, fileEmit);
          // #endif
        } else {
          console.log('录音已取消');
          uni.showToast({
            title: '已取消',
            icon: 'none'
          });
        }
      },

      // 处理录音错误
      handleRecordError(error) {
        console.error('录音错误:', error);
        this.isRecording = false;
        uni.showToast({
          title: '录音失败，请重试',
          icon: 'none'
        });
        this.$emit('closeAudioShow', 0);
      },

      // 统一的音频文件上传方法
      uploadAudioFile(filePath, fileEmit) {
        const token = uni.getStorageSync('token');
        console.log(filePath, '音频文件路径');

        // 设置不同平台的请求头
        let headers = {
          'x-www-iap-assertion': token
        };

        // #ifdef MP-WEIXIN
        headers['dx-source'] = 'ZHEN_XUAN##WX##MINIAPP';
        // #endif

        // #ifdef APP-PLUS
        headers['dx-source'] = 'ZHEN_XUAN##APP##NATIVE';
        // #endif

        // #ifdef H5
        headers['dx-source'] = 'ZHEN_XUAN##H5##WEB';
        // #endif

        // 发送POST请求上传文件
        uni.uploadFile({
          url: `${this.baseUrl}zx/common/uploadFile`,
          filePath: filePath,
          name: 'file',
          formData: {
            type: 'audio/mp3'
          },
          header: headers,
          success: (uploadFileRes) => {
            try {
              let result = JSON.parse(uploadFileRes.data);
              console.log(result.data, result.data.fileUrl, '上传成功');
              this.$emit('submit', result.data.fileUrl, fileEmit);
            } catch (error) {
              console.error('解析上传结果失败：', error);
              uni.showToast({
                title: '上传失败',
                icon: 'none'
              });
            }
          },
          fail: (err) => {
            console.error('uploadFile fail: ', err);
            uni.showToast({
              title: '上传失败',
              icon: 'none'
            });
          }
        });
      }
    }
  };
</script>
<style lang="scss" scoped>
  .audioPlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 999;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: flex-end;
  }
  .content {
    position: absolute;
    left: 0;
    bottom: 100rpx;
  }
  .close {
    margin-left: 80rpx;
    width: 110rpx;
    height: 110rpx;
  }
  .red {
    background-color: #ff3435;
  }
  .black {
    background-color: #000;
  }
  .audio-icon {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    top: 40%;
    width: 28rpx;
    height: 35rpx;
  }
  /* 上方语音动画 */
  .record-animate-box {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    width: 300rpx;
    height: 140rpx;
    background-color: #ffffff;
    border-radius: 28rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s;
    &.active {
      background-color: #f56c6c;
      color: #fff;
      width: 140rpx;
    }
    /* 语音音阶 */
    .voice-scale {
      width: 60%;
      height: 40rpx;
      display: flex;
      align-items: center;
      justify-content: space-between;
      .item {
        display: block;
        background: #2db265;
        width: 4rpx;
        height: 10%;
        margin-right: 2.5px;
        float: left;
        &.active {
          background-color: #fff;
        }
        &:last-child {
          margin-right: 0px;
        }
        &:nth-child(1) {
          animation: load 1s 0.8s infinite linear;
        }

        &:nth-child(2) {
          animation: load 1s 0.6s infinite linear;
        }

        &:nth-child(3) {
          animation: load 1s 0.4s infinite linear;
        }

        &:nth-child(4) {
          animation: load 1s 0.2s infinite linear;
        }

        &:nth-child(5) {
          animation: load 1s 0s infinite linear;
        }

        &:nth-child(6) {
          animation: load 1s 0.2s infinite linear;
        }

        &:nth-child(7) {
          animation: load 1s 0.4s infinite linear;
        }

        &:nth-child(8) {
          animation: load 1s 0.6s infinite linear;
        }

        &:nth-child(9) {
          animation: load 1s 0.8s infinite linear;
        }

        &:nth-child(10) {
          animation: load 1s 1s infinite linear;
        }
      }
    }

    @keyframes load {
      0% {
        height: 10%;
      }

      50% {
        height: 100%;
      }

      100% {
        height: 10%;
      }
    }
  }
</style>
