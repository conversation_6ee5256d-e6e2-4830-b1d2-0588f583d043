<template>
	<view class="courseItem radius-20 pb-10 positionRelative" @tap="onTap">
		<view class="courseimg relative">
			<image :src="item.goodsPicUrl" @load="emitHeight" @error="emitHeight" class="wh100" mode="widthFix"></image>
			<!-- 	<view class="positionAbsolute courseTip">
				<image
					:src="item.courseLabel==1?imgHost+'dxSelect/tip_tyk.png':imgHost+'dxSelect/tip_zsk.png'"
					class="ty_img" mode="widthFix"></image>
			</view> -->
		</view>
		<view class="plr-25 mtb-20">
			<view class="bold goods_name_css twolist f-28 mb-12">{{ item.goodsName }}</view>
			<view class="font12 mtb-16 displayflex displayflexbetween">
				<view> <span class="bold f-34 color_red">￥{{item.goodsOriginalPrice}}</span>
					<span class="f-24 cB4B1B1 price_text_css">鼎币</span>
				</view>
					<view class="f-24 cB4B1B1">已兑换{{item.goodsSales}}件</view>
			</view>
		</view>
	</view>
	<!-- <view class="waterfall-item" @tap="onTap">
		<image :src="params.courseImage" mode="widthFix" @load="emitHeight" @error="emitHeight"></image>
		<view class="content">
			<view>{{params.courseName}}</view>
			<view class="money">{{params.originalPrice}}元</view>
			<view style="margin: 0 0 8rpx 0;">
				<text class="label">{{params.courseName}}</text>
			</view>
			<view class="shop-name">{{params.courseName}}</view>
		</view>
	</view> -->
</template>

<script>
	export default {
		name: "helang-waterfall",
		props: {
			item: {
				type: Object,
				default () {
					return {}
				}
			},
			tag: {
				type: String | Number,
				default: ''
			},
			index: {
				type: Number,
				default: -1
			}
		},
		data() {
			return {
				imgHost: getApp().globalData.imgsomeHost,
				identityType: uni.getStorageSync('identityType')
			};
		},
		methods: {
			// 发出组件高度信息，在此处可以区分正确和错误的加载，给予错误的提示图片
			emitHeight(e) {
				const query = uni.createSelectorQuery().in(this);
				query.select('.courseItem').boundingClientRect(data => {
					let height = Math.floor(data.height);
					this.$emit("height", height, this.$props.tag);
				}).exec();
			},
			onTap() {
				this.$emit("click", this.$props.index, this.$props.tag);
			},
			shareVip(type, item) {
				this.$emit('shareVip', type, item)
			}

		}
	}
</script>

<style lang="scss" scoped>
	.goods_name_css {
		// overflow: hidden;
		// text-overflow: ellipsis;  /* 超出部分省略号 */
		// word-break: break-all;  /* break-all(允许在单词内换行。) */  
		// display: -webkit-box; /* 对象作为伸缩盒子模型显示 */
		// -webkit-box-orient: vertical; /* 设置或检索伸缩盒对象的子元素的排列方式 */
		// -webkit-line-clamp: 2; /* 显示的行数 */
	}

	.label_css {
		padding: 1rpx 12rpx;
		background-color: #DFFFE4;
		font-size: 21rpx;
	}

	.display_inline {
		display: inline-block;
		color: #339378;
	}

	.courseItem {
		// width: 330upx;
		border-radius: 20upx;
		background-color: #fff;
		margin-bottom: 30rpx;

		.courseimg {
			width: 100%;
		}

		.courseTip {
			width: 95upx;
			height: 40rpx;
			top: 20upx;
			right: 0;
		}

		.ty_img {
			width: 95rpx;
			height: 50rpx;
		}

		.productShare {
			width: 30upx;
			height: 30upx;
		}
	}

	.price_text_css {
		display: inline-block;
		margin-left: 8rpx;
	}
	.cB4B1B1{
		color:#B4B1B1;
	}
</style>