<template>
  <view class="container">
    <view class="experience">
      <h2>{{ examList.examName }}</h2>
    </view>

    <view class="testPaper-description" v-if="examList.questionList">
      题数：{{ ansterNum }}/{{ examList.questionList && examList.questionList.length }} 考试时间剩余
      <u-count-down @finish="timeFinish" :time="timeDown * 60 * 1000" format="mm:ss"></u-count-down>
    </view>
    <!-- <liu-slide-questions :dataList="list" @submit="subData"></liu-slide-questions> -->
    <view style="margin-top: 40rpx; margin-left: 30rpx; position: relative; z-index: 99" v-for="(item, index) in examList.questionList" :key="index">
      <h2 class="radio-title">{{ index + 1 }}. [{{ formatStatus(item.questionType) }}]{{ item.questionName }}</h2>
      <u-radio-group
        @change="singleQustionChange(index)"
        v-if="item.questionType == 1 || item.questionType == 3"
        size="30"
        labelSize="30"
        v-model="item.qusAnster"
        placement="column"
      >
        <u-radio
          size="30"
          labelSize="30"
          activeColor="#43927a"
          v-for="(v, i) in item.optionList"
          :key="i"
          :label="v.questionOptionDescription + '、' + v.questionOptionContent"
          :name="JSON.stringify({ id: v.id, isAnswer: 1 })"
        ></u-radio>
      </u-radio-group>
      <u-checkbox-group @change="mutilQustionChange($event, index)" v-if="item.questionType == 2" size="30" labelSize="30" v-model="item.qusAnster" placement="column">
        <u-checkbox
          size="30"
          labelSize="30"
          activeColor="#43927a"
          :customStyle="{ marginBottom: '8px' }"
          v-for="(v, i) in item.optionList"
          :key="i"
          :label="v.questionOptionDescription + '、' + v.questionOptionContent"
          :name="JSON.stringify({ id: v.id, isAnswer: 1 })"
        ></u-checkbox>
      </u-checkbox-group>
      <!-- <view class="final-answer">
				<h2 style="color: #555555;">答题结果</h2>
				<h2 class="correctAnswer">回答正确</h2>
			</view> -->
    </view>
    <view class="footer">
      <!-- <button class="btn" :disabled="!optionListCount" @click="questionSubmit">提交</button> -->
      <u-button shape="circle" @click="questionSubmit" :customStyle="submitCustomStyle">提交</u-button>
    </view>
  </view>
</template>

<script>
  const { $http } = require('@/util/methods.js');
  export default {
    components: {},
    name: 'Question',
    data() {
      return {
        radiovalue: '',
        courseId: '',
        examList: {},
        // accumulatedIds: new Set(),
        optionListParams: {},
        timeDown: '',
        selectedCount: 0,
        uniqueIds: [],
        submitCustomStyle: {
          width: '200rpx',
          height: '72rpx',
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          background: 'linear-gradient(278deg, #2b60ff 0%, #3284ff 100%)',
          borderRadius: '240rpx',
          margin: '0 auto',
          color: '#ffffff',
          fontSize: '30rpx',
          fontWeight: '500'
          // fontSize: "32rpx",
          // color: "#fff",
          // backgroundColor: "#43927a",
          // borderRadius: "0",
          // marginBottom: "20rpx",
        },
        disabledBtn: false,
        radiolist1: [
          {
            name: '非常好',
            disabled: false
          },
          {
            name: '较好',
            disabled: false
          },
          {
            name: '一般',
            disabled: false
          },
          {
            name: '不太好',
            disabled: false
          },
          {
            name: '较差',
            disabled: false
          }
        ],
        answersQusOptionListParams: [], //已经答题数量
        singleQusArr: [],
        mutilQusArr: [],
        ansterNum: 0,
        isAllAnswer: false,
        examListTmp: {},
        isLessonFinish: false
      };
    },

    mounted() {},
    computed: {
      optionListCount() {
        return this.uniqueIds?.length === this.examList.questionList?.length;
      }
    },
    onLoad(option) {
      this.courseId = option.courseId;
      this.isLessonFinish = Boolean(option.isLessonFinish === 'false' ? false : true);
      this.getExamList();
    },

    methods: {
      async questionSubmit() {
        // ansterNum }}/{{ examList.questionList

        if (this.ansterNum < this.examList.questionList.length) {
          return uni.showToast({
            title: '请先完成答题',
            icon: 'none'
          });
        }

        this.optionListParams = this.examList.questionList.map((item) => {
          if (item.qusAnster && typeof item.qusAnster == 'string') {
            item.qusAnster = [JSON.parse(item.qusAnster)];
          }
          if (item.qusAnster instanceof Array) {
            console.log(item.qusAnster, 'item.qusAnster');

            item.qusAnster = item.qusAnster.map((v) => {
              return typeof v == 'string' ? JSON.parse(v) : v;
            });
          }

          return {
            id: item.id,
            optionList: item.qusAnster || []
          };
        });

        // const isAnswer = this.optionListParams.every(item => {
        //   return item.optionList != ''
        // })
        // console.log(isAnswer, "isAnswer");
        console.log(this.optionListParams, 'optionListParams');
        // if (!isAnswer)

        //   return uni.showToast({
        //     title: '请先完成答题',
        //     icon: 'none'
        //   })

        let certificateInfo = JSON.parse(uni.getStorageSync('certificateInfo'));
        const params = {
          examId: this.examList.examId,
          courseId: this.courseId,
          userId: uni.getStorageSync('bvAdminId') ? uni.getStorageSync('bvAdminId') : '',
          roleTag: uni.getStorageSync('roleTag') ? uni.getStorageSync('roleTag') : '',
          questionList: this.optionListParams
          // userName: certificateInfo.userName
        };
        console.log(params, '提交的参数');

        // this.disabledBtn = true
        const res = await $http({
          url: 'train/web/exam/training/handPaper',
          method: 'POST',
          data: params
        });

        if (res.code !== 20000) return uni.showToast({ title: '提交成功', icon: 'success' });
        this.disabledBtn = false;
        // 提交成功, 若当前课程所有课时均已学完，则生成证书
        if (res.data && res.data.passFlag == 1 && this.isLessonFinish) {
          this.createCert();
        }
        uni.redirectTo({
          url: `/growth/centerList/viewTestPaper?examRecordId=${res.data.examRecordId}&courseId=${this.courseId}`
        });
      },
      // 倒计时结束调用
      async autocommit() {
        const optionListParams = this.examList.questionList.map((item) => {
          if (item.qusAnster && typeof item.qusAnster == 'string') {
            item.qusAnster = [JSON.parse(item.qusAnster)];
          }
          if (item.qusAnster instanceof Array) {
            console.log(item.qusAnster, 'item.qusAnster');

            item.qusAnster = item.qusAnster.map((v) => {
              return typeof v == 'string' ? JSON.parse(v) : v;
            });
          }

          return {
            id: item.id,
            optionList: item.qusAnster || []
          };
        });

        // const isAnswer = optionListParams.every(item => {
        //   return item.optionList != ''
        // })
        // // console.log(isAnswer, "isAnswer");
        // if (!isAnswer) return uni.showToast({
        //   title: '请先完成答题',
        //   icon: 'none'
        // })

        let certificateInfo = JSON.parse(uni.getStorageSync('certificateInfo'));
        const params = {
          examId: this.examList.examId,
          courseId: this.courseId,
          userId: uni.getStorageSync('bvAdminId') ? uni.getStorageSync('bvAdminId') : '',
          roleTag: uni.getStorageSync('roleTag') ? uni.getStorageSync('roleTag') : '',
          questionList: optionListParams
          // userName: certificateInfo.userName
        };
        console.log(params, '提交的参数');

        const res = await $http({
          url: 'train/web/exam/training/handPaper',
          method: 'POST',
          data: params
        });
        console.log(res, '333');
        if (res.code !== 20000) return uni.showToast({ title: '提交成功', icon: 'success' });
        if (res.data && res.data.passFlag == 1 && this.isLessonFinish) {
          this.createCert();
        }
        uni.redirectTo({
          url: `/growth/centerList/viewTestPaper?examRecordId=${res.data.examRecordId}&courseId=${this.courseId}`
        });
      },
      // 生成证书
      async createCert() {
        let certificateInfo = JSON.parse(uni.getStorageSync('certificateInfo'));
        let certificateRole = uni.getStorageSync('certificateRole');
        const params = {
          courseId: this.courseId,
          mobile: certificateInfo.mobile,
          realName: certificateInfo.userName,
          roleTag: certificateRole
        };
        await $http({
          url: 'train/web/certificate/createCertificate',
          data: params
        });
      },
      singleQustionChange(index) {
        this.singleQusArr = [...new Set([...this.singleQusArr, index])];

        console.log('已经答单选', this.singleQusArr.length);

        this.ansterNum = this.singleQusArr.length + this.mutilQusArr.filter((v) => v).length;

        // const id = val.id;
        // 检查 id 是否已经在 uniqueIds 数组中
        // if (!this.uniqueIds.includes(id)) {
        //   // 如果不在，则将 id 添加到数组中
        //   this.uniqueIds.push(id);
        //   console.log("添加新 id:", id);
        // } else {
        //   this.uniqueIds.splice(id, 1);
        //   console.log("重复 id，不添加:", id);
        // }
      },

      mutilQustionChange(val, index) {
        console.log(val, index);

        this.mutilQusArr[index] = val.length;
        const tmp = this.mutilQusArr.filter((v) => v);
        this.ansterNum = this.singleQusArr.length + this.mutilQusArr.filter((v) => v).length;
        console.log('多选题已答', tmp.length);

        // const id = val.questionId;
        // const index = this.uniqueIds.indexOf(id);
        // if (index === -1) {
        //   // 如果不在数组中，则添加
        //   this.uniqueIds.push(id);
        //   console.log("添加新 id:", id);
        // } else {
        //   // 如果在数组中，则移除
        //   this.uniqueIds.splice(index, 1);
        //   console.log("移除 id:", id);
        // }

        // const id = val.id;
        // // // 检查 id 是否已经在 uniqueIds 数组中
        // if (!this.uniqueIds.includes(id)) {
        //   // 如果不在，则将 id 添加到数组中
        //   this.uniqueIds.push(id);
        //   // console.log("添加新 id:", id);
        // } else {
        //   this.uniqueIds.splice(id, 1)
        //   // console.log("重复 id，不添加:", id);
        // }
        // // console.log(qusAnster, 'qusAnster-------');

        // console.log(this.uniqueIds, "this.uniqueIds.length");

        // console.log('触发2', val[index].id);
        // const id = val.id;

        // // 检查当前 id 是否在 uniqueIds 数组中
        // const index = this.uniqueIds.indexOf(id);

        // if (index === -1) {
        //   // 如果 id 不在数组中，说明是选中状态，添加 id
        //   this.uniqueIds.push(id);
        //   console.log("添加新 id:", id);
        // } else {
        //   // 如果 id 已经在数组中，说明是取消选中状态，删除 id
        //   this.uniqueIds.splice(index, 1);
        //   console.log("取消选中 id:", id);
        // }

        // console.log('触发2');
        // const id = val.id;
        // const isSelected = this.uniqueIds.includes(id);
        // if (!isSelected) {
        //   // 如果未选中状态变为选中状态，则添加
        //   this.uniqueIds.push(id);
        //   console.log("添加新 id:", id);
        // } else {
        //   // 如果选中状态变为未选中状态，则移除
        //   this.uniqueIds = this.uniqueIds.filter(itemId => itemId !== id);
        //   console.log("移除 id:", id);
        // }
      },
      formatStatus(status) {
        const MAP = {
          1: '单选',
          2: '多选',
          3: '判断'
        };
        return MAP[status];
      },
      async getExamList() {
        const res = await $http({
          url: 'train/web/exam/training/paper',
          data: {
            userId: uni.getStorageSync('bvAdminId') ? uni.getStorageSync('bvAdminId') : '',
            roleTag: uni.getStorageSync('roleTag') ? uni.getStorageSync('roleTag') : '',
            // courseId:this.courseId
            courseId: this.courseId
          }
        });
        this.examList = res.data;
        this.examList.questionList.forEach((item) => {
          item.qusAnster = '';
        });
        this.timeDown = this.examList.examTimeLimit;
        console.log(this.timeDown, 'this.timeDown');

        console.log(res, '----------');

        // this.courseDetails = res.data
        this.examListTmp = JSON.parse(JSON.stringify(this.examList));
      },
      groupChange() {},

      timeFinish() {
        this.autocommit();
        console.log('倒计时结束');
      }
      // subData(data) {
      //   console.log("提交的数据：", data);
      //   uni.navigateTo({
      //     url: "/growth/centerList/viewTestPaper",
      //   });
      // },
    }
  };
</script>

<style>
  page {
    background-color: #ffffff;
  }
</style>

<style lang="scss" scoped>
  .container {
    padding-bottom: 150rpx;
  }

  .experience {
    height: 88rpx;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #555555;
    font-size: 28rpx;
  }

  .testPaper-description {
    height: 120rpx;
    display: flex;
    justify-content: center;
    align-items: center;
    // flex-wrap: wrap;
    padding: 0 32rpx;
    color: #555555;
    font-size: 24rpx;
    background-color: #f5f8fa;
  }

  .radio-title {
    color: #333333;
    font-size: 28rpx;
    font-weight: bold;
  }

  ::v-deep .u-radio {
    margin-top: 36rpx !important;
  }

  .final-answer {
    display: flex;
    align-items: center;
    height: 82rpx;
    margin-top: 40rpx;
    background-color: #ecfaf3;
    font-size: 28rpx;
  }

  .correctAnswer {
    margin-left: 20rpx;
    color: #6bae99;
  }

  .footer {
    height: 130rpx;
    width: 100%;
    position: fixed;
    z-index: 99;
    bottom: 0;
    margin: auto;
    display: flex;
    flex-direction: column;
  }

  .btn {
    width: 200rpx;
    height: 72rpx;
    display: flex;
    justify-content: center;
    align-items: center;
    background: linear-gradient(278deg, #2b60ff 0%, #3284ff 100%);
    border-radius: 240rpx;
    margin: 0 auto;
    color: #ffffff;
    font-size: 30rpx;
    font-weight: 500;
  }
</style>
