{
  "name": "parent_mall",
  "appid": "__UNI__62A3F03",
  "description": "",
  "versionName": "1.0.0",
  "versionCode": "100",
  "transformPx": false,
  "sassImplementationName": "node-sass",
  /* 5+App特有相关 */
  "app-plus": {
    "usingComponents": true,
    "nvueStyleCompiler": "uni-app",
    "compilerVersion": 3,
    "splashscreen": {
      "alwaysShowBeforeRender": true,
      "waiting": true,
      "autoclose": true,
      "delay": 0
    },
    /* 模块配置 */
    "modules": {
      "OAuth": {},
      "Payment": {},
      "Share": {},
      "VideoPlayer": {},
      "Camera": {}
    },
    /* 应用发布信息 */
    "distribute": {
      /* android打包配置 */
      "android": {
        "permissions": [
          "<uses-feature android:name=\"android.hardware.camera\"/>",
          "<uses-feature android:name=\"android.hardware.camera.autofocus\"/>",
          "<uses-permission android:name=\"android.permission.ACCESS_NETWORK_STATE\"/>",
          "<uses-permission android:name=\"android.permission.ACCESS_WIFI_STATE\"/>",
          "<uses-permission android:name=\"android.permission.CAMERA\"/>",
          "<uses-permission android:name=\"android.permission.CHANGE_NETWORK_STATE\"/>",
          "<uses-permission android:name=\"android.permission.CHANGE_WIFI_STATE\"/>",
          "<uses-permission android:name=\"android.permission.FLASHLIGHT\"/>",
          "<uses-permission android:name=\"android.permission.GET_ACCOUNTS\"/>",
          "<uses-permission android:name=\"android.permission.MODIFY_AUDIO_SETTINGS\"/>",
          "<uses-permission android:name=\"android.permission.MOUNT_UNMOUNT_FILESYSTEMS\"/>",
          "<uses-permission android:name=\"android.permission.READ_LOGS\"/>",
          "<uses-permission android:name=\"android.permission.READ_PHONE_STATE\"/>",
          "<uses-permission android:name=\"android.permission.VIBRATE\"/>",
          "<uses-permission android:name=\"android.permission.WAKE_LOCK\"/>",
          "<uses-permission android:name=\"android.permission.WRITE_SETTINGS\"/>"
        ]
      },
      /* ios打包配置 */
      "ios": {
        "dSYMs": false,
        "capabilities": {
          "entitlements": {
            "com.apple.developer.associated-domains": ["document.dxznjy.com"]
          }
        }
      },
      /* SDK配置 */
      "sdkConfigs": {
        "oauth": {
          "weixin": {
            "appid": "wx79465579ceea44ae",
            "UniversalLinks": "https://document.dxznjy.com/zhenxuanUlink/"
          }
        },
        "payment": {
          "weixin": {
            "__platform__": ["android"],
            "appid": "wx79465579ceea44ae",
            "UniversalLinks": "https://document.dxznjy.com/zhenxuanUlink/"
          }
        },
        "share": {
          "weixin": {
            "appid": "wx79465579ceea44ae",
            "UniversalLinks": "https://document.dxznjy.com/zhenxuanUlink/"
          }
        },
        "ad": {}
      }
    },
    "nativePlugins": {},
    "safearea": {
      "bottom": {
        "offset": "none"
      }
    }
  },
  /* 快应用特有相关 */
  "quickapp": {},
  /* 小程序特有相关 */
  "mp-weixin": {
    "appid": "wx9ffadc161c84c7c7",
    "setting": {
      "urlCheck": false,
      "minified": true,
      "es6": true
    },
    "usingComponents": true,
    "permission": {
      "scope.userLocation": {
        "desc": "你的位置信息将用于小程序位置接口的效果展示"
      }
    },
    "libVersion": "latest",
    "requiredPrivateInfos": ["getLocation"],
    "lazyCodeLoading": "requiredComponents",
    "optimization": {
      "subpackages": true
    }
  },
  "mp-alipay": {
    "usingComponents": true
  },
  "mp-baidu": {
    "usingComponents": true
  },
  "mp-toutiao": {},
  "uniStatistics": {
    "enable": false
  },
  "vueVersion": "2",
  "h5": {
    "template": ""
  }
}
/* 5+App特有相关 */