<template>
  <page-meta :page-style="'overflow:' + (rollShow ? 'hidden' : 'visible')"></page-meta>
  <view class="container">
    <view class="navbar">
      <image src="https://document.dxznjy.com/dxSelect/5ee17794-ee92-4b88-9e4c-ca267453f4c3.png" style="width: 100%; height: 100%"></image>
    </view>
    <view class="students">
      <view style="color: #a4a4a4; font-size: 28rpx">选择学员：</view>
      <view class="studentsInput" @click="openStudents">
        <view style="color: #a4a4a4; font-size: 28rpx">{{ studentInfo }}</view>
        <uni-icons type="right" style="color: #555"></uni-icons>
      </view>
    </view>
    <view class="selects">
      <view style="width: 400rpx; margin: 0 auto">
        <u-tabs
          :list="tabList"
          @change="tabChange"
          :activeStyle="{
            color: '#303133',
            fontWeight: 'bold',
            fontSize: '30rpx',
            transform: 'scale(1.05)'
          }"
          :inactiveStyle="{
            color: '#606266',
            fontSize: '30rpx',
            transform: 'scale(1)'
          }"
          lineColor="#3EAA8C"
          :lineWidth="30"
          :lineHeight="5"
        ></u-tabs>
      </view>
      <view class="subject">
        <view style="flex: 1">
          <picke-type ref="coursePicker" @open="openPicker(1)" :showSubjectName="showSubjectName" :options="courseOptions" @confirm="handleCourseConfirm($event, 1)"></picke-type>
        </view>
        <view style="flex: 1">
          <picke-type ref="coursePicker" @open="openPicker(2)" :showSubjectName="courseName" :options="courseOptions1" @confirm="handleCourseConfirm($event, 2)"></picke-type>
        </view>
        <view style="flex: 1">
          <picke-type ref="coursePicker" @open="openPicker(3)" :showSubjectName="paperType" :options="courseOptions2" @confirm="handleCourseConfirm($event, 3)"></picke-type>
        </view>
      </view>
    </view>
    <view style="background-color: #fff" v-if="pagesList.length <= 0">
      <u-empty mode="data" icon="http://cdn.uviewui.com/uview/empty/data.png"></u-empty>
    </view>
    <scroll-view
      v-else
      @scrolltolower="scrolltolower"
      :show-scrollbar="false"
      bounces
      :throttle="false"
      scroll-with-animation
      scroll-anchoring
      scroll-y
      enhanced
      style="height: 800rpx"
    >
      <view class="items">
        <view class="item" v-for="item in pagesList" :key="item">
          <view class="testText" @click="showFullName(item.testPaperName)">{{ item.testPaperName }}</view>
          <view style="display: flex; margin-bottom: 20rpx">
            <view class="testItmes">课程类型：{{ item.curriculumName }}</view>
            <view class="testItmes">试卷类型 {{ listTyep[item.testPaperCategory] }}</view>
          </view>
          <view style="display: flex; margin-bottom: 20rpx">
            <view class="testItmes">总分：{{ item.score }}分</view>
            <view class="testItmes">考试时长：{{ item.examTime }}分钟</view>
          </view>
          <view style="display: flex; margin-bottom: 20rpx">
            <view class="testItmes">试卷创建时间：{{ item.createTime }}</view>
          </view>
          <view class="buttons" v-if="tabIndex == 1">
            <view class="btn out" @click="goTest(4, item.testPaperId)">查看解析</view>
            <view class="btn fill" v-if="item.testPaperCategory == 3" @click="goTest(5, item.testPaperId)">查看学情报告</view>
            <!-- <view class="btn out" @click="goTest(6)">批改中</view> -->
          </view>
          <view class="buttons" v-if="tabIndex == 0">
            <view class="btn fill" v-if="item.examStatus == 0" @click="goTest(1, item.testPaperId)">查看试卷</view>
            <view class="btn out" v-if="item.examStatus == 0" @click="$noMultipleClicks(goTest, 2, item)">开始答题</view>
            <view class="btn out" v-if="item.examStatus == 1" @click="$noMultipleClicks(goTest, 3, item.testPaperId)">继续答题</view>
          </view>
        </view>
        <view v-if="no_more && pagesList && pagesList.length > 0" style="width: 100%; text-align: center">
          <u-divider text="到底了"></u-divider>
        </view>
      </view>
    </scroll-view>
    <!-- 选择学员弹窗 -->
    <uni-popup ref="popopTest" :mask-click="false" type="center" @change="changeStudent">
      <view class="dialogBG">
        <view class="reviewCard_box positionRelative">
          <view class="reviewCard">
            <view class="reviewTitle bold pb-10">开始答题</view>
            <view class="">此试卷严格限时,到达考试限制时间将自动交卷</view>
            <view class="mask-footer">
              <button class="cancel-button" @click="closeStartTest">取消</button>
              <button class="confirm-button" @click="startTestComfig">知道了</button>
            </view>
          </view>
        </view>
      </view>
    </uni-popup>
    <!-- 选择学员弹窗 -->
    <uni-popup ref="popopChooseStudent" :mask-click="false" type="center" @change="changeStudent">
      <view class="dialogBG">
        <view class="reviewCard_box positionRelative">
          <view class="cartoom_image">
            <image src="https://document.dxznjy.com/dxSelect/dialog_icon.png" mode="widthFix"></image>
          </view>
          <view class="review_close" @click="closeDialog">
            <uni-icons type="clear" size="28" color="#B1B1B1"></uni-icons>
          </view>
          <view class="reviewCard">
            <view class="reviewTitle bold pb-10">选择学员</view>
            <view class="dialogContent" @click="chooseStudentlist(item, index)" v-for="(item, index) in arrayStudent" :class="isactive == index ? 'addclass' : 'not-selected'">
              {{ item.realName + '（' + item.studentCode + '）' }}
            </view>
            <view class="mask-footer">
              <button class="confirm-button" @click="confirmStudent()">确定</button>
              <button class="cancel-button" @click="closeDialog">取消</button>
            </view>
          </view>
        </view>
      </view>
    </uni-popup>
    <uni-popup ref="namePopup" type="center">
      <view style="padding: 40rpx; font-size: 32rpx; max-width: 80vw; word-break: break-all; background-color: aliceblue; border-radius: 20px">
        {{ testName }}
      </view>
    </uni-popup>
  </view>
</template>

<script>
  import pickeType from './components/pickeType.vue';
  export default {
    data() {
      return {
        testName: '',
        noClick: true, //防抖
        testPaperCategory: '',
        disciplineId: '',
        subjectId: '',
        paperType: '试卷类型',
        courseName: '科目',
        showSubjectName: '课程大类',
        shouldOpenPicker: false,
        courseOptions: [],
        courseOptions1: [],
        courseOptions2: [],
        no_more: false,
        rollShow: false, //禁止滚动穿透,
        studentCode: '',
        studentName: '',
        studentInfo: '请选择学员',
        isactive: -1,
        listTyep: ['课堂测试', '课后习题 ', '错题带刷', '学前测试'],
        tabList: [
          {
            name: '待测评试卷',
            id: 0
          },
          {
            name: '测评记录',
            id: 1
          }
        ],
        arrayStudent: [],
        memberId: '',
        testId: 0,
        curriculumIndex: 0,
        tabIndex: 0,
        page: 1,
        pageNo: 1,
        pagesList: [],
        curriculumList: [],
        app: 0
      };
    },
    components: {
      pickeType
    },
    onLoad(e) {
      // #ifdef APP-PLUS
      console.log(e);
      this.app = e.app;
      if (e.token) {
        this.$handleTokenFormNative(e); // 从app获取token
      }
      // console.log(screen);
      // screen.orientation.addEventListener('change', () => {
      //   console.log(`方向已更改为: ${screen.orientation.type}`);
      // });
      // #endif
      this.memberId = e.memberId;
      this.studentCode = e.studentCode;
      // this.studentCode = 625042999;
      // this.memberId = '9250412555';
      this.init();
    },
    onUnload() {
      // if (this.app) {
      plus.runtime.quit();
      // }
    },
    onShow() {
      if (this.studentCode) {
        this.getStudentMathTestPaper();
      }
    },
    onReachBottom() {
      if (this.page >= Number(this.pageNo)) {
        this.no_more = true;
        return false;
      }
      this.getStudentMathTestPaper(true, ++this.page);
    },
    methods: {
      showFullName(e) {
        this.testName = e;
        this.$refs.namePopup.open();
      },
      //继续答题
      async continue(e) {
        let obj = {
          testPaperId: e,
          studentCode: this.studentCode
        };
        let { data } = await this.$httpUser.post('dyf/math/wap/studentMathTestPaper/checkInAnswerTime', obj);
        if (data.success) {
          uni.navigateTo({
            url: `/incomeDetails/testContent?id=${e}&status=3&studentCode=${this.studentCode}`
          });
        }
      },
      async startTest(e) {
        // console.log(e);
        let obj = {
          testPaperId: e.testPaperId,
          studentCode: this.studentCode
        };
        let { data } = await this.$httpUser.post('dyf/math/wap/studentMathTestPaper/checkInAnswerTime', obj);
        // console.log(data.success);
        if (data.success) {
          // console.log(e.isForce);
          if (e.isForce) {
            this.testId = e.testPaperId;
            this.$refs.popopTest.open();
          } else {
            // console.log(222);
            uni.navigateTo({
              url: `/incomeDetails/testContent?id=${e.testPaperId}&status=1&studentCode=${this.studentCode}`
            });
          }
        }
      },
      closeStartTest() {
        this.$refs.popopTest.close();
      },
      async startTestComfig() {
        this.$refs.popopTest.close();
        let obj = { testPaperId: this.testId, studentCode: this.studentCode };
        await this.$httpUser.post(`dyf/math/wap/studentMathTestPaper/startAnswer`, obj);
        uni.navigateTo({
          url: `/incomeDetails/testContent?id=${this.testId}&status=1&studentCode=${this.studentCode}`
        });
      },
      async goTest(id, e) {
        switch (id) {
          case 1:
            uni.navigateTo({
              url: `/incomeDetails/testContent?id=${e}&status=0&studentCode=${this.studentCode}`
            });
            break;
          case 2:
            this.startTest(e);
            break;
          case 3:
            this.continue(e);

            break;
          case 4:
            uni.navigateTo({
              url: `/incomeDetails/testContent?id=${e}&status=2&studentCode=${this.studentCode}`
            });
            break;
          case 5:
            uni.navigateTo({
              url: `/parentEnd/report/academicReport?studentCode=${this.studentCode}&classPlanStudyId=${e}`
            });
            break;
          case 6:
            uni.navigateTo({
              url: '/pages/index/web'
            });
            break;
        }
      },
      // 打开选择器
      async openPicker(id) {
        if (id == 1) {
          let res = await this.$httpUser.get('dyf/math/wap/common/curriculumList');
          this.courseOptions = res.data.data;
        }
        if (id == 2) {
          if (!this.subjectId)
            return uni.showToast({
              title: '请先选择课程大类',
              icon: 'none'
            });
          let res = await this.$httpUser.get('dyf/math/wap/common/selectSubjectInfo', {
            curriculumId: this.subjectId
          });
          this.courseOptions1 = res.data.data;
        }
        if (id == 3) {
          let res = await this.$httpUser.get('dyf/math/wap/common/enum/list', {
            type: 'mathPaperType'
          });
          this.courseOptions2 = res.data.data;
        }
      },

      // 确认选择事件
      handleCourseConfirm(item, id) {
        console.log(item, id);
        if (id == 1) {
          this.showSubjectName = item.enName;
          this.subjectId = item.id;
        }
        if (id == 2) {
          this.courseName = item.subjectName;
          this.disciplineId = item.id;
        }
        if (id == 3) {
          this.paperType = item.desc;
          this.testPaperCategory = item.value;
        }
        this.getStudentMathTestPaper();
      },

      scrolltolower() {
        console.log(this.page, this.pageNo, '下拉加载更多');
        if (this.page >= Number(this.pageNo)) {
          this.no_more = true;
          return false;
        }
        this.getStudentMathTestPaper(true, ++this.page);
      },
      //试卷列表
      async getStudentMathTestPaper(isPage, page) {
        console.log(page, 'page');
        page = page || 1;
        let data = {
          pageNum: page || 1,
          pageSize: 10,
          studentCode: this.studentCode,
          curriculumId: this.subjectId,
          disciplineId: this.disciplineId,
          testPaperCategory: this.testPaperCategory,
          status: this.tabIndex
        };
        let res = await this.$httpUser.get('dyf/math/wap/studentMathTestPaper/page', data);
        let list = res.data.data.data;
        if (isPage) {
          this.pagesList = [...this.pagesList, ...list];
        } else {
          this.pagesList = list;
        }
        this.pageNo = res.data.data.totalPage;
      },
      tabChange(e) {
        if (!this.studentCode)
          return uni.showToast({
            title: '请先选择学生',
            icon: 'none'
          });
        this.tabIndex = e.id;
        this.pagesList = [];
        this.showSubjectName = '课程大类';
        this.subjectId = '';
        this.courseName = '科目';
        this.disciplineId = '';
        this.paperType = '试卷类型';
        this.testPaperCategory = '';
        this.getStudentMathTestPaper();
      },
      async init() {
        let res = await this.$httpUser.get(`znyy/course/queryStudentList/1/10`, {
          memberId: this.memberId
        });
        this.arrayStudent = res.data.data.data;
        this.arrayStudent.forEach((e) => {
          if (e.studentCode == this.studentCode) {
            this.studentName = e.realName;
            this.studentInfo = `${e.realName}(${e.studentCode})`;
          }
        });
      },
      // 禁止滚动穿透
      changeStudent(e) {
        this.rollShow = e.show;
      },
      //关闭弹窗
      closeDialog() {
        this.studentCode = '';
        this.studentInfo = '请选择学员';
        this.$refs.popopChooseStudent.close();
      },
      openStudents() {
        this.$refs.popopChooseStudent.open();
      },
      //选择学生
      confirmStudent() {
        this.studentInfo = `${this.studentName}(${this.studentCode})`;
        //查询课程列表
        this.getStudentMathTestPaper();
        this.$refs.popopChooseStudent.close();
      },
      chooseStudentlist(item, index) {
        this.isactive = index;
        this.studentCode = item.studentCode;
        this.studentName = item.realName;
      }
    }
  };
</script>

<style lang="scss" scoped>
  .addclass {
    width: 100%;
    height: 70rpx;
    line-height: 70rpx;
    font-size: 30rpx;
    color: #2e896f;
    border: 1px solid #2e896f;
    border-radius: 35rpx;
  }

  .not-selected {
    width: 100%;
    height: 70rpx;
    line-height: 70rpx;
    font-size: 30rpx;
    color: #000;
    border: 1px solid #c8c8c8;
    border-radius: 35rpx;
  }

  /* 底部按钮样式 */
  .mask-footer {
    margin-top: 20px;
    display: flex;
    justify-content: space-around;
  }

  .mask-footer button {
    width: 250rpx;
    height: 80rpx;
    font-size: 30rpx;
    border-radius: 45rpx;
  }

  .buttons {
    width: 100%;
    height: 60rpx;
    display: flex;
    justify-content: flex-end;

    .btn {
      box-sizing: border-box;
      font-size: 26rpx;
      text-align: center;
      width: 235rpx;
      height: 60rpx;
      line-height: 60rpx;
      border-radius: 36rpx;
      margin-right: 30rpx;
    }

    .out {
      background-color: #fff;
      color: #428a6f;
      border: 2rpx solid #428a6f;
    }

    .fill {
      background-color: #428a6f;
      color: #fff;
    }
  }

  .items {
    height: 760rpx;
    margin-top: 20rpx;
    overflow-y: auto;

    .item {
      border-radius: 20rpx;
      background-color: #fff;
      width: 702rpx;
      padding: 30rpx;
      box-sizing: border-box;
      border-radius: 20rpx;
      margin-bottom: 20rpx;

      .testText {
        height: 42rpx;
        line-height: 42rpx;
        font-size: 30rpx;
        color: #333333;
        font-weight: bold;
        margin-bottom: 21rpx;
        width: 637rpx;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .testItmes {
        flex: 1;
        font-size: 28rpx;
        height: 40rpx;
        line-height: 40rpx;
        color: #333333;
      }
    }

    // background-color: pink;
  }

  .addclass {
    width: 100%;
    height: 70rpx;
    line-height: 70rpx;
    font-size: 30rpx;
    color: #2e896f;
    border: 1px solid #2e896f;
    border-radius: 35rpx;
  }

  /* 弹窗样式 */
  .dialogBG {
    width: 100%;
    /* height: 100%; */
  }

  .cartoom_image {
    width: 420rpx;
    position: absolute;
    top: -250rpx;
    left: 145rpx;
    z-index: -1;
  }

  .pickerStyle {
    color: #666;
    font-size: 28rpx;
    text-align: center;
  }

  .confirm-button {
    display: flex;
    justify-content: space-around;
    align-items: center;
    /* 文本垂直居中对齐 */
    background: #2e896f;
    color: #ffffff !important;
  }

  .cancel-button {
    display: flex;
    justify-content: space-around;
    align-items: center;
    /* 文本垂直居中对齐 */
    color: #2e896f !important;
    border: 1rpx solid #2e896f !important;
  }

  .dialogContent {
    box-sizing: border-box;
    font-size: 32upx;
    // line-height: 45upx;
    text-align: center;
    margin-top: 40rpx;
  }

  .review_btn {
    width: 240upx;
    height: 80upx;
    background: linear-gradient(180deg, #88cfba 0%, #1d755c 100%);
    border-radius: 45upx;
    font-size: 30upx;
    color: #ffffff;
    line-height: 80upx;
    margin: 60rpx auto 0 auto;
    justify-content: center;
    text-align: center;
  }

  .reviewCard {
    position: relative;
    width: 100%;
    height: 100%;
    background: #ffffff;
    color: #000;
    border-radius: 24upx;
    padding: 50upx 55upx;
    box-sizing: border-box;
  }

  /* 21天结束复习弹窗样式 */
  .reviewCard_box {
    width: 670rpx;
    /* height: 560rpx; */
    position: relative;
  }

  .reviewCard_box image {
    width: 100%;
    height: 100%;
  }

  .reviewTitle {
    width: 100%;
    text-align: center;
    font-size: 34upx;
    display: flex;
    justify-content: center;
  }

  .review_close {
    position: absolute;
    top: 20rpx;
    right: 20rpx;
    z-index: 1;
  }

  .container {
    background-color: #f3f8fc;
    padding: 0 24rpx;

    .navbar {
      width: 702rpx;
      height: 298rpx;
      margin-bottom: 20rpx;
    }

    .subject {
      display: flex;
      width: 618rpx;
      justify-content: space-between;
      margin: 30rpx auto;
      height: 40rpx;
    }

    .selects {
      margin-top: 20rpx;
      height: 190rpx;
      border-radius: 20rpx;
      background-color: #fff;
    }

    .students {
      display: flex;
      align-items: center;
      height: 110rpx;
      padding: 0 29rpx 0 20rpx;
      background-color: #fff;
      border-radius: 20rpx;

      .studentsInput {
        margin-left: 10rpx;
        width: 420rpx;
        display: flex;
        justify-content: space-between;
        padding: 0 40rpx 0;
        align-items: center;
        height: 70rpx;
        border-radius: 40rpx;
        border: 2rpx solid #dfdfdf;
      }
    }
  }
</style>
<style>
  .u-tabs__wrapper__nav__line {
    left: 10rpx !important;
  }
</style>
