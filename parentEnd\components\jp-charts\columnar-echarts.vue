<!-- <template>
	<view class="charts-box">
		<qiun-data-charts type="column" :opts="opts" :chartData="chartData" />
	</view>

</template>

<script>
	export default {
		props: {
			lineChartData: {
				type: Object,
				required: true
			},
		},
		data() {
			return {
				chartData: {},
				//您可以通过修改 config-ucharts.js 文件中下标为 ['column'] 的节点来配置全局默认参数，如都是默认参数，此处可以不传 opts 。实际应用过程中 opts 只需传入与全局默认参数中不一致的【某一个属性】即可实现同类型的图表显示不同的样式，达到页面简洁的需求。
				opts: {
					color: ["#FEFFFE", "#37C67D", ],
					fontColor: '#FFFFFF',
					padding: [15, 15, 0, 5],
					legend: {
						show: false // 隐藏图例
					},
					xAxis: {
						disableGrid: true,
						fontColor: '#FFFFFF'
					},
					yAxis: {
						data: [{
							min: 0
						}]
					},
					extra: {
						column: {
							type: "group",
							width: 60,
							linearType: "custom",
							seriesGap: 5,
							linearOpacity: 0.5,
							barBorderCircle: false,
							customColor: [
								"#37C67D",
								"#FEFFFE"
							]
						}
					}
				}
			};
		},
		mounted() {
			this.getServerData();
		},

		methods: {
			getServerData() {
				//模拟从服务器获取数据时的延时
				setTimeout(() => {
					//模拟服务器返回数据，如果数据格式和标准格式不同，需自行按下面的格式拼接
					let res = {
						categories: this.lineChartData.xAxisData,
						series: [{
							name: '',
							data: this.lineChartData.expectedData,
							label: {
								show: false // 设置为 false 隐藏名称
							}
						}]
					};
					this.chartData = JSON.parse(JSON.stringify(res));
				}, 500);
			},
		}
	};
</script>

<style scoped>
	/* 请根据实际需求修改父元素尺寸，组件自动识别宽高 */
	.charts-box {
		width: 100%;
		height: 210px;
	}
</style> -->

<template>
    <view>
        <jpCharts v-if="lineChartData.length>0" :list="lineChartData" :Y="Charts.Y" :X="Charts.X" :keyId="Charts.keyId"
            :width="Charts.width" :bgColor="Charts.bgColor" :height="Charts.height" :canClick="Charts.canClick"
            :x_width="Charts.x_width" :items="items" :proportion="Charts.proportion" :line="lineData"
            :scrollLeft="100"></jpCharts>
    </view>
</template>
<script>
    import jpCharts from './index.vue';
    export default {
        components: {
            jpCharts
        },
        props: {
        	lineChartData: [],
        	lineData: [], //五天线
        },
        data() {
            return {
                Charts: { //y轴配置 value在list中的键 showY是否一直显示数据 size字大小 units文字后缀
                    Y: {
                        value: 'value',
                        size: 22,
                        units: '',
                        color: '#333'
                    },
                    X: {
                        value: 'name',
                        size: 22,
                        units: '',
                        color: '#333'
                    },
                    width: 650,
                    height: 380,
                    x_width: 60,
                    proportion: 100, 
                    bgColor: '#00B72D',
                },
            }
        },
    }
</script>