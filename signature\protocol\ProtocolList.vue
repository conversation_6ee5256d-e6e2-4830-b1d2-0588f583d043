<template>
  <div class="box">
    <div class="item" v-for="item in list" :key="item.name" @click="goweburl(item)">
      <div class="">{{ item.name }}</div>
      <uni-icons type="right" size="16" color="#5a5a5a"></uni-icons>
    </div>
  </div>
</template>

<script>
  import Config from '@/util/config.js';
  export default {
    data() {
      return {
        app: 0,
        list: [],
        userList: [
          {
            url: 'https://document.dxznjy.com/applet/agreeon/useragreement.html',
            name: '用户服务协议'
          }
        ],
        memberList: [
          {
            url: 'https://document.dxznjy.com/dxSelect/fourthEdition/h5/Membership.html',
            name: '超级会员服务协议'
          }
        ],
        parentMemberList: [
          {
            url: 'https://document.dxznjy.com/dxSelect/fourthEdition/h5/parentMemberServiceRules.html',
            name: '家长会员服务协议'
          },
          {
            url: 'https://document.dxznjy.com/dxSelect/fourthEdition/h5/parentMemberBenefitsRules.html',
            name: '家长会员权益规则'
          }
          // {
          //   url: 'https://document.dxznjy.com/app/parentMember.html',
          //   name: '家长会员服务协议'
          // },
          // {
          //   url: 'https://document.dxznjy.com/app/parentMemberBenefitsRules.html',
          //   name: '家长会员权益规则'
          // }
        ]
      };
    },
    onUnload() {
      this.list = [];
      // #ifdef APP-PLUS
      if (this.app) {
        plus.runtime.quit();
      }
      // #endif
    },
    // onShow() {
    //   // #ifdef APP-PLUS
    //   // oppo 等机型可能返回不触发onunload 在onshow触发清空list缓存 todo：可能出现抖动？
    //   this.list = [];
    //   // #endif
    // },
    onLoad(e) {
      if (e.token) {
        this.app = e.app;
        this.$handleTokenFormNative(e);
      }

      this.list = [];
      let token = uni.getStorageSync('token') || '';
      if (token) {
        this.list = this.userList;
        if (Config.curUseApp == 'zx') {
          this.list.push({
            url: 'https://document.dxznjy.com/applet/agreeon/app_privacypolicy.html',
            name: '隐私协议'
          });
        } else {
          this.list.push({
            url: 'https://document.dxznjy.com/applet/agreeon/privacypolicy.html',
            name: '隐私协议'
          });
        }
        console.log('uni.getStorageSync(identityType)', uni.getStorageSync('identityType'));
        if (uni.getStorageSync('identityType') == 4) {
          this.list = [...this.list, ...this.memberList];
        }
        console.log('uni.getStorageSync(parentMemberType)', uni.getStorageSync('parentMemberType'));
        if (uni.getStorageSync('parentMemberType') == 5) {
          this.list = [...this.list, ...this.parentMemberList];
        }
      } else {
        this.list = [];
      }
    },
    methods: {
      goweburl(item) {
        console.log(item);

        uni.navigateTo({
          url: `/pages/index/web?url=${item.url}&name=${item.name}`
        });
      }
    },
    components: {},
    created() {},
    mounted() {}
  };
</script>

<style lang="scss" scoped>
  .box {
    margin: 24rpx 32rpx;
    background-color: #fff;
    padding: 14rpx 40rpx 14rpx 24rpx;
    border-radius: 16rpx;
  }
  .item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 100rpx;
    border-bottom: 3rpx solid #f5f5f5;
    font-size: 28rpx;
    color: #333333;
  }
  .item:nth-last-child(1) {
    border-bottom: 0;
  }
</style>
