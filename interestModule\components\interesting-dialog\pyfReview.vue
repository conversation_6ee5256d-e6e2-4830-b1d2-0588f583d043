<template>
  <view class="reviewCard_box">
    <!-- <view class="review_close" @click="close()">
      <uni-icons type="clear" size="28" color="#B1B1B1"></uni-icons>
    </view> -->
    <view class="interesting_popupCenter_1">
      <view class="reviewTitle bold pb-20">{{ conclusionText }}</view>

      <!-- 过关的文字 -->
      <!-- <view class="finishAnd">{{ conclusionText }}</view> -->

      <!-- 闯关数据 -->
      <view class="popup_content" v-if="finishData.length > 0">
        <view class="popup_content_item" :key="index" v-for="(item, index) in finishData">
          <view>
            <image :src="iconList[item.iconIndex]" mode="scaleToFill" />
            <text>{{ item.name }}</text>
          </view>

          <!-- <text :style="{ 'font-size': item.data.length < 10 ? '56rpx' : '45rpx' }" class="popup_content_item_right">{{ item.data }}</text> -->
          <text :style="{ 'font-size': item.iconIndex != 2 ? '56rpx' : '45rpx' }" class="popup_content_item_right">{{ item.data || '虚无' }}</text>
        </view>
      </view>
      <!-- 错误单词 -->
      <view class="finishAnd" v-if="errorList.length > 0">错误单词</view>
      <scroll-view scroll-y class="errWord" v-if="errorList.length > 0">
        <view class="errWord_list" v-for="(item, index) in errorList" :key="index" @click="playWord(item.wordAudioUrl)">
          <text>{{ item.word }}</text>
          <!-- <text>{{ item[word] }}</text> -->
          <view class="errWord_list_play"></view>
        </view>
      </scroll-view>

      <view class="popopPower_bottom">
        <button class="popopPower_exit_comfirm nextBtn right" @click="confirm()">再来一轮</button>
        <button class="popopPower_exit_back" @click="close(true)">返回首页</button>
      </view>
    </view>
  </view>
</template>

<script>
  import Util from '@/util/util.js';
  import interestChart from '@/common/interestChart.js';

  var innerAudioContext;

  export default {
    /**
     * 组件名称
     * @property {Array} finishData 闯关数据
     * @property {Array} errorList 错误单词
     * @property {String} conclusionText 提示文字
     * @property {String} scheduleCode 闯关课程编码
     * @property {String} word 单词键名
     */
    props: {
      finishData: {
        type: Array,
        default: () => []
      },
      errorList: {
        type: Array,
        default: () => []
      },
      conclusionText: {
        type: String,
        default: '恭喜你已经完成啦'
      },
      scheduleCode: String,
      word: String
    },
    data() {
      return {
        dialog_iconUrl: Util.getCachedPic('https://document.dxznjy.com/dxSelect/dialog_icon.png', 'dialog_icon_path'),
        timbre: 'W', // 音色默认女声 M  W
        pronunciationType: 0, // 1英式  0美式  默认美式
        playType: 2, // 版本
        linkUrl: '',
        iconList: [
          'https://document.dxznjy.com/course/e86a12c17b064b429c118912270b8c1b.png', // 正确
          'https://document.dxznjy.com/course/168e016e23cd41d494bd3dc7b3d7e3a0.png', // 百分比
          'https://document.dxznjy.com/course/4756143dd162495b850957d2470781b0.png' // 时间
        ]
      };
    },

    methods: {
      //确认
      confirm() {
        this.$emit('confirm');
      },

      //关闭弹窗
      close() {
        this.$emit('close');
      },
      //播放音频
      playWord(list) {
        // console.log(word, wordList);
        let word = JSON.parse(list).word;
        let wordList = JSON.parse(list).list;
        if (!word) return this.$util.alter('该单词还没有音频哦');
        // #ifdef MP-WEIXIN
        innerAudioContext.obeyMuteSwitch = false;
        // #endif
        var that = this;

        innerAudioContext.obeyMuteSwitch = false;

        that.currentIndex = 0; // 当前播放的音频索引

        // 重置音频上下文 在每次播放之前先解绑所有事件
        const resetAudioContext = () => {
          innerAudioContext.stop();
          innerAudioContext.offEnded();
          innerAudioContext.offCanplay();
        };
        const hasEmptyAudioUrl = () => {
          let hasEmpty = false;
          wordList.forEach((item) => {
            if (!item.wordSyllableAudioUrl || item.wordSyllableAudioUrl.length === 0) {
              uni.showToast({
                title: '该单词无音频哦',
                icon: 'error',
                duration: 2000
              });
              hasEmpty = true;
              return;
            }
          });
          return hasEmpty;
        };

        const playNextAudio = () => {
          if (hasEmptyAudioUrl()) {
            resetAudioContext(); // 先重置音频上下文，避免事件冲突
            return;
          }
          resetAudioContext(); // 先重置音频上下文，避免事件冲突

          if (that.currentIndex < wordList.length) {
            innerAudioContext.src = wordList[that.currentIndex].wordSyllableAudioUrl;
            //监听音频进入可以播放状态的事件
            innerAudioContext.onCanplay(() => {
              console.log('音频播放1111');
              innerAudioContext.play();
            });

            innerAudioContext.onEnded(() => {
              that.currentIndex++;
              playNextAudio(); // 播放下一个音频
            });

            innerAudioContext.onError((res) => {
              console.error('音频播放错误', res);
            });

            // 触发一次以检查音频是否可播放
            // innerAudioContext.play();
          } else {
            // 播放 word 的音频
            innerAudioContext.src = word;

            innerAudioContext.onCanplay(() => {
              innerAudioContext.play();
            });

            innerAudioContext.onEnded(() => {
              resetAudioContext();
              that.currentIndex = 0;
            });

            innerAudioContext.onError((res) => {
              console.error('音频播放错误', res);
            });

            // 触发一次以检查音频是否可播放
            innerAudioContext.play();
          }
        };

        playNextAudio();
      }
    },
    created() {
      innerAudioContext = uni.createInnerAudioContext();
    },
    beforeDestroy() {
      innerAudioContext.stop();
      innerAudioContext.offEnded();
      innerAudioContext.offCanplay();
    }
  };
</script>

<style lang="scss" scoped>
  page {
    height: 100vh;
    padding: 0;
  }

  .interesting_popupCenter_1 {
    position: absolute;
    top: 560rpx;
    width: 100%;
    height: calc(100% - 560rpx);
    color: #000;
    border-radius: 24upx;
    padding: 10upx 42upx;
    box-sizing: border-box;
  }

  .finishAnd {
    display: inline-block;
    font-size: 32rpx;
    color: #333333;
    text-align: center;
    height: 60rpx;
    line-height: 60rpx;
    margin: 40rpx 0 24rpx;
  }

  .interet_popupTitle1 {
    width: 210rpx;
    height: 230rpx;
    margin-bottom: 0;
  }

  .reviewCard_box {
    width: 100vw;
    /* height: 560rpx; */
    height: 100vh;
    position: relative;

    background: url('https://document.dxznjy.com/course/b41472fd379a4729bbbf28fc3e9b8c58.png') 100% 100% no-repeat;
    background-color: #fff;
    background-size: 100% auto;
    background-position: top left;
  }

  .reviewCard_box image {
    width: 100%;
    height: 100%;
  }

  .cartoom_image {
    width: 420rpx;
    position: absolute;
    top: -265rpx;
    left: 145rpx;
    z-index: -1;
  }

  .reviewCard {
    position: relative;
    width: 100%;
    height: 100%;
    background: #ffffff;
    color: #000;
    border-radius: 24upx;
    padding: 50upx 55upx;
    box-sizing: border-box;
  }
  .review_close {
    position: absolute;
    top: 418rpx;
    right: 20rpx;
    z-index: 1;
  }

  .reviewTitle {
    width: 100%;
    text-align: center;
    font-size: 32rpx;
    display: flex;
    justify-content: center;
    position: absolute;
    top: -351rpx;
    left: 0;
    color: #fff;
  }

  .popup_content {
    display: flex;
    flex-direction: column;
    flex-wrap: nowrap;
    height: 200rpx;
    padding: 0;

    .popup_content_item:nth-child(2n) {
      margin-top: 30rpx;
    }
  }

  .popup_content_item {
    width: 100%;
    line-height: 80rpx;
    display: flex;
    justify-content: space-between;
    flex-shrink: 0;
    image {
      width: 58rpx;
      height: 58rpx;
      margin-right: 20rpx;
    }
    view {
      display: flex;
      align-items: center;
      font-size: 32rpx;
      color: #555555;
    }
    .popup_content_item_right {
      color: #4bb051;
    }
  }
  .errWord {
    display: flex;
    justify-content: space-around;
    flex-wrap: wrap;
    flex-direction: column;
    align-items: center;
    max-height: 470rpx;
    margin: 0 0 20rpx;
    box-sizing: border-box;
    .errWord_list:nth-child(2n) {
      background-color: #fff;
    }
  }
  .errWord_list {
    width: 100%;
    height: 96rpx;
    line-height: 96rpx;
    font-size: 28rpx;
    color: #555555;
    background-color: #f7ffee;
    /* border-radius: 50rpx; */
    display: flex;
    justify-content: space-between;
    align-items: center;

    box-sizing: border-box;
    padding: 0 24rpx;
    .errWord_list_play {
      width: 38rpx;
      height: 38rpx;
      background: url('https://document.dxznjy.com/course/48157a5e123b42d39f6f72bf8f1039e7.png') 100% 100% no-repeat;
      background-size: 100% 100%;
    }
  }
  .popopPower_bottom {
    position: absolute;
    bottom: 30rpx;
    left: 0;
    width: 100%;
  }
  .popopPower_exit_comfirm {
    width: 632rpx;
    height: 84rpx;
    margin: 0 auto;
    position: initial;
    font-size: 28rpx;
    background: url('https://document.dxznjy.com/course/58b85043e5cd4f89b641476d1b4d975b.png') 100% 100% no-repeat;
    background-size: 100% 100%;

    color: #fff;
    display: grid;
    place-items: center;
  }
  .popopPower_exit_back {
    height: 40rpx;
    font-family: AlibabaPuHuiTi_3_85_Bold;
    font-size: 28rpx;
    color: #ffa332;
    line-height: 40rpx;
    font-style: normal;
    margin-top: 26rpx;
  }
</style>
