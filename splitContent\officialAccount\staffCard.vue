<!-- 展示员工企业微信名片，短信链接跳转此界面，本项目暂时没有直接入口 -->
<template>
	<view class="">
		<view class="content">
			<view class="trialStyle f-24" v-if="showTrialClass">
				<view class="f-24">您的试课单已填写成功，进入交付流程</view>
				若您没有添加学管师请识别下方二维码
				
			</view>
			<view class="userCard" :style="{'margin': !showTrialClass?'100rpx auto 0 auto;':'30rpx auto;'}">
				<view v-if="!showTrialClass" class="tip_text bold f-34" style="margin: 0;color: black" >
					学管师-崔老师
				</view>
				<image class="ewm" :src="imgHost+'dxSelect/fourthEdition/code.jpg'" :show-menu-by-longpress="true" mode=""></image>
				<view class="tip_text">
					<view class="">
						长按识别二维码添加
					</view>
					（如无法直接识别，请保存图片后使用微信扫一扫添加）
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import Config from 'util/config.js'
export default {
	data() {
		return {
			studyManageCode:'',
			getStudyManageInfo:{},
			showTrialClass:false,		//是否展示试课提示
			imgHost: getApp().globalData.imgsomeHost,
			
		};
	},
	onLoad(option) {
		this.studyManageCode = option.manageCode;
		if(option.type){
			this.showTrialClass = true;
		}
		
		// this.getStudyManageIn();
		
	},

	methods: {
		// 获取学管师信息
		getStudyManageIn(){
			uni.request({
				url: `${Config.DXSCRMHost}open/userCode/getQrCodeUrl?userNumber=${this.studyManageCode}`,
				// url: `${Config.DXHost}open/userCode/getQrCodeUrl?userNumber=${this.studyManageCode}`,
				method: 'get',
				header: {
					"Content-Type": "application/json"
				},
				success: (res) => {
					console.log(res.data);
					this.getStudyManageInfo = res.data.data;
			
				}
			});
		}
		
	}
};
</script>

<style>
	.content {
		width: 100%;
		height: 100vh;
		text-align: center;
		/* background-color: rgb(244, 245, 247); */
		justify-content: center;
		flex-wrap: wrap;
	}
	.userCard {
		height: 740upx;
		display: flex;
		justify-content: center;
		flex-wrap: wrap;
		align-items: center;
	}
	
	.ewm{
		width: 400upx;
		height: 400upx;
	}
	.tip_text{
		font-size: 26upx;
		width: 380upx;
		line-height: 45upx;
		color: #999999;
		margin-top: -40upx;
	}
	.trialStyle{
		line-height: 50upx;
		margin: 100upx auto 0 auto;
		width: 500upx;
		height: 120upx;
		color: #666666;
		
	}
</style>