<template>
	<view class="plr-30">
		<view class="bg-ff radius-15 flex-c" style="padding: 80rpx 0;">
			<uniFilePicker :limit="3" :imageStyles="borderShow? imageStyle :imageStyles" file-mediatype="image" @select="select" @delete="delect" >
				<view class="t-c">
					<image :src="imgHost+'dxSelect/fourthEdition/image-icon.png'" class="img"></image>
					<view class="mt-15 f-26 c-99">{{imgList.length==0?'添加图片':'可再添加'+(3-imgList.length)+'张'}}</view>
				</view>
			</uniFilePicker>
		</view>
		<view class="bg-ff radius-15 mt-30 positionRelative" :style="{height:useHeight+'rpx'}">
			<view class="flex-a-c flex-y-s p-30">
				<image v-if="!assess" :src="imgHost+'dxSelect/fourthEdition/entry-icon.png'" class="entry"></image>
				<textarea :value="assess" @input="sumfontnum" @blur="onblur" placeholder="展开说说对商品的想法吧…" maxlength="500" style="width: 100%;height: 600rpx;"/>
			</view>
			<view class="t-r plr-30 digit c-99">{{fontNum}}/500</view>
			<view class="ptb-40 plr-30 rate judge">
				<view class="flex-a-c">
					<text class="f-30">综合评价：</text>
					<uni-rate v-model="rateValue" @change="onChange" class="mt-20" color="#D9D9D9" active-color="#E57126"/>
				</view>
				
				<view class="flex-s mt-40">
					<view class="flex-a-c">
						<uni-icons v-if="radio==1" type="circle" size="20" color="#999" @click="anmendRadio(1)"></uni-icons>
						<uni-icons v-if="radio==0" type="checkbox-filled" size="20" color="#2E896F" @click="anmendRadio(0)"></uni-icons>
						<!-- <uni-data-checkbox v-model="radio" :localdata="anonymous" selectedColor="#2E896F" selectedTextColor="#000"></uni-data-checkbox> -->
						<view class="f-28 c-66">匿名会隐藏您的昵称</view>
					</view>
					<view class="btn" @click="submit">提交评价</view>
				</view>
			</view>
		</view>
		
		<!-- 发布中提示 -->
		<uni-popup ref="publishingPopup" type="center">
		    <view class="t-c bg-ff content-fail">
				<image :src="imgHost+'dxSelect/fourthEdition/publishing.png'" class="publishing-img"></image>
				<view class="mt-10 c-66 f-32">发布中...</view>
		    </view>
		</uni-popup>
		
		
		<!-- 发布成功 -->
		<uni-popup ref="successPopup" type="center">
		    <view class="t-c bg-ff content-fail">
				<image :src="imgHost+'dxSelect/fourthEdition/success.png'" class="success-img"></image>
				<view class="mt-25 c-66 f-32">发布成功</view>
		    </view>
		</uni-popup>
	</view>
</template>

<script>
	import Config from '@/util/config.js'
	import uniFilePicker from '../components/uni-file-picker/components/uni-file-picker/uni-file-picker.vue'
	const {$navigationTo,$getSceneData,$showError,$http,$showMsg} = require("@/util/methods.js")
	export default {
		components:{
			uniFilePicker
		},
		data() {
			return {
				imageStyles: {
					width: 100,
					height: 100,
					border:false, // 是否显示边框
				},
				
				imageStyle:{
					width: 100,
					height: 100,
					border:true, // 是否显示边框
					border:{
						color:"#D9D9D9",
						width:1,
						radius:'7px'
					}
				},
				
				imgList:[], // 选择图片
				commentImage:[], // 评论图片
				imgString:"",
				useHeight:0,
				imgHost: getApp().globalData.imgsomeHost,
				fontNum:0,  // 文本域字数
				rateValue: 0, // 评分
				assess:"", // 评价
				// radio: 1,
				// anonymous: [{
				// 	text: '匿名',
				// 	value: 0
				// }],
				
				borderShow:false, // 边框显示状态
				orderNo:"", // 订单编号
				type:1 , // 1第一次评价   2追评
				
				radio:1 ,  // 0匿名 1 不匿名
			}
		},
		onLoad(e) {
			this.orderNo = e.orderNo;
			this.type = Number(e.type);
		},
		
		onShow() {
		},
		
		onReady() {
			uni.getSystemInfo({
				success: (res) => {
					// 可使用窗口高度，将px转换rpx
					let h = (res.windowHeight * (750 / res.windowWidth));
					this.useHeight = h - 400;
				}
			})
		},
		methods: {
			// 获取上传状态
			select(e) {
				console.log('选择文件：', e)
				for (let i = 0; i < e.tempFilePaths.length; i++) {
					this.imgList.push(e.tempFilePaths[i])
				}
				console.log(this.imgList);
				if(this.imgList.length>0){
					this.borderShow = true;
				}
				
			},

			delect(e) {
				console.log('删除文件：', e)
				this.imgList = this.imgList.filter(obj => obj != e.tempFilePath);
				console.log(this.imgList)
				if(this.imgList.length==0){
					this.borderShow = false;
				}
			},
			
			// 限制文本框字数
			sumfontnum(e) {
				console.log(e)
				this.fontNum = e.detail.value.length;
				this.assess = e.detail.value;
			},
			// 评价内容
			onblur(e){
				this.assess = e.detail.value;
			},
			
			onChange(e) {
				console.log(e)
				console.log('rate发生改变:' + JSON.stringify(e))
				this.rateValue= e.value +'';
			},
			
			// 提交评价(上传图片)
			async submit(){
				if(this.rateValue ==0) {
					this.$util.alter('综合评价不能为空');
					return;
				}
				this.$refs.publishingPopup.open();
				if(this.imgList.length>0){
					await this.upload();
				}else{
					await this.getEvaluate();
				}
			},
			
			// 上传图片
			upload(){
				let _this =this;
				this.uploadFile();
				// .then((res)=>{
				// 	console.log('555555555555');
				// 	console.log(res);
				// 	console.log(_this.commentImage);
				// 	var string = _this.commentImage.toString();
				// 	console.log(string)
				// }).catch((err)=>{
				// 	console.log('22222222222');
				// 	console.log(err);
				// });
				// console.log('3333333333333333')
			
				// await _this.getEvaluate();
			},
			
			uploadFile(){
				let _this = this;
				console.log(_this.imgList)
				_this.commentImage=[]
				// return new Promise((resolve,reject)=>{
					for(let i=0;i<_this.imgList.length;i++){
						uni.uploadFile({
							url: `${Config.DXHost}zx/common/uploadFile`,
							filePath:_this.imgList[i],
							name: 'file',
							header: {
								Token: uni.getStorageSync('token')
							},
							success: function(res) {
								console.log(res)
								if(res.data){
									let data = JSON.parse(res.data)
									console.log(data)
									if (data.status == 1) {
										_this.commentImage.push(data.data.fileUrl);
										console.log(_this.commentImage)
										console.log(_this.commentImage.length)
										console.log(_this.imgList.length)
										console.log('------------------------------------------')
										if(_this.commentImage.length==_this.imgList.length){
											_this.imgString = JSON.stringify(_this.commentImage);
											// console.log(_this.imgString)
											_this.getEvaluate();
										}
									} else {
										uni.showToast({
											title: data.message,
											icon: 'none'
										})
									}
								}
								
								// resolve(_this.commentImage);
							},
							fail: function(err) {
								console.log(err);
								reject(err)
								$showMsg(err.errMsg)
							},
							complete: function(res) {
								uni.hideLoading()
							},
						})
					}
				// })
				
				
			},
			
			

			
			// 提交评价
			async getEvaluate(){
				console.log(this.radio)
				debugger
				console.log(this.assess.length);
				if(this.assess.length>500){
					this.$util.alter('评价字数不能超过500字');
				}
				
				console.log('图片');
				console.log(this.imgString);
				console.log('图片');
				
				const res = await $http({
					url: 'zx/order/evaluate/addOrderEvaluate',
					method: 'post',
					data: {
						evaluateContent: this.assess,
						evaluateGrade: this.rateValue,
						isAnonymity: this.radio,
						orderNo: this.orderNo,
						photoUrl: this.imgString,
						type:this.type,
						whetherGoodsType:1
					}
				})
				if (res) {
					this.$refs.publishingPopup.close();
					this.$refs.successPopup.open();
					this.imgList =[];
					this.commentImage= [];
					this.imgString="";
					setTimeout(()=>{
						uni.navigateBack();
					},1500)
				}else{
					this.$refs.publishingPopup.close();
					this.$util.alter('评价失败，请稍后再试');
				}
			},
			
			
			anmendRadio(value){
				console.log(value)
				this.radio = value==1?0:1;
				console.log(this.radio)
			}
		}
	}
</script>

<style lang="scss" scoped>
	/deep/ .uni-file-picker__container {
		justify-content: center !important;
	}
	
	.img{
		width: 64rpx;
		height: 60rpx;
	}
	
	.entry{
		width: 32rpx;
		height: 33rpx;
		margin-right: 15rpx;
		margin-top: 5rpx;
	}
	
	.rate{
		background-color: #F4F4F4;
	}
	
	.btn{
		color: #fff;
		width: 200rpx;
		height: 70rpx;
		text-align: center;
		border-radius: 40rpx;
		line-height: 70rpx;
		background-image: linear-gradient(to bottom, #88CFBA, #1D755C);
	}
	
	/deep/ .checklist-text{
		color: #000 !important;
	}
	
	.digit{
		position: absolute;
		bottom: 300rpx;
		width: 91%;
	}
	
	.judge{
		position: absolute;
		bottom: 0%;
		width: 91.6%;
	}
	
	.content-fail{
		border-radius: 30rpx;
		padding: 90rpx 260rpx 100rpx;
	}
	
	.publishing-img{
		width: 140rpx;
		height: 140rpx;
	}
	
	.success-img {
	    width: 110rpx;
		height: 110rpx;
	}
</style>
