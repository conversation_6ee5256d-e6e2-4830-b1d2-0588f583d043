<template>
  <view class="body">
    <view class="center">
      <u-sticky bgColor="#F3F8FC" class="top_sticky">
        <view class="roll">
          <u-notice-bar :volume-icon="icon" :bg-color="color" :font-size="size" :color="fc" :text="title"></u-notice-bar>
        </view>
        <view class="sort" style="margin-top: 40rpx; padding-bottom: 6rpx; margin-left: 360rpx">
          <div style="width: 400rpx; height: 10rpx"></div>
          <view class="dot_css f-24">
            <view class="mr-55 flexbox" style="padding-top: 7rpx">
              <view class="text mr-8">销量</view>
              <!-- <image  class="wh24"  src="https://document.dxznjy.com/course/f3cc005ea5bc4add86560a9d06619e4c.png"></image> -->
              <u-icon
                name="arrow-down"
                @tap="getSelectInfo('goodsSales', 'asc')"
                v-if="goodsSalesOrderType == 'desc' || goodsSalesOrderType == ''"
                color="#565656"
                size="28"
              ></u-icon>
              <u-icon name="arrow-up" @tap="getSelectInfo('goodsSales', 'desc')" v-if="goodsSalesOrderType == 'asc'" color="#565656" size="28"></u-icon>
              <!-- arrow-down -->
            </view>
            <view class="mr-55">
              <text class="text">收益</text>
              <image
                v-if="goodsProfiteOrderType == ''"
                @tap="getSelectInfo('profit', 'asc')"
                class="wh24"
                src="https://document.dxznjy.com/course/e1ed9cd113a1408b9782c9b6baa5350a.png"
              ></image>
              <image
                v-if="goodsProfiteOrderType == 'desc'"
                @tap="getSelectInfo('profit', '')"
                class="wh24"
                src="https://document.dxznjy.com/course/a6dea11bfce04d8ebb10613944e7a72e.png"
              ></image>
              <image
                v-if="goodsProfiteOrderType == 'asc'"
                @tap="getSelectInfo('profit', 'desc')"
                class="wh24"
                src="https://document.dxznjy.com/course/80dd312f9b4e45e9bb1d2eebf2e666e6.png"
              ></image>
            </view>
            <view>
              <text class="text">时间</text>
              <image
                v-if="goodsCreatedTimeOrderType == ''"
                @tap="getSelectInfo('createdTime', 'asc')"
                class="wh24"
                src="https://document.dxznjy.com/course/e1ed9cd113a1408b9782c9b6baa5350a.png"
              ></image>
              <image
                v-if="goodsCreatedTimeOrderType == 'desc'"
                @tap="getSelectInfo('createdTime', '')"
                class="wh24"
                src="https://document.dxznjy.com/course/a6dea11bfce04d8ebb10613944e7a72e.png"
              ></image>
              <image
                v-if="goodsCreatedTimeOrderType == 'asc'"
                @tap="getSelectInfo('createdTime', 'desc')"
                class="wh24"
                src="https://document.dxznjy.com/course/80dd312f9b4e45e9bb1d2eebf2e666e6.png"
              ></image>
            </view>
          </view>
        </view>
      </u-sticky>

      <view class="lucency_center" v-if="icomeList.length > 0">
        <view class="lucency_box" v-for="(item, index) in icomeList" :key="index">
          <view class="goods_name">
            {{ item.goodsName }}
          </view>
          <view class="goods_prices">
            <view class="goods_prices_item">
              <view class="estimate_box">
                <image src="https://document.dxznjy.com/course/242bc61690154c939787564a3834bbca.png" mode="" class="estimate_price_img"></image>
                <view class="estimate_price">预估收益</view>
              </view>

              <view class="price bottom">{{ item.originalProfit }}元</view>
            </view>
            <view class="goods_prices_item_one">
              <view class="estimate_box positionRelative">
                <image src="https://document.dxznjy.com/course/ddde281a2a2d4d4eafe4c0458d77a269.png" mode="" class="estimate_price_img"></image>
                <view>
                  <view class="estimate_vip_price">会员价购买</view>
                  <view class="estimate_price">预估收益</view>
                </view>
              </view>
              <view class="estimate_box">
                <view class="price bottom">{{ item.vipProfit }}元</view>
                <image src="https://document.dxznjy.com/course/77a6d9501665440dad25366cbba140f8.png" mode="" class="estimate_price_tow_img"></image>
              </view>
            </view>
          </view>
        </view>
      </view>
      <view class="lucency_center_other" v-else></view>
    </view>
  </view>
</template>

<script>
  const { $http, $showMsg } = require('@/util/methods.js');
  export default {
    data() {
      return {
        roleVal: -1,
        title: '具体收益以实付款为准，此页面展示仅为预估收益值，仅做参考',
        color: 'rgba(255,255,255,0)',
        size: '30',
        fc: '#000',
        icon: false,
        icomeList: [],
        goodsSalesOrderType: '',
        goodsProfiteOrderType: '',
        goodsCreatedTimeOrderType: '',
        column: 'createdTime',
        sort: 'asc'
      };
    },
    onLoad(option) {
      console.log(option);
      this.roleVal = option.id;
    },

    onShow() {
      this.getEstimatedIncome();
    },
    methods: {
      goback() {
        uni.navigateBack();
      },
      getSelectInfo(orderBy, orderType) {
        console.log(orderBy);
        console.log(orderType);
        switch (orderBy) {
          case 'profit':
            this.goodsProfiteOrderType = orderType;
            break;
          case 'goodsSales':
            this.goodsSalesOrderType = orderType;
            break;
          case 'createdTime':
            this.goodsCreatedTimeOrderType = orderType;
            break;
        }

        this.column = orderBy;
        this.sort = orderType;
        this.getEstimatedIncome();
      },
      async getEstimatedIncome() {
        console.log(typeof this.roleVal);
        const res = await $http({
          url: 'zx/order/estimateProfit',
          method: 'get',
          data: {
            roleVal: this.roleVal,
            column: this.column,
            sort: this.sort
          }
        });
        if (res.status == 1) {
          this.icomeList = res.data;
          if (res.data.length == 0) {
            uni.showLoading({
              title: '数据加载中'
            });
          }
        } else {
          $showMsg(res.message);
        }
      }
    }
  };
</script>

<style lang="scss" scoped>
  .body {
    width: 100%;
    // background: linear-gradient(to bottom right, #829e98 60%, #eef2f2 100%);
    background-image: url('https://document.dxznjy.com/course/e4f078ba1edc49748a33021737ed0f70.png');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    padding-bottom: 40rpx;
  }
  .top_sticky {
    width: 100%;
    height: 100rpx;
  }
  .sort {
  }
  .dot_css {
    margin-bottom: 20rpx;
    display: flex;
    color: #5a5a5a;

    .wh24 {
      display: inline-block;
      width: 22rpx;
      height: 24rpx;
      margin-left: 8rpx;
      vertical-align: middle;
    }

    .text {
      vertical-align: middle;
    }
  }
  .roll {
    height: 20rpx;
  }

  .lucency_center {
    overflow: scroll; /* 添加滚动条 */
    margin-top: 30rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-direction: column;
  }
  .lucency_center_other {
    margin-top: 80rpx;
    height: 100vh;
  }
  .lucency_box {
    width: 632rpx;
    height: 288rpx;
    background: rgba(255, 255, 255, 0.68);
    border-radius: 32rpx;

    backdrop-filter: blur(10px);
    margin-bottom: 24rpx;
    padding: 32rpx;
  }
  .goods_name {
    font-size: 28rpx;
    color: #08745c;
    margin-bottom: 24rpx;
  }
  .goods_prices {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  .goods_prices_item {
    width: 300rpx;
    height: 210rpx;
    background: linear-gradient(143deg, #ffffff 0%, #fafbff 100%);
    box-shadow: 0rpx 4rpx 20rpx 2rpx rgba(235, 238, 236, 0.38);
    border-radius: 24rpx;
    border: 1rpx solid rgba(90, 203, 142, 0.5);
    display: flex;
    justify-content: space-between;
    flex-direction: column;
  }
  .goods_prices_item_one {
    width: 299rpx;
    height: 210rpx;
    background: #5ecf9a;
    box-shadow: 0rpx 4rpx 20rpx 2rpx #cbdfd1;
    border-radius: 24rpx;
    display: flex;
    justify-content: space-between;
    flex-direction: column;
  }
  .estimate_price {
    font-size: 26rpx;
    color: #4c8773;
    font-weight: 600;
  }

  .estimate_vip_price {
    position: absolute;
    top: 18rpx;
    right: 15rpx;
    text-align: right;
    font-size: 20rpx;
    color: #4c8773;
    font-weight: 600;
  }
  .estimate_box {
    display: flex;
    align-items: center;
    padding-left: 22rpx;
    padding-top: 18rpx;
  }
  .estimate_price_img {
    width: 90rpx;
    height: 90rpx;
  }
  .estimate_price_tow_img {
    width: 56rpx;
    height: 62rpx;
    position: relative;
    top: -50rpx;
  }
  .price {
    text-align: left;
    font-size: 38rpx;
    color: #4c8773;
    font-weight: 600;
    padding-left: 28rpx;
  }
  .bottom {
    padding-bottom: 30rpx;
  }
</style>
