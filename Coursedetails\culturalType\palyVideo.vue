<template>
  <view class="bg-00 paly-video-css c-ff" @click="getVideo">
    <!-- #ifdef  APP-PLUS  -->
    <video
      id="myVideo"
      :initial-time="startTime"
      :autoplay="true"
      @timeupdate="onTimeUpdate"
      @loadedmetadata="onLoadedMetadata"
      @ended="bindEnded"
      :src="'https://v.polyv.net/uc/video/getMp4?vid=' + videoInfo.videoUrl"
      style="width: 100%; height: 95%"
    ></video>
    <!-- #endif -->
    <!-- #ifdef   MP-WEIXIN -->
    <polyv-player
      id="polyv-player-id"
      :defaultQuality="startTime"
      :startTime="startTime"
      @loadedmetadata="bindloadedmetadata"
      @ended="bindEnded"
      :autoplay="true"
      :playerId="playerIdcont"
      :vid="videoInfo.videoUrl"
      :width="width"
      :height="height"
      :ts="ts"
      :sign="sign"
    ></polyv-player>
    <!-- #endif -->

    <growthPopup ref="growthPopupRefs"></growthPopup>
  </view>
</template>

<script>
  import growthPopup from '../components/growthPopup.vue';
  const { $http } = require('@/util/methods.js');
  const MD5 = require('../../util/md5.js');
  let secretkey = 'Jkk4ml1Of8';
  let vid = '';
  let ts = new Date().getTime();
  let sign = '';
  export default {
    components: { growthPopup },
    data() {
      return {
        videoInfo: {},
        studyTime: 0,
        playerIdcont: 'polyvPlayercont',
        startTime: 0,
        ts: ts,
        sign: sign,
        width: '100%',
        height: '95%',
        currentTime: 0, // 当前播放时长（秒）
        duration: 0, // 总时长（秒），
        eventType: ''
      };
    },
    onLoad(e) {
      this.eventType = e.eventType;
      uni.setNavigationBarTitle({
        title: e.eventType ? '家庭文化' : '读书文化'
      });
    },

    onReady() {
      let info = uni.getStorageSync('videoInfo');
      this.videoInfo = info;
      this.studyTime = info.studyTime;
      this.startTime = info.studyTime;

      // #ifdef MP-WEIXIN
      this.getVideo();
      // #endif
    },
    onUnload() {
      this.closeVideo();
    },
    methods: {
      getVideo() {
        let _this = this;
        let polyvPlayerContext = _this.selectComponent('#polyv-player-id');
        console.log(polyvPlayerContext);
        const ts = new Date().getTime();
        const sign = MD5.md5(`${secretkey}${vid}${ts}`);
        polyvPlayerContext.changeVid({
          vid: _this.videoInfo.videoUrl,
          ts,
          sign
        });
      },
      bindloadedmetadata() {
        let polyvPlayerContext = this.selectComponent('#polyv-player-id');
        // polyvPlayerContext.pause()
        polyvPlayerContext.seek(Number(this.studyTime));
        polyvPlayerContext.switchQuality(1);
      },
      bindEnded() {
        if (this.eventType) {
          this.getGrowthValue();
        }
      },
      onTimeUpdate(e) {
        this.duration = e.detail.duration;
        this.currentTime = e.detail.currentTime;
      },

      // 获取视频总时长
      onLoadedMetadata(e) {
        console.log(this.duration);
      },
      async closeVideo() {
        // #ifdef  MP-WEIXIN
        let polyvPlayerContext = this.selectComponent('#polyv-player-id');
        let learningProgress = (polyvPlayerContext.rCurrentTime / polyvPlayerContext.rDuration).toFixed(2);
        let rCurrentTime = polyvPlayerContext.rCurrentTime.toFixed(2);
        // #endif
        // #ifdef APP-PLUS

        let learningProgress = (this.currentTime / this.duration).toFixed(2);
        let rCurrentTime = this.currentTime.toFixed(2);
        // #endif
        console.log(rCurrentTime, learningProgress);
        let _this = this;
        let courseType = this.eventType == 'LEARN' ? 1 : 2;
        const res = await $http({
          url:
            'zx/wap/recorded/course/saveLeanProcess?courseType=' +
            courseType +
            '&learningProgress=' +
            learningProgress * 100 +
            '&courseId=' +
            this.videoInfo.id +
            '&studyTime=' +
            rCurrentTime,
          showLoading: true,
          method: 'POST'
        });
        if (res) {
        }
      },
      async getGrowthValue() {
        const res = await $http({
          url: 'zx/wap/CultureCircle/getGrowthValue?eventType=' + this.eventType + '&userId=' + uni.getStorageSync('user_id'),
          method: 'POST',
          data: {}
        });
        if (res) {
          if (Number(res.data)) {
            this.$refs.growthPopupRefs.open(res.data);
          }
        }
      }
    }
  };
</script>

<style>
  .paly-video-css {
    height: 100vh;
  }
</style>
