<template>
	<page-meta :page-style="'overflow:'+(rollShow?'hidden':'visible')"></page-meta>
	<view class="plr-30">
		<view class=" pt-30 radius-15 mb-20 search">
			<view class="plr-20 flex">
				<view class="bold">
					搜索用户:
				</view>
				<view class="search-css pl-25">
					<!-- <image :src="imgHost+'dxSelect/index_tab_cpzx.png'" class="box-50 search-image" mode="widthFix"></image> -->
					<u-icon name="search" class="box-50 search-image" color="#CFCFCF" size="60"></u-icon>
					<input class="search-input f-28 c-55" v-model="searchValue" type="text" @blue="blur"
						@focus="blur" readonly placeholder="请输入用户名称......" />
				</view>
			</view>
			<!-- <view class="mt-20">
				<u-tabs :list="listCate" keyName="name" lineWidth="30" lineHeight="5" lineColor="#2E896F"
					:activeStyle="{
					        color: '#000',
					        fontWeight: 'bold',
							paddingBottom:'20rpx',
					        transform: 'scale(1.04)'
					    }" :inactiveStyle="{
					        color: '#666',
							paddingBottom:'20rpx',
					        transform: 'scale(1)'
					    }" itemStyle="padding-left: 35px; padding-right: 35px; height: 34px;" @click="stateSwtich">
				</u-tabs>
			</view> -->
			<scroll-view scroll-y="true" @scrolltolower='scrolltable' class="scrollClass mt-45">
				<uniTable ref="table" :loading="loading" border stripe 
					@selection-change="selectionChange" style="overflow-y: auto;">
					<uniTr>
						<uniTh width='70' style="color: #000;word-break: break-all;">会员信息</uniTh>
						<uniTh width="80">到期时间</uniTh>
						<uniTh width="80">累计收益</uniTh>
						<uniTh width="50">推荐人</uniTh>
					</uniTr>
			
					<uniTr v-for="(value,index) in activityList" :key="index">
						<uniTd>{{value.userInfo.split(',')[0]}}<br>
							{{value.userInfo.split(',')[1]}}
						</uniTd>
						<uniTd>
							{{value.type}}
						</uniTd>
						<uniTd>{{value.reward}}</uniTd>
						<uniTd>{{value.acquisitionTime}}</uniTd>
					</uniTr>
			
				</uniTable>
			</scroll-view>
		</view>
	<!-- 	<view style="margin-top: 250rpx;">
			<view v-if="payStatus==-1 && totalList !=0">共{{totalList}}位学习超人</view>
			<view v-if="payStatus==2 && totalList !=0">共{{totalList}}位学习超人即将到期，可提醒去续费</view>
			<view v-if="payStatus==0 && totalList !=0">共{{totalList}}位学习超人已到期，提醒去续费</view>
		</view> -->
		
	<!-- 	<view class="mt-20">
			<block v-for="(item,index) in listS.list" :key="index">
				<view class="bg-ff mb-30 p-30 radius-15">
					<view class="flex-x">
						<view class="box-100 radius-50">
							<image :src="item.headPortrait == ''? avaUrl : item.headPortrait" class="wh100"></image>
						</view>
						<view class="flex-box ml-20">
							<view class="flex-dir-row" style="justify-content: space-between;align-items: center;">
								<view class="flex-s w100">
									<view class="flex-a-c">
										<text class="f-32 grade_name" :class="item.status==0? 'c-99': 'c-00'">{{ item.remark ? item.remark : item.nickName  }}</text>
										<view class="ml-20" @click.stop="changeFoucus(item)">
											<image src="https://document.dxznjy.com/dxSelect/image/edit_icon.png" style="width:30upx;height:30upx;display: inline-block;"></image>
										</view>
									</view>
									<view class="f-26">
										<text :class="item.status==0?'gray':'label'"
											v-if="item.gradeLevel==4||item.gradeLeve==5||item.gradeLevel==6">{{item.gradeLevel!=4?(item.gradeLevel==5?'B2俱乐部':'B3俱乐部'):'B1俱乐部'}}</text>
										<text :class="item.status==0?'gray':'label'" v-else>学习超人</text>
									</view>
								</view>
							</view>
							<view class="f-28 flex mt-20" :class="item.status==0?'c-99':'c-33'">
								<text class="f-30" :class="item.status==0?'c-99':'c-00'">{{ item.mobile}}</text>
								<text >累计收益： <text class="bold" :class="item.status==0?'c-99':'color_tangerine'">{{ item.totalAmount }}</text></text>
							</view>
							<view class="flex-s mt-15">
								<view v-if="item.isExpire==1 && item.expireTime!=''" class="f-28 c-66"  :class="item.status==0?'c-99':'c-66'">
									超人有效期至{{item.expireTime}}</view>
								<view v-if="item.isExpire==0" class="f-26" :class="item.status==0?'c-99':'c-fea'" @click="contact(item)">身份已到期，提醒去续费</view>
								<view v-if="item.isExpire==2 && item.expireTime!=''" class="f-28" :class="item.status==0?'c-99':'c-fea'" @click="contact(item)">
									超人有效期至{{item.expireTime}},提醒续费</view>
								<view v-if="item.isUpgrade==0" class="flex-s" style="display: flex;">
									<image :src="imgHost+'dxSelect/arrow-up.png'" class="img_icon"></image>
									<text class="c-fea ml-5 f-26">{{item.gradeLevel!=4?(item.gradeLevel==5?'B2俱乐部':'B3俱乐部'):'B1俱乐部'}}</text>
								</view>
							</view>
							
							<view v-if="item.status==0" class="f-26 c-fea mt-15">该超人已经变更上级俱乐部，暂停分润</view>
						</view>
					</view>
				</view>

			</block>
		</view> -->
		<view v-if="listS.list!= undefined && listS.list.length==0" class="t-c flex-col" :style="{height: useHeight+'rpx'}">
			<image :src="imgHost+'alading/correcting/no_data.png'" class="mb-20 img_s" mode="widthFix"></image>
			<view style="color: #BDBDBD;">暂无数据</view>
		</view>
		<view v-if="no_more && listS.list.length>0">
			<u-divider text="到底了"></u-divider>
		</view>


		<!-- 联系家长 -->
		<uni-popup ref="contactPopup" type="center" @change="changePopup">
			<view class="dialogBG">
				<view class="reviewCard_box positionRelative">
					<view class="cartoom_image">
						<image :src="dialog_iconUrl" mode="widthFix"></image>
					</view>
					<view class="reviewCard">
						<view class="reviewTitles">是否发送短信给客户提醒续费？</view>
						<view class="flex-s mt-55">
							<view class="review_btn" @click="contactType">确定</view>
							<view class="close_btn" @click="closeDialog">取消</view>
						</view>
					</view>
				</view>
			</view>
		</uni-popup>

		<!-- 短信发送成功 -->
		<uni-popup ref="popup" type="center" @change="changePopup">
			<view class="t-c bg-ff content">
				<u-icon name="checkmark-circle-fill" color="#2DC032" size="136"></u-icon>
				<view class="mt-30">短信发送成功</view>
			</view>
		</uni-popup>
		
		<!-- 输入框弹窗 -->
		<uni-popup ref="inputPopup" type="center" :mask-click="false" @change="changePopup">
			<view class="dialogBG">
				<view class="reviewCard_box positionRelative">
					<view class="cartoom_image">
						<image :src="dialog_iconUrl" mode="widthFix"></image>
					</view>
					<view class="reviewCard t-c">
						<view class="flex-a-c flex-x-e close-icon">
							<uni-icons type="clear" size="30" color="#B1B1B1" @click="closeDialog"></uni-icons>
						</view>
						<view class="f-38 bold">备注</view>
						<view class="mt-80 t-c input pl-30" :class="showClearIcon?'pr-20':'pr-30'">
							<input ref="inputRefs" :value="remark" @input="onInput" placeholder="请输入" :maxlength="importShow?'6':'11'" :class="showClearIcon?'mr-30':''"/>
							<uni-icons v-if="showClearIcon" type="closeempty" size="20" color="#B1B1B1" @click="clearInput"></uni-icons>
						</view>
						
						<view class="flex-s mt-80">
							<view class="determine_btn" @click="determine">确定</view>
							<view class="close_btn" @click="closeInput">取消</view>
						</view>
					</view>
				</view>
			</view>
		</uni-popup>
	</view>
</template>

<script>
	const { $navigationTo, $http } = require("@/util/methods.js")
	import Util from '@/util/util.js'
	import uniTable from "../components/uni-table/components/uni-table/uni-table.vue"
	import uniTd from "../components/uni-table/components/uni-td/uni-td.vue"
	import uniTr from "../components/uni-table/components/uni-tr/uni-tr.vue"
	import uniTh from "../components/uni-table/components/uni-th/uni-th.vue"
	export default {
		components: {
			uniTable,
			uniTr,
			uniTd,
			uniTh
		},
		data() {
			return {
				rollShow: false, //禁止滚动穿透
				sortIndex: "", // 学习超人列表类型 1已升级 2已到期
				showSort: false,
				searchValue: "", //搜索框
				payStatus: -1,
				listS: {},
				page: 1,
				avaUrl: Util.getCachedPic("https://document.dxznjy.com/dxSelect/home_avaUrl.png","home_avaUrl_path"),
				no_more: false,
				useHeight: 0, //除头部之外高度
				imgHost: getApp().globalData.imgsomeHost,
				// 成交数据
				transactionData: {
					totalUserNum: "0",
					upgradeNum: 0,
					expireNum: "0"
				},
				totalList: '', // 数据总数
				userId:"", // 当前列表id
				
				remark:"",
				showClearIcon: false,
				information:{}, // 当前点击的客户列表
				importShow:false, 
				activityList:[],
                dialog_iconUrl:Util.getCachedPic("https://document.dxznjy.com/dxSelect/dialog_icon.png","dialog_icon_path"),
			}
		},
		watch:{},
		onLoad() {},
		onReady() {
			uni.getSystemInfo({
				success: (res) => {
					// 可使用窗口高度，将px转换rpx
					let h = (res.windowHeight * (750 / res.windowWidth));
					this.useHeight = h - 285;
				}
			})
		},

		onShow() {
			// this.getCustomerRelationsNum();
			this.supermanClublist();
			// this.$refs.contactPopup.open();
			// this.$refs.popup.open();
		},

		onReachBottom() {
			if (this.page >= this.listS.totalPage) {
				this.no_more = true
				return false;
			}
			this.supermanClublist(true, ++this.page);
		},
		methods: {
			// 获取总人数 已升级  已到期人数
			async getCustomerRelationsNum() {
				const res = await $http({
					url: 'zx/merchant/selMerchantAddUserNum'
				})
				if (res) {
					this.transactionData = res.data;
				}

			},


			// 下级俱乐部列表
			async supermanClublist(isPage, page) {
				let _this = this;
				const res = await $http({
					url: 'zx/merchant/memberPage',
					data: {
						descType: 1,
						isExpire: _this.payStatus == -1 ? '' : _this.payStatus,
						searchName: _this.searchValue,
						page: page || 1,
						pageSize: 10
					}
				})
				console.log(res)
				if (res) {
					_this.totalList = res.data.totalCount;
					if (isPage) {
						let old = _this.listS.list;
						_this.listS.list = [...old, ...res.data.list];
					} else {
						_this.listS = res.data;
					}
				}
			},
			
			// 提醒续费
			async reminderRenewal() {
				let _this = this;
				const res = await $http({
					url: 'zx/merchant/sendMemberBeAboutToExpireSms',
					data: {
						userId: _this.userId,
					}
				})
				console.log(res)
				if (res) {
					_this.$refs.popup.open();
					setTimeout(function() {
						_this.$refs.popup.close();
					}, 1500);
					_this.supermanClublist();
				}else{
					_this.$util.alter('请稍后再试')
				}
			},
			

			showSortDialog() {
				this.showSort = !this.showSort;
			},

			// 点击排序
			chooseSort(index) {
				if (index == 0) {
					this.sortIndex = '';
				} else {
					this.sortIndex = index;
				}
				this.supermanClublist();
				this.showSortDialog();
			},

			// tab(e) {
			//     this.tabindex = e;
			//     this.page = 1;
			//     this.no_more = false;
			//     this.supermanClublist();
			// },
			stateSwtich(e) {
				this.payStatus = e.value;
				console.log(this.payStatus);
				this.page = 1;
				this.no_more = false;
				this.supermanClublist();
			},
			
			blur(e) {
				this.searchValue = e.value;
				this.supermanClublist();
			},

			clear() {
				this.searchValue = "";
				this.supermanClublist();
			},

			contact(item) {
				this.userId = item.userId;
				this.$refs.contactPopup.open();
			},

			// 禁止滚动穿透
			changePopup(e) {
				this.rollShow = e.show;
			},

			//关闭弹窗
			closeDialog() {
				this.$refs.contactPopup.close();
				this.$refs.inputPopup.close();
				this.importShow = false;
				this.remark = "";
			},

			contactType() {
				this.$refs.contactPopup.close();
				this.importShow = false;
				this.reminderRenewal();
			},
			
			// 点击修改备注
			changeFoucus(item) {
				if(item.remark){
					this.remark = item.remark;
				}else{
					this.remark = item.nickName;
				}
				this.showClearIcon = true;
				this.information = item;
				this.$refs.inputPopup.open();
			},
			
			closeInput(){
				this.$refs.inputPopup.close();
				this.importShow = false;
				this.remark = "";
				
			},
			
			determine(){
				this.modifyRemarks();
			},
			
			clearInput(){
				this.remark = '';
				this.importShow = true;
			},
			
			onInput(e) {
				console.log(e);
				console.log(this.remark);
				this.remark = e.detail.value;
				if (e.detail.value.length > 0) {
					this.showClearIcon = true;
					if(e.detail.value.length<=6){
						this.importShow= true;
					}
				} else {
					this.showClearIcon = false;
				}
			},
			
			change(e) {
				this.show = e.show
			},
			
			// 修改备注
			async modifyRemarks(){
				let _this = this;
				if(_this.remark.length>6){
					_this.$util.alter('备注不可超过六个字符');
					return;
				}
				uni.showLoading({mask:true});
				const res = await $http({
					url: 'zx/merchant/setUserMerchantRelationRemark',
					method:'post',
					data: {
						remark: _this.remark,
						userId: _this.information.userId
					}
				})
                uni.hideLoading();
				if(res.status==1){
					this.$refs.inputPopup.close();
					this.remark = "";
					this.importShow = false;
					this.supermanClublist();
				}else{
					_this.$util.alter('操作失败啦，请稍后再试');
				}
			},
		}
	}
</script>

<style lang="scss" scoped>
	.search{
		position: fixed;
		top: 0;
		width: 690rpx;
	}
	/* 搜索框 */
	.search-css {
		display: flex;
		margin-left: 20rpx;
		flex: 1;
		line-height: 80rpx;
		background-color: #FFF;
	
		.search-image {
			vertical-align: middle;
		}
	
		.search-input {
			display: inline-block;
			height: 80rpx;
			line-height: 80rpx;
			vertical-align: middle;
		}
	}
	.label {
		background-image: linear-gradient(to right, #FEEFD5, #E7BD7B);
		padding: 5rpx 15rpx;
		height: 40rpx;
		line-height: 40rpx;
		border-radius: 20rpx 0;
		color: #886A34;
		font-size: 24rpx;
	}
	
	.gray {
		background-color: #B1B1B1;
		padding: 5rpx 15rpx;
		height: 40rpx;
		line-height: 40rpx;
		border-radius: 20rpx 0;
		color: #fff;
		font-size: 24rpx;
	}

	.grade_name {
		max-width: 260rpx;
		overflow: hidden;
		white-space: nowrap;
		text-overflow: ellipsis;
	}

	.screenitem {
		width: 150rpx;
		height: 60rpx;
		border: 1rpx solid #C8C8C8;
		border-radius: 35rpx;
		padding: 0 30rpx;

		.screenPicker {
			flex: 1;
		}

		.xiaimg {
			width: 20rpx;
			height: 20rpx;
		}
	}

	.flex-x {
		display: flex;
		justify-content: space-between;
	}

	.flex_s {
		display: flex;
		align-items: center;
	}

	.img_s {
		width: 160rpx;
		height: 160rpx;
	}

	.tab_s {
		height: 60rpx;
	}

	.changing-over {
		background-color: #2E896F;
		width: 30rpx;
		height: 4rpx;
	}

	/deep/ .uni-searchbar {
		padding-left: 20rpx !important;
	}

	/deep/.u-tabs__wrapper__nav__line {
		margin-left: 18rpx !important;
	}


	.sortBox {
		width: 220upx;
		background: #FFFFFF;
		border: 1upx solid #CFC8C8;
		position: absolute;
		top: 100upx;
		left: 30upx;
	}

	.sortItem {
		width: 100%;
		height: 80upx;
		text-align: center;
		line-height: 80upx;
		font-size: 30upx;
		color: #666666;
		background-color: #fff;
	}

	.sortItem.active {
		background: #F4F4F4;
		color: #000000;
		font-weight: bold;
	}

	.img_icon {
		width: 22upx;
		height: 28upx;
	}


	// 分享选项弹出层
	.dialogBG {
		width: 100%;
		/* height: 100%; */
	}


	/* 21天结束复习弹窗样式 */
	.reviewCard_box {
		width: 670rpx;
		position: relative;
	}

	.reviewCard_box image {
		width: 100%;
		height: 100%;
	}

	.reviewCard {
		position: relative;
		width: 100%;
		height: 100%;
		background: #FFFFFF;
		color: #000;
		border-radius: 24upx;
		padding: 50upx 60upx;
		box-sizing: border-box;
	}

	.cartoom_image {
		width: 420rpx;
		position: absolute;
		top: -250rpx;
		left: 145rpx;
		z-index: -1;
	}

	.review_close {
		position: absolute;
		top: 20rpx;
		right: 20rpx;
		z-index: 1;
	}

	.reviewTitles {
		padding: 0 100rpx;
		font-size: 32rpx;
		padding-top: 20rpx;
		text-align: center;
	}

	.review_btn {
		width: 250upx;
		height: 80upx;
		background: linear-gradient(180deg, #88CFBA 0%, #1D755C 100%);
		border-radius: 45upx;
		font-size: 30upx;
		color: #FFFFFF;
		line-height: 80upx;
		justify-content: center;
		text-align: center;
	}

	.close_btn {
		width: 250upx;
		height: 80upx;
		color: #2E896F;
		font-size: 30upx;
		line-height: 80upx;
		text-align: center;
		border-radius: 45upx;
		box-sizing: border-box;
		border: 1px solid #2E896F;
	}

	// 短信
	.content {
		color: #333;
		border-radius: 15rpx;
		padding: 90rpx 20rpx;
		width: 600rpx;
	}

	/deep/.u-icon--right {
		justify-content: center;
	}
	
	.close-icon{
		position: absolute;
		right: 20rpx;
		top: 20rpx;
	}
	
	// 文本超出隐藏
	.container {
	    width: 200rpx;
	    overflow: hidden;
	    text-overflow: ellipsis;
	    white-space: nowrap;
	}
	
	.total-income{
		color: #FA370E;
	}
	
	.determine_btn{
		width: 250upx;
		height: 80upx;
		color: #FFFFFF;
		font-size: 30upx;
		line-height: 80upx;
		text-align: center;
		border-radius: 45upx;
		background: linear-gradient(180deg, #88CFBA 0%, #1D755C 100%);
	}
	
	
	.input{
		border: 1px solid #C8C8C8;
		border-radius: 50rpx;
		display: flex;
		align-items: center;
		justify-content: space-between;
	}
	
	.reviewCard /deep/ .vue-ref{
		padding: 20rpx 0;
		width: 100%;
	}
</style>
