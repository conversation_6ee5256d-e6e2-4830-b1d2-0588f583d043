<template>
  <view class="bg-ff" :style="{ height: useHeight + 'rpx' }">
    <view class="plr-15 bg-ff radius-20 ptb-30">
      <view class="midTxt">
        <image src="https://document.dxznjy.com/course/7bd71437aeed4c62a3fa36ae89190573.png" mode="scaleToFill" style="width: 750rpx; height: 634rpx"></image>
      </view>
      <view class="botBtn">
        <view class="btn_b b_r" @click="goToday">今日复习</view>
        <view class="btn_b b_l" @click="goPast">往期复习</view>
      </view>
    </view>
  </view>
</template>

<script>
  export default {
    data() {
      return { useHeight: 0, studentCode: '', merchantCode: '', app: 0 };
    },
    onLoad(options) {
      if (options.token) {
        this.app = options.app;
        this.$handleTokenFormNative(options);
      }
      this.studentCode = options.studentCode;
      this.merchantCode = options.merchantCode;
    },
    onReady() {
      uni.getSystemInfo({
        success: (res) => {
          // 可使用窗口高度，将px转换rpx
          this.useHeight = res.windowHeight * (750 / res.windowWidth);
        }
      });
    },
    onUnload() {
      // #ifdef APP-PLUS
      if (this.app) {
        plus.runtime.quit();
      }
      // #endif
    },
    methods: {
      async goToday() {
        let { data } = await this.$httpUser.get('znyy/superReadReview/getReviewNumber?studentCode=' + this.studentCode + '&merchantCode=' + this.merchantCode);
        if (data.success) {
          let a = data.data.todayReviewNum - 0 + (data.data.lastReviewNum - 0);
          if (a > 0) {
            uni.navigateTo({
              url: `/ReadForget/todayForget?studentCode=${this.studentCode}&merchantCode=${this.merchantCode}`
            });
          } else {
            uni.showToast({
              icon: 'none',
              title: '今日无复习的课程'
            });
          }
        }
      },
      goPast() {
        uni.navigateTo({
          url: `/ReadForget/pastForget?studentCode=${this.studentCode}&merchantCode=${this.merchantCode}`
        });
      }
    }
  };
</script>

<style lang="scss" scoped>
  .midTxt {
    margin-top: 50%;
  }
  .botBtn {
    display: flex;
    flex-direction: row-reverse;
    position: absolute;
    bottom: 60rpx;
    right: 32rpx;
  }
  .btn_b {
    width: 328rpx;
    height: 92rpx;
    border-radius: 60rpx;
    line-height: 92rpx;
    text-align: center;
  }
  .b_l {
    background-color: #fff;
    color: #4e9f87;
    border: 1px solid #7baea0;
  }
  .b_r {
    background-image: linear-gradient(to bottom, #88cfba, #1d755c);
    color: #ffffff;
    margin-left: 32rpx;
  }
</style>
