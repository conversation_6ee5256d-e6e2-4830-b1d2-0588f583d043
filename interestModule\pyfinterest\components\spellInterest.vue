<template>
  <view>
    <view class="word">
      <view style="width: 144rpx; height: 144rpx; margin-right: 10rpx">
        <image src="https://document.dxznjy.com/course/ae33333815ed4628a04f1271d58e13dd.png" style="width: 100%; height: 100%"></image>
      </view>
      <view class="wordTitle">
        {{ word.wordSyllable }}
      </view>
    </view>

    <view class="items">
      <view class="item" v-for="(e, i) in list" :class="e.class" @click="checkWord(e, i)">
        {{ e.text }}
      </view>
    </view>
    <view class="" style="margin-top: 40rpx" v-if="isOK">
      <view class="rightItems">
        <view class="rightItem" v-for="(item, index) in word.splitList" :class="word.syllableList.find((e) => e.wordSyllable === item.wordSyllable) ? 'right' : ''">
          {{ item.wordSyllable }}
        </view>
      </view>
    </view>
    <view class="btn" @click="btnOK">{{ isOK ? (end ? '提交' : '下一题') : '确定' }}</view>
    <uni-popup ref="guideOne" type="bottom" :maskClick="true" :classBG="''">
      <view style="position: relative" class="test-bg">
        <image
          @click="guideNext()"
          mode="aspectFit"
          style="width: 100%; height: 100%"
          src="https://document.dxznjy.com/course/c81898649c7e4c08b998ffcf344a59fb.png"
        ></image>
      </view>
    </uni-popup>
  </view>
</template>

<script>
  export default {
    data() {
      return {
        list: [],
        isOK: false,
        screenHeight: 0,
        screenWidth: 0,
        status: 0,
        goNum: 0,
        Guide: uni.getStorageSync('spellGuide')
      };
    },
    props: {
      word: {
        type: Object,
        default: {}
      },
      end: {
        type: Boolean,
        default: false
      }
    },
    computed: {
      computedList() {
        // 为每个 item 计算 className
        return this.list.map((item) => ({
          ...item,
          className: this.getClass(item.type)
        }));
      }
    },
    created() {
      this.init();
      this.goNext();
      this.$emit('setTitle', 1);
    },
    mounted() {
      this.getHeight();
      if (!this.Guide) {
        setTimeout(() => {
          this.$refs.guideOne.open();
        }, 10);
      }
    },
    methods: {
      guideNext() {
        uni.setStorageSync('spellGuide', true);
        this.$refs.guideOne.close();
      },
      init() {
        let arr = this.word.splitList.map((e) => {
          return {
            wordSyllable: e.wordSyllable,
            list: e.wordSyllable.split('').map((o) => {
              return { text: o, type: 0 };
            })
          };
        });
        arr = this.addSuffixToRepeatedSyllables(arr);
        this.list = [];
        arr.forEach((p) => {
          p.list.forEach((e) => {
            this.list.push({
              text: e.text,
              type: 0,
              word: p.wordSyllable
            });
          });
        });
        // console.log(arr);
        // this.list = arr.map((e) => {
        //   return {
        //     text: e,
        //     type: 0
        //   };
        // });
        // this.rightList = JSON.parse(JSON.stringify(this.list));
        // let a = this.word.syllableList.map((e) => e.wordSyllable);
        // a = a.map((e) => e.split(''));
        // a.forEach((p) => {
        //   p.forEach((c) => {
        //     if (this.rightList.find((k) => k.text == c)) {
        //       this.rightList.find((k) => k.text == c).type = 2;
        //     }
        //     //
        //   });
        // });
      },
      addSuffixToRepeatedSyllables(objects) {
        // 创建一个对象来跟踪每个syllable出现的次数
        const syllableCounts = {};
        return objects.map((obj, index) => {
          const wordSyllable = obj.wordSyllable;
          // 如果syllable已经出现过，则增加计数并添加后缀
          if (syllableCounts[wordSyllable]) {
            syllableCounts[wordSyllable]++;
            return {
              ...obj,
              wordSyllable: `${wordSyllable}[${syllableCounts[wordSyllable]}]`
            };
          } else {
            // 如果是第一次出现，则记录该syllable并设置计数为1
            syllableCounts[wordSyllable] = 1;
            return obj;
          }
        });
      },
      getHeight() {
        // 获取系统信息
        const systemInfo = uni.getSystemInfoSync();

        // 获取屏幕高度
        this.screenHeight = systemInfo.windowHeight * 2;
        this.screenWidth = systemInfo.windowWidth * 2;
        // // 打印屏幕高度
        //  console.log(this.screenHeight,"打印屏幕高度");
      },
      btnOK() {
        if (this.isOK) {
          this.$emit('next', this.status, this.goNum);
        } else {
          if (!this.list.find((e) => e.type == 1)) {
            return uni.showToast({
              icon: 'none',
              title: '请选择'
            });
          }
          let tempA = this.addSuffixToRepeatedSyllables(this.word.syllableList);
          let a = tempA.map((e) => e.wordSyllable);
          a.forEach((e) => {
            this.list.forEach((c, i) => {
              if (e == c.word) {
                if (c.type == 1) {
                  c.type = 2;
                  c.class = 'right';
                } else {
                  c.type = 3;
                  c.class = 'error';
                }
              }
            });
          });
          this.list.forEach((c, i) => {
            if (c.type == 0) {
            } else if (c.type == 1) {
              c.type = 3;
              c.class = 'error';
            }
          });
          this.$forceUpdate();

          this.isOK = true;
          let temp = true;
          this.list.forEach((c, i) => {
            if (c.type == 3) {
              temp = false;
            }
          });
          if (!temp) {
            this.status = 0;
          } else {
            this.status = 1;
          }
        }
      },
      checkWord(e, i) {
        if (this.isOK) return;
        if (this.list[i].type == 0) {
          this.list[i].type = 1;
          this.list[i].class = 'check';
        } else if (this.list[i].type == 1) {
          this.list[i].type = 0;
          this.list[i].class = '';
        }
        this.$forceUpdate();
      },
      goNext() {
        if (uni.getStorageSync('pyGrade') == '2') {
          // 高年级划音节
          if (this.word.syllableList.find((e) => e.wordSyllableType == 3)) {
            this.goNum = 2;
          } else if (this.word.syllableList.find((e) => e.wordSyllableType == 7)) {
            this.goNum = 3;
          } else if (this.word.syllableList.find((e) => e.wordSyllableType == 2 && e.syllableList.length)) {
            this.goNum = 4;
          } else {
            this.goNum = 7;
            this.$emit('goNum', this.goNum);
          }
        } else {
          if (this.word.syllableList.find((e) => e.wordSyllableType == 3)) {
            this.goNum = 2;
          } else if (this.word.syllableList.find((e) => e.syllableList.length)) {
            this.goNum = 4;
          } else {
            this.goNum = 6;
            this.$emit('goNum', this.goNum);
          }
        }
      },
      getClass(type) {
        // 根据 type 返回对应的类名
        switch (type) {
          case 1:
            return 'check';
          case 2:
            return 'right';
          case 3:
            return 'error';
          default:
            return '';
        }
      }
    }
  };
</script>

<style scoped lang="scss">
  .test-bg {
    padding-top: env(safe-area-inset-top);
    // padding-bottom: env(safe-area-inset-bottom);
    height: 100vh;
    box-sizing: border-box;
  }
  .guide_btn_close {
    position: absolute;
    bottom: 179rpx;
    right: 110rpx;
    width: 312rpx;
    height: 93rpx;
  }
  .rightItems {
    width: 686rpx;
    // height: 190rpx;
    max-height: 286rpx;
    overflow-y: auto;
    margin: 0 auto;
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    align-items: center;
    border-radius: 32rpx;
    padding: 20rpx 0;
    background-color: rgba(255, 255, 255, 0.51);
    .rightItem {
      background-color: #fff;
      color: #555555;
      font-size: 36rpx;
      height: 92rpx;
      line-height: 92rpx;
      margin-right: 8rpx;
      border-radius: 16rpx;
      margin-bottom: 10rpx;
      padding: 0 60rpx;
    }

    .right {
      background-color: #4bb051;

      color: #ffffff;
    }
  }
  .btn {
    width: 632rpx;
    height: 84rpx;
    background: #89c844;
    box-shadow: 0 16rpx 2rpx -2rpx rgba(99, 180, 11, 1);
    border-radius: 42rpx;
    text-align: center;
    line-height: 84rpx;
    position: fixed;
    bottom: 50rpx;
    left: 50%;
    transform: translateX(-50%);
  }
  .items {
    // width: 600rpx;
    display: flex;
    padding: 0 32rpx;
    box-sizing: border-box;
    flex-wrap: wrap;
    justify-content: center;
    .item {
      height: 92.11rpx;
      width: 120rpx;
      box-sizing: border-box;
      // border: 2rpx solid #000;
      margin-right: 14rpx;
      text-align: center;
      background-color: #ffffff;
      color: #555555;
      font-size: 36rpx;
      border-radius: 24rpx;
      line-height: 90rpx;
      box-shadow: 0 16rpx 2rpx -2rpx rgba(97, 170, 244, 1);
      margin-bottom: 34rpx;

      &:nth-child(5n) {
        margin-right: 0;
      }
    }
    .check {
      border: 2rpx solid #ffc800;
      background-color: #fff7db;
    }
    .right {
      background-color: #4bb051;
      box-shadow: 0 16rpx 2rpx -2rpx rgba(57, 141, 61, 1);
      color: #ffffff;
    }
    .error {
      background-color: #ffa332;
      box-shadow: 0 16rpx 2rpx -2rpx rgba(231, 133, 13, 1);
      color: #ffffff;
    }
  }
  .word {
    margin-top: 60rpx;
    height: 144rpx;
    padding-left: 32rpx;
    display: flex;
    align-items: center;
    margin-bottom: 90rpx;
  }
  .wordTitle {
    width: 474rpx;
    height: 110rpx;
    padding-left: 14rpx;
    background: url('https://document.dxznjy.com/course/a37e49a58c724f7ca8d2fbd8ce85252d.png') no-repeat;
    background-size: contain;
    text-align: center;
    line-height: 100rpx;
    font-size: 40rpx;
    color: #555555;
  }
</style>
