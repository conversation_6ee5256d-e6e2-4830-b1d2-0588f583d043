<template>
  <view>
    <page-meta :page-style="'overflow:' + (show || viewQuestionShow ? 'hidden' : 'visible')"></page-meta>
    <view class="container">
      <view class="header-area">
        <!-- 搜索区域 -->
        <view class="search-area" style="position: relative">
          <view class="flex-c">
            <!-- 题目类型按钮及下拉 -->
            <view class="select-item-wrap">
              <view class="select-item f-30 mr-20" @click="showTypeFilter">
                <text class="select-label">题目类型</text>
                <uni-icons type="bottom" size="18" color="#bbb" :class="{ 'icon-rotate': showTypeFilterPopup }"></uni-icons>
              </view>
              <view v-if="showTypeFilterPopup" class="dropdown-popup" style="left: 0" @click.stop>
                <view
                  v-for="type in questionTypeList"
                  :key="type.value"
                  class="filter-option"
                  :class="{ active: selectedTypeList.includes(type.value) }"
                  @click="toggleType(type.value)"
                >
                  {{ type.desc }}
                </view>
              </view>
            </view>
            <!-- 难度按钮及下拉 -->
            <view class="select-item-wrap">
              <view class="select-item f-30" @click="showDifficultyFilter">
                <text class="select-label">难度</text>
                <uni-icons type="bottom" size="18" color="#bbb" :class="{ 'icon-rotate': showDifficultyFilterPopup }"></uni-icons>
              </view>
              <view v-if="showDifficultyFilterPopup" class="dropdown-popup" style="left: 0" @click.stop>
                <view
                  v-for="diff in questionDifficultyList"
                  :key="diff.type"
                  class="filter-option"
                  :class="{ active: selectedDifficultyList.includes(diff.type) }"
                  @click="toggleDifficulty(diff.type)"
                >
                  {{ diff.name }}
                </view>
              </view>
            </view>
          </view>
          <view class="t-r mr-12" @click="refresh">
            <image src="https://document.dxznjy.com/dxSelect/fca7703c-0de5-4f79-bb57-35e3b4010648.png" mode="" style="width: 40rpx; height: 40rpx"></image>
          </view>
          <!-- 遮罩 -->
          <view
            v-if="showTypeFilterPopup || showDifficultyFilterPopup"
            class="dropdown-mask"
            @click="
              showTypeFilterPopup = false;
              showDifficultyFilterPopup = false;
            "
          ></view>
        </view>
      </view>

      <!-- 题目列表 -->
      <view v-for="(group, groupIdx) in questionList" :key="groupIdx">
        <!-- 日期 -->
        <view class="moment-area f-30 c-99 lh-80 h-80 bg-ff" style="position: relative">
          <text class="f-30 ml-18">{{ group.date }}</text>
          <text class="line"></text>
        </view>
        <view class="question-list-area bg-ff" v-for="(item, index) in group.questionList" :key="item.id">
          <viewQuestion ref="viewQuestion" :item="item" :index="index" :status="1" :deleteDisabled="item.deleteDisabled"></viewQuestion>
        </view>
      </view>
      <view v-if="!hasMoreQuestion" class="no-more">没有更多数据了</view>

      <!-- 视频弹框区域 -->
      <u-popup :closeOnClickOverlay="false" :show="show" :round="10" mode="bottom" @close="close" @change="changePopup" class="knowledgePointVideo">
        <view class="flex-c" style="padding: 32rpx 30rpx 36rpx; position: relative">
          <text class="f-34 c-33 knowledgePointVideoText">知识点视频</text>
          <view style="position: absolute; right: 38rpx; top: 38rpx" @click="close">
            <u-icon name="close" size="20" color="#333"></u-icon>
          </view>
        </view>
        <view class="ml-30 mr-30" style="display: inline-block; height: 1rpx; background: rgba(153, 153, 153, 0.3)"></view>
        <scroll-view
          :scroll-top="0"
          scroll-y="true"
          style="height: 600rpx"
          @touchmove.stop.prevent
          @scrolltolower="onVideoScrollToLower"
          refresher-enabled
          :refresher-triggered="isRefreshing"
          @refresherrefresh="onVideoRefresh"
        >
          <view class="video-row flex-c f-32 c-33 bg-ff" v-for="(item, index) in videoList" :key="index" @click="goVideoPlay(index)">
            <text class="video-title" :style="{ color: item.isUnlock ? '#000' : '#666' }">{{ item.videoName }}</text>
            <view class="video-play-btn flex-c">
              <image src="https://document.dxznjy.com/dxSelect/df8d8077-0516-494c-82a1-76e282564cb0.png" mode="" style="width: 48rpx; height: 48rpx"></image>
            </view>
          </view>
          <view v-if="!hasMoreVideo" class="no-more">没有更多数据了</view>
        </scroll-view>
      </u-popup>

      <!-- 查看原题 -->
      <u-popup :closeOnClickOverlay="false" :show="viewQuestionShow" :round="20" mode="bottom" @change="changePopup">
        <view class="expandContent pl-20 pr-20">
          <view class="questionArea">
            <view class="iconSet" @click="closeViewQuestion"></view>
            <view style="overflow-y: auto; height: 800rpx">
              <viewQuestion v-if="viewQuestionShow" :item="originalQuestionData" :index="0" :status="2"></viewQuestion>
            </view>
          </view>
        </view>
      </u-popup>

      <!-- 举一反三 -->
      <uni-popup ref="doingExercises" :mask-click="false" type="center" @change="changePopup">
        <view class="dialogBG">
          <view class="reviewCard_box positionRelative">
            <view class="review_close" @click="closeStartTest">
              <uni-icons type="clear" size="28" color="#B1B1B1"></uni-icons>
            </view>
            <view class="reviewCard">
              <view class="reviewTitle bold pb-10">举一反三</view>
              <view class="mb-20" style="text-align: center">根据错题创建训练题目</view>
              <view class="mask-footer">
                <button class="cancel-button" @click="closeStartTest">取消</button>
                <button class="confirm-button" @click="startTestComfig">确定</button>
              </view>
            </view>
          </view>
        </view>
      </uni-popup>

      <!-- 删除 -->
      <uni-popup ref="deleteConfirm" :mask-click="false" type="center" @change="changePopup">
        <view class="dialogBG">
          <view class="review_close" @click="closeDeleteConfirm">
            <uni-icons type="clear" size="28" color="#B1B1B1"></uni-icons>
          </view>
          <view class="reviewCard_box positionRelative">
            <view class="reviewCard">
              <view class="reviewTitle bold pb-10">移除错题本</view>
              <view class="mb-20 mt-20" style="text-align: center">确认是否将此题移出错题本？</view>
              <view class="mask-footer">
                <button class="cancel-button" @click="closeDeleteConfirm">取消</button>
                <button class="confirm-button" @click="confirmDelete">确定</button>
              </view>
            </view>
          </view>
        </view>
      </uni-popup>
    </view>
  </view>
</template>
<script>
  import viewQuestion from '@/errorBook/components/viewQuestion.vue';
  export default {
    data() {
      return {
        page: {
          pageSize: 10,
          pageNum: 1
        },
        currentDate: '',
        isPreviewingImage: false,
        isPlayingVideo: false,

        show: false,
        // 查看原题
        total: 0,
        viewQuestionShow: false,
        rollShow: false,
        expandImproveShow: false,

        // 跳转数学错题本参数
        selectedStudentCode: '',
        selectedPickTypeId: '',
        PhaseStudentId: '',

        questionList: [],
        collapseValue: [0],

        videoList: [],
        // 原题数据
        originalQuestionData: [],
        deleteDisabled: false,
        questionTypeList: [],
        questionDifficultyList: [],

        showTypeFilterPopup: false,
        showDifficultyFilterPopup: false,
        selectedTypeList: [''],
        selectedDifficultyList: [''],
        // 视频列表相关
        hasMoreVideo: true,
        isRefreshing: false,
        videoPageNum: 1,
        videoPageSize: 10,
        answerQuestionId: '',
        // 错题列表相关
        hasMoreQuestion: true,
        isQuestionLoading: false
      };
    },
    components: {
      viewQuestion
    },
    onLoad(options) {
      // 学员-阶段-学科
      this.selectedStudentCode = options.studentCode;
      this.PhaseStudentId = options.phase;
      this.selectedPickTypeId = options.subjectId;
      this.getQuestionTypeAndDifficulty();
    },
    onShow() {
      // 如果是图片预览或视频播放触发的 onShow，则不重新加载数据
      if (this.isPreviewingImage || this.isPlayingVideo) {
        this.isPreviewingImage = false;
        this.isPlayingVideo = false;
        return;
      }
      this.page.pageNum = 1;
      this.hasMoreQuestion = true;
      this.questionList = [];
      this.getErrorQuestions();
    },
    onReachBottom() {
      if (!this.hasMoreQuestion || this.isQuestionLoading) return;
      let totalQuestions = this.questionList.reduce((sum, group) => sum + (group.questionList ? group.questionList.length : 0), 0);
      if (totalQuestions >= this.page.pageSize * this.page.pageNum) {
        this.page.pageNum += 1;
        this.getErrorQuestions(true, this.page.pageNum);
      }
    },

    methods: {
      // 获取题目类型和难度
      getQuestionTypeAndDifficulty() {
        this.$httpUser
          .get('dyf/math/wap/common/enum/list', {
            type: 'testPaperQuestionType'
          })
          .then((res) => {
            if (res.data.success) {
              this.questionTypeList = [{ desc: '全部', value: '' }, ...res.data.data];
            } else {
              this.questionTypeList = [];
            }
          });
        this.$httpUser
          .get('dyf/math/wap/common/enum/list', {
            type: 'QuestionDifficultyForAppType'
          })
          .then((res) => {
            if (res.data.success) {
              this.questionDifficultyList = [{ name: '全部', type: '' }, ...res.data.data];
            } else {
              this.questionDifficultyList = [];
            }
          });
      },
      formatDate(dateStr) {
        if (!dateStr) return '';
        const date = new Date(dateStr);
        const year = date.getFullYear();
        const month = date.getMonth() + 1;
        const day = date.getDate();
        return `${year}年${month}月${day}日`;
      },
      //   错题本列表
      getErrorQuestions(loadMore = false, page) {
        if (!this.hasMoreQuestion || this.isQuestionLoading) return;
        this.isQuestionLoading = true;
        // 只选全部时，参数为空字符串，否则用逗号拼接
        let questionType = this.selectedTypeList.includes('') ? '' : this.selectedTypeList.join(',');
        let questionDifficulty = this.selectedDifficultyList.includes('') ? '' : this.selectedDifficultyList.join(',');
        uni.showLoading({ title: '加载中' });
        this.$httpUser
          .get('dyf/math/wap/correctionNoteBook/page', {
            pageNum: page || 1,
            pageSize: this.page.pageSize,
            studentCode: this.selectedStudentCode,
            // 学科
            // disciplineId: selectInfo.disciplineId,
            // 学段
            gradeId: this.PhaseStudentId,
            questionType,
            questionDifficulty
          })
          .then((res) => {
            const newGroups = res.data.data.data || [];
            this.total = res.data.data.totalItems;
            if (newGroups.length > 0) {
              this.questionList = loadMore ? [...this.questionList, ...newGroups] : newGroups;
              let loadedQuestions = this.questionList.reduce((sum, group) => sum + (group.questionList ? group.questionList.length : 0), 0);
              this.hasMoreQuestion = loadedQuestions < this.total;
            } else {
              this.hasMoreQuestion = false;
            }
            this.isQuestionLoading = false;
          })
          .catch((err) => {
            uni.showToast({ title: '加载失败', icon: 'none' });
            this.isQuestionLoading = false;
          })
          .finally(() => {
            // 隐藏 loading
            uni.hideLoading();
          });
      },
      // 刷新
      refresh() {
        this.deleteDisabled = false;
        this.selectedTypeList = [''];
        this.selectedDifficultyList = [''];
        this.showTypeFilterPopup = false;
        this.showDifficultyFilterPopup = false;
        this.page.pageNum = 1;
        this.hasMoreQuestion = true;
        this.questionList = [];
        this.getErrorQuestions();
      },
      // 禁止滚动穿透
      changePopup(e) {
        this.rollShow = e.show;
      },
      //   去学习
      goStudyBut(id) {
        this.rollShow = true;
        this.show = true;
        this.answerQuestionId = id;
        this.videoPageNum = 1;
        this.hasMoreVideo = true;
        this.videoList = []; // 清空之前的视频列表
        // uni.showLoading({ title: '加载中' });
        this.$httpUser
          .get('dyf/math/wap/correctionNoteBook/videoPage', {
            answerQuestionId: this.answerQuestionId,
            studentCode: this.selectedStudentCode,
            pageNum: this.videoPageNum,
            pageSize: this.videoPageSize,
            studentCode: this.selectedStudentCode
          })
          .then((res) => {
            uni.hideLoading();
            const newVideos = res.data.data.data || [];
            if (newVideos.length > 0) {
              this.videoList = newVideos;
              this.hasMoreVideo = newVideos.length >= this.videoPageSize;
            } else {
              this.hasMoreVideo = false;
            }
          })
          .catch((err) => {
            uni.showToast({ title: '加载失败', icon: 'none' });
            uni.hideLoading();
          });
      },
      goVideoPlay(index) {
        if (this.videoList[index].isUnlock == 0) {
          uni.showToast({
            title: '该视频暂未解锁',
            icon: 'none'
          });
        } else {
          this.isPlayingVideo = true; // 设置视频播放标记
          uni.navigateTo({
            url: `/knowledgeGraph/knowVideoPlay?type=1&index=${index}&videoInfo=` + JSON.stringify(this.videoList)
          });
        }
      },
      close() {
        this.show = false;
        this.rollShow = false;
      },
      // 查看原题
      viewQuestionClick(id) {
        this.rollShow = true;
        this.$httpUser.get('dyf/math/wap/correctionNoteBook/getInfo?id=' + id).then((res) => {
          if (res.data.success) {
            this.originalQuestionData = res.data.data;
            this.viewQuestionShow = true;
          } else {
            this.originalQuestionData = [];
            this.viewQuestionShow = false;
          }
        });
      },
      closeViewQuestion() {
        this.viewQuestionShow = false;
        this.rollShow = false;
      },
      // 单题更新
      singleQuestionUpdate(id) {
        this.$httpUser.put('dyf/math/wap/correctionNoteBook/singleUpdate?answerQuestionId=' + id).then((res) => {
          if (res.data.success) {
            this.refresh();
          } else {
            this.$toast(res.data.message);
          }
        });
      },
      //   删除更新
      deleteQuestion(id) {
        const question = this.questionList.find((q) => q.id === id);
        if (question && question.deleteDisabled) return;
        this.answerQuestionId = id;
        this.$refs.deleteConfirm.open();
      },
      closeDeleteConfirm() {
        this.$refs.deleteConfirm.close();
      },
      confirmDelete() {
        this.$httpUser.delete('dyf/math/wap/correctionNoteBook/delete?id=' + this.answerQuestionId).then((res) => {
          if (res.data.success) {
            uni.showToast({
              title: '移出成功，请手动刷新',
              icon: 'none'
            });
            // 寻找包含这个题目的组和索引
            for (let groupIdx = 0; groupIdx < this.questionList.length; groupIdx++) {
              const group = this.questionList[groupIdx];
              const itemIdx = group.questionList.findIndex((q) => q.id === this.answerQuestionId);
              if (itemIdx !== -1) {
                // 使用Vue的响应式更新方法修改deleteDisabled属性
                this.$set(group.questionList[itemIdx], 'deleteDisabled', true);
                break;
              }
            }
          } else {
            uni.showToast({
              title: res.message,
              icon: 'none'
            });
          }
          this.$refs.deleteConfirm.close();
        });
      },
      // 举一反三
      goExpandImprove(id) {
        this.answerQuestionId = id;
        this.$refs.doingExercises.open();
      },
      closeStartTest() {
        this.$refs.doingExercises.close();
      },
      startTestComfig() {
        let selectInfo = uni.getStorageSync('selectInfo');
        uni.showLoading({
          title: '加载中...'
        });
        this.$httpUser
          .get('dyf/math/wap/correctionNoteBook/getQuestionByUnCorrect', {
            answerQuestionId: this.answerQuestionId,
            studentCode: this.selectedStudentCode
          })
          .then((res) => {
            if (res.data.success) {
              const encodedData = encodeURIComponent(JSON.stringify(res.data.data));
              uni.navigateTo({
                url: `/errorBook/expandImprove?expandImproveData=${encodedData}&studentCode=${this.selectedStudentCode}`
              });
            } else {
              uni.showToast({
                title: res.message,
                icon: 'none'
              });
            }
          })
          .catch((err) => {
            uni.showToast({
              title: '请求失败',
              icon: 'none'
            });
          })
          .finally(() => {
            uni.hideLoading();
            this.$refs.doingExercises.close();
          });
      },
      showTypeFilter() {
        this.showTypeFilterPopup = true;
      },
      showDifficultyFilter() {
        this.showDifficultyFilterPopup = true;
      },
      // 题目类型多选逻辑
      toggleType(val) {
        if (val === '') {
          // 选"全部"时，清空其他选项，只选全部
          this.selectedTypeList = [''];
        } else {
          let idx = this.selectedTypeList.indexOf(val);
          if (idx > -1) {
            // 已选中则取消
            this.selectedTypeList.splice(idx, 1);
            if (this.selectedTypeList.length === 0) {
              this.selectedTypeList = ['']; // 没有选中任何项时，回到"全部"
            }
          } else {
            // 新增选项
            this.selectedTypeList = this.selectedTypeList.filter((i) => i !== ''); // 取消"全部"
            this.selectedTypeList.push(val);
          }
        }
        // 重置状态
        this.page.pageNum = 1;
        this.hasMoreQuestion = true;
        this.questionList = [];
        this.getErrorQuestions(); // 每次选择后刷新
      },
      // 难度同理
      toggleDifficulty(val) {
        if (val === '') {
          this.selectedDifficultyList = [''];
        } else {
          let idx = this.selectedDifficultyList.indexOf(val);
          if (idx > -1) {
            this.selectedDifficultyList.splice(idx, 1);
            if (this.selectedDifficultyList.length === 0) {
              this.selectedDifficultyList = [''];
            }
          } else {
            this.selectedDifficultyList = this.selectedDifficultyList.filter((i) => i !== '');
            this.selectedDifficultyList.push(val);
          }
        }
        // 重置状态
        this.page.pageNum = 1;
        this.hasMoreQuestion = true;
        this.questionList = [];
        this.getErrorQuestions(); // 每次选择后刷新
      },
      // 视频列表下拉刷新
      onVideoRefresh() {
        this.isRefreshing = true;
        this.videoPageNum = 1;
        this.hasMoreVideo = true;
        this.getVideoList();
      },
      // 视频列表滚动到底部
      onVideoScrollToLower() {
        if (this.hasMoreVideo) {
          this.videoPageNum++;
          this.getVideoList(true);
        }
      },
      // 获取视频列表
      getVideoList(loadMore = false) {
        if (!this.hasMoreVideo) return;
        this.$httpUser
          .get('dyf/math/wap/correctionNoteBook/videoPage', {
            answerQuestionId: this.answerQuestionId,
            studentCode: this.selectedStudentCode,
            pageNum: this.videoPageNum,
            pageSize: this.videoPageSize
          })
          .then((res) => {
            const newVideos = res.data.data.data || [];
            if (newVideos.length > 0) {
              this.videoList = loadMore ? [...this.videoList, ...newVideos] : newVideos;
            } else {
              this.hasMoreVideo = false;
            }
            this.isRefreshing = false;
          })
          .catch((err) => {
            uni.showToast({ title: '加载失败', icon: 'none' });
            this.isRefreshing = false;
          });
      }
    }
  };
</script>

<style lang="scss" scoped>
  .container {
    width: 100%;
    height: 100vh; /* 使容器高度占满整个视口 */
    padding: 0 20rpx;
    box-sizing: border-box;
  }
  .header-area {
    width: 100%;
    padding: 20rpx 20rpx 0;
    background-color: #fff;
    box-sizing: border-box;
  }
  .search-area {
    width: 100%;
    height: 87rpx;
    background: #f7f7f7;
    border-radius: 8rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 18rpx;
    box-sizing: border-box;
    position: relative;
  }
  .select-item {
    display: flex;
    align-items: center;
    color: #333333;
    height: 100%;
    cursor: pointer;
    justify-content: center; /* 内容居中 */
  }
  .select-item:first-child {
    // width: 180rpx;
    justify-content: flex-start;
  }
  .select-item:last-child {
    // width: 120rpx;
    justify-content: flex-end;
  }
  .select-label {
    margin-right: 8rpx;
    color: #222;
  }

  .componentMargin {
    margin-left: -20rpx;
    margin-right: -20rpx;
  }
  .question-list-area {
    padding: 0 20rpx 20rpx;
    box-sizing: border-box;
    margin-bottom: 20rpx;
  }
  .container > .question-list-area + .question-list-area {
    margin-top: 20rpx;
  }
  .moment-area {
    padding: 0 20rpx;
  }
  .line {
    display: inline-block;
    width: 94%;
    height: 2rpx;
    background: #979797;
    position: absolute;
    left: 20rpx;
    bottom: 0;
  }
  /deep/ .u-cell__body {
    padding: 22rpx 56rpx !important;
  }
  /deep/ .u-cell {
    background: #fbfbfb;
  }
  /deep/ .u-cell__title-text {
    font-size: 28rpx !important;
    color: #428a6f !important;
  }
  /deep/.u-collapse-item__content__text {
    background: #effbf6;
    padding: 5rpx 12rpx 29rpx 47rpx !important;
  }
  .question-btn {
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 20rpx 0;
  }
  .btn-setting {
    border: 2rpx solid #428a6f;
    box-sizing: border-box;
  }

  .option {
    width: 626rpx;
    height: 90rpx;
    line-height: 90rpx;
    border-radius: 20rpx;
    padding-left: 33rpx;
    box-sizing: border-box;
    border: 1rpx solid #efeff0;
  }
  .knowledgePointVideoText {
    font-weight: 400;
    line-height: 48rpx;
  }
  .video-row {
    font-weight: 400;
    padding: 24rpx 80rpx 24rpx 53rpx;
    line-height: 45rpx;
    box-sizing: border-box;
  }
  .video-title {
    flex: 1;
    word-wrap: break-word;
    word-break: break-all;
    white-space: normal;
  }
  .video-play-btn {
    width: 56rpx;
    height: 56rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #effbf6;
    border-radius: 50%;
    margin-left: 20rpx;
  }
  .iconSet {
    width: 80rpx;
    height: 40rpx;
    background: url('https://document.dxznjy.com/dxSelect/7b90d16d-520f-4247-95a6-f3755ca8c4ab.png') center/cover no-repeat;
    margin: 0 auto 27rpx;
  }
  .viewAnalysis /deep/ .u-cell__body {
    background: #effbf7;
  }

  .filter-popup {
    display: flex;
    flex-direction: column;
    background: #fff;
    border-radius: 12rpx;
    padding: 20rpx;
  }
  .filter-option {
    padding: 16rpx 10rpx;
    text-align: center;
    color: #222;
    font-size: 32rpx;
    border-bottom: 1rpx solid #f0f0f0;
  }
  .filter-option:last-child {
    border-bottom: none;
  }
  .filter-option.active {
    color: #428a6f;
    font-weight: bold;
  }

  .select-item-wrap {
    position: relative;
    display: inline-block;
  }
  .dropdown-popup {
    position: absolute;
    top: 100%; // 紧贴按钮下方
    left: 0;
    min-width: 140rpx;
    background: #fff;
    border-radius: 12rpx;
    box-shadow: 0 4rpx 24rpx rgba(0, 0, 0, 0.08);
    z-index: 11;
    padding: 10rpx 0;
  }

  .dropdown-mask {
    position: fixed;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
    background: transparent;
    z-index: 10;
  }

  .icon-rotate {
    display: inline-block;
    transition: transform 0.3s;
    transform: rotate(180deg);
  }
  .no-more {
    text-align: center;
    padding: 15px;
    color: #999;
    font-size: 14px;
  }
  /* 弹窗样式 */
  .dialogBG {
    width: 100%;
    /* height: 100%; */
    position: relative;
  }
  .review_close {
    position: absolute;
    top: 20rpx;
    right: 20rpx;
    z-index: 1;
  }
  /* 21天结束复习弹窗样式 */
  .reviewCard_box {
    width: 670rpx;
    /* height: 560rpx; */
    position: relative;
  }
  .reviewCard_box {
    width: 670rpx;
    /* height: 560rpx; */
    position: relative;
  }
  .confirm-button {
    display: flex;
    justify-content: center;
    align-items: center;
    /* 文本垂直居中对齐 */
    background: #2e896f;
    color: #ffffff !important;
  }
  .mask-footer {
    margin-top: 20px;
    display: flex;
    justify-content: space-around;
  }

  .mask-footer button {
    width: 250rpx;
    height: 80rpx;
    font-size: 30rpx;
    border-radius: 45rpx;
  }
  .cancel-button {
    display: flex;
    justify-content: space-around;
    align-items: center;
    /* 文本垂直居中对齐 */
    color: #2e896f !important;
    border: 1rpx solid #2e896f !important;
  }
  .reviewCard_box image {
    width: 100%;
    height: 100%;
  }
  .reviewCard {
    position: relative;
    width: 100%;
    height: 100%;
    background: #ffffff;
    color: #000;
    border-radius: 24upx;
    padding: 50upx 55upx;
    box-sizing: border-box;
  }
  .reviewTitle {
    width: 100%;
    text-align: center;
    font-size: 34upx;
    display: flex;
    justify-content: center;
  }
</style>
