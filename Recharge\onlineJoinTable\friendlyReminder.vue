<template>
  <view class="container">
    <view class="content-area">
      <view class="toast1">
        <image class="toast-icon" src="https://document.dxznjy.com/course/646ec3f4e378415da3834f3c33dce03c.png" mode="aspectFit"></image>
        <text>恭喜你上课信息对接表填写成功</text>
      </view>
      <view class="toast2">请下载二维码并分享给家长</view>
      <view class="toast3">邀请家长下载“鼎校甄选”APP，否则学员将无法上课!</view>
      <view class="qr-img">
        <image class="img" :src="testQrcode" mode="aspectFit"></image>
      </view>
      <view class="download-btn" @click="downloadQrImg">下载二维码</view>
    </view>

    <!-- 识别二维码弹出层 -->
    <view>
      <uni-popup ref="popup">
        <view class="popup-content">
          <view class="recognize" @click="recognizeQrCode">识别二维码</view>
          <view class="margin-area"></view>
          <view class="cancel" @click="popupCancel">取消</view>
        </view>
      </uni-popup>
    </view>
  </view>
</template>

<script>
  export default {
    data() {
      return {
        app: 0,
        zxAppQrCode: 'https://document.dxznjy.com/course/da3435044a064572a64dd9a7a028c26d.png',
        testQrcode: 'https://document.dxznjy.com/course/cfc6004182764aa38e8a96f01390299a.png'
        // testQrcode: 'https://document.dxznjy.com/course/caa5b1386c8b4e6ea5d40abfd30d2a2b.png'
      };
    },
    onLoad(e) {
      // this.app = e?.app ?? 0;
    },
    methods: {
      showActionMenu() {
        this.$refs.popup.open('bottom');
      },
      recognizeQrCode() {
        uni.navigateTo({
          url: '/Trialclass/downloadGuide'
        });
      },
      popupCancel() {
        this.$refs.popup.close();
      },
      downloadQrImg() {
        let that = this;
        uni.downloadFile({
          url: that.testQrcode, //鼎校APP下载二维码
          success: (res) => {
            // console.log('保存文件地址', res.tempFilePath);
            if (res.statusCode === 200) {
              // #ifdef MP-WEIXIN
              const filePath = res.tempFilePath;
              const newFilePath = `${wx.env.USER_DATA_PATH}/qrcode.png`; // 改成你想要的文件名和后缀
              // 使用 FileSystemManager 复制并加后缀
              const fs = wx.getFileSystemManager();
              fs.copyFile({
                srcPath: filePath,
                destPath: newFilePath,
                success: () => {
                  uni.saveImageToPhotosAlbum({
                    filePath: newFilePath,
                    success: () => {
                      uni.showToast({ title: '图片已保存', duration: 2000 });
                    },
                    fail: (err) => {
                      uni.showToast({ title: '保存失败', duration: 2000, icon: 'none' });
                    }
                  });
                },
                fail: (err) => {
                  console.error('文件重命名失败', err);
                  uni.showToast({ title: '处理失败', duration: 2000, icon: 'none' });
                }
              });
              // #endif
              // #ifndef MP-WEIXIN
              uni.saveImageToPhotosAlbum({
                filePath: res.tempFilePath,
                success: function () {
                  uni.showToast({
                    title: '图片已保存',
                    duration: 2000
                  });
                },
                fail: function () {
                  uni.showToast({
                    title: '保存失败',
                    duration: 2000,
                    icon: 'none'
                  });
                }
              });
              // #endif
            }
          },
          fail: (err) => {
            console.log(err, '下载失败');
          }
        });
      }
    }
  };
</script>

<style lang="scss" scoped>
  .container {
    width: 100%;
    min-height: 100vh;
    padding: 20rpx;
    padding-top: 0;
    box-sizing: border-box;
    background-image: url('https://document.dxznjy.com/course/da3435044a064572a64dd9a7a028c26d.png');
    background-size: 100% 100%;
    background-repeat: no-repeat;
    position: relative;
  }
  .content-area {
    width: 100%;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding-top: 434rpx;
  }
  .toast1 {
    color: #006f57;
    font-size: 36rpx;
    font-family: AlibabaPuHuiTi_3_85_Bold;
    margin-bottom: 40rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    .toast-icon {
      width: 55rpx;
      height: 55rpx;
      margin-right: 25rpx;
    }
  }

  .toast2,
  .toast3 {
    width: 500rpx;
    font-size: 30rpx;
    font-weight: 500;
    line-height: 50rpx;
    text-align: left;
    color: #666;
    font-family: AlibabaPuHuiTi_3_55_Regular;
  }
  .qr-img {
    width: 360rpx;
    height: 360rpx;
    // background-color: #666;
    margin-top: 28rpx;
    .img {
      width: 100%;
      height: 100%;
    }
  }
  .download-btn {
    width: 357rpx;
    height: 94rpx;
    border-radius: 47rpx;
    background-color: #428a6f;
    display: inline-block;
    color: #fff;
    font-family: AlibabaPuHuiTi_3_85_Bold;
    font-size: 36rpx;
    line-height: 94rpx;
    text-align: center;
    position: absolute;
    bottom: 74rpx;
    left: 50%;
    transform: translateX(-50%);
  }

  // 弹出层样式
  .popup-content {
    border-top-left-radius: 20rpx;
    border-top-right-radius: 20rpx;
    background-color: #fff;
    text-align: center;
    .recognize,
    .cancel {
      height: 110rpx;
      line-height: 110rpx;
      font-size: 32rpx;
      color: #333;
    }
    .margin-area {
      height: 20rpx;
      background-color: #f0f0f0;
    }
  }
</style>
