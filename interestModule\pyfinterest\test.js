export const wordlist=[{"id":"1325918367601545216","wordSyllable":"animal","wordSyllableAudioUrl":"","wordSyllableType":"1","wordRemark":"animal-a,ni,ma-ni--a,ni,ma,l/ni(i)&ma","wordType":4,"isSelect":3,"videoUrl":"1325918021869260800","isReadSelect":1,"isWriteSelect":1,"syllableList":[{"id":"1325918367953866752","wordSyllable":"a","wordSyllableAudioUrl":"","wordSyllableType":"2","wordRemark":"animal-a,ni,ma-ni--a,ni,ma,l/ni(i)&ma","wordType":4,"isSelect":0,"videoUrl":"1325918021869260800","isReadSelect":0,"isWriteSelect":0,"syllableList":[],"splitList":null},{"id":"1325918368301993984","wordSyllable":"ni","wordSyllableAudioUrl":"","wordSyllableType":"3","wordRemark":"animal-a,ni,ma-ni--a,ni,ma,l/ni(i)&ma","wordType":4,"isSelect":0,"videoUrl":"1325918021869260800","isReadSelect":0,"isWriteSelect":0,"syllableList":[{"id":"1325918368650121216","wordSyllable":"ni(i)","wordSyllableAudioUrl":"","wordSyllableType":"9","wordRemark":"animal-a,ni,ma-ni--a,ni,ma,l/ni(i)&ma","wordType":4,"isSelect":0,"videoUrl":"1325918021869260800","isReadSelect":0,"isWriteSelect":0,"syllableList":null,"splitList":null}],"splitList":null},{"id":"1325918368998248448","wordSyllable":"ma","wordSyllableAudioUrl":"","wordSyllableType":"2","wordRemark":"animal-a,ni,ma-ni--a,ni,ma,l/ni(i)&ma","wordType":4,"isSelect":0,"videoUrl":"1325918021869260800","isReadSelect":0,"isWriteSelect":0,"syllableList":[{"id":"1325918369350569984","wordSyllable":"ma","wordSyllableAudioUrl":"","wordSyllableType":"5","wordRemark":"animal-a,ni,ma-ni--a,ni,ma,l/ni(i)&ma","wordType":4,"isSelect":0,"videoUrl":"1325918021869260800","isReadSelect":0,"isWriteSelect":0,"syllableList":null,"splitList":null}],"splitList":null}],"splitList":[{"id":"1325918369698697216","wordSyllable":"a","wordSyllableAudioUrl":"","wordSyllableType":"4","wordRemark":"animal-a,ni,ma-ni--a,ni,ma,l/ni(i)&ma","wordType":4,"isSelect":0,"videoUrl":"1325918021869260800","isReadSelect":0,"isWriteSelect":0,"syllableList":null,"splitList":null},{"id":"1325918370055213056","wordSyllable":"ni","wordSyllableAudioUrl":"","wordSyllableType":"3","wordRemark":"animal-a,ni,ma-ni--a,ni,ma,l/ni(i)&ma","wordType":4,"isSelect":0,"videoUrl":"1325918021869260800","isReadSelect":0,"isWriteSelect":0,"syllableList":[{"id":"1325918368301993984","wordSyllable":"ni","wordSyllableAudioUrl":"","wordSyllableType":"3","wordRemark":"animal-a,ni,ma-ni--a,ni,ma,l/ni(i)&ma","wordType":4,"isSelect":0,"videoUrl":"1325918021869260800","isReadSelect":0,"isWriteSelect":0,"syllableList":[{"id":"1325918368650121216","wordSyllable":"ni(i)","wordSyllableAudioUrl":"","wordSyllableType":"9","wordRemark":"animal-a,ni,ma-ni--a,ni,ma,l/ni(i)&ma","wordType":4,"isSelect":0,"videoUrl":"1325918021869260800","isReadSelect":0,"isWriteSelect":0,"syllableList":null,"splitList":null}],"splitList":null}],"splitList":null},{"id":"1325918370403340288","wordSyllable":"ma","wordSyllableAudioUrl":"","wordSyllableType":"4","wordRemark":"animal-a,ni,ma-ni--a,ni,ma,l/ni(i)&ma","wordType":4,"isSelect":0,"videoUrl":"1325918021869260800","isReadSelect":0,"isWriteSelect":0,"syllableList":null,"splitList":null},{"id":"1325918370759856128","wordSyllable":"l","wordSyllableAudioUrl":"","wordSyllableType":"4","wordRemark":"animal-a,ni,ma-ni--a,ni,ma,l/ni(i)&ma","wordType":4,"isSelect":0,"videoUrl":"1325918021869260800","isReadSelect":0,"isWriteSelect":0,"syllableList":null,"splitList":null}]},{"id":"1325918371112177664","wordSyllable":"capital","wordSyllableAudioUrl":"","wordSyllableType":"1","wordRemark":"capital-ca,pi,ta-ca--ca,pi,ta,l/ca(a)&ta","wordType":4,"isSelect":3,"videoUrl":"1325918021869260800","isReadSelect":1,"isWriteSelect":1,"syllableList":[{"id":"1325918371456110592","wordSyllable":"ca","wordSyllableAudioUrl":"","wordSyllableType":"3","wordRemark":"capital-ca,pi,ta-ca--ca,pi,ta,l/ca(a)&ta","wordType":4,"isSelect":0,"videoUrl":"1325918021869260800","isReadSelect":0,"isWriteSelect":0,"syllableList":[{"id":"1325918371808432128","wordSyllable":"ca(a)","wordSyllableAudioUrl":"","wordSyllableType":"6","wordRemark":"capital-ca,pi,ta-ca--ca,pi,ta,l/ca(a)&ta","wordType":4,"isSelect":0,"videoUrl":"1325918021869260800","isReadSelect":0,"isWriteSelect":0,"syllableList":null,"splitList":null}],"splitList":null},{"id":"1325918372156559360","wordSyllable":"pi","wordSyllableAudioUrl":"","wordSyllableType":"2","wordRemark":"capital-ca,pi,ta-ca--ca,pi,ta,l/ca(a)&ta","wordType":4,"isSelect":0,"videoUrl":"1325918021869260800","isReadSelect":0,"isWriteSelect":0,"syllableList":[],"splitList":null},{"id":"1325918372504686592","wordSyllable":"ta","wordSyllableAudioUrl":"","wordSyllableType":"2","wordRemark":"capital-ca,pi,ta-ca--ca,pi,ta,l/ca(a)&ta","wordType":4,"isSelect":0,"videoUrl":"1325918021869260800","isReadSelect":0,"isWriteSelect":0,"syllableList":[{"id":"1325918372852813824","wordSyllable":"ta","wordSyllableAudioUrl":"","wordSyllableType":"5","wordRemark":"capital-ca,pi,ta-ca--ca,pi,ta,l/ca(a)&ta","wordType":4,"isSelect":0,"videoUrl":"1325918021869260800","isReadSelect":0,"isWriteSelect":0,"syllableList":null,"splitList":null}],"splitList":null}],"splitList":[{"id":"1325918373200941056","wordSyllable":"ca","wordSyllableAudioUrl":"","wordSyllableType":"3","wordRemark":"capital-ca,pi,ta-ca--ca,pi,ta,l/ca(a)&ta","wordType":4,"isSelect":0,"videoUrl":"1325918021869260800","isReadSelect":0,"isWriteSelect":0,"syllableList":[{"id":"1325918371456110592","wordSyllable":"ca","wordSyllableAudioUrl":"","wordSyllableType":"3","wordRemark":"capital-ca,pi,ta-ca--ca,pi,ta,l/ca(a)&ta","wordType":4,"isSelect":0,"videoUrl":"1325918021869260800","isReadSelect":0,"isWriteSelect":0,"syllableList":[{"id":"1325918371808432128","wordSyllable":"ca(a)","wordSyllableAudioUrl":"","wordSyllableType":"6","wordRemark":"capital-ca,pi,ta-ca--ca,pi,ta,l/ca(a)&ta","wordType":4,"isSelect":0,"videoUrl":"1325918021869260800","isReadSelect":0,"isWriteSelect":0,"syllableList":null,"splitList":null}],"splitList":null}],"splitList":null},{"id":"1325918373553262592","wordSyllable":"pi","wordSyllableAudioUrl":"","wordSyllableType":"4","wordRemark":"capital-ca,pi,ta-ca--ca,pi,ta,l/ca(a)&ta","wordType":4,"isSelect":0,"videoUrl":"1325918021869260800","isReadSelect":0,"isWriteSelect":0,"syllableList":null,"splitList":null},{"id":"1325918373905584128","wordSyllable":"ta","wordSyllableAudioUrl":"","wordSyllableType":"4","wordRemark":"capital-ca,pi,ta-ca--ca,pi,ta,l/ca(a)&ta","wordType":4,"isSelect":0,"videoUrl":"1325918021869260800","isReadSelect":0,"isWriteSelect":0,"syllableList":null,"splitList":null},{"id":"1325918374253711360","wordSyllable":"l","wordSyllableAudioUrl":"","wordSyllableType":"4","wordRemark":"capital-ca,pi,ta-ca--ca,pi,ta,l/ca(a)&ta","wordType":4,"isSelect":0,"videoUrl":"1325918021869260800","isReadSelect":0,"isWriteSelect":0,"syllableList":null,"splitList":null}]},{"id":"1325918374610227200","wordSyllable":"difficult","wordSyllableAudioUrl":"","wordSyllableType":"1","wordRemark":"difficult-di,ffi,cu-di--di,ffi,cu,l,t/di(i)&cu","wordType":4,"isSelect":3,"videoUrl":"1325918021869260800","isReadSelect":1,"isWriteSelect":1,"syllableList":[{"id":"1325918374966743040","wordSyllable":"di","wordSyllableAudioUrl":"","wordSyllableType":"3","wordRemark":"difficult-di,ffi,cu-di--di,ffi,cu,l,t/di(i)&cu","wordType":4,"isSelect":0,"videoUrl":"1325918021869260800","isReadSelect":0,"isWriteSelect":0,"syllableList":[{"id":"1325918375319064576","wordSyllable":"di(i)","wordSyllableAudioUrl":"","wordSyllableType":"9","wordRemark":"difficult-di,ffi,cu-di--di,ffi,cu,l,t/di(i)&cu","wordType":4,"isSelect":0,"videoUrl":"1325918021869260800","isReadSelect":0,"isWriteSelect":0,"syllableList":null,"splitList":null}],"splitList":null},{"id":"1325918375667191808","wordSyllable":"ffi","wordSyllableAudioUrl":"","wordSyllableType":"2","wordRemark":"difficult-di,ffi,cu-di--di,ffi,cu,l,t/di(i)&cu","wordType":4,"isSelect":0,"videoUrl":"1325918021869260800","isReadSelect":0,"isWriteSelect":0,"syllableList":[],"splitList":null},{"id":"1325918376015319040","wordSyllable":"cu","wordSyllableAudioUrl":"","wordSyllableType":"2","wordRemark":"difficult-di,ffi,cu-di--di,ffi,cu,l,t/di(i)&cu","wordType":4,"isSelect":0,"videoUrl":"1325918021869260800","isReadSelect":0,"isWriteSelect":0,"syllableList":[{"id":"1325918376363446272","wordSyllable":"cu","wordSyllableAudioUrl":"","wordSyllableType":"5","wordRemark":"difficult-di,ffi,cu-di--di,ffi,cu,l,t/di(i)&cu","wordType":4,"isSelect":0,"videoUrl":"1325918021869260800","isReadSelect":0,"isWriteSelect":0,"syllableList":null,"splitList":null}],"splitList":null}],"splitList":[{"id":"1325918376715767808","wordSyllable":"di","wordSyllableAudioUrl":"","wordSyllableType":"3","wordRemark":"difficult-di,ffi,cu-di--di,ffi,cu,l,t/di(i)&cu","wordType":4,"isSelect":0,"videoUrl":"1325918021869260800","isReadSelect":0,"isWriteSelect":0,"syllableList":[{"id":"1325918374966743040","wordSyllable":"di","wordSyllableAudioUrl":"","wordSyllableType":"3","wordRemark":"difficult-di,ffi,cu-di--di,ffi,cu,l,t/di(i)&cu","wordType":4,"isSelect":0,"videoUrl":"1325918021869260800","isReadSelect":0,"isWriteSelect":0,"syllableList":[{"id":"1325918375319064576","wordSyllable":"di(i)","wordSyllableAudioUrl":"","wordSyllableType":"9","wordRemark":"difficult-di,ffi,cu-di--di,ffi,cu,l,t/di(i)&cu","wordType":4,"isSelect":0,"videoUrl":"1325918021869260800","isReadSelect":0,"isWriteSelect":0,"syllableList":null,"splitList":null}],"splitList":null}],"splitList":null},{"id":"1325918377063895040","wordSyllable":"ffi","wordSyllableAudioUrl":"","wordSyllableType":"4","wordRemark":"difficult-di,ffi,cu-di--di,ffi,cu,l,t/di(i)&cu","wordType":4,"isSelect":0,"videoUrl":"1325918021869260800","isReadSelect":0,"isWriteSelect":0,"syllableList":null,"splitList":null},{"id":"1325918377412022272","wordSyllable":"cu","wordSyllableAudioUrl":"","wordSyllableType":"4","wordRemark":"difficult-di,ffi,cu-di--di,ffi,cu,l,t/di(i)&cu","wordType":4,"isSelect":0,"videoUrl":"1325918021869260800","isReadSelect":0,"isWriteSelect":0,"syllableList":null,"splitList":null},{"id":"1325918377760149504","wordSyllable":"l","wordSyllableAudioUrl":"","wordSyllableType":"4","wordRemark":"difficult-di,ffi,cu-di--di,ffi,cu,l,t/di(i)&cu","wordType":4,"isSelect":0,"videoUrl":"1325918021869260800","isReadSelect":0,"isWriteSelect":0,"syllableList":null,"splitList":null},{"id":"1325918378108276736","wordSyllable":"t","wordSyllableAudioUrl":"","wordSyllableType":"4","wordRemark":"difficult-di,ffi,cu-di--di,ffi,cu,l,t/di(i)&cu","wordType":4,"isSelect":0,"videoUrl":"1325918021869260800","isReadSelect":0,"isWriteSelect":0,"syllableList":null,"splitList":null}]}]

class ZhiMiCacheData {

  // 正则获取括号里面的字符串
  static extractContentInParentheses(input) {
    const regex = /\(([^)]+)\)/;
    const match = input.match(regex);
    if (match) {
      return match[1];
    }
    return '';
  }

  // 重音/弱音音节判断
  static correctRepeatSyllable(arr, index, syllable, syllableArr) {
    let isMatch = false;
    arr.forEach(item => {
      let onlySyllable = getOnlySyllable(item);  // 需要根据你的项目写出这个函数
      if (item.includes('[') || item.includes(']')) {
        const regex = /\[(\d+)\]/;
        const match = item.match(regex);
        if (match) {
          if (onlySyllable === syllable && getStrOrderInArr(index, syllableArr) === parseInt(match[1])) {
            isMatch = true;
          }
        }
      } else {
        if (item === syllable && index === getSmallIndexFormStringArr(syllableArr, syllable)) {
          isMatch = true;
        }
      }
    });
    return isMatch;
  }

  // 获取当前字母在数组中的位置
  static getStrOrderInArr(index, arr) {
    const findStr = arr[index];
    const findIndexArr = arr.reduce((acc, val, i) => {
      if (val === findStr) acc.push(i);
      return acc;
    }, []);
    const target = findIndexArr.indexOf(index);
    return target === -1 ? target : target + 1;
  }

  // 获取最小索引
  static getSmallIndexFormStringArr(strs, char) {
    let minIndex = -1;
    strs.forEach((item, i) => {
      if (item === char && (minIndex === -1 || i < minIndex)) {
        minIndex = i;
      }
    });
    return minIndex;
  }

  // 正则提取括号内数据
  static extractDataFromBrackets(input) {
    const regex = /\(([^)]+)\)/;
    const match = input.match(regex);
    if (match) {
      return match[1].split('|');
    }
    return [];
  }

  // 获取拼音部分 (示例方法，可根据需求更改)
  static getPartsTypeOne(syllableList) {
    let parts = [];
    syllableList.forEach(syllable => {
      let strArr = getOnlySyllable(syllable.wordSyllable).split("");
      strArr.forEach((s, j) => {
        let data = new SyllableRule(s); // SyllableRule 是你定义的类或对象
        data.parentName = syllable.wordSyllable; // word
        if (syllable.wordSyllableType === "2") {
          data.isSyllable = true;
          syllable.syllableList.forEach(e => {
            let arr = extractDataFromBrackets(e.wordSyllable);
            if (correctRepeatSyllable(arr, j, data.syllable, strArr)) {
              data.id = e.id;
              data.isUndertone = true;
              data.longIndex = j;
            }
          });
        } else if (syllable.wordSyllableType === "3") {
          data.isStress = true;
          data.isSyllable = true;
        } else if (syllable.wordSyllableType === "7") {
          data.isSecondaryStress = true;
          data.isSyllable = true;
        }
        parts.push(data);
      });
    });
    return parts;
  }

  // 获取正确的数组
  static getPartsTypeRight(syllableList) {
    let parts = [];
    syllableList.forEach(syllable => {
      let data = new SyllableRule(syllable.wordSyllable);
      if (syllable.wordSyllableType === "3") {
        data.isStress = true;
        data.isSyllable = true;
        data.isRight = 1;
      } else if (syllable.wordSyllableType === "2") {
        data.isSyllable = true;
        data.isUndertone = true;
        data.isRight = 1;
      } else if (syllable.wordSyllableType === "7") {
        data.isSecondaryStress = true;
        data.isSyllable = true;
        data.isRight = 1;
      }
      parts.push(data);
    });
    return parts;
  }

}
class ZhiMiCacheData {
  ///体验课 正式课区分
  static bool isFormal = false;
  static setIsFormal(val) {
    isFormal = (val == "true" ? true : false);
  }

  static bool getIsFormal() {
    return isFormal;
  }

  ///课程id
  static String courseId = "";
  static setCourseId(val) {
    courseId = val;
  }

  static String getCourseId() {
    return courseId;
  }

  ///视屏地址
  static AllFormalVideo allFormalVideo = AllFormalVideo();

  ///话音节规则  和 拼读拼写规则  下标
  static int curRuleIndex = 0;

  ///当前下标对应的视频id
  static String curRuleIndexVideoId = "";

  ///本次是划音节 还是 拼读拼写 流程  1音节  2拼读拼写
  static int syllableOrRead = 0;

  ///判断是否是循环中的最后一个视频
  static judgeLastVideoId(type) {
    if (type == 1) {
      return allFormalVideo.videoList.length - 1 == curRuleIndex;
    } else if (type == 2) {
      //判断拼读拼写是否是最后一个视频
      return allFormalVideo.readList.length - 1 == curRuleIndex;
    }
    return allFormalVideo.readList.length - 1 == curRuleIndex;
  }

  ///复习数据
  static late CourseReview courseReview;

  ///学生点击的数据记录
  /// 正确的元/辅音，正确的音节，正确的拼读检测词和拼写检测词
  ///  -1辅音，0元音，1音节，2 拼读检测词，3拼写检测词
  static List<DtoList> clickWordData = [];
  static List<DtoList> noWordData = [];

  static List<ZhiMiWord> yuanfuyinListData = [];

  static addClickWordData(data) {
    clickWordData.add(data);
  }

  static addAllClickWordData(dataArr) {
    clickWordData.addAll(dataArr);
  }

  static addAllNoWordData(dataArr) {
    noWordData.addAll(dataArr);
  }

  static addYuanFuYinWordList(List<ZhiMiWord> list) {
    yuanfuyinListData.addAll(list);
  }

  static clearYuanFuYinList() {
    yuanfuyinListData.clear();
  }

  static getWordIdForList(List<ZhiMiWord> list,word){
    for (int i = 0; i < list.length; i++) {
      ZhiMiWord data = list[i];
      if(data.wordSyllable == word){
        return data.id;
      }
    }
    return "";
  }

  //去重
  static removeRepeat(arr) {
    List<DtoList> uniqueList = [];
    for (int i = 0; i < arr.length; i++) {
      DtoList currentItem = arr[i];
      bool isDuplicate = false;
      for (int j = 0; j < uniqueList.length; j++) {
        DtoList existingItem = uniqueList[j];
        if (currentItem.wordSyllable == existingItem.wordSyllable &&
            currentItem.wordType == existingItem.wordSyllable) {
          isDuplicate = true;
          break;
        }
      }
      if (!isDuplicate) {
        uniqueList.add(currentItem);
      }
    }
    return uniqueList;
  }

  ///排课学习id
  static String planStudyId = "";
  ///开始学习id
  static String startStudyTime = "";
  ///获取当前时间的年月日时分秒
  static getYMDHMS(){
    DateTime now = DateTime.now();
    String formattedDate = DateFormat('yyyy-MM-dd HH:mm:ss').format(now);
    print(formattedDate);
    return formattedDate;
  }

  ///清除本次只迷学习数据
  static cleanData() {
    // startStudyTime = "";//修改获取位置
    curRuleIndexVideoId = "";
    curRuleIndex = 0;
    clickWordData = [];
    yuanfuyinListData.clear();
  }

  ///正则获取括号里面的字符串
  ///适配后台数据 给的是adc(c)这种格式，c是正确的音节
  static String extractContentInParentheses(String input) {
    // 定义一个正则表达式来匹配括号内的内容
    RegExp regExp = RegExp(r'\(([^)]+)\)');

    // 查找第一个匹配项
    Match? match = regExp.firstMatch(input);

    if (match != null) {
      // 提取并返回括号内的内容
      return match.group(1)!;
    } else {
      // 如果没有找到匹配的内容，返回一个空字符串或其他默认值
      return '';
    }
  }

  //arr 重音/弱音音节 可能为hh(h)/hh(h[2])
  //index 比较类似h[2]中的2
  //syllable 当前在比对的音节
  //syllableArr 当前这个大音节的string拆分数组
  static correctRepeatSyllable(arr,index,syllable,syllableArr){
    bool isMatch = false;
    for (int i = 0; i < arr.length; i++) {
        String onlySyllable = getOnlySyllable(arr[i]);
        if(arr[i].contains('[') || arr[i].contains(']')){
          final regex = RegExp(r'\[(\d+)\]');
          final match = regex.firstMatch(arr[i]);
          if (match != null) {
              //是syllableArr中相同两个字母的位置比较
              if(onlySyllable == syllable && getStrOrderInArr(index,syllableArr).toString() == match.group(1)!.toString()){
                isMatch = true;
              }
          }
        }else{
          if(arr[i] == syllable && index == getSmallIndexFormStringArr(syllableArr,syllable)){
            isMatch = true;
          }
        }
    }
    return isMatch;
  }

  ///获取当前字母在数字中是第几个
  static getStrOrderInArr(index,arr){
    String findStr = arr[index];
    List<int> findIndexArr = [];
    for (int i = 0; i < arr.length; i++) {
      if(arr[i] == findStr){
        findIndexArr.add(i);
      }
    }
    int target = findIndexArr.indexOf(index);
    if(target == -1){
      return target;
    }
    return target+1;
  }

  ///获取在字符数组中最前位
  static getSmallIndexFormStringArr(strs,char){
    int minIndex = -1;
    for (int i = 0; i < strs.length; i++) {
      if(strs[i] == char){
        int index = i;
        if (index != -1) {
          if (minIndex == -1 || index < minIndex) {
            minIndex = index;
          }
        }
      }
    }
    return minIndex;
  }

  //正则2.0 获取"(d|c)" 生成数组 [d,c]
  static List<String> extractDataFromBrackets(String input) {
    // 定义正则表达式来匹配括号内的内容
    RegExp regExp = RegExp(r'\(([^)]+)\)');
    Match? match = regExp.firstMatch(input);

    if (match != null) {
      // 获取括号内的内容
      String content = match.group(1)!;
      // 分割内容生成数组
      return content.split('|');
    }

    // 如果没有匹配到，返回空数组
    return [];
  }

  /// 划拼音
  static getPartsTypeOne(List<SyllableList> syllableList) {
    List<SyllableRule> parts = [];
    for (var syllable in syllableList) {
      List<String> strArr = getOnlySyllable(syllable.wordSyllable).split("");
      for (int j = 0; j < strArr.length; j++) {
        SyllableRule data = SyllableRule(strArr[j]);
        data.parentName = (syllable.wordSyllable); //wo rd
        if (syllable.wordSyllableType == "2") {
          data.isSyllable = true;
          for (int i = 0; i < syllable.syllableList.length; i++) {
            SyllableList e = syllable.syllableList[i];
            List<String> arr = extractDataFromBrackets((e.wordSyllable));
            if (correctRepeatSyllable(arr,j,data.syllable,strArr)) {//arr.contains(data.syllable) ||
              data.id = e.id;
              data.isUndertone = true;
              data.longIndex = j;
            }
          }
        } else if (syllable.wordSyllableType == "3") {
          data.isStress = true;
          data.isSyllable = true;
        } else if (syllable.wordSyllableType == "7") {
          data.isSecondaryStress = true;
          data.isSyllable = true;
        }
        parts.add(data);
      }
    }
    return parts;
  }

  /// 划重音 wo rd
  static getPartsTypeTwo(List<SyllableList> syllableList) {
    List<SyllableRule> parts = [];
    for (int i = 0; i < syllableList.length; i++) {
      SyllableRule data = SyllableRule(getOnlySyllable(syllableList[i].wordSyllable));
      if (syllableList[i].wordSyllableType == "3") {
        data.isStress = true;
        data.isSyllable = true;
      } else if (syllableList[i].wordSyllableType == "2") {
        data.isSyllable = true;
      } else if (syllableList[i].wordSyllableType == "7") { //次重音
        data.isSecondaryStress = true;
        // data.isSyllable = true;//重音时次重音不可选
      }
      parts.add(data);
    }
    return parts;
  }

  /// 弱音
  static getPartsTypeThree(List<SyllableList> syllableList) {
    List<SyllableRule> parts = [];
    for (int i = 0; i < syllableList.length; i++) {
      SyllableRule data = SyllableRule(getOnlySyllable(syllableList[i].wordSyllable));
      data.id = syllableList[i].id;
      if (syllableList[i].wordSyllableType == "3") {
        data.isStress = true;
        // data.isSyllable = true;//弱音时重音不可选
      }else if (syllableList[i].wordSyllableType == "7") { //次重音
        data.isSecondaryStress = true;
        // data.isSyllable = true;//弱音时次重音不可选
      } else if (syllableList[i].wordSyllableType == "2") {
        data.isSyllable = true;
        if(syllableList[i].syllableList != null && syllableList[i].syllableList.isNotEmpty){
          data.isUndertone = true;
          data.weakType = getWeakType(syllableList[i]);
        }
      }
      parts.add(data);
    }
    // List<SyllableRule> parts = [];
    // for (var syllable in syllableList) {
    //   List<String> strArr = getOnlySyllable(syllable.wordSyllable).split("");
    //   for (int j = 0; j < strArr.length; j++) {
    //     SyllableRule data = SyllableRule(strArr[j]);
    //     data.parentName = (syllable.wordSyllable); //wo rd
    //     if (syllable.wordSyllableType == "2") {
    //       data.isSyllable = true;
    //       for (int i = 0; i < syllable.syllableList.length; i++) {
    //         SyllableList e = syllable.syllableList[i];
    //         List<String> arr = extractDataFromBrackets((e.wordSyllable));
    //         if (correctRepeatSyllable(arr,j,data.syllable,strArr)) {//arr.contains(data.syllable) ||
    //           data.id = e.id;
    //           data.isUndertone = true;
    //           data.longIndex = j;
    //         }
    //       }
    //     } else if (syllable.wordSyllableType == "3") {
    //       data.isStress = true;
    //       data.isSyllable = true;
    //     }
    //     parts.add(data);
    //   }
    // }
    return parts;
  }

  static getWeakType(data){
    List<SyllableList> syllableList = data.syllableList;
    if(syllableList.isNotEmpty){
      SyllableList syllable = syllableList[0];
      return (syllable.wordSyllableType!=null && syllable.wordSyllableType!="")?int.parse(syllable.wordSyllableType):2;
    }
    return 2;
  }

  /// 定长音 重音的分开  其他音节连一起
  static getPartsTypeFour(List<SyllableList> syllableList) {
    List<SyllableRule> parts = [];

    for (var syllable in syllableList) {
      List<String> strArr = getOnlySyllable(syllable.wordSyllable).split("");
      for (int j = 0; j < strArr.length; j++) {
        SyllableRule data = SyllableRule(strArr[j]);
        data.parentName = (syllable.wordSyllable); //wo rd
        if (syllable.wordSyllableType == "3") {
          data.isStress = true;
          data.isSyllable = true;
          for (int i = 0; i < syllable.syllableList.length; i++) {
            SyllableList e = syllable.syllableList[i];
            List<String> arr = extractDataFromBrackets((e.wordSyllable));
            if (correctRepeatSyllable(arr,j,data.syllable,strArr)) {//arr.contains(data.syllable) ||
              data.id = e.id;
              data.isLong = true;
              data.longType = getLongType(syllable);
              data.longIndex = j;
              break;
            }
          }
        } else if (syllable.wordSyllableType == "2") {
          data.isSyllable = true;
        }
        parts.add(data);
      }
    }
    return parts;
  }
  ///获取长音类型
  static getLongType(data){
    List<SyllableList> syllableList = data.syllableList;
    if(syllableList.isNotEmpty){
      SyllableList syllable = syllableList[0];
      return (syllable.wordSyllableType!=null && syllable.wordSyllableType!="")?int.parse(syllable.wordSyllableType):6;
    }
    return 6;
  }

  ///获取正确的数组
  static getPartsTypeRight(List<SyllableList> syllableList) {
    List<SyllableRule> parts = [];
    for (int i = 0; i < syllableList.length; i++) {
      SyllableRule data = SyllableRule((syllableList[i].wordSyllable));
      if (syllableList[i].wordSyllableType == "3") {
        // 长音正确数据 getLongPartRight
        data.isStress = true;
        data.isSyllable = true;
        data.isRight = 1;
      } else if (syllableList[i].wordSyllableType == "2") {
        // 弱音正确数据 getWeakPartRight
        data.isSyllable = true;
        data.isUndertone = true;
        data.isRight = 1;
      } else if (syllableList[i].wordSyllableType == "7") {
        data.isSecondaryStress = true;
        data.isSyllable = true;
        data.isRight = 1;
      }
      parts.add(data);
    }
    return parts;
  }

  ///获取重音正确的数组
  static getLongPartAccent(List<SyllableList> syllableList) {
    List<SyllableRule> parts = [];
    for (int i = 0; i < syllableList.length; i++) {
      SyllableRule data = SyllableRule((syllableList[i].wordSyllable));
      if (syllableList[i].wordSyllableType == "3") {
        data.isStress = true;
        data.isSyllable = true;
        data.isRight = 1;
      } else if (syllableList[i].wordSyllableType == "2") {
        data.isSyllable = true;
        data.isUndertone = true;
      } else if (syllableList[i].wordSyllableType == "7") {
        data.isSecondaryStress = true;
      }

      parts.add(data);
    }
    return parts;
  }

  static getPartsTypeW(List<SyllableList> syllableList) {
    List<SyllableRule> parts = [];
    for (int i = 0; i < syllableList.length; i++) {
      SyllableRule data = SyllableRule(getOnlySyllable(syllableList[i].wordSyllable));
      if (syllableList[i].wordSyllableType == "3") {
        // 长音正确数据 getLongPartRight
        data.isStress = true;
        data.isSyllable = true;
      } else if (syllableList[i].wordSyllableType == "2") {
        // 弱音正确数据 getWeakPartRight
        data.isSyllable = true;
        data.isUndertone = true;
      }
      data.isRight = 1;
      parts.add(data);
    }
    return parts;
  }

  ///补全单词 规则 音节拆分
  static List<SyllableRule> completeWordRule(
      List<SyllableList> syllableList, String word, type) {
    List<SyllableRule> parts = [];
    if (type == 1) {
      //划拼音或弱音
      parts = getPartsTypeOne(syllableList);
    } else if (type == 2) {
      //划重音
      parts = getPartsTypeTwo(syllableList);
    } else if (type == 3) {
      //弱音
      parts = getPartsTypeThree(syllableList);
    } else if (type == 4) {
      //划长音
      parts = getPartsTypeFour(syllableList);
    } else if (type == 5) {
      //正确数据（划拼音规则）
      parts = getPartsTypeRight(syllableList);
    } else if (type == 6) {
      //正确数据 （划弱音）
      parts = getWeakPartRight(syllableList);
    } else if (type == 7) {
      //正确数据 （划长音）
      parts = getLongPartRight(syllableList);
    } else if (type == 8) {
      //正确数据 （重音）
      parts = getLongPartAccent(syllableList);
    }
    List<SyllableRule> result = [];
    int partsIndex = 0;
    for (int i = 0; i < word.length; i++) {
      String lastWord = word.substring(i,word.length);
      if(partsIndex < parts.length){
        String partsSyllable = getOnlySyllable(parts[partsIndex].syllable);
        if(lastWord.startsWith(partsSyllable)){
          result.add(parts[partsIndex]);
          partsIndex += 1;
          i += partsSyllable.length-1;
        }else{
          result.add(SyllableRule(word[i]));
        }
      }else{
        result.add(SyllableRule(word[i]));
      }
    }
    // int startIndex = 0;
    // for (int i = 0; i < parts.length; i++) {
    //   String onlyS = (getOnlySyllable(parts[i].syllable));
    //   int endIndex = startIndex + onlyS.length;
    //   if (endIndex <= word.length) {
    //     result.add(parts[i]);
    //     startIndex = endIndex;
    //   } else {
    //     result.add(SyllableRule(word.substring(startIndex)));
    //     startIndex = word.length;
    //     break;
    //   }
    // }
    // if (startIndex < word.length) {
    //   for (int i = startIndex; i < word.length; i++) {
    //     result.add(SyllableRule(word[i]));
    //   }
    // }
    return result;
  }

  ///补全单词
  static List<String> completeWord(
      List<SyllableList> syllableList, String word) {
    List<String> parts = [];
    for (int i = 0; i < syllableList.length; i++) {
      parts.add(getOnlySyllable(syllableList[i].wordSyllable));
    }
    List<String> result = [];
    // 递归函数，用于处理单词的截断部分
    void findParts(String remainingWord, int index) {
      // 如果单词被处理完毕，则返回
      if (remainingWord.isEmpty) return;
      // 检查每个部分是否是单词的前缀
      for (int i = index; i < parts.length; i++) {
        if (remainingWord.startsWith(parts[i])) {
          result.add(parts[i]);
          // 截取未匹配的部分并继续递归处理
          findParts(remainingWord.substring(parts[i].length), i + 1);
          return;
        }
      }
      // 如果没有匹配的部分，则截取单词的第一个字符并继续递归处理
      result.add(remainingWord.substring(0, 1));
      findParts(remainingWord.substring(1), 0);
    }

    // 调用递归函数开始处理单词
    findParts(word, 0);
    return result;
  }

  ///弱音 正确答案
  static List<SyllableRule> getWeakPartRight(List<SyllableList> syllableList) {
    List<SyllableRule> parts = [];
    for (int i = 0; i < syllableList.length; i++) {
      SyllableRule data = SyllableRule((syllableList[i].wordSyllable));
      data.id = syllableList[i].id;
      if (syllableList[i].wordSyllableType == "3") {
        data.isStress = true;
        // data.isSyllable = true;//弱音时重音不高亮可选
      } else  if (syllableList[i].wordSyllableType == "7") {
        data.isSecondaryStress = true;
        // data.isSyllable = true;//弱音时次重音不高亮可选
      } else if (syllableList[i].wordSyllableType == "2") {
        data.isSyllable = true;
        if(syllableList[i].syllableList != null && syllableList[i].syllableList.isNotEmpty){
          data.isUndertone = true;
          data.isRight = 1;
          data.weakType = getWeakType(syllableList[i]);
        }
      }
      parts.add(data);
    }
    return parts;
  }

  static List<SyllableRule> getLongPartRight(List<SyllableList> syllableList) {
    List<SyllableRule> parts = [];
    for (var syllable in syllableList) {
      SyllableRule data = SyllableRule((syllable.wordSyllable));
      data.id = syllable.id;
      if (syllable.wordSyllableType == "3") {
        data.isStress = true;
        data.isSyllable = true;
        data.isRight = 1;
        data.isLong = true;
        data.longType = getLongType(syllable);
      } else if (syllable.wordSyllableType == "2") {
        data.isSyllable = true;
        data.isUndertone = true;
      }
      parts.add(data);
    }
    return parts;
  }

  //去除[2]
  static getOnlySyllable(syllable){
    return syllable.replaceAll(RegExp(r'\[\d+\]'), '');
  }

  static getPlayUrlListEmpty(list) {
    if (list.length == 0) {
      return false;
    }
    for (var val in list) {
      if (val is SyllableList) {
        if (val.wordSyllableAudioUrl == "") {
          return false;
        }
      }
      if (val is ZhiMiWord) {
        if (val.wordSyllableAudioUrl == "") {
          return false;
        }
      }
      if (val is AttachCourseWordData) {
        if (val.wordAudioUrl == "") {
          return false;
        }
      }
      if (val is AttachCourseWordSplitDtoList) {
        if (val.audioUrl == "") {
          return false;
        }
      }
    }
    return true;
  }

  static getPlayUrlListEmpty1(list){
    if(list.length == 0){
      return false;
    }
    for (var val in list) {
      if(val["wordSyllableAudioUrl"] == ""){
        return false;
      }
    }
    return true;
  }

  // 6a-e定长短 9i-e定长短 10e-e定长短 11o-e定长短 12u-e定长短
  static getLongFlag(longType){
    if(longType == 9){
      return "i-e";
    } else if(longType == 10){
      return "e-e";
    } else if(longType == 11){
      return "o-e";
    } else if(longType == 12){
      return "u-e";
    }
    return "a-e";
  }

  //8 i划弱音  2弱音
  static getWeakFlag(weakType){
    if(weakType == 8){
      return "i";
    }
    return "ur";
  }
}

class SyllableRule {
  String id = "";
  String syllable = "";
  int tempIndex = -1;
  bool isSyllable = false; //是否是个音节
  bool isStress = false; //重音
  bool isUndertone = false; //弱音
  bool isLong = false; //长音 重音中寻找
  bool isChose = false; //是否选中
  int isRight = -1; //0错误  1正确
  String parentName = ""; //组索引
  int longIndex = -1; //组索引

  List indexList = []; //多选索引

  int index = -1;

  int longType = 6; //长音类型
  int weakType = 2; //弱音类型
  bool isSecondaryStress = false; //次重音

  ///每个字母所在索引  区别相同单词选择

  SyllableRule(this.syllable);
}

