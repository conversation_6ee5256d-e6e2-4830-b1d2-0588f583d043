<template>
  <view class="content">
    <!-- 自定义导航栏 -->
    <view class="custom-nav">
      <view class="custom-nav-item">
        <view>
          <!-- #ifdef MP-WEIXIN -->
          <u-icon name="arrow-left" size="46" @click="goBack"></u-icon>
          <!-- #endif -->

          <!-- #ifdef APP-PLUS -->
          <u-icon name="arrow-left" size="23" @click="goBack"></u-icon>
          <!-- #endif -->
        </view>

        <view class="nav-title">{{ userType === 'formal' ? '正式课流程' : '试课流程' }}</view>
        <view></view>
      </view>
    </view>

    <!-- <view class="tips displayflex"> -->
    <view class="tips displayflex">
      <image class="imgIcon" :src="imgIcon" mode=""></image>
      <view class="tips-item" v-if="userType === 'trial'">
        如有任何疑问，请及时与您的
        <text class="col-green">专属推荐顾问</text>
        沟通
      </view>
      <view class="tips-item" v-if="userType === 'formal'">
        下载鼎校甄选APP,您的
        <text class="col-green">专属教练</text>
        在这等您
      </view>
    </view>
    <view class="timeLine">
      <view v-for="(item, index) in currentSteps" :key="index" class="timeLine-item">
        <view class="left">
          <view class="circle" :class="item.checked ? 'checked' : 'unchecked'"></view>
          <view v-if="index !== currentSteps.length - 1" class="line"></view>
        </view>
        <view class="descWord" :class="{ 'desc-blod': item.checked }">{{ item.desc }}</view>
      </view>
    </view>
    <view class="dis_center">
      <image class="imgCode" :src="codeForm.qrCode" show-menu-by-longpress="true"></image>
      <!-- #ifdef MP-WEIXIN -->
      <view class="codeText">请长按识别二维码</view>
      <!-- #endif -->
      <!-- #ifndef MP-WEIXIN -->
      <view class="codeText">请保存下载二维码</view>
      <!-- #endif -->
    </view>
    <view class="text-btn displayflex">
      <text @click="saveCode">下载二维码</text>
    </view>
  </view>
</template>

<script>
  export default {
    data() {
      return {
        app: 0,
        imgIcon: 'https://document.dxznjy.com/course/04397719575b4f0088b4fec593b84316.png',
        rightIcon: 'https://document.dxznjy.com/course/6278d8221aec4bd29e4e05ba2a481338.png',
        codeForm: {
          qrCode: 'https://document.dxznjy.com/course/7b20a35bbdbd4afeb54e42d84e3a5cbc.png'
        },
        userType: '',
        orderStatus: 0,
        orderStatusDesc: '',
        //试课步骤
        trialSteps: [
          { desc: '购买课程成功', checked: true },
          { desc: '等待专属推荐顾问填写试课单', checked: false, status: 0 },
          { desc: '派单中', checked: false, status: 2 },
          { desc: '教练已接单，请登录鼎校APP消息查看', checked: false, status: 3 },
          { desc: '课程已结束，请联系您的专属顾问', checked: false, status: 4 }
        ],
        //正式课步骤
        formalSteps: [
          { desc: '购买正式课成功', checked: true },
          { desc: '等待专属推荐顾问填写上课信息对接表', checked: false, status: 1 },
          { desc: '派单中', checked: false, status: 2 },
          { desc: '教练已接单，请登录鼎校APP消息查看', checked: false, status: 3 }
        ]
      };
    },
    computed: {
      currentSteps() {
        return this.userType === 'formal' ? this.formalSteps : this.trialSteps;
      }
    },
    methods: {
      goBack() {
        console.log('返回11上一页');
        uni.navigateBack();
        // uni.navigateBack({
        //   delta: 1
        // });
        // uni.redirectTo({
        //   url: '/splitContent/order/order' // 跳转到订单列表首页或其他主页
        // });
      },
      saveCode() {
        let that = this;
        uni.downloadFile({
          url: that.codeForm.qrCode,
          success: (res) => {
            console.log('res', res);
            if (res.statusCode === 200) {
              // 手动加后缀
              // #ifdef MP-WEIXIN
              const filePath = res.tempFilePath;
              const newFilePath = `${wx.env.USER_DATA_PATH}/qrcode.png`; // 改成你想要的文件名和后缀
              console.log('filePath', filePath);
              console.log('newFilePath', newFilePath);
              // 使用 FileSystemManager 复制并加后缀
              const fs = wx.getFileSystemManager();
              fs.copyFile({
                srcPath: filePath,
                destPath: newFilePath,

                success: () => {
                  uni.saveImageToPhotosAlbum({
                    filePath: newFilePath,
                    success: () => {
                      uni.showToast({ title: '图片已保存', duration: 2000 });
                    },
                    fail: (err) => {
                      uni.showToast({ title: '保存失败', duration: 2000, icon: 'none' });
                    }
                  });
                },
                fail: (err) => {
                  console.error('文件重命名失败', err);
                  uni.showToast({ title: '处理失败', duration: 2000, icon: 'none' });
                }
              });
              // #endif
              // #ifndef MP-WEIXIN
              uni.saveImageToPhotosAlbum({
                filePath: res.tempFilePath,
                success: function () {
                  uni.showToast({
                    title: '图片已保存',
                    duration: 2000
                  });
                },
                fail: function () {
                  uni.showToast({
                    title: '保存失败',
                    duration: 2000,
                    icon: 'none'
                  });
                }
              });
              // #endif
            }
          },
          fail: () => {
            uni.showToast({ title: '下载失败', duration: 2000, icon: 'none' });
          }
        });
      },
      updateSteps() {
        const steps = this.userType === 'formal' ? this.formalSteps : this.trialSteps;

        // 遍历所有步骤，根据status和orderStatus的关系来设置checked状态
        steps.forEach((step) => {
          if (step.status !== undefined) {
            // 如果步骤的status小于等于当前orderStatus，则设置为checked
            step.checked = step.status <= this.orderStatus;
          }
        });
      }
    },
    onLoad(options) {
      // this.app = options?.app ?? 0;
      console.log('🚀 ~ onLoad ~ options:', options);
      if (options) {
        // 设置用户类型
        if (options.goodsType) {
          this.userType = options.goodsType == 2 ? 'trial' : 'formal';
          console.log('🚀 ~ onLoad ~ options.goodsType:', options.goodsType);
        }

        // 设置订单状态
        if (options.orderStatus) {
          this.orderStatus = parseInt(options.orderStatus);
        }
        // 设置订单描述值
        if (options.orderStatusDesc) {
          this.orderStatusDesc = options.orderStatusDesc;
        }

        // 更新步骤状态
        this.updateSteps();
      }
    },
    onUnload() {
      // #ifdef APP-PLUS
      // if (this.app) {
      // plus.runtime.quit();
      // }
      // #endif
    }
  };
</script>
<style lang="scss" scoped>
  .content {
    display: flex;
    flex-direction: column;
    align-items: center;
    min-height: 100vh;
	padding-bottom: 108rpx;
	box-sizing: border-box;
    position: relative;
    font-family: 'AlibabaPuHuiTi-3-85-Bold', 'Alibaba PuHuiTi', sans-serif;
  }

  .custom-nav {
    width: 100%;
    height: 169rpx;
    background-color: #ffffff;

    position: fixed;
    top: 0;
    left: 0;
    z-index: 999;
    box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
    padding-top: 105rpx;
    box-sizing: border-box;
  }

  .custom-nav-item {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 10rpx;
  }

  .custom-nav-item view {
    width: 33%;
  }

  .nav-title {
    font-size: 32rpx;
    // margin-top: 82rpx;
    font-weight: bold;
    color: #333333;
    text-align: center;
  }

  .tips {
    background-color: rgba(249, 173, 93, 0.15);
    height: 170rpx;
    width: 100vw;
    color: #f95e5d;
    font-size: 30rpx;
    margin-top: 88rpx;
    padding-top: 80rpx;
    box-sizing: border-box;
    /* 为自定义导航栏留出空间 */
  }

  .tips-item {
    text-align: center;
  }

  .imgIcon {
    width: 40rpx;
    height: 40rpx;
    margin-right: 15rpx;
  }

  .col-green {
    color: #3ba39d;
  }

  .imgCode {
    margin-top: 60rpx;
    height: 360rpx;
    width: 360rpx;
  }

  .timeLine {
    // background-color: purple;
    margin-top: 33rpx;
    width: 575rpx;
    // min-height: 530rpx;//时间线占比高度
  }

  .text-btn {
    height: 94.25rpx;
    width: 357.14rpx;
    background-color: #428a6f;
    border-radius: 47rpx;
    color: #ffffff;
    font-size: 36rpx;
	margin-top: 48rpx;
    // position: absolute;
    // bottom: 5vh;
    // left: 50%;
    /* 将元素的左边缘移动到父容器的中心点 */
    // transform: translateX(-50%);
    /* 将元素向左移动自身宽度的一半 */
  }

  .timeLine-item {
    display: flex;
    position: relative;
    // margin-bottom: 80rpx;
  }

  .left {
    width: 40rpx;
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    margin-right: 20rpx;
  }

  .circle {
    width: 36rpx;
    height: 36rpx;
    border-radius: 50%;
    border: 2rpx solid #979797;
  }

  .checked {
    border: 2rpx solid #3ba39d;
    background-image: url('https://document.dxznjy.com/course/6278d8221aec4bd29e4e05ba2a481338.png');
    background-repeat: no-repeat;
    background-size: cover;
  }

  .line {
    height: 65rpx;
    border: 1px dashed #979797;
    margin: 10rpx 0;
  }

  .descWord {
    font-size: 30rpx;
    color: #333333;
    line-height: 40rpx;
    flex: 1;
  }

  .desc-blod {
    font-weight: bold;
  }

  .codeText {
    text-align: center;
    font-size: 28rpx;
    margin-top: 20rpx;
    color: #222222;
  }
</style>
