<template>
  <view class="plr-30">
    <view class="plr-20 ptb-40 bg-ff radius-15 f-30">
      <!-- <image :src="imgHost+'dxSelect/share_img.png'"></image> -->
      <view class="t-c">
        <u-icon name="checkmark-circle-fill" color="#2DC032" size="145"></u-icon>
      </view>

      <view class="t-c mt-40 lh-56 c-33">
        <view>购买成功，恭喜您成为学习超人</view>
        <view>
          <text class="c-fea">长按识别二维码</text>
          加入学习超人联盟
        </view>
        <view>有效期至 {{ userinfo.expireTime || datetime }}</view>
        <image :src="codeImg" mode="widthFix" class="img_s" :show-menu-by-longpress="true"></image>
      </view>

      <!-- <view class="t-c mt-40 lh-56 c-33" v-else>
				<view>购买成功</view>
				<view><text class="c-fea">长按识别二维码</text>加入专属服务群</view>
				<image :src="codeImg" mode="widthFix" class="img_s" :show-menu-by-longpress="true"></image>
			</view> -->

      <view class="flex-s mt-50">
        <button type="default" class="suggest" @click="goJump()">{{ type == 2 ? '推荐课程' : '订单列表' }}</button>
        <button type="default" class="superman" @click.stop="goSuperman('2')">{{ type == 2 ? '发展学习超人' : '学习超人' }}</button>
      </view>
    </view>
    <view class="mt-40">
      <view class="flex-s">
        <u-line dashed length="39%" color="#C3C3C3"></u-line>
        <view class="bold f-34">热门课程</view>
        <u-line dashed length="39%" color="#C3C3C3"></u-line>
      </view>

      <view class="mt-40 pb-50">
        <view class="courseList">
          <block v-for="(item, index) in infoLists.list" :key="index">
            <view class="courseItem radius-20 pb-10 positionRelative" @tap.stop="skintap('Coursedetails/productDetils?id=' + item.courseId)">
              <view class="courseimg relative">
                <image :src="item.courseImage" class="wh100" mode="widthFix"></image>
                <view class="positionAbsolute courseTip">
                  <image :src="imgHost + 'dxSelect/tip_tyk.png'" class="wh100" mode="widthFix"></image>
                </view>
              </view>
              <view class="plr-30 mtb-20">
                <view class="bold f-28">{{ item.courseName }}</view>
                <view class="color_red font12 mtb-16 displayflex displayflexbetween">
                  <view>
                    会员价
                    <span class="bold f-34">￥{{ item.memberPrice }}</span>
                  </view>
                  <!-- <image class="productShare" :src="imgHost+'dxSelect/share_icon.png'" @click.stop="shareVip('product',item)" ></image> -->
                </view>
                <view class="displayflex color_grey f-24" style="justify-content: space-between">
                  <view>
                    原价
                    <text style="text-decoration: line-through">￥{{ item.originalPrice }}</text>
                  </view>
                  <view>{{ item.studyNumber }}+人付款</view>
                </view>
              </view>
            </view>
          </block>
          <view v-if="no_more && infoLists.list.length > 0" style="width: 100%; text-align: center">
            <u-divider text="到底了"></u-divider>
          </view>
        </view>
      </view>
    </view>

    <!-- 分享弹窗 -->
    <uni-popup ref="sharePopup" type="bottom" style="padding: 0">
      <view class="shareCard">
        <view class="reviewTitle bold">立即分享给好友</view>
        <view class="review_close" @click="closeDialog">
          <uni-icons type="clear" size="26" color="#B1B1B1"></uni-icons>
        </view>
        <view class="displayflexbetween mt-30 ptb-30" style="justify-content: space-around">
          <!-- #ifdef MP-WEIXIN -->
          <view class="t-c" @click="posterShare">
            <image :src="imgHost + 'dxSelect/share_img.png'" class="shareIcon" mode=""></image>
            <view class="f-26 mt-10 color_grey66">海报分享</view>
          </view>
          <view class="t-c">
            <button open-type="share" class="fillButton">
              <image :src="imgHost + 'dxSelect/share_lj.png'" class="shareIcon" mode=""></image>
              <view class="f-26 mt-10 color_grey66">链接分享</view>
            </button>
          </view>
          <!-- #endif -->
          <!-- #ifdef APP-PLUS -->
          <view class="t-c" @click="linkShareApp(false)">
            <button class="fillButton">
              <image :src="imgHost + 'dxSelect/weiimg.png'" class="shareIcon" mode=""></image>
              <view class="f-26 mt-10 color_grey66">微信</view>
            </button>
          </view>
          <view class="t-c" @click="linkShareApp(true)">
            <button class="fillButton">
              <image :src="imgHost + 'dxSelect/share_lj.png'" class="shareIcon" mode=""></image>
              <view class="f-26 mt-10 color_grey66">复制链接</view>
            </button>
          </view>
          <!-- #endif -->
        </view>
        <view class="bd-ee"></view>
        <!-- <view class="shareCancelBox mt-60">
					<view class="share_cancel" @click="closeDialog">取消</view>
				</view> -->
      </view>
    </uni-popup>
  </view>
</template>

<script>
  const { $navigationTo, $http } = require('@/util/methods.js');
  import Config from '@/util/config.js';
  import dayjs from 'dayjs';
  export default {
    data() {
      return {
        lineBg: 'http://mshnimg.vtui365.com/20220908104716f2e325872.png',
        listCate: [],
        cateId: '',
        infoLists: {},
        page: 1,
        no_more: false,
        imgHost: getApp().globalData.imgsomeHost,
        codeImg: '', // 二维码
        datetime: '', // 超人到期时间
        shareContent: {}, // 分享内容
        type: '',
        codeType: '',
        id: '', // 课程id
        mealLists: {}, // 商品详情
        userinfo: {},
        app: 0
      };
    },
    onUnload() {
      // #ifdef APP-PLUS
      if (this.app) {
        plus.runtime.quit();
      }
      // #endif
    },
    onLoad(e) {
      this.app = e.app;
      if (e.type != undefined) {
        this.type = e.type; //下单页类型   2学习超人  否则不管
      }
      this.id = e.id;
      this.list();
    },
    onShow() {
      this.getTime();
      this.getQrcode();
      this.meal();
      setTimeout(() => {
        this.homeData();
      }, 500);
    },
    onReachBottom() {
      if (this.page >= this.infoLists.totalPage) {
        this.no_more = true;
        return false;
      }
      this.course(true, ++this.page);
    },
    onShareAppMessage(res) {
      let userId = uni.getStorageSync('user_id');
      let url = `/pages/beingShared/index?scene=${uni.getStorageSync('user_id')}&type=${this.shareContent.type}&id=${this.shareContent.id}`;
      console.log(333333);
      console.log(url);
      setTimeout(() => {
        this.$refs.sharePopup.close();
      }, 2000);
      return {
        title: '叮，你的好友敲了你一下，赶紧过来看看',
        imageUrl: this.shareContent.imgurl,
        // imageUrl: "https://document.dxznjy.com/dxSelect/superman_share.jpg",
        path: `/pages/beingShared/index?scene=${uni.getStorageSync('user_id')}&type=${this.shareContent.type}&id=${this.shareContent.id}`
      };
    },
    methods: {
      linkShareApp(isCopy) {
        setTimeout(() => {
          this.$refs.sharePopup.close();
        }, 2000);
        let shareInfo = {
          title: '叮，你的好友敲了你一下，赶紧过来看看',
          imageUrl: this.shareContent.imgurl,
          path: `/pages/beingShared/index?scene=${uni.getStorageSync('user_id')}&type=${this.shareContent.type}&id=${this.shareContent.id}`
        };
        if (isCopy) {
          this.$copyFunc(shareInfo.path);
        } else {
          uni.$appShare(shareInfo, 2);
          plus.runtime.quit();
        }
      },
      // 获取商品详情
      async meal() {
        let _this = this;
        const res = await $http({
          url: 'zx/meal/mealList',
          data: {
            indexShow: 0,
            cityCode: '',
            page: 1,
            pageSize: 20
          }
        });
        if (res) {
          _this.mealLists = res.data.list[0];
        }
      },

      getTime() {
        let data = dayjs().add(1, 'year');
        this.datetime = dayjs(data).format('YYYY年MM月DD日');
        console.log(this.datetime);
      },

      // 获取首页信息
      async homeData() {
        debugger;
        let _this = this;
        const res = await $http({
          url: 'zx/user/userInfoNew'
        });
        if (res) {
          _this.userinfo = res.data;
        }
      },
      // 会员专享分享
      shareVip(type) {
        this.$refs.sharePopup.open();
        this.shareContent.type = type;
        // imgurl = this.imgHost+'dxSelect/sumperman_share.png';
        this.shareContent.id = this.mealLists.mealId;
        this.shareContent.imgurl = Config.supermanShareImage;
      },

      // 点击海报
      posterShare() {
        this.$refs.sharePopup.close();
        uni.navigateTo({
          url: `/splitContent/poster/index?type=${this.shareContent.type}&id=${this.shareContent.id}`
        });
      },

      ///app链接分享
      linkShare() {
        this.$refs.sharePopup.close();
        uni.share({
          provider: 'weixin',
          scene: 'WXSceneSession',
          type: 5,
          title: '叮，你的好友敲了你一下，赶紧过来看看',
          imageUrl: this.shareContent.imgurl, //分享封面
          miniProgram: {
            id: Config.miniOriginalId,
            path: '/pages/beingShared/index?scene=' + uni.getStorageSync('user_id') + '&type=' + this.shareContent.type + '&id=' + this.shareContent.id,
            type: 0,
            webUrl: Config.webUrl
          },
          success: (ret) => {
            console.log(JSON.stringify(ret));
            uni.showToast({
              icon: 'none',
              title: '分享成功'
            });
          },
          fail: (ret) => {
            console.log(JSON.stringify(ret));
            uni.showToast({
              icon: 'none',
              title: '分享失败'
            });
          }
        });
      },
      //关闭弹窗
      closeDialog() {
        this.$refs.sharePopup.close();
        this.shareContent = {};
      },
      click(item) {
        console.log(item);
        let _this = this;
        _this.page = 1;
        _this.no_more = false;
        _this.cateId = item.cateId;
        _this.course();
      },
      navigationToto(id) {
        uni.navigateTo({
          url: './courseDetail?id=' + id
        });
      },
      async list() {
        let _this = this;
        const res = await $http({
          url: 'zx/course/cateList',
          data: {}
        });
        if (res) {
          let newToDo = {
            cateId: '',
            cateName: '全部'
          };
          _this.listCate = [newToDo, ...res.data];
          _this.cateId = _this.listCate[0].cateId;
          _this.course();
        }
      },
      async course(isPage, page) {
        let _this = this;
        const res = await $http({
          url: 'zx/course/courseList',
          data: {
            indexShow: 1,
            cityCode: '',
            cateId: _this.cateId,
            page: page || 1,
            cateType: 1
          }
        });
        if (res) {
          if (isPage) {
            let old = _this.infoLists.list;
            _this.infoLists.list = [...old, ...res.data.list];
          } else {
            _this.infoLists = res.data;
          }
        }
      },
      //获取客服二维码
      async getQrcode() {
        let res = await $http({
          url: 'zx/common/getKfCodeByType',
          data: {
            type: 1
          }
        });
        // console.log(res)
        this.codeImg = res.data.codeUrl;
      },
      goJump() {
        if (this.type == 2) {
          uni.switchTab({
            url: '/pages/selection/index'
          });
        } else {
          uni.navigateTo({
            url: '/splitContent/meal/order'
          });
        }
      },

      goSuperman(type) {
        if (this.type == 2) {
          this.shareVip(type);
        } else {
          uni.navigateTo({
            url: `/supermanClub/superman/superman?type=2`
          });
        }
      },

      skintap(url) {
        $navigationTo(url);
      }
    }
  };
</script>

<style lang="scss" scoped>
  /deep/.u-icon--right {
    flex-direction: column !important;
  }
  .img_s {
    width: 374rpx;
    margin-top: 40rpx;
  }

  .suggest {
    background-color: transparent;
    color: #1d755c;
    width: 310rpx;
    border: 1px solid #1d755c;
    border-radius: 50rpx;
    text-align: center;
    padding: 17rpx 0;
    font-size: 30rpx;
    box-sizing: border-box;
  }

  .superman {
    background-image: linear-gradient(to bottom, #88cfba, #1d755c);
    color: #fff;
    width: 310rpx;
    border-radius: 50rpx;
    text-align: center;
    padding: 18rpx 0;
    font-size: 30rpx;
  }

  .courseList {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
  }

  .courseItem {
    width: 330upx;
    border-radius: 20upx;
    background-color: #fff;
    margin-bottom: 30rpx;
  }

  .courseTip {
    width: 115upx;
    bottom: 0;
    right: 0;
  }

  .courseItem_tig {
    width: 115upx;
    top: 0;
    left: 0;
  }

  .productShare {
    width: 30upx;
    height: 30upx;
  }

  /*分享弹窗样式*/
  .shareCard {
    position: relative;
    width: 100%;
    height: 100%;
    background: #ffffff;
    color: #000;
    padding-top: 50upx;
    box-sizing: border-box;
  }

  .shareIcon {
    width: 100upx;
    height: 100upx;
  }

  .shareCancelBox {
    background-color: #f3f8fc;
    width: 100%;
    height: 120upx;
    padding-top: 20upx;
    box-sizing: border-box;
  }
  .share_cancel {
    width: 100%;
    height: 100upx;
    line-height: 100upx;
    text-align: center;
    font-size: 30upx;
    background-color: #fff;
    color: #666666;
  }

  .review_close {
    position: absolute;
    top: 20rpx;
    right: 20rpx;
    z-index: 1;
  }

  .reviewTitle {
    width: 100%;
    text-align: center;
    font-size: 34upx;
    display: flex;
    justify-content: center;
  }

  .dialogContent {
    box-sizing: border-box;
    font-size: 32upx;
    line-height: 45upx;
    text-align: center;
    margin-top: 40rpx;
  }
</style>
