{
  "easycom": {
    "autoscan": true,
    "custom": {
      "^u-(.*)": "uview-ui/components/u-$1/u-$1.vue"
    }
  },
  "pages": [ //pages数组中第一项表示应用启动页，参考：https://uniapp.dcloud.io/collocation/pages
    {
      "path": "pages/index/index",
      "style": {
        "navigationBarTitleText": "",
        "enablePullDownRefresh": true,
        "navigationBarBackgroundColor": "#F3F8FC"
        // 使用小程序组件
        // "usingComponents": {
        // 	"van-button": "/wxcomponents/vant/dist/button/index"
        // }
      }
    }, {
      "path": "pages/index/cart",
      "style": {
        "navigationBarTitleText": "购物车"
      }
    }, {
      "path": "pages/index/web",
      "style": {
        "navigationBarTitleText": "政策及协议",
        "navigationStyle": "default"
      }
    }, {
      "path": "pages/home/<USER>/index",
      "style": {
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/home/<USER>/index",
      "style": {
        "navigationBarTitleText": "",
        "navigationBarBackgroundColor": "#fff"
      }
    }, {
      "path": "pages/beingShared/index",
      "style": {
        "navigationBarTitleText": "加载中...",
        "navigationBarBackgroundColor": "#fbfbfb",
        "enablePullDownRefresh": false
      }
    },
    {
      "path": "pages/selection/index",
      "style": {
        "navigationBarTitleText": "",
        "enablePullDownRefresh": false
      }
    },
    {
      "path": "pages/feedback/share_feedback",
      "style": {
        "navigationBarTitleText": "",
        "navigationStyle": "custom",
        "navigationBarBackgroundColor": "#2e896f"
      }
    }
  ],
  "globalStyle": {
    "navigationBarTextStyle": "black",
    "navigationBarTitleText": "",
    "navigationBarBackgroundColor": "#F3F8FC",
    "backgroundColor": "#f3f8fc"
    // "navigationStyle": "custom" //禁用原生导航
  },
  "tabBar": {
    "color": "#757575",
    "selectedColor": "#2E896F",
    "backgroundColor": "#ffffff",
    "borderStyle": "white",
    "spacing": "5px",
    "list": [{
        "pagePath": "pages/index/index",
        "text": "首页",
        "iconPath": "/static/tab/tab_index.png",
        "selectedIconPath": "/static/tab/tab_index_active.png"
      },
      {
        "pagePath": "pages/selection/index",
        "text": "甄选",
        "iconPath": "/static/tab/tab_meet.png",
        "selectedIconPath": "/static/tab/tab_meet_active.png"
      },
      {
        "pagePath": "pages/home/<USER>/index",
        "text": "学习",
        "iconPath": "/static/tab/tab_parent.png",
        "selectedIconPath": "/static/tab/tab_parent_active.png"
      },
      {
        "pagePath": "pages/home/<USER>/index",
        "text": "我的",
        "iconPath": "/static/tab/tab_mine.png",
        "selectedIconPath": "/static/tab/tab_mine_active.png"
      }
    ]
  },

  // 配置分包
  "subPackages": [{
      "root": "Coursedetails",
      "name": "couser",
      "plugins": {
        "polyv-player": {
          "version": "1.0.0",
          "provider": "wx4a350a258a6f7876"
        }
      },
      "pages": [{
          "path": "feedback/index",
          "style": {
            "navigationBarTitleText": "反馈",
            "enablePullDownRefresh": false,
            "navigationStyle": "custom",
            "app-plus": {
              "titleNView": false
            }
          }
        },
        {
          "path": "centerList/videoDetails",
          "style": {
            "navigationBarTitleText": "课程详情",
            "enablePullDownRefresh": false,
            "mp-weixin": {
              "usingComponents": {
                "polyv-player": "plugin://polyv-player/player"
              }
            }
          }
        },
        {
          "path": "pinYin/preschoolVideo",
          "style": {
            "navigationBarTitleText": "学前视频",
            "enablePullDownRefresh": false,
            "navigationStyle": "default",
            "mp-weixin": {
              "usingComponents": {
                "polyv-player": "plugin://polyv-player/player"
              }
            }
          }
        },
        {
          "path": "culturalType/familyCulture",
          "style": {
            "navigationBarTitleText": "家庭文化",
            "enablePullDownRefresh": false

          }
        },
        {
          "path": "culturalType/readCultural",
          "style": {
            "navigationBarTitleText": "读书文化",
            "enablePullDownRefresh": false,
            "mp-weixin": {
              "usingComponents": {
                "polyv-player": "plugin://polyv-player/player"
              }
            }
          }
        }, {
          "path": "culturalType/palyVideo",
          "style": {
            "navigationBarTitleText": "家庭文化",
            "enablePullDownRefresh": false,
            "mp-weixin": {
              "usingComponents": {
                "polyv-player": "plugin://polyv-player/player"
              }
            }
          }
        },
        {
          "path": "leave/leave",
          "style": {
            "navigationBarTitleText": "请假",
            "enablePullDownRefresh": false,
            "navigationStyle": "custom",
            "app-plus": {
              "titleNView": false
            }
          }
        },
        {
          "path": "my/myEquity",
          "style": {
            "navigationBarTitleText": "会员权益中心",
            "enablePullDownRefresh": false,
            "mp-weixin": {
              "usingComponents": {
                "polyv-player": "plugin://polyv-player/player"
              }
            }
          }
        },
        {
          "path": "my/parentEquity",
          "style": {
            "navigationBarTitleText": "会员权益中心",
            "enablePullDownRefresh": false,
            "mp-weixin": {
              "usingComponents": {
                "polyv-player": "plugin://polyv-player/player"
              }
            }
          }
        },
        {
          "path": "my/joggingCamp",
          "style": {
            "navigationBarTitleText": "陪跑营",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "my/joggingCampAct",
          "style": {
            "navigationBarTitleText": "活动",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "my/studentInfo",
          "style": {
            "navigationBarTitleText": "学员信息登记",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "Career/planning",
          "style": {
            "navigationBarTitleText": "规划报告",
            "enablePullDownRefresh": false,
            "mp-weixin": {
              "usingComponents": {
                "polyv-player": "plugin://polyv-player/player"
              }
            }
          }

        },
        {
          "path": "share/share",
          "style": {
            "navigationBarTitleText": "学习分享",
            "navigationStyle": "default",
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "share/NewShare",
          "style": {
            "navigationBarTitleText": "学习分享",
            "navigationStyle": "default",
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "share/reviewshare",
          "style": {
            "navigationBarTitleText": "复习分享",
            "navigationStyle": "default",
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black",
            "enablePullDownRefresh": false
          }

        }, {
          "path": "share/trial",
          "style": {
            "navigationBarTitleText": "学习分享",
            "enablePullDownRefresh": false
          }

        },
        {
          "path": "study/courseDetail",
          "style": {
            "navigationBarTitleText": "课程详情",
            "enablePullDownRefresh": false,
            "mp-weixin": {
              "usingComponents": {
                "polyv-player": "plugin://polyv-player/player"
              }
            }
          }
        },
        {
          "path": "study/learningMaterials",
          "style": {
            "navigationBarTitleText": "学习资料",
            "enablePullDownRefresh": false,
            "mp-weixin": {
              "usingComponents": {
                "polyv-player": "plugin://polyv-player/player"
              }
            }
          }
        },
        {
          "path": "productDetils",
          "style": {
            "navigationBarTitleText": "商品详情",
            "enablePullDownRefresh": false,
            "mp-weixin": {
              "usingComponents": {
                "polyv-player": "plugin://polyv-player/player"
              }
            }
          }
        }, {
          "path": "helpProductDetils",
          "style": {
            "navigationBarTitleText": "商品详情",
            "enablePullDownRefresh": false,
            "mp-weixin": {
              "usingComponents": {
                "polyv-player": "plugin://polyv-player/player"
              }
            }
          }
        }, {
          "path": "study/downloadProfile",
          "style": {
            "navigationBarTitleText": "学习资料",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "mealDetail",
          "style": {
            "navigationBarTitleText": "套餐详情"
          }
        },
        {
          "path": "feedback/newIndex",
          "style": {
            "navigationBarTitleText": "学习反馈",
            // "enablePullDownRefresh": false,
            "navigationStyle": "custom",
            "app-plus": {
              "titleNView": false
            }
          }
        },
        {
          "path": "tips/lessonTips",
          "style": {
            "navigationBarTitleText": "温馨提示",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "tips/webview",
          "style": {
            "navigationBarTitleText": "鼎校甄选",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "tips/trialProcess",
          "style": {
            "navigationBarTitleText": "试课流程",
            "navigationStyle": "custom"
          }
        }
      ]
    },
    {
      "root": "Personalcenter",
      "name": "personal",
      "pages": [{
          "path": "my/mystudent",
          "style": {
            "navigationBarTitleText": "我的学员",
            "navigationStyle": "default",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "my/mystudentAdd",
          "style": {
            "navigationBarTitleText": "新增学员",
            "navigationStyle": "default",
            "enablePullDownRefresh": false
          }
        }, {
          "path": "my/myCourse",
          "style": {
            "navigationBarTitleText": "我的课程",
            "enablePullDownRefresh": false
          }
        }, {
          "path": "my/course",
          "style": {
            "navigationBarTitleText": "产品中心"
          }
        }, {
          "path": "my/partnerUnion",
          "style": {
            "navigationBarTitleText": "",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "studyPrint/studyPrint",
          "style": {
            "navigationBarTitleText": "学习内容打印",
            "navigationStyle": "default",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "studyPrint/studyContentPrint",
          "style": {
            "navigationBarTitleText": "学习内容打印",
            "navigationStyle": "default",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "studyPrint/studyZip",
          "style": {
            "navigationBarTitleText": "结业单词打印",
            "navigationStyle": "default",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "index/index",
          "style": {
            "navigationBarTitleText": "课程表",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "vip/index",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "vip/customer",
          "style": {
            "navigationBarTitleText": "客户列表"
          }
        },
        {
          "path": "vip/commission",
          "style": {
            "navigationBarTitleText": "佣金记录"
          }
        },
        {
          "path": "vip/Withdrawal",
          "style": {
            "navigationBarTitleText": "提现信息",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "vip/With_List",
          "style": {
            "navigationBarTitleText": "提现明细"
          }
        },
        {
          "path": "vip/apply",
          "style": {
            "navigationBarTitleText": "成为渠道商"
          }
        },
        {
          "path": "vip/qd_apply",
          "style": {
            "navigationBarTitleText": "渠道商申请"
          }
        },
        {
          "path": "vip/wallet",
          "style": {
            "navigationBarTitleText": "提现",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "vip/cashAdvance",
          "style": {
            "navigationBarTitleText": "提现",
            "enablePullDownRefresh": false
          }
        }, {
          "path": "vip/WithDetails",
          "style": {
            "navigationBarTitleText": "提现",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "suggest/index",
          "style": {
            "navigationBarTitleText": "意见反馈"
          }
        },
        {
          "path": "my/nomyEquity",
          "style": {
            "navigationBarTitleText": "会员权益",
            "enablePullDownRefresh": false

          }
        },
        {
          "path": "my/parentVipEquity",
          "style": {
            "navigationBarTitleText": "家长会员权益",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "my/customerServiceQRcode",
          "style": {
            "navigationBarTitleText": "添加企业微信",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "my/meetingServiceQRcode",
          "style": {
            "navigationBarTitleText": "添加企业微信",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "Career/index",
          "style": {
            "navigationBarTitleText": "初始问卷调查",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "Career/enter",
          "style": {
            "navigationBarTitleText": "学业生涯规划",
            "enablePullDownRefresh": false
          }

        },
        {
          "path": "Career/news",
          "style": {
            "navigationBarTitleText": "会员信息",
            "enablePullDownRefresh": false
          }

        },
        {
          "path": "my/myIntegral",
          "style": {
            "navigationBarTitleText": "积分中心",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "my/growthReport",
          "style": {
            "navigationBarTitleText": "成长报告",
            "enablePullDownRefresh": false
          }
        }, {
          "path": "my/pronunciationList",
          "style": {
            "navigationBarTitleText": "我的发音",
            "enablePullDownRefresh": false
          }

        }, {
          "path": "my/pronunciationSettings",
          "style": {
            "navigationBarTitleText": "发音设置",
            "enablePullDownRefresh": false
          }

        },
        {
          "path": "my/wordCheck/wordCheck",
          "style": {
            // "usingComponents": {
            // 	"van-empty": "/wxcomponents/vant/dist/empty/index",
            // 	"van-divider": "/wxcomponents/vant/dist/divider/index",
            // 	"van-sticky": "/wxcomponents/vant/dist/sticky/index"
            // },
            "navigationBarTitleText": "词汇量检测"
          }
        }, {
          "path": "my/wordCheck/wordCheckReport",
          "style": {
            // "usingComponents": {
            // 	"van-empty": "/wxcomponents/vant/dist/empty/index",
            // 	"van-divider": "/wxcomponents/vant/dist/divider/index",
            // 	"van-sticky": "/wxcomponents/vant/dist/sticky/index"
            // },
            "navigationBarTitleText": "词汇量测试报告"
          }
        },
        {
          "path": "my/integralRecord",
          "style": {
            "navigationBarTitleText": "积分记录",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "my/myCollection",
          "style": {
            "navigationBarTitleText": "我的收藏",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "my/collectionManage",
          "style": {
            "navigationBarTitleText": "",
            "enablePullDownRefresh": false
          }
        }, {
          "path": "home/info",
          "style": {
            "navigationBarTitleText": "个人信息"
          }
        }, {
          "path": "home/page",
          "style": {
            "navigationBarTitleText": ""
          }
        }, {
          "path": "home/petName",
          "style": {
            "navigationBarTitleText": "设置昵称",
            "enablePullDownRefresh": false
          }
        }, {
          "path": "login/login",
          "style": {
            "navigationBarTitleText": "鼎校甄选",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "login/phoneLogin",
          "style": {
            "navigationBarTitleText": "手机号登录",
            "enablePullDownRefresh": false,
            "navigationStyle": "custom"
          }

        },
        {
          "path": "login/wxPhonePwdLogin",
          "style": {
            "navigationBarTitleText": "手机号登录",
            "enablePullDownRefresh": false,
            "navigationStyle": "custom"
          }
        }, {
          "path": "interest/orderDetail",
          "style": {
            "navigationBarTitleText": "订单",
            "navigationStyle": "default",
            // "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black"
          }
        }, {
          "path": "password/PasswordChange",
          "style": {
            "navigationBarTitleText": "修改密码",
            "navigationStyle": "default",
            // "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black"
          }
        },
        {
          "path": "my/myCourseList",
          "style": {
            "navigationBarTitleText": "我的课程"
          }
        }
      ]
    },
    // 错题本页面

    {
      "root": "errorBook",
      "name": "errorBook",
      "pages": [{
          "path": "index",
          "style": {
            "navigationBarTitleText": "错题本",
            "navigationStyle": "default",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "questionPage",
          "style": {
            "navigationBarTitleText": "错题页",
            "navigationStyle": "default",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "knowledgeReview",
          "style": {
            "navigationBarTitleText": "知识点复习",
            "navigationStyle": "default",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "topicPage",
          "style": {
            "navigationStyle": "default",
            "navigationBarTitleText": "错题",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "topicPaged",
          "style": {
            "navigationStyle": "default",
            "navigationBarTitleText": "错题",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "detailsPage",
          "style": {
            "navigationStyle": "custom",
            "enablePullDownRefresh": true
          }
        }, {
          "path": "doneCheckpoint",
          "style": {
            "navigationStyle": "default",
            "navigationBarTitleText": "",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "doneTestQuestions",
          "style": {
            "navigationStyle": "default",
            "navigationBarTitleText": "",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "mathsErrorBook",
          "style": {
            "navigationStyle": "default",
            "navigationBarTitleText": "数学错题本",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "expandImprove",
          "style": {
            "navigationStyle": "default",
            "navigationBarTitleText": "举一反三",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "correctAnswer",
          "style": {
            "navigationStyle": "default",
            "navigationBarTitleText": "举一反三",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "questionAnalysis",
          "style": {
            "navigationStyle": "custom",
            "navigationBarTitleText": "举一反三"
          }
        }
      ]
    },
    // 错题本 end

    {
      "root": "signature",
      "name": "signature",
      "pages": [{
          "path": "contract/cManagement",
          "style": {
            "navigationBarTitleText": "合同管理",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "contract/createContract",
          "style": {
            "navigationBarTitleText": "创建合同",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "contract/cResult",
          "style": {
            "navigationBarTitleText": "合同结果",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "contract/contractDetail",
          "style": {
            "navigationBarTitleText": "合同明细",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "contract/confirmOrder",
          "style": {
            "navigationBarTitleText": "确认订单",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "contract/orderRecord",
          "style": {
            "navigationBarTitleText": "订购记录",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "contract/orderDetails",
          "style": {
            "navigationBarTitleText": "订单详情",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "contract/qyCertification",
          "style": {
            "navigationBarTitleText": "企业认证",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "contract/cManageCheck",
          "style": {
            "navigationBarTitleText": "认证信息",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "contract/manageSigning",
          "style": {
            "navigationBarTitleText": "合同签署",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "contract/signingPage",
          "style": {
            "navigationBarTitleText": "合同签署",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "protocol/ProtocolList",
          "style": {
            "navigationBarTitleText": "隐私协议",
            "enablePullDownRefresh": false
          }
        }
      ]
    },
    {
      "root": "splitContent",
      "name": "splitContent",
      "pages": [{
          "path": "address/list/list",
          "style": {
            "navigationBarTitleText": "地址列表"
          }
        },
        {
          "path": "address/add/add",
          "style": {
            "navigationBarTitleText": "新增地址"
          }
        },
        {
          "path": "address/edit/edit",
          "style": {
            "navigationBarTitleText": "编辑地址"
          }
        },
        {
          "path": "authen/authen",
          "style": {
            "navigationBarTitleText": "实名认证"
          }
        },
        {
          "path": "authen/webview",
          "style": {
            "navigationBarTitleText": ""
          }
        },
        {
          "path": "authen/Privacyagree",
          "style": {
            "navigationBarTitleText": "隐私协议"
          }
        },
        {
          "path": "authen/useragree",
          "style": {
            "navigationBarTitleText": "用户服务协议"
          }
        },
        {
          "path": "channel/index",
          "style": {
            // "navigationBarTitleText": "渠道商服务"
            "navigationStyle": "custom"
          }
        },
        {
          "path": "channel/customer",
          "style": {
            "navigationBarTitleText": "客户列表"
          }
        },
        {
          "path": "channel/qdsList",
          "style": {
            "navigationBarTitleText": "渠道商列表"
          }
        },
        {
          "path": "channel/Finance",
          "style": {
            "navigationBarTitleText": "财务管理"
          }
        },
        {
          "path": "channel/qd_apply",
          "style": {
            "navigationBarTitleText": "渠道商申请"
          }
        },
        {
          "path": "meal/order",
          "style": {
            "navigationBarTitleText": "套餐订单中心"
          }
        },
        {
          "path": "meal/logistics",
          "style": {
            "navigationBarTitleText": "物流详情"
          }
        },
        {
          "path": "poster/index",
          "style": {
            "navigationBarTitleText": "推荐海报"
          }
        },
        {
          "path": "poster/congratulations",
          "style": {
            "navigationBarTitleText": "定制喜报"
          }
        },
        {
          "path": "order/order",
          "style": {
            "navigationBarTitleText": "订单列表",
            "enablePullDownRefresh": true
          }
        },
        {
          "path": "order/helpOrder",
          "style": {
            "navigationBarTitleText": "订单列表",
            "enablePullDownRefresh": true
          }
        },
        {
          "path": "order/orderCancel",
          "style": {
            "navigationBarTitleText": "订单取消原因",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "order/payCancel",
          "style": {
            "navigationBarTitleText": "支付取消原因",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "order/orderdetail",
          "style": {
            "navigationBarTitleText": "订单详情"
          }
        },
        {
          "path": "order/evaluate",
          "style": {
            "navigationBarTitleText": "发布评价",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "order/viewComments",
          "style": {
            "navigationBarTitleText": "查看评价",
            "enablePullDownRefresh": false
          }

        }, {
          "path": "order/evaluateList",
          "style": {
            "navigationBarTitleText": "评论列表",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "order/refundDetails",
          "style": {
            "navigationBarTitleText": "已申请退款详情",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "officialAccount/index",
          "style": {
            "navigationBarTitleText": "关注公众号",
            "enablePullDownRefresh": false
          }
        }, {
          "path": "officialAccount/staffCard",
          "style": {
            "navigationBarTitleText": "添加专属学管师",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "writeoff/writeoff",
          "style": {
            "navigationBarTitleText": "订单核销"
          }
        }, {
          "path": "order/refundRequest",
          "style": {
            "navigationBarTitleText": "申请退款",
            "enablePullDownRefresh": false
          }
        }, {
          "path": "test",
          "style": {
            "navigationBarTitleText": "测试"
          }
        },
        {
          "path": "order/paymentSuccess",
          "style": {
            "navigationBarTitleText": "支付成功",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "message/message",
          "style": {
            "navigationBarTitleText": "消息",
            "enablePullDownRefresh": true
          }
        },
        {
          "path": "message/MessageDetial",
          "style": {
            "navigationBarTitleText": "消息",
            "enablePullDownRefresh": false
          }
        }
      ]
    },
    {
      "root": "growth",
      "name": "growth",
      "pages": [{
          "path": "centerList/growthCenter",
          "style": {
            "navigationBarTitleText": "成长中心",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "centerList/courseDetails",
          "style": {
            "navigationBarTitleText": "课程详情",
            "enablePullDownRefresh": false

          }
        },

        {
          "path": "centerList/fileView",
          "style": {
            "navigationBarTitleText": "文件查看",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "centerList/courseAssessment",
          "style": {
            "navigationBarTitleText": "课程考核",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "centerList/viewTest",
          "style": {
            "navigationBarTitleText": "考试",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "centerList/examRecords",
          "style": {
            "navigationBarTitleText": "考试记录",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "centerList/viewTestPaper",
          "style": {
            "navigationBarTitleText": "考试记录",
            "enablePullDownRefresh": false,
            "navigationStyle": "custom"

          }
        }
      ]
    },
    {
      "root": "supermanClub",
      "name": "superman",
      "pages": [{
          "path": "list",
          "style": {
            "navigationBarTitleText": "超人俱乐部列表",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "supermanSign/sign",
          "style": {
            "navigationBarTitleText": "超人码管理",
            "enablePullDownRefresh": false
            // "app-plus": {
            // 	"titleNView": false
            // }
          }
        }, {
          "path": "supermanSign/treatHandleShipment",
          "style": {
            "navigationBarTitleText": "待处理出货单",
            "enablePullDownRefresh": false,
            "app-plus": {
              "titleNView": false
            }
          }
        }, {
          "path": "supermanSign/processing",
          "style": {
            "navigationBarTitleText": "超人码待处理",
            "enablePullDownRefresh": false,
            "app-plus": {
              "titleNView": false
            }
          }
        }, {
          "path": "supermanSign/stockprocessing",
          "style": {
            "navigationBarTitleText": "进货管理",
            "enablePullDownRefresh": false,
            "app-plus": {
              "titleNView": false
            }
          }
        }, {
          "path": "supermanSign/stockGoods",
          "style": {
            "navigationBarTitleText": "进货单",
            "enablePullDownRefresh": false
            // "app-plus": {
            // 	"titleNView": false
            // }
          }
        }, {
          "path": "supermanSign/purchaseCodesList",
          "style": {
            "navigationBarTitleText": "进货管理",
            "enablePullDownRefresh": false
            // "app-plus": {
            // 	"titleNView": false
            // }
          }
        },
        {
          "path": "supermanSign/shipment",
          "style": {
            "navigationBarTitleText": "出货",
            "enablePullDownRefresh": false,
            "navigationStyle": "custom",
            "app-plus": {
              "titleNView": false
            }
          }
        },
        {
          "path": "supermanSign/news",
          "style": {
            "navigationBarTitleText": "消息"
          }
        },
        {
          "path": "applyDetails/index",
          "style": {
            "navigationBarTitleText": "申请详情",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "clubManagement/clubUpgrade",
          "style": {
            "navigationBarTitleText": "超级俱乐部申请",
            "enablePullDownRefresh": false
          }
        }, {
          "path": "clubManagement/customerList",
          "style": {
            //2024-11-14 紧急修改
            "navigationBarTitleText": "会员",
            // "navigationBarTitleText": "下级会员",
            "enablePullDownRefresh": false
          }
        }, {
          "path": "supermanSign/tabulation",
          "style": {
            "navigationBarTitleText": "B3平推列表",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "clubApplication/index",
          "style": {
            "navigationBarTitleText": "俱乐部申请",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "clubApplication/applicationWaiting",
          "style": {
            "navigationBarTitleText": "",
            "enablePullDownRefresh": false,
            "navigationStyle": "custom"
          }
        }, {
          "path": "owner/ownerChange",
          "style": {
            "navigationBarTitleText": "上级变更通知"
          }
        }, {
          "path": "superman/superman",
          "style": {
            "navigationBarTitleText": "超人",
            "enablePullDownRefresh": false,
            "navigationStyle": "custom"
          }
        }, {
          "path": "superman/learnMore",
          "style": {
            "navigationBarTitleText": "了解更多",
            "enablePullDownRefresh": false
          }
        }
      ]
    },
    // 收款平台
    {
      "root": "Recharge",
      "name": "Recharge",
      "pages": [{
          "path": "Collection/Collection",
          "style": {
            "navigationBarTitleText": "收款",
            "enablePullDownRefresh": false

          }
        }, {
          "path": "payment/payment",
          "style": {
            "navigationBarTitleText": "付款",
            "enablePullDownRefresh": false
          }
        }, {
          "path": "Record/Record",
          "style": {
            "navigationBarTitleText": "充值记录",
            "enablePullDownRefresh": false
          }

        }, {
          "path": "index",
          "style": {
            "navigationBarTitleText": "商户收款平台",
            "enablePullDownRefresh": false,
            "navigationStyle": "custom"
          }
        }, {
          "path": "lessonDetails",
          "style": {
            "navigationBarTitleText": "学时详情",
            "enablePullDownRefresh": false,
            "navigationStyle": "custom"
          }
        }, {
          "path": "paySuccess",
          "style": {
            "navigationBarTitleText": "付款",
            "enablePullDownRefresh": false,
            "navigationStyle": "custom"
          }
        }, {
          "path": "onlineJoinTable/onlineClass",
          "style": {
            "navigationBarTitleText": "上课信息对接表",
            "enablePullDownRefresh": false,
            "navigationStyle": "default"
          }
        }, {
          "path": "onlineJoinTable/coursePlan",
          "style": {
            "navigationBarTitleText": "课程规划",
            "navigationStyle": "custom"
          }
        }, {
          "path": "onlineJoinTable/onlineJoinTable",
          "style": {
            "navigationBarTitleText": "上课信息对接表",
            "navigationStyle": "default"
          }
        }
        // ,
        // {
        // 	"path": "onlineJoinTable/friendlyReminder",
        // 	"style": {
        // 	  "navigationBarTitleText": "温馨提示",
        // 	  "enablePullDownRefresh": false
        // 	}
        // }
      ]
    },
    //拼音法抗遗忘
    {
      "root": "PYFforget",
      "name": "PYFforget",
      "pages": [{
          "path": "forgetReview",
          "style": {
            "navigationBarTitleText": "抗遗忘",
            "enablePullDownRefresh": false,
            "navigationStyle": "custom"
            // "navigationStyle": "default"

          }
        }, {
          "path": "pastReview",
          "style": {
            "navigationBarTitleText": "往期复习",
            "enablePullDownRefresh": false,
            "navigationStyle": "custom"
          }
        }, {
          "path": "todayReview",
          "style": {
            "navigationBarTitleText": "今日复习",
            "enablePullDownRefresh": false,
            "navigationStyle": "custom"
          }
        }, {
          "path": "dayLessonPreview",
          "style": {
            "navigationBarTitleText": "当日课程预览",
            "enablePullDownRefresh": false,
            "navigationStyle": "custom"
          }

        },
        {
          "path": "pastLessonPreview",
          "style": {
            "navigationBarTitleText": "往期课程预览",
            "enablePullDownRefresh": false,
            "navigationStyle": "custom"
          }
        }, {
          "path": "lessonPreview",
          "style": {
            "navigationBarTitleText": "复习课程",
            "enablePullDownRefresh": false,
            "navigationStyle": "default"
          }

        },
        // {
        //   "path": "preschoolVideo",
        //   "style": {
        //     "navigationBarTitleText": "学前视频",
        //     "enablePullDownRefresh": false,
        //     "navigationStyle": "default"
        //   }
        // }
        // , 
        {
          "path": "yfyReviewCheck",
          "style": {
            "navigationBarTitleText": "元辅音复习检测",
            "enablePullDownRefresh": false,
            "navigationStyle": "default"
          }

        },
        {
          "path": "dcReviewCheck",
          "style": {
            "navigationBarTitleText": "单词复习检测",
            "enablePullDownRefresh": false,
            "navigationStyle": "default"
          }
        }, {
          "path": "reviewReport",
          "style": {
            "navigationBarTitleText": "完成本次作业情况",
            "enablePullDownRefresh": false,
            "navigationStyle": "custom"
          }

        }
      ]
    },
    {
      "root": "parentEnd",
      "name": "parentEnd",
      "pages": [{
          "path": "vocabulary/vocabulary",
          "style": {
            "navigationBarTitleText": "词汇量报告",
            "navigationStyle": "default",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "dictation/dictation",
          "style": {
            "navigationBarTitleText": "听写能力检测",
            "navigationStyle": "default",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "dictation/dictationReport",
          "style": {
            "navigationBarTitleText": "听写检测报告",
            "navigationStyle": "default",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "recharge/lessonDetails",
          "style": {
            "navigationBarTitleText": "",
            "navigationStyle": "custom",
            "enablePullDownRefresh": false
          }
        },

        {
          "path": "report/report",
          "style": {
            "navigationBarTitleText": "",
            "navigationStyle": "custom",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "report/dictationSingleReport",
          "style": {
            "navigationBarTitleText": "",
            "navigationStyle": "custom",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "report/aiReviewReport",
          "style": {
            "navigationBarTitleText": "AI智能报告",
            "enablePullDownRefresh": true,
            "navigationStyle": "custom"
          }
        },
        {
          "path": "report/academicReport",
          "style": {
            "navigationBarTitleText": "",
            "navigationStyle": "custom",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "report/aiLessonPlanReport",
          "style": {
            "navigationBarTitleText": "AI课时规划单",
            "enablePullDownRefresh": false,
            "navigationStyle": "custom",
            "popGesture": "none"
          }
        }
      ]

    },

    {
      "root": "antiAmnesia",
      "name": "antiAmnesia",
      "pages": [{
          "path": "review/list",
          "style": {
            "onReachBottomDistance": 100,
            "navigationBarTitleText": "复习单词",
            "navigationStyle": "default",
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black"
          }

        }, {
          "path": "review/report",
          "style": {
            "navigationBarTitleText": "复习报告",
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black"
          }
        }, {
          "path": "review/history",
          "style": {
            "onReachBottomDistance": 100,
            "navigationBarTitleText": "往期复习",
            "navigationStyle": "custom"
            // "navigationBarBackgroundColor": "#FFFFFF",
            // "navigationBarTextStyle": "black"
          }
        }, {
          "path": "review/index",
          "style": {
            "navigationBarTitleText": "复习",
            "navigationStyle": "default",
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black"
          }
        },
        {
          "path": "review/funReview",
          "style": {
            "navigationStyle": "custom",
            "enablePullDownRefresh": true,
            "app-plus": {
              "titleNView": false
            }
          }
        },
        {
          "path": "review/studyFunReview",
          "style": {
            "navigationBarTitleText": "学习",
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black"
          }
        },
        {
          "path": "review/allWords",
          "style": {
            "navigationBarTitleText": "今日复习",
            "enablePullDownRefresh": false,
            "navigationStyle": "default"
          }

        }, {
          "path": "review/wordPreview",
          "style": {
            "navigationBarTitleText": "单词预览",
            "enablePullDownRefresh": false,
            "navigationBarBackgroundColor": "#f3f8fc",
            "navigationStyle": "custom",
            "navigationBarTextStyle": "black"
          }
        },
        {
          "path": "review/grammarPreview",
          "style": {
            "navigationBarTitleText": "语法预览",
            "enablePullDownRefresh": false,
            "navigationBarBackgroundColor": "#f3f8fc",
            "navigationStyle": "custom",
            "navigationBarTextStyle": "black"
          }
        },
        {
          "path": "review/grammarReview",
          "style": {
            "navigationBarTitleText": "复习语法",
            "enablePullDownRefresh": true,
            "navigationStyle": "default"
          }
        },
        {
          "path": "review/handout",
          "style": {
            "navigationStyle": "custom",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "antiForgetting/reviewReport",
          "style": {
            // "usingComponents": {
            // 	"van-popup": "/wxcomponents/vant/dist/popup/index"
            // },
            "navigationStyle": "custom"
          }
        }, {
          "path": "antiForgetting/historyReviewReport",
          "style": {
            // "usingComponents": {
            // 	"van-popup": "/wxcomponents/vant/dist/popup/index"
            // },
            "navigationStyle": "custom"
          }
        },
        {
          "path": "antiForgetting/grammarReport",
          "style": {
            // "usingComponents": {
            // 	"van-popup": "/wxcomponents/vant/dist/popup/index"
            // },
            "navigationBarTitleText": "复习报告",
            "navigationStyle": "default",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "review/aiReviewList",
          "style": {
            "navigationBarTitleText": "",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "review/aiReviewDetail",
          "style": {
            "navigationBarTitleText": "",
            "navigationStyle": "custom"
          }
        }, {
          "path": "review/aiForgetReview",
          "style": {
            "navigationBarTitleText": "",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "review/aiReviewSetting",
          "style": {
            "navigationBarTitleText": "设置",
            "navigationStyle": "custom"
          }
        }
      ]
    },
    // AI智阅
    {
      "root": "aiIntelligentWords",
      "name": "aiIntelligentWords",
      "pages": [{
          "path": "wordsSelect/wordsSelect",
          "style": {
            "navigationBarTitleText": "AI智阅",
            "navigationStyle": "default",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "wordsSelect/aiLogging",
          "style": {
            "navigationBarTitleText": "AI智阅",
            "navigationStyle": "default",
            "enablePullDownRefresh": false
          }
        }
      ]
    },

    ///趣味复习界面
    {
      "root": "interestModule",
      "name": "interestModule",
      "pages": [{
          "path": "interestingSccg",
          "style": {
            "navigationStyle": "custom",
            "enablePullDownRefresh": true,
            "app-plus": {
              "titleNView": false
            }
          }
        },
        {
          "path": "interestingTyby",
          "style": {
            "navigationStyle": "custom",
            "enablePullDownRefresh": true,
            "app-plus": {
              "titleNView": false
            }
          }
        },
        {
          "path": "interestingLlk",
          "style": {
            "navigationStyle": "custom",
            "enablePullDownRefresh": true,
            "app-plus": {
              "titleNView": false
            }
          }
        },
        {
          "path": "interestingPpl",
          "style": {
            "navigationStyle": "custom",
            "enablePullDownRefresh": true,
            "app-plus": {
              "titleNView": false
            }
          }
        },
        {
          "path": "interestHistory",
          "style": {
            "navigationStyle": "custom",
            "enablePullDownRefresh": true,
            "app-plus": {
              "titleNView": false
            }
          }
        },
        {
          "path": "round",
          "style": {
            "navigationStyle": "custom",
            "app-plus": {
              "titleNView": false
            }
          }
        },
        {
          "path": "wordIdentifying",
          "style": {
            "navigationStyle": "custom",
            "enablePullDownRefresh": true,
            "app-plus": {
              "titleNView": false
            }
          }
        },
        {
          "path": "playbill",
          "style": {
            "navigationBarTitleText": "",
            "navigationBarBackgroundColor": "#20D0BC",
            "navigationBarTextStyle": "black"
          }
        },
        {
          "path": "searchPage",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "pyfinterest/index",
          "style": {
            "navigationStyle": "custom",
            "navigationBarTitleText": "拼音法趣味复习"
          }
        },
        {
          "path": "pyfinterest/rulesBroken",
          "style": {
            "navigationBarTitleText": "",
            "navigationStyle": "custom",
            "enablePullDownRefresh": false,
            "disableScroll": true //禁止页面弹性
          }
        },
        {
          "path": "pyfinterest/interestingTysc",
          "style": {
            "navigationStyle": "custom",
            "navigationBarTitleText": "听音识词",
            "disableScroll": true //禁止页面弹性

          }
        },
        {
          "path": "pyfinterest/interestingPpl",
          "style": {
            "navigationStyle": "custom",
            "navigationBarTitleText": "拼拼乐",
            "disableScroll": true //禁止页面弹性

          }
        },
        {
          "path": "pyfinterest/interestingLlk",
          "style": {
            "navigationStyle": "custom",
            "navigationBarTitleText": "连连看",
            "disableScroll": true //禁止页面弹性
          }
        }
      ]
    },
    {
      "root": "meeting",
      "name": "meeting",
      "plugins": {
        "qiyukf": {
          "version": "1.19.5",
          "provider": "wxae5e29812005203f"
        }
      },
      "pages": [{
          "path": "meetH5",
          "style": {
            "navigationBarTitleText": "",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "meetWeb",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "meetingList",
          "style": {
            "navigationBarTitleText": "更多会议",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "meetVerifyInfo",
          "style": {
            "navigationBarTitleText": "订单详情",
            "enablePullDownRefresh": false
          }
        }, {
          "path": "myVerifyList",
          "style": {
            "navigationBarTitleText": "我的核销码",
            "enablePullDownRefresh": false
          }
        }, {
          "path": "meetIndex",
          "style": {
            "navigationStyle": "custom",
            "navigationBarTitleText": "会议中心",
            "navigationBarBackgroundColor": "#fff",
            "enablePullDownRefresh": false
          }
        }
      ]
    },
    {
      "root": "InvitationGifts",
      "name": "InvitationGifts",
      "pages": [{
          "path": "index",
          "style": {
            "navigationBarTitleText": "邀请有礼",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "invitation",
          "style": {
            "navigationBarTitleText": "加入甄选福利群",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "components/invitationPoster",
          "style": {
            "navigationBarTitleText": "邀请海报",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "components/invitationDetai",
          "style": {
            "navigationBarTitleText": "邀请记录",
            "enablePullDownRefresh": false,
            "navigationBarBackgroundColor": "#F3F8FC"
          }
        }
      ]
    },
    {
      "root": "Trialclass",
      "name": "course",
      "pages": [{
          "path": "index",
          "style": {
            "navigationBarTitleText": "试课单",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "recommend",
          "style": {
            "navigationBarTitleText": "试课推荐",
            "enablePullDownRefresh": false,
            "navigationStyle": "custom",
            "popGesture": "none"
          }
        },
        {
          "path": "shopnews",
          "style": {
            "navigationBarTitleText": "试课详情",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "result",
          "style": {
            "navigationBarTitleText": "试课报告",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "trialreport",
          "style": {
            "navigationBarTitleText": "试课报告",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "collectionProcess",
          "style": {
            "navigationBarTitleText": "红包领取流程",
            "enablePullDownRefresh": false
          }
        }
      ]
    }, {
      "root": "shoppingMall",
      "name": "shoppingMall",
      "pages": [{
          "path": "index",
          "style": {
            "navigationBarTitleText": "鼎币商城",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "details",
          "style": {
            "navigationBarTitleText": "商品详情",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "exchangeRecord",
          "style": {
            "navigationBarTitleText": "兑换记录",
            "enablePullDownRefresh": false
          }
        }
      ]
    }, {
      "root": "aptitudeTesting",
      "name": "aptitudeTesting",
      "pages": [{
          "path": "index",
          "style": {
            "navigationBarTitleText": "测评报告",
            "enablePullDownRefresh": false,
            "navigationBarBackgroundColor": "#F3F8FC"
          }
        },
        {
          "path": "startTest",
          "style": {
            "navigationBarTitleText": "天赋测评",
            "enablePullDownRefresh": false,
            "navigationBarBackgroundColor": "#F3F8FC"
          }
        }
      ]
    }, {
      "root": "memberCenter",
      "name": "memberCenter",
      "pages": [{
          "path": "index",
          "style": {
            "navigationBarTitleText": "会员文化中心",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "rankingList",
          "style": {
            "navigationBarTitleText": "排行榜",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "medalIndex",
          "style": {
            "navigationBarTitleText": "勋章",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "releaseIndex",
          "style": {
            "navigationBarTitleText": "发布瞬间",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "addProblem",
          "style": {
            "navigationBarTitleText": "去提问",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "homepage",
          "style": {
            "navigationBarTitleText": "个人主页",
            "enablePullDownRefresh": false
          }
        },

        {
          "path": "culturalType/labourCulture",
          "style": {
            "navigationBarTitleText": "劳动文化",
            "enablePullDownRefresh": false
          }
        },

        {
          "path": "culturalType/sportsCulture",
          "style": {
            "navigationBarTitleText": "运动文化",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "culturalType/familyPage/teacherIntroduction",
          "style": {
            "navigationBarTitleText": "家庭文化",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "culturalType/familyPage/addQuestions",
          "style": {
            "navigationBarTitleText": "",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "detail/problemDetail",
          "style": {
            "navigationBarTitleText": "问答详情",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "detail/rankingRecord",
          "style": {
            "navigationBarTitleText": "领取记录",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "detail/releaseDetail",
          "style": {
            "navigationBarTitleText": "评论详情",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "detail/growUpRecord",
          "style": {
            "navigationBarTitleText": "成长值获取明细",
            "enablePullDownRefresh": false
          }
        }
      ]
      // {
      // 	"path" : "detail/expertProblem",
      // 	"style" : 
      // 	{
      // 		"navigationBarTitleText" : "",
      // 		"enablePullDownRefresh" : false
      // 	}
      // }
    },
    {
      "root": "incomeDetails",
      "name": "incomeDetails",
      "pages": [{
        "path": "index",
        "style": {
          "navigationBarTitleText": "收益明细",
          "enablePullDownRefresh": false,
          "navigationBarBackgroundColor": "#F3F8FC"
        }
      }, {
        "path": "testContent",
        "style": {
          "navigationBarTitleText": "鼎校甄选"
        }
      }]
    },
    //会员升级合伙人升俱乐部
    {
      "root": "partnerApplication",
      "name": "partnerApplication",
      "pages": [{
          "path": "index",
          "style": {
            "navigationBarTitleText": "超级合伙人申请",
            "enablePullDownRefresh": false,
            "navigationBarBackgroundColor": "#F3F8FC"
          }
        },
        {
          "path": "partnerDetails",
          "style": {
            "navigationBarTitleText": "收益明细",
            "enablePullDownRefresh": false,
            "navigationBarBackgroundColor": "#F3F8FC"
          }
        },
        {
          "path": "levelPartners",
          "style": {
            "navigationBarTitleText": "下级合伙人",
            "enablePullDownRefresh": false,
            "navigationBarBackgroundColor": "#F3F8FC"
          }
        },
        {
          "path": "levelMember",
          "style": {
            //2024-11-14 紧急修改
            "navigationBarTitleText": "会员",
            // "navigationBarTitleText": "下级会员",
            "enablePullDownRefresh": false,
            "navigationBarBackgroundColor": "#F3F8FC"
          }
        },
        {
          "path": "students",
          "style": {
            "navigationBarTitleText": "学员认领",
            "enablePullDownRefresh": false,
            "navigationBarBackgroundColor": "#F3F8FC"
          }
        },
        {
          "path": "auditDetail",
          "style": {
            "navigationBarTitleText": "申诉信息",
            "enablePullDownRefresh": false,
            "navigationBarBackgroundColor": "#F3F8FC"
          }
        },
        {
          "path": "auditResult",
          "style": {
            "navigationBarTitleText": "申诉结果",
            "enablePullDownRefresh": false,
            "navigationBarBackgroundColor": "#F3F8FC"
          }
        },
        {
          "path": "recomBrand",
          "style": {
            "navigationBarTitleText": "已推品牌",
            "enablePullDownRefresh": false,
            "navigationBarBackgroundColor": "#F3F8FC"
          }
        },
        {
          "path": "recomPartners",
          "style": {
            "navigationBarTitleText": "已推合伙人",
            "enablePullDownRefresh": false,
            "navigationBarBackgroundColor": "#F3F8FC"
          }
        },
        {
          "path": "recomClub",
          "style": {
            "navigationBarTitleText": "已推俱乐部",
            "enablePullDownRefresh": false,
            "navigationBarBackgroundColor": "#F3F8FC"
          }
        },
        {
          "path": "myProcurement",
          "style": {
            "navigationBarTitleText": "我的采购",
            "enablePullDownRefresh": false,
            "navigationBarBackgroundColor": "#F3F8FC"
          }
        },
        {
          "path": "codeProcurement",
          "style": {
            "navigationBarTitleText": "邀请码采购",
            "enablePullDownRefresh": false,
            "navigationBarBackgroundColor": "#F3F8FC"
          }
        },
        {
          "path": "anticipatedIncome",
          "style": {
            "navigationBarTitleText": "课时预估收益",
            "enablePullDownRefresh": false,
            "navigationBarBackgroundColor": "#F3F8FC"
          }
        }
      ]
    },
    //俱乐部升品牌
    {
      "root": "clubApplication",
      "name": "clubApplication",
      "pages": [{
          "path": "addSuperbrands",
          "style": {
            "navigationBarTitleText": "超级品牌申请",
            "enablePullDownRefresh": false,
            "navigationBarBackgroundColor": "#F3F8FC"
          }
        },
        {
          "path": "partnerList",
          "style": {
            "navigationBarTitleText": "合伙人列表",
            "enablePullDownRefresh": false,
            "navigationBarBackgroundColor": "#F3F8FC"
          }
        },
        {
          "path": "clubList",
          "style": {
            "navigationBarTitleText": "俱乐部列表",
            "enablePullDownRefresh": false,
            "navigationBarBackgroundColor": "#F3F8FC"
          }
        },
        {
          "path": "addPartner",
          "style": {
            "navigationBarTitleText": "",
            "enablePullDownRefresh": false,
            "navigationBarBackgroundColor": "#F3F8FC"
          }
        },
        {
          "path": "levelProcurement",
          "style": {
            //2024-11-14 紧急修改
            "navigationBarTitleText": "采购单",
            // "navigationBarTitleText": "下级采购单",
            "enablePullDownRefresh": false,
            "navigationBarBackgroundColor": "#F3F8FC"
          }
        },
        {
          "path": "buyProcurement",
          "style": {
            "navigationBarTitleText": "采购邀请码",
            "enablePullDownRefresh": false,
            "navigationBarBackgroundColor": "#F3F8FC"
          }
        }
      ]
    },
    {
      "root": "coupons",
      "name": "coupons",
      "pages": [{
        "path": "CouponsList",
        "style": {
          "navigationBarTitleText": "优惠券列表",
          "enablePullDownRefresh": false,
          "onReachBottomDistance": 100,
          "navigationBarBackgroundColor": "#F3F8FC"
        }
      }]
    },
    {
      "root": "ReadForget",
      "name": "ReadForget",
      "pages": [{
          "path": "index",
          "style": {
            "navigationBarTitleText": "新阅读理解抗遗忘",
            "enablePullDownRefresh": false
            // "navigationBarBackgroundColor": "#F3F8FC"
          }
        },
        {
          "path": "todayForget",
          "style": {
            "navigationBarTitleText": "今日复习",
            "enablePullDownRefresh": false
            // "navigationBarBackgroundColor": "#F3F8FC"
          }
        },
        {
          "path": "pastForget",
          "style": {
            "navigationBarTitleText": "往期复习",
            "enablePullDownRefresh": false
            // "navigationBarBackgroundColor": "#F3F8FC"
          }
        },
        {
          "path": "forgetReview",
          "style": {
            "navigationBarTitleText": "",
            "enablePullDownRefresh": false,
            "navigationStyle": "custom"
            // "navigationBarBackgroundColor": "#F3F8FC"
          }
        },
        {
          "path": "ReviewCheckPoint",
          "style": {
            "navigationBarTitleText": "复习关卡",
            "enablePullDownRefresh": false
          }
        }, {
          "path": "ReviewBook",
          "style": {
            "navigationBarTitleText": "",
            "enablePullDownRefresh": false,
            "navigationStyle": "custom"
          }
        },
        {
          "path": "readReport",
          "style": {
            "navigationBarTitleText": "",
            "enablePullDownRefresh": false,
            "navigationStyle": "custom"
          }
        }


      ]
    },
    {
      "root": "Listen",
      "name": "Listen",
      "pages": [{
          "path": "index",
          "style": {
            "navigationBarTitleText": "全能听力",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "originalTranslation",
          "style": {
            "navigationBarTitleText": ""
          }
        },
        {
          "path": "previewListen",
          "style": {
            "navigationBarTitleText": "听力预览"
          }
        },
        {
          "path": "answer",
          "style": {
            "navigationBarTitleText": ""
          }
        },
        {
          "path": "listenReport",
          "style": {
            "navigationBarTitleText": "查看报告"
          }
        },
        {
          "path": "answerAnalysis",
          "style": {
            "navigationBarTitleText": "答案解析"
          }
        },
        {
          "path": "reveiewAnswer",
          "style": {
            "navigationBarTitleText": ""
          }
        }
      ]
    },
    // 知识图谱 start
    {
      "root": "knowledgeGraph",
      "name": "knowledgeGraph",
      "pages": [{
          "path": "index",
          "style": {
            "navigationBarTitleText": "数学知识图谱",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "knowgraph",
          "style": {
            "navigationBarTitleText": "数学知识图谱",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "testEnter",
          "style": {
            "navigationBarTitleText": "数学测试卷"
          }
        },
        {
          "path": "knowVideoList",
          "style": {
            "navigationBarTitleText": "知识点视频",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "knowVideoPlay",
          "style": {
            "navigationBarTitleText": "视频播放页",
            "enablePullDownRefresh": false,
            "mp-weixin": {
              "usingComponents": {
                "polyv-player": "plugin://polyv-player/player"
              }
            }
          }
        }
      ]
    },
    // 知识图谱 end
    {
      "root": "activity",
      "name": "activity",
      "pages": [{
          "path": "index",
          "style": {
            "navigationBarTitleText": "活动",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "invite",
          "style": {
            "navigationBarTitleText": "邀请列表",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "salesSection/index",
          "style": {
            "navigationBarTitleText": "活动",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "salesSection/rankList",
          "style": {
            "navigationBarTitleText": "排行榜",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "groupBuying/index",
          "style": {
            "navigationBarTitleText": "拼团",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "groupBuying/detail",
          "style": {
            "navigationBarTitleText": "活动详情",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "groupBuying/myGroupBuying",
          "style": {
            "navigationBarTitleText": "我的拼团",
            "enablePullDownRefresh": false,
            "navigationBarBackgroundColor": "#f9fcff"
          }
        },
        {
          "path": "groupBuying/groupBuyingFail",
          "style": {
            "navigationBarTitleText": "拼团失败",
            "enablePullDownRefresh": false
          }
        }
      ]
    },
    {
      "root": "middlePage",
      "name": "middlePage",
      "pages": [{
          "path": "callPhone",
          "style": {
            "navigationBarTitleText": "",
            "enablePullDownRefresh": false,
            "navigationBarBackgroundColor": "#FFF"
          }
        },
        {
          "path": "callHistory",
          "style": {
            "navigationBarTitleText": "最近通话",
            "navigationBarBackgroundColor": "#FFF",
            "enablePullDownRefresh": false,
            "onReachBottomDistance": 100
          }
        }
      ]
    },
    // 课程规划单
    {
      "root": "lessonPlan",
      "name": "lessonPlan",
      "pages": [{
          "path": "index",
          "style": {
            "navigationBarTitleText": "课程规划单",
            "navigationBarBackgroundColor": "#FFF",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "createPlan",
          "style": {
            "navigationBarTitleText": "课程规划单",
            "navigationBarBackgroundColor": "#FFF",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "planReportList",
          "style": {
            "navigationBarTitleText": "AI课程规划单报告",
            "navigationBarBackgroundColor": "#FFF",
            "enablePullDownRefresh": false,
            "popGesture": "none"
          }
        }
      ]
    }
  ],

  "sitemapLocation": "sitemap.json",

  "optimization": {
    "subPackages": true,
    "nodeModules": true, // 允许分包处理 node_modules
    "components": true // 按需处理组件
  },
  "lazyCodeLoading": "required", // 开启代码按需加载
  "condition": { //模式配置，仅开发期间生效
    "current": 0, //当前激活的模式(list 的索引项)
    "list": [{
      "name": "", //模式名称
      "path": "", //启动页面，必选
      "query": "" //启动参数，在页面的onLoad函数里面得到
    }]
  }
}