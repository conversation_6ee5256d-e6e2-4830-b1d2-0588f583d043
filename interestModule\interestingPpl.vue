<template>
  <view style="overflow-x: hidden; width: 100%; height: 100%">
    <view class="funContent">
      <interesting-head :title="titleText" @backPage="backPage" :hasTitleBg="true" :closeWhite="false" :hasRight="false"></interesting-head>

      <!-- 趣味复习标题 -->
       <template v-if="showListData[qIdIndex]">
         <view class="interesting_title" @click="playWord(showListData[qIdIndex].word)">
           <text class="interesting_text">{{ showListData[qIdIndex].translation.split('；')[0] }}</text>
           <image class="title_icon"></image>
           <!-- 选项展示 -->
           <view class="answerShowList">
             <view class="answerMask" :class="isRight" v-for="(item, index) in rightList">
               {{ showChooseAnswer[index] == undefined ? '' : setSubstr(showChooseAnswer[index]) }}
               <image v-if="index == rightList.length - 1" :class="isRightIcon"></image>
             </view>
           </view>
         </view>
       </template>

      <!-- 答案列表 -->
      <view class="answerListBox">
        <view class="answerListParent" v-for="(item, index) in showList" @click="chooseAnswer(item, index)">
          <view>
            <view class="answerList">
              <text class="answerListText">{{ setSubstr(item) }}</text>
            </view>
            <view class="answerListDetail">
              <text class="answerListDetailText">{{ setDetail(item) }}</text>
            </view>
          </view>
        </view>
      </view>

      <view class="userProgressbg">
        <view class="userProgress">
          <cmd-progress
            :percent="((downTimeCount - showDownTime) * 100) / downTimeCount"
            :showInfo="false"
            status="normal"
            :stroke-width="20"
            stroke-color="#4EB2FA"
          ></cmd-progress>
        </view>
        <view class="interesting_countdown">
          <text style="">{{ showDownTime == undefined || showDownTime < 0 ? 0 : showDownTime }}s</text>
        </view>
      </view>

      <!--结束弹窗 -->
      <uni-popup ref="popopPower" type="center" :maskClick="true" :classBG="''">
        <interesting-dialog
          :gradScreenWheel="3"
          :pageUrl="'interestingPpl'"
          :showData="showData"
          :play="play"
          :scheduleCode="showData.scheduleCode"
          @closeDialog="closeDialog1"
        ></interesting-dialog>
      </uni-popup>

      <!-- 引导 -->
      <uni-popup ref="guideOne" type="bottom" :maskClick="true" :classBG="''">
        <view style="position: relative">
          <image
            :style="{ height: screenHeight + 'rpx', width: screenWidth + 'rpx' }"
            src="https://document.dxznjy.com/applet/newInteresting/interesting_guide_ppl_one.png"
          ></image>
          <image class="guide_btn_next" @click="guideNext()" src="https://document.dxznjy.com/applet/newInteresting/interesting_guide_next.png"></image>
        </view>
      </uni-popup>
      <uni-popup ref="guideTwo" type="bottom" :maskClick="true" :classBG="''">
        <view style="position: relative">
          <image
            :style="{ height: screenHeight + 'rpx', width: screenWidth + 'rpx' }"
            src="https://document.dxznjy.com/applet/newInteresting/interesting_guide_ppl_two.png"
          ></image>
          <image class="guide_btn_close" @click="guideClose()" src="https://document.dxznjy.com/applet/newInteresting/interesting_guide_end.png"></image>
        </view>
      </uni-popup>
    </view>
  </view>
</template>

<script>
  import interestingHead from './components/interesting-head/interestingHead.vue';
  import interestingDialog from './components/interesting-dialog/review.vue';
  import cmdProgress from './components/cmd-progress/cmd-progress.vue';

  export default {
    components: {
      interestingHead,
      interestingDialog,
      cmdProgress
    },
    data() {
      return {
        imgHost: getApp().globalData.imguseHost,
        titleText: '拼拼乐',
        showListData: [],
        waitGroup: 0,
        nowGroup: 0,
        nowLevel: 0,
        distrubList: [], //干扰项列表数据
        qIdIndex: 0, //当前题目编号
        successList: [], //正确列表数据存储
        errorList: [], //错误列表数据存储
        correctRate: 0, //正确率
        play: '4',
        gradScreenWheel: 1,
        showList: [], //展示数据
        showData: {}, //当前课程所有数据
        rightList: [], //正确答案列表
        showChooseAnswer: [], //选择的答案
        wordInfo: {
          roundId: null,
          scheduleCode: null,
          courseCode: null,
          nowGroup: null,
          wordCount: null,
          studentWordId: null,
          play: '4'
        },

        downTimeCount: getApp().globalData.interestDownTime,
        showDownTime: this.downTimeCount,
        isRight: '', //当前题目是否正确
        countdown: '',
        isEnd: false,

        isGuide: 0, //0未显示过引导-下一步 1知道了-已完成引导 2已完成引导
        screenHeight: 0,
        screenWidth: 0,
        isRightIcon: '',
        timbre: 'W', // 音色默认女声 M  W
        pronunciationType: 0, // 1英式  0美式  默认美式
        playType: 2, // 版本
        linkUrl: ''
      };
    },
    onLoad(options) {
      this.getHeight();
      this.showData = JSON.parse(decodeURIComponent(options.params));
      this.getWordversion();
      this.isGuide = uni.getStorageSync('pplGuide');
      if(!this.isGuide){
         this.isGuide = 0;
      }
      if (this.isGuide == 0) {
        setTimeout(() => {
          this.$refs.guideOne.open();
        }, 100);
      }
      if (this.isGuide == 1) {
        setTimeout(() => {
          this.$refs.guideTwo.open();
        }, 100);
      }
      if(this.isGuide == 2){
        this.initialFunction();
      }
    },
    watch: {
      isRight(val) {
        console.log(val);
      }
    },
    methods: {
      judgeSame(arr) {
        let duplicateIndices = [];
        let seen = {};
        arr.forEach((element, index) => {
          if (seen[element]) {
            duplicateIndices.push(index);
          } else {
            seen[element] = true;
          }
        });
        if (duplicateIndices.length > 0) {
          return true;
        } else {
          return false;
        }
      },
      sameWordOption(word) {
        if (this.judgeSame(this.rightList)) {
          this.rightList = this.$util.wordRandoms(word);
        } else {
          return;
        }
        this.sameWordOption(word);
      },
      //截取后
      setSubstr(item) {
        let bb = '';
        if (item.length > 4) {
          bb = item.substring(0, 4);
        } else {
          bb = item;
        }
        return bb;
      },
      //全词
      setDetail(item) {
        let bb = '';
        if (item.length <= 4) {
          bb = '';
        } else {
          bb = '全词：' + item;
        }
        return bb;
      },
      getHeight() {
        // 获取系统信息
        const systemInfo = uni.getSystemInfoSync();

        // 获取屏幕高度
        this.screenHeight = systemInfo.windowHeight * 2;
        this.screenWidth = systemInfo.windowWidth * 2;
        // 打印屏幕高度
        console.log(this.screenHeight);
      },
      //初始化数据
      initialFunction() {
        let that = this;
        that.nowGroup = that.showData.levelGroup;
        that.nowLevel = that.showData.nowLevel;
        that.waitGroup = that.showData.levelTotalGroup - that.showData.levelGroup;
        that.wordInfo.nowGroup = that.showData.nowGroup;
        that.wordInfo.wordCount = that.showData.wordCount;
        that.wordInfo.nowLevel = that.showData.nowLevel;
        that.wordInfo.roundId = that.showData.roundId;

        that.getNoLevelData();

        //判断除当前组之外其它组是否还有单词
        // if (that.showData.cos.play1.length == 0 && that.showData.cos.play2.length == 0 && that.showData.cos.play3.length ==
        // 	0) {
        // 	that.otherWord = false;
        // } else {
        // 	that.otherWord = true;
        // 	that.gradScreenWheel = 4;
        // }
        // if (!that.otherWord) {
        // 	// 判断这组结束是组还是轮还是关
        // 	if (!that.showData.status) {
        // 		that.gradScreenWheel = 1;
        // 	}
        // 	if (that.showData.status && that.showData.nowLevel != that.showData.totalLevel) {
        // 		that.gradScreenWheel = 2;
        // 	} //当前关数==总关数 代表是最后一关，所以是一轮结束(其它组都为空的时候才可以开启下一轮)
        // 	if (that.showData.status && that.showData.nowLevel == that.showData.totalLevel) {
        // 		that.gradScreenWheel = 3;
        // 	}
        // }
      },
      // 获取当前学员设置的语音版本
      getWordversion() {
        var that = this;
        that.$httpUser
          .get('znyy/course/info', {
            studentCode: that.showData.studentCode
          })
          .then((res) => {
            if (res.data.success) {
              console.log(res.data.data);
              let name = res.data.data.voiceModel.split('#');
              that.pronunciationType = name[1];
              that.timbre = name[2];
              that.playType = name[0];
            } else {
              that.$util.alter(res.data.message);
            }
          });
      },

      getNoLevelData() {
        let that = this;
        that.$httpUser.get('znyy/course/query/fun/words/byType?scheduleCode=' + that.showData.scheduleCode + '&type=4').then((res) => {
          if (res.data.success) {
            if (res.data.data && res.data.data.length != 0) {
              that.showListData = res.data.data;
              that.qIdIndex = 0;
              that.chooseAnswerIndex = -1;

              that.wordInfo.studentWordId = that.showListData[that.qIdIndex].id;
              that.rightList = that.$util.wordRandoms(that.showListData[that.qIdIndex].word);
              that.sameWordOption(that.showListData[that.qIdIndex].word);
              do {
                let aa = that.$util.randomWordStr();
                if (that.showList.indexOf(aa) == -1 && that.rightList.indexOf(aa) == -1 && aa != undefined) {
                  that.showList.push(aa);
                }
              } while (that.showList.length < 6 - that.rightList.length);

              var newArr = JSON.parse(JSON.stringify(that.rightList));
              that.showList = that.showList.concat(newArr).sort();

              that.wordExecute();
            } else {
              that.endDialog();
              return;
            }
          } else {
            uni.navigateBack({ delta: 1 });
            // uni.navigateTo({
            // uni.redirectTo({
            // 	url: '/antiAmnesia/review/funReview?scheduleCode=' + that.showData.scheduleCode
            // })
            return;
          }
        });
      },

      //播放音频
      playWord(word) {
        this.$playVoiceApp(word, false, this.timbre, this.pronunciationType, 1, this.showData.studentCode);
      },

      //选择答案
      chooseAnswer(item, index) {
        if (this.isEnd) {
          this.$util.alter('当前玩法已结束');
          return;
        }
        let hasTet = this.showChooseAnswer.indexOf(item);
        if (hasTet != -1) {
          this.showChooseAnswer.splice(hasTet, 1);
          return;
        }
        this.showChooseAnswer.push(item);
        if (this.showChooseAnswer.length == this.rightList.length) {
          this.doItRight();
        }
      },
      //答对一个单词
      doItRight() {
        for (var i = 0; i < this.showChooseAnswer.length; i++) {
          if (this.showChooseAnswer[i] != this.rightList[i]) {
            this.markWrongWord(false);
            // this.showChooseAnswer = []; //错误时也显示 倒计时结束清空
            return;
          }
        }
        this.newMarkRightWord();
        // this.nextQuestion();
      },
      // 下一题
      nextQuestion() {
        let that = this;
        clearInterval(that.countdown);
        if (that.qIdIndex < that.showListData.length - 1) {
          setTimeout(function () {
            that.qIdIndex++;
            that.isRight = '';
            that.isRightIcon = '';
            that.wordExecute();
            that.$forceUpdate();
            console.log(that.isRight, that.isRightIcon);
          }, 500);
        } else {
          //提交趣味复习单词
          setTimeout(function () {
            that.getNoLevelData();
          }, 600);
        }
      },

      endDialog() {
        let that = this;
        if (that.successList.length + that.errorList.length == 0) {
          that.correctRate = 0;
        } else if (that.successList.length != 0 && that.errorList.length == 0) {
          that.correctRate = 100;
        } else {
          that.correctRate = parseInt((that.successList.length * 100) / (that.successList.length + that.errorList.length));
          if (isNaN(that.correctRate)) {
            that.correctRate = 0;
          }
        }

        that.isEnd = true;

        that.$refs.popopPower.open();
      },

      //题目执行
      wordExecute() {
        let that = this;
        that.$playVoiceApp(that.showListData[that.qIdIndex].word, false, that.timbre, that.pronunciationType, that.playType, that.showData.studentCode);
        that.downFun();
        that.showDownTime = that.downTimeCount;
        that.showChooseAnswer = [];
        that.showList = [];
        that.isRight = '';
        that.isRightIcon = '';
        that.$forceUpdate();
        console.log(2222222222);
        that.rightList = that.$util.wordRandoms(that.showListData[that.qIdIndex].word);
        that.sameWordOption(that.showListData[that.qIdIndex].word);
        do {
          let aa = that.$util.randomWordStr();
          if (that.showList.indexOf(aa) == -1 && that.rightList.indexOf(aa) == -1 && aa != undefined) {
            that.showList.push(aa);
          }
        } while (that.showList.length < 6 - that.rightList.length);
        var newArr = JSON.parse(JSON.stringify(that.rightList));
        that.showList = that.showList.concat(newArr).sort();
      },

      // 倒计时执行事件
      downFun() {
        let that = this;
        that.showDownTime = that.downTimeCount;
        clearInterval(that.countdown);
        that.countdown = setInterval(() => {
          that.showDownTime--;
          if (that.showDownTime < 0) {
            clearInterval(that.countdown);
            that.countdown = null;
            return;
          }
          if (that.showDownTime < 1) {
            var succHas = that.successList.some((item) => {
              if (that.showListData[that.qIdIndex].word == item.word) {
                return true;
              }
            });
            if (!succHas) {
              that.markWrongWord(true);
            }
          }
        }, 1000);
      },

      //复习报告取消
      closeDialog() {
        this.$refs.popopPower.close();
      },

      //查看报告弹框关闭
      closeDialog1() {
        // #ifdef APP-PLUS
        this.backPage();
        // #endif
        this.$refs.popopPower.close();
      },

      // 标记正确单词
      markRightWord() {
        console.log('标记正确单词');
        let that = this;
        let nowItem = this.showListData[this.qIdIndex];
        this.wordInfo.studentWordId = nowItem.id;
        this.wordInfo.scheduleCode = nowItem.scheduleCode;
        this.$httpUser.post(`znyy/course/fun/play/word`, this.wordInfo).then((res) => {
          if (!res.data.success) {
            that.$util.alter(res.data.message);
          } else {
            that.isRight = 'answerMaskSuccess';
            that.isRightIcon = 'answerMaskSuccessIcon';
            var errHas = that.errorList.some((item) => {
              if (that.showListData[that.qIdIndex].word == item.word) {
                return true;
              }
            });
            if (!errHas) {
              that.successList.push(that.showListData[that.qIdIndex]);
            }
          }
        });
      },

      // 标记正确单词--new
      newMarkRightWord(isEx) {
        console.log('标记正确单词');
        let that = this;
        let nowItem = this.showListData[this.qIdIndex];
        this.wordInfo.studentWordId = nowItem.id;
        this.wordInfo.scheduleCode = nowItem.scheduleCode;
        this.$httpUser.post(`znyy/course/noLevel/fun/play/word/finish`, this.wordInfo).then((res) => {
          if (!res.data.success) {
            that.$util.alter(res.data.message);
            console.log(33444444444444);
          } else {
            if (isEx) {
              //倒计时结束圆圈显示
              that.isRight = '';
            } else {
              that.$playVoiceApp('task_success.mp3', true, that.timbre, that.pronunciationType, that.playType, that.showData.studentCode);
              that.isRight = 'answerMaskSuccess';
              that.isRightIcon = 'answerMaskSuccessIcon';
            }
            that.nextQuestion();
            var errHas = that.errorList.some((item) => {
              if (that.showListData[that.qIdIndex].word == item.word) {
                return true;
              }
            });
            if (!errHas) {
              that.successList.push(that.showListData[that.qIdIndex]);
            }
          }
        });
      },

      // 标记错误单词   isEx true 倒计时结束执行
      markWrongWord(isEx) {
        let that = this;
        if (isEx) {
          that.$playVoiceApp('game_over.mp3', true, that.timbre, that.pronunciationType, that.playType, that.showData.studentCode);
        } else {
          that.$playVoiceApp('ino.mp3', true, that.timbre, that.pronunciationType, that.playType, that.showData.studentCode);
        }

        let nowItem = that.showListData[that.qIdIndex];
        that.isRight = 'answerMaskErr';
        that.isRightIcon = 'answerMaskErrIcon';
        setTimeout(() => {
          that.isRight = '';
          that.isRightIcon = '';
          console.log(that.isRight, that.isRightIcon);
          this.showChooseAnswer = [];
          that.$forceUpdate();
        }, 1000);

        that.wordInfo.studentWordId = nowItem.id;
        // that.$httpUser.post("znyy/course/mark/wrong/words", that.wordInfo).then((res) => {
        // 	if (res.data.success) {
        console.log('标记错误成功');
        var errHas = that.errorList.some((item) => {
          if (that.showListData[that.qIdIndex].word == item.word) {
            return true;
          }
        });
        var succHas = that.successList.some((item) => {
          if (that.showListData[that.qIdIndex].word == item.word) {
            return true;
          }
        });
        if (!errHas && !succHas) {
          that.errorList.push(that.showListData[that.qIdIndex]);
        }
        // 如果倒计时结束就标记玩过
        if (isEx) {
          that.newMarkRightWord(isEx);
          // that.nextQuestion();
        }
        // } else {
        // 	that.$util.alter(res.data.message)
        // }
        // })
      },
      // 返回上一页
      backPage() {
        clearInterval(this.countdown);
        this.countdown = null;
        //返回按钮
        uni.navigateBack({ delta: 1 });
        // uni.redirectTo({
        // 	url: '/antiAmnesia/review/funReview?scheduleCode=' + this.showData.scheduleCode + '&merchantCode='+this.showData.merchantCode
        // })
      },
      async guideNext() {
        this.isGuide = 1;
        await uni.setStorageSync('pplGuide', this.isGuide);
        this.$refs.guideOne.close();
        this.$refs.guideTwo.open();
      },
      async guideClose() {
        this.isGuide = 2;
        await uni.setStorageSync('pplGuide', this.isGuide);
        this.$refs.guideTwo.close();
        this.initialFunction();
      }
    },

    onUnload() {
      // 页面关闭后销毁实例
      clearInterval(this.countdown);
      this.countdown = null;
    }
  };
</script>

<style>
  page {
    height: 100vh;
    padding: 0;
  }

  .funContent {
    width: 100%;
    padding: 0 30rpx;
    box-sizing: border-box;
    height: 100%;
    background: url('https://document.dxznjy.com/applet/newInteresting/interesting_ppl_bg.png') 100% 100% no-repeat;
    background-size: 100% 100%;
    position: relative;
  }

  .interesting_title {
    margin-left: -30rpx;
    margin-right: -30rpx;
    position: relative;
    /* width: 100%; */
    height: 474rpx;
    background: url('https://document.dxznjy.com/applet/newInteresting/interesting_ppl_title.png') top center no-repeat;
    background-size: 100% 100%;
  }

  .interesting_text {
    position: absolute;
    top: 78rpx;
    left: 48%;
    transform: translateX(-30%);
    max-width: 136rpx;
    word-wrap: break-word;
    color: #9d582a;
    font-weight: bold;
    font-size: 32rpx;
  }
  .title_icon {
    top: 35%;
    left: 48%;
    position: absolute;
    width: 40rpx;
    height: 32rpx;
    background: url('https://document.dxznjy.com/applet/newInteresting/interesting_laba.png') 100% 100% no-repeat;
    background-size: 100% 100%;
  }

  .answerShowList {
    position: absolute;
    top: 55%;
    left: 50%;
    transform: translateX(-50%); /* 通过负的左边距来居中 */
    text-align: center;
    display: flex;
    justify-content: center;
  }

  .answerMaskErr {
    background: #e84335 !important;
    border: 3rpx solid #ffffff !important;
    color: #ffffff !important;
  }

  .answerMaskSuccess {
    background: #2db702 !important;
    border: 3rpx solid #ffffff !important;
    color: #ffffff !important;
  }

  .answerMask {
    position: relative;
    display: inline-block;
    margin: 17rpx;
    border-radius: 50%;
    width: 100rpx;
    height: 100rpx;
    line-height: 100rpx;
    background: #ecd396;
    border: 3rpx solid #9d582a;
    color: #803f10;
  }

  .answerMaskSuccessIcon {
    top: -7rpx;
    right: -11rpx;
    position: absolute;
    width: 40rpx;
    height: 40rpx;
    background: url('https://document.dxznjy.com/applet/newInteresting/interesting_ppl_right.png') 100% 100% no-repeat;
    background-size: 100% 100%;
  }

  .answerMaskErrIcon {
    top: -7rpx;
    right: -11rpx;
    position: absolute;
    width: 40rpx;
    height: 40rpx;
    background: url('https://document.dxznjy.com/applet/newInteresting/interesting_ppl_wrong.png') 100% 100% no-repeat;
    background-size: 100% 100%;
  }

  /* 趣味复习倒计时 */
  .userProgressbg {
    position: fixed;
    bottom: 0;
    width: 100%;
    margin-left: -30rpx;
    height: 118rpx;
    background-color: #daf5ff;
  }
  .userProgress {
    position: absolute;
    left: 27rpx;
    bottom: 46rpx;
    width: 610rpx;
    height: 40rpx;
  }
  .userProgress .cmd-progress-line {
    display: block !important;
  }
  .userProgress .cmd-progress-line .cmd-progress-inner {
    border: 4rpx solid #44a6fb;
    height: 40rpx;
    background-color: #ffffff;
  }
  /deep/.cmd-progress-anim {
    background-image: none !important;
  }
  .interesting_countdown {
    position: absolute;
    bottom: 35rpx;
    right: 27rpx;
    font-size: 36rpx;
    color: #4eb2fa;
    text-align: center;
  }

  .guide_btn_next {
    position: absolute;
    bottom: 193rpx;
    right: 64rpx;
    width: 269rpx;
    height: 142rpx;
  }

  .guide_btn_close {
    position: absolute;
    bottom: 524rpx;
    right: 54rpx;
    width: 269rpx;
    height: 142rpx;
  }

  .answerListBox {
    width: 666rpx;
    /* 		width: 520rpx; */
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    margin: 0 auto 0 auto;
    overflow-y: auto;
    padding-bottom: 40rpx;
  }

  .answerListParent {
  }

  .answerList {
    position: relative;
    width: 186rpx;
    height: 184rpx;
    /* 		height: 226rpx; */
    margin-left: 84rpx;
    /* margin-top: 40rpx; */
    line-height: 226rpx;
    background: url('https://document.dxznjy.com/applet/newInteresting/interesting_ppl_chose1.png') 100% 100% no-repeat;
    background-size: 100% 100%;
  }
  .answerListText {
    position: absolute;
    top: 16rpx;
    /*        top:52rpx; */
    left: 50%; /* 水平居中 */
    transform: translateX(-50%); /* 通过负的左边距来居中 */
    font-size: 36rpx;
    color: #ffffff;
  }
  .answerListDetail {
    text-align: center;
    width: 333rpx;
    height: 76rpx;
  }
  .answerListDetailText {
    font-size: 28rpx;
    line-height: 38rpx;
    color: #ffffff;
    max-width: 333rpx;
    word-wrap: break-word;
  }
</style>