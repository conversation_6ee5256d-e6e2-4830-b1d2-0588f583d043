<template>
  <view>
    <!-- 认领 -->
    <view class="claim-content" v-if="showContentStatus === '1'">
      <view class="claim-main flex space-between items-center">
        <view class="name-space">{{ searchResult.nickName }} {{ searchResult.mobile }}</view>
        <view class="button-box" :style="{ width: showContentStatus === '1' ? '80rpx' : '120rpx' }">
          <view class="button-box-text" v-if="showContentStatus === '1'" @click="claimBtn">认领</view>
        </view>
      </view>
    </view>
    <!-- 申诉 -->
    <view class="show-content" v-if="showContentStatus === '2'">
      <view class="show-main flex space-between items-center">
        <view class="name-space">
          <view class="student flex flex-x-s">
            <view class="student-name mr-10">{{ searchResult.nickName }}</view>
            <view class="student-phone">{{ searchResult.mobile }}</view>
          </view>
          <view class="teacher flex flex-x-s f-24">
            <view class="teacher-name mr-10">{{ searchResult.currentPartnerRealName }}</view>
            <view class="teacher-phone">编码{{ searchResult.currentPartnerMerchantCode }}</view>
          </view>
        </view>
        <view class="button-box">
          <view class="button-box-text" style="width: 120rpx" @click="claimSubmitBtn">申诉</view>
        </view>
      </view>
    </view>
    <!-- 已确认 -->
    <view class="show-content" v-if="showContentStatus === '3'">
      <view class="show-main flex space-between items-center">
        <view class="name-space">
          <view class="student flex flex-x-s f-28">
            <view class="student-name mr-10">{{ searchResult.nickName }}</view>
            <view class="student-phone">{{ searchResult.mobile }}</view>
          </view>
          <view class="teacher flex flex-x-s f-24">
            <view class="teacher-name mr-10">{{ searchResult.currentPartnerRealName }}</view>
            <view class="teacher-phone">编码{{ searchResult.currentPartnerMerchantCode }}</view>
          </view>
        </view>
        <view class="button-box">
          <view class="button-box-text" style="width: 120rpx">已确认</view>
        </view>
      </view>
    </view>
    <!--  -->
    <view class="show-content" v-if="showContentStatus === '4'">
      <view class="show-main flex space-between items-center">
        <view class="name-space">
          <view class="student flex flex-x-s f-28">
            <view class="student-name mr-10">{{ searchResult.nickName }}</view>
            <view class="student-phone">{{ searchResult.mobile }}</view>
          </view>
          <view class="teacher flex flex-x-s f-24">
            <view class="teacher-name mr-10">{{ searchResult.currentPartnerRealName }}</view>
            <view class="teacher-phone">编码{{ searchResult.currentPartnerMerchantCode }}</view>
          </view>
        </view>
        <view class="button-box" v-if="showContentStatus !== '4'">
          <view class="button-box-text" style="width: 120rpx"></view>
        </view>
      </view>
    </view>
    <!-- 请输入  -->
    <image
      v-if="showContentStatus === '5'"
      src="https://document.dxznjy.com/dxSelect/7b12b0d2-a30e-4cd0-8370-df106ad75082.png"
      mode="widthFix"
      style="width: 100%; height: 464rpx"
    ></image>
    <!-- 未搜到 -->
    <image
      v-if="showContentStatus === '6'"
      src="https://document.dxznjy.com/dxSelect/9d7f0ee7-3a52-4889-a50c-0d122e65c650.png"
      mode="widthFix"
      style="width: 100%; height: 464rpx"
    ></image>
  </view>
</template>

<script>
  export default {
    name: 'StudentMessage',
    props: {
      searchResult: {
        type: Object,
        default: {}
      },
      showContentStatus: {
        type: String,
        default: ''
      }
    },
    data() {
      return {};
    },
    methods: {
      claimSubmitBtn() {
        this.$emit('claimSubmitBtn');
      },
      claimBtn() {
        this.$emit('claimBtn');
      }
    }
  };
</script>

<style lang="scss" scoped>
  .claim-content {
    width: 686rpx;
    height: 168rpx;
    background: #b4d5c8;
    padding: 40rpx 24rpx;
    box-sizing: border-box;
    border-radius: 10rpx;

    .claim-main {
      width: 636rpx;
      height: 88rpx;
      line-height: 88rpx;
      background: #ffffff;
      border-radius: 8rpx;

      .name-space {
        margin-left: 24rpx;
        color: #555555;
        font-size: 28rpx;
        height: 40rpx;
        line-height: 40rpx;
      }

      .button-box {
        width: 80rpx;
        height: 40rpx;
        font-family: AlibabaPuHuiTi_3_85_Bold;
        font-size: 28rpx;
        color: #45856e;
        line-height: 40rpx;
        text-align: left;
        font-style: normal;
        margin-right: 16rpx;
        border-left: 1px solid #f3f3f3;

        .button-box-text {
          margin-left: 20rpx;
        }
      }
    }
  }
  .show-content {
    width: 686rpx;
    height: 168rpx;
    background: #f3f9f3;
    border-radius: 10rpx;
    border: 1rpx solid #b4d5c8;
    box-sizing: border-box;
    padding: 24rpx;

    .show-main {
      width: 636rpx;
      height: 122rpx;
      background: #ffffff;
      border-radius: 8rpx;

      .name-space {
        margin-left: 24rpx;

        .student {
          color: #555555;
          margin-bottom: 20rpx;

          .student-name {
            width: 230rpx;
          }
        }

        .teacher {
          color: #bcbcbc;

          .teacher-name {
            width: 230rpx;
          }
        }
      }

      .button-box {
        width: 120rpx;
        height: 40rpx;
        font-family: AlibabaPuHuiTi_3_85_Bold;
        font-size: 28rpx;
        color: #fd9853;
        line-height: 40rpx;
        text-align: left;
        font-style: normal;
        // margin-right: 16rpx;

        border-left: 1px solid #f3f3f3;

        .button-box-text {
          margin-left: 20rpx;
        }
      }
    }
  }
</style>
