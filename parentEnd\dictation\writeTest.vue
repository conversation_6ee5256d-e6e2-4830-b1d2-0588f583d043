<!-- 学习打印页面 -->
<template>
  <view class="write_warpper">
    <!-- <audio ref="myAudio" preload="auto" controls="">
		  <source :src="linkUrl" type="audio/mpeg">  
		</audio>  -->
    <view class="write_box">
      <view>
        <image src="https://document.dxznjy.com/applet/newimages/play.png" class="playAudio" @click="playAudio" mode="widthFix" v-if="!showLink" />
        <image src="https://document.dxznjy.com/course/e13bf8284d68462c9ea7d5921eddf292.png" v-if="showLink" class="playAudio" mode="widthFix" />
      </view>
      <view>
        <view class="playtext" @click="playAudio">点击开始播放单词音频</view>
        <view class="Progress_wapper">
          <!-- <image src="../../static/dictation/Progress.png" class="Progress" mode="widthFix"> -->
          <image src="https://document.dxznjy.com/applet/newimages/speed.png" class="speed" mode="widthFix" />
          <u-line-progress class="progress_bar" :percentage="percentage" :showText="false" activeColor="#7BD686" height="14" inactiveColor="#E4E4E4"></u-line-progress>
        </view>
      </view>
    </view>
    <view class="writeInput_wapper">
      <view class="inputHead">
        <view class="title">单词拼写区</view>
        <view class="Countdown" v-if="showTime">
          <image src="https://document.dxznjy.com/applet/newimages/countdown.png" class="countdown" mode="widthFix" />
          <div>{{ endTime + 's' }}</div>
        </view>
      </view>
      <view class="input_Box">
        <textarea class="textarea" placeholder="请输入单词" v-model="writeWord" @input="inputWord" />
      </view>
    </view>
    <view class="dictation_footer">
      <!-- 开始答题 -->
      <view class="dictation_btn">
        <view class="lastTest" @click="dontKnow">不会</view>
        <view class="startTest" @click="nextWord">下一题</view>
      </view>
    </view>
    <view class="popup" v-show="showDialog">
      <!-- 弹窗内容 -->
      <view class="popup-content" style="background-image: url('https://document.dxznjy.com/applet/newimages/answerEndBg.png')">
        <view class="tips">本轮测试已结束，请复习后再来测试</view>
        <view class="btn_box">
          <view class="popupBtn" style="background-image: url('https://document.dxznjy.com/applet/newimages/confirm.png')" @click="submitTest">确定</view>
          <view class="popupBtn" style="background-image: url('https://document.dxznjy.com/applet/newimages/cancel.png')" @click="submitTest">取消</view>
        </view>
      </view>
      <view class="popup-mask" @tap="hide"></view>
      <!-- 遮罩层，点击关闭弹窗 -->
    </view>
  </view>
</template>

<script>
  import CryptoJS from 'crypto-js';
  const { $http } = require('@/util/methods.js');
  export default {
    props: {
      writeDuration: {
        type: String,
        default: ''
      },
      wordList: {
        type: Array,
        default: ''
      },
      startTime: {
        type: String,
        default: ''
      },
      studentCode: {
        type: String,
        default: ''
      }
    },
    data() {
      return {
        endTime: 0, //答题剩余时间倒计时
        showTime: false,
        timer: null, //定时器
        innerAudioContext: null,
        writeWord: '',
        parentCode: '', //家长code
        writeIndex: 0, //答题下标
        timbre: 'W', // 音色默认女声 M  W
        pronunciationType: 0, // 1英式  0美式  默认美式
        playType: 1, // 版本
        linkUrl: '', //音频连接
        percentage: 0, //单个单词听写进度
        showLink: false,
        notWriteNum: 0, //记录不会或者做错的次数
        showDialog: false, //答题技术弹窗
        writeAnswer: {
          singleList: [],
          manyList: [],
          suffixList: []
        }, //听写结果字段
        answerEndTime: '', //答题结束时间
        clickSubmit: false
      };
    },
    onReady() {
      if (this.timer) {
        clearInterval(this.timer);
      }
      this.endTime = Math.ceil((this.endTime = Number(this.writeDuration) * 60));
      this.showTime = this.writeDuration == '0' ? false : true;
      this.homeData();
      this.startCountdown(this.endTime);
      this.initAudio();
    },
    onLoad(option) {},
    watch: {
      writeIndex(val) {
        console.log(val);
      },
      endTime(val) {
        if (val <= 0) {
          this.checkIsOvertest('1');
        }
      }
    },
    methods: {
      checkIsOvertest(type) {
        let _this = this;
        if (this.writeIndex < this.wordList.length - 1) {
          this.wordList.forEach((item, index) => {
            if (_this.writeIndex <= index) {
              const studyType = item.studyType;
              item.isSuccess = '-1';
              switch (studyType) {
                case 'DYJ1':
                  _this.writeAnswer.singleList.push(item);
                  break;
                case 'DYJ2':
                  _this.writeAnswer.manyList.push(item);
                  break;
                case 'QHZ':
                  _this.writeAnswer.suffixList.push(item);
                  break;
                default:
                  break;
              }
            }
          });
        }
        if (type === '1') {
          uni.showToast({
            icon: 'none',
            title: '倒计时结束,答题结束！',
            duration: 2000
          });
        } else {
          uni.showToast({
            icon: 'none',
            title: '您已结束检测!',
            duration: 2000
          });
        }
        clearInterval(_this.timer);
        _this.recordEndTime();
        _this.showDialog = true;
      },
      async submitTest() {
        if (this.clickSubmit) {
          uni.showToast({
            icon: 'none',
            title: '请勿重复提交'
          });
          return;
        }
        this.clickSubmit = true;
        const readAnswer = uni.getStorageSync('readAnswer');
        readAnswer.singleList = readAnswer.singleList.filter((item) => item.studyType === 'DYJ1');
        readAnswer.manyList = readAnswer.manyList.filter((item) => item.studyType === 'DYJ2');
        readAnswer.suffixList = readAnswer.suffixList.filter((item) => item.studyType === 'QHZ');
        console.log('测试单词数量', this.writeAnswer.length);
        let param = {
          studentCode: this.studentCode,
          wordNumber: this.wordList.length,
          read: readAnswer,
          write: this.writeAnswer,
          startTime: this.startTime,
          endTime: this.answerEndTime
        };
        console.log('saveStudentLevel  param------------------', param);
        /* return */
        let result = await this.$httpUser.post('znyy/pd/mobile/saveStudentLevel', param);
        console.log('result-----', result);
        if (result.data.code == '20000') {
          this.showDialog = false;
          uni.showToast({
            icon: 'none',
            title: '提交成功',
            duration: 1500
          });
          this.writeAnswer = [];
          uni.removeStorageSync('readAnswer');
          setTimeout(() => {
            uni.navigateTo({
              url: '/parentEnd/report/dictationSingleReport?id=' + '' + '&studentCode=' + this.studentCode
            });
          }, 1000);
          this.endTime = 0;
          clearInterval(this.timer);
        } else {
          this.showDialog = false;
        }
      },
      hide() {
        this.showDialog = false;
        // this.$emit('update:show', false); // 通知父组件关闭弹窗
      },
      async initAudio() {
        // 创建一个内部音频上下文对象
        this.innerAudioContext = uni.createInnerAudioContext();
        // 设置音频地址
        /* const audioUrl = '*/

        // 可选：设置音频的其他属性，如是否自动播放、音量等
        this.innerAudioContext.autoplay = true; // 默认不自动播放
        this.innerAudioContext.volume = 1; // 音量范围 0~1

        this.innerAudioContext.onPlay(() => {
          this.showLink = true;
          console.log('音频开始播放');
        });
        // 监听播放结束事件
        this.innerAudioContext.onEnded(() => {
          this.showLink = false;
          console.log('音频播放结束');
          // 在这里可以添加播放结束后的处理逻辑
        });
        // 播放音频
      },
      playAudio() {
        this.sayWord();
      },
      // 获取当前学员设置的语音版本
      async getWordversion() {
        var that = this;
        that.$httpUser
          .get('znyy/course/info', {
            studentCode: that.studentCode
          })
          .then((res) => {
            if (res.data.success) {
              let name = res.data.data.voiceModel.split('#');
              that.pronunciationType = name[1];
              that.timbre = name[2];
              that.playType = name[0];
            } else {
              that.$util.alter(res.data.message);
            }
          });
      },
      //获取用户信息
      async homeData() {
        let _this = this;
        const res = await $http({
          url: 'zx/user/userInfoNew'
        });
        if (res) {
          let userinfo = res.data;
          this.parentCode = userinfo.userCode;
          let data = userinfo.userCode + 'L0anhf';
          this.sg = CryptoJS.SHA1(data).toString();
          // this.getWordversion();
        }
      },
      //倒计时
      startCountdown(seconds) {
        let _this = this;
        // 确保秒数是整数
        // seconds = Math.round(seconds);
        // 检查秒数是否有效
        if (seconds < 0) {
          return;
        }
        // 定时器ID，用于稍后清除定时器
        // 倒计时函数
        function countdown() {
          _this.endTime = seconds;
          // 每秒执行一次
          seconds--;
          // 如果倒计时结束，清除定时器并打印消息
          if (seconds < 0) {
            _this.endTime = 0;
            clearInterval(_this.timer);
          }
          const pages = getCurrentPages();
          const currentPage = pages[pages.length - 1]; // 假设数组最后一个是当前页面
          if (currentPage.route != 'parentEnd/dictation/dictation') {
            clearInterval(_this.timer);
          }
        }
        // 启动定时器
        _this.timer = setInterval(countdown, 1000);
      },

      inputWord(val) {
        let len1 = val.target.cursor;
        let len2 = Number(this.wordList[this.writeIndex].word.length);
        this.percentage = (len1 / len2) * 100;
      },
      dontKnow() {
        this.notWriteNum++;
        if (this.notWriteNum === 3) {
          this.checkIsOvertest('2');
          return;
        }
        const wordObj = this.wordList[this.writeIndex];
        wordObj.isSuccess = '-1';
        const studyType = wordObj.studyType;
        switch (studyType) {
          case 'DYJ1':
            this.writeAnswer.singleList.push(wordObj);
            break;
          case 'DYJ2':
            this.writeAnswer.manyList.push(wordObj);
            break;
          case 'QHZ':
            this.writeAnswer.suffixList.push(wordObj);
            break;
          default:
            break;
        }
        this.writeIndex++;
        this.writeWord = '';
        this.percentage = 0;
        if (this.writeIndex > this.wordList.length - 1) {
          clearInterval(this.timer);
          this.recordEndTime();
          this.writeIndex = 0;
          this.showDialog = true;
          return;
        }
        this.sayWord();
      },
      //下一题
      nextWord() {
        //先收集当前题目的答案
        this.putAnswer();
        console.log(this.wordList[this.writeIndex]);
        this.writeIndex++;
        this.percentage = 0;
        if (this.writeIndex <= this.wordList.length - 1) {
          this.writeWord = '';
        }
        if (this.writeIndex > this.wordList.length - 1) {
          clearInterval(this.timer);
          this.recordEndTime();
          this.showDialog = true;
          return;
        }
        this.sayWord();
      },
      recordEndTime() {
        let date = new Date();
        let year = date.getFullYear();
        // 注意：JavaScript 中的月份是从 0 开始的，所以需要 +1
        let month = (date.getMonth() + 1).toString().padStart(2, '0');
        // 类似地，日期和小时、分钟、秒也可能只有一位数，所以需要 padStart
        let day = date.getDate().toString().padStart(2, '0');
        let hours = date.getHours().toString().padStart(2, '0');
        let minutes = date.getMinutes().toString().padStart(2, '0');
        let seconds = date.getSeconds().toString().padStart(2, '0');
        this.answerEndTime = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
      },
      //收集答案
      putAnswer() {
        const wordObj = this.wordList[this.writeIndex];
        wordObj.isSuccess = wordObj.word == this.writeWord ? '1' : '-1';
        if (wordObj.isSuccess === '1') {
          this.notWriteNum = 0;
        } else {
          this.notWriteNum++;
          if (this.notWriteNum === 3) {
            this.checkIsOvertest('2');
            return;
          }
        }
        const studyType = wordObj.studyType;
        switch (studyType) {
          case 'DYJ1':
            this.writeAnswer.singleList.push(wordObj);
            break;
          case 'DYJ2':
            this.writeAnswer.manyList.push(wordObj);
            break;
          case 'QHZ':
            this.writeAnswer.suffixList.push(wordObj);
            break;
          default:
            break;
        }
      },
      sayWord() {
        const word = this.wordList[this.writeIndex].word;
        var that = this;
        that.$httpUser
          .get('znyy/app/query/word/voice', {
            word: word
          })
          .then((result) => {
            if (result.data.success) {
              let voiceUrl;
              let url;
              if (that.playType == 1) {
                voiceUrl = 'https://document.dxznjy.com/' + encodeURIComponent(result.data.data);
                that.linkUrl = voiceUrl;
              } else {
                voiceUrl = result.data.data;
                that.linkUrl = voiceUrl;
              }
              this.innerAudioContext.autoplay = false; // 默认不自动播放
              this.innerAudioContext.volume = 1; // 音量范围 0~1
              this.innerAudioContext.src = that.linkUrl;
              this.innerAudioContext.play();
            } else {
              that.$util.alter(result.data.message);
            }
          })
          .catch((err) => {
            console.log('err', err);
          });
      }
    }
  };
</script>

<style lang="scss">
  .write_warpper {
    width: 100%;
    height: auto;
    background: #f0f8f0;
    padding-top: 144rpx;
  }

  .write_box {
    width: 686rpx;
    height: 204rpx;
    padding-top: 36rpx;
    display: flex;
    // align-items: center;
    justify-content: center;
    position: relative;
    box-sizing: border-box;
    background-image: url('https://document.dxznjy.com/applet/newimages/write_bg.png');
    background-size: cover;
    background-repeat: no-repeat;
    margin: 0 auto;
    box-sizing: border-box;

    .playAudio {
      width: 96rpx;
      height: 96rpx;
      margin-right: 24rpx;
    }

    .playtext {
      width: 304rpx;
      height: 40rpx;
      line-height: 40rpx;
      margin-bottom: 24rpx;
      font-size: 24rpx;
      white-space: nowrap;
      background: #efffe4;
      color: #14806c;
      border-radius: 29rpx 12rpx 12rpx 29rpx;
      text-align: center;
    }

    .Progress_wapper {
      position: relative;

      .Progress {
        width: 406rpx;
        height: 16rpx;
      }

      .speed {
        position: absolute;
        left: 0;
        top: -13px;
        z-index: 9999;
        width: 30rpx;
        height: 40rpx;
      }
    }
  }

  .writeInput_wapper {
    width: 686rpx;
    height: 734rpx;
    margin: 0 auto;
    position: relative;
    top: -40rpx;
    background: #ffffff;
    box-shadow: 0rpx -4rpx 20rpx 0rpx rgba(0, 0, 0, 0.08);
    border-radius: 40rpx;
    padding: 24rpx 32rpx;
    box-sizing: border-box;

    .inputHead {
      display: flex;
      align-items: center;
      justify-content: space-between;

      .title {
        font-weight: bold;
        font-size: 32rpx;
        color: #333333;
      }

      .Countdown {
        width: 128rpx;
        height: 48rpx;
        background: #f3ffe1;
        border-radius: 49rpx;
        display: flex;
        font-size: 24rpx;
        align-items: center;
        justify-content: center;

        image {
          width: 25rpx;
          height: 25rpx;
          margin-right: 5rpx;
        }
      }
    }

    .input_Box {
      margin-top: 32rpx;

      .textarea {
        border: 2rpx solid #f3f3f3;
      }
    }
  }

  .dictation_footer {
    width: 100%;
    margin-top: 60rpx;
    font-size: 40rpx;
    display: flex;
    justify-content: center;

    .dictation_btn {
      display: flex;
      justify-content: center;
    }

    .lastTest {
      width: 312rpx;
      height: 94rpx;
      line-height: 94rpx;
      margin-right: 30rpx;
      color: #931413;
      box-sizing: border-box;
      background-image: url('https://document.dxznjy.com/applet/newimages/lastTest.png');
      background-size: cover;
      background-repeat: no-repeat;
      text-align: center;
    }

    .startTest {
      width: 312rpx;
      height: 102rpx;
      line-height: 102rpx;
      color: #178071;
      box-sizing: border-box;
      background-image: url('https://document.dxznjy.com/applet/newimages/startTest.png');
      background-size: cover;
      background-repeat: no-repeat;
      text-align: center;
    }
  }

  .popup {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.3);
    display: flex;
    justify-content: center;
    z-index: 6666;

    .popup-content {
      .tips {
        width: 100%;
        font-weight: bold;
        white-space: nowrap;
        font-size: 32rpx;
        color: #333333;
        // margin: 0 auto;
        text-align: center;
        margin-top: 320rpx;
      }

      // background-image: url('../../static/dictation/answerEndBg.png');
      background-size: cover;
      background-repeat: no-repeat;
      /* 定义弹窗内容的样式 */
      // border-radius: 10px;
      box-sizing: border-box;
      width: 620rpx;
      height: 778rpx;
      margin-top: 170rpx;
      z-index: 9999;
      text-align: center;

      .btn_box {
        margin-top: 230rpx;
        display: flex;
        align-items: center;
        justify-content: center;

        .popupBtn {
          width: 274rpx;
          height: 114rpx;
          line-height: 114rpx;
          text-align: center;
          background-size: cover;
          background-repeat: no-repeat;
          color: #ffffff;
          margin: 0 10rpx;
        }
      }

      /* ...其他样式 */
    }

    /* .popup-mask {  
	    position: absolute;  
	    top: 0;  
	    left: 0;  
	    width: 100%;  
	    height: 100%;  
		background-color: rgba(0,0,0,0.3);
	  } */
  }
</style>
