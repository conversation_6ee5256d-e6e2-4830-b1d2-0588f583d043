<template>
  <view style="padding: 20rpx 30rpx">
    <view v-if="show">
      <view class="order" v-if="reportlist.needDetail">
        <view class="details">日期：{{ list.dateTime || '' }}</view>
        <view class="details">姓名：{{ list.studentName || '' }}</view>
        <view class="details">年级：{{ list.gradeName || '' }}</view>
        <view class="details">学习时间-结束时间：{{ list.studyTime || '' }}</view>
        <!-- <view class="details">教练：{{list.studyTime || ''}}</view> -->
        <view class="details">试学学时：{{ list.studyHour || '' }}小时</view>
        <view class="details">词汇测试水平：{{ list.vocabularyLevel || '' }}</view>
        <view class="details">首测词汇量：{{ list.expWords || '' }}个</view>
        <view class="details">识记词汇数量：{{ list.todayWords || '' }}个</view>
        <view class="details">遗忘数量：{{ list.forgetWords || '' }}个</view>
        <view class="details">记忆率：{{ list.wordsRate || '' }}%</view>
        <view class="details">体验词库：{{ list.studyBooks || '' }}</view>
        <view class="details" v-if="list.memory == 1">记忆特点：弱</view>
        <view class="details" v-if="list.memory == 2">记忆特点：正常</view>
        <view class="details" v-if="list.memory == 3">记忆特点：强</view>
        <view class="details flex-a-c flex-y-s">
          <view class="">教练反馈：</view>
          <text class="flex-box">{{ reportlist.tutorFeedback || '' }}</text>
        </view>
      </view>
      <view class="order" v-if="!reportlist.needDetail">
        <view class="details flex-a-c flex-y-s">
          <view class="">教练反馈：</view>
          <text class="flex-box">{{ reportlist.tutorFeedback || '' }}</text>
        </view>
      </view>
      <view class="shop_content">
        <view class="details bold" v-if="reportlist.parentFeedback == ''">
          请您对本次试课满意度打分：
          <view style="margin-top: 40rpx">
            <uni-rate :readonly="true" :size="19" :value="fraction" @change="rateChange" activeColor="#FFD400" />
          </view>
        </view>
        <!-- 我的满意度打分 -->
        <view class="details" v-if="reportlist.parentFeedback != ''">
          <view class="rate">
            <view class="bold">我的满意度打分</view>
            <view>
              <uniDateCheckbox v-model="reportlist.parentIsAnonymity == true ? 1 : 0" :localdata="anonymous" selectedColor="#2E896F"></uniDateCheckbox>
            </view>
          </view>
          <view style="margin-top: 40rpx">
            <uni-rate :readonly="true" :size="19" :value="reportlist.parentScore" activeColor="#FFD400" />
          </view>
          <view class="margins">
            <text class="content_wrap">{{ reportlist.parentFeedback }}</text>
          </view>

          <view style="margin: 30rpx 0" v-if="reportlist.deliverReply != ''">
            <view class="deliver bold">交付系统反馈</view>
            <view class="margins">
              <text class="content_wrap">{{ reportlist.deliverReply }}</text>
            </view>
          </view>
        </view>
        <u--textarea v-if="reportlist.parentFeedback == ''" v-model="feedback" :value="feedback" placeholder="写点反馈吧" placeholderStyle="color:#999;" height="240"></u--textarea>
        <view v-if="reportlist.parentFeedback == ''">
          <uniDateCheckbox v-model="radio" :localdata="anonymous" @change="change" selectedColor="#2E896F"></uniDateCheckbox>
        </view>
      </view>
      <view v-if="reportlist.parentFeedback == ''" class="ptb-10 plr-50">
        <button class="phone-btn" @click="getEvaluation">提交反馈</button>
      </view>
    </view>
    <view v-else class="t-c flex-col bg-ff radius-15" :style="{ height: useHeight + 'rpx' }">
      <image :src="imgHost + 'alading/correcting/no_data.png'" class="mb-20 img_s"></image>
      <view style="color: #bdbdbd">暂无数据</view>
    </view>
  </view>
</template>

<script>
  const { $showError, $showMsg, $http } = require('@/util/methods.js');
  import uniDateCheckbox from './components/uni-data-checkbox/components/uni-data-checkbox/uni-data-checkbox.vue';
  export default {
    components: {
      uniDateCheckbox
    },
    data() {
      return {
        //试课反馈记忆特点下拉框
        value: 0,
        range: [
          {
            value: 1,
            text: '弱'
          },
          {
            value: 2,
            text: '正常'
          },
          {
            value: 3,
            text: '强'
          }
        ],

        feedback: '', // 试课报告反馈
        fraction: '', //评分
        radio: 0,
        anonymous: [
          {
            text: '匿名提交',
            value: 1
          }
        ],
        anonymity: false, // 是否匿名
        satisfaction: false, // 我的满意度
        reportlist: [],
        expId: '',
        list: [],
        show: false,

        useHeight: 0, //除头部之外高度
        imgHost: getApp().globalData.imgsomeHost
      };
    },
    onLoad(e) {
      console.log('试课报告', e);
      this.expId = e.expId;
    },
    onReady() {
      uni.getSystemInfo({
        success: (res) => {
          // 可使用窗口高度，将px转换rpx
          let h = res.windowHeight * (750 / res.windowWidth);
          this.useHeight = h - 50;
        }
      });
    },
    onShow() {
      this.getTestreport();
    },
    methods: {
      //评分
      rateChange(e) {
        this.fraction = e.value;
      },
      //是否匿名
      change(e) {
        if (this.radio == 1) {
          this.anonymity = true;
        }
      },
      // 试课报告详情
      async getTestreport() {
        let _this = this;
        uni.showLoading();
        let res = await $http({
          url: 'deliver/app/feedback/experienceReportDetail',
          data: {
            experienceId: _this.expId
          }
        });
        uni.hideLoading();
        if (res) {
          _this.show = true;
          _this.reportlist = res.data;
          _this.list = res.data.experienceStatisticsDto;
          console.log(_this.list);
        }
      },
      // 家长反馈评价
      async getEvaluation() {
        let res = await $http({
          url:
            'deliver/app/feedback/parentFeedback?expId=' +
            this.expId +
            '&parentFeedback=' +
            encodeURI(this.feedback) +
            '&parentIsAnonymity=' +
            this.anonymity +
            '&parentScore=' +
            this.fraction,
          method: 'POST'
        });
        if (res) {
          console.log(res, '提交反馈');
          uni.navigateBack();
        }
      }
    }
  };
</script>

<style lang="scss">
  .img_s {
    width: 160rpx;
    height: 160rpx;
  }

  .order {
    background-color: #fff;
    padding: 30rpx;
    font-size: 30rpx;
    border-radius: 14rpx;
    color: #333;
  }

  .back {
    margin-top: 20rpx;
  }

  .bold {
    font-size: 32rpx;
    font-weight: 700;
    color: #333;
  }

  .details {
    font-size: 30rpx;
    margin-bottom: 24rpx;
  }

  /deep/.u-textarea__field {
    font-size: 28rpx !important;
  }

  .u-textarea {
    margin-bottom: 15rpx;
  }

  .shop_content {
    background-color: #fff;
    border-radius: 14rpx;
    padding: 40rpx 30rpx 30rpx;
    margin: 20rpx 0;
  }

  .title {
    font-size: 32rpx;
    color: #000;
    margin-bottom: 14rpx;
    font-weight: 700;
  }

  .introduce {
    color: #333;
    font-size: 30rpx;
    line-height: 45rpx;
  }

  /deep/.checklist-content text {
    font-size: 30rpx;
    color: #333 !important;
  }

  /deep/.phone-btn {
    width: 100%;
    height: 90rpx;
    line-height: 90rpx;
    border-radius: 90rpx;
    font-size: 30rpx;
    color: #fff;
    background: linear-gradient(to bottom, #88cfba, #1d755c);
  }

  .rate {
    display: flex;
    justify-content: space-between;
  }

  /deep/.checklist-box {
    margin: 0 !important;
  }

  .deliver {
    padding-top: 30rpx;
    border-top: 1px dashed #c8c8c8;
  }

  .margins {
    margin-top: 20rpx;
  }

  .content_wrap {
    white-space: pre-wrap;
  }

  text {
    word-wrap: break-word;
    word-break: normal;
  }
</style>
