<template>
	<view>
		<!-- 顶部日期 -->
		<view class="bg-ff p-30 flex_s f-30 c-66 radius-15 choose_time">
			<image src="https://document.dxznjy.com/dxSelect/image/rili-5.png" class="time-img"></image>
			<uniDatetimePicker rangeSeparator="至" type="daterange" @change="tiemChange" />
			<view class="icon">
				<u-icon name="arrow-down" color="#C7C7C7"></u-icon>
			</view>
		</view>
		<!-- 学员信息 -->
		<view class="studentbox">
			<view class="stutext">
				<text>张三（622012901290）</text>
				<text class="hour">+15学时</text>
			</view>
			<view class="stutext">
				<view class="fei">
					<text>充值软件使用费：</text>
					<text>15学时</text>
				</view>
				<view>
					<text class="rightbtn success">充值成功</text>
					<!-- 					<text class="rightbtn have">充值中</text>
					<text class="rightbtn fail">充值失败</text> -->
				</view>
			</view>
			<view class="fei">2023-05-08 09:56</view>
		</view>
		<!-- 学员信息 -->
		<!-- 学员信息 -->
		<view class="studentbox">
			<view class="stutext">
				<text>张三（622012901290）</text>
				<text class="hour">+15学时</text>
			</view>
			<view class="stutext">
				<view class="fei">
					<text>充值软件使用费：</text>
					<text>15学时</text>
				</view>
				<view>
					<!-- 					<text class="rightbtn success">充值成功</text> -->
					<text class="rightbtn have">充值中</text>
					<!-- 					<text class="rightbtn fail">充值失败</text> -->
				</view>
			</view>
			<view class="fei">2023-05-08 09:56</view>
		</view>
		<!-- 学员信息 -->

		<!-- 学员信息 -->
		<view class="studentbox">
			<view class="stutext">
				<text>张三（622012901290）</text>
				<text class="hour">+15学时</text>
			</view>
			<view class="stutext">
				<view class="fei">
					<text>充值软件使用费：</text>
					<text>15学时</text>
				</view>
				<view>
					<!-- 					<text class="rightbtn success">充值成功</text> -->
					<!-- 					<text class="rightbtn have">充值中</text> -->
					<text class="rightbtn fail">充值失败</text>
				</view>
			</view>
			<view class="fei">2023-05-08 09:56</view>
		</view>
		<!-- 学员信息 -->

		<!-- 学员信息 -->
		<view class="studentbox">
			<view class="stutext">
				<text>张三（622012901290）</text>
				<text class="hour">+15学时</text>
			</view>
			<view class="stutext">
				<view class="fei">
					<text>充值软件使用费：</text>
					<text>15学时</text>
				</view>
				<view>
					<text class="rightbtn success">充值成功</text>
					<!-- 					<text class="rightbtn have">充值中</text>
					<text class="rightbtn fail">充值失败</text> -->
				</view>
			</view>
			<view class="fei">2023-05-08 09:56</view>
		</view>
		<!-- 学员信息 -->

		<!-- 学员信息 -->
		<view class="studentbox">
			<view class="stutext">
				<text>张三（622012901290）</text>
				<text class="hour">+15学时</text>
			</view>
			<view class="stutext">
				<view class="fei">
					<text>充值软件使用费：</text>
					<text>15学时</text>
				</view>
				<view>
					<text class="rightbtn success">充值成功</text>
					<text class="rightbtn have">充值中</text>
					<text class="rightbtn fail">充值失败</text>
				</view>
			</view>
			<view class="fei">2023-05-08 09:56</view>
		</view>
		<!-- 学员信息 -->

		<!-- 暂无数据 -->
		<!-- 		<view class="None">
			<view class="Nonecontent">
				<image src="https://document.dxznjy.com/dxSelect/image/queshengye_zanwushuju-2.png" class="None-img">
				</image>
				<view>暂无数据</view>
			</view>
		</view> -->
		<!-- 查询范围不可超过一年提示框 -->
		<view class="save" v-if="isAlert">
			<image src="https://document.dxznjy.com/dxSelect/image/fail.png" class="save-img">
			</image>
			<text class="save-text">查询范围不可超过一年</text>
		</view>

	</view>
</template>

<script>
 import uniDatetimePicker from '../components/uni-datetime-picker/components/uni-datetime-picker/uni-datetime-picker.vue'
	export default {
		components:{
			uniDatetimePicker
		},
		data() {
			return {
				isAlert: false, //超过一年提示框
			}
		},
		methods: {
			// 选择日期
			tiemChange(e) {
				const startDate = e[0];
				const endDate = e[1];

				this.checkTimeRange(startDate, endDate);
			},
			checkTimeRange(startDate, endDate) {
				const oneYearAgo = new Date();
				oneYearAgo.setFullYear(oneYearAgo.getFullYear() - 1);
				if (this.dateDiff(startDate, endDate) > 365) {
					setTimeout(() => {
						this.isAlert = true;

						setTimeout(() => {
							this.isAlert = false;
						}, 4000);
					}, 1000);
				}
			},
			// 计算时间有没有超过一年
			dateDiff(startDate, endDate) {
				const oneDay = 24 * 60 * 60 * 1000; // 一天的毫秒数
				const start = new Date(startDate);
				const end = new Date(endDate);
				const diffDays = Math.round(Math.abs((start - end) / oneDay)); // 计算天数差

				return diffDays;
			}
		}
	}
</script>

<style lang="scss">
	page {
		padding: 0 30rpx;

		// 顶部日期

		.time {
			width: 477rpx;
			height: 60rpx;
			border: 1rpx solid #C8C8C8;
			display: inline-flex;
			flex-direction: row;
			align-items: center;
			border-radius: 35rpx;
			padding: 0 30rpx;
		}


		.flex_s {
			display: flex;
			align-items: center;
		}

		/deep/.uni-date-x--border {
			border-radius: 35rpx !important;
		}

		/deep/.u-calendar-month__days__day__select__buttom-info {
			bottom: 5rpx !important;
		}

		/deep/.uni-date-x--border {
			border-radius: 45rpx !important;
		}

		/deep/.uni-date-x {
			background-color: transparent !important;
		}

		/deep/.uni-icons {
			color: #fff !important;
		}

		/deep/.uni-date__x-input {
			margin-right: 30rpx !important;
			color: #000000;
		}

		/deep/.range-separator {
			margin-right: 30rpx !important;
		}

		.choose_time {
			position: relative;
			width: 85%;

			.time-img {
				position: absolute;
				left: 60rpx;
				z-index: 99;
				width: 30rpx;
				height: 28rpx;
			}
		}

		.icon {
			position: absolute;
			right: 55rpx;
		}

		// 学员信息
		.studentbox {
			line-height: 60rpx;
			width: 635rpx;
			margin-top: 30rpx;
			padding: 30rpx;
			background-color: #fff;

			.stutext {
				display: flex;
				justify-content: space-between;

				.hour {
					font-size: 30rpx;
					font-family: AlibabaPuHuiTiM;
					color: #E57126;
					font-weight: bold;
				}

				.rightbtn {
					border-radius: 4rpx;
					font-size: 26rpx;
					font-family: AlibabaPuHuiTiR;
					color: #FFFFFF;

				}

				.success {
					background: #2DC032;
				}

				.have {
					background: #E57126;
				}

				.fail {
					background: #C6C6C6;
				}
			}
		}

		.fei {
			font-size: 30rpx;
			font-family: AlibabaPuHuiTiR;
			color: #666666;
		}

		// 暂无数据
		.None {
			margin-top: 30rpx;
			width: 694rpx;
			height: 1225rpx;
			background: #fff;
			border-radius: 14rpx;
			font-size: 30rpx;
			font-family: SourceHanSansSC-Regular, SourceHanSansSC;
			color: #BDBDBD;
			display: flex;
			justify-content: center;
			align-items: center;

			.Nonecontent {
				text-align: center;

				.None-img {
					width: 136rpx;
					height: 136rpx;
					margin-bottom: 30rpx;
				}

			}
		}

		// 查询范围不可超过一年提示框
		.save {
			position: fixed;
			top: 0;
			left: 50%;
			transform: translateX(-50%);
			width: 623rpx;
			height: 100rpx;
			line-height: 100rpx;
			display: flex;
			justify-content: center;
			align-items: center;
			background: #FFFFFF;
			box-shadow: 1rpx -2rpx 20rpx 1rpx rgba(0, 0, 0, 0.12);
			border-radius: 50rpx;

			.save-img {
				width: 38rpx;
				height: 38rpx;
				margin-right: 17rpx;
			}

			.save-text {
				font-size: 34rpx;
				font-family: AlibabaPuHuiTiR;
				color: #161616;
			}

		}


	}
</style>