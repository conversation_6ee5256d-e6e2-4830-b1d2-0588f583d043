<template>
  <view class="wrap">
    <view>
      <view class="course_banner">
        <view class="banner-item">
          <h2 class="number">{{ courseData.learnedMustCount }}</h2>
          <view class="content-text">已学习课程数(必修)</view>
        </view>
        <view class="banner-item">
          <h2 class="number">{{ courseData.learnedNotMustCount }}</h2>
          <view class="content-text">已学习课程数(选修)</view>
        </view>
      </view>
      <view class="course_banner">
        <view class="banner-item">
          <h2 class="number">{{ courseData.completedExamCount }}</h2>
          <view class="content-text">已完成试卷数</view>
        </view>
        <view class="banner-item">
          <h2 class="number">{{ courseData.notCompletedExamCount }}</h2>
          <view class="content-text">未完成试卷数</view>
        </view>
      </view>
    </view>
    <view style="height: 80rpx; background-color: #ffffff; margin-top: 10rpx">
      <u-sticky>
        <!-- :class="showScroll?'tabs_css_content':''" -->
        <view>
          <u-tabs
            :list="listTabs"
            lineWidth="0"
            lineHeight="0"
            :activeStyle="{ color: '#333333', fontWeight: 'bold', fontSize: '28rpx' }"
            :inactiveStyle="{
              color: '#5A5A5A',
              transform: 'scale(1)',
              fontSize: '28rpx'
            }"
            itemStyle="padding-left:5px; padding-right: 25px; height: 34px;"
            :lineColor="`url(${lineBg}) 100% 110%`"
            @click="tabsCourse"
          ></u-tabs>
        </view>
      </u-sticky>
    </view>
    <view class="course_list">
      <scroll-view>
        <view class="course_title">
          <h2 class="course_coach">{{ listTabs[itemIndex].dictLabel }}</h2>
          <view v-if="listTabs.length" class="course_num">
            共
            <text style="color: #348970">{{ listTabs[itemIndex].courseCount }}</text>
            个课程
          </view>
        </view>
        <view class="noData" v-if="listTabs.length === 0">暂无数据</view>
        <view v-if="listTabs.length" class="course_advise">
          <uni-icons color="#FDA743" type="star-filled" size="10"></uni-icons>
          {{ listTabs[itemIndex].categoryDescription }}
        </view>
        <view style="margin-top: 20rpx; padding-bottom: 40rpx">
          <u-tabs
            :current="tabsCurrent"
            :list="listTabs[itemIndex].childList"
            keyName="dictLabel"
            lineWidth="0"
            lineHeight="0"
            :activeStyle="{ color: '#333333', fontWeight: 'bold', fontSize: '28rpx' }"
            :inactiveStyle="{
              color: '#5A5A5A',
              transform: 'scale(1)',
              fontSize: '28rpx'
            }"
            itemStyle="padding-left:5px; padding-right: 25px; height: 34px;"
            :lineColor="`url(${lineBg}) 100% 110%`"
            @click="tabsSecondClick"
          ></u-tabs>

          <view v-for="(v, i) in couseCategoryList" :key="i">
            <view class="course_tetx">
              <view class="line"></view>
              <h2>{{ v.name }}</h2>
            </view>
            <view style="display: flex; flex-wrap: wrap; justify-content: space-between; padding: 0 25rpx">
              <view class="course_listImg" v-for="(course, courseIdx) in v.courseList" :key="courseIdx">
                <view class="course_listImg_item" @tap="chooseCourse(course)">
                  <image :src="course.courseCover" mode="widthFix" class="ty_img" lazy-load></image>
                  <view :style="{ backgroundColor: course.isMustLearn === 0 ? '#fdb868' : '#348970' }" class="isMandatory">{{ course.isMustLearn === 0 ? '选修' : '必修' }}</view>
                  <h2 class="course_name">{{ course.courseName }}</h2>
                  <h2 class="course_status" :style="{ color: getStatusColor(course.learningStatus) }">
                    {{ course.learningStatus }}
                    <text class="classHour">共{{ course.lessonCount }}课时</text>
                  </h2>
                </view>

                <!-- <view class="course_listImg_item" @tap="chooseCourse">
									<image src="https://document.dxznjy.com/course/8a7107c6708a4e2fbc16a0941e5c4a50.jpg" class="ty_img" mode="widthFix"></image>
									<h2 class="course_name">工具包课程名称1</h2>
									<h2 class="course_status">进行中<text class="classHour">共2课时</text></h2>
								</view> -->
              </view>
            </view>
            <view class="noData" v-if="v.courseList.length === 0">暂无数据</view>
            <!-- <u-empty v-if="!couseCategoryList.length" mode="data"
							icon="http://cdn.uviewui.com/uview/empty/car.png">
						</u-empty> -->
          </view>
        </view>

        <!-- 1111 -->
      </scroll-view>
    </view>
  </view>
</template>

<script>
  const { $http } = require('@/util/methods.js');

  export default {
    data() {
      return {
        lineBg: 'https://document.dxznjy.com/course/01c96465d9ea46f69aa97465b1201aef.png',
        listTabs: [],

        userCode: '',
        userInfo: {},
        // 课程数据
        courseData: {},
        // 课程分类
        courseCategoryList: [],
        itemIndex: 0,
        someCondition: false,
        listTabsData: [
          {
            name: '入门篇'
          },
          {
            name: '基础篇'
          },
          {
            name: '进阶篇'
          },
          {
            name: '高手篇'
          }
        ],
        tabsCurrent: 0,
        courseList: [],
        // curFirstIndex: 0,
        curSecondIndex: 0,
        couseCategoryList: [],
        errorImageUrl: 'https://document.dxznjy.com/course/857d24a8adcf435badd2c48fb9bd0477.png' // 图片加载失败占位图
      };
    },
    async onLoad(option) {
      console.log(option, '------');
      console.log('第一步');
      // this.someCondition = true
      this.userCode = option.userCode;
      // await this.getPaperUserInfo()

      // await this.getUserCourseInfo()

      // await this.getCourseCategory()
    },
    async onShow() {
      console.log('第二步');
      // await this.getUserCourseInfo()
      // await this.getPaperUserInfo();

      await this.getUserCourseInfo();
      await this.getCourseCategory();
      // if (this.someCondition) {
      // 	await this.getCourseCategory()
      // 	this.someCondition = false
      // }
      // await this.getCourseCategory()
    },
    onHide() {
      this.someCondition = true;
    },
    methods: {
      async tabsSecondClick(item) {
        this.tabsCurrent = item.index;
        // this.categoryId = item.id;
        // this.getProduct(this.categoryId);

        // this.curSecondIndex = item.index;
        this.couseCategoryList = [];
        if (item.childList && item.childList.length) {
          // 显示加载提示
          // uni.showLoading({
          // 	title: '正在加载...',
          // 	mask: true,
          // })
          for (let i = 0; i < item.childList.length; i++) {
            const v = item.childList[i];
            uni.showLoading({
              title: '正在加载...',
              mask: true
            });
            const res = await $http({
              url: 'train/web/dict/coursePageByCategoryCode',
              data: {
                categoryCode: v.dictCode,
                userId: this.userInfo.bvAdminId,
                roleTag: this.userInfo.roleTag
              }
            });

            const tmpObj = {
              name: v.dictLabel,
              courseList: res.data
            };
            this.couseCategoryList = [...this.couseCategoryList, tmpObj];
            uni.hideLoading();
          }
        }
      },
      getStatusColor(status) {
        const MAP = {
          未开始: '#8F8F8F',
          进行中: '#348970',
          已学完: '#F35F05'
        };
        return MAP[status];
      },
      async tabsCourse({ index = 0, childList = [] }) {
        this.itemIndex = index;
        this.couseCategoryList = [];
        this.tabsCurrent = 0;
        if (childList?.length) {
          const curItems = childList[0];
          if (curItems && curItems?.childList?.length) {
            for (let i = 0; i < curItems?.childList.length; i++) {
              const v = curItems.childList[i];
              // 显示加载提示
              uni.showLoading({
                title: '正在加载...',
                mask: true
              });
              const res = await $http({
                url: 'train/web/dict/coursePageByCategoryCode',
                data: {
                  categoryCode: v.dictCode,
                  userId: this.userInfo.bvAdminId,
                  roleTag: this.userInfo.roleTag
                }
              });

              const tmpObj = {
                name: v.dictLabel,
                courseList: res.data
              };
              this.couseCategoryList = [...this.couseCategoryList, tmpObj];
              uni.hideLoading();
            }
          }
        }
      },
      // 获取公告用户id和角色信息
      async getPaperUserInfo() {
        const res = await $http({
          url: 'train/wap/token/getUserInfo',
          data: {
            merchantCode: this.userCode
          }
        });
        console.log(res, '获取公告用户id和角色信息---------');

        this.userInfo = res.data;
      },
      // 查询角色相关数据
      async getUserCourseInfo() {
        console.log(this.userInfo, 'this.userInfo');

        const res = await $http({
          url: 'train/web/exam/course/getUserCourseInfo',
          data: {
            userId: this.userInfo.bvAdminId,
            roleTag: this.userInfo.roleTag
          }
        });
        uni.setStorageSync('bvAdminId', this.userInfo.bvAdminId);
        uni.setStorageSync('roleTag', this.userInfo.roleTag);
        this.courseData = res.data;
        console.log('查询角色相关数据');
      },
      // 查询课程分类
      async getCourseCategory() {
        const { data } = await $http({
          url: 'train/web/dict/queryCourseCategory',
          data: {
            roleTag: this.userInfo.roleTag
          }
        });
        this.listTabs = data.map((item) => {
          return {
            name: item.dictLabel,
            ...item
          };
        });
        console.log(data, '课程分类');
        const tmpItems = data[this.itemIndex]?.childList || [];
        const secTmpItems = tmpItems[this.tabsCurrent]?.childList || [];
        // console.log(secTmpItems, 'secTmpItems');

        if (secTmpItems?.length) {
          // 显示加载提示
          // uni.showLoading({
          // 	title: '正在加载...',
          // 	mask: true,
          // })
          this.couseCategoryList = [];
          for (let i = 0; i < secTmpItems?.length; i++) {
            const v = secTmpItems[i];
            // console.log(v, 'vvvvv');
            // uni.setStorageSync('categoryCode', v.dictCode);
            // console.log(uni.getStorageSync("categoryCode"), 'uni.getStorageSync("categoryCode")');

            uni.showLoading({
              title: '正在加载...',
              mask: true
            });
            const res = await $http({
              url: 'train/web/dict/coursePageByCategoryCode',
              data: {
                // categoryCode: uni.getStorageSync("categoryCode") || '',
                // categoryCode: v.dictCode,
                categoryCode: v.dictCode,
                userId: this.userInfo.bvAdminId,
                roleTag: this.userInfo.roleTag
              }
            });

            console.log(res.data, 'res---data---------');

            const tmpObj = {
              name: v.dictLabel,
              courseList: [...res.data]
            };
            // this.couseCategoryList = []
            this.couseCategoryList = [...this.couseCategoryList, tmpObj];
            // if (this.someCondition) {
            // 	this.couseCategoryList.pop()
            // }

            // const uniqueList = [...new Set([...this.couseCategoryList, tmpObj])];
            // this.couseCategoryList = uniqueList;
            // console.log(tmpObj, 'tmpObj');

            console.log(this.couseCategoryList, '重复数据吗');
            uni.hideLoading();
          }
        }
      },
      async chooseCourse(item) {
        console.log(item.categoryCode, 'item---------');
        // const res = await $http({
        // 	url: 'paper/web/exam/training/course/detail',
        // 	data: {
        // 		userId: uni.getStorageSync('bvAdminId') ? uni.getStorageSync('bvAdminId') : '',
        // 		roleTag: uni.getStorageSync('roleTag') ? uni.getStorageSync('roleTag') : '',
        // 		courseId: item.courseId
        // 		// courseId:'1296526404959866880'
        // 	}
        // })
        if (!item.isToLearn) return uni.showToast({ title: '没有权限查看该课程', icon: 'none' });
        // this.courseDetails = res.data

        // console.log(res, '课程详情');
        // this.someCondition = true
        uni.navigateTo({
          url: `/growth/centerList/courseDetails?courseId=${item.courseId}`
        });
      }
    }
  };
</script>

<style scoped>
  .wrap {
    padding: 0 20rpx;
  }

  .course_banner {
    background-color: #fcfbfc;
    padding: 30rpx;
    display: flex;
    justify-content: space-around;
    flex-wrap: wrap;
    text-align: center;
    /* flex-direction: column;
	justify-content: space-around; */
  }

  .number {
    color: #3a8e76;
    font-size: 40rpx;
  }

  .content-text {
    color: #8f8f8f;
    font-size: 24rpx;
  }

  .banner-item {
    /* flex: 1; */
  }

  .course_list {
    width: 100%;
    height: 100%;
    border-radius: 30rpx;
    margin-top: 15rpx;
    /* padding: 0 20rpx; */
    margin-top: 40rpx;
    /* background-color: pink; */
  }

  .course_title {
    display: flex;
    align-items: center;
    background-color: #f4fbf8;
  }

  .course_coach {
    color: #333333;
    font-size: 30rpx;
    font-weight: bold;
  }

  .course_num {
    margin-left: 17rpx;
    color: #585858;
  }

  .course_advise {
    color: #b6b9b7;
    font-size: 24rpx;
    margin-top: 16rpx;
    background-color: #f4fbf8;
  }

  .line {
    width: 6rpx;
    height: 20rpx;
    background-color: #348970;
    margin-right: 10rpx;
  }

  .course_tetx {
    display: flex;
    align-items: center;
    margin-top: 50rpx;
  }

  .course_listImg {
    display: flex;
    margin-top: 15rpx;
    justify-content: space-around;
    /* padding: 0 20rpx; */
  }

  .course_listImg_item {
    /* width: 100%; */
    position: relative;
    width: 296rpx;
    /* height: 296rpx; */
    display: flex;
    flex-direction: column;
  }

  .ty_img {
    width: 100%;
    height: 200rpx;
    max-height: 300rpx !important;
    /* object-fit: contain; */
  }

  .course_name {
    margin-top: 15rpx;
    color: #555555;
    font-size: 28rpx;
    /* white-space: nowrap; */
  }

  .course_status {
    color: #348970;
    font-size: 28rpx;
    margin-top: 25rpx;
  }

  .classHour {
    color: #8f8f8f;
    font-size: 24rpx;
    margin-left: 13rpx;
  }

  .isMandatory {
    position: absolute;
    display: flex;
    justify-content: center;
    align-items: center;
    right: 0;
    width: 80rpx;
    height: 40rpx;
    background: #fdb868;
    border-radius: 0rpx 8rpx 0rpx 8rpx;
    color: #ffffff;
    font-size: 24rpx;
  }

  .noData {
    margin: 100rpx auto;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #c0c4cc;
  }
</style>
