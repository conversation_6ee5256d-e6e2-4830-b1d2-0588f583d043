function formatTime(mdate, fmt) {
	if (fmt == undefined) {
		fmt = 'yyyy-MM-dd hh:mm:ss'
	}
	var date = new Date(mdate);
	if (/(y+)/.test(fmt)) {
		fmt = fmt.replace(RegExp.$1, (date.getFullYear() + '').substr(4 - RegExp.$1.length));
	}
	let o = {
		'M+': date.getMonth() + 1,
		'd+': date.getDate(),
		'h+': date.getHours(),
		'm+': date.getMinutes(),
		's+': date.getSeconds()
	};
	for (let k in o) {
		if (new RegExp(`(${k})`).test(fmt)) {
			let str = o[k] + '';
			fmt = fmt.replace(RegExp.$1, (RegExp.$1.length === 1) ? str : ('00' + str).substr(str.length));
		}
	}
	return fmt;
}

function queryUserRole(phone){
    this.$httpUser.get('znyy/bvadmin/getSelect/roleTag/'+phone, {
    }).then((res) => {
    	console.log(res)
    	
    });
}

function formatDate(mdate) {
	mdate = mdate.replace("-", "/").replace("-", "/").replace("T", " ").substr(0, 19);
	if (mdate.indexOf('1970') > -1) {
		mdate = '';
	}
	return mdate;
}

function dateFormat(time) {
	if (time != null && time != "") {
		var time = time.replace("T", " ");
		var time_1 = time.substr(0, 19);
		if (time_1.indexOf('1970') > -1) {
			time_1 = '';
		}
		return time_1;
	}
	return "";
}

function formatLocation(longitude, latitude) {
	if (typeof longitude === 'string' && typeof latitude === 'string') {
		longitude = parseFloat(longitude)
		latitude = parseFloat(latitude)
	}

	longitude = longitude.toFixed(2)
	latitude = latitude.toFixed(2)

	return {
		longitude: longitude.toString().split('.'),
		latitude: latitude.toString().split('.')
	}
}



var dateUtils = {
	UNITS: {
		'年': 31557600000,
		'月': 2629800000,
		'天': 86400000,
		'小时': 3600000,
		'分钟': 60000,
		'秒': 1000
	},
	humanize: function(milliseconds) {
		var humanize = '';
		for (var key in this.UNITS) {
			if (milliseconds >= this.UNITS[key]) {
				humanize = Math.floor(milliseconds / this.UNITS[key]) + key + '前';
				break;
			}
		}
		return humanize || '刚刚';
	},
	format: function(dateStr) {
		var date = this.parse(dateStr)
		var diff = Date.now() - date.getTime();
		if (diff < this.UNITS['天']) {
			return this.humanize(diff);
		}
		var _format = function(number) {
			return (number < 10 ? ('0' + number) : number);
		};
		return date.getFullYear() + '/' + _format(date.getMonth() + 1) + '/' + _format(date.getDay()) + '-' +
			_format(date.getHours()) + ':' + _format(date.getMinutes());
	},
	parse: function(str) { //将"yyyy-mm-dd HH:MM:ss"格式的字符串，转化为一个Date对象
		var a = str.split(/[^0-9]/);
		return new Date(a[0], a[1] - 1, a[2], a[3], a[4], a[5]);
	}
};

function checkNetwork() {
	uni.getNetworkType({
		success: function(res) {
			if (res.networkType == 'none') {
				uni.showToast({
					icon: 'none',
					title: '咦？网去哪了',
					duration: 5000
				});
			}
		}
	});
}

function isWeixn() {
	var ua = navigator.userAgent.toLowerCase();
	if (ua.match(/MicroMessenger/i) == "micromessenger") {
		return true;
	} else {
		return false;
	}
}
// 保留两位小数
function returnFloat(value) {
	var value = Math.round(parseFloat(value) * 100) / 100;
	var xsd = value.toString().split(".");
	if (xsd.length == 1) {
		value = value.toString() + ".00";
		return value;
	}
	if (xsd.length > 1) {
		if (xsd[1].length < 2) {
			value = value.toString() + "0";
		}
		return value;
	}
}

function downLoadImage(that, image) {

	//#ifndef H5

	uni.downloadFile({
		url: image,
		success: (result) => {
			if (result.statusCode === 200) {
				var img = result.tempFilePath;
				uni.getSetting({
					success(res) {
						if (res && res.authSetting && res.authSetting.hasOwnProperty('scope.writePhotosAlbum')) {
							if (res.authSetting['scope.writePhotosAlbum']) {
								downImg(that, img);
							} else { // 拒绝授权，打开授权设置
								uni.openSetting({
									success() {
										downImg(that, img);
									}
								})
							}
						} else {
							downImg(that, img);
						}
					}
				})
			}
		}
	})


	//#endif

	//#ifdef H5
	uni.previewImage({
		urls: [image]
	});
	that.$util.alter('请使用截图功能')
	//#endif
}

function downImg(that, image) {
	uni.saveImageToPhotosAlbum({
		filePath: image,
		success: function() {
			that.$util.alter('海报已保存到相册')
		},
		fail: function() {
			that.$util.alter('保存失败，请稍后重试')
		}
	});
}

function countDown(bettime) {
	var sec = Math.floor(bettime / 1000);
	var day = Math.floor((Math.floor((Math.floor(sec / 60)) / 60)) / 24);
	var hours = (Math.floor((Math.floor(sec / 60)) / 60)) % 24;
	var minutes = (Math.floor(sec / 60)) % 60;
	var seconds = sec % 60;
	// if (hours.toString().length < 2) {
	// 	hours = "0" + hours;
	// }
	// if (minutes.toString().length < 2) {
	// 	minutes = "0" + minutes;
	// }
	// if (seconds.toString().length < 2) {
	// 	seconds = "0" + seconds;
	// }
	// if (day == 0) {
	// 	return hours + "时" + minutes + "分" + seconds + "秒";
	// } else {
	// 	return day + "天" + hours + "时" + minutes + "分" + seconds + "秒";
	// }
	return {
		day,
		hours,
		minutes,
		seconds
	};
}

function getWeekDay(mdate) {
	var date = new Date(mdate);
	var week = date.getDay();
	if (week == 1) {
		return "周一";
	} else if (week == 2) {
		return "周二";
	} else if (week == 3) {
		return "周三";
	} else if (week == 4) {
		return "周四";
	} else if (week == 5) {
		return "周五";
	} else if (week == 6) {
		return "周六";
	} else {
		return "周日";
	}
}

/**
 * 判断date1是否早于date2
 * **/
function dateCompare(date1, date2) {
	if (typeof date1 == "string") {
		date1 = new Date(date1.replace(/-/ig, "/"));
	}
	if (typeof date2 == "string") {
		date2 = new Date(date2.replace(/-/ig, "/"));
	}
	var stime = Date.parse(date1);
	var etime = Date.parse(date2);
	return stime <= etime ? true : false;
}

function sortNumber(a, b) { //升序
	return a - b
}

function getQueryValue(para, queryName) {
	var query = decodeURI(para);
	var vars = query.split("$");
	for (var i = 0; i < vars.length; i++) {
		var pair = vars[i].split("=");
		if (pair[0] == queryName) {
			return pair[1];
		}
	}
	return '';
}

function getWxCode(resolve, reject) {
	uni.login({
		provider: 'weixin',
		success: function(loginRes) {
			resolve(loginRes.code)
		},
		fail() {
			reject('fail');
		}
	});
}


function substr(title, limitLength) {
	if (title) {
		if (title.length <= limitLength) {
			return title;
		} else {
			return title.substring(0, limitLength) + "..."
		}
	}
}


function openAd(opentype, openurl) {
	if (opentype == "url") {
		uni.navigateTo({
			url: '/pages/index/web?url=' + encodeURIComponent(openurl)
		})
	} else if (opentype == "mini") {
		uni.navigateTo({
			url: openurl
		})
	}
}

module.exports = {
	formatTime: formatTime,
	formatDate: formatDate,
	dateFormat: dateFormat,
	formatLocation: formatLocation,
	dateUtils: dateUtils,
	checkNetwork: checkNetwork,
	returnFloat: returnFloat,
	isWeixn: isWeixn,
	downLoadImage: downLoadImage,
	countDown: countDown,
	dateCompare: dateCompare,
	sortNumber: sortNumber,
	getWeekDay: getWeekDay,
	getQueryValue: getQueryValue,
	getWxCode: getWxCode,
	substr: substr,
	openAd: openAd
}
