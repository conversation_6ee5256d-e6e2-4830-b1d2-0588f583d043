<template>
  <view class="">
    <web-view :src="reportUrl" v-if="!showDate"></web-view>
    <view v-if="showDate" class="curriculum_css_no pt-30 pb-55 f-28">
      <image class="curriculum_image" src="https://document.dxznjy.com/course/ac587707bf314badadb28a158852c77d.png"></image>
      <view class="c-66 f-24">暂无测评报告</view>
    </view>
  </view>
</template>

<script>
  export default {
    data() {
      return {
        userId: '',
        reportUrl: '',
        showDate: false
      };
    },
    onLoad(options) {
      this.userId = options.userId;
      this.fetchReport();
    },
    methods: {
      async fetchReport() {}
    }
  };
</script>

<style lang="scss" scoped>
  .curriculum_css_no {
    position: relative;
    width: 710rpx;
    margin: auto;
    text-align: center;
    .curriculum_image {
      width: 74rpx;
      height: 76rpx;
      display: block;
      margin: 16rpx auto;
    }
    .curriculum_title {
      text-align: left;
    }
  }
</style>
