<template>
  <view class="container">
    <u-navbar title="个人中心" bgColor="#f3f8fc" placeholder>
      <!-- @click="leftClick" -->
      <view class="" slot="left" @click="goBack">
        <u-icon name="arrow-left" size="35" bold color="#000"></u-icon>
      </view>
      <view class="u-nav-slot" slot="center">考试记录</view>
    </u-navbar>
    <view class="experience">
      <h2>{{ examDetailObj.examName }}</h2>
    </view>
    <view class="testPaper-description">
      {{
        `试卷说明：本试卷共${examDetailObj.examSingleNum ? examDetailObj.examSingleNum : 0}道单选题，${examDetailObj.examMultiNum ? examDetailObj.examMultiNum : 0}道多选题，${
          examDetailObj.examTofNum ? examDetailObj.examTofNum : 0
        }道判断题,满分${examDetailObj.examFullMarks ? examDetailObj.examFullMarks : 0},考试时间${examDetailObj.examTimeLimit ? examDetailObj.examTimeLimit : 0}分钟`
      }}
    </view>

    <view style="margin-top: 40rpx; margin-left: 30rpx" v-for="(item, index) in examDetailObj.questionList" :key="index">
      <!-- <uni-data-checkbox @change="changeValue"  :localdata="range" selectedColor="#000000"></uni-data-checkbox> -->
      <h2 class="radio-title">{{ index + 1 }}. [{{ formatStatus(item.questionType) }}]{{ item.questionName }}</h2>
      <u-radio-group disabled v-if="item.questionType == 1 || item.questionType == 3" size="30" labelSize="30" v-model="item.qusAnster" placement="column">
        <u-radio
          size="30"
          labelSize="30"
          activeColor="#40937b"
          v-for="(v, i) in item.optionList"
          :key="i"
          :label="`${v.questionOptionDescription}、${v.questionOptionContent}`"
          :name="v.userAnswerIsSelect"
        ></u-radio>
      </u-radio-group>
      <u-checkbox-group disabled v-if="item.questionType == 2" size="30" labelSize="30" v-model="item.qusAnster" placement="column">
        <u-checkbox
          size="30"
          labelSize="30"
          activeColor="#40937b"
          :customStyle="{ marginBottom: '8px' }"
          v-for="(v, i) in item.optionList"
          :key="i"
          :label="`${v.questionOptionDescription}、${v.questionOptionContent}`"
          :name="v.userAnswerIsSelect"
        ></u-checkbox>
      </u-checkbox-group>
      <view class="final-answer">
        <h2 style="color: #555555">结果</h2>
        <h2 :style="{ color: item.userAnswerIsCorrect ? '#6bae99' : '#f8676d' }" class="correctAnswer">回答{{ item.userAnswerIsCorrect ? '正确' : '错误' }}</h2>
      </view>
    </view>
  </view>
</template>

<script>
  const { $http } = require('@/util/methods.js');
  export default {
    name: 'Question',

    data() {
      return {
        radiovalue: '',
        examRecordId: '',

        examDetailObj: {},
        courseId: '',
        // 是否是考试记录跳进来的
        isDetail: ''
      };
    },

    mounted() {},
    computed: {},
    onLoad(option) {
      console.log(option, 'option');

      this.examRecordId = option.examRecordId;
      this.courseId = option.courseId;
      if (option.isDetail) {
        this.isDetail = option.isDetail;
      }
      this.examDetail();
    },
    methods: {
      formatStatus(status) {
        const MAP = {
          1: '单选',
          2: '多选',
          3: '判断'
        };
        return MAP[status];
      },
      radioChange(val) {
        console.log(val);
      },
      goBack() {
        console.log('哈哈哈');
        if (this.isDetail) {
          uni.navigateBack();
        } else {
          // uni.redirectTo({
          //     url: `/growth/centerList/courseDetails?courseId=${this.courseId}`
          // });
          uni.navigateBack({
            delta: 2
          });
        }

        // uni.reLaunch({
        //     url: '/pages/index/index'
        // });
      },
      async examDetail() {
        const res = await $http({
          url: 'train/web/exam/training/exam_record_detail',
          data: {
            examRecordId: this.examRecordId
            // courseId: '1296526404959866880',
            // userId: uni.getStorageSync("bvAdminId")
            //     ? uni.getStorageSync("bvAdminId")
            //     : "",
            // roleTag: uni.getStorageSync("roleTag")
            //     ? uni.getStorageSync("roleTag")
            //     : ""
          }
        });
        this.examDetailObj = res.data;
        this.examDetailObj.questionList.forEach((item) => {
          item.qusAnster = '';
        });
        this.examDetailObj.questionList.forEach((item) => {
          if (item.questionType === 1 || item.questionType == 3) {
            if (item.optionList.every((v) => !v.userAnswerIsSelect)) {
              return (item.qusAnster = '');
            }
            item.qusAnster = item.optionList.some((v) => v.userAnswerIsSelect);
          } else if (item.questionType === 2 && item.optionList.length) {
            item.qusAnster = item.optionList.filter((v) => v.userAnswerIsSelect).map((v) => v.userAnswerIsSelect);
          }
        });
        console.log(this.examDetailObj, '2222');
      }
    }
  };
</script>

<style>
  page {
    background-color: #ffffff;
  }
</style>

<style lang="scss" scoped>
  .experience {
    height: 88rpx;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #555555;
    font-size: 28rpx;
  }

  .testPaper-description {
    height: 120rpx;
    display: flex;
    justify-content: center;
    align-items: center;
    // flex-wrap: wrap;
    padding: 0 32rpx;
    color: #555555;
    font-size: 24rpx;
    background-color: #f5f8fa;
  }

  .radio-title {
    color: #333333;
    font-size: 28rpx;
    font-weight: bold;
  }

  ::v-deep .u-radio {
    margin-top: 36rpx !important;
  }

  .final-answer {
    display: flex;
    align-items: center;
    height: 82rpx;
    margin-top: 40rpx;
    background-color: #ecfaf3;
    font-size: 28rpx;
  }

  .correctAnswer {
    margin-left: 20rpx;
    color: #6bae99;
  }
</style>
