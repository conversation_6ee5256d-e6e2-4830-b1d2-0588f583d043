<template>
  <view class="u-waterfall">
    <view class="u-column">
      <slot name="left" :leftList="leftList"></slot>
    </view>
    <view class="u-column">
      <slot name="right" :rightList="rightList"></slot>
    </view>
  </view>
</template>

<script>
  export default {
    name: 'mettingWaterfall',
    props: {
      value: {
        type: Array,
        required: true,
        default: () => []
      },
      addTime: {
        type: [Number, String],
        default: 200
      },
      idKey: {
        type: String,
        default: 'id'
      }
    },
    data() {
      return {
        leftList: [],
        rightList: [],
        tempList: [],
        leftHeight: 0,
        rightHeight: 0
      };
    },
    watch: {
      copyFlowList(nVal, oVal) {
        let startIndex = Array.isArray(oVal) && oVal.length > 0 ? oVal.length : 0;
        this.tempList = this.tempList.concat(this.cloneData(nVal.slice(startIndex)));
        this.splitData();
      }
    },
    mounted() {
      this.tempList = this.cloneData(this.copyFlowList);
      this.splitData();
    },
    computed: {
      copyFlowList() {
        return this.cloneData(this.value);
      }
    },
    methods: {
      async splitData() {
        if (!this.tempList.length) return;
        let item = this.tempList[0];
        if (!item) return;

        let itemHeight = this.estimateHeight(item);

        if (this.leftHeight <= this.rightHeight) {
          this.leftList.push(item);
          this.leftHeight += itemHeight;
        } else {
          this.rightList.push(item);
          this.rightHeight += itemHeight;
        }

        this.tempList.splice(0, 1);

        if (this.tempList.length) {
          setTimeout(() => {
            this.splitData();
          }, this.addTime);
        }
      },
      estimateHeight(item) {
        if (item.width && item.height) {
          let scale = item.height / item.width;
          return 330 * scale + 100;
        } else {
          return 400;
        }
      },
      cloneData(data) {
        return JSON.parse(JSON.stringify(data));
      },
      clear() {
        this.leftList = [];
        this.rightList = [];
        this.leftHeight = 0;
        this.rightHeight = 0;
        this.$emit('input', []);
        this.tempList = [];
      },
      remove(id) {
        let index = -1;
        index = this.leftList.findIndex((val) => val[this.idKey] == id);
        if (index != -1) {
          this.leftList.splice(index, 1);
        } else {
          index = this.rightList.findIndex((val) => val[this.idKey] == id);
          if (index != -1) this.rightList.splice(index, 1);
        }
        index = this.value.findIndex((val) => val[this.idKey] == id);
        if (index != -1) this.$emit('input', this.value.splice(index, 1));
      },
      modify(id, key, value) {
        let index = -1;
        index = this.leftList.findIndex((val) => val[this.idKey] == id);
        if (index != -1) {
          this.leftList[index][key] = value;
        } else {
          index = this.rightList.findIndex((val) => val[this.idKey] == id);
          if (index != -1) this.rightList[index][key] = value;
        }
        index = this.value.findIndex((val) => val[this.idKey] == id);
        if (index != -1) {
          let data = this.cloneData(this.value);
          data[index][key] = value;
          this.$emit('input', data);
        }
      }
    }
  };
</script>

<style lang="scss" scoped>
  .u-waterfall {
    display: flex;
    flex-direction: row;
    align-items: flex-start;
    justify-content: space-between;
    gap: 20rpx;
  }

  .u-column {
    flex: 1;
    display: flex;
    flex-direction: column;
    height: auto;
  }

  .u-image {
    width: 100%;
  }
</style>
