<template>
  <view>
    <page-meta :page-style="'overflow:' + (rollShow ? 'hidden' : 'visible')"></page-meta>
    <view class="down-page">
      <view class="list" v-if="profileList.length > 0">
        <view class="list-item" :class="activeId == item.id ? 'active' : ''" v-for="item in profileList" :key="item.id" @tap="handleTap(item)">
          <text>{{ item.fileName }}</text>
        </view>
      </view>
      <view v-else :style="{ height: useHeight + 'rpx', marginTop: '40rpx' }" class="t-c flex-col radius-15">
        <image :src="imgHost + 'alading/correcting/no_data.png'" class="mb-20 img_s" mode="widthFix"></image>
        <view style="color: #bdbdbd">暂无数据</view>
      </view>
    </view>
  </view>
</template>

<script>
  const { $http, $showMsg } = require('@/util/methods.js');
  export default {
    data() {
      return {
        activeId: '',
        profileList: [],
        isBuy: false,
        // 文件是否打开
        openLoading: false,
        imgHost: getApp().globalData.imgsomeHost
      };
    },
    props: {
      onloadInfo: {
        type: Object,
        default: true
      }
    },
    computed: {
      id() {
        return this.onloadInfo?.id || '';
      }
    },
    // onLoad(options) {
    //   this.id = options.id;
    //   if (!uni.getStorageSync('token')) {
    //     uni.navigateTo({
    //       url: '/Personalcenter/login/login'
    //     });
    //     return;
    //   }
    // },
    onShow() {
      this.activeId = '';
      this.openLoading = false;
    },
    mounted() {
      this.getProfileList();
      this.getUserHasBuyGoods();
    },
    methods: {
      refreshData() {
        if (!uni.getStorageSync('token')) {
          uni.navigateTo({
            url: '/Personalcenter/login/login'
          });
          return;
        }
        this.activeId = '';
        this.openLoading = false;
      },
      handleTap(item) {
        let _this = this;
        if (!this.isBuy) {
          return $showMsg('您需要购买后才可进行查看/下载');
        }

        if (this.openLoading) {
          return $showMsg('文件打开中...');
        }

        this.activeId = item.id;
        this.openLoading = true;
        uni.downloadFile({
          url: item.url,
          success: function (res) {
            var filePath = res.tempFilePath;
            _this.openLoading = false;
            uni.openDocument({
              filePath: filePath,
              fileType: item.fileExt ? item.fileExt.split('.').pop() : 'pdf',
              showMenu: true,
              success: function (res) {
                _this.openLoading = false;
                console.log('打开文档成功');
              }
            });
          },
          fail: (err) => {
            this.openLoading = false;
            console.log(err, '下载失败');
          }
        });
      },
      // 是否购买过录播课
      async getUserHasBuyGoods() {
        const res = await $http({
          url: 'zx/wap/goods/isBuyGoods',
          data: {
            goodsId: this.id,
            userId: uni.getStorageSync('user_id') ? uni.getStorageSync('user_id') : ''
          }
        });
        this.isBuy = res.data;
      },
      async getProfileList() {
        const res = await $http({
          url: 'zx/wap/goods/learn/materials',
          data: {
            goodsId: this.id
          }
        });

        if (res) {
          this.profileList = res.data;
        }
      }
    }
  };
</script>

<style lang="scss" scoped>
  .down-page {
    padding: 32rpx;

    .list {
      width: 100%;
      padding-bottom: 130rpx;
    }

    .list-item {
      width: 100%;
      padding: 28rpx 32rpx;
      font-size: 28rpx;
      color: #555555;
      line-height: 40rpx;
      border-radius: 16rpx;
      border: 2rpx solid #efeff0;
      box-sizing: border-box;
      margin-bottom: 32rpx;

      &:last-child {
        margin-bottom: 0;
      }

      &.active {
        color: #46d49e;
        background: rgba(49, 207, 147, 0.1);
        border: 2rpx solid #31cf93;
      }
    }

    .img_s {
      width: 160rpx;
    }
  }
</style>
