<template>
    <view class="plr-30">
        <view class="p-30 flex_x list_bgc radius-15 c-66 f-30">
            级别：
            <view class="screenitem flex">
                <picker class="screenPicker" @change="bindPickerChange" range-key="name" :value="arrayIndex"
                    :range="array">
                    <view class="c-00">{{ array[arrayIndex].name || '成交状态'}}</view>
                </picker>
                <image  :src="imgHost+'dxSelect/fourthEdition/xia.png'" class="xiaimg"></image>
            </view>
        </view>
        <view v-if="listS.list != undefined && listS.list.length>0" class="list_bgc mt-30 plr-30 radius-15">
            <block v-for="(item,index) in listS.list" :key="index">
                <view class="flex">
                    <view class="box-100 radius-50">
                        <image :src="item.headPortrait" class="wh100"></image>
                    </view>
                    <view class="flex-box ml-30 ptb-30">
                        <view class="flex-dir-row">
                            <!-- <text class="f-28">{{ item.merchantName }}-</text> -->
                            <text class="f-32">{{ item.realName }}</text>
                            <view class="label">
                                <text class="f-26">等级{{ item.rankName }}</text>
                            </view>
                        </view>
                        <view class="list_flex mt-15 f-30">
                            <view>{{ item.phone }}</view>
                            <view class="f-26 c-99">{{ item.createdTime }}</view>
                        </view>
                    </view>
                </view>
            </block>
        </view>
        <view v-if="listS.list != undefined && listS.list.length==0" class="t-c flex-col"
            :style="{height: useHeight+'rpx'}">
            <!-- <image src="/static/cart/no_data.png" mode="widthFix" class="mb-20 img_s"></image> -->
            <image :src="imgHost+'alading/correcting/no_data.png'" class="mb-20 img_s" mode="widthFix"></image>
            <view style="color: #BDBDBD;">暂无数据</view>
        </view>
        <view v-if="no_more && listS.list != undefined && listS.list.length>0">
            <u-divider text="到底了"></u-divider>
        </view>
    </view>
</template>

<script>
    const {
        $navigationTo,
        $showError,
        $http,
    } = require("@/util/methods.js")
    export default {
        data() {
            return {
                arrayIndex: 0,
                hasDeal: -1,
                imgHost: getApp().globalData.imgsomeHost,
                array: [{
                    name: '全部',
                    value: ''
                }, {
                    name: '等级A',
                    value: 'A'
                }, {
                    name: '等级B',
                    value: 'B'
                }, {
                    name: '等级C',
                    value: 'C'
                }, {
                    name: '等级D',
                    value: 'D'
                }, {
                    name: '等级E',
                    value: 'E'
                }, {
                    name: '等级F',
                    value: 'F'
                }],
                rankName: '',
                listS: {},
                page: 1,
                no_more: false,
                useHeight: 0, //除头部之外高度
            }
        },
        onLoad() {

        },
        onShow() {
            this.list()
        },
        onReachBottom() {
            if (this.page >= this.listS.totalPage) {
                this.no_more = true
                return false;
            }
            this.list(true, ++this.page);
        },
        onReady() {
            uni.getSystemInfo({
                success: (res) => {
                    // 可使用窗口高度，将px转换rpx
                    let h = (res.windowHeight * (750 / res.windowWidth));
                    this.useHeight = h - 146;
                }
            })
        },
        methods: {
            // 渠道商等级筛选
            bindPickerChange(e) {
                let index = e.detail.value
                this.arrayIndex = index
                this.rankName = this.array[index].value
                console.log(this.arrayIndex)
                this.page = 1
                this.no_more = false
                this.list()
            },
            async list(isPage, page) {
                let _this = this
                const res = await $http({
                    url: 'zx/user/myMerchantList',
                    data: {
                        merchantId: '', //用户渠道商id
                        rankName: _this.rankName, //渠道商等级
                        page: page || 1
                    }
                })
                if (res) {
                    if (isPage) {
                        let old = _this.listS.list
                        _this.listS.list = [...old, ...res.data.list]
                    } else {
                        _this.listS = res.data
                    }
                }
            },
        }
    }
</script>

<style lang="scss">
    .flex_x {
        display: flex;
        align-items: center;
    }

    .list_flex {
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .label {
        background-image: linear-gradient(to right, #FEEFD5, #E7BD7B);
        padding: 0 20rpx;
        height: 40rpx;
        line-height: 40rpx;
        border-radius: 20rpx 0;
        color: #90621A;
        font-size: 24rpx;
        margin-left: 20rpx;
    }

    .list_bgc {
        background-color: #fff;
    }

    .screenitem {
		flex: 1;
        // width: 477rpx;
        height: 70rpx;
        border: 1rpx solid #C8C8C8;
        border-radius: 35rpx;
        line-height: 70rpx;
        padding: 0 30rpx;

        .screenPicker {
            flex: 1;
        }

        .xiaimg {
            width: 20rpx;
            height: 20rpx;
        }
    }

    .img_s {
        width: 160rpx;
    }
</style>