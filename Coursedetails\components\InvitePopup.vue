<template>
  <!-- 邀请拼团弹窗 -->
  <uni-popup ref="inviteGroupBuyingPopup" type="center" style="padding: 0">
    <view class="popup-container">
      <view class="popup-content">
        <view class="close-css" @click="closeDialog()"></view>
        <view class="content">
          <view :style="{ marginBottom: '32rpx' }">
            再邀请
            <!-- <text class="orange big">{{ groupOverNum - 1 > 0 ? groupOverNum - 1 : 0 }}</text> -->
            <text class="orange big">{{ showNumber }}</text>
            人 超值商品立马到手
          </view>
          <view :style="{ marginBottom: '8rpx' }">
            去
            <text class="orange">大群</text>
            邀约
          </view>
          <view>
            将提升
            <text class="orange">95%</text>
            拼团成功率哦！
          </view>
          <!-- #ifdef MP-WEIXIN -->
          <button open-type="share" :data-invite="true" @click="handleClose" class="invite" hover-class="none"></button>
          <!-- #endif -->
          <!-- #ifdef APP-PLUS -->
          <button @click="shareFriend" :data-invite="true" class="invite" hover-class="none"></button>
          <!-- #endif -->
        </view>
        <view class="footer">
          <view class="text">查看拼团订单：可前往“我的 > 我的拼团” 查看详情</view>
          <view class="btn-go" @click="handleGo"></view>
        </view>
      </view>
    </view>
  </uni-popup>
</template>

<script>
  const { $navigationTo, $http } = require('@/util/methods.js');
  export default {
    data() {
      return {
        inviteInfo: {}, // 邀请信息
        groupUserList: [], // 参团用户信息
        groupSize: 0,
        groupOverNum: 0,
        showNumber: 0 // 显示人数
      };
    },
    methods: {
      //app 分享
      shareFriend() {
        setTimeout(() => {
          this.handleClose();
        }, 10000);
        let shareInfo = {
          title: this.inviteInfo.groupText,
          imageUrl: this.inviteInfo.groupImg, //分享封面
          path: this.inviteInfo.groupPath
        };
        uni.$appShare(shareInfo, 2);
      },

      handleClose() {
        this.$refs.inviteGroupBuyingPopup.close();
      },
      //关闭弹窗
      closeDialog() {
        // 分享
        this.$refs.inviteGroupBuyingPopup.close();
        console.log('🚀 ~ closeDialog ~ this.inviteInfo:', this.inviteInfo);
        $navigationTo(`activity/groupBuying/detail?groupInstanceId=${this.inviteInfo.groupInstanceId}&groupFlag=${this.inviteInfo.groupFlag}`);
      },
      open(info) {
        this.inviteInfo = info;
        this.getGroupInstanceInfo();
      },
      async getGroupInstanceInfo() {
        const res = await $http({
          url: 'zx/wap/group/instances/single',
          method: 'get',
          data: {
            instancesId: this.inviteInfo.groupInstanceId
          }
        });

        if (res.data) {
          this.groupSize = res.data && res.data ? res.data.groupSize : 0;
          this.groupOverNum = res.data && res.data && res.data.groupOverNum > 0 ? res.data.groupOverNum : 0;
          // groupFlag 发起拼团团长 2、参与拼团用户  发起拼团不减1 参与拼团减1
          if (this.inviteInfo.groupFlag == 1) {
            this.showNumber = this.groupOverNum;
          } else {
            this.showNumber = this.groupOverNum - 1 >= 0 ? this.groupOverNum - 1 : 0;
          }
          this.groupUserList = res.data.piGroupParticipantsVos || [];
          this.$refs.inviteGroupBuyingPopup.open();
        }
      },
      handleGo() {
        this.$refs.inviteGroupBuyingPopup.close();
        $navigationTo('activity/groupBuying/myGroupBuying');
      }
    }
  };
</script>
<style lang="scss" scoped>
  .popup-content {
    position: relative;
    width: 694rpx;
    height: 640rpx;
    margin-bottom: 300rpx;
    background-image: url('https://document.dxznjy.com/dxSelect/8795e4d0-8116-42f5-823e-5a6a7d124f0c.png');
    background-size: 100% 100%;

    .close-css {
      position: absolute;
      top: -32rpx;
      right: 6rpx;
      width: 64rpx;
      height: 64rpx;
      background: url('https://document.dxznjy.com/dxSelect/9a407f93-744a-4173-8e69-1b4ba2963064.png');
      background-size: 100% 100%;
    }

    .content {
      text-align: center;
      font-size: 32rpx;
      color: #555555;
      line-height: 42rpx;
      padding: 232rpx 128rpx 0 122rpx;

      .big {
        font-size: 42rpx;
        padding: 0 8rpx;
      }

      .orange {
        font-family: AlibabaPuHuiTiBold;
        font-weight: bold;
        color: #e9a043;
      }

      .invite {
        margin: 48rpx auto 0;
        width: 440rpx;
        height: 80rpx;
        background-image: url('https://document.dxznjy.com/dxSelect/1760644b-b3d9-4790-882d-93cf777e9f8e.png');
        background-size: 100% 100%;
      }
    }

    .footer {
      position: absolute;
      width: 100%;
      bottom: -168rpx;
      margin: 0 auto;

      .text {
        font-family: AlibabaPuHuiTiBold;
        font-weight: bold;
        font-size: 28rpx;
        color: #ffffff;
        line-height: 42rpx;
      }

      .btn-go {
        margin: 24rpx auto 0;
        width: 278rpx;
        height: 80rpx;
        background: url('https://document.dxznjy.com/dxSelect/365e6deb-f43e-4f58-8c91-194048c2a3cd.png');
        background-size: 100% 100%;
      }
    }
  }
</style>
