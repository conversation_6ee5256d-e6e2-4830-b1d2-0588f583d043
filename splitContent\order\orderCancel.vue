<template>
  <view>
    <view class="cancel-page">
      <view class="d-flex direction-column align-items-center justify-center">
        <view class="mar-t">
          <u-icon
            name="checkmark-circle-fill"
            color="#2DC032"
            size="80"
          ></u-icon>
        </view>
        <view class="font-huge"> 订单已取消</view>
      </view>
      <view class="box d-flex direction-column mar-b-2 font-base">
        <text class="title font-large mar-b-2"
          >请选择取消订单原因，帮助我们改进
        </text>
        <!-- <radio-group name="radio" :value="cancelReasons" @change="changeReason">
          <label
            class="d-flex align-items-center justify-between mar-b-1 pad-b-1 border-bottom"
            v-for="value in cancelReasons"
            :key="value"
          >
            <text>{{ value }}</text>
            <radio :value="value" />
          </label>
          <label class="d-flex align-items-center justify-between">
            <text>其他</text>
            <radio :value="null" />
          </label>
        </radio-group> -->
        <view class="list">
          <view
            class="list-item"
            v-for="value in cancelReasons"
            :class="cancelReason === value && !showTextarea ? 'active' : ''"
            :key="value"
            @tap="handleTap(value)"
          >
            <text>{{ value }}</text>
          </view>
          <view
            class="list-item"
            :class="showTextarea ? 'active' : ''"
            @tap="handleTap('other')"
          >
            <text>其他</text>
          </view>
        </view>
        <textarea
          v-if="showTextarea"
          v-model="cancelReason"
          :placeholder="'请输入取消原因'"
          :auto-height="true"
          :auto-focus="showTextarea"
        />
      </view>
      <view class="footer-button">
        <button class="btn" :disabled="disabled" @tap="handleSubmit">
          提交
        </button>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      order: {},
      cancelReasons: [],
      vipCancelReasons: [
        "会员权益不明确，不清楚具体能获得什么",
        "觉得会员权益对我来说价值不大",
        "会员价格过高，超出我的预算",
        "没有足够的优惠活动，价格不划算",
        "对平台的信誉和服务质量有疑虑",
        "担心会员权益无法顺利兑现",
        "会员权益适用范围太窄，不满足我的需求",
        "会员期限与我的使用时间不匹配",
        "没有看到足够的会员成功案例，对效果存疑",
        "不清楚会员售后政策，不放心购买",
        "我的学习计划不确定，担心浪费",
        "没有长期使用平台的规划",
      ],
      classCancelReasons: [
        "课程内容与我的需求不匹配",
        "课程难度过高 / 过低",
        "对课程的效果存疑",
        "课程价格过高，超出预算",
        "没有优惠活动，觉得不划算",
        "对授课教师的水平不放心",
        "担心课程的教学方法不适合自己",
        "对平台的技术稳定性有疑虑（如课程播放卡顿等）",
        "担心售后服务不完善（如答疑不及时等）",
        "看到了关于课程或平台的负面评价",
        "对课程质量的承诺缺乏信心",
        "课程介绍不够详细，无法做出购买决策",
        "没有看到学员的学习成果展示，对课程效果不确定",
        "学习目标发生了改变，不再需要该课程",
      ],
      cancelReason: "",
      showTextarea: false,
      /** 课程订单 class 会员套餐订单 vip */
      cancelType: "",
      type: undefined
    };
  },
  computed: {
    disabled() {
      return !this.cancelReason;
    },
  },
  onLoad(q) {
    this.orderId = q.orderId;
    this.cancelType = q.cancelType;
    this.cancelReasons =
      q.cancelType == "vip" ? this.vipCancelReasons : this.classCancelReasons;
    this.type = q.type;
  },
  methods: {
    handleTap(val) {
      if (val === "other") {
        this.cancelReason = "";
        this.showTextarea = true;
      } else {
        this.showTextarea = false;
        this.cancelReason = val;
      }
    },
    changeReason({ detail }) {
      let cancelReason = detail.value;
      this.showTextarea = !cancelReason;
      if (!cancelReason) {
        cancelReason = "";
      }
      this.cancelReason = cancelReason;
    },
    handleSubmit() {
      return this.$httpUser
        .post(
          this.cancelType == "vip"
            ? "zx/order/mealOrderFeedBack"
            : "zx/wap/order/feedback",
          {
            orderId: this.orderId,
            cancelReason: this.cancelReason,
            cancelType: this.type
          }
        )
        .then((res) => {
          if (res.data.status == 1 || res.data.code == 20000) {
            uni.showToast({
              title: "取消订单成功",
              icon: "none",
              duration: 1000,
            });
            setTimeout(() => {
              uni.redirectTo({
                url: "/splitContent/order/order",
              });
            }, 1000);
          } else {
            uni.showToast({
              title: res.data.message,
              icon: "none",
              duration: 2000,
            });
          }
        });
    },
  },
};
</script>

<style lang="scss">
.d-flex {
  display: flex;
}
.direction-column {
  flex-direction: column;
}
.align-items-center {
  align-items: center;
}
.justify-between {
  justify-content: space-between;
}
.justify-center {
  justify-content: center;
}
.mar-b-1 {
  margin-bottom: 20rpx;
}
.mar-b-2 {
  margin-bottom: 30rpx;
}
.mar-t {
  margin-top: 66rpx;
}
.pad-b-1 {
  padding-bottom: 20rpx;
}
.font-weight {
  font-weight: bold;
}
.font-large {
  font-size: 28rpx;
  line-height: 40rpx;
}
.title {
  color: #489981;
}

.font-base {
  font-size: 30rpx;
}

.font-huge {
  font-size: 40rpx;
  line-height: 54rpx;
  margin-top: 24rpx;
}

page {
  height: 100%;
  // padding-bottom: 160rpx;
}

// 取消订单页面
.cancel-page {
  padding: 32rpx;
  .box {
    margin-top: 34rpx;
    padding: 26rpx 28rpx;
    background-color: #fff;
    border-radius: 4rpx;
  }
  textarea {
    width: 100%;
    min-height: 120rpx;
    margin-top: 30rpx;
  }

  .list-item {
    width: 100%;
    padding: 28rpx 32rpx;
    font-size: 28rpx;
    color: #555555;
    line-height: 40rpx;
    border-radius: 16rpx;
    border: 2rpx solid #efeff0;
    box-sizing: border-box;
    margin-bottom: 32rpx;
    &:last-child {
      margin-bottom: 0;
    }

    &.active {
      color: #46d49e;
      background: rgba(49, 207, 147, 0.1);
      border: 2rpx solid #31cf93;
    }
  }
}

.footer-button {
  // position: fixed;
  // bottom: 0;
  // left: 0;
  // right: 0;
  // padding-top: 10rpx;
  padding-bottom: 40rpx;
  box-sizing: border-box;

  .btn {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 24rpx 0;
    background: #428a6f;
    border-radius: 46rpx;
    font-size: 32rpx;
    line-height: 44rpx;
    color: #fff;
    box-sizing: border-box;
    min-width: 188rpx;
  }
}
</style>
