<template>
	<view>
		<view class="mymenber">
			<view class="list" v-for="(item, index) in myInvitelist" :key="index">
				<image src="/static/mrtx02.png" class="mrtx"></image>
				<view class="number">
					<view class="left">
						<view class="title">
							<image src="/static/icon.png"></image>
							<text>会员</text>
						</view>
						<text class="ml10  color-999">{{item.LoginName}}</text>
					</view>
					<view class="right">
						<text class="time color-999">{{dateFormat(item.AddTime)}}</text>
					</view>
				</view>			
			</view>
		</view>
		<uni-load-more :status="loadingType"></uni-load-more>
	</view>
</template>

<script>
import uniLoadMore from '@/components/uni-load-more/uni-load-more.vue';
import util from '@/utils/util.js';
export default {
	components: {
		uniLoadMore
	},
	data() {
		return {
			headerTop: '0px',
			loadingType: 'more', //加载更多状态
			myInvitelist: [1,2],
			pageindex: 1
		};
	},
	methods: {
		dateFormat: function(mdate, fmt) {
			return util.dateFormat(mdate, fmt);
		},
		async loadMyMember(type = 'add', loading) {
			if (type === 'add') {
				if (this.loadingType === 'nomore') {
					return;
				}
				this.loadingType = 'loading';
			} else {
				this.loadingType = 'more';
			}
			var mindex = this.pageindex;
			let result = await this.$http.post('Member/MyInvite', {
				pageIndex: mindex
			});
			if (type === 'refresh') {
				this.myInvitelist = [];
			}
			if (result) {
				if (mindex <= 1 && result.data.data.Items[0].length == 0) {
					this.myInvitelist = [];
					this.loadingType = 'nodata';
				} else {
					if (result.data.data.Items.length > 0 && result.data.data.Items[0].length > 0) {
						this.myInvitelist = this.myInvitelist.concat(result.data.data.Items[0]);
					}
					this.loadingType = this.pageindex >= result.data.data.TotalPages ? 'nomore' : 'more';
				}
				if (type === 'refresh') {
					if (loading == 1) {
						uni.hideLoading();
					} else {
						uni.stopPullDownRefresh();
					}
				}
			}
		}
	},
	onLoad(options) {
		this.loadMyMember();
	},
	//下拉刷新
	onPullDownRefresh() {
		this.pageindex = 1;
		this.loadMyMember('refresh');
	},
	//加载更多
	onReachBottom() {
		this.pageindex++;
		this.loadMyMember();
	}
};
</script>

<style>
.flex {
	flex-grow: 1;
}
.number {
	flex-grow: 1;
	display: flex;
	justify-content: space-between;
}
.right {
	text-align: right;
}
.color-999 {
	color: #999;
}
.title {
	margin-left: 20rpx;
	margin-right: 20rpx;
	font-size: 30rpx;
}
.ml10 {
	margin-left: 20rpx;
	margin-top: 6rpx;
	font-size: 28rpx;
	display: block;
}
.list {
	margin-left: 20rpx;
	margin-right: 20rpx;
	border-radius: 10rpx;
	background: #ffffff;
	padding-top: 30rpx;
	padding-bottom: 30rpx;
	margin-top: 10rpx;
	display: flex;
	justify-content: space-between;
	align-items: center;
	font-size: 28rpx;
}
.mrtx {
	width: 90rpx;
	height: 90rpx;
	margin-left: 20rpx;
}
.title image {
	width: 34rpx;
	height: 28rpx;
}
.time {
	margin-right: 20rpx;
	margin-top: 46rpx;
	font-size: 28rpx;
	display: block;
}
.list:nth-child(1) {
	margin-top: 20rpx;
}
</style>
