<template>
  <page-meta :page-style="'overflow:' + (rollShow ? 'hidden' : 'visible')"></page-meta>
  <view>
    <view class="col-12 header_css">
      <!-- <view class="col-12 personl_header_bg"></view>  parent-bgc-->
      <!-- <view class="icon_img" @click="skintap('pages/home/<USER>/index')">
                <uni-icons type="left" size="20" color="#000"></uni-icons>
            </view> -->
      <!-- <view class="page_title bold flex-col c-00">
                <text>家长中心</text>
            </view> -->
      <view class="home_bg" style="display: flex">
        <view class="ml-30 mr-20">
          <image :src="userinfo != null && userinfo.headPortrait ? userinfo.headPortrait : avaUrl" class="box-128 radius-all"></image>
        </view>
        <view class="center_css">
          <!-- 	<view class="nicknameElli f-32 c-ff mt-10 bold">
						{{ userinfo!=null&&userinfo.nickName?userinfo.nickName: "昵称"}}
					</view> -->
          <view v-if="userinfo != null && userinfo.nickName" class="nicknameElli f-32 c-ff mt-10">
            {{ userinfo != null && userinfo.nickName ? userinfo.nickName : '昵称' }}
          </view>
          <view v-else class="nicknameElli f-32 c-ff mt-10" @click="goToLogin">请登录</view>
          <view class="mt-8">
            <view class="f-30 identity_css radius-8 f-24" v-if="userinfo != null && userinfo.identityTypeName">
              {{ userinfo.identityTypeName }}
            </view>
            <!-- <button class="f-30 t-l " style="color: #E4E4E4;" hover-class="none" v-else
							open-type="getPhoneNumber" @getphonenumber="onGetPhoneNumber">授权手机号</button> -->
          </view>
        </view>
        <view class="flex-a-c content_right" @tap="getIntegral">
          <text class="c-ff f-24 integral_css">积分中心</text>
          <u-icon name="arrow-right" color="#555555" size="28"></u-icon>
        </view>
      </view>
      <!-- 	<view v-if="superCodeList.length>0" style="position: relative;">
				<swiper  class="content_main_css" :current="bannerIndex"  @change="changeBannerCurrent">
					<block class="" v-for="(item, index) in superCodeList"  :key="index">
						<swiper-item class="content_main_css_item bg-ff"  @click="goGrowTth(item)">
							<view class="flexbox">
								<view class="nicknameElli f-28 bold c-33">
								  {{ item.studentName}}
								</view>
								<view class="bold">
									<text class="c-33 f-28">所属门店：</text>
									<text class="f-24" style="color:#319579;">{{item.merchantCode||'--'}}</text>
								</view>
							</view>
							<view class="study_time">
								<view class="study_time_item">
									<view class="bold">
										<text class="f-40 green_css">{{item.studyDurationMinutesLastWeek.split(':')[0]||0}}</text>
										<text class="f-24">时</text>
										<text class="f-40 green_css">{{item.studyDurationMinutesLastWeek.split(':')[1]||0}}</text>
										<text class="f-24">分</text>
									</view>
									<view class="f-24 c-55 mt-8">本周学习</view>
								</view>
								<view class="study_time_item">
									<view class="bold">
										<text class="f-40 green_css">{{item.studyDurationMinutesToday.split(':')[0]||0}}</text>
										<text class="f-24">时</text>
										<text class="f-40 green_css">{{item.studyDurationMinutesToday.split(':')[1]||0}}</text>
										<text class="f-24">分</text>
									</view>
									<view class="f-24 c-55 mt-8">今日学习</view>
								</view>
								<view class="study_time_item">
									<view class="bold">
										<text class="f-40 green_css">{{item.studyDurationMinutesTotal.split(':')[0]||0}}</text>
										<text class="f-24">时</text>
										<text class="f-40 green_css">{{item.studyDurationMinutesTotal.split(':')[1]||0}}</text>
										<text class="f-24">分</text>
									</view>
									<view class="f-24 c-55 mt-8">总学习时长</view>
								</view>
							</view>
						</swiper-item>
					</block>
				</swiper>
				<view v-if="superCodeList.length>1">
					<view class="banner_indicator_style">
						<view v-for="(item,index) in superCodeList" :style="'width:'+48/superCodeList.length+'rpx'" :key="index" :class="['indicator_style_css ',bannerIndex==index?'active_current':'']"></view>
					</view>
				</view>
			</view>
			<view class="empty-content bg-ff content_main_css pb-20" v-else>
				<view class="flexbox" style="width:636rpx;">
					<view v-if="userinfo!=null&&userinfo.nickName" class="nicknameElli f-28 bold c-33">
					  {{ userinfo!=null&&userinfo.nickName?userinfo.nickName: "昵称"}}
					</view>
					<view class="bold">
						<text class="c-33 f-28">所属门店：</text>
						<text class="f-24" style="color:#319579;">-</text>
					</view>
				</view>
				<view class="no_study_time mt-24 pt-8 pb-12">
					<image src="https://document.dxznjy.com/course/3fcd609790e445208bd9262b0e0583b4.png" class="empty-img"></image>
					<view style="color: #BDBDBD;margin-top: 20rpx;">
						暂无数据
				</view>
			</view>
		</view> -->
      <view class="home_con mb-25">
        <view class="bg-ff radius-15 course-img mb-30">
          <view class="plan" v-if="studentShow && info && info.length > 0">
            <uni-swiper-dot @clickItem="clickItem" :info="info" :current="current" mode="dot" field="content" :dotsStyles="dotsStyles" style="height: 240rpx">
              <swiper style="height: 400rpx" @change="change" :current="swiperDotIndex">
                <swiper-item v-for="(item, index) in info" :key="index">
                  <view class="title">
                    <view class="title-name">{{ name }}</view>
                    <view class="title-merchant">门店编号：{{ studentFormMerchant }}</view>
                    <view class="title-line"></view>
                  </view>
                  <view class="hour">
                    <view class="time">
                      <text>已用学时：{{ useHours === '0.00' ? 0 : useHours || 0 }} 时</text>
                      <text style="float: right">剩余学时：{{ haveHours === '0.00' ? 0 : haveHours || 0 }} 时</text>
                    </view>
                    <u-line-progress class="progress_bar" :percentage="percentage" :showText="false" activeColor="#378771" height="20" inactiveColor="#E4E4E4"></u-line-progress>
                  </view>
                </swiper-item>
              </swiper>
            </uni-swiper-dot>
          </view>
          <view class="empty-content" v-else>
            <image :src="imgHost + 'dxSelect/lake_page.png'" class="empty-img"></image>
            <view style="color: #bdbdbd; margin-top: 20rpx">暂无数据，请先在我的学员里添加学员</view>
          </view>
        </view>
        <!-- 我的课程 -->
        <mycurriculum ref="mycurriculum"></mycurriculum>
        <!-- 		<view class="mb-30 plr-30">
				<view class="plr-30 pt-30 bg-ff radius-20">
					<view class="f-32 flex">
						<view class="fontWeight">我的课程</view>
						<view class="flex-c c-99" @click="skintap('Personalcenter/my/myCourse')"
							v-if="myClassList.nextStudyCourse!= null">
							<text class="mr-8">查看更多</text>
							<uni-icons class="mt-8" type="right" size="20" color="#999"></uni-icons>
						</view>
					</view>
					<view class="">
						<view class="m-b">
							<view class="card-content">
								<view class="f-3">
									<view class="myClassList flex_s">
										<view class="col-4">课程内容：</view>
										<view class="col-4 t-c">{{myClassList.nextStudyCourse!= null?"鼎英语":"-"}}</view>
										<view
											v-if="JSON.stringify(myClassList.nextStudyCourse) != '{}' && myClassList.nextStudyCourse!= null">
											<view class="col-4 t-r" style="width: 210rpx;"
												v-if="!myClassList.nextStudyCourse.experience">
												{{myClassList.nextStudyCourse.type == 1 ? '正式课程' : '复习课程' || ''}}
											</view>
											<view class="col-4 t-r" style="width: 210rpx;" v-else>
												<image :src="imgHost+'dxSelect/shike.png'" class="shike-img"></image>
												<text>试课课程</text>
											</view>
										</view>
										<view class="col-4 t-r" style="width: 210rpx;" v-else>-</view>
									</view>

									<view class="myClassList flex-s border-t">
										<view class="">助教老师</view>
										<view>{{myClassList.nextStudyCourse.teacher || '-'}}</view>
									</view>
									<view class="myClassList flex-s border-t">
										<view class="">上课时间</view>
										<view class="" v-if="myClassList.nextStudyCourse!= null">
											{{myClassList.nextStudyCourse.date || ''}}
											{{myClassList.nextStudyCourse.startTime || ''}}-{{myClassList.nextStudyCourse.endTime || ''}}
										</view>
										<view v-else>-</view>
									</view>
								</view>
								<view class="mb-40 t-r flex-c flex-x-e" v-if="myClassList.nextStudyCourse!= null">
									<button v-if="checkNextTime(myClassList.nextStudyCourse.dateTime)"
										@click="gotoNextleave()" class="btn btn_orange f-32" type="default"
										plain="true">请假</button>
									<button v-else @click="goNextback()" class="btn btn_orange f-32" type="default"
										plain="true">查看反馈</button>
								</view>
							</view>
						</view>
					</view>
				</view> -->
      </view>

      <view class="f-28 c-66 plr-30">
        <view class="bg-ff radius-15 pt-40" v-if="showTabDetail">
          <view class="study_service mb-40 bold f-28 c-33">学习服务</view>
          <view class="templateItem">
            <view class="template" @click="goMystudent">
              <image src="https://document.dxznjy.com/course/ba3f120bcc4b4b659b029b1979102f1d.png" class="w44"></image>
              <view>我的学员</view>
            </view>
            <view class="template" @click="skintap('Trialclass/trialreport?type=1', '试课报告')">
              <image src="https://document.dxznjy.com/course/d54aad37f659469c8ac57d53dc403918.png" class="w44"></image>
              <view>试课报告</view>
            </view>
            <view class="template" @click="skintap('Personalcenter/studyPrint/studyPrint', '学习内容打印')">
              <image src="https://document.dxznjy.com/course/8bee9f01c0574e32b4700095be8c374f.png" class="w44"></image>
              <view>学习内容打印</view>
            </view>
          </view>
          <view class="templateItem bg-ff radius-15">
            <view class="template" @click="showList(1, '抗遗忘复习')">
              <image src="https://document.dxznjy.com/course/a372ed9912594b79b980a20c89562ec8.png" class="w44"></image>
              <view>抗遗忘复习</view>
            </view>
            <!--   <view class="template" @click="forget()">
              <image src="https://document.dxznjy.com/course/cfd4e229977643f1a2fc6fcdb54f778f.png" class="w44"></image>
              <view>21天抗遗忘</view>
            </view> -->
            <view class="template" @click="showList(3, '趣味复习')">
              <image src="https://document.dxznjy.com/course/6ca5eea3037344f1a7319cba55da0bc4.png" class="w44"></image>
              <view>趣味复习</view>
            </view>
            <view class="template" @click="showList(2, '检测报告查询')">
              <image src="https://document.dxznjy.com/course/5a2c847d6fd34d8d81e11e2c9b31d507.png" class="w44"></image>
              <view>检测报告查询</view>
            </view>
          </view>
          <view class="templateItem bg-ff radius-15">
            <!--    <view class="template" @click="gotoDictationReport">
              <image :src="imgHost + 'dxSelect/fourthEdition/icon_chlbg.png'" class="w44"></image>
              <view>听写检测报告</view>
            </view> -->
            <view class="template" @click="gotoCourseDetail">
              <image src="https://document.dxznjy.com/course/7d53c96e7f404f429a49eb1f06f4e4e6.png" class="w44"></image>
              <view>学时详情</view>
            </view>
            <view class="template" @click="skintap('Personalcenter/my/pronunciationList?memberId=' + userinfo.userCode, '发音设置')">
              <image src="https://document.dxznjy.com/course/9744d4300f314f1c9d4b7ad3a4eae166.png" class="w44"></image>
              <view>发音设置</view>
            </view>
            <!--   <view class="template">
           
              <view @click="selectPerson(1)">
                <image src="https://document.dxznjy.com/course/d776350ca71e4ffb8093107db8a95d1f.png" class="w44"></image>
                <view>拼音法抗遗忘</view>
              </view>
            </view> -->
            <view class="template" @click="skintap('Personalcenter/my/myCourseList', '鼎英语课程表')">
              <image src="https://document.dxznjy.com/automation/1726800126000" class="w44"></image>
              <view>鼎英语课程表</view>
            </view>
            <view class="template" @click="wrongBook()">
              <image :src="imgHost + 'dxSelect/fourthEdition/parent-kyw.png'" class="w44"></image>
              <view>错题本</view>
            </view>
            <!--   <view class="template" @click="selectPerson(2)">
              <image :src="imgHost + 'course/0cd569990fde4a24a01b49a6e7f0ade5.png'" class="w44"></image>
              <view>新阅读理解抗遗忘</view>
            </view> -->
            <view class="template" @click="gotoAiIntelligentWords">
              <image src="/static/ai/aiIcon.png" class="w44"></image>
              <view>AI智阅</view>
            </view>
            <view class="template" @click="skintap('Listen/index', '学习记录')">
              <image src="https://document.dxznjy.com/course/e3189ca3f7a64c3397f456eec818129d.png" class="w44"></image>
              <view>学习记录</view>
            </view>
            <view class="template" @click="goLessonpPractice">
              <image src="https://document.dxznjy.com/course/221103930a8e47be8448078cf5d480e6.png" class="w44"></image>
              <view>学能一课一练</view>
            </view>
          </view>
        </view>

        <view class="f-28 c-66" v-else>
          <tabDetail :title="title" @updateState="showParentLIst" @useForget="forgetOne" @useVocabulary="useVocabulary" @useInterest="useInterest"></tabDetail>
        </view>
      </view>

      <view style="height: 30rpx"></view>
    </view>

    <u-popup :show="show" mode="center" :round="10" :closeable="true" :safeAreaInsetBottom="false" @close="close">
      <view class="phonecon t-c">
        <view class="tit f-32 bold mb-40">手机号码授权</view>
        <text class="tips pb-30 f-30">为了提供更好的服务请\n允许获取您的手机号码</text>
        <button class="login_in f-32 c-ff" open-type="getPhoneNumber" @getphonenumber="$noMultipleClicks(onGetPhoneNumber)">确认授权</button>
      </view>
    </u-popup>

    <!-- 选择校区弹窗 -->
    <uni-popup ref="popopChooseSchool" type="center" @change="changeSchool">
      <view class="dialogBG">
        <view class="reviewCard_box positionRelative">
          <view class="cartoom_image">
            <image src="https://document.dxznjy.com/dxSelect/dialog_icon.png" mode="widthFix"></image>
          </view>
          <view class="review_close" @click="closeDialog">
            <uni-icons type="clear" size="28" color="#B1B1B1"></uni-icons>
          </view>
          <view class="reviewCard">
            <view class="reviewTitle bold pb-20">选择校区</view>
            <scroll-view :scroll-top="scrollTop" scroll-y="true" class="scroll-Y" @scrolltoupper="upper" @scrolltolower="lower" @scroll="scroll">
              <view
                class="dialogContent"
                @click="chooseSchoollist(item, index)"
                v-for="(item, index) in arraySchool"
                :class="isChoosemerchantCode == index ? 'addclass' : 'not-selected'"
              >
                {{ item.merchantName }}
              </view>
            </scroll-view>
            <view class="review_btn" @click="$noMultipleClicks(confirmSchool)">确定</view>
          </view>
        </view>
      </view>
    </uni-popup>

    <!-- 选择孩子-->
    <uni-popup ref="popopChooseChilds" type="bottom">
      <u-picker
        :show="show2"
        :immediateChange="true"
        mode="selector"
        :default-selector="[0]"
        :columns="arrayPyf"
        title="请选择您的孩子"
        keyName="realName"
        confirmColor="#357B71"
        @confirm="confirm2"
        @cancel="cancel2"
        @change="changeHandler2"
      ></u-picker>
    </uni-popup>
    <!-- 选择学员弹窗 -->
    <uni-popup ref="popopChooseStudent" :mask-click="false" type="center" @change="changeStudent">
      <view class="dialogBG">
        <view class="reviewCard_box positionRelative">
          <view class="cartoom_image">
            <image src="https://document.dxznjy.com/dxSelect/dialog_icon.png" mode="widthFix"></image>
          </view>
          <view class="review_close" @click="closeDialog">
            <uni-icons type="clear" size="28" color="#B1B1B1"></uni-icons>
          </view>
          <view class="reviewCard">
            <view class="reviewTitle bold pb-10">选择学员</view>
            <view class="dialogContent" @click="chooseStudentlist(item, index)" v-for="(item, index) in arrayStudent" :class="isactive == index ? 'addclass' : 'not-selected'">
              {{ item.realName + '（' + item.studentCode + '）' }}
            </view>
            <view class="mask-footer">
              <button class="confirm-button" @click="confirmStudent()">确定</button>
              <button class="cancel-button" v-show="aiIntelligentWords" @click="closeDialog">取消</button>
            </view>
            <!--            <view class="review_btn" @click="confirmStudent()">确定</view>-->
          </view>
        </view>
      </view>
    </uni-popup>

    <!-- 没有开通权限 -->
    <uni-popup ref="popopPower" type="center" @change="changePower">
      <view class="dialogBG">
        <view class="reviewCard_box positionRelative">
          <view class="cartoom_image">
            <image src="https://document.dxznjy.com/dxSelect/dialog_icon.png" mode="widthFix"></image>
          </view>
          <view class="review_close" @click="closeDialog">
            <uni-icons type="clear" size="28" color="#B1B1B1"></uni-icons>
          </view>
          <view class="reviewCard">
            <view class="reviewTitle bold">温馨提示</view>
            <view class="c-f0 t-c mtb-50 ptb-50">您还没有开通使用权限</view>
            <view class="review_btn" @click="nowBuy()">立即购买</view>
          </view>
        </view>
      </view>
    </uni-popup>

    <uni-popup ref="popopChooseStudent21" type="center" @change="changeStudent">
      <view class="dialogBG">
        <view class="reviewCard_box positionRelative">
          <view class="cartoom_image">
            <image src="https://document.dxznjy.com/dxSelect/dialog_icon.png" mode="widthFix"></image>
          </view>
          <view class="review_close" @click="cancel">
            <uni-icons type="clear" size="28" color="#B1B1B1"></uni-icons>
          </view>
          <view class="reviewCard">
            <view class="reviewTitle bold pb-10">选择学员</view>
            <view class="dialogContent" @click="handleButtonClick(item, index)" v-for="(item, index) in arrayStudent" :class="activeIndex == index ? 'addclass' : 'not-selected'">
              {{ item.realName + '（' + item.studentCode + '）' }}
            </view>
            <view class="mask-footer">
              <button class="confirm-button" @click="$noMultipleClicks(confirm)">确定</button>
              <button class="cancel-button" @click="cancel">取消</button>
            </view>
          </view>
        </view>
      </view>
    </uni-popup>

    <user-dialog />
  </view>
</template>
<script>
  const { httpUser } = require('@/util/luch-request/indexUser.js');
  const { $navigationTo, $showMsg, $http } = require('@/util/methods.js');
  import { tabDetail } from '@/pages/home/<USER>/tabDetail.vue';
  import mycurriculum from '@/components/mycurriculum.vue';
  import UserDialog from '@/components/user-dialog/index.vue';
  import dayjs from 'dayjs';
  import getNotReadNews from '@/util/messages.js';
  export default {
    components: {
      mycurriculum,
      tabDetail,
      UserDialog
    },
    data() {
      return {
        title: '',
        showTabDetail: true,
        noClick: true, //防抖
        show2: false,
        arrayPyf: [],
        index1: 0,
        show1: true,
        info: [],
        current: 0,
        swiperDotIndex: 0,
        dotsStyles: {
          backgroundColor: 'rgba(236, 236, 236, 1)',
          border: '1px rgba(236, 236, 236, 1) solid',
          color: '#48917D',
          selectedBackgroundColor: '#48917D',
          selectedBorder: '1px #48917D solid'
        },
        show: false,
        pyfOrRead: 0,
        pageShow: true,
        userinfo: null,
        code1: null,
        studyCentre: '',
        percentage: '', // 进度条数值
        scheduleList: {}, // 进度列表
        schedule: {}, // 学员进度详情
        studentShow: true,
        bannerIndex: 0,
        superCodeList: [],
        userCode: '',
        studentCode: '',
        interestType: 1,
        name: '',
        studentFormMerchant: '', //课时区域门店编号显示
        haveHours: 0, //课时区域剩余学时显示
        useHours: 0, //课时区域已用学时显示
        merchantCode: '',
        tudentcode: '', // 学员进度详情code
        mobile: '', // 账号手机号
        imgHost: getApp().globalData.imgsomeHost,
        avaUrl: 'https://document.dxznjy.com/dxSelect/home_avaUrl.png',
        rollShow: false, //禁止滚动穿透,
        myClassList: {
          lastStudyCourse: {},
          nextStudyCourse: {}
        },
        isactive: -1, // 选中索引
        isChoosemerchantCode: -1, // 门店索引
        interestSelect: false,
        LessonpPracticeSelect: false,
        txtContent: '您还没有开通使用权限',
        arraySchool: [],
        arrayStudent: [],
        scrollTop: '',
        //选择学生信息
        studentInfo: {},
        //21天
        showMask: false, // 控制遮罩层的显示与隐藏
        activeIndex: -1,
        Buttonclick: '',
        buttonclickName: '',

        //5天
        pyfStudentCode: '',

        // 订阅消息
        useropenid: '',
        mytoken: '',
        templateId: 'a2pCjUTIytYmKob9R2w5u_e4OrvqqhV5s4EOhe9NLts', // 要发送订阅的模板id
        wxappid: '', // 小程序appID
        wxsecret: '', // 小程序密钥
        pushmsg: {
          touser: '', // 获取到的用户openid
          template_id: '', // 对应的模板id（微信公众平台中的订阅消息，选择对应模板）
          data: {
            thing1: {
              value: '今日抗遗忘训练还未完成，请点击进入'
            },
            thing2: {
              value: '合理有效的循环是抵抗遗忘最好的方法'
            }
          },
          page: 'pages/home/<USER>/index' //进入哪个页面
        },
        authorizationShow: false,
        subscribeStatus: false, // 授权状态
        defaultSelectStudent: null,
        curriculumId: '1283284867302313984', //正式1283284867302313984
        PYFcurriculumId: '1283343602584145920', //正式1283343602584145920
        oneStudy: false
      };
    },
    mounted() {},

    onShow() {
      let _this = this;
      // _this.getDetailList()
      let token = uni.getStorageSync('token');
      if (token) {
        getNotReadNews.getNotReadNews();
        uni.removeStorage({
          key: 'logintokenReview'
        });
        setTimeout(function () {
          _this.$refs.mycurriculum.getSubject();
        });
        _this.homeData();
        _this.getCurriculumId();
        _this.getStudent(); // 学员列表
        this.getSubject();
        // #ifdef MP-WEIXIN
        uni.login({
          success(res) {
            _this.code1 = res.code;
          }
        });
        // #endif
      } else {
        //甄选审核修改
        // _this.goToLogin();
      }
    },
    onTabItemTap(e) {
      // #ifdef MP-WEIXIN
      getApp().sensors.track('tabBarClick', {
        pagePath: e.pagePath,
        title: e.text
      });
      // #endif
    },
    methods: {
      async getCurriculumId() {
        let { data } = await $http({
          url: 'znyy/bvstatus/findByEnCodeAndEnType?enCode=PYF&enType=CURRICULUM'
        });
        this.PYFcurriculumId = data.id;
      },
      goToLogin() {
        uni.navigateTo({
          url: '/Personalcenter/login/login'
        });
      },
      ChangeHourMinutestr(str) {
        if (str !== '0' && str !== '' && str !== null) {
          return Math.floor(str / 60).toString() + ':' + (str % 60).toString();
        } else {
          return '';
        }
      },
      // 错题本跳转
      wrongBook() {
        // #ifdef MP-WEIXIN
        getApp().sensors.track('studyServiceItemClick', {
          name: '错题本'
        });
        // #endif
        if (!uni.getStorageSync('token')) {
          this.goToLogin();
          return;
        }
        uni.navigateTo({
          url: '/errorBook/index?memberId=' + this.userinfo.userCode
        });
      },
      async getDetailList(e) {
        const res = await $http({
          url: 'zx/wap/course/user/record/growth/track/query',
          data: {
            userId: uni.getStorageSync('user_id') ? uni.getStorageSync('user_id') : ''
          }
        });
        if (res) {
          this.superCodeList = res.data;
          this.superCodeList.forEach((item) => {
            item.studyDurationMinutesToday = this.ChangeHourMinutestr(item.studyDurationMinutesToday);
            item.studyDurationMinutesLastWeek = this.ChangeHourMinutestr(item.studyDurationMinutesLastWeek);
            item.studyDurationMinutesTotal = this.ChangeHourMinutestr(item.studyDurationMinutesTotal);
          });
        }
      },
      // 时间判断
      checkNextTime(time) {
        if (time != undefined) {
          let datetime = time;
          let nowTime = Date.now();
          let setTime = dayjs(datetime).unix() * 1000;
          return nowTime < setTime;
        }
      },
      // 查看下次反馈
      goNextback() {
        if (!uni.getStorageSync('token')) {
          this.goToLogin();
          return;
        }
        uni.navigateTo({
          url: '/Coursedetails/feedback/index?data=' + JSON.stringify(this.myClassList.nextStudyCourse)
        });
      },
      // 下次课程请假跳转
      gotoNextleave() {
        uni.navigateTo({
          url: '/Coursedetails/leave/leave?data=' + encodeURIComponent(JSON.stringify(this.myClassList.nextStudyCourse))
        });
      },
      // 获取我的课程
      async getSubject() {
        let res = await this.$httpUser.get('deliver/app/parent/getCourse');
        if (res.data.success) {
          this.myClassList = res.data.data;
        } else {
          this.$util.alter(res.data.message);
        }
      },

      // 禁止滚动穿透
      changeStudent(e) {
        this.rollShow = e.show;
      },
      changePower(e) {
        this.rollShow = e.show;
      },
      changeSchool(e) {
        this.rollShow = e.show;
      },
      changeSecurities(e) {
        this.rollShow = e.show;
      },
      // scroll-view 滑动
      upper: function (e) {
        console.log(e);
      },
      lower: function (e) {
        console.log(e);
      },
      scroll: function (e) {
        console.log(e);
        this.old.scrollTop = e.detail.scrollTop;
      },

      change(e) {
        this.current = e.detail.current;
        this.studentcode = this.info[this.current].studentCode;
        this.name = this.info[this.current].content;
        this.studentFormMerchant = this.info[this.current].studentFormMerchant;
        this.haveHours = this.info[this.current].haveHours;
        this.useHours = this.info[this.current].useHours;
        this.percentage = this.info[this.current].percentage;
        console.log(this.name);
        // this.getDetails()
      },
      clickItem(e) {
        this.swiperDotIndex = e;
      },
      // 我的学员
      goMystudent() {
        // #ifdef MP-WEIXIN
        getApp().sensors.track('studyServiceItemClick', {
          name: '我的学员'
        });
        // #endif
        if (!uni.getStorageSync('token')) {
          this.goToLogin();
          return;
        }
        uni.navigateTo({
          url: '/Personalcenter/my/mystudent?memberId=' + this.userinfo.userCode
        });
      },
      // 学员进度列表
      async getSchedule() {
        let that = this;
        that.info = [];
        // let result = await httpUser.post("deliver/app/parent/getScheduleList");
        let result = await httpUser.get('znyy/course/getMemberStudentList?memberId=' + that.userinfo.userCode);
        // let result = await httpUser.get('znyy/course/getMemberStudentList?memberId=' + '9241218888');
        if (result.data.success) {
          that.scheduleList = result.data.data;
          if (that.scheduleList.length != 0) {
            if (that.info.length == 0) {
              that.scheduleList.forEach((item) =>
                that.info.push({
                  content: item.realName,
                  studentCode: item.studentCode,
                  studentFormMerchant: item.merchantCode,
                  haveHours: item.haveHours,
                  useHours: item.useHours,
                  percentage: item.rate
                })
              );
            }
            if (that.info.length >= 1) {
              that.studentShow = true;
              that.studentcode = that.info[0].studentCode;
              that.name = that.info[0].content;
              that.studentFormMerchant = that.info[0].studentFormMerchant;
              that.haveHours = that.info[0].haveHours;
              that.useHours = that.info[0].useHours;
              that.percentage = that.info[0].percentage;
              // that.getDetails()
            } else {
              that.studentShow = false;
            }
          }
        }
        uni.navigateTo({
          url: '/Personalcenter/my/mystudent?memberId=' + this.userinfo.userCode
        });
      },
      // 学员进度列表
      async getSchedule() {
        let that = this;
        that.info = [];
        // let result = await httpUser.post("deliver/app/parent/getScheduleList");
        let result = await httpUser.get('znyy/course/getMemberStudentList?memberId=' + that.userinfo.userCode);
        if (result.data.success) {
          that.scheduleList = result.data.data;
          if (that.scheduleList.length != 0) {
            if (that.info.length == 0) {
              that.scheduleList.forEach((item) =>
                that.info.push({
                  content: item.realName,
                  studentCode: item.studentCode,
                  studentFormMerchant: item.merchantCode,
                  haveHours: item.haveHours,
                  useHours: item.useHours,
                  percentage: item.rate
                })
              );
            }
            if (that.info.length >= 1) {
              that.studentShow = true;
              that.studentcode = that.info[0].studentCode;
              that.name = that.info[0].content;
              that.studentFormMerchant = that.info[0].studentFormMerchant;
              that.haveHours = that.info[0].haveHours;
              that.useHours = that.info[0].useHours;
              that.percentage = that.info[0].percentage;
              // that.getDetails()
            } else {
              that.studentShow = false;
            }
          }
        }
      },
      // 点击趣味复习列表
      gotoInterest(e) {
        if (!uni.getStorageSync('token')) {
          this.goToLogin();
          return;
        }
        this.interestType = e;
        this.subScribeMsg();
        this.LessonpPracticeSelect = false;
        setTimeout(() => {
          let that = this;
          that.interestSelect = true;
          that.aiIntelligentWords = false;
          // 选择校区弹窗\
          if (that.arrayStudent.length == 0) {
            uni.showToast({
              title: '您还没有学员或没有正式学员哦',
              icon: 'none',
              duration: 3000
            });
            return;
          }

          this.$refs.popopChooseStudent.open();
          if (this.defaultSelectStudent) {
            this.isactive = this.defaultSelectStudent.index;
            this.studentCode = this.defaultSelectStudent.studentCode;
            this.studentInfo = this.defaultSelectStudent.studentInfo;
          } else {
            this.studentCode = '';
            this.studentInfo = {};
          }
        }, 500);
        let that = this;
        that.interestSelect = true;
        // 选择校区弹窗
        if (that.arrayStudent.length == 0) {
          uni.showToast({
            title: '您还没有学员或没有正式学员哦',
            icon: 'none',
            duration: 3000
          });
          return;
        }

        this.$refs.popopChooseStudent.open();
        if (this.defaultSelectStudent) {
          this.isactive = this.defaultSelectStudent.index;
          this.studentCode = this.defaultSelectStudent.studentCode;
          this.studentInfo = this.defaultSelectStudent.studentInfo;
        } else {
          this.studentCode = '';
          this.studentInfo = {};
        }
      },
      gotoAiIntelligentWords() {
        // #ifdef MP-WEIXIN
        getApp().sensors.track('studyServiceItemClick', {
          name: 'AI智阅'
        });
        // #endif
        if (!uni.getStorageSync('token')) {
          this.goToLogin();
          return;
        }
        this.subScribeMsg();
        this.LessonpPracticeSelect = false;
        setTimeout(() => {
          let that = this;
          that.aiIntelligentWords = true;
          that.interestSelect = false;
          // 选择校区弹窗\
          if (that.arrayStudent.length == 0) {
            uni.showToast({
              title: '您还没有学员或没有正式学员哦',
              icon: 'none',
              duration: 3000
            });
            return;
          }

          this.$refs.popopChooseStudent.open();
          this.studentCode = '';
          this.studentInfo = {};
        }, 500);
        let that = this;
        that.aiIntelligentWords = true;
        that.interestSelect = false;
        // 选择校区弹窗
        if (that.arrayStudent.length == 0) {
          uni.showToast({
            title: '您还没有学员或没有正式学员哦',
            icon: 'none',
            duration: 3000
          });
          return;
        }

        this.$refs.popopChooseStudent.open();
        this.studentCode = '';
        this.studentInfo = {};
      },
      // 获取学员列表
      async getStudent() {
        let that = this;
        let loginMobile = uni.getStorageSync('LoginMobile');
        // console.log(loginMobile);
        that.StudentCodeKey = 'review_' + loginMobile;
        let result = await httpUser.get('znyy/review/query/my/student');
        if (result != undefined && result != '') {
          if (result.data.data != null) {
            that.arrayStudent = result.data.data;
            console.log('that.arrayStudent--', that.arrayStudent);
            if (result.data.data.length > 0) {
              const data = result.data.data[0];
              this.defaultSelectStudent = {
                index: 0,
                studentCode: data.studentCode,
                studentInfo: data
              };
            }
            //获取拼音法抗遗忘接口人员数据
            that.arrayPyf = that.arrayStudent.map((item, index, array) => {
              return {
                realName: item.realName,
                studentCode: item.studentCode,
                isFormal: item.isFormal
              };
            });
            console.log('arrayPyf', that.arrayPyf);
            // picker直接使用 不需要
            // that.arrayPyf=that.arrayPyf
            // u-picker外面要再套一个数组
            that.arrayPyf = [that.arrayPyf];
          }
        }
      },
      //点击选择学员
      chooseStudentlist(item, index) {
        this.isactive = index;
        this.studentCode = item.studentCode;
        this.studentInfo = { ...item };
      },
      async getCourseHours(token) {
        let that = this;
        const res = await $http({
          url: 'zx/wap/course/judge',
          data: {
            studentCode: this.studentCode
          }
        });
        if (res) {
          if (res.errMessage == 'true') {
            wx.navigateToMiniProgram({
              appId: 'wxcbd506017e4956dc',
              extraData: {
                token: token,
                code: that.studentInfo.studentCode,
                studentName: that.studentInfo.realName,
                grade: that.studentInfo.grade
              },
              envVersion: 'release',
              success: (res) => {
                that.closeDialog();
                console.log('打开成功', res);
                that.LessonpPracticeSelect = false;
              },
              fail: (err) => {
                console.log('打开失败', err);
                that.closeDialog();
                that.LessonpPracticeSelect = false;
              }
            });
            return;
          }
        }
      },
      // 选择学员确定
      async confirmStudent() {
        this.pyfOrRead = 0;
        let that = this;
        //一课一练判断
        if (this.LessonpPracticeSelect) {
          if (!this.studentInfo.studentCode) {
            uni.showToast({
              title: '请选择学员',
              icon: 'none',
              duration: 3000
            });
            return;
          }
        }
        // if (!that.interestSelect && !that.aiIntelligentWords) {
        //   that.getPadToken();
        //   that.closeDialog();
        //   return;
        // }
        if (this.studentCode == '') {
          uni.showToast({
            title: '请选择学员',
            icon: 'none',
            duration: 3000
          });
          return;
        }
        // uni.showLoading()
        // AI智阅接口修改
        let httpUrl = '';
        let curriculumId = this.curriculumId;
        if (this.interestType == 2) {
          curriculumId = this.PYFcurriculumId;
        }
        if (this.aiIntelligentWords) {
          httpUrl = 'znyy/superReadReview/queryMerchantList';
        } else {
          if (this.oneStudy) {
            httpUrl = 'zx/wap/course/student/course/summary';
          } else if (this.interestType == 2) {
            httpUrl = 'znyy/pd/mobile/funReview/exist/entrance';
          } else {
            httpUrl = 'v2/mall/getStudentMerchantList';
          }
        }
        httpUser.get(httpUrl + '?studentCode=' + that.studentCode + '&curriculumId=' + curriculumId).then((result) => {
          // uni.hideLoading()
          if (result.data.success) {
            that.oneStudy = false;
            that.$refs.popopChooseStudent.close();
            // 没有购买去购买
            if (result.data.code == 20004) {
              setTimeout(function () {
                that.$refs.popopPower.open();
              }, 500);
              return;
            }
            if (result.data.success) {
              that.oneStudy = false;
              that.$refs.popopChooseStudent.close();
              that.arraySchool = result.data.data;
              // uni.setStorageSync('merchantCode', that.arraySchool[0].merchantCode)
              if (result.data.data == null || result.data.data.length == 0) {
                that.$util.alter('您还没有学习过课程');
              }
              if (result.data.data.length == 1 && result.data.data != null) {
                that.merchantCode = that.arraySchool[0].merchantCode;
                if (this.interestType == 2) {
                  uni.navigateTo({
                    url: `/interestModule/pyfinterest/index?studentCode=${that.studentCode}&merchantCode=${that.merchantCode}`
                  });
                } else {
                  that.getPadToken();
                }
              } else if (result.data.data.length > 1 && result.data.data != null) {
                if (this.interestType == 2) {
                  that.merchantCode = that.arraySchool[0].merchantCode;
                  let flagTemp = that.arraySchool[0].merchantCode == that.arraySchool[1].merchantCode;
                  if (!flagTemp) {
                    // 若购买高低年级课程不一致，则默认选择第一个校区（后续根据实际情况修改）
                    that.merchantCode = that.arraySchool[0].merchantCode;
                  }
                  console.log('arraySchool', that.arraySchool, that.merchantCode);
                  uni.navigateTo({
                    url: `/interestModule/pyfinterest/index?studentCode=${that.studentCode}&merchantCode=${that.merchantCode}`
                  });
                } else {
                  this.isactive = -1;
                  that.$refs.popopChooseSchool.open();
                }
              }
            }
          } else {
            console.log(result);
            that.$util.alter(result.data.message);
          }
        });
      },
      //点击选择校区
      chooseSchoollist(item, index) {
        this.merchantCode = item.merchantCode;
        this.isChoosemerchantCode = index;
      },
      // 选择校区确定
      // 选择校区确定
      confirmSchool() {
        if (!this.merchantCode) {
          uni.showToast({
            title: '请选择校区',
            icon: 'none',
            duration: 3000
          });
        } else {
          if (this.pyfOrRead) {
            uni.navigateTo({
              url: '/ReadForget/index?merchantCode=' + this.merchantCode + '&studentCode=' + this.pyfStudentCode
            });
            this.closeDialog();
          } else {
            this.getPadToken(this.merchantCode);
          }
        }
      },

      //立即购买趣味复习
      nowBuy() {
        let code = this.studentCode;
        console.log('立即购买趣味复习');
        this.closeDialog();
        uni.navigateTo({
          url: `/Personalcenter/interest/orderDetail?studentCode=${code}`
        });
      },
      getIntegral() {
        // #ifdef MP-WEIXIN
        getApp().sensors.track('integralCenterClick', {
          name: '积分中心'
        });
        // #endif
        $navigationTo('Personalcenter/my/myIntegral');
      },
      //关闭弹窗
      closeDialog() {
        this.pyfOrRead = 0;
        this.isactive = -1;
        this.studentCode = '';
        this.merchantCode = '';
        this.isChoosemerchantCode = -1;
        this.oneStudy = false;
        this.$refs.popopChooseStudent.close();
        this.$refs.popopChooseSchool.close();
        this.$refs.popopPower.close();
      },
      // 获取pad token
      getPadToken(scheduleCode) {
        let that = this;
        if (that.merchantCode == '') {
          that.$util.alter('请选择门店');
          return;
        }
        // that.closeDialog();
        var logintoken = uni.getStorageSync('token');
        console.log(logintoken);
        uni.showLoading();
        httpUser.get(`new/security/v2/login/student/member/token?memberToken=${logintoken}&studentCode=${that.studentCode}&merchantCode=${that.merchantCode}`).then((res) => {
          uni.hideLoading();
          if (res.data.success) {
            uni.setStorageSync('logintokenReview', res.data.data.token);
            if (that.LessonpPracticeSelect) {
              this.getCourseHours(res.data.data.token);
              return;
            }
            that.isactive = -1;
            if (that.aiIntelligentWords) {
              uni.navigateTo({
                url: `/aiIntelligentWords/wordsSelect/wordsSelect?studentCode=${that.studentCode}&merchantCode=${that.merchantCode}`
              });
            }
            if (that.interestSelect) {
              uni.navigateTo({
                url: `/antiAmnesia/review/funReview?studentCode=${that.studentCode}&merchantCode=${that.merchantCode}`
              });
            }
            this.closeDialog();
          } else {
            that.$util.alter(res.data.message);
          }
        });
      },
      goLessonpPractice() {
        // #ifdef MP-WEIXIN
        getApp().sensors.track('studyServiceItemClick', {
          name: '学能一课一练'
        });
        // #endif
        if (!uni.getStorageSync('token')) {
          this.goToLogin();
          return;
        }
        this.LessonpPracticeSelect = true;
        this.interestSelect = false;
        this.aiIntelligentWords = false;
        // 选择校区弹窗\
        if (this.arrayStudent.length == 0) {
          uni.showToast({
            title: '您还没有学员或没有正式学员哦',
            icon: 'none',
            duration: 3000
          });
          return;
        }
        this.oneStudy = true;
        this.$refs.popopChooseStudent.open();
        this.studentInfo = {};
      },
      // banner下列表点击

      // 学员进度详情
      async getDetails() {
        let result = await httpUser.post('deliver/app/parent/getScheduleInfo/' + this.studentcode);
        if (result.data.success) {
          this.schedule = result.data.data;
          this.percentage = this.schedule.progress;
        }
      },
      // goReport(){
      // 	if(this.userinfo.identityType!=0){
      // 		uni.navigateTo({
      // 			url:'/pages/home/<USER>/recommend'
      // 		})
      // 	}else{
      // 		uni.navigateTo({
      // 			url:'/pages/home/<USER>/result'
      // 		})
      // 	}
      // },
      skintap(url, name) {
        // #ifdef MP-WEIXIN
        getApp().sensors.track('studyServiceItemClick', {
          name: name
        });
        // #endif
        if (uni.getStorageSync('token')) {
          $navigationTo(url);
        } else {
          this.goToLogin();
        }
      },
      close() {
        this.show = false;
      },
      // 去提现
      withdrawl_s() {
        if (uni.getStorageSync('certStatus') == 0) {
          uni.showModal({
            title: '提示',
            content: '未认证，请先前往进行实名认证',
            showCancel: false,
            success: function (res) {
              if (res.confirm) {
                uni.navigateTo({
                  url: '../info/info'
                });
              } else if (res.cancel) {
                console.log('用户点击取消');
              }
            }
          });
          return false;
        } else {
          uni.navigateTo({
            url: '../Withdrawal/Withdrawal?money=' + this.userinfo.takeAmount
          });
        }
      },
      // 手机号授权
      async onGetPhoneNumber(e) {
        let _this = this;
        uni.showLoading({
          title: '请等待',
          mask: false
        });
        // 检查登录态是否过期
        wx.checkSession({
          success: async function (res) {
            const encryptedData = e.detail.encryptedData;
            const iv = e.detail.iv;
            if (e.detail.errMsg == 'getPhoneNumber:ok') {
              const resdata = await $http({
                url: 'zx/common/decodeWechatPhone',
                method: 'POST',
                data: {
                  code: _this.code1,
                  encryptedData: encryptedData,
                  iv: iv
                }
              });
              if (resdata) {
                _this.show = false;
                uni.showToast({
                  title: '手机号码授权成功',
                  icon: 'none'
                });
                _this.homeData();
              }
            } else {
              uni.showToast({
                title: '请重新获取',
                icon: 'none'
              });
            }
          },
          fail(err) {
            uni.login({
              success: (res) => {
                _this.code1 = res.code;
              }
            });
          }
        });
      },
      // 获取首页信息
      async homeData() {
        let _this = this;
        const res = await $http({
          url: 'zx/user/userInfoNew'
        });
        if (res) {
          _this.pageShow = false;
          // _this.setting()
          _this.userinfo = res.data;
          _this.getSchedule(); // 学员进度列表
          uni.setStorageSync('user_id', res.data.userId);
          if (!res.data.mobile) {
            _this.show = true;
          }
          uni.setStorageSync('isBindPayPhone', res.data.isBindPayPhone || 0); //是否绑定手机号
          uni.setStorageSync('isMember', res.data.isMember);
          uni.setStorageSync('identityType', res.data.identityType);

          uni.setStorageSync('certStatus', res.data.certStatus); //0未认证  1已认证
          uni.setStorageSync('merchantId', res.data.merchantId);
          uni.setStorageSync('headPortrait', res.data.headPortrait);
          uni.setStorageSync('nickName', res.data.nickName);
          uni.setStorageSync('userCode', res.data.userCode);
          uni.setStorageSync('log_userCode', res.data.userCode);
        }
      },

      infobtn() {
        let _this = this;
        uni.navigateTo({
          url: '/splitContent/authen/authen'
        });
      },
      toStudy() {
        uni.navigateTo({
          url: '/Personalcenter/home/<USER>'
        });
        //window.history.href="https://appjhz9aetm9567.h5.xiaoeknow.com/xe.community.community_service/v2/feedList?app_id=appjhz9aetm9567&community_id=c_6261711172317_dO4cmXrA4965&product_id=&share_user_id="
      },
      goGrowTth(item) {
        console.log('--------------------------------------');
        uni.navigateTo({
          url: '/Personalcenter/my/growthReport?studentCode=' + item.studentCode
        });
      },
      //展示抗遗忘列表
      showList(id, name) {
        // #ifdef MP-WEIXIN
        getApp().sensors.track('studyServiceItemClick', {
          name: name
        });
        // #endif
        let that = this;
        if (!uni.getStorageSync('token')) {
          this.goToLogin();
          return;
        }
        that.showTabDetail = false;
        if (id == 1) {
          that.title = '抗遗忘选择';
        }
        if (id == 2) {
          that.title = '检测报告';
        }
        if (id == 3) {
          that.title = '趣味复习';
        }
      },

      // 点击21天抗遗忘
      forget() {
        this.subScribeMsg();
        if (this.arrayStudent.length == 0) {
          uni.showToast({
            title: '您还没有学员或没有正式学员哦',
            icon: 'none',
            duration: 3000
          });
          return;
        }
        this.$refs.popopChooseStudent21.open();
        this.showMask = !this.showMask;
      },
      // 选择学员弹窗取消按钮
      cancel() {
        this.$refs.popopChooseStudent21.close();
        this.showMask = false; // 点击取消按钮，隐藏遮罩层
        this.handleClearData();
      },
      // 选择学员弹窗清除之前选中样式
      handleClearData() {
        this.activeIndex = -1;
        this.Buttonclick = '';
        this.buttonclickName = '';
      },
      // 处理选择按钮点击事件
      handleButtonClick(item, index) {
        this.activeIndex = index;
        this.Buttonclick = item.studentCode;
        this.buttonclickName = item.realName;
      },
      // 选择学员弹窗确定按钮
      confirm() {
        // 如果未选择学员提示
        if (this.Buttonclick == '') {
          this.$util.alter('请先选择学员');
          return;
        } else {
          this.$refs.popopChooseStudent21.close();
          this.showMask = false; // 隐藏遮罩层
          // 跳转到页面
          uni.navigateTo({
            url: '/antiAmnesia/review/index?buttonclickName=' + this.buttonclickName + '&buttonClick=' + encodeURIComponent(this.Buttonclick)
          });
          this.handleClearData();
        }
      },
      //词汇量报告
      gotoVocabulary() {
        if (!uni.getStorageSync('token')) {
          this.goToLogin();
          return;
        }
        this.subScribeMsg();

        if (this.arrayStudent.length == 0) {
          uni.showToast({
            title: '您还没有学员或没有正式学员哦',
            icon: 'none',
            duration: 3000
          });
          return;
        }
        this.$refs.popopChooseStudent21.open();
        this.showMask = !this.showMask;
      },
      //听写检测报告
      gotoDictationReport() {
        uni.navigateTo({
          url: '/parentEnd/dictation/dictationReport'
        });
      },
      // 选择学员弹窗取消按钮
      cancel() {
        this.$refs.popopChooseStudent21.close();
        this.showMask = false; // 点击取消按钮，隐藏遮罩层
        this.handleClearData();
      },
      // 选择学员弹窗清除之前选中样式
      handleClearData() {
        this.activeIndex = -1;
        this.Buttonclick = '';
        this.buttonclickName = '';
      },
      // 处理选择按钮点击事件
      handleButtonClick(item, index) {
        this.activeIndex = index;
        this.Buttonclick = item.studentCode;
        this.buttonclickName = item.realName;
      },
      // 选择学员弹窗确定按钮
      confirm() {
        // 如果未选择学员提示
        if (this.Buttonclick == '') {
          this.$util.alter('请先选择学员');
          return;
        } else {
          this.$refs.popopChooseStudent21.close();
          this.showMask = false; // 隐藏遮罩层
          // 跳转到页面
          uni.navigateTo({
            url: '/antiAmnesia/review/index?buttonclickName=' + this.buttonclickName + '&buttonClick=' + encodeURIComponent(this.Buttonclick)
          });
          this.handleClearData();
        }
      },
      //词汇量报告
      gotoVocabulary() {
        uni.navigateTo({
          url: '/parentEnd/vocabulary/vocabulary'
        });
      },

      //学时详情
      gotoCourseDetail() {
        // #ifdef MP-WEIXIN
        getApp().sensors.track('studyServiceItemClick', {
          name: '学时详情'
        });
        // #endif
        if (!uni.getStorageSync('token')) {
          this.goToLogin();
          return;
        }
        uni.navigateTo({
          url: '/parentEnd/recharge/lessonDetails'
        });
      },
      changeBannerCurrent(val) {
        this.bannerIndex = val.detail.current;
      },
      async confirm2(e) {
        this.show2 = false;
        this.$refs.popopChooseChilds.close();
        if (this.pyfOrRead == 1) {
          uni.showTabBar();
          uni.navigateTo({
            url: '/PYFforget/forgetReview?studentCode=' + this.pyfStudentCode
          });
          this.pyfOrRead = 0;
        } else {
          uni.showTabBar();
          //
          let { data } = await httpUser.get('znyy/superReadReview/queryMerchantList?studentCode=' + this.pyfStudentCode);
          this.arraySchool = data.data;
          if (data.data.length == 1 && data.data != null) {
            this.merchantCode = this.arraySchool[0].merchantCode;
            uni.navigateTo({
              url: '/ReadForget/index?merchantCode=' + this.merchantCode + '&studentCode=' + this.pyfStudentCode
            });
          } else if (data.data.length > 1 && data.data != null) {
            this.$refs.popopChooseSchool.open();
          } else {
            uni.showToast({
              title: '暂无记录',
              icon: 'none',
              duration: 3000
            });
          }
        }
        // 显示底部导航
      },
      cancel2() {
        this.show2 = false;
        this.$refs.popopChooseChilds.close();
        // this.pyfOrRead = 0;
        uni.showTabBar();
      },
      changeHandler2(e) {
        this.pyfStudentCode = e.value[0].studentCode;
        console.log('选中的id', this.pyfStudentCode);
        uni.setStorageSync('pyfStudentCode', this.pyfStudentCode);
      },
      selectPerson(e) {
        if (this.arrayPyf[0].length == 0) {
          return uni.showToast({
            title: '暂无数据',
            icon: 'none',
            duration: 3000
          });
        }
        this.show2 = true;
        this.pyfOrRead = e;
        let arrayPyf = this.arrayPyf[0];
        if (this.pyfStudentCode.length == 0) {
          this.pyfStudentCode = arrayPyf[0].studentCode;
          uni.setStorageSync('pyfStudentCode', this.pyfStudentCode);
        }
        // 隐藏底部导航
        uni.hideTabBar();
      },
      // 用户授权
      subScribeMsg(url) {
        var that = this;
        uni.getSetting({
          withSubscriptions: true,
          success(res) {
            if (!res.subscriptionsSetting.mainSwitch) {
              uni.openSetting({
                //打开设置页
                success(res) {
                  console.log('打开设置页', res.authSetting);
                }
              });
            } else {
              uni.requestSubscribeMessage({
                tmplIds: ['a2pCjUTIytYmKob9R2w5u_e4OrvqqhV5s4EOhe9NLts'],
                success(res) {
                  console.log('requestSubscribeMessage 订阅信息', res);
                  if (res['a2pCjUTIytYmKob9R2w5u_e4OrvqqhV5s4EOhe9NLts'] == 'accept') {
                    // 用户点击确定后
                    console.log('用户订阅点击确定按钮');
                  } else {
                    console.log('拒绝');
                  }
                },
                fail(errMessage) {
                  console.log('订阅消息 失败', errMessage);
                }
              });
            }
          }
        });
      },
      showParentLIst() {
        this.showTabDetail = true;
      },
      forgetOne(type) {
        if (type == 1) {
          this.forget();
        }
        if (type == 2) {
          this.selectPerson(1);
          this.$refs.popopChooseChilds.open();
        }
        if (type == 3) {
          this.selectPerson(2);
          this.$refs.popopChooseChilds.open();
        }
      },
      useVocabulary(type) {
        if (type == 1) {
          this.gotoVocabulary();
        }
        if (type == 2) {
          this.gotoDictationReport();
        }
      },
      useInterest(type) {
        if (type == 1) {
          this.gotoInterest(1);
        }
        if (type == 2) {
          this.gotoInterest(2);

          // // 暂时开发使用
          // uni.navigateTo({
          //   url: `/interestModule/pyfinterest/index`
          // });
          // $showMsg('开发中，敬请期待');
        }
      }
    },

    onShareAppMessage(res) {
      return {
        title: '阿拉鼎星球会员',
        success(res) {},
        fail(res) {}
      };
    }
  };
</script>

<style lang="scss" scoped>
  /deep/ .u-toolbar.data-v-55c89db1 {
    border-bottom: 1px solid #e0e0e0;
  }

  /deep/ .u-line-1 {
    line-height: 68rpx !important;
    background-color: #f4f4f4 !important;
  }

  /deep/ .u-picker__view {
    height: 440rpx !important;
  }

  /deep/ .u-picker__view__column.data-v-f45a262e {
    border-radius: 12rpx;
  }

  /deep/ .u-popup__content.data-v-3a231fda {
    border-radius: 12rpx;
    margin: 0 20rpx 20rpx 20rpx;
  }

  /deep/ .u-picker__view__column__item {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    font-size: 30rpx !important;
    margin-left: 10rpx;
  }

  /deep/ .u-toolbar__title.data-v-6d25fc6f {
    color: #303133;
    padding: 0 60rpx;
    font-size: 16px;
    font-weight: 700;
    flex: 1;
    text-align: center;
    background-color: #ffffff !important;
  }

  /deep/ .u-toolbar.data-v-6d25fc6f {
    height: 42px;
    width: 96%;
    display: flex;
    margin: 0 auto;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #e0e0e0;
    padding: 20rpx 0;
    box-sizing: b;
  }

  .personl_header_bg {
    height: 462upx;
    background: linear-gradient(to bottom, #2f8c70, #c7e0d8);
  }

  .banner_indicator_style {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    position: absolute;
    bottom: -40rpx;
    left: 50%;
    width: 48rpx;
    height: 8rpx;
    background-color: #fae1c5;
    transform: translateX(-50%);
    border-radius: 4rpx;

    .indicator_style_css {
      height: 8rpx;
      border-radius: 4rpx;
    }

    .active_current {
      height: 8rpx;
      background-color: #fd9b2a;
      border-radius: 4rpx;
    }
  }

  .page_title {
    position: absolute;
    top: 80upx;
    width: 100%;
    text-align: center;
  }

  .study_service {
    padding-left: 24rpx;
  }

  page {
    background-color: #f7f7f7;
  }

  .home_bg {
    height: 160rpx;
  }

  .icon_img {
    position: absolute;
    top: 80rpx;
    left: 30rpx;
    z-index: 999 !important;
  }

  /deep/ .mlr-20 {
    margin-right: 0 !important;
  }

  .box {
    position: absolute;
    right: -5rpx;
  }

  /deep/ .data-v-26a60cd2 {
    padding: 0;
  }

  /deep/ .wid36 {
    width: 39upx;
    height: 39upx;
  }

  .home_con {
    position: relative;
    // margin-top: 20rpx;
    border-radius: 20upx 20upx 0 0;
    // padding: 30upx;
  }

  .nicknameElli {
    max-width: 100%;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }

  .phonecon {
    padding: 50upx 30upx;
  }

  .login_in {
    background-image: linear-gradient(to right, #329e8b, #2c6e62);
    height: 100upx;
    border-radius: 50upx;
    width: 500upx;
    line-height: 100upx;
    margin-top: 50upx;
  }

  /deep/ .swiper-box {
    height: 100rpx !important;
  }

  /deep/ .data-v-1fff95d2 {
    height: 100rpx !important;
  }

  /deep/ .u-badge--not-dot.data-v-662d25bf {
    padding: 2rpx 6rpx;
  }

  // 学习进度
  .plan {
    position: absolute;
    top: 0;
    border-radius: 30rpx;
    z-index: 9;
    color: #000;
    height: 240rpx;
    width: 92%;
  }

  .title {
    position: relative;
    font-size: 32rpx;
    font-family: Microsoft YaHei;
    padding: 30rpx 0 0 0;
    margin-left: 30rpx;
    margin-right: 30rpx;
  }

  .title-line {
    height: 1rpx;
    background-color: #eeeeee;
    margin-top: 60rpx;
  }

  .title-name {
    position: absolute;
    left: 0;
  }

  .title-merchant {
    position: absolute;
    right: 0;
  }

  .hour {
    padding: 20rpx 30rpx 0 30rpx;
    color: #000;
  }

  .time {
    font-size: 26rpx;
    margin-bottom: 20rpx;
  }

  .w44 {
    width: 44upx;
    height: 44upx;
  }

  /deep/ .uni-swiper__warp swiper {
    height: 296rpx !important;
  }

  .myClassList {
    height: 100upx;
    box-sizing: border-box;
    line-height: 100upx;
  }

  .flex_s {
    display: flex;
  }

  .border-t {
    border-top: 1px dashed #eee;
  }

  .shike-img {
    position: absolute;
    width: 45rpx;
    height: 45rpx;
    top: 320rpx;
    left: 210rpx;
  }

  .templateItem {
    width: 100%;
    text-align: center;
    display: flex;
    align-content: flex-start;
    flex-flow: row wrap;
    font-size: 24rpx;
  }

  .template {
    flex: 0 0 33.33%;
    margin-bottom: 50rpx;
    font-size: 24rpx;
  }

  /* 弹窗样式 */
  .dialogBG {
    width: 100%;
    /* height: 100%; */
  }

  /* 21天结束复习弹窗样式 */
  .reviewCard_box {
    width: 670rpx;
    /* height: 560rpx; */
    position: relative;
  }

  .reviewCard_box image {
    width: 100%;
    height: 100%;
  }

  .reviewCard {
    position: relative;
    width: 100%;
    height: 100%;
    background: #ffffff;
    color: #000;
    border-radius: 24upx;
    padding: 50upx 55upx;
    box-sizing: border-box;
  }

  .cartoom_image {
    width: 420rpx;
    position: absolute;
    top: -250rpx;
    left: 145rpx;
    z-index: -1;
  }

  .review_close {
    position: absolute;
    top: 20rpx;
    right: 20rpx;
    z-index: 1;
  }

  .reviewTitle {
    width: 100%;
    text-align: center;
    font-size: 34upx;
    display: flex;
    justify-content: center;
  }

  .dialogContent {
    box-sizing: border-box;
    font-size: 32upx;
    line-height: 45upx;
    text-align: center;
    margin-top: 40rpx;
  }

  .review_btn {
    width: 240upx;
    height: 80upx;
    background: linear-gradient(180deg, #88cfba 0%, #1d755c 100%);
    border-radius: 45upx;
    font-size: 30upx;
    color: #ffffff;
    line-height: 80upx;
    margin: 60rpx auto 0 auto;
    justify-content: center;
    text-align: center;
  }

  .addclass {
    width: 100%;
    height: 70rpx;
    line-height: 70rpx;
    font-size: 30rpx;
    color: #2e896f;
    border: 1px solid #2e896f;
    border-radius: 35rpx;
  }

  .not-selected {
    width: 100%;
    height: 70rpx;
    line-height: 70rpx;
    font-size: 30rpx;
    color: #000;
    border: 1px solid #c8c8c8;
    border-radius: 35rpx;
  }

  .scroll-Y {
    height: 440rpx;
  }

  .btn_orange {
    background: linear-gradient(to bottom, #88cfba, #1d755c);
    color: #fff !important;
    height: 60rpx;
    width: 150rpx;
    font-size: 28rpx;
    line-height: 60rpx;
    border-radius: 45rpx;
    border: none !important;
  }

  /deep/ .uni-swiper__dots-box {
    bottom: 75rpx !important;
  }

  /deep/ .uni-swiper__dots-item {
    width: 6rpx !important;
    height: 6rpx !important;
  }

  .course-img {
    margin-left: 30rpx;
    margin-right: 30rpx;
    height: 254rpx;
  }

  // 21天抗遗忘
  /* 底部按钮样式 */
  .mask-footer {
    margin-top: 20px;
    display: flex;
    justify-content: space-around;
  }

  .mask-footer button {
    width: 250rpx;
    height: 80rpx;
    font-size: 30rpx;
    border-radius: 45rpx;
  }

  .confirm-button {
    display: flex;
    justify-content: space-around;
    align-items: center;
    /* 文本垂直居中对齐 */
    background: #2e896f;
    color: #ffffff !important;
  }

  .cancel-button {
    display: flex;
    justify-content: space-around;
    align-items: center;
    /* 文本垂直居中对齐 */
    color: #2e896f !important;
    border: 1rpx solid #2e896f !important;
  }

  .empty-img {
    width: 114rpx;
    height: 107rpx;
  }

  .empty-content {
    z-index: 9;
    width: 100%;
    display: flex;
    margin-bottom: 30rpx !important;
    padding-top: 18rpx;
    flex-direction: column;
    /* 垂直布局，子视图按列排列 */
    justify-content: center;
    /* 水平居中 */
    align-items: center;
    /* 垂直居中 */
  }

  .header_css {
    background: url('https://document.dxznjy.com/course/7cf268d57fba49739ee8514bfe813b8f.png') no-repeat;
    background-size: 100%;
    width: 750rpx;
    height: 460rpx;
    padding-top: 40rpx;

    .box-128 {
      width: 128rpx;
      height: 128rpx;
    }

    .identity_css {
      color: #006f57;
      line-height: 34rpx;
      padding: 1rpx 40rpx;
      display: inline-block;
      background-color: #dfffe4;
    }

    .center_css {
      width: 270rpx;
      margin-left: 2rpx;
    }

    .content_right {
      height: 60rpx;
      margin-left: 150rpx;

      .integral_css {
        vertical-align: middle;
        display: block;
        margin-right: 18rpx;
      }
    }

    .content_main_css {
      width: 686rpx;
      margin: 0 auto;
      height: 254rpx;
      margin-bottom: 74rpx;
    }

    .content_main_css_item {
      width: 638rpx !important;
      border-radius: 24rpx;
      margin: 0 auto;
      padding: 18rpx 24rpx;
      height: 219rpx !important;
    }

    .no_study_time {
      background-color: #f6fcff;
      border-radius: 16rpx;
      width: 636rpx;
      text-align: center;
    }

    .study_time {
      display: flex;
      justify-content: space-around;
      align-items: center;
      background-color: #f6fcff;
      padding-top: 20rpx;
      padding-bottom: 32rpx;
      margin-top: 24rpx;

      .study_time_item {
        width: 33%;
        text-align: center;
        position: relative;

        .green_css {
          color: #339378;
          line-height: 54rpx;
          display: inline-block;
        }
      }

      .study_time_item::after {
        content: ' ';
        height: 48rpx;
        border-right: 2rpx solid #ebf2f6;
        position: absolute;
        top: 30rpx;
        right: 0;
      }

      .study_time_item:last-child::after {
        display: none;
      }
    }
  }
</style>
