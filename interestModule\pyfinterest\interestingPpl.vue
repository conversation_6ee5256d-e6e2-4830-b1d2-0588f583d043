<template>
  <div class="funContent">
    <interesting-head :title="titleText" @backPage="beforeleave" :dark="true" :hasTitleBg="true" :closeWhite="false" :hasRight="false"></interesting-head>
    <!-- 倒计时 -->
    <pyf-progress :countdownStyle="2" :percent="((downTimeCount - showDownTime) * 100) / downTimeCount" :time="!showDownTime || showDownTime < 0 ? 0 : showDownTime"></pyf-progress>
    <!-- 喇叭 -->
    <view class="interesting_title">
      <view></view>
      <template v-if="showListData[qIdIndex] && showListData[qIdIndex].wordSyllable">
        <image
          class="title_icon"
          @click="playWord(showListData[qIdIndex].wordSyllableAudioUrl, showListData[qIdIndex].splitList)"
          v-show="showListData[qIdIndex].wordSyllable"
        ></image>
      </template>
    </view>
    <!-- 倒计时 -->
    <!-- <view class="countdown">
      <u-icon name="hourglass-half-fill" color="#d4d195" size="86"></u-icon>
      <text style="font-weight: 600">{{ showDownTime == undefine || showDownTime < 0 ? 0 : showDownTime }}s</text>
    </view> -->
    <!-- 答案列表 -->
    <scroll-view @scroll="scrollView" :scroll-top="scrollTop || 0" scroll-y style="overflow-y: scroll" class="scroll_view">
      <!-- 1.wordRemarkList -->
      <template v-if="showListData[qIdIndex] && showListData[qIdIndex].wordRemarkList">
        <view class="answerListBox" v-show="showListData[qIdIndex].wordRemarkList">
          <view class="answerList" v-for="(item, index) in answerList" :key="index" @click="chooseAnswerItem(item, index)">
            <view :class="isSubmit ? (showListData[qIdIndex].wordRemarkList[index] == item ? 'answerRight' : 'answerWrang') : 'answerNormal'">{{ item ? item : '' }}</view>
          </view>
        </view>
      </template>
      <!-- 2.splitList -->
      <!-- <view scroll-y class="answerListBox" v-show="showListData[qIdIndex].splitList">
        <view
          class="answerList"
          :class="isSubmit ? (showListData[qIdIndex].splitList[index].wordSyllable == item ? 'answerRight' : 'answerWrang') : 'answerNormal'"
          v-for="(item, index) in answerList"
          :key="index"
          @click="chooseAnswerItem(item, index)"
        >
          {{ item ? item : '' }}
        </view>
      </view> -->
    </scroll-view>
    <view class="down_arrow" v-show="answerList.length > 8">
      <view @click="scrollAdd(1)"></view>
    </view>
    <!-- 正确答案 -->
    <view class="translationBox" v-if="isSubmit">{{ showListData[qIdIndex] && showListData[qIdIndex].wordSyllable ? showListData[qIdIndex].wordSyllable : '' }}</view>
    <!-- <view class="translationBox">{{ showListData[qIdIndex].wordSyllable }}</view> -->
    <!-- 选项列表 -->
    <scroll-view
      @scroll="scrollView2"
      :scroll-top="scrollTop2 || 0"
      class="optionListBox"
      scroll-y
      style="max-height: 24vh; margin-top: 30rpx; min-height: 200rpx"
      enable-flex="true"
    >
      <!--  -->
      <view class="optionList">
        <!--  -->
        <view :class="['optionList_item', { show: item.isShow }]" v-for="(item, index) in distrubList" :key="index" @click="chooseItem(item.text, index)">
          {{ item.isShow ? item.text : '' }}
        </view>
      </view>
    </scroll-view>
    <view class="down_arrow bottom" v-show="distrubList.length > 12">
      <view @click="scrollAdd(2)"></view>
    </view>
    <!-- 下一题 -->
    <!-- isSubmit/本题是否完成提交 --true完成 -->
    <view class="nextQuestion right" v-if="isSubmit" @click="nextQuestion()">{{ qIdIndex + 1 >= showListData.length ? '完成' : '下一题' }}</view>
    <view :class="isComplete ? 'nextQuestion right' : 'nextQuestion'" v-else @click="submitAnswer()">{{ '提交' }}</view>
    <!--结束弹窗 -->
    <uni-popup ref="popopPower" type="center" :maskClick="false" :classBG="''">
      <interesting-dialog
        :conclusionText="'太棒了，成功通关！看下战绩如何吧~'"
        :finishData="[
          { name: '题目数量', data: reportData.topicNum, iconIndex: 0 },
          { name: '正确率', data: (reportData.accuracy || 0).toFixed(1) + '%', iconIndex: 1 }
        ]"
        word="word"
        :errorList="reportData.errorWordList"
        :showData="showData"
        @close="closeDialog"
        @confirm="onceMore"
      ></interesting-dialog>
    </uni-popup>
    <!-- 退出弹框 -->
    <uni-popup ref="popopPowerExit" type="bottom" :maskClick="false" :classBG="''">
      <pyf-dialog-exit title="是否退出答题？" @close="closePopopPowerExit" @confirm="submitBackPage()"></pyf-dialog-exit>
    </uni-popup>
    <!-- 是否查看报告 -->
    <uni-popup ref="popopPowerIsContinue" type="center" :maskClick="false">
      <view class="popopPower_isContinue">
        <view class="lessonitme_title"></view>
        <view>题目已答完，是否要查看答题报告？</view>
        <view class="popopPower_isContinue_bot">
          <view class="popopPower_isContinue_cancel" @click="submitBackPage"></view>
          <view class="popopPower_isContinue_confirm" @click="endDialog"></view>
        </view>
      </view>
    </uni-popup>
    <!-- 引导 -->
    <uni-popup ref="guideOne" type="bottom" :maskClick="true" :classBG="''">
      <view style="position: relative">
        <image style="height: 100vh; width: 100vw" src="https://document.dxznjy.com/course/d6ad650a394a4c16bf7daf222dff1dae.png"></image>
        <image class="guide_btn_next" @click="guideNext()" src="https://document.dxznjy.com/applet/newInteresting/interesting_guide_next.png"></image>
      </view>
    </uni-popup>
    <uni-popup ref="guideTwo" type="bottom" :maskClick="true" :classBG="''">
      <view style="position: relative">
        <image style="height: 100vh; width: 100vw" src="https://document.dxznjy.com/course/986c2ce6bec34a03b99df563a85c2dfe.png"></image>
        <image class="guide_btn_close" @click="guideClose()" src="#"></image>
      </view>
    </uni-popup>
    <!-- 处理所有返回 -->
    <page-container v-if="showPage" :show="showPage" :duration="false" :overlay="false" @beforeleave="beforeleave"></page-container>
  </div>
</template>

<script>
  import interestingHead from '../components/interesting-head/pyfInterestingHead.vue';
  import interestingDialog from '../components/interesting-dialog/pyfReview.vue';
  import pyfProgress from '../components/cmd-progress/pyf-progress.vue';
  import pyfDialogExit from '../components/interesting-dialog/pyfDialogExit.vue';
  // 一些共同方法
  import { countStudyDuration, downFun, submitData, getNoLevelData, getWordversion, getHeight, playWord, resetAudioContext, beforeleave, handleReset } from './pyfUtils';

  export default {
    components: {
      interestingHead,
      interestingDialog,
      pyfProgress,
      pyfDialogExit
    },
    data() {
      return {
        titleText: '拼拼乐',
        downTimeCount: 60, // 倒计时默认时间
        showDownTime: this.downTimeCount, // 倒计时显示时间
        countdown: null, // 倒计时定时器

        // 滚动
        scrollTop: 0,
        scrollTop2: 0,
        old: {
          scrollTop: 0,
          scrollTop2: 0
        },

        answerList: [], //当前答题选项/上方
        distrubList: [], //当前题目选项/下方
        showListData: [], // 当前题目所有数据
        showData: {}, //当前课程所有数据
        reportData: {}, // 报告数据

        qIdIndex: null, //当前题目编号
        isSubmit: false, //本题是否提交
        isLoading: false, //是否加载中/防止重复点击

        isEnd: false, //当前玩法是否结束
        isSended: false, //是否发送过请求

        successList: [], //正确列表数据存储
        errorList: [], //错误列表数据存储
        // correctRate: 0, //正确率

        isGuide: 0, //0未显示过引导-下一步 1知道了-已完成引导 2已完成引导
        screenHeight: 0,
        screenWidth: 0,
        showPage: true, // 返回保护

        timbre: 'W', // 音色默认女声 M  W
        pronunciationType: 0, // 1英式  0美式  默认美式
        playType: 2 // 版本
      };
    },
    computed: {
      // // 判断当前题目 是否完成 /是否可以提交
      isComplete() {
        // console.log('this.answerList', this.answerList);
        return this.answerList.every((item) => item);
      }
  },
    onLoad(options) {
      console.log(options, 'options');

      getHeight(this);
      // 获取当前课程信息
      this.showData = options;

      // 获取当前学员设置的语音版本信息
      getWordversion(this);
      // 获取引导进度
      this.isGuide = uni.getStorageSync('PyfPplGuide');
      console.log(this.isGuide, 'this.isGuide');

      if (!this.isGuide) {
        this.isGuide = 0;
      }
      if (this.isGuide == 0) {
        setTimeout(() => {
          this.$refs.guideOne.open();
        }, 100);
      }
      if (this.isGuide == 1) {
        setTimeout(() => {
          this.$refs.guideTwo.open();
        }, 100);
      }
      if (this.isGuide == 2) {
        this.getShowData();
      }
    },
    methods: {
      scrollAdd(type) {
        if (type === 1) return (this.scrollTop = this.old.scrollTop += 20);
        this.scrollTop2 = this.old.scrollTop2 += 20;
      },
      scrollView(e) {
        this.old.scrollTop = e.detail.scrollTop;
      },
      scrollView2(e) {
        this.old.scrollTop2 = e.detail.scrollTop;
      },
      getShowData() {
        let that = this;

        this.showData.studyStatus = this.showData.studyStatus * 1; // 学习状态： 0-开始学习；1-继续学习；2-再来一轮
        this.showData.passType = 2; // 关卡类型： 1-听音识词；2-拼拼乐；3-连连看；4-规则大闯关
        this.showData.studyStartTime = uni.$u.timeFormat(new Date(), 'yyyy-mm-dd hh:MM:ss'); // 学习开始时间
        // this.showData.studyDuration = uni.getStorageSync('PyfLlkStudyDuration') || 0; // 学习时长
        this.showData.studyDuration = 0; // 学习时长
        // 获取题目数据
        getNoLevelData(that);
      },
      //题目执行 打乱顺序
      wordExecute() {
        let that = this;
        // 添加倒计时
        downFun(that); // 倒计时
        // 打乱顺序
        // 1.wordRemarkList
        // wordRemark = 'bag-b-a-b,ag'
        let wordRemark = that.showListData[that.qIdIndex].wordRemark.split('-');
        // temp值为兼容次重音定长短多拼接-问题，若没有次重音定长短则表示分词在-拆出来的最后一个里面，若有次重音定长短则在倒数第二个
        let temp = 1;
        temp = that.showListData[that.qIdIndex].syllableList.find((e) => e.wordSyllableType == 7 && e.syllableList.length) ? 2 : 1;
        let wordRemark2 = wordRemark[wordRemark.length - temp].split('/')[0];
        wordRemark2 = wordRemark2.split('&')[0];
        let wordRemarkList = wordRemark2.split(',');
        that.showListData[that.qIdIndex].wordRemarkList = wordRemarkList;
        // let qIdIndexList = JSON.parse(JSON.stringify(wordRemarkList)).sort(() => Math.random() - 0.5);
        let qIdIndexList = JSON.parse(JSON.stringify(wordRemarkList));

        that.$util.shuffleArray(qIdIndexList);
        // 2.splitList
        // let qIdIndexList = JSON.parse(JSON.stringify(that.showListData[that.qIdIndex].splitList)).sort(() => Math.random() - 0.5);
        console.log(qIdIndexList, 'qIdIndexList');

        // 重置答题列表和选项列表
        // that.answerList.length = that.showListData[that.qIdIndex].List.length;
        (that.distrubList = []), (that.answerList = []);
        qIdIndexList.forEach((item) => {
          // 1.wordRemarkList
          that.distrubList.push({ text: item, isShow: true });
          // 2.splitList
          // that.distrubList.push({ text: item.wordSyllable, isShow: true });
          that.answerList.push(null);
        });
        that.isSubmit = false; // 重置提交状态
        that.isLoading = false; // 防止重复点击
        // 读题
        that.playWord(that.showListData[that.qIdIndex].wordSyllableAudioUrl, that.showListData[that.qIdIndex].splitList);
      },
      // 本题是否提交完毕 或 选项是否已空
      isSubmissionCompleted(condition) {
        let that = this;

        if (that.isSubmit) {
          that.$util.alter('作答已结束，请点击下一题');
          return false;
        }
        if (condition) {
          that.$util.alter('该选项已经空了');
          return false;
        }
        return true;
      },
      // 选择下面项
      chooseItem(item, index) {
        let that = this;
        let res = that.isSubmissionCompleted(!that.distrubList[index].isShow);
        if (!res) {
          return;
        }
        that.distrubList[index].isShow = false;
        for (let i = 0; i < that.answerList.length; i++) {
          if (that.answerList[i] == null) {
            // that.answerList[i] = item;
            that.answerList.splice(i, 1, item);
            break;
          }
        }
      },
      // 选择上面项
      chooseAnswerItem(item, index) {
        let that = this;

        let res = that.isSubmissionCompleted(that.answerList[index] == null);
        if (!res) {
          return;
        }
        // that.answerList[index] = null;
        that.answerList.splice(index, 1, null);
        for (let i = 0; i < that.distrubList.length; i++) {
          if (that.distrubList[i].text == item && !that.distrubList[i].isShow) {
            that.distrubList[i].isShow = true;
            break;
          }
        }
      },
      // 下一题
      nextQuestion() {
        let that = this;
        // 判断是否结束
        if (this.isEnd) {
          this.$util.alter('当前玩法已结束');
          return;
        }
        // 节流
        if (that.isLoading || !that.isSubmit) {
          return that.$util.alter('题目加载中，请勿频繁点击');
        } else {
          that.isLoading = true;
        }
        // 删除倒计时定时器
        clearInterval(that.countdown);
        that.countdown = null;

        that.old.scrollTop = 0; // 重置滚动距离
        that.old.scrollTop2 = 0; // 重置滚动距离

        if (that.qIdIndex < that.showListData.length - 1) {
          setTimeout(function () {
            that.qIdIndex++;
            that.wordExecute(); // 执行题目
          }, 0);
        } else {
          // 计算学习时长
          countStudyDuration(that, 2);
          //提交趣味复习单词
          that.endDialog();
        }
      },
      // 提交问题
      submitAnswer(over = false) {
        let that = this;
        console.log(
          this.answerList.every((item) => item),
          'this.answerList.every((item) => item)'
        );

        if (!that.isComplete && !over) {
          that.$util.alter('请完成所有选项');
          return;
        }
        that.isSubmit = true; // 已提交
        // 删除倒计时定时器
        clearInterval(that.countdown);
        that.countdown = null;
        let Answer = that.answerList.join('');

        let isTrue = Answer == that.showListData[that.qIdIndex].wordSyllable;
        // console.log(isTrue, 'isTrue', that.showListData[that.qIdIndex].wordSyllable, Answer);
        resetAudioContext(); // 重置音频上下文
        //是否正确
        if (isTrue) {
          //  console.log("回答正确")
          that.$playVoice('task_success.mp3', true, that.timbre, that.pronunciationType, that.playType, that.showData.studentCode);
          that.isRight = true;
          // 标记正确单词 + 下一题
          that.markRightWord();
          return;
        }

        //回答错误
        //  console.log("回答错误")
        that.$playVoice('ino.mp3', true, that.timbre, that.pronunciationType, that.playType, that.showData.studentCode);
        that.isRight = false;
        // 标记错误单词
        that.markWrongWord();
      },
      // 结束弹窗
      endDialog() {
        let that = this;
        that.isEnd = true;

        // 计算正确率 --弃用
        // if (that.successList.length + that.errorList.length == 0) {
        //   that.correctRate = 0;
        // } else if (that.successList.length != 0 && that.errorList.length == 0) {
        //   that.correctRate = 100;
        // } else {
        //   that.correctRate = ((that.successList.length * 100) / (that.successList.length + that.errorList.length)).toFixed(1);
        //   console.log(that.correctRate, '正确率');
        //   console.log(that.successList.length, that.errorList.length);

        //   if (isNaN(that.correctRate)) {
        //     that.correctRate = 0;
        //   }
        // }
        // 提交答题数据
        submitData(that);
        that.$refs.popopPowerIsContinue.close();
      },
      // 标记错误单词
      markWrongWord() {
        let that = this;

        // 错误列表中添加已答错的单词
        let successItem = that.showListData[that.qIdIndex];
        that.errorList.push(successItem);
        // console.log('标记错误单词', that.errorList);
        // 保存答题数据
        that.showData.pdFunReviewStudyWordSaveDtoList[that.qIdIndex].studyStatus = 1; // 是否学习：0-未学 1-已学
        that.showData.pdFunReviewStudyWordSaveDtoList[that.qIdIndex].answerStatus = 0; // 做题对错状态： 0-错；1-正确
      },
      // 标记正确单词
      markRightWord() {
        let that = this;

        // 正确列表中添加已答对的单词
        let successItem = that.showListData[that.qIdIndex];
        that.successList.push(successItem);
        // 保存答题数据
        that.showData.pdFunReviewStudyWordSaveDtoList[that.qIdIndex].studyStatus = 1; // 是否学习：0-未学 1-已学
        that.showData.pdFunReviewStudyWordSaveDtoList[that.qIdIndex].answerStatus = 1; // 做题对错状态： 0-错；1-正确
        // console.log('标记正确单词', that.errorList);
      },

      //播放音频
      playWord(word, wordList) {
        playWord(this, word, wordList);
      },
      // 查看报告弹框关闭
      closeDialog() {
        this.showPage = false;
        uni.navigateBack({ delta: 1 });

        this.$refs.popopPower.close();
      },
      // 查看报告弹框确定
      onceMore() {
        // 重置
        handleReset(this, this.showData.passType);
        this.showData.studyStatus = 2; // 学习状态： 0-开始学习；1-继续学习；2-再来一轮
        getNoLevelData(this);
        this.$refs.popopPower.close();
      },
      closePopopPowerExit() {
        this.showPage = true;
        this.$refs.popopPowerExit.close();
      },
      // 返回上一页
      backPage() {
        uni.navigateBack({ delta: 1 });
      },
      /** 提交答题数据并返回上一页。
       * 提交答题数据并返回上一页。
       * 该方法调用 submitData 函数来处理数据提交。
       */
      submitBackPage(isSystemBack) {
        this.$refs.popopPowerIsContinue.close();
        // 计算学习时长
        countStudyDuration(this, 2);
        submitData(this, false, isSystemBack); // 提交答题数据并返回上一页
      },
      // 返回
      beforeleave() {
        // 最后一道题做完提示是否查看报告
        if (this.isSubmit && this.qIdIndex + 1 >= this.showListData.length) return beforeleave(this, 2);
        beforeleave(this);
      },
      // 引导
      async guideNext() {
        this.isGuide = 1;
        await uni.setStorageSync('PyfPplGuide', this.isGuide);
        this.$refs.guideOne.close();
        this.$refs.guideTwo.open();
      },
      async guideClose() {
        this.isGuide = 2;
        await uni.setStorageSync('PyfPplGuide', this.isGuide);
        this.$refs.guideTwo.close();
        this.getShowData();
      }
    },

    onUnload() {
      this.submitBackPage(true);
      //  console.log("--页面关闭后销毁实例--");
      // 页面关闭后销毁实例
      resetAudioContext(); // 重置音频上下文
      // 提交答题数据 /未提交 且 已作答 可以提交
      // if (!this.isSended && (this.errorList.length > 0 || this.successList.length > 0)) submitData(this);
      // 销毁定时器
      // 1.清除倒计时定时器
      clearInterval(this.countdown);
      this.countdown = null;
      // 2.清除学习时长定时器
      // clearInterval(this.studyDurationTimer);
      // this.studyDurationTimer = null;
    }
  };
</script>
<style>
  page {
    height: 100vh;
    padding: 0;
  }
</style>
<style lang="scss">
  $font-size: 28rpx;
  $color: #70cc0f;
  $color-b: #555;
  $nextQuestion: 'https://document.dxznjy.com/course/876615d6850949d88ead31f147887b7b.png';
  $nextQuestionWrong: 'https://document.dxznjy.com/course/21c6f6df0fef494f9d0ea7b5ab456598.png';

  .funContent {
    width: 100%;
    padding: 0 30rpx;
    box-sizing: border-box;
    height: 100%;

    background: url('https://document.dxznjy.com/course/1b54a3bf41904a41b4b611f5ac131938.png') 100% 100% no-repeat;
    background-size: 100% 100%;
    position: relative;
    overflow: hidden;
  }

  .interesting_title {
    width: 100%;
    // height: 250rpx;
    height: 15.5vh;
    position: relative;
    z-index: 22;
    margin: 8vh auto 1vh;
    display: grid;
    place-items: center;
  }

  .down_arrow {
    // position: absolute;
    // top: 39%;
    // right: 14rpx;
    width: 100%;
    height: 40rpx;
    display: grid;
    place-items: center;
    &.bottom {
      position: absolute;
      bottom: 150rpx;
      left: 0;
    }
    view {
      width: 50rpx;
      height: 40rpx;
      background: url('https://document.dxznjy.com/course/6abf58d98fc348a886572be122c706d3.png') 100% 100% no-repeat;
      background-size: 100% 100%;
    }
  }
  .interesting_title view {
    position: absolute;
    top: 0;
    left: 0;
    width: 238rpx;
    // height: 215rpx;
    height: 85%;

    background: url('https://document.dxznjy.com/course/e9f9ba08464c484e99e47111bad1d7d1.png') 100% 100% no-repeat;
    background-size: 100% 100%;
  }

  .title_icon {
    width: 220rpx;
    // height: 120rpx;
    height: 48%;
    top: 40rpx;
    left: 242rpx;
    position: absolute;
    background: url('https://document.dxznjy.com/course/fdb94bf8044444abb3c76799a6303783.png') 100% 100% no-repeat;
    background-size: 100% 100%;
  }

  /deep/.cmd-progress-anim {
    background-image: none !important;
  }
  .countdown {
    position: absolute;
    top: 200rpx;
    right: 27rpx;
    font-size: 36rpx;
    color: #d4d195;
    text-align: center;
  }

  .guide_btn_next {
    position: absolute;
    bottom: 60rpx;
    right: 64rpx;
    width: 269rpx;
    height: 142rpx;
  }

  .guide_btn_close {
    position: absolute;
    bottom: 171rpx;
    right: 125rpx;
    width: 312rpx;
    height: 93rpx;
  }

  .answerListBox {
    width: 100%;
    display: flex;
    justify-content: space-evenly;
    flex-wrap: wrap;

    .answerList:nth-child(4n) {
      margin-right: 0;
    }
  }

  .answerList {
    flex-shrink: 0;
    position: relative;
    width: calc(25% - 19rpx);
    height: 88rpx;
    margin-top: 20rpx;
    text-align: center;
    box-sizing: border-box;
    font-weight: bold;
    color: $color-b;
    display: grid;
    place-items: center;
    font-size: 40rpx;
    margin-right: 24rpx;
    padding-bottom: 10rpx;
    border-bottom: 5rpx solid #70cc0f;

    // width: auto;

    view {
      width: 100%;
      height: 100%;
      line-height: 50rpx;
      padding: 5rpx 0;
      box-sizing: border-box;

      // width: auto;
      // padding: 0 50rpx;

      &.answerRight {
        color: #fff;
        background: url('https://document.dxznjy.com/course/54ffaa950fe7480caae1d782eedd1f8a.png') 100% 100% no-repeat;
        background-size: 100% 100%;
      }

      &.answerWrang {
        color: #fff;
        background: url('https://document.dxznjy.com/course/4bb71b95da8e46d5b4b14f450a4bc7d4.png') 100% 100% no-repeat;
        background-size: 100% 100%;
      }

      &.answerNormal {
        background: url('https://document.dxznjy.com/course/cf0f9f94d27b4dd393d775229366c86c.png') 100% 100% no-repeat;
        background-size: 100% 100%;
      }
    }
  }
  .translationBox {
    position: absolute;
    top: 52%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    font-weight: bold;
    font-size: 50rpx;
    color: $color;
  }

  .optionListBox {
    position: absolute;
    bottom: 204rpx;
    width: calc(100% - 60rpx);
    overflow: hidden;

    box-sizing: border-box;
    padding: 34rpx 24rpx;
    background: url('https://document.dxznjy.com/course/e5e4ff3fdedf45e9b6ec0356ae745947.png') 100% 100% no-repeat;
    background-size: 100% 100%;
  }
  .optionList {
    width: 100%;
    display: flex;
    justify-content: space-evenly;

    flex-wrap: wrap;
    margin: 0 auto;

    .optionList_item:nth-child(4n) {
      margin-right: 0;
    }
  }

  .optionList_item {
    flex-shrink: 0;
    position: relative;
    width: calc(25% - 22rpx);
    height: 70rpx;
    margin-bottom: 45rpx;
    margin-right: 26rpx;
    text-align: center;
    background-color: #add582;

    // line-height: 220rpx;
    box-sizing: border-box;
    border-radius: 24rpx;
    font-weight: bold;
    color: $color-b;
    line-height: 55rpx;
    font-size: 36rpx;

    // width: auto;
    // padding: 0 50rpx;
    &.show {
      background: url('https://document.dxznjy.com/course/8e8733779af34331bee2f8e3f2d48467.png') 100% 100% no-repeat;
      background-size: 100% 100%;
    }
  }
  .nextQuestion {
    position: absolute;
    bottom: 48rpx;
    left: 50%;
    transform: translateX(-50%);
    width: 630rpx;
    height: 84rpx;
    background-color: #a4adb3;
    border-radius: 50rpx;

    color: #fff;
    font-size: 30rpx;
    font-weight: bold;

    display: grid;
    place-items: center;

    &.right {
      background: url($nextQuestion) 100% 100% no-repeat;
      background-size: 100% 100%;
    }
    &.wrong {
      background: url($nextQuestionWrong) 100% 100% no-repeat;
      background-size: 100% 100%;
    }
  }
  .popopPower_isContinue {
    position: relative;
    width: 632rpx;
    height: 364rpx;
    line-height: 324rpx;
    font-size: $font-size;

    text-align: center;
    background: url('https://document.dxznjy.com/course/2322c002582f430b806ca1a4781122f6.png') 100% 100% no-repeat;
    background-size: 100% 100%;
    // 小鸟
    .lessonitme_title {
      position: absolute;
      top: -70rpx;
      right: 40rpx;
      width: 70rpx;
      height: 70rpx;
      color: #555555;
      line-height: 334rpx;
      background: url('https://document.dxznjy.com/course/d0b884bc62b84fbbbceba1c1a9be96c8.png') 100% 100% no-repeat;
      background-size: 100% 100%;
    }
    .popopPower_isContinue_bot {
      position: absolute;
      bottom: 40rpx;
      right: 50%;
      transform: translateX(50%);
      width: 530rpx;
      height: 84rpx;

      display: flex;
      justify-content: space-between;
    }
    .popopPower_isContinue_confirm {
      width: 252rpx;
      height: 84rpx;
      background: url('https://document.dxznjy.com/course/f065a5323c7b4e7eaea1c556a4777dc0.png') 100% 100% no-repeat;
      background-size: 100% 100%;
    }
    .popopPower_isContinue_cancel {
      width: 252rpx;
      height: 84rpx;
      background: url('https://document.dxznjy.com/course/25eb8f70457d4691823b09f185fc5e2f.png') 100% 100% no-repeat;
      background-size: 100% 100%;
    }
  }
  .scroll_view {
    max-height: 14vh;
  }
  @media (min-width: 500px) {
    .scroll_view {
      max-height: 11vh;
    }
    .interesting_title {
      margin: 6vh auto 0;
    }
    .interesting_title view {
      top: 10%;
      left: 5%;
      width: 169rpx;
    }
    .title_icon {
      width: 171rpx;
      height: 58%;
    }
  }
</style>
