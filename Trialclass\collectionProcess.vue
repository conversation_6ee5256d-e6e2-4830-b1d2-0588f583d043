<template>
	<view>
		<image :src="imgHost+'alading/correcting/flowpath_icon.png'" class="flowpath_icon" mode="widthFix"></image>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				imgHost: getApp().globalData.imgsomeHost,
				useWidth: 0,
			};
		},
		onReady() {
			uni.getSystemInfo({
				success: (res) => {
					// 可使用窗口高度，将px转换rpx
					// let h = (res.windowHeight * (750 / res.windowWidth));
					this.useWidth = res.screenWidth;
				}
			})
		},
	}
</script>

<style lang="scss">
	.flowpath_icon{
		width: 100%;
	}
</style>
