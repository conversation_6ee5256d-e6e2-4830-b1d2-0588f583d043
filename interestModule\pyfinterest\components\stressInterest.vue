<template>
  <view>
    <view class="word">
      <view style="width: 144rpx; height: 144rpx; margin-right: 10rpx">
        <image src="https://document.dxznjy.com/course/ae33333815ed4628a04f1271d58e13dd.png" style="width: 100%; height: 100%"></image>
      </view>
      <view class="wordTitle">
        {{ word.wordSyllable }}
      </view>
    </view>
    <view class="items">
      <view class="item" v-for="(item, index) in computedList" :class="item.className" @click="checkWord(index)">
        {{ item.text.replace(/\[.*\]$/, '') }}
        <view class="SecondaryStress" v-if="item.isSecondaryStress"></view>
        <view class="Stress" v-if="item.type == 1 || item.type == 2 || item.type == 3"></view>
      </view>
    </view>
    <view class="" style="margin-top: 80rpx" v-if="isOK">
      <view class="rightItems">
        <view
          class="rightItem"
          v-for="(item, index) in computedList"
          :class="item.isStress ? 'right' : ''"
          :style="{
            color: item.type == 4 ? '#c9c9c9' : '#555'
          }"
        >
          {{ item.text.replace(/\[.*\]$/, '') }}
          <view class="Stress" v-if="item.isStress"></view>
          <view class="SecondaryStress" v-if="item.isSecondaryStress"></view>
        </view>
      </view>
    </view>
    <view class="btn" @click="btnOK">{{ isOK ? (end ? '提交' : '下一题') : '确定' }}</view>
    <uni-popup ref="guideOne" type="bottom" :maskClick="true" :classBG="''">
      <view style="position: relative" class="test-bg">
        <image @click="guideNext()" mode="aspectFit" style="width: 100%; height: 100%" src="https://document.dxznjy.com/course/8d6e5c3b2707469c9b33670cf5fe6035.png"></image>
      </view>
    </uni-popup>
  </view>
</template>

<script>
  export default {
    data() {
      return {
        list: [],
        isOK: false,
        status: 0,
        screenHeight: 0,
        screenWidth: 0,
        goNum: 0,
        Guide: uni.getStorageSync('stressGuide')
      };
    },
    props: {
      word: {
        type: Object,
        default: {}
      },
      end: {
        type: Boolean,
        default: false
      }
    },
    computed: {
      computedList() {
        // 为每个 item 计算 className
        return this.list.map((item) => ({
          ...item,
          className: this.getClass(item.type)
        }));
      }
    },
    created() {
      this.init();
      this.goNext();
      this.$emit('setTitle', 2);
    },
    mounted() {
      this.getHeight();
      if (!this.Guide) {
        this.$refs.guideOne.open();
      }
    },
    methods: {
      getHeight() {
        // 获取系统信息
        const systemInfo = uni.getSystemInfoSync();

        // 获取屏幕高度
        this.screenHeight = systemInfo.windowHeight * 2;
        this.screenWidth = systemInfo.windowWidth * 2;
        // // 打印屏幕高度
        //  console.log(this.screenHeight,"打印屏幕高度");
      },
      guideNext() {
        uni.setStorageSync('stressGuide', true);
        this.$refs.guideOne.close();
      },
      goNext() {
        if (this.word.syllableList.find((e) => e.wordSyllableType == 2 && e.syllableList.length)) {
          this.goNum = 4;
        } else if (this.word.syllableList.find((e) => e.wordSyllableType == 3 && e.syllableList.length)) {
          this.goNum = 5;
        } else {
          this.goNum = 6;
          this.$emit('goNum', this.goNum);
        }
      },
      addSuffixToRepeatedSyllables(objects) {
        // 创建一个对象来跟踪每个syllable出现的次数
        const syllableCounts = {};
        return objects.map((obj, index) => {
          const wordSyllable = obj.wordSyllable;
          // 如果syllable已经出现过，则增加计数并添加后缀
          if (syllableCounts[wordSyllable]) {
            syllableCounts[wordSyllable]++;
            return {
              ...obj,
              wordSyllable: `${wordSyllable}[${syllableCounts[wordSyllable]}]`
            };
          } else {
            // 如果是第一次出现，则记录该syllable并设置计数为1
            syllableCounts[wordSyllable] = 1;
            return obj;
          }
        });
      },
      checkWord(e) {
        if (this.list[e].type == 4) return;
        if (this.isOK) return;
        if (this.list[e].type == 0) {
          this.list[e].type = 1;
        } else if (this.list[e].type == 1) {
          this.list[e].type = 0;
        }
      },
      init() {
        let arr = this.addSuffixToRepeatedSyllables(this.word.splitList);

        this.list = arr.map((e, i) => {
          return {
            text: e.wordSyllable,
            type: 4
          };
        });
        this.word.syllableList.forEach((e) => {
          // this.list.find((o) => o.text === e.wordSyllable).isSyllable = true;
          this.list.find((o) => o.text === e.wordSyllable).type = 0;
          if (e.wordSyllableType == 3) {
            this.list.find((o) => o.text === e.wordSyllable).isStress = true;
            this.list.find((o) => o.text === e.wordSyllable).type = 0;
          } else if (e.wordSyllableType == 7) {
            this.list.find((o) => o.text === e.wordSyllable).isSecondaryStress = true;
            this.list.find((o) => o.text === e.wordSyllable).type = 4;
          } else {
          }
        });
      },
      btnOK() {
        if (this.isOK) {
          this.$emit('next', this.status, this.goNum);
        } else {
          if (!this.list.find((e) => e.type == 1)) {
            return uni.showToast({
              icon: 'none',
              title: '请选择'
            });
          }
          this.list.forEach((e, i) => {
            if (e.isStress && e.type == 1) {
              this.list[i].type = 2;
            }
            if (e.type == 1 && !e.isStress) {
              this.list[i].type = 3;
            }
            if (e.isStress && e.type == 0) {
              this.list[i].type = 3;
            }
          });
          this.isOK = true;
          if (this.list.find((p) => p.type == 3)) {
            this.status = 0;
          } else {
            this.status = 1;
          }
        }
      },
      getClass(type) {
        // 根据 type 返回对应的类名
        switch (type) {
          case 1:
            return 'check';
          case 2:
            return 'right';
          case 3:
            return 'error';
          case 4:
            return 'nocheck';
          default:
            return '';
        }
      }
    }
  };
</script>

<style scoped lang="scss">
  .test-bg {
    padding-top: env(safe-area-inset-top);
    // padding-bottom: env(safe-area-inset-bottom);
    height: 100vh;
    box-sizing: border-box;
  }
  .guide_btn_close {
    position: absolute;
    bottom: 179rpx;
    right: 110rpx;
    width: 312rpx;
    height: 93rpx;
  }
  .rightItems {
    width: 686rpx;
    margin: 0 auto;
    max-height: 286rpx;
    overflow-y: auto;
    // height: 190rpx;
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    align-items: center;
    border-radius: 32rpx;
    padding: 20rpx 0;
    background-color: rgba(255, 255, 255, 0.51);
    .rightItem {
      position: relative;
      background-color: #fff;
      color: #555555;
      font-size: 36rpx;
      height: 92rpx;
      line-height: 92rpx;
      margin-right: 8rpx;
      border-radius: 16rpx;
      margin-bottom: 10rpx;
      padding: 0 40rpx;
    }

    .right {
      background-color: #4bb051;
      color: #ffffff;
    }
  }
  .SecondaryStress {
    position: absolute;
    width: 4rpx;
    height: 24rpx;
    line-height: 1;
    background-color: #555555;
    bottom: 14rpx;
    left: 22rpx;
  }
  .btn {
    width: 632rpx;
    height: 84rpx;
    background: #89c844;
    box-shadow: 0 16rpx 2rpx -2rpx rgba(99, 180, 11, 1);
    border-radius: 42rpx;
    text-align: center;
    line-height: 84rpx;
    position: fixed;
    bottom: 50rpx;
    left: 50%;
    transform: translateX(-50%);
  }
  .Stress {
    position: absolute;
    width: 4rpx;
    height: 24rpx;
    line-height: 1;
    background-color: #555555;
    top: 10rpx;
    left: 22rpx;
  }
  .items {
    display: flex;
    padding: 0 32rpx;
    flex-wrap: wrap;
    justify-content: center;
    .item {
      position: relative;
      height: 92.11rpx;
      min-width: 120rpx;
      padding: 0 30rpx;
      border: 2rpx solid transparent;
      box-sizing: border-box;
      font-size: 36rpx;
      // border: 2rpx solid #000;
      margin-right: 14rpx;
      text-align: center;
      background-color: #ffffff;
      color: #555555;
      border-radius: 24rpx;
      line-height: 90rpx;
      box-shadow: 0 16rpx 2rpx -2rpx rgba(97, 170, 244, 1);
      margin-bottom: 34rpx;
    }
    .check {
      border: 2rpx solid #ffc800;
      background-color: #fff7db;
    }
    .right {
      background-color: #4bb051;
      box-shadow: 0 16rpx 2rpx -2rpx rgba(57, 141, 61, 1);
      color: #ffffff;
    }
    .error {
      background-color: #ffa332;
      box-shadow: 0 16rpx 2rpx -2rpx rgba(231, 133, 13, 1);
      color: #ffffff;
    }
    .nocheck {
      color: #c9c9c9;
      // background-color: rgba(187, 187, 187, 0.7);
    }
  }
  .word {
    margin-top: 60rpx;
    height: 144rpx;
    padding-left: 32rpx;
    display: flex;
    align-items: center;
    margin-bottom: 90rpx;
  }
  .wordTitle {
    width: 474rpx;
    height: 110rpx;
    padding-left: 14rpx;
    background: url('https://document.dxznjy.com/course/a37e49a58c724f7ca8d2fbd8ce85252d.png') no-repeat;
    background-size: contain;
    text-align: center;
    line-height: 100rpx;
    font-size: 40rpx;
    color: #555555;
  }
</style>
