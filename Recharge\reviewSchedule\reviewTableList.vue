<template>
	<view class="plr-30">
		<scroll-view v-if="classList.data != undefined && classList.data.length!=0" scroll-y="true"
			:scroll-top="scrollTop" :style="{height:useHeight+'rpx'}" class="ptb-25" @scrolltolower="scrolltolower">
			<view class="detailed" v-for="item in classList.data">
				<view style="line-height: 42rpx;" class="c-00 f-30 bold">学员：{{item.studentName}}</view>
				<view class="code-view">
					<text>编号: {{item.studentCode}}</text>
					<text>已购复习包：{{item.rechargeReviewTime}}分钟</text>
				</view>
				<view class="line-view"></view>
				<view class="option-text">
					<view class="btn-view btn-color-1" @click="showOption(item,false)">
						<text>{{item.isSubmit?'查看':'去填写'}}</text>
					</view>
					<view v-if="item.isSubmit" class="btn-view btn-color-2 ml-30" @click="showOption(item,true)">
						编辑
					</view>
				</view>
				<view :class="item.isSubmit?'green-view':'grey-view'">
					{{ item.isSubmit?'已填写':'未填写' }}
				</view>
			</view>

			<view v-if="no_more && classList.data != undefined && classList.data.length>0">
				<u-divider text="到底了"></u-divider>
			</view>
		</scroll-view>

		<view v-if="classList.data == undefined ||classList.data != undefined && classList.data.length==0"
			class="t-c flex-col bg-ff radius-15" :style="{height: useHeight+'rpx'}">
			<image :src="imgHost+'alading/correcting/no_data.png'" class="mb-20 img_s" mode="widthFix"></image>
			<view style="color: #BDBDBD;">暂无数据</view>
		</view>
	</view>
</template>

<script>
	const {
		$getSceneData,
		$showError,
		$showMsg,
		$http,
	} = require("@/util/methods.js")
	import Util from '@/util/util.js'
	const {
		httpUser
	} = require('@/util/luch-request/indexUser.js')
	export default {
		data() {
			return {
				imgHost: getApp().globalData.imgsomeHost,
				svHeight: 50,
				useHeight: 0, //除头部之外高度

				classList: {},

				scrollTop: 0,
				no_more: false,
				page: 1,
				pageSize: 20,
			};
		},

		onLoad(e) {
			console.log(e);
		},
		onReady() {
			let that = this;
			uni.getSystemInfo({ //调用uni-app接口获取屏幕高度
				success(res) {
					// 可使用窗口高度，将px转换rpx
					let h = (res.windowHeight * (750 / res.windowWidth));
					that.useHeight = h - 30;
				}
			})
		},

		onShow() {
			this.page = 1;
			// this.clickHandle();
			this.getContactList(false);
		},

		methods: {
			async getContactList(isPage) {
				let _this = this;
				uni.showLoading();
				let res = await this.$httpUser.get(
					`deliver/web/student/reviewTime/info/selStudentReviewTimeInfoList?pageNum=${_this.page}&pageSize=${_this.pageSize}`
					);
				uni.hideLoading();
				if (res && res.data) {
					if (isPage) {
						let old = _this.classList.data;
						_this.classList.data = [...old, ...res.data.data.data];
					} else {
						_this.classList = res.data.data;
					}
				}
			},
			// 滚动条回到顶部
			clickHandle() {
				this.scrollTop = this.scrollTop === 0 ? 1 : 0;
			},
			scrolltolower() {
				if (this.page >= this.classList.totalPage) {
					this.no_more = true;
					return false;
				}
				++this.page;
				this.getContactList(true);
			},

			//跳轉
			showOption(item, isEdit) {
				console.log(isEdit);
				console.log(item)
				uni.navigateTo({
					url: '/Recharge/reviewSchedule/index?data=' + JSON.stringify(item) + "&isEdit=" + isEdit
				})
			},
		}
	}
</script>

<style lang="scss" scoped>
	page {
		background-color: #F3F8FC;
	}

	.img_s {
		width: 160rpx;
	}

	.detailed {
		background-color: #fff;
		border-radius: 14rpx;
		margin-bottom: 30rpx;
		position: relative;
		padding: 35rpx 30rpx 20rpx 30rpx;
	}

	.code-view {
		display: flex;
		justify-content: space-between;
		font-size: 30rpx;
		line-height: 42rpx;
		color: #666666;
		margin-top: 20rpx;
	}

	.line-view {
		margin-bottom: 20rpx;
		margin-top: 35rpx;
		height: 2rpx;
		background-color: #EFEFEF;
	}

	.option-text {
		display: flex;
		align-items: center;
		justify-content: flex-end;
		font-size: 30rpx;
		color: #2E896F;
		line-height: 42rpx;
	}

	.green-view {
		position: absolute;
		right: 30rpx;
		top: 38rpx;
		width: 90rpx;
		height: 36rpx;
		background: #2DC032;
		border-radius: 4rpx;
		border: 1rpx solid #2DC032;
		text-align: center;
		font-size: 26rpx;
		color: #FFFFFF;
	}

	.grey-view {
		position: absolute;
		right: 30rpx;
		top: 38rpx;
		text-align: center;
		width: 90rpx;
		height: 36rpx;
		background: #C6C6C6;
		border-radius: 4rpx;
		border: 1rpx solid #C6C6C6;
		font-size: 26rpx;
		color: #FFFFFF;
	}

	.btn-view {
		display: flex;
		align-items: center;
		justify-content: center;
		width: 150rpx;
		height: 60rpx;
		border-radius: 45rpx;
	}

	.btn-color-1 {
		font-size: 30rpx;
		color: #FFFFFF;
		background: linear-gradient(180deg, #88CFBA 0%, #1D755C 100%);
	}

	.btn-color-2 {
		font-size: 30rpx;
		color: #2E896F;
		border: 1rpx solid #2E896F;
	}
</style>