<template>
	<page-meta :page-style="'overflow:' + (show ? 'hidden' : 'visible')"></page-meta>
	<view class="plr-20 pb-30">
		<view class="bg-ff plr-20 pb-40 radius-15 positionRelative" :style="{height:useHeight+'rpx'}">
			<form id="#nform">
				<view class="information ptb-35 borderB">
					<view style="width: 40%;" class="f-30 bold">学员姓名：</view>
					<view style="width: 60%;color: #666;" class="f-30">{{infolist.studentName}}</view>
				</view>
				<view class="information ptb-35 borderB">
					<view style="width: 40%;" class="f-30 bold">学员编号：</view>
					<view style="width: 60%;color: #666;" class="f-30">{{infolist.studentCode}}</view>
				</view>
				<view class="information ptb-35 borderB">
					<view style="width: 40%;" class="f-30 bold">联系方式：</view>
					<view style="width: 60%;color: #666;" class="f-30">{{infolist.phone}}</view>
				</view>
				<view class="information ptb-35 borderB">
					<view style="width: 40%;" class="f-30 bold">年级：</view>
					<view style="width: 60%;color: #666;" class="f-30">{{getGrade(infolist.grade-1)}}</view>
				</view>
				<view class="information ptb-35 borderB">
					<view style="width: 40%;" class="f-30 bold">充值复习包：</view>
					<view style="width: 60%;color: #666;" class="f-30">{{infolist.rechargeReviewTime}}</view>
				</view>
				<view class="ptb-20 borderB">
					<view class="information">
						<view style="width: 70%;" class="f-30 bold">复习时间：</view>
						<view class="uni-list-cell-db pr-20 positionRelative" @click="openReviewTime"
							v-if="!isDisable || isEdit">
							<view style="position: absolute;right: 20rpx;"  v-if="(comfireReviewData.week != undefined 
                                && comfireReviewData.week.length > 0 
                                && comfireReviewData.startTime != undefined 
                                && comfireReviewData.startTime !='') || isEdit">
								<text style="font-size: 30rpx;color: #2E896F;line-height: 42rpx;">编辑</text>
							</view>
							<view class="flex-s" v-else>
								<view class="flex-a-c"><text style="float: left;">请选择</text></view>
								<view class="time-icon">
									<u-icon v-if="!isDisable" name="arrow-right" color="#c7c7c7" size="30"></u-icon>
								</view>
							</view>
						</view>
					</view>
					<view v-if="(comfireReviewData.week != undefined && comfireReviewData.week.length > 0
                        && comfireReviewData.startTime != undefined && comfireReviewData.startTime !='')||isDisable">
						<view class="flex-a-c">
							<view class="mt-30 mb-30 c-33 text-view" v-for="(item,index) in comfireReviewData.week"
								:key="index">
								{{getWeekName(item,index)}}
							</view>
						</view>
						<view class="text-view mb-15" v-if="comfireReviewData.startTime">{{comfireReviewData.startTime}}
						</view>
					</view>
				</view>
				<view class="tips" v-if="!isDisable || isEdit" :style="{height:svHeight+'px'}">
					<button class="phone-btn" @click="sendCourse" :disabled="disabled">确定</button>
				</view>
			</form>
		</view>

		<uni-popup ref="reviewShow" type="bottom" @maskClick="cancelAtion()">
			<view class="dialogBG pt-30 pb-40">
				<view class="top-close">
					<text>复习时间</text>
					<uni-icons type="clear" size="28" color="#B1B1B1" @click="cancelAtion"></uni-icons>
				</view>
				<view class="center-content">
					<view class="chose-week-text">
						<view class="chose-week-item" @click="reviewChoseWeekFunc(item,index)"
							v-for="(item,index) in normalWeekData"
							:class="(reviewChoseWeek.indexOf(index))!=-1 ? 'week-chose' : 'week-normal'">
							{{normalWeekData[index]}}
						</view>
					</view>
					<view class="start-time" @click="reviewStartTimeDialog()">
						<view>
							<text>开始时间</text>
							<text style="font-size: 32rpx;margin-left: 70rpx;">
								{{rewviewdatevalue.length<=0?"请选择":rewviewdatevalue}}</text>
						</view>
						<u-icon name="arrow-right" color="#c7c7c7" size="30"></u-icon>
					</view>
				</view>
				<view class="top-button">
					<button class="radius-50 confirm-button" @click="reviewConfirm()">确定</button>
				</view>
			</view>
		</uni-popup>
		<u-datetime-picker ref="reviewtimePicker" :show="showReviewTime" v-model="rewviewdatevalue" mode="time"
			itemHeight="68" confirmColor="#2e896f" @cancel="cancel" @confirm="reviewDateChange" :immediateChange="true"
			:formatter="formatter">
		</u-datetime-picker>

		<uni-popup ref="tipsPopup" type="center" @change="change">
			<view class="dialog_style">
				<view class="reviewCard_box positionRelative">
					<view class="cartoom_image">
						<image src="https://document.dxznjy.com/dxSelect/dialog_icon.png" mode="widthFix"></image>
					</view>
					<view class="review_close" @click="closeTipsDialog">
						<uni-icons type="clear" size="30" color="#B1B1B1"></uni-icons>
					</view>
					<view class="reviewCard t-c">
						<view class="reviewTitle bold pb-20">温馨提示</view>
						<view class="lh-50 mt-40">首次开课的学员必须填写上课信息对接表后，才可正常排课上课。</view>
						<view class="flex-s flex-x-s-e mt-60">
							<view class="review_btn" @click="skintap('Recharge/onlineJoinTable/onlineJoinTable?orderId='+ orderId+'&payStatus='+0)">
								填写上课信息表</view>
							<view class="close_btn" @click="goBack">{{isPayMent?'返回继续充值':'取消'}}</view>
						</view>
					</view>
				</view>
			</view>
		</uni-popup>
	</view>
</template>

<script>
	const {
		httpUser
	} = require('@/util/luch-request/indexUser.js')
	const {
		$navigationTo,
		$http,
		$showError
	} = require("@/util/methods.js")
	import dayjs from "dayjs"
	export default {
		data() {
			return {
				show: false, // 禁止穿透

				svHeight: 50,
				useHeight: 0, //除头部之外高度

				infolist: {}, // 回显信息
				orderId: '',
				payStatus: '', //0填写   1查看
				isDisable: false, //是否禁用

				//是否试过课
				noExp: 0,
				yesExp: 0,
				isExp: "",
				//新生
				yesNew: 0,
				noNew: 0,
				isNewStudent: "",
				remark: '', // 体验需求

				flag: false, // 防止重复点击
				disabled: false,

				imgHost: getApp().globalData.imgsomeHost,

				localWordBase: -1,

				showDialog: false,
				normalWeekData: '周一_周二_周三_周四_周五_周六_周日'.split('_'),

				///复习
				comfireReviewData: {},
				reviewChoseWeek: [], //复习选择的星期
				rewviewdatevalue: "",
				showReviewTime: false,

				//上课
				comfireCourseData: [],
				courseChoseWeek: [], //上课选择的星期 [{week:"周一", time:[{startTime:"",endTime:""}]},...]
				showCourseTime: false,

				curChoseItem: null,
				curChoseTimeIndex: null,
				curChoseStartFlag: false,

				//课程规划
				// courseChoseList:[],

				gradeNameArr: "一年级_二年级_三年级_四年级_五年级_六年级_初一_初二_初三_高一_高二_高三_大一_大二_大三_大四_其他".split("_"),

				isEdit: false,
				changeRemark: false,
				changeNewStudent: false,
				changeExp: false,
				changeReview: false,
				changeStudy: false,

				firstStudyTime: "",
				firstWeek: "",
				firstTime: "",

				reviewTimeShow: false,
				studentCode: '',
				isPayMent: false, // 是否从付款页面跳转来的
			};
		},
		watch: {
			isNewStudent(newVal, oldVal) {
				console.log("isNewStudent修改");
				if (this.isEdit && this.firstStudyTime) {
					this.changeNewStudent = this.isNewStudent != this.infolist.isNewStudent;
					this.setChangeFirstTime()
				}
			},

			isExp(newVal, oldVal) {
				console.log("isExp修改");
				if (this.isEdit && this.firstStudyTime) {
					this.changeExp = this.isExp != this.infolist.isExp;
					this.setChangeFirstTime()
				}
			},

			remark(newVal, oldVal) {
				console.log("remark修改");
				if (this.isEdit && this.firstStudyTime) {
					this.changeRemark = this.remark != this.infolist.remark;
					this.setChangeFirstTime()
				}
			},
		},

		onLoad(e) {
			if (e.data != undefined && e.data != null) {
				let data = JSON.parse(e.data);
				this.orderId = data.id;
				this.studentCode = data.studentCode;
				this.payStatus = data.isSubmit;
				this.isDisable = this.payStatus == 0 ? false : true;
				this.isEdit = e.isEdit == "false" ? false : true;
			}

			if (e.orderId != undefined && !e.payMent) {
				this.orderId = e.orderId;
			}

			if (e.payMent != undefined && e.payMent) {
				console.log('进入')
				this.isPayMent = true;
				this.studentCode = e.studentCode;
				this.orderId = e.orderId;
				this.isEdit = false;
			}
			//标题
			let title = "复习时间表"
			if(this.payMent){
				title = "填写复习时间表"
			}else{
				if (this.isEdit) {
					title = "修改复习时间表"
				} else {
					title = "查看复习时间表"
					if (this.payStatus == 0) {
						title = "填写复习时间表"
					}
				}
			}
			
			uni.setNavigationBarTitle({
				title: title
			});
			
			if (this.isPayMent) {
				console.log(this.isPayMent)
				this.getOrderId();
			}else{
				this.getCourseinfo();
			}
		},
		onReady() {
			let that = this;
			uni.getSystemInfo({ //调用uni-app接口获取屏幕高度
				success(res) {
					// 可使用窗口高度，将px转换rpx
					let h = (res.windowHeight * (750 / res.windowWidth));
					that.useHeight = h - 70;
				}
			})
			// 微信小程序需要用此写法
			this.$refs.reviewtimePicker.setFormatter(this.formatter)
			// this.$refs.coursetimePicker.setFormatter(this.formatter)
		},

		onShow() {
			// this.$refs.tipsPopup.open();
			
			// uni.$on('coursePlan',function(data){
			// 	that.courseChoseList = data;
			// })
			
		},
		methods: {
			closeTipsDialog() {
				this.$refs.tipsPopup.close();
			},

			skintap(url) {
				$navigationTo(url);
			},

			change(e) {
				this.show = e.show;
			},

			setChangeFirstTime() {
				console.log("修改");
				let isneed = this.changeExp || this.changeNewStudent ||
					this.changeRemark || this.changeReview ||
					this.changeStudy;
				if (isneed) {
					this.getFirstStudyTime();
				} else {
					if (this.isEdit && this.firstStudyTime) {
						this.getFormatToService(this.infolist.firstTime, this.infolist.firstWeek)
					} else {
						this.firstTime = "";
						this.firstStudyTime = "";
						this.firstWeek = "";
					}
				}
			},
			formatter(type, value) {
				if (type === 'hour') {
					return `${value}时`
				}
				if (type === 'minute') {
					return `${value}分`
				}
				return value
			},

			cancel() {
				this.showReviewTime = false;
				this.showCourseTime = false;
			},
			////////Start////
			// 复习开始时间选择
			reviewDateChange: function(e) {
				this.showReviewTime = false;
				this.$forceUpdate()
			},
			//复习时间--开始时间
			reviewChoseWeekFunc(item, index) {
				let weelIndex = this.reviewChoseWeek.indexOf(index);
				if (weelIndex == -1) {
					this.reviewChoseWeek.push(index)
				} else {
					this.reviewChoseWeek.splice(weelIndex, 1);
				}
			},

			//显示时间
			reviewStartTimeDialog() {
				this.showReviewTime = true;
			},

			reviewConfirm() {
				if (this.reviewChoseWeek.length == 0 && this.rewviewdatevalue.length > 0) {
					uni.showToast({
						icon: "none",
						title: "请选择星期"
					})
					return
				}
				if (this.reviewChoseWeek.length > 0 && this.rewviewdatevalue.length <= 0) {
					uni.showToast({
						icon: "none",
						title: "请选择开始时间"
					})
					return
				}
				this.reviewChoseWeek.sort((a, b) => a - b);
				this.comfireReviewData = {
					week: this.reviewChoseWeek,
					startTime: this.rewviewdatevalue
				}
				this.$refs.reviewShow.close();
				this.showDialog = false;
				this.reviewTimeShow = true;
				if (this.isEdit && this.firstStudyTime) {
					this.changeReview = true;
					this.setChangeFirstTime()
				}
			},
			openReviewTime() {
				this.$refs.reviewShow.open();
				this.showDialog = true;
			},
			cancelAtion() {
				this.$refs.reviewShow.close();
				this.showDialog = false;
			},
			///复习时间/////End//////

			/////开始时间////START//////////////
			//修改复习表
			async editOption() {
				let _this = this;
				uni.showLoading();
				let data = {
					id: _this.orderId,
					orderNo: _this.infolist.orderNo,
					studentCode: _this.infolist.studentCode,
					merchantCode: _this.infolist.merchantCode,
					studentName: _this.infolist.studentName,
					reviewTime: _this.comfireReviewData.startTime,
					reviewWeek: JSON.stringify(_this.comfireReviewData.week)
				};
				try {
					let res = await $http({
						url: 'deliver/web/student/reviewTime/info/updateStudentReviewTimeInfo',
						method: 'POST',
						data: data
					})
					if (res) {
						uni.navigateBack()
						_this.flag = false;
						_this.disabled = false;
					} else {
						_this.flag = false;
						_this.disabled = false;
					}
				} catch (e) {
					_this.flag = false;
					_this.disabled = false;
				}
				uni.hideLoading()
			},

			//新增上课对接信息表
			async sendCourse() {
				let _this = this;
				if (_this.flag) {
					return;
				}
				_this.flag = true;
				_this.disabled = true;
				if (_this.comfireReviewData.week == undefined || _this.comfireReviewData.week.length <= 0 ||
					_this.comfireReviewData.startTime == undefined || _this.comfireReviewData.startTime == "") {
					_this.flag = false;
					_this.disabled = false;
					return $showError('请填写复习时间')
				}

				//修改上课信息对接表
				if (_this.isEdit) {
					_this.editOption();
					return;
				}
				uni.showLoading();
				let data = {
					id: _this.orderId,
					orderNo: _this.infolist.orderNo,
					studentCode: _this.infolist.studentCode,
					merchantCode: _this.infolist.merchantCode,
					studentName: _this.infolist.studentName,
					reviewTime: _this.comfireReviewData.startTime,
					reviewWeek: JSON.stringify(_this.comfireReviewData.week)
				};
				try {
					let res = await $http({
						url: 'deliver/web/student/reviewTime/info/submitStudentReviewTime',
						method: 'POST',
						data: data
					})
					if (res) {
						uni.hideLoading()
						if (!res.data) {
							_this.$refs.tipsPopup.open();
							return;
						}
						uni.navigateBack();
						_this.flag = false;
						_this.disabled = false;
					} else {
						uni.hideLoading()
						_this.flag = false;
						_this.disabled = false;
					}
				} catch (e) {
					uni.hideLoading()
					_this.flag = false;
					_this.disabled = false;
				}
			},

			goBack() {
				if (this.isPayMent) {
					uni.navigateBack({ //redirectTo
						delta: 2
					})
				} else {
					this.$refs.tipsPopup.close();
				}
			},

			//[{week:"周一", time:[{startTime:"",endTime:""}]},...]
			//=>
			// {"endTime": "string","startTime": "string","usableHourEnd": 0,
			// "usableHourStart": 0,"usableMinuteEnd": 0,"usableMinuteStart": 0,"usableWeek": 0
			getStudyTimeList() {
				let dataArr = [];
				for (let i = 0; i < this.comfireCourseData.length; i++) {
					for (let j = 0; j < this.comfireCourseData[i].time.length; j++) {
						let startTimeArr = this.getSplitHour(this.comfireCourseData[i].time[j].startTime, ":");
						let endTimeArr = this.getSplitHour(this.comfireCourseData[i].time[j].endTime, ":");
						let data = {
							endTime: "",
							startTime: "",
							usableHourEnd: Number(endTimeArr[0]),
							usableMinuteEnd: Number(endTimeArr[1]),
							usableHourStart: Number(startTimeArr[0]),
							usableMinuteStart: Number(startTimeArr[1]),
							usableWeek: this.comfireCourseData[i].week
						}
						dataArr.push(data);
					}
				}
				return dataArr;
			},

			getCourseDataFormServer() {
				let dataArr = [];
				for (let i = 0; i < this.infolist.studyTimeList.length; i++) {
					console.log(this.infolist.studyTimeList[i]);
					let weekIndex = this.getChoseIndex(dataArr, this.infolist.studyTimeList[i].usableWeek)
					if (weekIndex == -1) {
						let date = {
							week: this.infolist.studyTimeList[i].usableWeek,
							time: [{
								startTime: this.infolist.studyTimeList[i].startTime,
								endTime: this.infolist.studyTimeList[i].endTime,
								hour: this.getHourForService(this.infolist.studyTimeList[i])
							}, ],
						}
						dataArr.push(date)
					} else {
						let timeData = {
							startTime: this.infolist.studyTimeList[i].startTime,
							endTime: this.infolist.studyTimeList[i].endTime,
							hour: this.getHourForService(this.infolist.studyTimeList[i])
						}
						dataArr[weekIndex].time.push(timeData)
					}
				}
				console.log(dataArr);
				this.comfireCourseData = dataArr;
				this.courseChoseWeek = dataArr;
			},

			getHourForService(timeData) {
				let timePartstart = timeData.startTime.split(':');
				let startHours = parseInt(timePartstart[0], 10);
				let timePartend = timeData.endTime.split(':');
				let endHours = parseInt(timePartend[0], 10) + 24;
				let diff = endHours - startHours
				if (diff > 24) {
					return diff - 24;
				} else {
					return diff;
				}
			},

			getChoseIndex(arr, item) {
				for (let i = 0; i < arr.length; i++) {
					let data = arr[i];
					if (data.week == item) {
						return i;
					}
				}
				return -1;
			},

			getSplitHour(time, str) {
				let arr = time.split(str)
				return arr;
			},

			getWeekName(week, index) {
				if (this.comfireReviewData.week.length == 1) {
					return this.normalWeekData[week];
				}
				if (index && index == this.comfireReviewData.week.length - 1) {
					return this.normalWeekData[week];
				} else {
					return this.normalWeekData[week] + '、';
				}
			},

			async getOrderId() {
				let res = await $http({
					url: 'deliver/web/student/reviewTime/info/getStudentReviewTimeInit',
					method: 'get',
					data: {
						orderNo: this.orderId,
						studentCode: this.studentCode
					}
				})
				if (res) {
					this.orderId = res.data;
					this.getCourseinfo();
				}
			},

			// 获取学生复习详情
			async getCourseinfo() {
				let _this = this;
				uni.showLoading();
				let res = await $http({
					url: 'deliver/web/student/reviewTime/info/getStudentReviewTimeInfoDetail',
					method: 'get',
					data: {
						id: _this.orderId
					}
				})
				uni.hideLoading()
				if (res && res.data) {
					_this.isDisable = res.data.isSubmit;
					_this.infolist = res.data;
					if (_this.infolist.isSubmit) {
						// this.comfireReviewData = {
						//     week: JSON.parse(_this.infolist.reviewWeek),
						//     startTime:_this.infolist.reviewTime
						// }
						_this.getNormalReviewData(_this.infolist);
					}
				}
			},

			getFormatToService(date, week) {
				if (date) {
					this.firstTime = date
					let str = dayjs(date).format("MM月DD日& HH:mm")
					let allStr = str;
					if (week) {
						allStr = str.replace("&", this.getWeekName(week))
						this.firstWeek = week
					} else {
						allStr = str.replace("&", "")
					}
					this.firstStudyTime = allStr;
				}
			},


			getNormalReviewData(infolist) {
				this.reviewChoseWeek = JSON.parse(infolist.reviewWeek);
				this.rewviewdatevalue = infolist.reviewTime;
				this.comfireReviewData = {
					startTime: this.rewviewdatevalue,
					week: this.reviewChoseWeek
				}
			},

			getNormalExp(val) {
				this.isExp = val;
				this.yesExp = this.isExp ? 1 : 0;
				this.noExp = this.isExp ? 0 : 1;
			},
			getNormalNew(val) {
				this.isNewStudent = val
				this.yesNew = this.isNewStudent ? 1 : 0
				this.noNew = this.isNewStudent ? 0 : 1
			},
			getGrade(val) {
				let index = 0;
				if (val <= 0) {
					index = 0;
				} else if (val >= this.gradeNameArr.length) {
					index = this.gradeNameArr.length - 1;
				} else {
					index = val;
				}
				return this.gradeNameArr[index];
			},


			//计算首次上课时间
			getFirstStudyTime() {
				console.log("计算");
				if (this.comfireCourseData.length == 0) {
					this.firstWeek = "";
					this.firstTime = "";
					this.firstStudyTime = "";
					return
				}
				let dateArr = [];
				let dateMinArr = [];
				let nowDate = dayjs().format("YYYY-MM-DD HH:mm");
				for (var i = 0; i < this.comfireCourseData.length; i++) {
					for (let j = 0; j < this.comfireCourseData[i].time.length; j++) {
						let date = this.getChoseDataToOption(this.comfireCourseData[i].week, this.comfireCourseData[i]
							.time[j]);
						dateArr.push(date)
						let dayjs1 = dayjs(date)
						dateMinArr.push(dayjs1.diff(nowDate, 'minute'));
					}
				}
				if (dateArr.length === 0) {
					this.firstWeek = "";
					this.firstTime = "";
					this.abutmentList.firstStudyTime = "";
					return;
				}
				let needIndex = -1;
				let minMinVal = Infinity;
				for (let i = 0; i < dateMinArr.length; i++) {
					if (dateMinArr[i] > 24 * 60 && dateMinArr[i] < minMinVal) {
						needIndex = i;
						minMinVal = dateMinArr[i];
					}
				}
				if (needIndex != -1) {
					this.firstStudyTime = this.getFormatToShow(dateArr[needIndex]);
				} else {
					let minIndex = dateMinArr.indexOf(Math.min(...dateMinArr));
					let lastDate = dayjs(dateArr[minIndex]).add(7, 'day');
					this.firstStudyTime = this.getFormatToShow(lastDate);
				}
			},

			getFormatToShow(date) {
				let str = dayjs(date).format("MM月DD日& HH:mm")
				let weekIndex = this.getLocalTypeWeek(dayjs(date).day());
				this.firstTime = dayjs(date).format("YYYY-MM-DD HH:mm");
				this.firstWeek = weekIndex;
				let allStr = str.replace("&", this.getWeekName(weekIndex))
				return allStr;
			},

			getChoseDataToOption(week, data) {
				let date = this.getDateForWeek(week)
				return date + " " + data.startTime
			},

			getDateForWeek(week) {
				let nowWeek = this.getLocalTypeWeek(dayjs().day());
				let diff = week - nowWeek;
				let date = "";
				if (diff >= 0) {
					date = dayjs().add(Math.abs(diff), 'day').format("YYYY-MM-DD")
				} else {
					date = dayjs().subtract(Math.abs(diff), 'day').format("YYYY-MM-DD")
				}
				return date
			},

			getLocalTypeWeek(nowWeek) {
				return (nowWeek + 6) % 7;
			},
		}
	}
</script>

<style lang="scss" scoped>
	.information {
		display: flex;
		justify-content: space-between;
		align-items: center;

		/deep/.uni-icons {
			color: #fff !important;
		}
	}

	.phone-input {
		background: #fff;
		border-radius: 8rpx;
		width: 100%;
		height: 70rpx;
		font-size: 28rpx;
		color: #999;
		display: flex;
		padding-left: 30rpx;
		align-items: center;
	}

	.uni-list-cell-db {
		background: #fff;
		border-radius: 8rpx;
		width: 100%;
		height: 70rpx;
		font-size: 30rpx;
		color: #999;
		display: flex;
		align-items: center;
	}

	/deep/.date_color {
		color: #000 !important;
	}

	/deep/.regions {
		color: #999 !important;
		font-size: 30upx;
	}

	/deep/.phone-btn {
		width: 586rpx;
		height: 80rpx;
		position: absolute;
		bottom: 40rpx;
		left: 54rpx;
		line-height: 80rpx;
		border-radius: 45rpx;
		font-size: 30rpx;
		color: #fff !important;
		background: linear-gradient(to bottom, #88CFBA, #1D755C);
	}

	/deep/.uni-select {
		padding: 0 10rpx 0 0;
		border: 0;
	}

	/deep/.uni-select__input-placeholder {
		font-size: 28rpx;
	}

	/deep/.uni-select--disabled {
		background-color: #fff;
	}

	/deep/.uni-stat__select {
		height: 60rpx !important;
	}

	.borderB {
		border-bottom: 1px solid #EFEFEF;
	}

	/deep/.uni-select__input-placeholder {
		color: #999 !important;
		font-size: 30rpx !important;
	}

	.icon_x {
		position: absolute;
		top: 28rpx;
		right: 0;
		z-index: 1;
	}

	.time-icon {
		/deep/.u-icon--right {
			position: absolute;
			right: 0;
			top: 20rpx;
		}
	}

	/deep/.u-picker__view {
		height: 600rpx !important;
	}

	/* 弹窗样式 */
	.dialog_style {
		width: 100%;
	}

	/* 21天结束复习弹窗样式 */
	.reviewCard_box {
		width: 680rpx;
		position: relative;
	}

	.reviewCard_box image {
		width: 100%;
		height: 100%;
	}

	.reviewCard {
		position: relative;
		width: 100%;
		height: 100%;
		background: #FFFFFF;
		color: #000;
		border-radius: 24upx;
		padding: 50upx 45upx;
		box-sizing: border-box;
	}

	.successFn-img {
		position: absolute;
		left: 160rpx;
		top: -298rpx;
		width: 300rpx;
		height: 300rpx;
		z-index: 9;

	}

	.successFnContent {
		font-size: 30rpx;
		font-family: AlibabaPuHuiTiR;
		color: #333333;
		line-height: 50rpx;
		margin-top: 40rpx;
		margin-bottom: 40rpx;
	}

	.cartoom_image {
		width: 420rpx;
		position: absolute;
		top: -250rpx;
		left: 145rpx;
		z-index: -1;
	}

	.review_close {
		position: absolute;
		top: 20rpx;
		right: 20rpx;
		z-index: 1;
	}

	.reviewTitle {
		width: 100%;
		text-align: center;
		font-size: 34upx;
		display: flex;
		justify-content: center;
	}

	.review_btn {
		width: 250upx;
		height: 80upx;
		background: linear-gradient(180deg, #88CFBA 0%, #1D755C 100%);
		border-radius: 45upx;
		font-size: 30upx;
		color: #FFFFFF;
		line-height: 80upx;
		justify-content: center;
		text-align: center;
	}

	.close_btn {
		width: 250upx;
		height: 80upx;
		color: #2E896F;
		font-size: 30upx;
		line-height: 80upx;
		text-align: center;
		border-radius: 45upx;
		box-sizing: border-box;
		border: 1px solid #2E896F;
	}


	.dialogContent {
		box-sizing: border-box;
		font-size: 32upx;
		line-height: 45upx;
		text-align: center;
		margin-top: 40rpx;
	}

	.dialogBG {
		margin: 0 35rpx 35rpx 35rpx;
		height: 961rpx;
		background-color: #fff;
		border-radius: 12rpx;
	}

	.top-close {
		color: black;
		font-size: 30rpx;
		margin: 0 35rpx;
		display: flex;
		justify-content: space-between;
		align-items: center;
	}

	.center-content {
		margin-top: 40rpx;
		height: 760rpx;
	}

	.start-time {
		padding: 30rpx 35rpx 30rpx 35rpx;
		display: flex;
		justify-content: space-between;
		align-items: center;
		color: black;
		border-top: 1px solid #f8f8f8;
		border-bottom: 1px solid #f8f8f8;
	}

	.top-button {
		text-align: center;
		height: 80rpx;
		display: flex;
		justify-content: center;
	}

	.confirm-button {
		width: 250rpx;
		height: 80rpx;
		background: linear-gradient(180deg, #88CFBA 0%, #1D755C 100%);
		color: #fff;
		font-size: 32rpx;
		display: flex;
		justify-content: center;
		/* 文本水平居中对齐 */
		align-items: center;
		/* 文本垂直居中对齐 */
	}

	.chose-week-text {
		width: 100%;
		margin: 0 15rpx;
		display: flex;
		align-content: flex-start;
		flex-flow: row wrap;
	}

	.chose-week-item {
		margin: 0 20rpx 30rpx 20rpx;
	}

	.week-normal {
		width: 120rpx;
		height: 65rpx;
		background: #efefef;
		border-radius: 12rpx;
		border: 1rpx solid #d4d4d4;
		color: #000;
		font-size: 30rpx;
		display: flex;
		align-items: center;
		/* 垂直居中 */
		justify-content: center;
		/* 水平居中 */
	}

	.week-chose {
		width: 120rpx;
		height: 65rpx;
		background: rgba(46, 137, 111, 0.1);
		border-radius: 12rpx;
		border: 1rpx solid #2E896F;
		color: #2E896F;
		font-size: 30rpx;
		display: flex;
		align-items: center;
		/* 垂直居中 */
		justify-content: center;
		/* 水平居中 */
	}

	.text-view {
		font-size: 32rpx;
		color: #333333;
	}

	.text-more-view {
		display: flex;
		flex-direction: row;
		/* 设置为水平排列 */
	}

	.course-bg-view {
		width: 500rpx;
		height: 60rpx;
		background: rgba(153, 153, 153, 0.1);
		border-radius: 12rpx;
		padding-top: 24rpx;
		padding-left: 24rpx;
		padding-right: 24rpx;
		font-size: 26rpx;
		color: #666666;
		overflow: hidden;
		white-space: nowrap;
		text-overflow: ellipsis;
	}

	.remake-bg-view {
		background: rgba(153, 153, 153, 0.1);
		border-radius: 12rpx;
		padding: 24rpx 35rpx;
		font-size: 28rpx;
		color: #000;
	}

	.time-flag {
		width: 38rpx;
		height: 38rpx;
		margin-top: 5rpx;
		margin-left: 20rpx;
	}

	.week-view {
		margin-left: 35rpx;
		margin-right: 24rpx;
		display: flex;
		justify-content: space-between;
	}

	.time-view {
		display: flex;
		justify-content: center;
		align-items: center;
	}

	.time-text-view-normal {
		color: #999999;
		width: 116rpx;
		font-size: 28rpx;
	}

	.time-text-view-normal-1 {
		border-radius: 12rpx;
		border: 1px solid #C8C8C8;
		;
		background-color: #F4F4F4;
		color: #000;
		width: 116rpx;
		padding: 10rpx 0;
		font-size: 28rpx;
	}

	.time-text-view {
		color: #000000;
		width: 116rpx;
		font-size: 30rpx;
	}
</style>