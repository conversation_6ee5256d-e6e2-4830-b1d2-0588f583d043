<template>
  <view class="positionRelative h-100" :style="{ height: useHeight + 'rpx' }">
    <view class="plr-30" style="padding-bottom: 130rpx">
      <view class="ptb-30 bg-ff mb-30 radius-15" v-for="(item, index) in listS.list" :key="index">
        <view class="plr-30">
          <view class="flex-s">
            <view class="flex-a-c">
              <image :src="item.headPortrait" class="head-img"></image>
              <view>
                <view class="c-66 flex-a-c">
                  <view class="mr-30 f-30">{{ item.userName }}</view>
                  <view class="golden">
                    {{ evaTypeList.find((i) => i.key == item.identityType).value }}
                  </view>
                  <!-- <view class="golden" v-if="item.identityType != 0 && item.identityType != 1">
                    {{ item.identityType != 4 ? (item.identityType == 5 ? 'B2俱乐部' : 'B3俱乐部') : 'B1俱乐部' }}
                  </view>
                  <view class="golden" v-if="item.identityType == 0 || item.identityType == 1">{{ item.identityType == 0 ? '家长' : '超人' }}</view> -->
                </view>
                <view class="f-28 c-66 mt-8">{{ item.createTime.slice(0, 11) }}</view>
              </view>
            </view>
          </view>
          <view class="mt-50" v-if="item.evaluateGrade">
            <uni-rate disabled v-model="item.evaluateGrade" active-color="#E57126" class="mt-20" size="20" />
          </view>
          <view class="mt-15 f-30">
            <text style="word-wrap: break-word">
              {{ item.evaluateContent || '该用户未填写评价内容' }}
            </text>
          </view>
          <view v-if="item.photoUrl">
            <image class="img" @click="previewSqs(item.photoUrl, val)" v-for="(val, idx) in firstimgs(item.photoUrl)" :key="idx" :src="val"></image>
          </view>
        </view>

        <view class="border-t pt-30 mt-30" v-if="item.orderEvaluateVo">
          <view class="plr-30">
            <view class="c-99 f-28">{{ item.orderEvaluateVo.createTime.slice(0, 11) }} 追评</view>
            <view class="mt-40" v-if="item.orderEvaluateVo.evaluateGrade">
              <uni-rate disabled v-model="item.orderEvaluateVo.evaluateGrade" active-color="#E57126" size="20" />
            </view>
            <view class="mt-20 c-33 lh-50 f-30">
              <text style="word-wrap: break-word">
                {{ item.orderEvaluateVo.evaluateContent || '该用户未填写评价内容' }}
              </text>
            </view>
            <view v-if="item.orderEvaluateVo.photoUrl">
              <image
                @click="previewSqs(item.orderEvaluateVo.photoUrl, val)"
                class="img"
                v-for="(val, idx) in firstimgs(item.orderEvaluateVo.photoUrl)"
                :key="idx"
                :src="val"
              ></image>
            </view>
          </view>
        </view>
      </view>
    </view>
    <view class="bg-ff ptb-20 plr-60 choose">
      <view class="go-choose" @click="goChoose">去选购</view>
    </view>
  </view>
</template>

<script>
  const { $http, $showSuccess, $showMsg } = require('@/util/methods.js');
  import Util from '@/util/util.js';
  export default {
    data() {
      return {
        useHeight: 0,
        avaUrl: Util.getCachedPic('https://document.dxznjy.com/dxSelect/home_avaUrl.png', 'home_avaUrl_path'),
        rateValue: 1, // 评价评分
        radio: 2, // 追加评价
        page: 1,
        no_more: false,
        imgHost: getApp().globalData.imgsomeHost,
        listS: {},
        /** 评价type */
        evaTypeList: [
          { key: '0', value: '家长' },
          { key: '1', value: '超人' },
          { key: '4', value: '超级会员' },
          { key: '5', value: '家长会员' },
          { key: '6', value: '超级合伙人' },
          { key: '7', value: '超级俱乐部' },
          { key: '8', value: '超级品牌' }
        ]
      };
    },
    onReady() {
      uni.getSystemInfo({
        success: (res) => {
          // 可使用窗口高度，将px转换rpx
          let h = res.windowHeight * (750 / res.windowWidth);
          this.useHeight = h;
        }
      });
    },
    onLoad(e) {
      console.log(e);
      this.id = e.id;
      this.getEvaluateList();
    },
    onReachBottom() {
      if (this.page >= this.listS.totalPage) {
        this.no_more = true;
        return false;
      }
      this.getEvaluateList(true, ++this.page);
    },
    methods: {
      goChoose() {
        uni.navigateBack();
      },

      async getEvaluateList(isPage, page) {
        let _this = this;
        const res = await $http({
          url: 'zx/order/evaluate/orderEvaluatePage',
          data: {
            courseId: _this.id,
            page: page || 1
          }
        });
        if (res) {
          if (isPage) {
            let old = _this.listS.list;
            _this.listS.list = [...old, ...res.data.list];
          } else {
            _this.listS = res.data;
          }
        }
      },

      firstimgs(item) {
        const jsonRegex = /^(\{.*\}|\[.*\])$/;
        if (jsonRegex.test(item)) {
          console.log('是');
          return JSON.parse(item);
        } else {
          console.log('否');
          return item;
        }
      },

      // 图片预览
      previewSqs(previewImg, index) {
        let imgData = JSON.parse(decodeURIComponent(previewImg));
        console.log('index', previewImg, index);
        console.log('预览');
        let _this = this;
        let imgsArray = [];
        //在这遍历的还是图片数据，也就是tabledata下的imgs_arr属性
        for (let i = 0; i < imgData.length; i++) {
          imgsArray.push(imgData[i]);
        }
        console.log(imgsArray, '------------');

        uni.previewImage({
          current: index, //当前所点击预览的图片地址
          urls: imgsArray, //这就是当前行图片数据，注意一定要是数组格式
          indicator: 'number',
          loop: true
        });
      }
    }
  };
</script>

<style lang="scss" scoped>
  .head-img {
    width: 80rpx;
    height: 80rpx;
    border-radius: 50%;
    margin-right: 20rpx;
  }

  .golden {
    color: #886a34;
    height: 38rpx;
    font-size: 26rpx;
    padding: 0 8rpx;
    line-height: 38rpx;
    text-align: center;
    border-radius: 6rpx;
    background: linear-gradient(to right, #f5ebd6, #dec288);
  }

  .img {
    width: 180rpx;
    height: 180rpx;
    border-radius: 20rpx;
    margin-top: 30rpx;
    margin-right: 30rpx;
    // background-color: #2E896F;
  }

  .border-t {
    border-top: 1px solid #eee;
  }

  .choose {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
  }

  .go-choose {
    color: #fff;
    width: 630rpx;
    height: 90rpx;
    font-size: 30rpx;
    line-height: 90rpx;
    text-align: center;
    border-radius: 45rpx;
    background-image: linear-gradient(to bottom, #88cfba, #1d755c);
  }

  .even {
    border-top: 1px solid #eee;
    // background-color: lightblue;
  }
</style>
