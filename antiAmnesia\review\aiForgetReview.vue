<template>
  <view class="">
    <u-navbar title=" " placeholder>
      <view class="u-nav-slot1" slot="left" @click="goBack">
        <u-icon name="arrow-left" color="#000" bold size="24"></u-icon>
      </view>
      <view class="u-nav-slot" slot="center">
        <view class="u-nav-slot-center">复习遗忘单词</view>
      </view>
      <view class="u-nav-slot1" slot="right" @click="goSetting">
        <u-icon name="setting" color="#000" bold size="30"></u-icon>
        <view style="font-size: 16rpx; color: #000">设置</view>
      </view>
    </u-navbar>
    <view v-if="isShow">
      <page-container :show="isShow" :overlay="false" @beforeleave="beforeleave"></page-container>
    </view>
    <view class="plr-30 t-c">
      <view class="page-content" :style="{ height: useHeight - 230 + 'rpx' }">
        <swiper
          :style="{ height: useHeight - 280 + 'rpx', background: '#fff', padding: '0 20rpx', borderRadius: '20rpx' }"
          @change="swiperChange"
          :disable-touch="isDisabled"
          :swiperDuration="duration"
          :current="originIndex"
          :class="disabled ? 'locked-swiper' : ''"
          :interval="interval"
          @touchend="stopTouch"
        >
          <swiper-item v-for="(itemParent, indexParent) in originList" :key="indexParent" :catchtouchmove="isDisabled ? '任意非空字符' : ''">
            <view class="top">
              <view class="f-30 flex-a-c">
                <text style="color: #555555">{{ originIndex + 1 }}/{{ originList.length }}</text>
                <text style="color: #555555" class="ml-10 f-24">(已完成{{ reviewList.length }}个)</text>
              </view>
              <view class="flex-a-c">
                <u-icon name="reload" color="#428a6f" size="40" @click="refreshFn"></u-icon>
              </view>
            </view>
            <view class="" :style="{ height: useHeight - 500 + 'rpx' }" style="display: flex; flex-direction: column; justify-content: space-around">
              <view class="word">
                <view class="toast" v-if="configData.autoReviewNext == 1">每个单词会播放{{ configData.playbackCount || 1 }}遍</view>
                <view class="english mtb-12 colorYellow" style="line-height: 69rpx">{{ itemParent.word || '' }}</view>
                <view class="chinese colorYellow">{{ itemParent.translation || itemParent.chinese || '' }}</view>
              </view>
              <view class="finger" v-show="configData.autoReviewNext != 1 && fingleShow">
                <view class="title">左滑下一个</view>
                <image src="https://document.dxznjy.com/course/6baa281714604e54b7f5de7f5f54236f.png" style="width: 60rpx; height: 80rpx" mode=""></image>
              </view>
              <view class="chat-bubble" v-if="originIndex == 0 && showTopic">
                <view class="bubble-content">为了您更好的掌握单词，请同时发音一次单词英文和中文，连续3次强化单词记忆</view>
                <view class="arrow arrow-down"></view>
              </view>
              <view class="footer">
                <view class="footer_button">
                  <view
                    class="start-talk flex-a-c"
                    @longpress="startTalkLong"
                    @click="talkingShort"
                    @touchmove.stop.prevent="audioTouchmove"
                    @touchend.stop.prevent="audioTouchendShow = true"
                  >
                    按住录音
                  </view>
                </view>
              </view>
            </view>
          </swiper-item>
        </swiper>
      </view>
      <view class="page-content" v-show="toastShow" :style="{ height: useHeight - 230 + 'rpx' }"></view>
      <!-- 语音组件 -->
      <chatAudio @submit="submitAudio" :audioXY="audioXY" :audioShow="audioShow" :audioTouchendShow="audioTouchendShow" @closeAudioShow="closeAudio"></chatAudio>
      <u-modal :show="refeshToast" title=" ">
        <view class="slot-content">
          <view style="color: #555; font-weight: 600; font-size: 32rpx; margin-bottom: 15rpx">刷新页面后,需重新进行录音操作</view>
        </view>
        <view class="confirmButton" slot="confirmButton">
          <view class="btn cancel" @click="changeRefeshConfig(0)">取消</view>
          <view class="btn confirm" @click="changeRefeshConfig(1)">确定</view>
        </view>
      </u-modal>
      <u-modal :show="voiceShow" title=" " showCancelButton>
        <view class="slot-content">
          <view style="color: #555; font-weight: 600; font-size: 32rpx; margin-bottom: 15rpx">“单词抗遗忘“</view>
          <view style="color: #555; font-weight: 600; font-size: 32rpx; margin-bottom: 15rpx">想访问您的麦克风</view>
          <view class="modal-title">使用麦克风发音复习和指令控制</view>
        </view>
        <view class="confirmButton" slot="confirmButton">
          <view class="btn cancel" @click="changeVoiceConfig(0)">不允许</view>
          <view class="btn confirm" @click="changeVoiceConfig(1)">允许</view>
        </view>
      </u-modal>
      <u-modal :show="modalShow" title="由于您改变了播音类型，是否重新？" showCancelButton>
        <view class="slot-content">
          <view class="modal-title">
            点击
            <text style="color: #428a6f">“允许”</text>
            将会用修改后的播音类型复习；
          </view>
          <view class="modal-title">
            点击
            <text style="color: #428a6f">“不允许”</text>
            将会用原播音类型继续复习，
          </view>
          <view class="modal-title">修改后的播音类型下次复习生效。</view>
        </view>
        <view class="confirmButton" slot="confirmButton">
          <view class="btn cancel" @click="changeConfig(0)">不允许</view>
          <view class="btn confirm" @click="changeConfig(1)">允许</view>
        </view>
      </u-modal>
    </view>
    <view class="overlay" :style="{ height: useHeight - 230 + 'rpx' }" v-show="toastShow" @click="startFn">
      <image src="https://document.dxznjy.com/course/e9c3e15659274760b6720e37e1400916.png" style="height: 100%" mode="heightFix"></image>
    </view>
  </view>
</template>

<script>
  import chatAudio from './component/chatAudio.vue';
  import Config from '@/util/config.js';
  import sensors from 'sa-sdk-miniprogram';
  import CryptoJS from 'crypto-js';
  const { $http } = require('@/util/methods.js');
  var innerAudioContext;
  export default {
    components: {
      chatAudio
    },
    data() {
      return {
        isShow: true,
        useHeight: 0,
        studentCode: '',
        useHeight: 0,
        displayData: {}, // swiper需要的数据
        originIndex: 0, // 记录源数据的下标
        // 源数据
        reviewList: [],
        originList: [],
        duration: 300,
        interval: 10000,
        toastShow: false,
        configData: {},
        isplay: false,
        audioXY: {
          x: 0,
          y: 0
        },
        audioShow: false, // 录制音频弹窗
        audioTouchendShow: false, // 是否结束录制音频
        timbre: 'W', // 音色默认女声 M  W
        pronunciationType: 0, // 1英式  0美式  默认美式
        playType: 2, // 版本
        linkUrl: '',
        userCode: '',
        sg: '',
        batchId: null,
        disabled: false,
        voiceShow: false,
        modalShow: false,
        isDisabled: false,
        wordList: [],
        refeshToast: false,
        firstRefresh: true,
        isGoBack: false,
        showTopic: true,
        fingleShow: false
      };
    },
    onReady() {
      let that = this;
      uni.getSystemInfo({
        //调用uni-app接口获取屏幕高度
        success(res) {
          // 可使用窗口高度，将px转换rpx
          let h = res.windowHeight * (750 / res.windowWidth);
          that.useHeight = h;
        }
      });
    },

    onLoad(e) {
      let that = this;
      uni.getStorage({
        key: 'aiForgetWordData',
        success(res) {
          that.originList = res.data;
          that.displayData = that.originList[0];
        }
      });
      if (e) {
        this.showData = e.showData ? JSON.parse(decodeURIComponent(e.showData)) : '';
        this.reviewId = e.reviewId ? e.reviewId : '';
        this.studentCode = e.studentCode ? e.studentCode : '';
        this.isGoBack = e.isGoBack ? true : false;
      }
      innerAudioContext = uni.createInnerAudioContext();
      innerAudioContext.onPlay(() => {
        console.log('开始播放');
      });
      innerAudioContext.onStop(function () {
        console.log('播放结束');
        that.isplay = false;
      });
      innerAudioContext.onPause(function () {
        console.log('播放暂停');
        that.isplay = false;
      });
      innerAudioContext.onError((res) => {
        console.log(res.errMsg);
        console.log(res.errCode);
        that.isplay = false;
      });
      setTimeout(() => {
        that.playVoice();
      }, 1000);
    },
    onShow() {
      this.checkConfig();
      this.checkMicrophonePermission();
      this.homeData();
      this.getWordversion();
    },

    methods: {
      changeConfig(val) {
        if (val == 1) {
          let data = uni.getStorageSync('aiConfigData');
          this.configData = JSON.parse(JSON.stringify(data));
          this.modalShow = false;
        } else {
          this.modalShow = false;
        }
      },
      isEqual(obj1, obj2) {
        // 如果类型不同，直接返回 false
        if (typeof obj1 !== typeof obj2) return false;
        // 如果是基本类型，直接比较值
        if (typeof obj1 !== 'object' || obj1 === null || obj2 === null) {
          return obj1 === obj2;
        }
        const keys1 = Object.keys(obj1);
        const keys2 = Object.keys(obj2);

        // 检查属性数量是否相同
        if (keys1.length !== keys2.length) return false;

        // 检查每个属性的值是否严格相等
        for (const key of keys1) {
          if (obj1[key] !== obj2[key]) return false;
        }

        return true;
      },
      async checkConfig() {
        let that = this;
        let newConfigData = uni.getStorageSync('aiConfigData');
        let oldConfigData = JSON.parse(JSON.stringify(that.configData));
        if (oldConfigData.id) {
          console.log(oldConfigData, 'oldConfigDataoldConfigDataoldConfigDataoldConfigData111111111111');
          if (that.isEqual(oldConfigData, newConfigData)) {
            return;
          } else {
            that.modalShow = true;
          }
        } else {
          console.log(oldConfigData, 'oldConfigDataoldConfigDataoldConfigDataoldConfigData22222222222222');
          let res = await this.$httpUser.get('znyy/word/review/play-config', {
            studentCode: this.studentCode
          });
          if (res && res.data.success) {
            const converted = Object.fromEntries(Object.entries(res.data.data).map(([key, value]) => [key, typeof value === 'boolean' ? (value ? 1 : 0) : value]));
            oldConfigData = JSON.parse(JSON.stringify(converted));
            that.configData = JSON.parse(JSON.stringify(converted));
          }
        }
      },
      beforeleave() {
        let that = this;
        that.isShow = false; //这个很重要，一定要先把弹框删除掉
        uni.removeStorage({
          key: 'aiForgetWordData',
          success(res) {
            that.goBack();
          }
        });
      },
      goBack() {
        let that = this;
        if (that.isGoBack) {
          uni.navigateBack();
        } else {
          innerAudioContext.stop();
          uni.redirectTo({
            url: `/parentEnd/report/aiReviewReport?reviewId=${that.reviewId}&history=0&studentCode=${that.studentCode}&showData=${encodeURIComponent(
              JSON.stringify(that.showData)
            )}`
          });
        }
      },
      changeRefeshConfig(type) {
        if (type == 1) {
          this.refresh();
          this.refeshToast = false;
          this.firstRefresh = false;
        } else {
          this.refeshToast = false;
        }
      },
      refreshFn() {
        innerAudioContext.stop();
        if (this.firstRefresh) {
          this.refeshToast = true;
        } else {
          this.refresh();
        }
      },
      refresh() {
        this.reviewList = this.reviewList.filter((item) => item.word != this.displayData.word);
        this.wordList = this.wordList.filter((item) => item.word != this.displayData.word);
        this.playVoice();
      },
      async homeData() {
        let _this = this;
        const res = await $http({
          url: 'zx/user/userInfoNew'
        });
        if (res) {
          _this.userinfo = res.data;
          let data = _this.userinfo.userCode + 'L0anhf';
          this.sg = CryptoJS.SHA1(data).toString();
        }
      },
      // 获取当前学员设置的语音版本
      getWordversion() {
        var that = this;
        that.$httpUser
          .get('znyy/course/info', {
            studentCode: that.studentCode
          })
          .then((res) => {
            if (res.data.success) {
              console.log(res.data.data);
              let name = res.data.data.voiceModel.split('#');
              that.pronunciationType = name[1];
              that.timbre = name[2];
              that.playType = name[0];
            } else {
              that.$util.alter(res.data.message);
            }
          });
      },
      sayWord(word) {
        var that = this;
        word = word.replaceAll('/', '');
        word = word.replaceAll('?', '');
        that.linkUrl = '';
        that.$httpUser
          .get('znyy/app/query/word/voice', {
            word: word,
            v: that.playType,
            rp: that.pronunciationType == 1 ? true : false,
            sex: that.timbre,
            sg: that.sg
          })
          .then((result) => {
            if (result.data.success) {
              let voiceUrl;
              // let url;
              if (that.playType == 1) {
                voiceUrl = 'https://document.dxznjy.com/' + encodeURIComponent(result.data.data);
                that.linkUrl = voiceUrl;
              } else {
                voiceUrl = result.data.data;
                that.linkUrl = voiceUrl;
              }
              innerAudioContext.obeyMuteSwitch = false;
              innerAudioContext.src = that.linkUrl;
              innerAudioContext.play();
            } else {
              that.$util.alter(result.data.message);
            }
          });
      },
      playVoice() {
        let that = this;
        let count = that.configData.autoReviewNext == 1 ? that.configData.playbackCount : 0;
        let counter = 0;
        that.sayWord(that.displayData.word);
        innerAudioContext.onEnded(() => {
          counter++;
          console.log('播放完毕', counter);
          if (counter < count) {
            // setTimeout(() => {
            // }, 500);
            innerAudioContext.play();
          } else {
            innerAudioContext.offEnded();
            // that.disabled = false;

            // setTimeout(() => {
            //   that.disabled = false;
            // }, 500);
          }
        });
      },
      // 获取麦克风权限
      checkMicrophonePermission() {
        uni.getSetting({
          success: (res) => {
            console.log(res, 'checkMicrophonePermissioncheckMicrophonePermission');
            if (res.authSetting['scope.record'] === undefined) {
              // 首次申请权限
              uni.authorize({
                scope: 'scope.record',
                success: () => {
                  // 授权成功
                  this.voiceShow = false;
                },
                fail: () => {
                  // this.showAlert(); // 拒绝后提示
                  this.voiceShow = true;
                }
              });
            } else if (res.authSetting['scope.record'] === false) {
              // 已拒绝过，显示提示
              // this.showAlert();
              this.voiceShow = true;
            } else {
              this.voiceShow = false;
            }
            // 已授权则无需处理
          }
        });
      },
      changeVoiceConfig(type) {
        if (type == 0) {
          this.voiceShow = false;
          setTimeout(() => {
            this.checkMicrophonePermission();
          }, 1000);
        } else {
          // 跳转至设置页
          uni.openSetting();
          // this.accreditOption();
        }
      },
      // 说话时间短
      talkingShort() {
        console.log(213);
        innerAudioContext.stop();
        if (this.disabled) return;
        setTimeout(() => {
          uni.showToast({
            title: '说话时间太短啦',
            icon: 'none'
          });
        }, 500);
      },
      // 手指移开
      audioTouchmove(e) {
        let x = e.changedTouches[0].clientX;
        let y = e.changedTouches[0].clientY;
        this.audioXY = {
          x: x,
          y: y
        };
      },
      // 录音长按
      async startTalkLong() {
        var that = this;
        that.showTopic = false;
        if (that.disabled) return;
        innerAudioContext.stop();
        let arr = that.reviewList.filter((i) => i.word == that.displayData.word);
        if (arr && arr.length > 0) {
          that.reviewList = that.reviewList.filter((i) => i.word != that.displayData.word);
        }
        let arr1 = that.wordList.filter((o) => o.word == that.displayData.word);
        if (arr1 && arr1.length > 0) {
          that.wordList = that.wordList.filter((p) => p.word != that.displayData.word);
        }
        that.audioShow = true;
      },
      //授权录音
      accreditOption() {
        var that = this;
        wx.authorize({
          scope: 'scope.record',
          success(res) {
            console.log('录音授权成功', res);
          },
          fail() {
            console.log('第一次录音授权失败');
            wx.showModal({
              title: '提示',
              content: '您未授权录音，功能将无法使用',
              showCancel: true,
              confirmText: '授权',
              confirmColor: '#AF1F25',
              success(res) {
                if (res.confirm) {
                  //确认则打开设置页面（自动切到设置页）
                  wx.openSetting({
                    success: (res) => {
                      console.log(res.authSetting);
                      if (!res.authSetting['scope.record']) {
                        console.log('未设置录音授权');
                        wx.showModal({
                          title: '提示',
                          content: '您未授权录音，功能将无法使用', // 可以自己编辑
                          showCancel: false,
                          success: function (res) {}
                        });
                      } else {
                        //第二次才成功授权
                        console.log('设置录音授权成功');
                      }
                    },
                    fail: function () {
                      console.log('授权设置录音失败');
                    }
                  });
                } else if (res.cancel) {
                  console.log('cancel');
                }
              },
              fail() {
                console.log('openfail');
              }
            });
          }
        });
      },
      // 取消发送语音
      async closeAudio(e) {
        console.log(e, 'eeeeeeeeeeeeeeeeeeeeeeee');
        let that = this;
        that.audioTouchendShow = false;
        that.audioShow = false;
      },

      // 发送语音
      async submitAudio(url, savedFilePath) {
        console.log(url, savedFilePath);
        let that = this;
        that.disabled = true;
        if (url) {
          uni.showLoading({
            title: '发音中',
            duration: 1500
          });
          that.wordList.push({
            audioUrl: url,
            word: that.displayData.word,
            chinese: that.displayData.translation
          });
          that.originList.forEach((i) => {
            if (i.word == that.displayData.word) {
              that.$set(i, 'hasResult', true);
            }
          });
          console.log(that.originList, ' that.originList that.originList that.originList');
          var model = {
            word: that.displayData.word,
            scheduleCode: that.displayData.scheduleCode,
            correct: 0,
            translation: that.displayData.translation,
            lastReviewCycle: that.displayData.lastReviewCycle,
            lastReviewDate: that.displayData.lastReviewDate,
            lastStudyTime: that.displayData.lastStudyTime,
            nextReviewDate: that.displayData.nextReviewDate,
            nowRound: that.displayData.nowRound
          };
          that.reviewList.push(model);
          setTimeout(() => {
            that.disabled = false;
            if (that.configData.autoReviewNext == 1 && that.originIndex + 1 < this.originList.length) {
              that.originIndex++;
            } else if (that.configData.autoReviewNext != 1) {
              that.fingleShow = true;
              that.showTopic = false;
            }
          }, 1500);
          if (that.reviewList.length == that.originList.length) {
            uni.showToast({
              title: '您已完成强化复习'
            });
            setTimeout(() => {
              uni.removeStorage({
                key: 'aiForgetWordData',
                success(res) {
                  that.goBack();
                }
              });
            }, 1000);
          }
        } else {
          that.disabled = false;
          uni.showToast({
            title: '发音失败',
            icon: 'none'
          });
        }
      },

      goSetting() {
        this.showTopic = false;
        innerAudioContext.stop();
        uni.navigateTo({
          url: `/antiAmnesia/review/aiReviewSetting?studentCode=${this.studentCode}`
        });
      },
      stopTouch(e) {
        console.log(e);
        if (this.originIndex + 1 == this.originList.length) {
          uni.showToast({
            title: '已经是最后一个啦',
            icon: 'none'
          });
        }
      },
      /**
       * swiper滑动时候
       */
      swiperChange(event) {
        let that = this;
        innerAudioContext.stop();
        let { current } = event.detail;
        console.log(current, that.originIndex);
        that.isDisabled = false;
        that.fingleShow = false;
        that.showTopic = false;
        that.originIndex = current;
        that.displayData = that.originList[current];
        that.playVoice();
      }
    }
  };
</script>

<style lang="scss">
  page {
    background-color: #f5f8fa;
  }

  .page-content {
    border-radius: 14rpx;
    // width: calc(100% - 60rpx);
    background-color: #f5f8fa;
    padding: 30rpx 0 30rpx 0;
  }
  .top {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 10rpx;
  }
  .word {
    // margin-top: 100rpx;
    width: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-around;
    align-items: center;
    padding-bottom: 200rpx;
    .toast {
      font-size: 24rpx;
      color: #b0b0b0;
    }
    .english {
      font-size: 56rpx;
      color: #333333;
      font-weight: 600;
    }
    .chinese {
      width: 100%;
      font-size: 40rpx;
      color: #333333;
      font-weight: 600;
    }
  }
  .right-false {
    width: 100%;
    margin-top: 200rpx;
    display: flex;
    align-items: center;
    justify-content: space-around;
    image {
      width: 266rpx;
      height: 80rpx;
    }
  }
  .start-talking {
    width: 150rpx;
    height: 50rpx;
    background: #f1fffa;
    border-radius: 50rpx;
    border: 2rpx solid #4fb693;
    .start-talk {
      width: 100%;
      height: 100%;
      justify-content: center;
      color: #4fb693;
      font-size: 28rpx;
    }
  }
  .footer_show {
    display: flex;
    justify-content: space-around;
    align-items: center;
    width: 100%;
    margin-top: 100rpx;
    .start-talking {
      width: 300rpx;
      height: 100rpx;
      background: #f1fffa;
      border-radius: 50rpx;
      border: 2rpx solid #4fb693;
      .start-talk {
        width: 100%;
        height: 100%;
        justify-content: center;
        color: #4fb693;
        font-size: 28rpx;
      }
    }
    .showChinese {
      width: 300rpx;
      height: 100rpx;
      background: #f1fffa;
      border-radius: 50rpx;
      border: 2rpx solid #4fb693;
      .showChinese_btn {
        width: 100%;
        height: 100%;
        justify-content: center;
        color: #4fb693;
        font-size: 28rpx;
      }
    }
  }
  .toast {
    font-size: 24rpx;
    color: #b0b0b0;
  }
  .toastgreen {
    font-size: 24rpx;
    color: #4fb693;
  }
  .disabled {
    background: #f8f8f8 !important;
    border: 2rpx solid #acacac !important;
  }
  .disabled-btn {
    color: #a6a6a6 !important;
  }
  .footer {
    width: 100%;
    position: absolute;
    bottom: 100rpx;
    display: flex;
    justify-content: center;
    align-content: center;
    margin-top: 60rpx;
    .footer_button {
      width: 600rpx;
      height: 92rpx;
      line-height: 92rpx;
      text-align: center;
      color: #fff;
      background: #f1fffa;
      border-radius: 50rpx;
      border: 1px solid #428a6f;
      font-size: 32rpx;
      background: #428a6f;
      border-radius: 46rpx;
    }
    .start-talk {
      width: 100%;
      height: 100%;
      justify-content: center;
      color: #fff;
      font-size: 28rpx;
    }
  }
  .title-bg {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    height: 90rpx;
    background: #f6f9fc;
    border-radius: 8rpx;
  }

  // .start-talk {
  //   width: 240rpx;
  //   height: 60rpx;
  //   display: flex;
  //   justify-content: center;
  //   align-items: center;
  //   background: linear-gradient(180deg, #88cfba 0%, #1d755c 100%);
  //   border-radius: 14rpx;
  // }

  .word-play {
    padding: 10rpx 24rpx;
    height: 40rpx;
    background: #f3f3f3;
    border-radius: 18rpx;
  }
  .warp {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
  }
  .warp1 {
    height: 100%;
    .title {
      font-size: 28rpx;
      color: #ffffff;
      font-weight: 600;
    }
    .title-center {
      font-size: 28rpx;
      color: #17ca8a;
      font-weight: 600;
      position: absolute;
      left: 50%;
      top: 50%;
      transform: translate(-50%, -50%);
    }
    .finger {
      position: absolute;
      right: 40rpx;
      bottom: 300rpx;
      .title {
        font-size: 28rpx;
        color: #ffffff;
        font-weight: 600;
        margin-bottom: 10rpx;
      }
    }
  }
  .rect {
    color: #fff;
    font-weight: bold;
    .number {
      font-size: 300rpx;
      margin-top: 50rpx;
    }
    .text {
      font-size: 40rpx;
      margin-top: 10rpx;
    }
  }
  .blur {
    // filter: blur(16rpx);
    // pointer-events: none;
    font-size: 40rpx;
    color: #333333;
    filter: blur(12rpx); /* 添加模糊效果 */
  }
  .modal-title {
    font-size: 28rpx;
    color: #555555;
    margin-bottom: 10rpx;
  }
  .confirmButton {
    display: flex;
    justify-content: space-around;
    align-items: center;
    .btn {
      width: 264rpx;
      height: 82rpx;
      border-radius: 12rpx;
      line-height: 82rpx;
      text-align: center;
    }
    .cancel {
      background: #f1f1f1;
      color: #555;
    }
    .confirm {
      background: #428a6f;
      color: #fff;
    }
  }
  .topicToastBottom-box {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    margin-bottom: 30rpx;
    position: absolute;
    left: 50%;
    bottom: 200rpx;
    transform: translateX(-50%);
  }
  .topicToastBottom {
    background-color: #626262;
    color: #fff;
    font-weight: 600;
    font-size: 24rpx;
    width: 200rpx;
    // height: 50rpx;
    line-height: 50rpx;
    border-radius: 20rpx;
    margin-bottom: 20rpx;
  }
  .topicToast-box {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 30rpx;
  }
  .topicToast {
    background-color: #626262;
    color: #fff;
    font-weight: 600;
    font-size: 28rpx;
    width: 600rpx;
    height: 50rpx;
    line-height: 50rpx;
    border-radius: 20rpx;
    // position: absolute;
    // top: 120rpx;
    // left: 50%;
    // z-index: 99;
    // transform: translateX(-50%);
  }
  .overlay {
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: #808080;
  }
  .chat-bubble {
    display: flex;
    flex-direction: column;
    align-items: center;
    max-width: 70%;
    margin: 20rpx auto;
    position: relative;
  }
  .bubble-content {
    background-color: #f0f0f0;
    padding: 20rpx 28rpx;
    border-radius: 24rpx;
    font-size: 24rpx;
    font-weight: 600;
    position: relative;
    box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.6);
  }
  .arrow-down {
    position: relative;
    width: 4rpx;
    height: 100rpx;
    margin: 0 auto;
    background: repeating-linear-gradient(to bottom, transparent, transparent 10rpx, #999 10rpx, #999 20rpx);
  }

  .arrow-down::after {
    content: '';
    position: absolute;
    bottom: -10rpx; /* ⬅️ 让箭头出现在底部 */
    left: 50%;
    transform: translateX(-50%);
    border-left: 12rpx solid transparent;
    border-right: 12rpx solid transparent;
    border-top: 20rpx solid #999; /* ⬅️ 朝下箭头 */
  }
  .finger {
    position: absolute;
    right: 0;
    bottom: 300rpx;
    .title {
      font-size: 28rpx;
      color: #000;
      font-weight: 600;
      margin-bottom: 10rpx;
    }
  }
</style>
<style>
  .u-navbar__content__right {
    right: 200rpx !important;
  }
  .u-nav-slot {
    width: 100%;
  }
  .u-nav-slot-center {
    width: 100%;
    text-align: left;
    padding-left: 100rpx;
  }
  .locked-swiper {
    touch-action: none !important; /* 禁止所有手势 */
    pointer-events: none !important; /* 禁止点击事件（可选） */
  }
  .colorYellow {
    color: #de8470 !important;
  }
</style>
