<template>
  <view class="">
    <u-navbar title=" " placeholder>
      <view class="u-nav-slot1" slot="left" @click="goBack">
        <u-icon name="arrow-left" color="#000" bold size="15"></u-icon>
      </view>
      <view class="u-nav-slot" slot="center">
        <view class="u-nav-slot-center">复习遗忘单词</view>
      </view>
      <view class="u-nav-slot1" slot="right" @click="goSetting">
        <u-icon name="setting" color="#000" bold size="15"></u-icon>
        <view style="font-size: 16rpx; color: #000">设置</view>
      </view>
    </u-navbar>

    <view class="main-content plr-30 t-c">
      <view class="page-content" :style="{ height: useHeight - 230 + 'rpx' }">
        <swiper
          :style="{ height: useHeight - 280 + 'rpx', background: '#fff', padding: '0 20rpx', borderRadius: '20rpx' }"
          @change="swiperChange"
          :disable-touch="isDisabled"
          :swiperDuration="duration"
          :current="originIndex"
          :class="disabled ? 'locked-swiper' : ''"
          :interval="interval"
          @touchend="stopTouch"
        >
          <swiper-item v-for="(itemParent, indexParent) in originList" :key="indexParent" :catchtouchmove="isDisabled ? '任意非空字符' : ''">
            <view class="top">
              <view class="f-30 flex-a-c">
                <text style="color: #555555">{{ originIndex + 1 }}/{{ originList.length }}</text>
                <text style="color: #555555" class="ml-10 f-24">(已完成{{ reviewList.length }}个)</text>
              </view>
              <view class="flex-a-c">
                <u-icon name="reload" color="#428a6f" size="20" @click="refreshFn"></u-icon>
              </view>
            </view>
            <view class="" :style="{ height: useHeight - 500 + 'rpx' }" style="display: flex; flex-direction: column; justify-content: space-around">
              <view class="word">
                <view class="toast" v-if="configData.autoReviewNext == 1">每个单词会播放{{ configData.playbackCount || 1 }}遍</view>
                <view class="english mtb-12 colorYellow" style="line-height: 69rpx">{{ itemParent.word || '' }}</view>
                <view class="chinese colorYellow">{{ itemParent.translation || itemParent.chinese || '' }}</view>
              </view>
              <view class="chat-bubble" v-if="originIndex == 0 && showTopic">
                <view class="bubble-content">为了您更好的掌握单词，请同时发音一次单词英文和中文，连续3次强化单词记忆</view>
                <view class="arrow arrow-down"></view>
              </view>
              <view class="footer">
                <view class="footer_button" :class="{ recording: isLongPress }" :hover-class="isLongPress ? '' : 'record-button-hover'" :hover-stay-time="100">
                  <view
                    class="start-talk flex-a-c"
                    @touchstart.stop.prevent="handleTouchStart"
                    @touchmove.stop.prevent="handleTouchMove"
                    @touchend.stop.prevent="handleTouchEnd"
                    @touchcancel.stop.prevent="handleTouchCancel"
                  >
                    {{ isLongPress ? '录音中...' : '按住录音' }}
                  </view>
                </view>
              </view>
            </view>
            <view class="finger" v-show="configData.autoReviewNext != 1 && fingleShow">
              <view class="title">左滑下一个</view>
              <image src="https://document.dxznjy.com/course/6baa281714604e54b7f5de7f5f54236f.png" style="width: 60rpx; height: 80rpx" mode=""></image>
            </view>
          </swiper-item>
        </swiper>
      </view>
      <view class="page-content" v-show="toastShow" :style="{ height: useHeight - 230 + 'rpx' }"></view>
      <!-- 语音组件 -->
      <chatAudio @submit="submitAudio" :audioXY="audioXY" :audioShow="audioShow" :audioTouchendShow="audioTouchendShow" @closeAudioShow="closeAudio"></chatAudio>
      <u-modal :show="refeshToast" title=" ">
        <view class="slot-content">
          <view style="color: #555; font-weight: 600; font-size: 32rpx; margin-bottom: 15rpx">刷新页面后,需重新进行录音操作</view>
        </view>
        <view class="confirmButton" slot="confirmButton">
          <view class="btn cancel" @click="changeRefeshConfig(0)">取消</view>
          <view class="btn confirm" @click="changeRefeshConfig(1)">确定</view>
        </view>
      </u-modal>
      <u-modal :show="voiceShow" title=" " showCancelButton>
        <view class="slot-content">
          <view style="color: #555; font-weight: 600; font-size: 32rpx; margin-bottom: 15rpx">“单词抗遗忘“</view>
          <view style="color: #555; font-weight: 600; font-size: 32rpx; margin-bottom: 15rpx">想访问您的麦克风</view>
          <view class="modal-title">使用麦克风发音复习和指令控制</view>
        </view>
        <view class="confirmButton" slot="confirmButton">
          <view class="btn cancel" @click="changeVoiceConfig(0)">不允许</view>
          <view class="btn confirm" @click="changeVoiceConfig(1)">允许</view>
        </view>
      </u-modal>
      <u-modal :show="modalShow" title="由于您改变了播音类型，是否重新？" showCancelButton>
        <view class="slot-content">
          <view class="modal-title">
            点击
            <text style="color: #428a6f">“允许”</text>
            将会用修改后的播音类型复习；
          </view>
          <view class="modal-title">
            点击
            <text style="color: #428a6f">“不允许”</text>
            将会用原播音类型继续复习，
          </view>
          <view class="modal-title">修改后的播音类型下次复习生效。</view>
        </view>
        <view class="confirmButton" slot="confirmButton">
          <view class="btn cancel" @click="changeConfig(0)">不允许</view>
          <view class="btn confirm" @click="changeConfig(1)">允许</view>
        </view>
      </u-modal>
    </view>
  </view>
</template>

<script>
  import chatAudio from './component/chatAudio.vue';
  import CryptoJS from 'crypto-js';
  const { $http } = require('@/util/methods.js');
  var innerAudioContext;
  export default {
    components: {
      chatAudio
    },
    data() {
      return {
        isShow: true,
        useHeight: 0,
        studentCode: '',
        useHeight: 0,
        displayData: {}, // swiper需要的数据
        originIndex: 0, // 记录源数据的下标
        // 源数据
        reviewList: [],
        originList: [],
        duration: 300,
        interval: 10000,
        toastShow: false,
        configData: {},
        isplay: false,
        audioXY: {
          x: 0,
          y: 0
        },
        audioShow: false, // 录制音频弹窗
        audioTouchendShow: false, // 是否结束录制音频
        timbre: 'W', // 音色默认女声 M  W
        pronunciationType: 0, // 1英式  0美式  默认美式
        playType: 2, // 版本
        linkUrl: '',
        userCode: '',
        sg: '',
        batchId: null,
        disabled: false,
        voiceShow: false,
        modalShow: false,
        isDisabled: false,
        wordList: [],
        refeshToast: false,
        firstRefresh: true,
        isGoBack: false,
        showTopic: true,
        fingleShow: false,
        statusBarHeight: 0,
        safeAreaBottom: 0,
        // App端固定高度
        fixedSwiperHeight: 1200,
        // 触摸事件相关
        touchStartTime: 0, // 触摸开始时间
        touchTimer: null, // 长按定时器
        isLongPress: false, // 是否为长按
        longPressDelay: 500, // 长按延迟时间(毫秒)
        touchStartPos: { x: 0, y: 0 }, // 触摸开始位置
        maxMoveDistance: 20 // 最大移动距离(超过则取消长按)
      };
    },
    onReady() {
      let that = this;
      uni.getSystemInfo({
        //调用uni-app接口获取屏幕高度
        success(res) {
          // 可使用窗口高度，将px转换rpx
          let h = res.windowHeight * (750 / res.windowWidth);
          that.useHeight = h;

          // 获取状态栏高度和安全区域信息
          that.statusBarHeight = (res.statusBarHeight || 0) * (750 / res.windowWidth);

          // #ifdef APP-PLUS
          // App端获取安全区域信息
          that.safeAreaBottom = res.safeAreaInsets ? res.safeAreaInsets.bottom * (750 / res.windowWidth) : 0;
          // #endif

          // #ifdef MP-WEIXIN
          // 微信小程序获取安全区域信息
          that.safeAreaBottom = res.safeArea ? (res.screenHeight - res.safeArea.bottom) * (750 / res.windowWidth) : 0;
          // #endif
        }
      });
    },

    onLoad(e) {
      let that = this;
      uni.getStorage({
        key: 'aiForgetWordData',
        success(res) {
          that.originList = res.data;
          that.displayData = that.originList[0];
        }
      });
      if (e) {
        this.showData = e.showData ? JSON.parse(decodeURIComponent(e.showData)) : '';
        this.reviewId = e.reviewId ? e.reviewId : '';
        this.studentCode = e.studentCode ? e.studentCode : '';
        this.isGoBack = e.isGoBack ? true : false;
      }
      innerAudioContext = uni.createInnerAudioContext();
      // #ifdef APP-PLUS
      // App端音频播放优化配置
      innerAudioContext.autoplay = false;
      // #endif

      innerAudioContext.onPlay(() => {
        console.log('开始播放');
        that.isplay = true;
      });
      innerAudioContext.onStop(function () {
        console.log('播放结束');
        that.isplay = false;
      });
      innerAudioContext.onPause(function () {
        console.log('播放暂停');
        that.isplay = false;
      });
      innerAudioContext.onError((res) => {
        console.error('音频播放错误:', res);
        console.error('错误码:', res.errCode);
        console.error('错误信息:', res.errMsg);
        that.isplay = false;

        // #ifdef APP-PLUS
        // App端特殊错误处理
        if (res.errCode === -99) {
          console.error('App端音频播放错误-99，尝试重新播放');
          that.handleAudioError();
        }
        // #endif
      });

      innerAudioContext.onCanplay(() => {
        console.log('音频可以播放');
      });

      innerAudioContext.onWaiting(() => {
        console.log('音频加载中...');
      });
      setTimeout(() => {
        that.playVoice();
      }, 1000);
    },
    onShow() {
      this.checkConfig();
      this.checkMicrophonePermission();
      this.homeData();
      this.getWordversion();
    },
    onUnload() {
      // 清除触摸定时器
      this.clearTouchTimer();

      // 销毁音频上下文
      if (innerAudioContext) {
        innerAudioContext.destroy();
      }
    },
    computed: {
      eventHandler() {
        return this.isDisabled ? '666' : '';
      },
      // 内容区域高度计算
      contentHeight() {
        // #ifdef APP-PLUS
        // App端需要考虑状态栏和安全区域
        return this.useHeight - 200 - this.safeAreaBottom;
        // #endif

        // #ifdef MP-WEIXIN
        // 微信小程序
        return this.useHeight - 230;
        // #endif

        // #ifdef H5
        // H5端
        return this.useHeight - 200;
        // #endif
      },
      // Swiper高度计算
      swiperHeight() {
        // #ifdef APP-PLUS
        // App端使用屏幕高度的70%，但不超过1400rpx，不少于1000rpx
        const calculatedHeight = this.useHeight * 0.7;
        return Math.max(1000, Math.min(1400, calculatedHeight));
        // #endif

        // #ifdef MP-WEIXIN
        // 微信小程序
        return this.useHeight - 280;
        // #endif

        // #ifdef H5
        // H5端
        return this.useHeight - 250;
        // #endif
      },
      // Swiper内部内容高度计算
      swiperContentHeight() {
        // #ifdef APP-PLUS
        // App端内容高度为swiper高度减去顶部区域
        return this.swiperHeight - 150;
        // #endif

        // #ifdef MP-WEIXIN
        // 微信小程序
        return this.useHeight - 500;
        // #endif

        // #ifdef H5
        // H5端
        return this.useHeight - 500;
        // #endif
      }
    },
    methods: {
      changeConfig(val) {
        if (val == 1) {
          let data = uni.getStorageSync('aiConfigData');
          this.configData = JSON.parse(JSON.stringify(data));
          this.modalShow = false;
        } else {
          this.modalShow = false;
        }
      },
      isEqual(obj1, obj2) {
        // 如果类型不同，直接返回 false
        if (typeof obj1 !== typeof obj2) return false;
        // 如果是基本类型，直接比较值
        if (typeof obj1 !== 'object' || obj1 === null || obj2 === null) {
          return obj1 === obj2;
        }
        const keys1 = Object.keys(obj1);
        const keys2 = Object.keys(obj2);

        // 检查属性数量是否相同
        if (keys1.length !== keys2.length) return false;

        // 检查每个属性的值是否严格相等
        for (const key of keys1) {
          if (obj1[key] !== obj2[key]) return false;
        }

        return true;
      },
      async checkConfig() {
        let that = this;
        let newConfigData = uni.getStorageSync('aiConfigData');
        let oldConfigData = JSON.parse(JSON.stringify(that.configData));
        if (oldConfigData.id) {
          console.log(oldConfigData, 'oldConfigDataoldConfigDataoldConfigDataoldConfigData111111111111');
          if (that.isEqual(oldConfigData, newConfigData)) {
            return;
          } else {
            that.modalShow = true;
          }
        } else {
          console.log(oldConfigData, 'oldConfigDataoldConfigDataoldConfigDataoldConfigData22222222222222');
          let res = await this.$httpUser.get('znyy/word/review/play-config', {
            studentCode: this.studentCode
          });
          if (res && res.data.success) {
            const converted = Object.fromEntries(Object.entries(res.data.data).map(([key, value]) => [key, typeof value === 'boolean' ? (value ? 1 : 0) : value]));
            oldConfigData = JSON.parse(JSON.stringify(converted));
            that.configData = JSON.parse(JSON.stringify(converted));
          }
        }
      },
      beforeleave() {
        let that = this;
        that.isShow = false; //这个很重要，一定要先把弹框删除掉
        uni.removeStorage({
          key: 'aiForgetWordData',
          success(res) {
            that.goBack();
          }
        });
      },
      goBack() {
        let that = this;
        if (that.isGoBack) {
          uni.navigateBack();
        } else {
          innerAudioContext.stop();
          uni.redirectTo({
            url: `/parentEnd/report/aiReviewReport?reviewId=${that.reviewId}&history=0&studentCode=${that.studentCode}&showData=${encodeURIComponent(
              JSON.stringify(that.showData)
            )}`
          });
        }
      },
      changeRefeshConfig(type) {
        if (type == 1) {
          this.refresh();
          this.refeshToast = false;
          this.firstRefresh = false;
        } else {
          this.refeshToast = false;
        }
      },
      refreshFn() {
        innerAudioContext.stop();
        if (this.firstRefresh) {
          this.refeshToast = true;
        } else {
          this.refresh();
        }
      },
      refresh() {
        this.reviewList = this.reviewList.filter((item) => item.word != this.displayData.word);
        this.wordList = this.wordList.filter((item) => item.word != this.displayData.word);
        this.playVoice();
      },
      async homeData() {
        let _this = this;
        const res = await $http({
          url: 'zx/user/userInfoNew'
        });
        if (res) {
          _this.userinfo = res.data;
          let data = _this.userinfo.userCode + 'L0anhf';
          this.sg = CryptoJS.SHA1(data).toString();
        }
      },
      // 获取当前学员设置的语音版本
      getWordversion() {
        var that = this;
        that.$httpUser
          .get('znyy/course/info', {
            studentCode: that.studentCode
          })
          .then((res) => {
            if (res.data.success) {
              console.log(res.data.data);
              let name = res.data.data.voiceModel.split('#');
              that.pronunciationType = name[1];
              that.timbre = name[2];
              that.playType = 1;
              // that.playType = name[0];s
            } else {
              that.$util.alter(res.data.message);
            }
          });
      },
      sayWord(word) {
        var that = this;
        // word = word.replace(/\//g, '');
        // word = word.replace(/\?/g, '');
        word = word.replace('/', '');
        word = word.replace('?', '');
        that.linkUrl = '';

        console.log('准备播放单词:', word);

        that.$httpUser
          .get('znyy/app/query/word/voice', {
            word: word,
            v: that.playType,
            rp: that.pronunciationType == 1 ? true : false,
            sex: that.timbre,
            sg: that.sg
          })
          .then((result) => {
            console.log('音频接口响应:', result);
            if (result.data.success) {
              let voiceUrl;
              console.log('音频数据:', result.data.data);

              if (that.playType == 1) {
                voiceUrl = 'https://document.dxznjy.com/' + encodeURIComponent(result.data.data);
                that.linkUrl = voiceUrl;
              } else {
                voiceUrl = result.data.data;
                that.linkUrl = voiceUrl;
              }

              console.log('最终音频URL:', that.linkUrl);

              // 验证URL有效性
              if (!that.linkUrl || that.linkUrl.trim() === '') {
                console.error('音频URL为空');
                uni.showToast({
                  title: '音频地址无效',
                  icon: 'none'
                });
                return;
              }

              // App端特殊处理
              // #ifdef APP-PLUS
              that.playAudioForApp(that.linkUrl);
              // #endif

              // 小程序和H5端处理
              // #ifdef MP-WEIXIN || H5
              that.playAudioForWeb(that.linkUrl);
              // #endif
            } else {
              console.error('音频接口返回失败:', result.data.message);
              that.$util.alter(result.data.message);
            }
          })
          .catch((error) => {
            console.error('音频接口请求失败:', error);
            uni.showToast({
              title: '获取音频失败',
              icon: 'none'
            });
          });
      },
      // App端音频播放
      // #ifdef APP-PLUS
      playAudioForApp(url) {
        console.log('App端播放音频:', url);
        try {
          // 停止当前播放
          innerAudioContext.stop();

          // 设置音频源
          innerAudioContext.src = url;

          // App端特殊配置
          // innerAudioContext.obeyMuteSwitch = false;

          // 延迟播放，确保资源加载完成
          setTimeout(() => {
            innerAudioContext.play();
          }, 300);
        } catch (error) {
          console.error('App端音频播放失败:', error);
          this.handleAudioError();
        }
      },
      // #endif

      // 小程序和H5端音频播放
      // #ifdef MP-WEIXIN || H5
      playAudioForWeb(url) {
        console.log('Web端播放音频:', url);
        try {
          // #ifdef MP-WEIXIN
          innerAudioContext.obeyMuteSwitch = false;
          // #endif

          innerAudioContext.src = url;

          // 立即播放
          innerAudioContext.play();

          // 备用延迟播放
          setTimeout(() => {
            if (!this.isplay) {
              innerAudioContext.play();
            }
          }, 500);
        } catch (error) {
          console.error('Web端音频播放失败:', error);
          uni.showToast({
            title: '音频播放失败',
            icon: 'none'
          });
        }
      },
      // #endif

      // 音频播放错误处理
      handleAudioError() {
        console.log('处理音频播放错误');

        // #ifdef APP-PLUS
        // App端音频错误处理
        try {
          // 停止当前播放
          if (innerAudioContext) {
            innerAudioContext.stop();
          }

          // 重新创建音频上下文
          setTimeout(() => {
            this.recreateAudioContext();
          }, 1000);
        } catch (error) {
          console.error('音频错误处理失败:', error);
          uni.showToast({
            title: '音频播放失败',
            icon: 'none'
          });
        }
        // #endif

        // #ifdef MP-WEIXIN || H5
        // 小程序和H5端错误处理
        uni.showToast({
          title: '音频播放失败，请重试',
          icon: 'none'
        });
        // #endif
      },

      // 重新创建音频上下文
      recreateAudioContext() {
        console.log('重新创建音频上下文');
        let that = this;

        try {
          // 销毁旧的音频上下文
          if (innerAudioContext) {
            innerAudioContext.destroy();
          }

          // 创建新的音频上下文
          innerAudioContext = uni.createInnerAudioContext();

          // #ifdef APP-PLUS
          innerAudioContext.autoplay = false;
          // #endif

          // 重新绑定事件
          innerAudioContext.onPlay(() => {
            console.log('重新创建后开始播放');
            that.isplay = true;
          });

          innerAudioContext.onStop(() => {
            console.log('重新创建后播放结束');
            that.isplay = false;
          });

          innerAudioContext.onError((res) => {
            console.error('重新创建后仍然出错:', res);
            that.isplay = false;
            uni.showToast({
              title: '音频播放失败',
              icon: 'none'
            });
          });

          console.log('音频上下文重新创建完成');
        } catch (error) {
          console.error('重新创建音频上下文失败:', error);
        }
      },

      playVoice() {
        let that = this;
        let count = that.configData.autoReviewNext == 1 ? that.configData.playbackCount : 0;
        let counter = 0;
        that.sayWord(that.displayData.word);
        innerAudioContext.onEnded(() => {
          counter++;
          console.log('播放完毕', counter);
          if (counter < count) {
            setTimeout(() => {
              innerAudioContext.play();
            }, 500);
          } else {
            innerAudioContext.offEnded();
            // that.disabled = false;

            // setTimeout(() => {
            //   that.disabled = false;
            // }, 500);
          }
        });
      },
      // 获取麦克风权限
      checkMicrophonePermission() {
        // 判断平台
        if (uni.getSystemInfoSync().platform === 'android') {
          // Android平台权限检查
          plus.android.requestPermissions(
            ['android.permission.RECORD_AUDIO'],
            (result) => {
              if (result.granted && result.granted.length > 0) {
                this.voiceShow = false;
                // 同时检查音频播放权限
                this.checkAudioPlayPermission();
              } else {
                this.voiceShow = true;
              }
            },
            (error) => {
              console.log('权限申请失败：', error);
              this.voiceShow = true;
            }
          );
        } else if (uni.getSystemInfoSync().platform === 'ios') {
          // iOS平台权限检查
          // iOS在录音时会自动弹出权限申请，这里直接设置为false
          this.voiceShow = false;
          this.checkAudioPlayPermission();
        } else {
          // 其他平台直接通过
          this.voiceShow = false;
        }
      },
      // App端录音权限检查
      // #ifdef APP-PLUS
      // App端音频播放权限检查
      checkAudioPlayPermission() {
        console.log('检查App端音频播放权限');
        try {
          // 测试音频播放能力
          const testAudio = uni.createInnerAudioContext();
          testAudio.src =
            'data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT';
          testAudio.onError((error) => {
            console.error('音频播放测试失败:', error);
          });
          testAudio.onCanplay(() => {
            console.log('音频播放测试成功');
            testAudio.destroy();
          });
        } catch (error) {
          console.error('音频播放权限检查失败:', error);
        }
      },
      // #endif
      // 音频播放错误处理
      handleAudioError() {
        console.log('处理音频播放错误');

        // #ifdef APP-PLUS
        // App端音频错误处理
        try {
          // 停止当前播放
          if (innerAudioContext) {
            innerAudioContext.stop();
          }

          // 重新创建音频上下文
          setTimeout(() => {
            this.recreateAudioContext();
          }, 1000);
        } catch (error) {
          console.error('音频错误处理失败:', error);
          uni.showToast({
            title: '音频播放失败',
            icon: 'none'
          });
        }
        // #endif

        // #ifdef MP-WEIXIN || H5
        // 小程序和H5端错误处理
        uni.showToast({
          title: '音频播放失败，请重试',
          icon: 'none'
        });
        // #endif
      },

      // 重新创建音频上下文
      recreateAudioContext() {
        console.log('重新创建音频上下文');
        let that = this;

        try {
          // 销毁旧的音频上下文
          if (innerAudioContext) {
            innerAudioContext.destroy();
          }

          // 创建新的音频上下文
          innerAudioContext = uni.createInnerAudioContext();

          // #ifdef APP-PLUS
          innerAudioContext.autoplay = false;
          // #endif

          // 重新绑定事件
          innerAudioContext.onPlay(() => {
            console.log('重新创建后开始播放');
            that.isplay = true;
          });

          innerAudioContext.onStop(() => {
            console.log('重新创建后播放结束');
            that.isplay = false;
          });

          innerAudioContext.onError((res) => {
            console.error('重新创建后仍然出错:', res);
            that.isplay = false;
            uni.showToast({
              title: '音频播放失败',
              icon: 'none'
            });
          });

          console.log('音频上下文重新创建完成');
        } catch (error) {
          console.error('重新创建音频上下文失败:', error);
        }
      },

      changeVoiceConfig(type) {
        if (type == 0) {
          this.voiceShow = false;
          setTimeout(() => {
            this.checkMicrophonePermission();
          }, 1000);
        } else {
          // 跳转至设置页
          uni.openSetting();
          // this.accreditOption();
        }
      },
      // 说话时间短
      talkingShort() {
        console.log(213);
        innerAudioContext.stop();
        if (this.disabled) return;
        setTimeout(() => {
          uni.showToast({
            title: '说话时间太短啦',
            icon: 'none'
          });
        }, 500);
      },
      // 优化的触摸事件处理
      handleTouchStart(e) {
        console.log('触摸开始');
        if (this.disabled) return;

        // 记录触摸开始时间和位置
        this.touchStartTime = Date.now();
        this.isLongPress = false;

        const touch = e.touches[0];
        this.touchStartPos = {
          x: touch.clientX,
          y: touch.clientY
        };

        // 设置长按定时器
        this.touchTimer = setTimeout(() => {
          this.isLongPress = true;
          console.log('触发长按事件');
          this.startTalkLong();
        }, this.longPressDelay);
      },

      handleTouchMove(e) {
        if (this.disabled) return;

        const touch = e.touches[0];
        const currentX = touch.clientX;
        const currentY = touch.clientY;

        // 计算移动距离
        const moveDistance = Math.sqrt(Math.pow(currentX - this.touchStartPos.x, 2) + Math.pow(currentY - this.touchStartPos.y, 2));

        // 如果移动距离超过阈值，取消长按
        if (moveDistance > this.maxMoveDistance) {
          this.clearTouchTimer();
        }

        // 如果已经是长按状态，更新录音组件的位置信息
        if (this.isLongPress) {
          this.audioXY = {
            x: currentX,
            y: currentY
          };
        }
      },

      handleTouchEnd() {
        console.log('触摸结束');
        if (this.disabled) return;

        // 清除定时器
        this.clearTouchTimer();

        if (this.isLongPress) {
          // 长按结束，触发录音结束
          console.log('长按结束，停止录音');
          this.audioTouchendShow = true;
        } else {
          // 短按，显示提示
          console.log('短按，显示提示');
          this.talkingShort();
        }

        // 重置状态
        this.isLongPress = false;
      },

      handleTouchCancel() {
        console.log('触摸取消');
        // 触摸被取消（如来电话等），清理状态
        this.clearTouchTimer();
        this.isLongPress = false;
      },

      // 清除触摸定时器
      clearTouchTimer() {
        if (this.touchTimer) {
          clearTimeout(this.touchTimer);
          this.touchTimer = null;
        }
      },

      // 手指移开 (保留原方法兼容性)
      audioTouchmove(e) {
        let x = e.changedTouches[0].clientX;
        let y = e.changedTouches[0].clientY;
        this.audioXY = {
          x: x,
          y: y
        };
      },
      // 录音长按
      async startTalkLong() {
        var that = this;
        that.showTopic = false;
        if (that.disabled) return;
        innerAudioContext.stop();
        let arr = that.reviewList.filter((i) => i.word == that.displayData.word);
        if (arr && arr.length > 0) {
          that.reviewList = that.reviewList.filter((i) => i.word != that.displayData.word);
        }
        let arr1 = that.wordList.filter((o) => o.word == that.displayData.word);
        if (arr1 && arr1.length > 0) {
          that.wordList = that.wordList.filter((p) => p.word != that.displayData.word);
        }
        that.audioShow = true;
      },
      //授权录音
      accreditOption() {
        // #ifdef MP-WEIXIN
        // 微信小程序授权
        wx.authorize({
          scope: 'scope.record',
          success(res) {
            console.log('录音授权成功', res);
          },
          fail() {
            console.log('第一次录音授权失败');
            wx.showModal({
              title: '提示',
              content: '您未授权录音，功能将无法使用',
              showCancel: true,
              confirmText: '授权',
              confirmColor: '#AF1F25',
              success(res) {
                if (res.confirm) {
                  //确认则打开设置页面（自动切到设置页）
                  wx.openSetting({
                    success: (res) => {
                      console.log(res.authSetting);
                      if (!res.authSetting['scope.record']) {
                        console.log('未设置录音授权');
                        wx.showModal({
                          title: '提示',
                          content: '您未授权录音，功能将无法使用', // 可以自己编辑
                          showCancel: false,
                          success: function () {}
                        });
                      } else {
                        //第二次才成功授权
                        console.log('设置录音授权成功');
                      }
                    },
                    fail: function () {
                      console.log('授权设置录音失败');
                    }
                  });
                } else if (res.cancel) {
                  console.log('cancel');
                }
              },
              fail() {
                console.log('openfail');
              }
            });
          }
        });
        // #endif

        // #ifdef APP-PLUS
        // App端授权
        this.checkAppRecordPermission();
        // #endif

        // #ifdef H5
        // H5端不需要特殊授权
        console.log('H5端录音授权');
        // #endif
      },
      // 取消发送语音
      async closeAudio(e) {
        console.log(e, 'eeeeeeeeeeeeeeeeeeeeeeee');
        let that = this;
        that.audioTouchendShow = false;
        that.audioShow = false;
      },

      // 发送语音
      async submitAudio(url, savedFilePath) {
        console.log(url, savedFilePath);
        let that = this;
        that.disabled = true;
        if (url) {
          uni.showLoading({
            title: '发音中',
            duration: 1500
          });
          that.wordList.push({
            audioUrl: url,
            word: that.displayData.word,
            chinese: that.displayData.translation
          });
          that.originList.forEach((i) => {
            if (i.word == that.displayData.word) {
              that.$set(i, 'hasResult', true);
            }
          });
          console.log(that.originList, ' that.originList that.originList that.originList');
          var model = {
            word: that.displayData.word,
            scheduleCode: that.displayData.scheduleCode,
            correct: 0,
            translation: that.displayData.translation,
            lastReviewCycle: that.displayData.lastReviewCycle,
            lastReviewDate: that.displayData.lastReviewDate,
            lastStudyTime: that.displayData.lastStudyTime,
            nextReviewDate: that.displayData.nextReviewDate,
            nowRound: that.displayData.nowRound
          };
          that.reviewList.push(model);
          setTimeout(() => {
            that.disabled = false;
            if (that.configData.autoReviewNext == 1 && that.originIndex + 1 < this.originList.length) {
              that.originIndex++;
            } else if (that.configData.autoReviewNext != 1) {
              that.fingleShow = true;
              that.showTopic = false;
            }
          }, 1500);
          if (that.reviewList.length == that.originList.length) {
            uni.showToast({
              title: '您已完成强化复习'
            });
            setTimeout(() => {
              uni.removeStorage({
                key: 'aiForgetWordData',
                success(res) {
                  that.goBack();
                }
              });
            }, 1000);
          }
        } else {
          that.disabled = false;
          uni.showToast({
            title: '发音失败',
            icon: 'none'
          });
        }
      },

      goSetting() {
        this.showTopic = false;
        innerAudioContext.stop();
        uni.navigateTo({
          url: `/antiAmnesia/review/aiReviewSetting?studentCode=${this.studentCode}`
        });
      },
      stopTouch(e) {
        console.log(e);
        if (this.originIndex + 1 == this.originList.length) {
          uni.showToast({
            title: '已经是最后一个啦',
            icon: 'none'
          });
        }
      },
      /**
       * swiper滑动时候
       */
      swiperChange(event) {
        let that = this;
        innerAudioContext.stop();
        let { current } = event.detail;
        console.log(current, that.originIndex);
        that.isDisabled = false;
        that.fingleShow = false;
        that.showTopic = false;
        that.originIndex = current;
        that.displayData = that.originList[current];
        that.playVoice();
      }
    }
  };
</script>

<style lang="scss">
  page {
    background-color: #f5f8fa;
  }

  /* App端专用样式 */
  /* #ifdef APP-PLUS */
  .main-content {
    padding-bottom: env(safe-area-inset-bottom);
  }

  .app-swiper {
    /* App端swiper优化 */
    transform: translateZ(0);
    -webkit-transform: translateZ(0);
    max-height: 1400rpx !important;
    min-height: 1000rpx !important;
    overflow: hidden !important;
  }
  /* #endif */

  /* 导航栏样式优化 */
  .nav-setting-text {
    font-size: 24rpx;
    color: #000;
    /* #ifdef APP-PLUS */
    font-size: 28rpx;
    /* #endif */
  }

  .page-content {
    border-radius: 14rpx;
    // width: calc(100% - 60rpx);
    background-color: #f5f8fa;
    padding: 30rpx 0 30rpx 0;
  }

  .swiper-content {
    display: flex;
    flex-direction: column;
    justify-content: space-around;
    /* #ifdef APP-PLUS */
    justify-content: space-between;
    padding: 40rpx 0;
    /* #endif */
  }
  .top {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 10rpx;
    z-index: 999;
  }
  .word {
    // margin-top: 100rpx;
    width: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-around;
    align-items: center;
    padding-bottom: 200rpx;
    /* #ifdef APP-PLUS */
    padding-bottom: 80rpx;
    justify-content: center;
    /* #endif */
    .toast {
      font-size: 24rpx;
      color: #b0b0b0;
    }
    .english {
      font-size: 56rpx;
      color: #333333;
      font-weight: 600;
      /* #ifdef APP-PLUS */
      font-size: 60rpx;
      letter-spacing: 2rpx;
      margin: 40rpx 0;
      /* #endif */
    }
    .chinese {
      width: 100%;
      font-size: 40rpx;
      color: #333333;
      font-weight: 600;
    }
  }
  .right-false {
    width: 100%;
    margin-top: 200rpx;
    display: flex;
    align-items: center;
    justify-content: space-around;
    image {
      width: 266rpx;
      height: 80rpx;
    }
  }
  .start-talking {
    width: 150rpx;
    height: 50rpx;
    background: #f1fffa;
    border-radius: 50rpx;
    border: 2rpx solid #4fb693;
    transition: all 0.2s ease;
    /* #ifdef APP-PLUS */
    margin-top: 20rpx;
    /* #endif */
    .start-talk {
      width: 100%;
      height: 100%;
      justify-content: center;
      color: #4fb693;
      font-size: 28rpx;
    }
  }

  /* 按钮hover效果 */
  .button-hover {
    background: #e8f8f2 !important;
    transform: scale(0.95);
  }
  .footer_show {
    display: flex;
    justify-content: space-around;
    align-items: center;
    width: 100%;
    margin-top: 100rpx;
    .start-talking {
      width: 300rpx;
      height: 100rpx;
      background: #f1fffa;
      border-radius: 50rpx;
      border: 2rpx solid #4fb693;
      .start-talk {
        width: 100%;
        height: 100%;
        justify-content: center;
        color: #4fb693;
        font-size: 28rpx;
      }
    }
    .showChinese {
      width: 300rpx;
      height: 100rpx;
      background: #f1fffa;
      border-radius: 50rpx;
      border: 2rpx solid #4fb693;
      .showChinese_btn {
        width: 100%;
        height: 100%;
        justify-content: center;
        color: #4fb693;
        font-size: 28rpx;
      }
    }
  }
  .toast {
    font-size: 24rpx;
    color: #b0b0b0;
  }
  .toastgreen {
    font-size: 24rpx;
    color: #4fb693;
  }
  .disabled {
    background: #f8f8f8 !important;
    border: 2rpx solid #acacac !important;
  }
  .disabled-btn {
    color: #a6a6a6 !important;
  }
  .footer {
    width: 100%;
    position: absolute;
    bottom: 100rpx;
    display: flex;
    justify-content: center;
    align-content: center;
    margin-top: 60rpx;
    /* #ifdef APP-PLUS */
    bottom: calc(20rpx + env(safe-area-inset-bottom));
    position: relative;
    margin-top: 40rpx;
    /* #endif */
    .footer_button {
      width: 600rpx;
      height: 92rpx;
      line-height: 92rpx;
      text-align: center;
      color: #fff;
      background: #428a6f;
      border-radius: 46rpx;
      border: 1px solid #428a6f;
      font-size: 32rpx;
      transition: all 0.2s ease;
      /* #ifdef APP-PLUS */
      box-shadow: 0 4rpx 12rpx rgba(66, 138, 111, 0.3);
      /* #endif */
    }
    .start-talk {
      width: 100%;
      height: 100%;
      justify-content: center;
      color: #fff;
      font-size: 28rpx;
    }
  }

  /* 录音按钮hover效果 */
  .record-button-hover {
    background: #3a7a5f !important;
    transform: scale(0.98);
    /* #ifdef APP-PLUS */
    box-shadow: 0 2rpx 8rpx rgba(66, 138, 111, 0.5) !important;
    /* #endif */
  }

  /* 录音状态样式 */
  .footer_button.recording {
    background: #ff4757 !important;
    border-color: #ff4757 !important;
    animation: recording-pulse 1.5s infinite;
    /* #ifdef APP-PLUS */
    box-shadow: 0 4rpx 16rpx rgba(255, 71, 87, 0.4) !important;
    /* #endif */
  }

  /* 录音脉冲动画 */
  @keyframes recording-pulse {
    0% {
      transform: scale(1);
      box-shadow: 0 4rpx 12rpx rgba(255, 71, 87, 0.3);
    }
    50% {
      transform: scale(1.02);
      box-shadow: 0 6rpx 20rpx rgba(255, 71, 87, 0.5);
    }
    100% {
      transform: scale(1);
      box-shadow: 0 4rpx 12rpx rgba(255, 71, 87, 0.3);
    }
  }
  .title-bg {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    height: 90rpx;
    background: #f6f9fc;
    border-radius: 8rpx;
  }

  // .start-talk {
  //   width: 240rpx;
  //   height: 60rpx;
  //   display: flex;
  //   justify-content: center;
  //   align-items: center;
  //   background: linear-gradient(180deg, #88cfba 0%, #1d755c 100%);
  //   border-radius: 14rpx;
  // }

  .word-play {
    padding: 10rpx 24rpx;
    height: 40rpx;
    background: #f3f3f3;
    border-radius: 18rpx;
  }
  .warp {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
  }
  .warp1 {
    height: 100%;
    .title {
      font-size: 28rpx;
      color: #ffffff;
      font-weight: 600;
    }
    .title-center {
      font-size: 28rpx;
      color: #17ca8a;
      font-weight: 600;
      position: absolute;
      left: 50%;
      top: 50%;
      transform: translate(-50%, -50%);
    }
    .finger {
      position: absolute;
      right: 0;
      bottom: 100rpx;
      .title {
        font-size: 28rpx;
        color: #ffffff;
        font-weight: 600;
        margin-bottom: 10rpx;
      }
    }
  }
  .rect {
    color: #fff;
    font-weight: bold;
    .number {
      font-size: 300rpx;
      margin-top: 50rpx;
    }
    .text {
      font-size: 40rpx;
      margin-top: 10rpx;
    }
  }
  .blur {
    // filter: blur(16rpx);
    // pointer-events: none;
    font-size: 40rpx;
    color: #333333;
    filter: blur(12rpx); /* 添加模糊效果 */
  }
  .modal-title {
    font-size: 28rpx;
    color: #555555;
    margin-bottom: 10rpx;
  }
  .confirmButton {
    display: flex;
    justify-content: space-around;
    align-items: center;
    .btn {
      width: 264rpx;
      height: 82rpx;
      border-radius: 12rpx;
      line-height: 82rpx;
      text-align: center;
    }
    .cancel {
      background: #f1f1f1;
      color: #555;
    }
    .confirm {
      background: #428a6f;
      color: #fff;
    }
  }
  .topicToast-box {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 30rpx;
  }
  .topicToast {
    background-color: #626262;
    color: #fff;
    font-weight: 600;
    font-size: 28rpx;
    width: 600rpx;
    height: 50rpx;
    line-height: 50rpx;
    border-radius: 20rpx;
    // position: absolute;
    // top: 120rpx;
    // left: 50%;
    // z-index: 99;
    // transform: translateX(-50%);
  }
  .overlay {
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: #808080;
    /* #ifdef APP-PLUS */
    padding-bottom: env(safe-area-inset-bottom);
    /* #endif */
  }

  .overlay-image {
    height: 100%;
    /* #ifdef APP-PLUS */
    max-height: calc(100% - env(safe-area-inset-bottom));
    /* #endif */
  }
  .finger {
    position: absolute;
    right: 0;
    bottom: 100rpx;
    .title {
      font-size: 28rpx;
      color: #000;
      font-weight: 600;
      margin-bottom: 10rpx;
    }
  }

  .topicToastBottom-box {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    position: absolute;
    right: -9%;
    // top: 150rpx;
    top: 10%;
    // transform: translateX(-50%);
    width: 620rpx;
    z-index: 99;
    // background-color: #f0f0f0;
    // padding: 20rpx 28rpx;
    // border-radius: 24rpx;
    // font-size: 24rpx;
    // font-weight: 600;
    // box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.6);
  }
  .chat-bubble {
    display: flex;
    flex-direction: column;
    align-items: center;
    max-width: 70%;
    margin: 20rpx auto;
    position: relative;
  }
  .bubble-content {
    background-color: #f0f0f0;
    padding: 20rpx 28rpx;
    border-radius: 24rpx;
    font-size: 24rpx;
    font-weight: 600;
    position: relative;
    box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.6);
  }

  .arrow-top {
    position: relative;
    width: 4rpx;
    height: 100rpx;
    margin: 0 auto;
    background: repeating-linear-gradient(to top, transparent, transparent 10rpx, #999 10rpx, #999 20rpx);
  }
  .arrow-top::after {
    content: '';
    position: absolute;
    top: -10rpx;
    left: 50%;
    transform: translateX(-50%);
    border-left: 12rpx solid transparent;
    border-right: 12rpx solid transparent;
    border-bottom: 20rpx solid #999;
  }
  .arrow-down {
    position: relative;
    width: 4rpx;
    height: 100rpx;
    margin: 0 auto;
    background: repeating-linear-gradient(to bottom, transparent, transparent 10rpx, #999 10rpx, #999 20rpx);
  }

  .arrow-down::after {
    content: '';
    position: absolute;
    bottom: -10rpx; /* ⬅️ 让箭头出现在底部 */
    left: 50%;
    transform: translateX(-50%);
    border-left: 12rpx solid transparent;
    border-right: 12rpx solid transparent;
    border-top: 20rpx solid #999; /* ⬅️ 朝下箭头 */
  }
</style>
<style>
  .u-navbar__content__right {
    right: 200rpx !important;
  }
  .u-nav-slot {
    width: 100%;
  }
  .u-nav-slot-center {
    width: 100%;
    text-align: left;
    padding-left: 100rpx;
  }
  .locked-swiper {
    touch-action: none !important; /* 禁止所有手势 */
    pointer-events: none !important; /* 禁止点击事件（可选） */
  }

  /* App端强制swiper高度控制 */
  /* #ifdef APP-PLUS */
  swiper {
    max-height: 1400rpx !important;
    min-height: 1000rpx !important;
  }

  .app-swiper swiper-item {
    height: 100% !important;
    max-height: 1400rpx !important;
    min-height: 1000rpx !important;
    overflow: hidden !important;
  }
  /* #endif */
  .colorYellow {
    color: #de8470 !important;
  }
</style>
