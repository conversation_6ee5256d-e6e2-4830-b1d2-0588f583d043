<template>
  <view class="space">
    <!-- 自定义导航栏 -->
    <view class="box-bg">
      <uni-nav-bar height="65px" backgroundColor="#F3F8FC">
        <block slot="left">
          <view class="left-icon" @click="back">
            <uni-icons type="back" color="#666" size="26" />
          </view>
        </block>
        <view class="leave-title">学习反馈</view>
      </uni-nav-bar>
    </view>
    <view style="height: 140rpx"></view>
    <!-- 自定义导航栏 -->
    <view class="content" v-show="loading">
      <view class="row">日期：{{ row.dateTime }}</view>
      <view class="row">姓名：{{ row.studentName }}</view>
      <view class="row">年级：{{ row.gradeName }}</view>
      <view class="row">课程类型：{{ row.curriculumName }}</view>
      <view class="row">学员编号：{{ row.studentCode }}</view>
      <view class="row">时间：{{ row.studyTime }}</view>
      <view class="row warp">
        实际时间：
        <view style="display: inline-block">
          <view class="flex-c">
            <view class="time" style="margin-right: 6rpx">{{ row.actualStart }}</view>
            至
            <view style="margin-left: 6rpx" class="time">{{ row.actualEnd }}</view>
          </view>
        </view>
      </view>
      <!-- 正式课 -->
      <view v-if="current == 0">
        <view class="row">学习学时：{{ row.studyHour }}小时</view>
        <view class="row">已购鼎英语学时：{{ row.totalCourseHours }}小时</view>
        <view class="row">剩余鼎英语学时：{{ row.leaveCourseHours }}小时</view>
        <view class="rowf">
          <view style="width: 160rpx">所学词库：</view>
          <view class="row">
            <view class="row" v-for="(item, index) in row.studyBooks" :key="index">{{ item }}</view>
          </view>
        </view>
        <view class="row">
          <view class="col">复习：{{ row.reviewWords }}</view>
          <view class="col">复习遗忘：{{ row.forgetWords }}</view>
          <view class="col">复习遗忘率：{{ row.forgetRate }}%</view>
        </view>
        <view class="row">
          <view class="col">学新：{{ row.newWords }}</view>
          <view class="col">学新遗忘：{{ row.newForget }}</view>
          <view class="col">学新遗率：{{ row.newForgetRate }}%</view>
        </view>
        <view class="rowf">
          <view style="width: 160rpx">学习进度：</view>
          <view class="row">
            <view class="row" v-for="item in row.learnSchedule" :key="item">{{ item }}</view>
          </view>
        </view>
        <view class="row">
          今日共识记词汇
          <span style="font-size: 14px; color: #9f9f9f">(复习遗忘词汇+学新词汇)</span>
          ：{{ row.todayWords }}个
        </view>
        <view class="row">学习效率：{{ row.studyRate }}%</view>
      </view>
      <view v-if="current == 1">
        <view class="row">试学学时：{{ row.studyHour }}小时</view>
        <view class="row">词汇量测试水平：{{ row.vocabularyLevel }}</view>
        <view class="row">首测词汇量：{{ row.expWords }}个</view>
        <view class="row">识记词汇数量：{{ row.todayWords }}</view>
        <view class="row">遗忘数量：{{ row.forgetWords }}</view>
        <view class="row">记忆率：{{ row.wordsRate }}%</view>
        <view class="row">体验词库：{{ row.studyBooks }}</view>
        <view class="row">记忆特点：{{ row.memoryTime }}分钟记住{{ row.memoryNum }}个单词</view>
        <view class="row">体验后学习意愿：{{ row.studyIntention }}</view>
      </view>
    </view>
    <view class="comment" v-if="current == 1">
      <view class="row" style="font-weight: bold">复习时间：</view>
      <view class="row" v-for="(item, index) in row.reviewDateList" :key="index">{{ item }}</view>
    </view>
    <view class="comment">
      <view class="row" style="font-weight: bold">
        {{ current == 0 ? '教练评语：' : '学生学习状况反馈：' }}
      </view>
      <view class="comentText">
        <text style="width: 100%; display: inline-block; white-space: pre-wrap; word-wrap: break-word; height: auto">
          {{ row.feedback }}
        </text>
      </view>
    </view>
  </view>
</template>
<script>
  const { httpUser } = require('@/util/luch-request/indexUser.js');
  import config from '../../util/config';
  export default {
    data() {
      return {
        loading: false,
        current: 0, // 1体验课 0正式课
        userInfo: {},
        row: {},
        app: 0,
        type: '' // 类型
      };
    },
    onLoad(option) {
      if (option.token) {
        this.app = option.app;
        this.$handleTokenFormNative(option);
      }
      this.userInfo = JSON.parse(option.data);
      this.current = this.userInfo.experience ? 1 : 0;
      this.init();
    },
    onUnload() {
      // #ifdef APP-PLUS
      if (this.app) {
        plus.runtime.quit();
      }
      // #endif
    },
    methods: {
      formatDate(dateString) {
        const date = new Date(dateString);
        const year = date.getFullYear();
        const month = ('0' + (date.getMonth() + 1)).slice(-2); // 月份从0开始，所以加1并补零
        const day = ('0' + date.getDate()).slice(-2); // 日期补零
        const hours = ('0' + date.getHours()).slice(-2); // 小时补零
        const minutes = ('0' + date.getMinutes()).slice(-2); // 分钟补零

        // 获取星期几，注意 getDay() 返回的是 0(周日) 到 6(周六)
        const daysOfWeek = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
        const dayOfWeek = daysOfWeek[date.getDay()];

        return `${month}月${day}日 ${dayOfWeek} ${hours}:${minutes}`;
      },
      async init() {
        uni.showLoading({
          title: '加载中'
        });
        this.loading = false;
        try {
          if (this.current == 1) {
            let res = await this.$httpUser.post('deliver/app/teacher/getExperienceInfo/' + this.userInfo.id);
            this.row = res.data.data;
            if (this.row.reviewDateList) {
              this.row.reviewDateList = this.row.reviewDateList.map((e) => this.formatDate(e));
            }
          } else {
            let obj = { id: this.userInfo.id, type: 1 };
            let res = await this.$httpUser.get('deliver/app/teacher/getFeedbackInfo', obj);
            this.row = res.data.data;
            this.row.studyBooks = this.row.studyBooks.split('、');
            this.row.learnSchedule = this.row.learnSchedule.split(',');
          }
          this.loading = true;
          uni.hideLoading();
        } catch (e) {
          setTimeout(() => {
            uni.navigateBack();
          }, 1000);

          //TODO handle the exception
        }
      },
      // 左侧按钮返回上一页
      back() {
        uni.navigateBack();
      }
    }
  };
</script>

<style lang="scss" scoped>
  $nav-height: 30px;
  // 导航栏
  .box-bg {
    position: fixed;
    height: 140rpx;
    background-color: #f3f8fc;
    z-index: 999;
    width: 100%;

    /deep/.uni-navbar__header {
      width: 100%;
      position: fixed;
      z-index: 999;
      background-color: #f3f8fc;
    }

    .left-icon {
      margin-top: 100rpx;
    }

    .leave-title {
      // text-align: center;
      font-size: 36rpx;
      margin: auto;
      margin-top: 100rpx;
      font-weight: 540;
    }
  }
  .space {
    /* #ifdef APP-PLUS */
    // margin-top: 50px;
    /* #endif */
    background-color: #f3f8fc;
    padding-bottom: 40rpx;
    .content {
      background-color: #fff;
      border-radius: 10rpx;
      margin: 0 30rpx;
      padding: 30rpx;
    }
    .comment {
      background-color: #fff;
      margin: 40rpx 30rpx 20rpx;
      padding: 30rpx;
      border-radius: 10rpx;
      .comentText {
        background-color: #f7f7f7;
        padding: 40rpx;
        color: #000;
        line-height: 50rpx;
      }
    }
    .row {
      display: flex;
      min-height: 80rpx;
      align-items: center;
      &.warp {
        flex-wrap: wrap;
      }
      .col {
        display: inline-block;
        width: 50%;
        height: 80rpx;
      }
      .time {
        display: inline-block;
        font-size: 23rpx;
        padding: 10rpx 6rpx;
        border-radius: 6rpx;
        border: 2rpx solid #d8d8d8;
        color: #888888;
      }
    }
    .rowf {
      display: flex;
      margin-bottom: 15rpx;
    }
  }
</style>
