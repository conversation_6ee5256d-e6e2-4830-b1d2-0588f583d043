<template>
  <view class="plr-30">
    <view class="positioning" @click="goback">
      <uni-icons type="left" size="24" color="#000"></uni-icons>
    </view>
    <view class="word-position t-c col-12">
      <view v-if="type == 1" class="f-34">当日词预览</view>
      <view v-else class="f-34">往期词预览</view>
    </view>
    <view class="f-32 mt-180 bg-ff p-30 radius-15">
      <view class="flex_s flex-y-c">
        <view>课程名称：</view>
        <view class="time-style flex f-30 flex_1">
          <!-- <view @click="getCourse" class="wh-90 flex_x"> -->
          <view class="wh-90 flex_x">
            <!-- <image class="one mr-15" src="/static/index/time-icon.png"></image>
						<text class="overstepSingle" :class="lesson? '':'date-style'">{{lesson || '请选择'}}</text>
						<u-picker :show="courseShow" :immediateChange ='true' :columns="couserlist" @cancel="cancelCourse"
							@confirm="confirmCourse" keyName="courseName" confirmColor="#357B71" :defaultIndex="[0]" ref="coursePicker"></u-picker> -->
            <u--input placeholder="请输入课程名称" border="none" @change="changeCourse" clearable></u--input>
          </view>

          <!-- <uni-icons v-if="lesson" type="closeempty" size="16" color="#C8C8C8" class="right-icon"
						@click.stop="emptyCouese"></uni-icons>
					<uni-icons v-else type="right" size="16" color="#C8C8C8" class="right-icon"></uni-icons> -->
        </view>
      </view>
      <view class="flex_s flex-y-c mt-30" v-if="type == 2">
        <view>日期：</view>
        <view class="time-style flex f-30 flex_1">
          <view @click="getTime" class="wh-90 flex_x">
            <image class="one mr-15" src="/static/index/time-icon.png"></image>
            <text :class="date ? '' : 'date-style'">{{ date || '请选择' }}</text>
            <u-picker
              :show="show"
              :immediateChange="true"
              :columns="beforeDate"
              keyName="time"
              @cancel="cancel"
              @confirm="confirm"
              confirmColor="#357B71"
              :defaultIndex="[0]"
              ref="timePicker"
            ></u-picker>
          </view>

          <uni-icons v-if="date" type="closeempty" size="16" color="#C8C8C8" class="right-icon" @click.stop="emptyDate"></uni-icons>
          <uni-icons v-else type="right" size="16" color="#C8C8C8" class="right-icon"></uni-icons>
        </view>
      </view>
    </view>

    <view class="bg-ff radius-15 plr-20 mt-30" v-if="allWord.totalItems != 0" :style="{ height: useHeight + 'rpx' }" style="position: relative">
      <view class="flex_s mt-30 c-66 f-32">
        <view class="col-4">课程名称</view>
        <view class="col-2 t-c">轮次</view>
        <view v-if="type == 2" class="col-6 t-r">应复习日期</view>
        <view v-else class="col-6 t-r">学习时间</view>
      </view>

      <scroll-view
        :scroll-top="scrollTop"
        scroll-y="true"
        class="scroll-Y ptb-20"
        @scrolltoupper="upper"
        @scrolltolower="lower"
        @scroll="scroll"
        :style="{ height: wordHeight + 'rpx' }"
      >
        <view class="flex_s mt-60 c-66 f-30" v-for="item in allWord.data" :key="item.id">
          <view class="col-4">{{ item.pdCourseName }}</view>
          <view class="col-2 t-c">{{ item.reviewNum }}</view>
          <view v-if="type == 2" class="col-6 t-r">{{ item.shouldReviewTime.slice(0, 10) || '' }}</view>
          <view v-else class="col-6 t-r">{{ item.lastReviewTime }}</view>
        </view>
      </scroll-view>

      <view class="title paging">
        <pagePagination
          ref="pageChange"
          :total="page.total"
          :pageSize="page.pageSize"
          :numAround="true"
          :currentPage="page.currentPage"
          size="large"
          @change="change"
        ></pagePagination>
      </view>
    </view>

    <view class="bg-ff radius-15 plr-20 mt-30 t-c flex-col" v-else :style="{ height: useHeight + 'rpx' }" style="position: relative">
      <image src="/static/index/<EMAIL>" mode="widthFix" class="mb-20 img_s"></image>
      <view style="color: #bdbdbd">暂无数据</view>
    </view>
  </view>
</template>

<script>
  import pagePagination from './components/page-pagination/components/page-pagination/page-pagination.vue';

  export default {
    components: {
      pagePagination
    },
    data() {
      return {
        page: {
          total: 1,
          pageSize: 9,
          currentPage: 1
        },
        useHeight: 0, // 除头部之外高度
        type: '', // 1当日单词 2往期单词
        studentCode: '', //学员Code
        lesson: '', // 课程
        courseCode: '', //  课程code
        couserlist: [], // 课程列表
        courseShow: false, // 课程选择
        datetime: '', // 日期时间
        date: '', //显示日期
        show: false,
        allWord: {}, // 所有单词（包括当日单词 、往期单词）
        beforeDate: [], // 往期单词日期集合
        scrollTop: 0,
        old: {
          scrollTop: 0
        },
        wordHeight: 0
        // defaultIndex:[0], // 默认索引
      };
    },
    onLoad(options) {
      // this.studentCode = options.studentCode;
      this.studentCode = uni.getStorageSync('pyfStudentCode');
      console.log('this.studentCode', this.studentCode);
      this.type = options.type;
    },
    onReady() {
      uni.getSystemInfo({
        success: (res) => {
          // 可使用窗口高度，将px转换rpx
          let h = res.windowHeight * (750 / res.windowWidth);
          if (this.type == 1) {
            this.useHeight = h - 370;
            this.wordHeight = this.useHeight - 220;
          } else {
            this.useHeight = h - 470;
            this.wordHeight = this.useHeight - 220;
          }
        }
      });
    },
    onShow() {
      if (this.type == 1) {
        this.getTodayWords();
      } else {
        this.getBeforeWords();
      }
    },
    methods: {
      // 课程选择(弹出层)
      // getCourse() {
      // 	// debugger
      // 	// this.$refs.coursePicker.setIndexs([0])  // 注意这里是数组[索引值]
      // 	if (this.type == 2) {
      // 		if (this.lesson == '') {
      // 			this.courseShow = true;
      // 			this.getCourseList();
      // 		} else {
      // 			this.getCourseList();
      // 			this.courseShow = true;
      // 			if (this.datetime != '') {
      // 				this.getBeforeWords();
      // 			}
      // 		}
      // 		if (this.lesson == '' && this.datetime == '') {
      // 			this.courseCode = '';
      // 			this.getBeforeWords();
      // 			this.getCourseList();
      // 			this.getBeforeDate();
      // 		}
      // 	}
      // 	else {
      // 		if (this.lesson == '') {
      // 			// this.courseShow = true;
      // 			// this.getCourseList();
      // 		} else {
      // 			this.courseCode = '';
      // 			this.courseShow = true;
      // 			this.getTodayWords();
      // 		}
      // 	}
      // },
      getTime() {
        // this.$refs.timePicker.setIndexs([0])    // 注意这里是数组[索引值]
        // debugger
        this.show = true;
        this.getBeforeDate();
        if (this.lesson != '') {
          this.getBeforeWords();
        }
        if (this.lesson == '' && this.date == '') {
          this.courseCode = '';
          this.getBeforeWords();
          // this.getCourseList();
          this.getBeforeDate();
        }
      },
      confirmCourse(e) {
        this.lesson = e.value[0].courseName;
        this.courseCode = e.value[0].courseCode;
        this.courseShow = false;
        if (this.type == 1) {
          this.page.currentPage = 1;
          this.$refs.pageChange.nowPage = 1;
          this.getTodayWords();
        } else {
          this.page.currentPage = 1;
          this.$refs.pageChange.nowPage = 1;
          this.getBeforeWords();
        }
      },
      cancelCourse() {
        this.courseShow = false;
        // if(this.type==1){
        // 	this.getTodayWords();
        // }else{
        // 	this.getBeforeWords();
        // }
      },
      // 日期选择
      confirm(e) {
        console.log(e);
        this.datetime = e.value[0].time;
        this.date = e.value[0].time;
        this.show = false;
        this.page.currentPage = 1;
        this.$refs.pageChange.nowPage = 1;
        this.getBeforeWords();
      },
      cancel() {
        this.show = false;
        // this.getBeforeWords();
      },

      // 清空课程
      emptyCouese() {
        this.lesson = '';
        this.courseCode = '';
        this.$refs.pageChange.nowPage = 1;
        if (this.type == 2) {
          this.getBeforeWords();
        } else {
          this.getTodayWords();
        }
      },
      // 清空日期
      emptyDate() {
        this.date = '';
        this.datetime = '';
        this.$refs.pageChange.nowPage = 1;
        this.getBeforeWords();
      },
      // 分页器
      change(currentPage, type) {
        console.log('currentPage,currentPage', currentPage, type);
        this.page.currentPage = currentPage;
        if (this.type == 1) {
          this.getTodayWords();
        } else {
          this.getBeforeWords();
        }
      },
      // 获得当日单词
      async getTodayWords() {
        uni.showLoading();
        let res = await this.$httpUser.post('znyy/pd/planReview/queryTodayReviewed', {
          studentCode: this.studentCode,
          pageNum: this.page.currentPage,
          pageSize: this.page.pageSize,
          pdCourseName: this.lesson
        });
        uni.hideLoading();
        if (res.data.success) {
          this.allWord = res.data.data;
          console.log('this.allWord', this.allWord);
          this.page.total = this.allWord.totalItems;
        }
      },

      // 获得往期单词
      async getBeforeWords() {
        uni.showLoading();
        let res = await this.$httpUser.post('znyy/pd/planReview/queryPastReviewed', {
          studentCode: this.studentCode,
          pageNum: this.page.currentPage,
          pageSize: this.page.pageSize,
          pdCourseName: this.lesson,
          dateStr: this.datetime
        });
        uni.hideLoading();
        if (res.data.success) {
          this.allWord = res.data.data;
          this.page.total = this.allWord.totalItems;
        }
      },

      // 获取往期单词日期列表
      async getBeforeDate() {
        uni.showLoading();
        let res = await this.$httpUser.post('znyy/pd/planReview/queryPastReviewedDate', {
          studentCode: this.studentCode
        });
        uni.hideLoading();
        if (res.data.success) {
          let timelist = [];
          timelist.push(res.data.data);
          let data = timelist[0].map((item) => ({
            time: item
          }));
          data.sort(function (a, b) {
            return a.time < b.time ? 1 : -1;
          });
          this.beforeDate = [];
          this.beforeDate.push(data);
        }
      },

      changeCourse(e) {
        console.log('e', e);
        this.lesson = e.toString().trim();
        uni.showLoading();
        this.page.currentPage = 1;
        if (this.type == 2) {
          if (this.lesson == '') {
            this.getBeforeWords();
          } else {
            this.getBeforeWords();
            if (this.datetime != '') {
              this.getBeforeWords();
            }
          }
          if (this.lesson == '' && this.datetime == '') {
            this.courseCode = '';
            this.getBeforeWords();
            // this.getCourseList();
            this.getBeforeDate();
          }
        } else {
          this.getTodayWords();
        }

        uni.hideLoading();
      },
      // 获取课程列表
      // async getCourseList() {
      // 	uni.showLoading();
      // 	// let res = await this.$httpUser.get('znyy/review/query/fun/word/queryBeforeCourseName', {
      // 	// 	studentCode: this.studentCode,
      // 	// 	date: this.datetime,
      // 	// 	type: this.type
      // 	// });
      // 	this.getTodayWords()
      // 	uni.hideLoading();
      // 	if (res.data.success) {
      // 		this.couserlist = []
      // 		this.couserlist.push(res.data.data)
      // 	}
      // },

      goback() {
        uni.navigateBack();
      },

      upper: function (e) {
        console.log(e);
      },
      lower: function (e) {
        console.log(e);
      },
      scroll: function (e) {
        console.log(e);
        this.old.scrollTop = e.detail.scrollTop;
      }
    }
  };
</script>

<style>
  page {
    background-color: #f3f8fc;
  }
</style>

<style lang="scss" scoped>
  .positioning {
    position: fixed;
    top: 100rpx;
    left: 30rpx;
    z-index: 9;
  }

  .word-position {
    position: fixed;
    top: 100rpx;
    left: 0;
  }

  .mt-180 {
    margin-top: 180rpx;
  }

  .flex_s {
    display: flex;
  }

  .flex_1 {
    flex: 1;
  }

  .flex_x {
    display: flex;
    align-items: center;
  }

  .title {
    width: 100%;
    text-align: center;
  }

  .paging {
    position: absolute;
    bottom: 40rpx;
    left: 0;
  }

  /deep/.page-pagination .page-con .page-scroll-child .page-num .ellipsis-btn {
    padding: 0 !important;
    border: 0 !important;
  }

  .time-style {
    position: relative;
    border: 1px solid #c8c8c8;
    width: 240rpx;
    padding: 12rpx 25rpx;
    border-radius: 35rpx;
  }

  .right-icon {
    position: absolute;
    top: 12rpx;
    right: 15rpx;
  }

  .one {
    width: 30rpx;
    height: 30rpx;
    min-width: 30rpx;
  }

  .date-style {
    color: #666;
  }

  .img_s {
    width: 160rpx;
  }

  /deep/.u-toolbar.data-v-55c89db1 {
    border-bottom: 1px solid #e0e0e0;
  }

  /deep/.u-line-1 {
    line-height: 68rpx !important;
    background-color: #f4f4f4 !important;
  }

  /deep/.u-picker__view {
    height: 440rpx !important;
  }

  /deep/.u-picker__view__column.data-v-f45a262e {
    border-radius: 12rpx;
  }

  /deep/.u-popup__content.data-v-3a231fda {
    border-radius: 12rpx;
    margin: 0 20rpx 20rpx 20rpx;
  }

  /deep/.u-picker__view__column__item {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    font-size: 30rpx !important;
    margin-left: 10rpx;
  }

  scroll-view ::-webkit-scrollbar {
    width: 0;
    height: 0;
    background-color: transparent;
  }

  .wh-90 {
    width: 94%;
  }

  /deep/.u-popup__content {
    margin: 20rpx;
    border-radius: 15rpx;
  }
</style>
