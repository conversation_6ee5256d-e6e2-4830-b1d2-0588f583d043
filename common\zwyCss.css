/*
	在App.vue全局引入common文件夹下的zwyCss.css
	为你想要效果的view添加class  ||  hover-class
	插件地址: https://ext.dcloud.net.cn/plugin?id=1973
*/
@keyframes zwyHeightThree {
	0% {
		transform: translate(0,
			0) translate(-50%, -50%);
	}

	35% {
		transform: translate(50%,
			0) translate(-50%, -50%);
	}

	50% {
		transform: translate(50%,
			50%) translate(-50%, -50%);
	}

	85% {
		transform: translate(0,
			50%) translate(-50%, -50%);
	}

	100% {
		transform: translate(0,
			0) translate(-50%, -50%);
	}

}

.zwyHeightThree::before {
	width: 200%;
	height: 200%;
	background: radial-gradient(circle at center, var(--heightColor) 0%, transparent 20%);
	animation: zwyHeightThree var(--speed) linear infinite;
}

.zwyHeightThree::after {
	opacity: 0.85;
	background: inherit;
	margin: 2px;
	top: 0;
	right: 0;
	bottom: 0;
	left: 0;
	border-radius: var(--borderRadius);
}

.zwyHeightThree::before,
.zwyHeightThree::after {
	content: '';
	display: block;
	position: absolute;
	top: 0;
	left: 0;
	z-index: -1;
}

.zwyHeightThree {
	position: relative;
	overflow: hidden;
	--borderRadius:100rpx;
	--heightColor:#fff;
	--speed:5s;
	border-radius: var(--borderRadius);
	z-index: 2;
}

.zwyHand {
	position: absolute;
	left: 30rpx;
	top: 40%;
	animation: zwyHand .3s infinite alternate;
}

@keyframes zwyHand {
	from {
		transform: translate(0, 0);
	}

	to {
		transform: translate(-10rpx, -10rpx);
	}
}

.zwyRotateAndScale {
	transform-origin: center bottom;
	animation: zwyRotateAndScale 1.2s .3s ease-in-out infinite;
}

@keyframes zwyRotateAndScale {
	0% {
		transform: rotate(0) scale(1);

	}

	30% {
		transform: rotate(0) scale(1);
	}

	45% {
		transform: rotate(-10deg) scale(.8);
	}

	65% {
		transform: rotate(-20deg) scale(.8);
	}

	100% {
		transform: rotate(0) scale(1);
	}
}

.zwyHide {
	background: linear-gradient(270deg, var(--btnColor), var(--btnColor), var(--bgColor), var(--bgColor));
	background-size: 300% 300%;
	background-position: 99% 50%;
	--bgColor: white;
	--btnColor: #1AAD19;
	color: var(--bgColor);
	animation: zwyHide 1s var(--count) ease both;
	--count: infinite;
}

@keyframes zwyHide {
	0% {
		background-position: 99% 50%;
	}

	100% {
		background-position: 1% 50%;
	}
}


@keyframes zwyCart {
	7% {
		transform: scale(1, 1);
	}

	10% {
		transform: scale(1, 0.8);
	}

	13% {
		transform: scale(1, 1);
	}

	37% {
		transform: scale(1, 1);
	}

	40% {
		transform: scale(1, 0.8);
	}

	43% {
		transform: scale(1, 1);
	}
}

.zwyCart {
	transform-origin: bottom;
	width: 30rpx;
	height: 30rpx;
	position: relative;
	animation: zwyCart 3s infinite ease;
}

@keyframes zwyDrop {
	0% {
		top: -30%;
		border-radius: 50%;
		background-color: #fff;
	}

	3% {
		top: 0;
		border-radius: 50%;
		background-color: #fff;
	}

	6% {
		top: 30%;
		height: 5rpx;
		border-radius: 10rpx 10rpx 5rpx 5rpx;
		transform: scaleX(1.7);
	}

	15% {
		top: 10%;
		height: 10rpx;
		border-radius: 50%;
		transform: scaleX(1);
	}

	30% {
		top: 40%;
		height: 10rpx;
		opacity: 1;
		border-radius: 50%;
		transform: scaleX(1);
		background-color: #fff;

	}

	31% {
		opacity: 0;
	}

	33% {
		top: -30%;
		opacity: 0;
	}

	34% {
		border-radius: 0;
		background-color: #1C9FFF;
		box-shadow: 0rpx 0rpx 10rpx 0rpx rgba(28, 159, 255, 0.4);
		opacity: 1;
		top: -30%;
	}

	40% {
		top: 30%;
		transform-origin: center bottom;
		transform: scaleX(0.8) scaleY(1.4);
	}

	49% {
		top: 10%;
		transform-origin: center bottom;
		transform: scaleX(1.3) scaleY(0.7);
	}

	64% {
		top: 40%;
		opacity: 1;
		transform-origin: center top;
		transform: scaleX(1) scaleY(1);
	}

	67% {
		opacity: 0;
	}

	100% {
		opacity: 0;
	}
}


.zwyDrop {
	width: 10rpx;
	height: 10rpx;
	position: absolute;
	-webkit-backface-visibility: hidden;
	-webkit-transform-style: preserve-3d;
	left: 0;
	right: 0;
	margin: 0 auto;
	transform-origin: 50%;
	animation: zwyDrop 3s infinite ease;
}



@keyframes zwyHeightSec {
	100% {
		background-position: right -40px top 0px;
	}
}

.zwyHeightSec {
	position: relative;
	overflow: hidden;
}

.zwyHeightSec::before {
	content: '';
	position: absolute;
	width: 100%;
	height: 100%;
	left: 0;
	right: 0;
	top: 0;
	bottom: 0;
	margin: auto;
	background-image: linear-gradient(90deg, rgba(255, 255, 255, 0), rgba(255, 255, 255, 0.5), rgba(255, 255, 255, 0));
	background-size: 40px 100%;
	background-repeat: no-repeat;
	background-position: left -40px top 0;
	animation: zwyHeightSec 1s ease infinite;
}


.zwyHeight {
	position: relative;
	overflow: hidden;
}

.zwyHeight::before {
	content: '';
	position: absolute;
	background-image: linear-gradient(to right, rgba(255, 255, 255, 0), rgba(255, 255, 255, 1));
	width: 140%;
	height: 80rpx;
	left: 140%;
	top: 0;
	bottom: 0;
	margin: auto;
	transform: rotateZ(-45deg);
	animation: isLeft 5s 2.3s infinite;
}

@keyframes isLeft {
	0% {
		left: -140%;
	}

	12% {
		left: 140%;
	}

	100% {
		left: 140%;
	}
}


/* 
	<view wx:for="{{3}}" style="animation-delay:{{index+'s'}}" class="zwyChevron" key="{{index}}" ></view>
*/
.zwyChevron {
	position: absolute;
	width: 14px;
	height: 4px;
	opacity: 0;
	transform: scale3d(0.5, 0.5, 0.5);
	animation: move 3s ease-out infinite;
}

.zwyChevron:before,
.zwyChevron:after {
	content: ' ';
	position: absolute;
	top: 0;
	margin: auto;
	height: 100%;
	width: 51%;
	background: #fff;
}

.zwyChevron:before {
	left: 0;
	transform: skew(0deg, 30deg);
}

.zwyChevron:after {
	right: 0;
	width: 50%;
	transform: skew(0deg, -30deg);
}

@keyframes move {
	25% {
		opacity: 1;
	}

	33% {
		opacity: 1;
		transform: translateY(30px);
	}

	67% {
		opacity: 1;
		transform: translateY(40px);
	}

	100% {
		opacity: 0;
		transform: translateY(55px) scale3d(0.5, 0.5, 0.5);
	}
}


@keyframes shockwave {
	0% {
		-webkit-transform: scale(1);
		transform: scale(1);
		box-shadow: 0 0 2px var(--color), inset 0 0 1px var(--color);
		opacity: 1;
	}

	95% {
		box-shadow: 0 0 50px var(--color), inset 0 0 30px var(--color);
		opacity: 0;
	}

	100% {
		-webkit-transform: scale(2.25);
		transform: scale(2.25);
		opacity: 0;
	}
}

.zwyHot {
	position: relative;
	--color: rgba(0, 0, 0, 0.15);
}

.zwyHot::before {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	bottom: 0;
	right: 0;
	border-radius: 50%;
	animation: shockwave 3s 0.5s ease-out infinite;
}

.zwyHot::after {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	bottom: 0;
	right: 0;
	border-radius: 50%;
	animation: shockwave 3s 0.65s ease-out infinite;
}

@keyframes zwyHover1 {
	0% {
		transform: scale(1);
	}

	50% {
		transform: scale(0.9);
	}

	100% {
		transform: scale(1);
	}
}

.zwyHover1 {
	--count: infinite;
	animation: zwyHover1 .6s var(--count) !important;
}

.zwyShake {
	transform-origin: center bottom;
	animation: zwyShake 2s 0.5s ease-out infinite;
}

@keyframes zwyShake {
	0% {
		transform: rotate(0deg);
		transition-timing-function: cubic-bezier(0.215, .61, .355, 1);
	}

	10% {
		transform: rotate(-12deg);
		transition-timing-function: cubic-bezier(0.215, .61, .355, 1);
	}

	20% {
		transform: rotate(12deg);
		transition-timing-function: cubic-bezier(0.215, .61, .355, 1);
	}

	28% {
		transform: rotate(-10deg);
		transition-timing-function: cubic-bezier(0.215, .61, .355, 1);
	}

	36% {
		transform: rotate(10deg);
		transition-timing-function: cubic-bezier(0.755, .5, .855, .06);
	}

	42% {
		transform: rotate(-8deg);
		transition-timing-function: cubic-bezier(0.755, .5, .855, .06);
	}

	48% {
		transform: rotate(8deg);
		transition-timing-function: cubic-bezier(0.755, .5, .855, .06);
	}

	52% {
		transform: rotate(-4deg);
		transition-timing-function: cubic-bezier(0.755, .5, .855, .06);
	}

	56% {
		transform: rotate(4deg);
		transition-timing-function: cubic-bezier(0.755, .5, .855, .06);
	}

	60% {
		transform: rotate(0deg);
		transition-timing-function: cubic-bezier(0.755, .5, .855, .06);
	}

	100% {
		transform: rotate(0deg);
		transition-timing-function: cubic-bezier(0.215, .61, .355, 1);
	}
}

.zwyHover2 {
	z-index: 999;
	position: relative;
	--color: #ff0081;
	--count: infinite;
}

.zwyHover2::before,
.zwyHover2::after {
	position: absolute;
	content: '';
	display: block;
	width: 140%;
	height: 100%;
	left: -20%;
	z-index: -1000;
	transition: all ease-in-out 0.5s;
	background-repeat: no-repeat;
}

.zwyHover2::before {
	top: -55%;
	background-image: radial-gradient(circle, var(--color) 20%, transparent 20%), radial-gradient(circle, transparent 20%, var(--color) 20%, transparent 30%), radial-gradient(circle, var(--color) 20%, transparent 20%), radial-gradient(circle, var(--color) 20%, transparent 20%), radial-gradient(circle, transparent 10%, var(--color) 15%, transparent 20%), radial-gradient(circle, var(--color) 20%, transparent 20%), radial-gradient(circle, var(--color) 20%, transparent 20%), radial-gradient(circle, var(--color) 20%, transparent 20%), radial-gradient(circle, var(--color) 20%, transparent 20%);
	background-size: 0% 0%, 0% 0%, 0% 0%, 0% 0%, 0% 0%, 0% 0%;
	animation: topBubbles ease-in-out 0.75s var(--count);
	display: block;
}

.zwyHover2::after {
	bottom: -55%;
	background-image: radial-gradient(circle, var(--color) 20%, transparent 20%), radial-gradient(circle, var(--color) 20%, transparent 20%), radial-gradient(circle, transparent 10%, var(--color) 15%, transparent 20%), radial-gradient(circle, var(--color) 20%, transparent 20%), radial-gradient(circle, var(--color) 20%, transparent 20%), radial-gradient(circle, var(--color) 20%, transparent 20%), radial-gradient(circle, var(--color) 20%, transparent 20%);
	background-size: 0% 0%, 0% 0%, 0% 0%, 0% 0%, 0% 0%, 0% 0%;
	display: block;
	animation: bottomBubbles ease-in-out 0.75s var(--count);
}


@keyframes topBubbles {
	0% {
		background-position: 5% 90%, 10% 90%, 10% 90%, 15% 90%, 25% 90%, 25% 90%, 40% 90%, 55% 90%, 70% 90%;
		background-size: 10% 10%, 20% 20%, 15% 15%, 20% 20%, 18% 18%, 10% 10%, 15% 15%, 10% 10%, 18% 18%;
	}

	50% {
		background-position: 0% 80%, 0% 20%, 10% 40%, 20% 0%, 30% 30%, 22% 50%, 50% 50%, 65% 20%, 90% 30%;
		background-size: 10% 10%, 20% 20%, 15% 15%, 20% 20%, 18% 18%, 10% 10%, 15% 15%, 10% 10%, 18% 18%;
	}

	100% {
		background-position: 0% 70%, 0% 10%, 10% 30%, 20% -10%, 30% 20%, 22% 40%, 50% 40%, 65% 10%, 90% 20%;
		background-size: 0% 0%, 0% 0%, 0% 0%, 0% 0%, 0% 0%, 0% 0%;
	}
}

@keyframes bottomBubbles {
	0% {
		background-position: 10% -10%, 30% 10%, 55% -10%, 70% -10%, 85% -10%, 70% -10%, 70% 0%;
		background-size: 15% 15%, 20% 20%, 18% 18%, 20% 20%, 15% 15%, 10% 10%, 20% 20%;
	}

	50% {
		background-position: 0% 80%, 20% 80%, 45% 60%, 60% 100%, 75% 70%, 95% 60%, 105% 0%;
		background-size: 15% 15%, 20% 20%, 18% 18%, 20% 20%, 15% 15%, 10% 10%, 20% 20%;
	}

	100% {
		background-position: 0% 90%, 20% 90%, 45% 70%, 60% 110%, 75% 80%, 95% 70%, 110% 10%;
		background-size: 0% 0%, 0% 0%, 0% 0%, 0% 0%, 0% 0%, 0% 0%;
	}
}

@keyframes zwyHover3 {
	0% {
		transform: scale(1, 1);
	}

	33% {
		transform: scale(1, 1.2);
	}

	66% {
		transform: scale(1.2, 1);
	}

	100% {
		transform: scale(1, 1);
	}
}

.zwyHover3 {
	--count: infinite;
	animation: zwyHover3 .3s var(--count) !important;
}

.zwyFly {
	animation: zwyFly 2s infinite alternate !important;
}

@keyframes zwyFly {
	100% {
		transform: translateY(12%);
	}
}

.zwyRotate {
	animation-name: zwyRotate;
	animation-duration: 10s;
	animation-timing-function: linear;
	animation-iteration-count: infinite;
}

@keyframes zwyRotate {
	0% {
		transform: rotate(0deg);
	}

	100% {
		transform: rotate(1turn);
	}

	0% {
		transform: rotate(0deg);
	}

	100% {
		transform: rotate(1turn);
	}
}

.zwyLive {
	width: 10%;
	height: 50%;
	position: relative;
	--color: white;
	background-color: var(--color);
	transform-origin: bottom;
	animation: zwyLive 0.6s 0.2s infinite ease-in-out;
}

.zwyLive::after {
	right: 200%;
	animation: zwyLive 0.6s 0.4s infinite ease-in-out;
}

.zwyLive::before {
	left: 200%;
	animation: zwyLive 0.6s 0s infinite ease-in-out;
}

.zwyLive::after,
.zwyLive::before {
	width: 100%;
	height: 100%;
	content: '';
	position: absolute;
	bottom: 0;
	background-color: var(--color);
	transform-origin: bottom;
}

@keyframes zwyLive {

	0%,
	100% {
		transform: scaleY(1);
	}

	50% {
		transform: scaleY(0.6);
	}
}

.zwyMusic {
	position: relative;
	--contentBefore: '♫';
	--contentAfter: '♩';
	--color: #000000;
	--zIndex: 9999;
	z-index: var(--zIndex);
}

.zwyMusic::after,
.zwyMusic::before {
	position: absolute;
	left: -10%;
	bottom: -30%;
	color: var(--color);
	z-index: calc(var(--zIndex) - 1);
	opacity: 0;
	transform: translateY(0) translateX(0) scale(0);
}

.zwyMusic::after {
	content: var(--contentAfter);
	animation: zwyMusic 2.5s 1.25s linear infinite;
}

.zwyMusic::before {
	content: var(--contentBefore);
	animation: zwyMusic 2.5s linear infinite;
}

@keyframes zwyMusic {
	0% {
		transform: translateX(0) translateY(0) scale(0);
		opacity: 0;
	}

	5% {
		transform: translateY(0) translateX(0) scale(.6);
		opacity: 1;
	}

	50% {
		transform: translateX(-40rpx) translateY(-50rpx) scale(1);
		opacity: 1;
	}

	90% {
		transform: translateX(-50rpx) translateY(-100rpx) scale(1);
		opacity: 0;
	}

	93% {
		transform: translateX(-50rpx) translateY(-100rpx) scale(0);
		opacity: 0;
	}

	100% {
		transform: translateX(0) translateY(0) scale(0);
		opacity: 0;
	}
}


.zwyPortrait {
	position: relative;
	--color: #ff0081;
}

.zwyPortrait::after,
.zwyPortrait::before {
	position: absolute;
	content: '';
	left: 0;
	right: 0;
	bottom: 0;
	top: 0;
	margin: auto;
	box-sizing: border-box;
	border: 1rpx solid var(--color);
	border-radius: 50%;
	width: 100%;
	height: 100%;
}

.zwyPortrait::after {
	transform: scale(1.1);
	animation: zwyPortrait 0.8s linear infinite;
}

@keyframes zwyPortrait {

	0%,
	100% {
		transform: scale(1);
		opacity: 0;
	}

	1% {
		transform: scale(1);
		opacity: 1;
	}

	95% {
		transform: scale(1.3);
		opacity: 0;
	}
}

@keyframes zwyBeat {
	0% {
		transform: scale(1);
	}

	50% {
		transform: scale(0.8);
	}

	100% {
		transform: scale(1);
	}
}

.zwyBeat {
	animation: zwyBeat 0.8s linear infinite;
}
