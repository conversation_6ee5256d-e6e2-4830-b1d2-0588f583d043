<template>
	<view style="height: 100vh;background-color:#F5F8FA;">
		<view class="detailTop">
			<view :class="detailsIndex===0?'details':''" @tap="clickCode(0)">
				全部
			</view>
			<view :class="detailsIndex===1?'details':''" @tap="clickCode(1)">
				待付款
			</view>
			<view :class="detailsIndex===2?'details':''" @tap="clickCode(2)">
				已完成
			</view>
		</view>
		<view>
			<view class="f-28 p-20" v-if="pointsList&&pointsList.length>0">
				<view class=" radius-16 mb-20" v-for="(item,index) in pointsList" :key="index">
					<view class="bg-ff p-10 sizing">
						<view class="flex-s lh-90" style="line-height: 90rpx;border-bottom: 2rpx solid #d0d0d0;">
							<view class="type">
								{{item.id}}
							</view>
							<view class="f-24 typeStatus t-c">
								<text v-if="item.status==1"
									class="f-26 t-c radius-8 tobe_paid button_right_css">未支付</text>
								<text v-if="item.status==2" class="f-26 radius-8 t-c paid button_right_css">支付中</text>
								<text v-if="item.status==3"
									class="f-26 radius-8 t-c button_right_css completed">已支付</text>
								<text v-if="item.status==4" class="c-ff f-26 t-c button_right_css refunded">支付失败</text>
							</view>
						</view>
						<view class="mtb-25">
							采购人名称: {{item.userName}}
						</view>
						<view class="mtb-25">
							采购类型: {{item.procureType}}
						</view>
						<view class="">
							订购内容: {{item.procureContent}}
						</view>
						<view class="mtb-25">
							数量(个): {{item.procureNum}}
						</view>
						<view class="">
							订购单价(元): {{item.amount}}
						</view>
						<view class="mtb-25">
							总金额(元): {{item.totalAmount}}
						</view>
						<view class="flex-a-c flex-x-e f-28 mt-24" v-if="item.displayPayment==0">
							<!-- <view class="button" @tap="updateClickIndex(index, 0)"
							:class="item.status===0?'clickButton':''" :key="index">
							编辑
						</view> -->
							<view class="button mlr-30" @tap="updateClickIndex(index, 1)"
								:class="item.isClick===1?'clickButton':''" :key="index">
								完款
							</view>
							<view class="button" @tap="updateClickIndex(index, 2)"
								:class="item.isClick===2?'clickButton':''" :key="index">
								抵扣
							</view>
						</view>
					</view>
				</view>
			</view>
			<view v-else class="curriculum_css_no pt-30 pb-55 f-28">
				<image class="curriculum_image"
					src="https://document.dxznjy.com/course/ac587707bf314badadb28a158852c77d.png"></image>
				<view class="c-66 f-24 mtb-25">暂无明细</view>
			</view>
			<view v-if="no_more && pointsList != undefined && pointsList.length>0">
				<u-divider text="到底了"></u-divider>
			</view>
		</view>
	</view>
	</view>
</template>

<script>
	const {
		$http
	} = require("@/util/methods.js")
	export default {
		data() {
			return {
				temp:false,
				detailsIndex: 0,
				tooltipInfoStatus: false,
				page: 1,
				no_more: false,
				timerId: '',
				merchantType: '',
				clubInvitationCodeNum: '',
				partnerInvitationCodeNum: '',
				pointsList: [],
				totalItems:0,
				subType: '',
				userCode: ''
			}
		},
		onShow() {
			this.fetchProcureList()
			this.fetchinvitaCode()
		},
		onLoad(option) {
			if (option != null) {
				this.subType = option.type
				this.userCode = option.userCode
			}
		},
		onHide() {
			clearTimeout(this.timerId)
		},
		onReachBottom() {
			if (this.pointsList.length >= this.totalItems) {
				this.no_more = true
				return false;
			}
			this.fetchProcureList(true, ++this.page);

		},
		methods: {
			//获取剩余邀请码
			async fetchinvitaCode() {
				const res = await $http({
					url: 'zxAdminCourse/web/invitation/codeNum',
					data: {
						merchantCode: this.userCode
					}
				})
				if (res) {
					this.clubInvitationCodeNum = res.data.clubInvitationCodeNum
					this.partnerInvitationCodeNum = res.data.partnerInvitationCodeNum
				}
			},
			// 获取采购单列表
			async fetchProcureList(isPage, page, status) {
				uni.showLoading({
					title:'加载中'
				})
				let parms = {
					pageNum: page || 1,
					pageSize: 10,
				}
				parms.subType = this.subType
				parms.userCode = this.userCode
				if (status) {
					parms.status = status
				}
				const res = await $http({
					url: 'zxAdminCourse/procure/getSubProcureRecordList',
					data: parms
				})
				if (res) {
					if (isPage) {
						let old = this.pointsList;
						this.pointsList = [...old, ...res.data.data]
					} else {
						this.pointsList = res.data.data
						this.totalItems=res.data.totalItems
					}
				}
				 uni.hideLoading()
				this.$forceUpdate()
			},
			clickCode(index) {
				this.detailsIndex = index
				if (this.detailsIndex == 0) {
					this.fetchProcureList()
				}
				if (this.detailsIndex == 1) {
					this.fetchProcureList(false, 1, 1)
				}
				if (this.detailsIndex == 2) {
					this.fetchProcureList(false, 1, 3)
				}
			},
			 updateClickIndex(index, value) {
				 if(this.temp) return 
				 this.temp=true
				this.pointsList.forEach(async (item, idx) => {
					if (idx === index) {
						item.isClick = value;
						if (value === 2) {
							uni.showLoading({
								title: '抵扣中...'
							})
							if (item.procureContent == '合伙人邀请码') {
								if (this.partnerInvitationCodeNum > 0) {
									
									const res = $http({
										url: 'zxAdminCourse/web/invitation/codeDeduction',
										method: 'post',
										data: {
											id: item.id,
										}
									})
									uni.hideLoading()
									if (res) {
										this.temp=false
										uni.showToast({
											title: '抵扣成功',
											icon: 'success'
										})
										setTimeout(()=>{
											this.clickCode(0)
										},500)
										
									}
								} else {
									this.temp=false
									uni.hideLoading()
									uni.showToast({
										title: '无合伙人邀请码，请采购',
										icon: 'none'
									})
								}
							}
							if (item.procureContent == '俱乐部邀请码') {
								if (this.clubInvitationCodeNum > 0) {
									const res =await $http({
										url: 'zxAdminCourse/web/invitation/codeDeduction',
										method: 'post',
										data: {
											id: item.id,
										}
									})
									uni.hideLoading()
									if (res) {
										this.temp=false
										uni.showToast({
											title: '抵扣成功',
											icon: 'success'
										})
									setTimeout(()=>{
										this.clickCode(0)
									},500)
									}
								} else {
									this.temp=false
									uni.hideLoading()
									uni.showToast({
										title: '无俱乐部邀请码，请采购',
										icon: 'none'
									})
								}
							}
						}
						 if (value === 1) {
							 uni.showLoading({
							 	title:'操作中..'
							 })
							 this.$httpUser.post('zxAdminCourse/web/invitation/codePayment', {
							     id: item.id
							 }).then(res=>{
								 this.temp=false
							 	let orderId=res.data.data.data.order.sourceOrderId
								let remark=res.data.data.data.order.remark
								let amount = res.data.data.data.order.amount
								let codeToken= res.data.data.data.token
								uni.hideLoading()
								uni.navigateTo({
									url:'/Recharge/Collection/Collection?type=code&codePrice='+amount+'&orderId='+orderId+'&remark='+remark+'&codeToken='+codeToken
								})
							 })
						
						}
					} else {
						item.isClick = null;
					}
				})
			},
			tooltipInfo() {
				this.tooltipInfoStatus = true
				this.timerId = setTimeout(() => {
					this.tooltipInfoStatus = false
				}, 6000)
			}
		}
	}
</script>

<style lang="scss" scoped>
	.details {
		border-bottom: 4rpx solid #339378;
		font-weight: 700;
	}

	.pointsBox {
		height: 96rpx;
		background-color: #F9FCFE;
		display: flex;
		align-items: center;
	}

	.detailTop {
		margin: 20rpx 0;
		display: flex;
		justify-content: space-evenly;
	}

	.button {
		width: 120rpx;
		line-height: 60rpx;
		text-align: center;
		border-radius: 60rpx;
		color: #4E9F87;
		border: 1px solid #4E9F87;
	}

	.type {
		display: flex;
	}

	.bgf {
		width: 376rpx;
		height: 112rpx;
		margin: 0 auto;
		background: url('https://document.dxznjy.com/course/1e6afed624714ef4825a7dba609ccd58.png') no-repeat;
		background-size: 100%;
	}

	.curriculum_css_no {
		position: relative;
		width: 710rpx;
		margin: auto;
		margin-top: 400rpx;
		text-align: center;

		.curriculum_image {
			width: 122rpx;
			height: 114rpx;
			display: block;
			margin: 16rpx auto;
		}

		.curriculum_title {
			text-align: left;
		}
	}

	.typeStatus {
		width: 90rpx;
		line-height: 40rpx;
		background: rgba(255, 221, 167, 0.15);
		border-radius: 8rpx;
		border: 2rpx solid #FFDDA7;
		color: #FD9B2A;
	}

	.tooltipClass {
		width: 320rpx;
		background: #FFF;
		font-size: 24rpx;
		color: #AFAEAE;
		box-sizing: border-box;
		position: absolute;
		top: 10rpx;
		right: 40rpx;

		.sanjiao {
			width: 0;
			height: 0;
			border: 10rpx solid transparent;
			border-top: 10rpx solid #FFF;
			position: absolute;
			bottom: -20rpx;
			right: 0;
		}
	}
</style>