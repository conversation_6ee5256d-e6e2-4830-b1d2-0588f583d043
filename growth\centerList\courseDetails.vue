<template>
  <view class="wrap">
    <view>
      <h2 class="title">{{ courseDetails.courseName }}</h2>
      <view class="examination">
        <text>是否考试:</text>
        <text class="yes">{{ courseDetails.needExam === 1 ? '是' : '否' }}</text>
        <view class="getCoupons" v-if="courseDetails.needExam === 1 && !courseDetails.isUserPassExam && courseDetails.isCreatedExam" @tap="startExam(courseDetails.id)">
          开始考试
        </view>
        <view class="is_getCoupons" v-if="courseDetails.isUserPassExam">考试已通过</view>
        <!-- <view class="getCoupons" @tap="startExam(courseDetails.id)">开始考试</view> -->
        <view v-if="courseDetails.needExam === 1" class="examRecords" @tap="examRecords(courseDetails.id)">考试记录</view>
      </view>
      <view class="introduce">{{ courseDetails.courseOverview }}</view>
    </view>

    <view class="courseCatalog">
      <text class="line"></text>
      <h2>课程目录</h2>
    </view>

    <view class="classHour_content" v-for="(item, index) in courseDetails.lessonList" @tap="goVideo(item)" :key="index">
      <h2 style="color: #000000; font-size: 28rpx">{{ item.lessonName }}</h2>
      <view class="video">
        <!-- <view v-if="item.lessonType === 1"><uni-icons type="videocam-filled" size="28"></uni-icons></view> -->
        <view v-if="item.lessonType === 1">
          <image class="Preview" src="https://document.dxznjy.com/dxSelect/video.png" />
        </view>
        <view v-if="item.lessonType !== 1">
          <image class="Preview" src="https://document.dxznjy.com/dxSelect/icon_Preview.png" />
        </view>
        <view style="margin-left: 15rpx; color: #555555; font-size: 24rpx">{{ item.lessonType === 1 ? '视频' : '文档' }}</view>
        <view style="margin-left: 15rpx; color: #555555; font-size: 24rpx">{{ formatStatus(item.learningStatus) }}</view>
        <!-- <view style="margin-left: 325rpx;color: #555555; font-size: 24rpx;">06:12</view> -->
      </view>
    </view>
  </view>
</template>

<script>
  const { $http } = require('@/util/methods.js');
  export default {
    data() {
      return {
        courseId: '',
        courseDetails: {}
      };
    },
    onLoad(option) {
      console.log(option, 'hhh');
      this.courseId = option.courseId;
    },
    onShow() {
      this.getCourseDetails();
      console.log('执行了吗');
    },
    methods: {
      // 生成证书
      async createCert() {
        let certificateInfo = JSON.parse(uni.getStorageSync('certificateInfo'));
        let certificateRole = uni.getStorageSync('certificateRole');
        const params = {
          courseId: this.courseId,
          mobile: certificateInfo.mobile,
          realName: certificateInfo.userName,
          roleTag: certificateRole
        };
        await $http({
          url: 'train/web/certificate/createCertificate',
          data: params
        });
      },
      async getLearningStatus(item) {
        const res = await $http({
          url: 'train/web/exam/training/lesson_progress',
          method: 'POST',
          data: {
            userId: uni.getStorageSync('bvAdminId') ? uni.getStorageSync('bvAdminId') : '',
            // courseId: this.courseId,
            learningStatus: 3,
            lessonId: item.id,
            roleTag: uni.getStorageSync('roleTag') ? uni.getStorageSync('roleTag') : ''
          }
        });
        // 若此课时为当前课程最后一课时，且课程无需考试或考试已通过，学习结束则调用生成证书接口
        const isLastLessonAndExamPass = this.courseDetails.lessonList.filter((el) => el.learningStatus != 3).length == 1 && item.learningStatus != 3;
        const isExamPass = this.courseDetails.isUserPassExam == true || this.courseDetails.needExam == 0;
        if (isLastLessonAndExamPass && isExamPass) {
          // 生成证书
          this.createCert();
        }
      },

      goVideo(item) {
        if (item.lessonType === 1) {
          // 此课时是否为当前课程最后一课时，该课程是否无需考试或考试已通过
          const isLastLesson = this.courseDetails.lessonList.filter((el) => el.learningStatus != 3).length == 1 && item.learningStatus != 3;
          const isExamPass = this.courseDetails.isUserPassExam == true || this.courseDetails.needExam == 0;

          uni.navigateTo({
            url: `/Coursedetails/centerList/videoDetails?name=${item.lessonName}&id=${item.id}&courseId=${item.courseId}&filePath=${item.lessonFile.filePath}&isFeedback=${item.isFeedback}&learningStatus=${item.learningStatus}&isLastLesson=${isLastLesson}&isExamPass=${isExamPass}`
            // url: `/growth/centerList/videoDetails?name=${item.lessonName}&filePath=${item.lessonFile.filePath}`
          });
        } else {
          uni.downloadFile({
            url: item.lessonFile.filePath,
            success: (res) => {
              let filePath = res.tempFilePath;
              uni.openDocument({
                filePath: filePath,
                showMenu: true,
                success: (res) => {
                  console.log('打开文档成功', item.id);
                  this.getLearningStatus(item);
                },
                fail: (err) => {
                  uni.showToast({
                    title: '打开文件失败请重试',
                    icon: 'none'
                  });
                }
              });
            },
            fail: (err) => {
              uni.hideLoading();
              uni.showToast({
                title: '加载失败请重试',
                icon: 'none'
              });
            }
          });
        }
      },
      examRecords(id) {
        uni.navigateTo({
          url: `/growth/centerList/examRecords?courseId=${id}`
        });
      },
      // 获取课程详情
      async getCourseDetails() {
        const res = await $http({
          url: 'train/web/exam/training/course/detail',
          data: {
            userId: uni.getStorageSync('bvAdminId') ? uni.getStorageSync('bvAdminId') : '',
            roleTag: uni.getStorageSync('roleTag') ? uni.getStorageSync('roleTag') : '',
            courseId: this.courseId
            // courseId:'1296526404959866880'
          }
        });
        if (res.code !== 20000) return uni.showToast({ title: res.message, icon: 'error' });
        this.courseDetails = res.data;
        console.log(res, '课程详情');
      },
      formatStatus(status) {
        const MAP = {
          1: '未学习',
          2: '学习中',
          3: '已学习'
        };
        // return 格式化之后的中文显示
        return MAP[status];
      },
      // previewPdf(url) {
      // 	uni.downloadFile({
      // 		url: url,
      // 		success: function (res) {
      // 			let filePath = res.tempFilePath;
      // 			uni.openDocument({
      // 				filePath: filePath,
      // 				showMenu: true,
      // 				success: function (res) {
      // 					console.log('打开文档成功');
      // 				},
      // 				fail: err => {
      // 					uni.showToast({
      // 						title: '打开文件失败请重试',
      // 						icon: 'none'
      // 					});
      // 				}
      // 			});
      // 		},
      // 		fail: err => {
      // 			uni.hideLoading();
      // 			uni.showToast({
      // 				title: '加载失败请重试',
      // 				icon: 'none'
      // 			});
      // 		}
      // 	});
      // },
      startExam(id) {
        console.log(id, 'id');
        // 该课程所有课程是否已经全部学完
        const isLessonFinish = this.courseDetails.lessonList.filter((el) => el.learningStatus != 3).length == 0;
        uni.navigateTo({
          url: `/growth/centerList/viewTest?courseId=${id}&isLessonFinish=${isLessonFinish}`
        });
      }
    }
  };
</script>

<style>
  page {
    background-color: #ffffff;
  }
</style>

<style scoped lang="scss">
  .wrap {
    padding: 40rpx 36rpx;
  }

  .title {
    color: #333333;
    font-size: 30rpx;
    font-weight: bold;
  }

  .examination {
    // width: 400rpx;
    display: flex;
    align-items: center;
    margin-top: 30rpx;
    color: #888896;
    font-size: 28rpx;
    // justify-content: space-around;
  }

  // .getCoupons{
  // 	display: flex;
  // 	justify-content: center;
  // 	align-items: center;
  // 	width: 120rpx;
  // 	height: 40rpx;
  // 	background: #339378;
  // 	border-radius: 8rpx;
  // }
  .getCoupons {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 120rpx;
    height: 40rpx;
    background: #339378;
    border-radius: 8rpx;
    color: #ffffff;
    margin-left: 15rpx;
    font-size: 24rpx;
  }

  .is_getCoupons {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 120rpx;
    height: 40rpx;
    background: #cccccc;
    border-radius: 8rpx;
    color: #ffffff;
    margin-left: 15rpx;
    font-size: 24rpx;
  }

  .yes {
    margin-left: 10rpx;
  }

  .examRecords {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 120rpx;
    height: 40rpx;
    border-radius: 8rpx;
    border: 2rpx solid #42917a;
    color: #469880;
    margin-left: 30rpx;
    font-size: 24rpx;
  }

  .introduce {
    margin-top: 20rpx;
    color: #555555;
    font-size: 28rpx;
  }

  .courseCatalog {
    display: flex;
    align-items: center;
    margin-top: 30rpx;
  }

  .line {
    width: 6rpx;
    height: 21rpx;
    background: #339378;
    margin-right: 18rpx;
  }

  .classHour_content {
    background-color: #f5f8fa;
    margin-top: 30rpx;
    padding: 40rpx 0;
  }

  .video {
    display: flex;
    align-items: center;
    margin-top: 30rpx;
  }

  .Preview {
    height: 32rpx;
    width: 32rpx;
    margin-left: 8rpx;
    margin-top: 7rpx;
  }
</style>
