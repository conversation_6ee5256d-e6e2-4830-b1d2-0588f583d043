<template>
  <view class="interesting_popupCenter" :class="isReview ? 'interesting_popupCenter1' : ''">
    <view class="interet_head">
      <image v-if="!isReview" class="interet_popupTitle" :src="imgHost + 'interesting/intert_popupTitle.png'" mode=""></image>
      <image v-if="isReview" class="interet_popupTitle" :src="imgHost + 'interesting/dialog_interesting_review_title.png'" mode=""></image>
      <text v-if="!isReview">{{ title }}</text>
    </view>

    <view class="popup_content" v-if="!isReview">
      <view :style="isRed ? 'color:red;' : ''">{{ txtContent }}</view>
    </view>

    <!-- 复习报告复习词数，错误词数，正确率 -->
    <view class="popup_content" v-if="isReview">
      <view class="reviewPopup_list">
        <text>复习词数</text>
        <text class="DesignFont">{{ reviewWordNum.length + errorWordNum.length }}</text>
      </view>
      <view class="reviewPopup_list">
        <text>错误词数</text>
        <text class="DesignFont" style="color: #ff3c3c">{{ errorWordNum.length }}</text>
      </view>
      <view class="reviewPopup_list">
        <text>正确率</text>
        <text class="DesignFont" style="color: #3c6c3c">{{ correctRate }}%</text>
      </view>
    </view>

    <view class="popBtnGroup" v-if="isSingle">
      <view class="popBtnGroupNowBuy" @click="nowBuy()">立即购买</view>
    </view>
    <view class="popBtnGroup" :class="isReview ? 'popBtnGroup1' : ''" v-if="!isSingle">
      <view class="popBtnGroup_list" @click="confirm()">确认</view>
      <view class="popBtnGroup_list" @click="cancel()">取消</view>
    </view>

    <image @click="closeDialog()" class="interesting_close" :src="imgHost + 'interesting/dialog_closed.png'" mode=""></image>
  </view>
</template>

<script>
  export default {
    // isReview:true,//是否是复习弹窗  isSingle:false,//是否是单个按钮  isRed:true,//字体颜色是否是红色
    props: { title: '', isReview: true, isSingle: false, isRed: true, txtContent: String, reviewWordNum: [], errorWordNum: [], correctRate: 0 },
    data() {
      return {
        imgHost: getApp().globalData.imguseHost
      };
    },
    methods: {
      //立即购买
      nowBuy() {
        this.$emit('nowBuy');
      },
      //确认
      confirm() {
        this.$emit('confirm');
      },
      //取消
      cancel() {
        this.closeDialog();
      },
      //关闭弹窗
      closeDialog() {
        this.$emit('closeDialog');
      }
    }
  };
</script>

<style></style>
