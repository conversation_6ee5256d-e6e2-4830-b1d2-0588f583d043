<template>
  <view class="bg" :style="{ minHeight: useHeight + 'rpx' }">
    <image :src="imgHost + 'dxSelect/fourthEdition/Career_bg.png'" class="bgc"></image>
    <view class="contain">
      <view class="title">
        <view class="problem">{{ index + 1 }}.{{ list[index].problem }}</view>
        <view class="page"
          ><span style="color: #339378">{{ index + 1 }}</span
          >/7</view
        >
      </view>
      <view class="placeholder" v-if="list[index].placeholder">{{ list[index].placeholder }}</view>
      <view class="items" v-if="list[index].type == 'radio'">
        <view
          class="item"
          v-for="item in list[index].answer"
          @click="radio(item)"
          :key="item.id"
          :class="item.lable == answer[index].answer ? 'active' : ''">
          <view class="text">
            {{ item.lable }}
          </view>
          <image :src="imgHost + item.url" class="itemimg"></image>
        </view>
      </view>
      <view class="items" v-if="list[index].type == 'checked'">
        <view
          class="item"
          v-for="item in list[index].answer"
          @click="checked(item)"
          :key="item.id"
          :class="answer[index].answer.includes(item.lable) ? 'active' : ''">
          <view class="text">
            {{ item.lable }}
          </view>
          <image :src="imgHost + item.url" class="itemimg"></image>
        </view>
      </view>
      <view class="items" v-if="list[index].type == 'filling'">
        <view
          class="item"
          v-for="item in list[index].answer"
          @click="radio(item, 1)"
          :key="item.id"
          :class="item.id == answer[index].answer ? 'active' : ''">
          <view class="text">
            {{ item.lable }}
          </view>
          <image :src="imgHost + item.url" class="itemimg"></image>
        </view>
      </view>
      <view v-if="list[index].type == 'filling'">
        <view class="input">
          <view style="margin-right: 40rpx; font-weight: bold">孩子昵称:</view>
          <u--input
            v-model="answer[index].name"
            border="none"
            placeholder="请输入孩子昵称"></u--input>
        </view>

        <view class="input">
          <view style="margin-right: 40rpx; font-weight: bold">孩子生日:</view>
          <uni-datetime-picker
            style="flex: 1"
            type="date"
            :clear-icon="false"
            v-model="answer[index].date" />
        </view>
        <view class="input">
          <view style="margin-right: 40rpx; font-weight: bold">孩子年级:</view>
          <picker
            class="grade-picker"
            @change="bindPickerChange"
            :value="answer[index].index"
            :range="array"
            range-key="label"
            name="studentGrade">
            <view
              style="color: #333333"
              class="text01 f-30"
              :class="array[answer[index].index != null] ? 'c-00' : 'c-99'">
              {{
                answer[index].index != null ? array[answer[index].index].label : "请选择学员年级"
              }}
            </view>
          </picker>
        </view>
      </view>
      <view v-if="list[index].type == 'line'">
        <view class="line" v-for="(item, index1) in list[index].answer" :key="index1">
          <view style="margin-right: 40rpx; font-weight: bold">{{ item }}</view>

          <uni-data-checkbox
            v-model="answer[index].answer[index1].value"
            :localdata="radiolist"></uni-data-checkbox>
        </view>
      </view>
      <view v-if="list[index].type == 'input'">
        <view
          class="line"
          v-for="(item, index1) in list[index].answer"
          :class="show ? 'all' : ''"
          :key="index1">
          <view style="width: 240rpx; font-weight: bold">{{ item }}:</view>
          <view class="rqueited" v-if="index1 < 3">*</view>
          <u--input
            maxlength="3"
            @change="(e) => change(e, index1)"
            v-model="answer[index].answer[index1].value"
            type="number"
            border="none"
            placeholder="请输入成绩"></u--input>
        </view>
        <view
          v-if="show"
          @click="show = !show"
          style="text-align: center; color: #339378; font-size: 24rpx"
          >展开其他科目<uni-icons type="down" color="#339378" size="12"></uni-icons
        ></view>
        <view
          v-else
          @click="show = !show"
          style="text-align: center; color: #339378; font-size: 24rpx"
          >收起其他科目<uni-icons type="up" color="#339378" size="12"></uni-icons
        ></view>
      </view>
    </view>
    <view class="btns" style="">
      <view class="btn top" @click="last" :style="{ opacity: index ? '1' : '0' }"> 上一题 </view>
      <view class="btn next" @click="next" v-if="index < 6"> 下一题 </view>
      <view class="btn next" @click="send" v-else> 生成报告 </view>
    </view>
  </view>
</template>

<script>
  import uniDataCheckbox from "@/Personalcenter/components/uni-data-checkbox/components/uni-data-checkbox/uni-data-checkbox.vue";
  import uniDatetimePicker from "@/Personalcenter/components/uni-datetime-picker/components/uni-datetime-picker/uni-datetime-picker.vue";
  const { $http } = require("@/util/methods.js");
  import { $list } from "@/util/methods/common.js";
  export default {
    data() {
      return {
        useHeight: 0,
        index: 0,
        btn: false,
        show: true,
        radiolist: [
          { text: "优秀", value: 1 },
          { text: "良好", value: 2 },
          { text: "薄弱", value: 3 },
        ],
        imgHost: getApp().globalData.imgsomeHost,
        answer: [
          {
            answer: "",
          },
          {
            answer: "",
          },
          {
            answer: "",
            date: "",
            index: null,
            name: "",
          },
          {
            answer: null,
          },
          {
            answer: [],
          },
          {
            answer: [],
          },
          {
            answer: [],
          },
        ],
        list: [],
        array: [],
        grade: 0,
        mobile: null,
        studentCode: null,
      };
    },
    components: {
      uniDatetimePicker,
      uniDataCheckbox,
    },
    onReady() {
      uni.getSystemInfo({
        success: (res) => {
          // 可使用窗口高度，将px转换rpx
          let h = res.windowHeight * (750 / res.windowWidth);
          this.useHeight = h - 30;
        },
      });
    },
    onLoad(option) {
      this.studentCode = option.id;
      this.list = $list;
      this.init();
      this.mobile = uni.getStorageSync("phone");
    },
    methods: {
      async init() {
        this.array = [
          {
            value: "18",
            label: "幼儿园",
            ext: "",
            children: null,
          },
          {
            value: "1",
            label: "一年级",
            ext: "",
            children: null,
          },
          {
            value: "2",
            label: "二年级",
            ext: "",
            children: null,
          },
          {
            value: "3",
            label: "三年级",
            ext: "",
            children: null,
          },
          {
            value: "4",
            label: "四年级",
            ext: "",
            children: null,
          },
          {
            value: "5",
            label: "五年级",
            ext: "",
            children: null,
          },
          {
            value: "6",
            label: "六年级",
            ext: "",
            children: null,
          },
          {
            value: "7",
            label: "七年级",
            ext: "",
            children: null,
          },
          {
            value: "8",
            label: "八年级",
            ext: "",
            children: null,
          },
          {
            value: "9",
            label: "九年级",
            ext: "",
            children: null,
          },
          {
            value: "10",
            label: "高中一年级",
            ext: "",
            children: null,
          },
          {
            value: "11",
            label: "高中二年级",
            ext: "",
            children: null,
          },
          {
            value: "12",
            label: "高中三年级",
            ext: "",
            children: null,
          },
        ];
        let { data } = await $http({
          url: "zx/career/student/get?studentCode=" + this.studentCode,
          // method:'post'
        });
        this.childObj = data;
        this.answer[2].date = data.birthday;
        this.answer[2].name = data.nick;
        let a = this.array.findIndex((e) => e.value == data.grade);
        this.answer[2].index = a == -1 ? null : a;
        this.answer[2].answer = data.gender;
      },
      radio(item, type) {
        if (type) {
          this.answer[this.index].answer = item.id;
        } else {
          this.answer[this.index].answer = item.lable;
        }
      },
      bindPickerChange: function (e) {
        this.answer[this.index].index = e.target.value;
      },
      change(e, i) {
        if (e - 0 > 150) {
          this.answer[this.index].answer[i].value = null;
        }
      },
      last() {
        if (this.index == 0) return;
        this.index--;
      },
      checked(item) {
        if (this.answer[this.index].answer.includes(item.lable)) {
          this.answer[this.index].answer = this.answer[this.index].answer.filter(
            (e) => e != item.lable
          );
        } else {
          if (this.index == 4 || this.index == 6) {
            if (this.answer[this.index].answer.length == 3) return;
          }
          this.answer[this.index].answer.push(item.lable);
        }
      },
      async send() {
        console.log(2222);
        uni.showLoading({
          title: "生成报告中",
        });
        if (!this.answer[this.index].answer.length)
          return uni.showToast({
            title: "请选择",
            icon: "none",
            duration: 2000,
          });
        let obj = {
          role: this.answer[0].answer,
          business: this.answer[1].answer,
          gender: this.answer[2].answer,
          birthday: this.answer[2].date,
          nick: this.answer[2].name,
          grade: this.array[this.answer[2].index].value,
          parentMobile: this.mobile,
          personalityFeatures: this.answer[4].answer,
          abilities: this.answer[5].answer,
          educationalPlans: this.answer[6].answer,
          studentCode: this.studentCode,
          avatar: this.childObj.avatar,
        };

        if (this.grade == 1) {
          obj.childScore = {};
          obj.childScore.health = this.answer[3].answer[0].value;
          obj.childScore.language = this.answer[3].answer[1].value;
          obj.childScore.society = this.answer[3].answer[2].value;
          obj.childScore.science = this.answer[3].answer[3].value;
          obj.childScore.art = this.answer[3].answer[4].value;
        } else if (this.grade == 2) {
          obj.primaryScore = {};
          obj.primaryScore.chinese = this.answer[3].answer[0].value;
          obj.primaryScore.math = this.answer[3].answer[1].value;
          obj.primaryScore.english = this.answer[3].answer[2].value;
          obj.primaryScore.natural = this.answer[3].answer[3].value;
          obj.primaryScore.society = this.answer[3].answer[4].value;
          obj.primaryScore.art = this.answer[3].answer[5].value;
          obj.primaryScore.music = this.answer[3].answer[6].value;
          obj.primaryScore.sport = this.answer[3].answer[7].value;
          obj.primaryScore.morality = this.answer[3].answer[8].value;
          obj.primaryScore.it = this.answer[3].answer[9].value;
        } else {
          obj.highScore = {};
          obj.highScore.chinese = this.answer[3].answer[0].value;
          obj.highScore.math = this.answer[3].answer[1].value;
          obj.highScore.english = this.answer[3].answer[2].value;
          obj.highScore.physics = this.answer[3].answer[3].value;
          obj.highScore.chemistry = this.answer[3].answer[4].value;
          obj.highScore.biology = this.answer[3].answer[5].value;
          obj.highScore.politics = this.answer[3].answer[6].value;
          obj.highScore.history = this.answer[3].answer[7].value;
          obj.highScore.geography = this.answer[3].answer[8].value;
        }
        let res = await $http({
          url: "zx/career/student/uploadSurvey",
          method: "post",
          data: obj,
        });
        if (res.data) {
          uni.redirectTo({
            url: "/Coursedetails/Career/planning?id=" + res.data,
          });
        } else {
          uni.showToast({
            title: "生成报告失败，请重新生成",
            icon: "none",
            duration: 2000,
          });
        }
        // setTimeout(()=>{
        uni.hideLoading();
        // },2000)
      },
      setFourlist(type) {
        if (type == 1) {
          this.grade = 1;
          this.list[3].type = "line";
          this.list[3].answer = ["健康", "语言", "社会", "科学", "艺术"];
          this.answer[3].answer = [
            { value: null },
            { value: null },
            { value: null },
            { value: null },
            { value: null },
          ];
        } else if (type == 2) {
          this.grade = 2;
          this.list[3].type = "input";
          this.list[3].answer = [
            "语文",
            "数学",
            "英语",
            "自然科学",
            "社会科学",
            "美术",
            "音乐",
            "体育",
            "品德与社会",
            "信息技术",
          ];
          this.answer[3].answer = [
            { value: null },
            { value: null },
            { value: null },
            { value: null },
            { value: null },
            { value: null },
            { value: null },
            { value: null },
            { value: null },
            { value: null },
          ];
        } else {
          this.grade = 3;
          this.list[3].type = "input";
          this.list[3].answer = [
            "语文",
            "数学",
            "英语",
            "物理",
            "化学",
            "生物",
            "政治",
            "历史",
            "地理",
          ];
          this.answer[3].answer = [
            { value: null },
            { value: null },
            { value: null },
            { value: null },
            { value: null },
            { value: null },
            { value: null },
            { value: null },
            { value: null },
          ];
        }
      },
      next() {
        if (this.index < 2) {
          if (!this.answer[this.index].answer)
            return uni.showToast({
              title: "请选择",
              icon: "none",
              duration: 2000,
            });
        }
        if (this.index == 2) {
          let obj = this.answer[this.index];
          if (obj.answer && obj.date && obj.index !== null && obj.name) {
          } else {
            return uni.showToast({
              title: "请填写完整",
              icon: "none",
              duration: 2000,
            });
          }

          if (this.answer[this.index].index == 0) {
            this.setFourlist(1);
          } else if (this.answer[this.index].index < 7) {
            this.setFourlist(2);
          } else {
            this.setFourlist(3);
          }
        }
        if (this.index == 3) {
          if (this.list[3].type == "input") {
            if (this.answer[3].answer.slice(0, 3).find((e) => !e.value)) {
              return uni.showToast({
                title: "请填入必选项",
                icon: "none",
                duration: 2000,
              });
            }
          }
        }
        if (this.index == 4) {
          if (this.answer[4].answer.length < 3)
            return uni.showToast({
              title: "请选择孩子的3个特点吧",
              icon: "none",
              duration: 2000,
            });
        }
        if (this.index > 4) {
          if (!this.answer[this.index].answer.length)
            return uni.showToast({
              title: "请选择",
              icon: "none",
              duration: 2000,
            });
        }
        this.index++;
      },
    },
  };
</script>

<style lang="scss" scoped>
  .bg {
    background-color: #f5f8fa;
    overflow: hidden;
    position: relative;
    ::v-deep .uni-date-x--border {
      border: none;
    }
    .bgc {
      top: 0;
      width: 750rpx;
      height: 540rpx;
      position: absolute;
      z-index: 1;
    }

    .btns {
      margin: 64rpx 30rpx 92rpx;
      display: flex;
      justify-content: space-between;
    }

    .btn {
      height: 74rpx;
      width: 328rpx;
      box-sizing: border-box;
      line-height: 74rpx;
      font-size: 28rpx;
      text-align: center;
      border-radius: 38rpx;
    }

    .top {
      border: 2rpx solid #339378;
      color: #339378;
      background-color: #fff;
    }

    .next {
      background-color: #339378;
      color: #fff;
    }

    .contain {
      position: relative;
      z-index: 2;
      margin: 110rpx 32rpx 0;
      background-color: #fff;
      border-radius: 40rpx;
      min-height: 1094rpx;
      padding: 34rpx;
      box-sizing: border-box;
      .placeholder {
        height: 42rpx;
        color: #c6c6c6;
        font-size: 28rpx;
      }
      .all {
        &:nth-child(n + 4) {
          display: none;
        }
      }
      .line {
        position: relative;
        height: 64rpx;
        margin: 40rpx 0;
        display: flex;
        align-items: center;
        .rqueited {
          position: absolute;
          top: 10rpx;
          left: -16rpx;
          color: red;
        }
      }
      .input {
        display: flex;
        height: 64rpx;
        font-size: 28rpx;
        color: #333333;

        margin: 64rpx 0;
        align-items: center;
      }

      .items {
        margin-top: 72rpx;
        display: flex;
        justify-content: space-between;
        flex-wrap: wrap;

        .active {
          border: 4rpx solid #339378;
        }

        .item {
          position: relative;
          width: 300rpx;
          height: 144rpx;
          margin-bottom: 24rpx;

          .itemimg {
            width: 300rpx;
            height: 144rpx;
            position: absolute;
            top: 0;
            left: 0;
            z-index: 1;
          }

          .text {
            line-height: 144rpx;
            width: 300rpx;
            height: 144rpx;
            padding-left: 32rpx;
            position: absolute;
            top: 0;
            left: 0;
            font-size: 32rpx;
            color: #555555;
            z-index: 2;
            font-weight: bold;
          }
        }
      }

      .title {
        position: relative;
        display: flex;
        height: 50rpx;
        justify-content: space-between;

        .problem {
          color: #339378;
          height: 50rpx;
          font-size: 36rpx;
          line-height: 50rpx;
        }

        .page {
          width: 132rpx;
          height: 64rpx;
          border-radius: 26rpx;
          background-color: #fafafa;
          font-size: 36rpx;
          text-align: center;
          line-height: 64rpx;
          color: #868688;
        }
      }
    }
  }
</style>
