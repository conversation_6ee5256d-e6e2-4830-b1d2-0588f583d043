<!-- 学员信息登记表 -->
<template>
  <page-meta :page-style="'overflow:' + (show ? 'hidden' : 'visible')"></page-meta>
  <view class="bg">
    <view class="title">
      <view class="f-32 t-c">{{ actTitle }}</view>
      <view>为了陪跑能够更加快捷高效落地，为了能够更好帮助到您和您的家庭，请你务必认真、如实填写以下表格：</view>
    </view>
    <scroll-view scroll-y class="bg-ff p-30 scroll-H">
      <uni-forms ref="baseForm" :rules="rules" :modelValue="formData">
        <!-- name表单域的属性名，在使用校验规则时必填 -->

        <view class="partnerFlex" v-for="(item, index) in formItemList" :key="index">
          <uni-forms-item required :name="item.name">
            <view class="label f-30">
              <text>{{ index + 1 }}.{{ item.title }}</text>
            </view>
            <view class="r_box">
              <input
                v-if="item.type == 'input'"
                placeholder-style="color:#999;"
                maxlength="30"
                v-model="formData[item.name]"
                class="flex-box f-30 uni-input"
                placeholder="请输入"
                :disabled="item.name == 'phone'"
              />
              <radio-group v-if="item.type == 'radio'" @change="(val) => radioChange(val, item)">
                <label class="radio-list-cell" :key="it.value" v-for="it in item.data">
                  <radio :value="it.value" />
                  {{ it.label }}
                  <input
                    style="width: 160rpx; margin-left: 20rpx"
                    v-if="it.haveInput && formData[item.name] == it.value"
                    placeholder-style="color:#999;"
                    v-model="formData[it.name]"
                    maxlength="30"
                    class="flex-box f-30 uni-input"
                    placeholder="请输入"
                  />
                </label>
              </radio-group>
            </view>
          </uni-forms-item>
        </view>

        <view class="text">感谢您填写次问卷，开营前2-3天会有班督导老师给您致电，请您注意电话接通，感谢您的支持与理解，期待您在陪跑中成长</view>

        <view class="foot-content">
          <view class="signBtn f-32 c-ff" @click="handleSubmit">立即报名</view>
        </view>
      </uni-forms>
    </scroll-view>
  </view>

  <!-- 温馨提示 -->
  <uni-popup ref="popopTips" type="center" @change="change">
    <view class="dialogBG">
      <view class="reviewCard_box positionRelative">
        <view class="cartoom_image">
          <image src="https://document.dxznjy.com/dxSelect/dialog_icon.png" mode="widthFix"></image>
        </view>
        <view class="review_close" @click="closeDialog">
          <uni-icons type="clear" size="28" color="#B1B1B1"></uni-icons>
        </view>
        <view class="reviewCard">
          <view class="reviewTitle bold">温馨提示</view>
          <view class="t-c mb-10 ptb-5 mt-35">恭喜您已报名成功</view>
          <view class="review_btn" @click="handleOk">确认</view>
        </view>
      </view>
    </view>
  </uni-popup>
</template>

<script>
  const { $http, $navigationTo, $showMsg } = require('@/util/methods.js');
  import uniForms from '../components/uni-forms/components/uni-forms/uni-forms.vue';
  import uniFormsItem from '../components/uni-forms/components/uni-forms-item/uni-forms-item.vue';
  export default {
    components: {
      uniForms,
      uniFormsItem
    },
    data() {
      return {
        show: false, //禁止穿透
        formData: {
          name: '',
          phone: '',
          age: '',
          beforeState: '',
          wantImproveProblem: '',
          parentsRelationship: '',
          childrensRelationship: '',
          childrenNum: '',
          childrenSituation: '',
          studyTimeSituation: '',
          studyTimeSlot: '',
          finishSchoolSituation: '',
          interactSituation: '',
          noteSituation: '',
          lovedSituation: '',
          lovedSituationDiy: '',
          purpose: '',
          referrerName: '',
          referrerPhone: '',
          zxIdentityType: ''
        },
        rules: {
          name: {
            rules: [
              {
                required: true,
                errorMessage: '不能为空'
              }
            ]
          },
          phone: {
            rules: [
              {
                required: true,
                errorMessage: '不能为空'
              },
              {
                pattern: '^1[3-9]\\d{9}$',
                errorMessage: '手机号格式不正确'
              }
            ]
          },
          age: {
            rules: [
              {
                required: true,
                errorMessage: '不能为空'
              },
              {
                pattern: '^(?:[1-9][0-9]?|1[01][0-9]|199)$',
                errorMessage: '年龄格式不正确'
              }
            ]
          },
          beforeState: {
            rules: [
              {
                required: true,
                errorMessage: '不能为空'
              }
            ]
          },
          wantImproveProblem: {
            rules: [
              {
                required: true,
                errorMessage: '不能为空'
              }
            ]
          },
          parentsRelationship: {
            rules: [
              {
                required: true,
                errorMessage: '不能为空'
              }
            ]
          },
          childrensRelationship: {
            rules: [
              {
                required: true,
                errorMessage: '不能为空'
              }
            ]
          },
          childrenNum: {
            rules: [
              {
                required: true,
                errorMessage: '不能为空'
              }
            ]
          },
          childrenSituation: {
            rules: [
              {
                required: true,
                errorMessage: '不能为空'
              }
            ]
          },
          studyTimeSituation: {
            rules: [
              {
                required: true,
                errorMessage: '不能为空'
              }
            ]
          },
          studyTimeSlot: {
            rules: [
              {
                required: true,
                errorMessage: '不能为空'
              }
            ]
          },
          finishSchoolSituation: {
            rules: [
              {
                required: true,
                errorMessage: '不能为空'
              }
            ]
          },
          interactSituation: {
            rules: [
              {
                required: true,
                errorMessage: '不能为空'
              }
            ]
          },
          noteSituation: {
            rules: [
              {
                required: true,
                errorMessage: '不能为空'
              }
            ]
          },
          lovedSituation: {
            rules: [
              {
                required: true,
                errorMessage: '不能为空'
              }
            ]
          },
          purpose: {
            rules: [
              {
                required: true,
                errorMessage: '不能为空'
              }
            ]
          },
          referrerName: {
            rules: [
              {
                required: true,
                errorMessage: '不能为空'
              }
            ]
          },
          referrerPhone: {
            rules: [
              {
                required: true,
                errorMessage: '不能为空'
              },
              {
                pattern: '^1[3-9]\\d{9}$',
                errorMessage: '手机号格式不正确'
              }
            ]
          },
          zxIdentityType: {
            rules: [
              {
                required: true,
                errorMessage: '不能为空'
              }
            ]
          }
        },
        formItemList: [
          {
            name: 'name',
            title: '您的姓名',
            type: 'input',
            data: []
          },
          {
            name: 'phone',
            title: '您的手机号码',
            type: 'input',
            data: []
          },
          {
            name: 'age',
            title: '您的年龄',
            type: 'input',
            data: []
          },
          {
            name: 'beforeState',
            title: '您进陪跑营前的状态是怎样的？',
            type: 'input',
            data: []
          },
          {
            name: 'wantImproveProblem',
            title: '目前最想改善的问题是什么？',
            type: 'radio',
            data: [
              { value: '1', label: '夫妻关系' },
              { value: '2', label: '亲子关系' },
              { value: '3', label: '原生家庭关系' },
              { value: '4', label: '事业、赚钱' },
              { value: '5', label: '自我关系' }
            ]
          },
          {
            name: 'parentsRelationship',
            title: '您跟父母的关系怎么样？',
            type: 'radio',
            data: [
              { value: '1', label: '好' },
              { value: '2', label: '一般' },
              { value: '3', label: '很好' },
              { value: '4', label: '不好' }
            ]
          },
          {
            name: 'childrensRelationship',
            title: '您跟孩子的关系怎么样？',
            type: 'radio',
            data: [
              { value: '1', label: '很好' },
              { value: '2', label: '极好' },
              { value: '3', label: '一般' },
              { value: '4', label: '不好' }
            ]
          },
          {
            name: 'childrenNum',
            title: '家中几个孩子？',
            type: 'radio',
            data: [
              { value: '1', label: '1个' },
              { value: '2', label: '2个' },
              { value: '3', label: '3个及以上' },
              { value: '4', label: '无小孩' }
            ]
          },
          {
            name: 'childrenSituation',
            title: '孩子年龄？上几年级？',
            type: 'input',
            data: []
          },
          {
            name: 'studyTimeSituation',
            title: '您能保证每天投入碎片化时间学习1-2小时吗',
            type: 'radio',
            data: [
              { value: '1', label: '可以的' },
              { value: '2', label: '不能' }
            ]
          },
          {
            name: 'studyTimeSlot',
            title: '您每天可以学习的时间段是',
            type: 'radio',
            data: [
              { value: '1', label: '早上' },
              { value: '2', label: '上午' },
              { value: '3', label: '下午' },
              { value: '4', label: '晚上' }
            ]
          },
          {
            name: 'finishSchoolSituation',
            title: '您每天能准时学习课程、完成作业吗？',
            type: 'radio',
            data: [
              { value: '1', label: '可以' },
              { value: '2', label: '克服一下没问题' },
              { value: '3', label: '不能' }
            ]
          },
          {
            name: 'interactSituation',
            title: '您每天能保证参加群内互动吗？',
            type: 'radio',
            data: [
              { value: '1', label: '可以' },
              { value: '2', label: '不能' },
              { value: '3', label: '我尽量及时互动' }
            ]
          },
          {
            name: 'noteSituation',
            title: '您有做笔记的习惯吗？',
            type: 'radio',
            data: [
              { value: '1', label: '有' },
              { value: '2', label: '没有' }
            ]
          },
          {
            name: 'lovedSituation',
            title: '您跟另一半的关系怎么样？',
            type: 'radio',
            data: [
              { value: '1', label: '很好，比较和谐，能够很好交流沟通' },
              { value: '2', label: '还行，会有争吵，希望遇事听我的' },
              { value: '3', label: '不好，焦虑、争吵、冷战、教育观念不一致等' },
              { value: '4', label: '其他' }
            ]
          },
          {
            name: 'purpose',
            title: '您出营的目标是什么？',
            type: 'input',
            data: []
          },
          {
            name: 'referrerName',
            title: '您的推荐人姓名',
            type: 'input',
            data: []
          },
          {
            name: 'referrerPhone',
            title: '您的推荐人电话号码',
            type: 'input',
            data: []
          },
          {
            name: 'zxIdentityType',
            title: '您在鼎校甄选的身份是：',
            type: 'radio',
            data: [
              { value: '1', label: '超级合伙人' },
              { value: '2', label: '超级俱乐部' },
              { value: '3', label: '交付中心' },
              { value: '4', label: '家长会员' }
            ]
          }
        ],
        actId: '',
        actTitle: '',
        qrCodeImage: ''
      };
    },
    mounted() {},
    methods: {
      change(e) {
        this.show = e.show;
      },
      closeDialog() {
        this.$refs.popopTips.close();
      },
      handleOk() {
        this.closeDialog();
        if (this.qrCodeImage) {
          uni.redirectTo({
            url: `/Personalcenter/my/meetingServiceQRcode?qrCodeImage=${this.qrCodeImage}`
          });
        } else {
          uni.redirectTo({
            url: '/Coursedetails/my/parentEquity'
          });
        }
      },
      radioChange(e, item) {
        this.$set(this.formData, item.name, e.target.value);
        console.log('🚀 ~ radioChange ~ formData:', this.formData);
      },
      handleSubmit() {
        this.$refs.baseForm.validate().then(async () => {
          console.log(this.formData);
          const params = JSON.parse(JSON.stringify(this.formData));

          params.courseId = this.actId;
          params.learningCampName = this.actTitle;

          uni.showLoading({
            title: '提交中...'
          });

          $http({
            url: 'zxAdminCourse/course/nonstriker/student/info/save',
            method: 'post',
            data: params
          })
            .then((res) => {
              uni.hideLoading();
              if (res.code == 20000) {
                this.$refs.popopTips.open();
              }
            })
            .catch(() => {
              uni.hideLoading();
            });
        });
      }
    },
    onShow() {
      let token = uni.getStorageSync('token');
      if (!token) {
        uni.navigateTo({
          url: '/Personalcenter/login/login'
        });
      }

      this.formData.phone = uni.getStorageSync('phone');
    },

    onLoad(options) {
      this.actId = options.id;
      this.actTitle = options.title;
      this.qrCodeImage = options.qrCodeImage;
    }
  };
</script>

<style lang="scss" scoped>
  /* 弹窗样式 */
  .dialogBG {
    width: 100%;

    .reviewCard_box {
      width: 670rpx;
      position: relative;
    }

    .reviewCard_box image {
      width: 100%;
      height: 100%;
    }

    .reviewCard {
      position: relative;
      width: 100%;
      height: 100%;
      background: #ffffff;
      color: #000;
      border-radius: 24upx;
      padding: 50upx 55upx;
      box-sizing: border-box;
    }

    .cartoom_image {
      width: 420rpx;
      position: absolute;
      top: -250rpx;
      left: 145rpx;
      z-index: -1;
    }

    .review_close {
      position: absolute;
      top: 20rpx;
      right: 20rpx;
      z-index: 1;
    }

    .reviewTitle {
      width: 100%;
      text-align: center;
      font-size: 34upx;
      display: flex;
      justify-content: center;
    }

    .dialogContent {
      box-sizing: border-box;
      font-size: 32upx;
      line-height: 45upx;
      text-align: center;
      margin-top: 40rpx;
    }

    .review_btn {
      width: 240upx;
      height: 80upx;
      background: linear-gradient(180deg, #88cfba 0%, #1d755c 100%);
      border-radius: 45upx;
      font-size: 30upx;
      color: #ffffff;
      line-height: 80upx;
      margin: 60rpx auto 0 auto;
      justify-content: center;
      text-align: center;
    }
  }
  .scroll-H {
    overflow-x: hidden;
    box-sizing: border-box;
  }
  .bg {
    background: #fff;
    padding-bottom: 140rpx;
  }
  .title {
    padding: 40rpx;
    padding-bottom: 0;
  }
  .foot-content {
    position: fixed;
    display: flex;
    align-items: center;
    justify-content: center;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 140rpx;
    z-index: 1;
    justify-content: center;
    background: #fff;

    .signBtn {
      width: 100%;
      height: 90rpx;
      line-height: 90rpx;
      text-align: center;
      border-radius: 45rpx;
      background-image: linear-gradient(to bottom, #88cfba, #1d755c);
    }
  }

  .partnerFlex {
    margin: 44rpx 0;

    /deep/.u-textarea {
      background-color: #f3f8fc !important;
      padding-left: 32rpx;
    }

    /deep/.u-textarea__field {
      color: black !important;
    }

    /deep/.input-placeholder {
      font-size: 28rpx;
      margin-left: 12rpx;
      color: #808080 !important;
    }
  }

  .label {
    line-height: 64rpx;
  }

  .red {
    color: red;
  }

  .text {
    margin-top: 30rpx;
  }

  .radio-list-cell {
    display: flex;
    align-items: center;
    line-height: 64rpx;
    font-size: 28rpx;
    color: #333;

    view {
      margin-left: 12rpx;
    }
  }

  .uni-input {
    height: 64rpx;
    background: rgba(243, 248, 252, 0.7);
    padding-left: 24rpx;
    margin-right: 24rpx;
    border: 2rpx solid #f3f8fc;
  }

  .r_box {
    width: 100%;
  }
</style>
