<!-- 俱乐部列表 -->
<template>
  <page-meta :page-style="'overflow:' + (show ? 'hidden' : 'visible')"></page-meta>
  <view class="plr-30">
    <view class="bg-ff pt-30 radius-15 mb-20 search">
      <view class="pl-20" style="width: 94%">
        <uni-search-bar v-model="searchValue" @blur="blur" @clear="clear" placeholder="请输入俱乐部名称搜索" :cancelButton="none" :radius="30"></uni-search-bar>
      </view>
      <view class="mt-20">
        <view class="flex-dir-row flex-x-s flex-y-s tabs mt-30">
          <view class="flex-col col-2" @tap="tab(0)">
            <text :class="tabindex == 0 ? 'active' : 'unchecked'">全部</text>
            <view v-if="tabindex == 0" class="changing-over mt-15"></view>
          </view>
          <view class="flex-col col-3" @tap="tab(1)">
            <text :class="tabindex == 1 ? 'active' : 'unchecked'">下级俱乐部</text>
            <view v-if="tabindex == 1" class="changing-over mt-15"></view>
          </view>
          <view class="flex-col col-4 positionRelative" @tap="tab(2)">
            <text :class="tabindex == 2 ? 'active' : 'unchecked'">处理中</text>
            <view v-if="tabindex == 2" class="changing-over mt-15"></view>
            <view v-if="processingNum > 0" class="c-ff badge">{{ processingNum }}</view>
          </view>
          <view class="flex-col col-3" @tap="tab(3)">
            <text :class="tabindex == 3 ? 'active' : 'unchecked'">已取消</text>
            <view v-if="tabindex == 3" class="changing-over mt-15"></view>
          </view>
        </view>
      </view>
    </view>

    <view class="content">
      <view class="bg-ff p-30 radius-15 mb-30 positionRelative" v-for="(item, index) in listS.list" :key="index" @click.stop="goUrl(item)">
        <view class="flex-self-s">
          <view>
            <image class="img_head" :src="item.headPortrait ? item.headPortrait : avaUrl"></image>
          </view>

          <view class="ml-20 w100">
            <view class="f-30 flex-s">
              <view class="flex-a-c nickname">
                <view>
                  <view class="container" :class="item.status == 0 ? 'c-99' : ''">{{ item.remark ? item.remark : item.merchantName }}</view>
                </view>
                <view class="ml-20" @click.stop="changeFoucus(item)" v-if="tabindex == 1">
                  <image src="https://document.dxznjy.com/dxSelect/image/edit_icon.png" style="width: 30upx; height: 30upx; display: inline-block"></image>
                </view>
              </view>
              <uni-icons v-if="item.status != -1 && item.status != 2" type="right" size="18" color="#999"></uni-icons>
            </view>

            <view class="t-r flex_s mt-10 flex-x-b">
              <view>
                <text :class="item.status == 0 ? 'c-99' : ''">
                  累计收益：
                  <text :class="item.status == 0 ? 'c-99' : 'total-income'" class="bold">{{ item.totalAmount }}</text>
                </text>
              </view>
              <view
                v-if="(item.status == 2 || item.status == -1) && item.auditType == 1"
                class="plr-15 ptb-5"
                :class="item.status != 9 ? (item.status == 0 ? 'freeze' : 'grade') : 'golden'"
              >
                {{ item.gradeLevel != 4 ? (item.gradeLevel == 5 ? '申请为B2俱乐部' : '申请为B3俱乐部') : '申请为B1俱乐部' }}
              </view>
              <view
                v-if="(item.status == 2 || item.status == -1) && item.auditType == 2"
                class="plr-15 ptb-5"
                :class="item.status != 9 ? (item.status == 0 ? 'freeze' : 'grade') : 'golden'"
              >
                {{ item.gradeLevel != 4 ? (item.gradeLevel == 5 ? '升级为B2俱乐部' : '升级为B3俱乐部') : '升级为B1俱乐部' }}
              </view>

              <!-- <view v-if="item.auditStatus==3&&item.auditType==2"class="plr-15 ptb-5" :class="item.status!=9?(item.status==0?'freeze':'grade'):'golden'">{{item.gradeLevel!=4?(item.gradeLevel==5?'申请为B2俱乐部':'申请为B3俱乐部'):'申请为B1俱乐部'}}</view> -->
              <view v-if="item.status == 9" class="plr-15 ptb-5" :class="item.status != 9 ? (item.status == 0 ? 'freeze' : 'grade') : 'golden'">
                {{ item.gradeLevel != 4 ? (item.gradeLevel == 5 ? 'B2俱乐部' : 'B3俱乐部') : 'B1俱乐部' }}
              </view>
              <!-- <view v-if="item.gradeLevel==4" class="plr-15 ptb-5 grade" :class="item.auditType==0?'grade-s':'grade'">{{item.auditType==0?'已升级B1俱乐部':'B1俱乐部'}}</view>
							<view v-if="item.gradeLevel==5" class="plr-15 ptb-5 grade" :class="item.auditType==0?'grade-s':'grade'">{{item.auditType==0?'已升级B2俱乐部':'B2俱乐部'}}</view>
							<view v-if="item.gradeLevel==6" class="plr-15 ptb-5 grade" :class="item.auditType==0?'grade-s':'grade'">{{item.auditType==0?'已升级B3俱乐部':'B3俱乐部'}}</view> -->
            </view>

            <view class="mt-20 f-28 flex-s">
              <text :class="item.status == 0 ? 'c-99' : 'c-66'">{{ item.createdTime }}</text>
              <view v-if="item.status == -1" class="canceled">已取消</view>
            </view>

            <view v-if="item.auditStatus == 4" class="mt-20 f-26">
              <text :class="item.status == 0 ? 'c-99' : 'c-fea'">由于您超时未处理申请，请联系总部处理！</text>
            </view>
          </view>
        </view>

        <view v-if="item.auditStatus == 3 || item.auditStatus == 1 || item.auditStatus == 2" class="mt-30 border_t flex-s pt-20">
          <view class="f-28 c-66" v-if="item.auditStatus == 1">请在{{ item.expireTime }}内完成升级</view>
          <view class="f-28 c-66" v-if="item.auditStatus == 2">请在{{ item.expireTime }}内完成进货</view>
          <view class="f-28 c-66" v-if="item.auditStatus == 3">请在{{ item.expireTime }}内完成确认</view>
          <view class="f-30 btn" @click.stop="goUrl(item)">去处理</view>
        </view>
      </view>
    </view>

    <view v-if="listS.list != undefined && listS.list.length == 0" class="t-c flex-col" :style="{ height: useHeight + 'rpx' }">
      <image :src="imgHost + 'alading/correcting/no_data.png'" class="mb-20 img_icon" mode="widthFix"></image>
      <view style="color: #bdbdbd">暂无数据</view>
    </view>

    <view v-if="no_more && listS.list.length > 0">
      <u-divider text="到底了"></u-divider>
    </view>

    <!-- 超时弹窗 -->
    <uni-popup ref="tipPopup" type="center" :mask-click="false" @change="change">
      <view class="dialogBG">
        <view class="reviewCard_box positionRelative">
          <view class="cartoom_image">
            <image :src="dialog_iconUrl" mode="widthFix"></image>
          </view>
          <view class="reviewCard">
            <view class="mt-60 t-c">
              <view v-if="chooseClub.auditType != 0 && chooseClub.auditStatus == 4">
                由于您超时未处理俱乐部申请
                <br />
                请联系总部处理
              </view>
              <view v-if="chooseClub.auditType != 0 && chooseClub.auditStatus == 1">
                由于您等级不足且超时未处理该俱乐部
                <br />
                申请，该俱乐部已转至其他下级
              </view>
            </view>

            <view class="review_btn" @click="closeDialog">确定</view>
          </view>
        </view>
      </view>
    </uni-popup>

    <!-- 输入框弹窗 -->
    <uni-popup ref="inputPopup" type="center" :mask-click="false" @change="change">
      <view class="dialogBG">
        <view class="reviewCard_box positionRelative">
          <view class="cartoom_image">
            <image :src="dialog_iconUrl" mode="widthFix"></image>
          </view>
          <view class="reviewCard t-c">
            <view class="flex-a-c flex-x-e close-icon">
              <uni-icons type="clear" size="30" color="#B1B1B1" @click="closeDialog"></uni-icons>
            </view>
            <view class="f-38 bold">备注</view>
            <view class="mt-80 t-c input pl-30" :class="showClearIcon ? 'pr-20' : 'pr-30'">
              <input ref="inputRefs" :value="remark" @input="onInput" placeholder="请输入" :maxlength="importShow ? '6' : '11'" :class="showClearIcon ? 'mr-30' : ''" />
              <uni-icons v-if="showClearIcon" type="closeempty" size="20" color="#B1B1B1" @click="clearInput"></uni-icons>
            </view>

            <view class="flex-s mt-80">
              <view class="determine_btn" @click="determine">确定</view>
              <view class="close_btn" @click="closeInput">取消</view>
            </view>
          </view>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script>
  const { $navigationTo, $http } = require('@/util/methods.js');
  import util from '@/util/util.js';
  export default {
    name: 'webview',
    data() {
      return {
        show: false, // 禁止穿透滚动
        page: 1,
        no_more: false,
        listS: {}, // 下级俱乐部列表
        useHeight: 0, //除头部之外高度
        isAsc: 'asc', // 俱乐部列表顺序
        imgHost: getApp().globalData.imgsomeHost,
        avaUrl: util.getCachedPic('https://document.dxznjy.com/dxSelect/home_avaUrl.png', 'home_avaUrl_path'),
        chooseClub: {}, //当前点击的俱乐部
        flag: false,
        remark: '',
        showClearIcon: false,
        tabindex: 0, // tab下标
        searchValue: '', //搜索框
        information: {}, // 当前点击的客户列表
        processingNum: '', // 处理中数量
        importShow: false,
        dialog_iconUrl: util.getCachedPic('https://document.dxznjy.com/dxSelect/dialog_icon.png', 'dialog_icon_path')
      };
    },
    onShow() {
      this.supermanClublist();
      this.supermanClubNum();
    },
    onLoad() {},
    onReady() {
      uni.getSystemInfo({
        success: (res) => {
          // 可使用窗口高度，将px转换rpx
          let h = res.windowHeight * (750 / res.windowWidth);
          this.useHeight = h - 285;
        }
      });
    },
    onReachBottom() {
      if (this.page >= this.listS.totalPage) {
        this.no_more = true;
        return false;
      }
      this.supermanClublist(true, ++this.page);
    },
    methods: {
      goUrl(item) {
        if (item.auditStatus == 4) {
          this.chooseClub = item;
          this.$refs.tipPopup.open();
          return;
        }
        if (item.status == -1) return;
        this.page = 1;
        uni.navigateTo({
          url: '/supermanClub/applyDetails/index?merchantCode=' + item.merchantCode
        });
      },

      tab(e) {
        this.tabindex = e;
        this.page = 1;
        this.no_more = false;
        this.supermanClublist();
      },

      //俱乐部列表
      async supermanClublist(isPage, page) {
        let _this = this;
        const res = await $http({
          url: 'zx/merchant/merchantPage',
          data: {
            isAsc: _this.isAsc,
            page: page || 1,
            pageSize: 10,
            searchName: _this.searchValue,
            status: _this.tabindex == 0 ? '' : _this.tabindex
          }
        });
        console.log(res);
        if (res) {
          if (isPage) {
            let old = _this.listS.list;
            _this.listS.list = [...old, ...res.data.list];
          } else {
            _this.listS = res.data;
          }
        }
      },

      // 俱乐部处理中数量
      async supermanClubNum() {
        let _this = this;
        const res = await $http({
          url: 'zx/merchant/merchantDealNum',
          data: {
            isAsc: _this.isAsc,
            page: 1,
            pageSize: 10,
            searchName: _this.searchValue,
            status: _this.tabindex == 0 ? '' : _this.tabindex
          }
        });
        console.log(res);
        if (res) {
          _this.processingNum = res.data;
        }
      },

      skintap(url) {
        $navigationTo(url);
      },

      closeDialog() {
        this.$refs.tipPopup.close();
        this.$refs.inputPopup.close();
        this.importShow = false;
        this.remark = '';
      },
      // 搜索框
      blur(e) {
        console.log(e);
        this.searchValue = e.value;
        this.supermanClublist();
      },
      // 搜索框清空
      clear() {
        this.searchValue = '';
        this.supermanClublist();
      },

      // 点击修改备注
      changeFoucus(item) {
        if (item.remark) {
          this.remark = item.remark;
        } else {
          this.remark = item.merchantName;
        }
        this.showClearIcon = true;
        this.information = item;
        this.$refs.inputPopup.open();
      },

      closeInput() {
        this.$refs.inputPopup.close();
        this.importShow = false;
        this.remark = '';
      },

      determine() {
        this.modifyRemarks();
      },

      clearInput() {
        this.remark = '';
        this.importShow = true;
      },

      onInput(e) {
        console.log(e);
        this.remark = e.detail.value;
        if (e.detail.value.length > 0) {
          this.showClearIcon = true;
          if (e.detail.value.length <= 6) {
            this.importShow = true;
          }
        } else {
          this.showClearIcon = false;
        }
      },

      change(e) {
        this.show = e.show;
      },

      // 修改备注
      async modifyRemarks() {
        let _this = this;
        if (_this.remark.length > 6) {
          _this.$util.alter('备注不可超过六个字符');
          return;
        }
        const res = await $http({
          url: 'zx/merchant/setMerchantRelationRemark',
          method: 'post',
          data: {
            remark: _this.remark,
            merchantCode: _this.information.merchantCode
          }
        });
        if (res.status == 1) {
          this.$refs.inputPopup.close();
          this.remark = '';
          this.importShow = false;
          this.supermanClublist();
        } else {
          _this.$util.alter('操作失败啦，请稍后再试');
        }
      }
    }
  };
</script>

<style lang="scss" scoped>
  .search {
    position: fixed;
    top: 0;
    width: 92%;
    z-index: 1;
  }
  .content {
    margin-top: 260rpx;
  }

  .changing-over {
    background-color: #2e896f;
    width: 30rpx;
    height: 4rpx;
  }
  .tabs {
    // padding: 30rpx 0;
    width: 100%;
  }

  .tabs text {
    font-size: 30upx;
    color: #666;
  }

  .tabs .active {
    color: #000;
    font-size: 34rpx;
    font-weight: bold;
  }

  .tabs .unchecked {
    color: #666;
    font-size: 32rpx;
  }

  .badge {
    min-width: 30rpx;
    min-height: 30rpx;
    font-size: 22rpx;
    border-radius: 50%;
    line-height: 30rpx;
    text-align: center;
    position: absolute;
    top: 0;
    right: 30rpx;
    background-color: #fa370e;
  }

  .img_s {
    width: 100rpx;
    height: 100rpx;
    border-radius: 50%;
    opacity: 1;
  }

  .img_head {
    width: 100rpx;
    height: 100rpx;
    border-radius: 50%;
  }

  .flex_s {
    display: flex;
    align-items: center;
  }

  .grade {
    height: 38rpx;
    color: #fff;
    font-size: 24rpx;
    line-height: 38rpx;
    text-align: center;
    border-radius: 20rpx 0;
    background-color: #2e896f;
  }

  // .grade-s {
  // 	height: 38rpx;
  // 	color: #fff;
  // 	font-size: 24rpx;
  // 	line-height: 38rpx;
  // 	text-align: center;
  // 	border-radius: 20rpx 0;
  // 	background-color: #c6c6c6;
  // }

  .golden {
    color: #886a34;
    height: 38rpx;
    font-size: 24rpx;
    padding: 0 12rpx;
    line-height: 38rpx;
    text-align: center;
    border-radius: 20rpx 0;
    background: linear-gradient(to bottom, #f5ebd6, #dec288);
  }

  .freeze {
    height: 38rpx;
    color: #fff;
    font-size: 24rpx;
    line-height: 38rpx;
    text-align: center;
    border-radius: 20rpx 0;
    background-color: #b1b1b1;
  }

  .canceled {
    color: #fff;
    width: 90rpx;
    height: 36rpx;
    padding: 2px 4rpx;
    font-size: 26rpx;
    text-align: center;
    line-height: 36rpx;
    border-radius: 4rpx;
    background-color: #c6c6c6;
  }

  .border_t {
    border-top: 1px solid #efefef;
  }

  .btn {
    width: 120rpx;
    height: 50rpx;
    border-radius: 40rpx;
    line-height: 50rpx;
    text-align: center;
    background: linear-gradient(to bottom, #88cfba, #1d755c);
  }

  .img_icon {
    width: 160rpx;
    height: 160rpx;
  }

  .dialogBG {
    width: 100%;
    display: flex;
    justify-content: center;
    /* height: 100%; */
  }

  /* 21天结束复习弹窗样式 */
  .reviewCard_box {
    width: 670rpx;
    /* height: 560rpx; */
    position: relative;
  }

  .reviewCard_box image {
    width: 100%;
    height: 100%;
  }

  .reviewCard {
    position: relative;
    width: 100%;
    height: 100%;
    background: #ffffff;
    color: #000;
    border-radius: 24upx;
    padding: 50upx 55upx;
    box-sizing: border-box;
  }

  .close-icon {
    position: absolute;
    right: 20rpx;
    top: 20rpx;
  }

  .cartoom_image {
    width: 420rpx;
    position: absolute;
    top: -250rpx;
    left: 145rpx;
    z-index: -1;
  }

  .review_close {
    position: absolute;
    top: 20rpx;
    right: 20rpx;
    z-index: 1;
  }

  .reviewTitle {
    width: 100%;
    text-align: center;
    font-size: 34upx;
    display: flex;
    justify-content: center;
  }
  .dialogContent {
    box-sizing: border-box;
    font-size: 32upx;
    line-height: 45upx;
    text-align: center;
    margin-top: 40rpx;
  }

  .review_btn {
    width: 240upx;
    height: 80upx;
    background: linear-gradient(180deg, #88cfba 0%, #1d755c 100%);
    border-radius: 45upx;
    font-size: 30upx;
    color: #ffffff;
    line-height: 80upx;
    margin: 60rpx auto 0 auto;
    justify-content: center;
    text-align: center;
  }

  .addclass {
    width: 100%;
    height: 70rpx;
    line-height: 70rpx;
    font-size: 30rpx;
    color: #2e896f;
    border: 1px solid #2e896f;
    border-radius: 35rpx;
  }

  .not-selected {
    width: 100%;
    height: 70rpx;
    line-height: 70rpx;
    font-size: 30rpx;
    color: #000;
    border: 1px solid #c8c8c8;
    border-radius: 35rpx;
  }

  .scroll-Y {
    height: 440rpx;
  }

  .btn_orange {
    background: linear-gradient(to bottom, #88cfba, #1d755c);
    color: #fff !important;
    height: 60rpx;
    width: 150rpx;
    line-height: 60rpx;
    border-radius: 45rpx;
    border: none !important;
  }

  /deep/.uni-swiper__dots-box {
    bottom: 30rpx !important;
  }

  /deep/.uni-swiper__dots-item {
    width: 8rpx !important;
    height: 8rpx !important;
  }

  .loss-bgc {
    position: absolute;
    top: 0;
    left: 0;
    background-color: #fff;
    opacity: 0.5;
    width: 100%;
    height: 100%;
  }

  .nickname {
    width: 48%;
  }

  // 文本超出隐藏
  .container {
    width: 200rpx;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .total-income {
    color: #fa370e;
  }

  .determine_btn {
    width: 250upx;
    height: 80upx;
    color: #ffffff;
    font-size: 30upx;
    line-height: 80upx;
    text-align: center;
    border-radius: 45upx;
    background: linear-gradient(180deg, #88cfba 0%, #1d755c 100%);
  }

  .close_btn {
    width: 250upx;
    height: 80upx;
    color: #2e896f;
    font-size: 30upx;
    line-height: 80upx;
    text-align: center;
    border-radius: 45upx;
    box-sizing: border-box;
    border: 1px solid #2e896f;
  }

  .input {
    border: 1px solid #c8c8c8;
    border-radius: 50rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .reviewCard /deep/ .vue-ref {
    padding: 20rpx 0;
    width: 100%;
  }

  /deep/ .u-tabs__wrapper__nav__item {
    display: flex;
    align-items: flex-start !important;
  }

  /deep/ .u-tabs__wrapper__nav__line {
    margin-left: 15rpx !important;
  }
</style>
