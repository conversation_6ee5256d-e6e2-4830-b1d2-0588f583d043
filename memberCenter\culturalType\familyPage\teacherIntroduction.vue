<template>
  <view class="teacher_content_css">
    <image class="teacher_image_css" mode="widthFix" src="https://document.dxznjy.com/course/891b0d84d25c4def85f663f0b4ebbc49.jpg"></image>
    <view class="teacher-bottom-style bg-ff"></view>
    <view class="teacher_bottom_button">
      <view class="button-css">
        <button class="button-css-style pt-20 f-28 c-ff" @click="goAddQuestion">
          <span v-if="identityType == 4">
            会员价
            <span>￥5.90</span>
            <span class="old_price f-24 ml-12">原价￥9.90</span>
          </span>
          <span v-else>
            支付
            <span>￥9.90</span>
            <span>进行提问</span>
          </span>
        </button>
        <!-- 2024-11-6 紧急修改 购买超级会员修改成购买家长会员 隐藏 -->
        <view @click="openMembership" v-if="false" class="mt-20 teacher_bottom_text c-55 f-24">
          <!-- 				<view @click="openMembership" v-if="identityType!=4" class="mt-20 teacher_bottom_text c-55 f-24"> -->
          <image mode="widthFix" style="width: 450rpx; margin: 0 auto" src="https://document.dxznjy.com/course/5ae7e998165149be92a584675346b0b9.png"></image>
        </view>
      </view>
      <view class="button-css-bottom"></view>
    </view>
  </view>
</template>

<script>
  const { $http } = require('@/util/methods.js');
  export default {
    data() {
      return {
        identityType: ''
      };
    },
    onLoad(e) {
      this.identityType = e.identityType;
      if (this.identityType == 4) {
        this.getHasFreeQa();
      } else {
        uni.setStorageSync('hasFreeQaFalse', 0);
      }
    },
    methods: {
      async getHasFreeQa() {
        let _this = this;
        const res = await $http({
          url: 'zx/wap/qa/hasFreeQa',
          data: {}
        });
        if (res) {
          if (res.data) {
            uni.setStorageSync('hasFreeQaFalse', 1);
          } else {
            uni.setStorageSync('hasFreeQaFalse', 0);
          }
        }
      },
      openMembership() {
        uni.navigateTo({
          url: '/Personalcenter/my/nomyEquity?type=2'
        });
      },
      goAddQuestion() {
        uni.redirectTo({
          url: '/memberCenter/culturalType/familyPage/addQuestions?identityType=' + this.identityType
        });
      }
    }
  };
</script>

<style lang="less" scoped>
  .teacher_content_css {
    background: url('https://document.dxznjy.com/course/891b0d84d25c4def85f663f0b4ebbc49.jpg') no-repeat;
    background-size: 100%;
    position: relative;
    .teacher_image_css {
      width: 750rpx;
    }
    .teacher-bottom-style {
      position: absolute;
      bottom: 0;
      width: 100%;
      height: 200rpx;
    }
    .teacher_bottom_button {
      position: fixed;
      bottom: 0;
      width: 750rpx;
      height: 192rpx;
      .button-css {
        height: 165rpx;
        background: url(https://document.dxznjy.com/course/6ca8c87baabb438cbd6bebde92c7b7f9.png) no-repeat;
        background-size: 100%;
      }
      .button-css-bottom {
        background-color: rgba(53, 131, 109, 0.2);
        height: 27rpx;
      }
      .button-css-style {
        width: 686rpx;
        border-radius: 38rpx;
        height: 92rpx;
        line-height: 92rpx;
        margin: 10rpx auto;
        // background-color: #339378;
        .old_price {
          display: inline-block;
          text-decoration: line-through;
        }
      }
    }
    .teacher_bottom_text {
      text-align: center;
    }
  }
</style>
