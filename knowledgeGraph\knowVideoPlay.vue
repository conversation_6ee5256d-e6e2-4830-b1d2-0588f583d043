<template>
  <view class="bg-00 paly-video-css c-ff">
    <view class="videoTitle plr-30">
      <text>{{ formatVideoTitle(videoInfo.videoName) }}</text>
    </view>
    <!-- #ifdef  APP-PLUS  -->
    <video
      :key="videoKey"
      id="myVideo"
      :initial-time="startTime"
      :autoplay="false"
      controls
      @timeupdate="onTimeUpdate"
      @ended="bindEnded"
      @fullscreenchange="fullscreenchange"
      :src="videoSrc"
      style="width: 100%; height: 80%"
    ></video>
    <!-- :src="'https://v.polyv.net/uc/video/getMp4?vid=' + videoInfo.vid" -->
    <!-- #endif -->
    <!-- #ifdef   MP-WEIXIN -->
    <polyv-player
      :key="playerKey"
      id="polyv-player-id"
      :startTime="startTime"
      :autoplay="false"
      :playerId="playerIdcont"
      :vid="videoInfo.videoUrl"
      :width="width"
      :height="height"
      :ts="ts"
      :sign="sign"
      :showControls="false"
      @ended="bindEnded"
    ></polyv-player>
    <!-- #endif -->
  </view>
</template>

<script>
  const { $http } = require('@/util/methods.js');
  const MD5 = require('../util/md5.js');
  let secretkey = 'Jkk4ml1Of8';
  let ts = new Date().getTime();
  let sign = '';
  export default {
    data() {
      return {
        fullScreen: false,
        type: 0,
        videoInfo: {},
        studyTime: 0,
        playerIdcont: 'polyvPlayercont',
        startTime: 0,
        ts: ts,
        sign: sign,
        width: '100%',
        height: '72%',
        videoBottomHeight: '208rpx',
        buttonWidth: '61rpx',
        eventType: '',
        videoList: [],
        videoIndex: 0,
        timer: null,
        isPlaying: false,
        currentTime: 0,
        durationTime: 0,
        progress: 0,
        qualityOptions: ['高清', '标清', '低清'],
        qualityIndex: 1,
        showPlayer: true,
        playerKey: 0,
        videoKey: 0,
        videoSrc: ''
      };
    },
    onLoad(e) {
      // const x = {
      //   token:
      //     '************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************',
      //   type: 1,
      //   index: 0,
      //   videoInfo: [
      //     {
      //       id: '1376537724372795392',
      //       curriculumId: '1223293140236390400',
      //       courseVersionNodeId: '1365372489790013440',
      //       courseVersionNodeName: '',
      //       courseSubjectNodeId: '1365386256284794880',
      //       courseSubjectNodeName: '',
      //       coursePeriodNodeId: '1376286603981897728',
      //       coursePeriodNodeName: '',
      //       knowledgeNum: 5,
      //       type: 1,
      //       videoName: '视频下面章节1下面5个知识都绑定',
      //       vid: '1723c88563f9bbc127797bcdf63d0cc2_1',
      //       sortsNum: 0,
      //       createTime: '2025-05-26 12:29:28',
      //       isUnlock: 1,
      //       videoPicUrl: ''
      //     }
      //   ]
      // };
      // uni.setStorageSync(
      //   'token',
      //   '************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************'
      // );
      let info = JSON.parse(e.videoInfo);
      this.videoIndex = e.index;
      this.videoList = info;
      console.log('视频播放onLoad ~ info:', info);
      e.type ? (this.type = e.type) : '';

      this.videoInfo = this.videoList[this.videoIndex];
      // 动态设置底部高度
      const platform = uni.getSystemInfoSync().platform;
      if (platform === 'android' || platform === 'ios') {
        this.videoBottomHeight = '150rpx'; // App 端
        this.buttonWidth = '80rpx';
      } else {
        this.videoBottomHeight = '208rpx'; // 小程序端
        this.buttonWidth = '61rpx';
      }
      // #ifdef MP-WEIXIN
      this.getVideo();
      // #endif
      // #ifdef APP-PLUS
      this.fetchUrl();
      // #endif
    },
    onReady() {
      this.videoContext = uni.createVideoContext('myVideo');
      this.startProgressTimer(); // 启动进度监听
      uni.onWindowResize((res) => {
        const isLandscape = res.size.windowWidth > res.size.windowHeight;
        if (isLandscape) {
          if (!this.fullScreen) {
            this.videoContext.requestFullScreen();
          }
        } else {
          setTimeout(() => {
            if (this.fullScreen) {
              this.videoContext.exitFullScreen();
            }
          }, 1000);
        }
      });
    },

    onUnload() {
      clearInterval(this.timer);
    },
    methods: {
      fullscreenchange(e) {
        this.fullScreen = e.detail.fullScreen;
      },
      formatVideoTitle(title) {
        const maxLength = 22;
        if (!title) return '';
        if (title.length <= maxLength) return title;
        const start = title.slice(0, 11);
        const end = title.slice(-8);
        return `${start}...${end}`;
      },
      getVideo() {
        let _this = this;
        let polyvPlayerContext = _this.selectComponent('#polyv-player-id');
        const ts = new Date().getTime();
        const vid = this.videoInfo.videoUrl;
        const sign = MD5.md5(`${secretkey}${vid}${ts}`);
        polyvPlayerContext.changeVid({
          vid: _this.videoInfo.vid,
          ts,
          sign
        });
        setTimeout(() => {
          polyvPlayerContext.pause();
        }, 500);
      },
      // 切换播放暂停
      togglePlayPause() {
        // #ifdef MP-WEIXIN
        const player = this.selectComponent('#polyv-player-id');
        if (this.isPlaying) {
          player.pause();
        } else {
          player.seek(this.currentTime);
          player.play();
        }
        // #endif
        // #ifdef APP-PLUS
        const videoContext = uni.createVideoContext('myVideo', this);
        console.log('app播放');
        if (this.isPlaying) {
          videoContext.pause();
        } else {
          videoContext.play();
        }
        // #endif
        this.isPlaying = !this.isPlaying;
      },
      // 定时更新进度
      startProgressTimer() {
        this.timer = setInterval(() => {
          // #ifdef MP-WEIXIN
          const player = this.selectComponent('#polyv-player-id');
          if (player && player.rCurrentTime && player.rDuration) {
            this.currentTime = player.rCurrentTime.toFixed(0);
            this.durationTime = player.rDuration.toFixed(0);
            this.progress = ((player.rCurrentTime / player.rDuration) * 100).toFixed(0);
          }
          // #endif
        }, 1000);
      },

      onQualityChange(e) {
        this.qualityIndex = e.detail.value;
        const qualityText = this.qualityOptions[this.qualityIndex];
        // #ifdef MP-WEIXIN
        let polyvPlayerContext = this.selectComponent('#polyv-player-id');
        if (qualityText === '高清') {
          polyvPlayerContext.switchQuality(3);
        } else if (qualityText === '标清') {
          polyvPlayerContext.switchQuality(2);
        } else if (qualityText === '低清') {
          polyvPlayerContext.switchQuality(1);
        }
        // #endif
      },
      bindEnded() {
        this.isPlaying = false;
      },
      // 进度条变化
      onSliderChange(e) {
        const percent = e.detail.value;
        const targetTime = (percent / 100) * this.durationTime;
        this.currentTime = targetTime.toFixed(0);
        // #ifdef MP-WEIXIN
        const player = this.selectComponent('#polyv-player-id');
        player.seek(targetTime);
        // #endif
        // // #ifdef APP-PLUS
        // const videoContext = uni.createVideoContext('myVideo', this);
        // videoContext.seek(targetTime);
        // // #endif
      },
      formatTime(seconds) {
        const m = Math.floor(seconds / 60);
        const s = Math.floor(seconds % 60);
        return `${m < 10 ? '0' + m : m}:${s < 10 ? '0' + s : s}`;
      },
      // 切换上一个视频
      previousVideo() {
        if (this.videoIndex == 0) return;

        // #ifdef MP-WEIXIN
        const player = this.selectComponent('#polyv-player-id');
        player.pause(); // 先暂停当前播放
        this.videoIndex--;
        this.videoInfo = this.videoList[this.videoIndex];
        this.resetPlayerState();
        this.getVideo();
        // #endif

        // #ifdef APP-PLUS
        const videoContext = uni.createVideoContext('myVideo', this);
        videoContext.pause(); // 先暂停当前播放
        this.videoIndex--;
        this.videoInfo = this.videoList[this.videoIndex];
        this.resetPlayerState();
        // #endif
      },

      // 切换下一个视频
      nextVideo() {
        if (this.videoIndex == this.videoList.length - 1) return;

        // #ifdef MP-WEIXIN
        const player = this.selectComponent('#polyv-player-id');
        player.pause(); // 先暂停当前播放
        this.videoIndex++;
        this.videoInfo = this.videoList[this.videoIndex];
        this.resetPlayerState();
        this.getVideo();
        // #endif

        // #ifdef APP-PLUS
        const videoContext = uni.createVideoContext('myVideo', this);
        videoContext.pause(); // 先暂停当前播放
        this.videoIndex++;
        this.videoInfo = this.videoList[this.videoIndex];
        this.resetPlayerState();
        // #endif
      },

      // 重置播放器状态
      resetPlayerState() {
        // #ifdef MP-WEIXIN
        this.playerKey++; // 触发 polyv-player 重建
        // #endif
        // #ifdef APP-PLUS
        this.videoKey++;
        // #endif
        this.currentTime = 0;
        this.progress = 0;
        this.durationTime = 0;
        this.isPlaying = false;
        this.startTime = 0;
      },
      // 获取视频清晰度
      fetchUrl() {
        this.$httpUser
          .get('znyy/pd/pdCourse/getVideoInfo', {
            vid: this.videoInfo.vid
            // vid: '1723c88563678183205104876ee13ca8_1'
          })
          .then((res) => {
            console.log(res, 6666);
            const videoData = res.data.data.data.data[0];
            console.log('🚀 ~ .then ~ videoData:', videoData);
            // 获取 playUrl
            if (videoData.transcodeInfos && videoData.transcodeInfos.length > 0 && videoData.transcodeInfos.length <= 2) {
              console.log('视频数组:', videoData.transcodeInfos[0]);
              const playUrl = videoData.transcodeInfos[0].playUrl; // 取第一个清晰度的播放地址
              // 你可以将 playUrl 赋值给 data 中的变量，用于页面播放
              this.videoSrc = playUrl;
            } else if (videoData.transcodeInfos && videoData.transcodeInfos.length >= 2) {
              console.log('视频数组:', videoData.transcodeInfos[1]);
              const playUrl = videoData.transcodeInfos[1].playUrl; // 取第二个清晰度的播放地址
              this.videoSrc = playUrl;
            } else {
              console.error('没有找到有效的视频播放地址');
            }
          });
      },
      onTimeUpdate(e) {
        // 仅 App 使用
        // #ifdef APP-PLUS
        this.currentTime = e.detail.currentTime;
        this.durationTime = e.detail.duration;
        this.progress = ((e.detail.currentTime / e.detail.duration) * 100).toFixed(0);
        // #endif
      }
    }
  };
</script>

<style lang="scss" scoped>
  .paly-video-css {
    height: 100vh;

    .videoTitle {
      line-height: 91rpx;
      background: linear-gradient(to left, rgba(255, 255, 255, 0) 0%, rgba(67, 67, 67, 1) 100%);
    }

    .videoBottom {
      width: 100%;
      background: rgba(255, 255, 255, 0.3);
      position: fixed;
      bottom: 0;
      left: 0;

      .videoButton {
        display: flex;
        justify-content: center;
        align-items: center;
      }

      .progress {
        display: flex;
        align-items: center;

        .progressBar {
          flex: 1;
          display: flex;
          align-items: center;

          .siderProgress {
            width: 90%;
          }
        }

        .picker-content {
          width: 86rpx;
          font-size: 28rpx;
          color: #65ab75;
          text-align: center;
          line-height: 50rpx;
          background: rgba(223, 234, 223, 0.2);
          border-radius: 12rpx;
        }
      }
    }
  }
</style>
