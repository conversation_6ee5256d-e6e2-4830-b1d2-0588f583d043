<template>
  <!-- 待办提醒 -->
  <view class="todo-content" v-if="todoList && todoList.length > 0">
    <view class="todo-icon"></view>
    <view class="todo-title">待办提醒</view>
    <view class="todo-list">
      <view v-for="item in todoList" :key="item.id" class="flexbox todo-item">
        <view class="todo-left" @tap="handleGoClass(item)">
          <view class="tips">待填写</view>
          <view class="item-name overstepSingle">{{ item.content }}</view>
        </view>
        <view class="todo-right">
          <!-- isRead 0未读  1已读 -->
          <!-- state 0未处理  1已处理 -->
          <view v-if="item.isRead == 0" class="red-dot"></view>
          {{ item.createTime }}
        </view>
      </view>
    </view>
  </view>
</template>

<script>
  const { $http } = require('@/util/methods.js');
  export default {
    data() {
      return {
        showCurriculum: false,
        todoList: []
      };
    },
    methods: {
      async handleGoClass(item) {
        // 读消息
        try {
          if (item.isRead == 0) {
            await $http({
              url: 'zx/wap/message/todo/read',
              method: 'post',
              data: {
                id: item.id
              }
            });
          }
        } catch (error) {
          console.log('🚀 ~ handleGoClass ~ error:', error);
        } finally {
          //前往填写试课推荐或者上课信息对接表
          if (item.jumpUrl) {
            uni.navigateTo({
              url: item.jumpUrl
            });
          }
        }
      },
      // 我的课程列表
      async getTodoReminderList() {
        let _this = this;
        const res = await $http({
          url: 'zx/wap/message/todo/message/all',
          data: {
            phone: uni.getStorageSync('phone') ? uni.getStorageSync('phone') : ''
          }
        });
        if (res) {
          _this.todoList = res.data;
        }
      },
      resetTodoReminderList() {
        this.todoList = [];
      }
    }
  };
</script>

<style lang="scss" scoped>
  .todo-content {
    position: relative;
    width: 686rpx;
    margin: auto;
    padding: 32rpx 0 22rpx 0;
    background: url('https://document.dxznjy.com/course/2f28ea682c054a9a974110dfc14754e5.png') no-repeat;
    background-size: 100%;
    box-sizing: border-box;
    overflow: hidden;

    .todo-icon {
      position: absolute;
      top: 6rpx;
      right: 18rpx;
      width: 110rpx;
      height: 110rpx;
      background: url('https://document.dxznjy.com/course/edd1339a81cf4b04a415c13832a60338.png') no-repeat;
      background-size: 100% 100%;
    }
    .todo-title {
      padding-left: 24rpx;
      font-weight: 600;
      font-size: 30rpx;
      color: #333333;
      line-height: 40rpx;
      text-align: left;
    }
    .todo-list {
      max-height: 200rpx;
      padding: 0 24rpx;
      overflow-y: auto;
      .todo-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 30rpx 0 30rpx 0;
        border-bottom: 2rpx solid #efefef;

        &:last-child {
          border-bottom: none;
        }
      }
      .todo-left {
        display: flex;
        align-items: center;
        .tips {
          display: flex;
          justify-content: center;
          align-items: center;
          width: 92rpx;
          height: 36rpx;
          margin-right: 8rpx;
          color: #fd9b2a;
          background: rgba(255, 221, 167, 0.15);
          border-radius: 8rpx;
          border: 2rpx solid #ffdda7;
          font-weight: 400;
          font-size: 24rpx;
        }
        .item-name {
          width: 448rpx;
          font-weight: 400;
          font-size: 28rpx;
          color: #717171;
          line-height: 40rpx;
          text-align: left;
        }
      }

      .todo-right {
        margin-right: 18rpx;
        position: relative;
        font-weight: 400;
        font-size: 24rpx;
        color: #717171;
        line-height: 34rpx;

        .red-dot {
          position: absolute;
          top: -4rpx;
          right: -16rpx;
          width: 12rpx;
          height: 12rpx;
          background: #fb4545;
          border-radius: 50%;
        }
      }
    }
    .arrow_bottom {
      position: absolute;
      background: url('https://document.dxznjy.com/course/f25ad6eafb9446629cc414ba592db677.png') no-repeat;
      background-size: 100%;
      width: 686rpx;
      height: 104rpx;
      left: 12rpx;
      bottom: 0rpx;
      margin: 0 auto;
    }
  }
</style>
