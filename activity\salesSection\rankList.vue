<template>
  <page-meta :page-style="'overflow:' + (show ? 'hidden' : 'visible')"></page-meta>
  <view class="ranking_content_css">
    <view class="ranking_content_title"></view>
    <view class="rank-content">
      <view class="top" v-if="rankInfo.rank && rankInfo.rank <= 100">已上榜</view>

      <view class="top" v-else-if="!rankInfo.rank && rankInfo.remainHours">
        距离上榜还需要消耗
        <text>{{ rankInfo.remainHours }}</text>
        个课时
      </view>
      <view class="top" v-else-if="rankInfo.rank && rankInfo.rank > 100">未上榜</view>

      <view class="top-3">
        <view class="top-no no-2">
          <view class="badge"></view>
          <view class="avatar">
            <image :src="rankList[1].avatar || headerAvaUrl"></image>
          </view>

          <view class="info" v-if="rankList[1].studentCode">
            <view class="student-name overstepSingle">{{ rankList[1].studentName }}</view>
            <view class="finish-text">
              完成
              <text class="text-css">{{ rankList[1].hours }}</text>
              课时
            </view>
            <view class="time">{{ rankList[1].completeTime }}时</view>
          </view>
          <view v-else class="no-student">虚位以待</view>
        </view>
        <view class="top-no no-1">
          <view class="badge"></view>
          <view class="avatar">
            <image :src="rankList[0].avatar || headerAvaUrl"></image>
          </view>

          <view class="info" v-if="rankList[0].studentCode">
            <view class="student-name overstepSingle">{{ rankList[0].studentName }}</view>
            <view class="finish-text">
              完成
              <text class="text-css">{{ rankList[0].hours }}</text>
              课时
            </view>
            <view class="time">{{ rankList[0].completeTime }}时</view>
          </view>
          <view v-else class="no-student">虚位以待</view>
        </view>
        <view class="top-no no-3">
          <view class="badge"></view>
          <view class="avatar">
            <image :src="rankList[2].avatar || headerAvaUrl"></image>
          </view>

          <view class="info" v-if="rankList[2].studentCode">
            <view class="student-name overstepSingle">{{ rankList[2].studentName }}</view>
            <view class="finish-text">
              完成
              <text class="text-css">{{ rankList[2].hours }}</text>
              课时
            </view>
            <view class="time">{{ rankList[2].completeTime }}时</view>
          </view>
          <view v-else class="no-student">虚位以待</view>
        </view>
      </view>

      <scroll-view class="rank-list" scroll-y scroll-with-animation :scroll-into-view="scrollId" @scroll="scroll">
        <view class="rank-item" v-for="(item, index) in rankList" :key="index" :id="'rank-item-' + item.studentCode">
          <template v-if="index > 2">
            <view class="user-info">
              <view class="left">
                <view class="title f-28 lh-40">{{ index + 1 }}</view>
                <image class="avatar-css radius-all" :src="item.avatar || headerAvaUrl"></image>
                <view v-if="item.studentName" class="name-css overstepSingle">{{ item.studentName }}</view>
                <view v-else class="name-css overstepSingle">虚位以待</view>
              </view>
              <view class="title_icon" v-if="item.completeTime">{{ item.completeTime }}时 完成{{ item.hours }}课时</view>
              <view class="title_icon" v-else></view>
            </view>
          </template>
        </view>
      </scroll-view>
    </view>

    <view class="ranking_main_bottom" @click="handleScroll">
      <view class="user-info">
        <view class="left">
          <view class="title f-28 lh-40">{{ rankInfo.rank && rankInfo.rank <= 100 ? rankInfo.rank : '未上榜' }}</view>
          <image class="avatar-css radius-all" :src="rankInfo.avatar || currentUserAvatar"></image>
          <view class="name-css overstepSingle" :class="!rankInfo.studentName ? 'no-name-css' : ''">{{ rankInfo.studentName || '您还未注册学员' }}</view>
        </view>
        <view v-if="rankInfo.studentName == '暂未报名' && !rankInfo.studentCode" class="title_icon">还差8课时</view>
        <view v-else class="title_icon">{{ rankInfo.completeTime ? `${rankInfo.completeTime}时 完成${rankInfo.hours}课时` : `还差${rankInfo.remainHours || 0}课时 ` }}</view>
      </view>
    </view>
  </view>
</template>

<script>
  const { $navigationTo, $http } = require('@/util/methods.js');
  export default {
    data() {
      return {
        currentUserAvatar: '',
        headerAvaUrl: 'https://document.dxznjy.com/dxSelect/home_avaUrl.png',
        nickName: '',
        rankList: Array(100).fill({}),
        rankInfo: {},
        show: false,
        scrollTop: 0,
        old: { scrollTop: 0 },
        scrollId: null,
        timer: null
      };
    },
    onLoad() {
      this.getRankList();
      this.getCurrentUserRankInfo();
    },
    onUnload() {
      clearInterval(this.timer);
    },
    onShow() {
      clearInterval(this.timer);
      this.getPolling();
      this.nickName = uni.getStorageSync('nickName');
      this.currentUserAvatar = uni.getStorageSync('headPortrait') ? uni.getStorageSync('headPortrait') : 'https://document.dxznjy.com/dxSelect/home_avaUrl.png';
    },
    methods: {
      getPolling() {
        this.timer = setInterval(() => {
          this.getRankList();
          this.getCurrentUserRankInfo();
        }, 5 * 60 * 1000);
      },
      scroll(e) {
        this.old.scrollTop = e.detail.scrollTop;
      },
      handleScroll() {
        if (this.rankInfo.studentCode && this.rankInfo.rank > 3 && this.rankInfo.rank <= 100) {
          this.scrollId = `rank-item-${this.rankInfo.studentCode}`;
          setTimeout(() => {
            this.scrollId = null;
          }, 500);
        }
      },
      async getCurrentUserRankInfo() {
        let mobile = uni.getStorageSync('phone');
        const res = await $http({
          url: 'zx/wap/dyy/student/single/info',
          showLoading: true,
          data: {
            mobile: mobile
          }
        });
        if (res) {
          this.rankInfo = res.data || { studentName: '暂未报名' };
        }
      },
      async getRankList() {
        const res = await $http({
          url: 'zx/wap/dyy/student/rank',
          showLoading: true,
          data: {
            page: 1,
            pageSize: 100
          }
        });
        if (res) {
          const data = res.data.data || [];
          this.rankList = data.concat(Array(100 - data.length).fill({})); // 填充空对象，确保数组长度为100
        }
      },

      change(e) {
        this.show = e.show;
      }
    }
  };
</script>

<style lang="scss" scoped>
  .ranking_content_css {
    width: 100%;
    height: 100vh;
    background: url('https://document.dxznjy.com/course/25ff77cbe9df42628f07f7f9e1571e4c.png') no-repeat;
    background-size: 100% 100%;

    .rank-content {
      position: absolute;
      left: 50%;
      margin-left: -343rpx;
      top: 346rpx;
      width: 686rpx;
      height: calc(100vh - 480rpx);
      padding: 80rpx 30rpx 10rpx;
      box-sizing: border-box;
      background: url('https://document.dxznjy.com/course/47ba9103277246a6996fb31ccfee059f.png') no-repeat;
      background-size: 100% 100%;

      .top {
        position: absolute;
        top: -28rpx;
        left: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 464rpx;
        height: 58rpx;
        margin-left: -232rpx;
        background: url('https://document.dxznjy.com/course/c9bcb3118abc4ef9930fac661d0454fa.png') no-repeat;
        background-size: 100% 100%;
        font-size: 28rpx;
        color: #ffffff;

        text {
          color: #ffe49a;
          font-size: 36rpx;
          margin: 0 8rpx;
        }
      }

      .top-3 {
        display: flex;
        justify-content: space-between;
      }
      .top-no {
        position: relative;
        width: 198rpx;
        height: 286rpx;

        .avatar {
          position: absolute;
          top: 6rpx;
          left: 50%;
          margin-left: -55rpx;
          width: 110rpx;
          height: 110rpx;
          image {
            width: 100%;
            height: 100%;
            border-radius: 50%;
          }
        }

        .badge {
          position: absolute;
          top: -28rpx;
          left: 44rpx;
          width: 50rpx;
          height: 44rpx;
        }

        .info {
          padding-top: 132rpx;
          width: 100%;
          text-align: center;
          .student-name {
            line-height: 40rpx;
            font-size: 28rpx;
            color: #333333;
          }

          .finish-text {
            margin-top: 10rpx;
            line-height: 40rpx;
            font-size: 28rpx;
            color: #555555;
          }

          .text-css {
            margin: 0 8rpx;
            font-size: 32rpx;
            color: #34c08b;
          }

          .time {
            margin-top: 6rpx;
            font-size: 28rpx;
            color: #b9bcbb;
            line-height: 40rpx;
          }
        }
        .no-student {
          padding-top: 132rpx;
          font-size: 28rpx;
          color: #b9bcbb;
          text-align: center;
          line-height: 40rpx;
        }
      }

      .no-1 {
        background: url('https://document.dxznjy.com/course/4d1fd001faac400cb56fd1557142bb5a.png') no-repeat;
        background-size: 100% 100%;
        .badge {
          background: url('https://document.dxznjy.com/course/4082adc7f30a42a5a57c7416955760ca.png') no-repeat;
          background-size: 100% 100%;
        }
      }
      .no-2 {
        margin-top: 32rpx;
        background: url('https://document.dxznjy.com/course/74bd880627fd4c20bccda8718c2cda42.png') no-repeat;
        background-size: 100% 100%;
        .badge {
          background: url('https://document.dxznjy.com/course/053328bc7d354e0c8dcd1ed968b388f7.png') no-repeat;
          background-size: 100% 100%;
        }
      }
      .no-3 {
        margin-top: 18rpx;
        height: 300rpx;
        background: url('https://document.dxznjy.com/course/cdc1959dd06e42a990c7a20cbc01ac31.png') no-repeat;
        background-size: 100% 100%;
        .badge {
          background: url('https://document.dxznjy.com/course/e7591f95e4c04af28f087132d1cf8e58.png') no-repeat;
          background-size: 100% 100%;
        }
      }

      .rank-list {
        width: 100%;
        height: calc(100% - 340rpx);
        overflow-y: scroll;
        margin-top: 26rpx;

        .rank-item {
          .user-info {
            .title {
              width: 40rpx;
            }
          }
        }
      }
    }

    .user-info {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 32rpx 0;

      .left {
        display: flex;
        align-items: center;
        justify-content: flex-start;
      }
      .title {
        text-align: center;
        font-size: 32rpx;
        color: #006f62;
      }
      .title_icon {
        font-size: 28rpx;
        color: #555555;
      }
    }
    .avatar-css {
      width: 68rpx;
      height: 68rpx;
      margin-left: 16rpx;
    }
    .name-css {
      margin-left: 16rpx;
      font-size: 28rpx;
      color: #555555;
      width: 200rpx;
    }
    .ranking_main_bottom {
      position: fixed;
      width: 100%;
      height: 140rpx;
      bottom: 0;
      left: 0;
      background: url('https://document.dxznjy.com/course/3711344a5d5645a9ba937de744991208.png') no-repeat;
      background-size: 100% 100%;

      .user-info {
        padding: 36rpx 56rpx 36rpx 50rpx;

        .title_icon {
          color: #006f62;
        }
      }

      .name-css {
        width: 160rpx;
      }

      .no-name-css {
        width: 220rpx;
      }
    }
  }
</style>
