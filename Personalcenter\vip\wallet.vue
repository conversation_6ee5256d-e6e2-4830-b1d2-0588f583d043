<template>
  <view class="p-30 sizing">
    <view class="f-c">
      <u-tabs
        :list="categoryList"
        lineWidth="0"
        lineHeight="13"
        :activeStyle="{ color: '#333333', fontWeight: 'bold', fontSize: '28rpx' }"
        :inactiveStyle="{
          color: '#5A5A5A ',
          transform: 'scale(1)',
          fontSize: '28rpx'
        }"
        itemStyle="padding-left:20rpx; height: 80rpx;"
        :lineColor="`url(${lineBg}) 100% 110%`"
        @click="tabsClick"
      ></u-tabs>
    </view>
    <!-- 	<view class="col-12 relative" style="position: fixed;top: 0;">
				<view>
				<image :src="imgHost+'dxSelect/fourthEdition/wallet-bgc.png'" class="header-img"></image>
			</view>
			<view class="icon_img"  @click="skintap('pages/home/<USER>/index')">
				<uni-icons type="left" size="20" color="#000"></uni-icons>
			</view>
			<view class="page_title bold flex-col c-00">
				<text class="">提现</text>
			</view>

			<view class="plr-60 c-ff" style="position: fixed;top: 250rpx;width: 84%;">
				<view class="mb-30">可提现</view>
				<view class="flex-s">
					<view class="bold f-46">{{userAccount != null?userAccount.availableCashAmount:0}}</view>
					<view class="cash-out" @click="goWithdrawal">申请提现</view>
				</view>
			</view>
		</view>
 -->
    <!-- 	<view class="plr-30 margin-t520 positionRelativ">
			<view class="total-revenue shake" v-if="totalRevenue">总收益=待返佣+已返佣</view>
			<view class="returned shake" v-if="returnedCommission">已返佣=可提现+已提现</view>
			<view class="mt-40">
				<view class="f-28 flex-s">
					<view class="positionRelative bg-ff radius-15 p-30 w100 mr-15">
						<view class="flex-a-c">
							<text class="mr-10 c-66 f-28">总收益</text>
							<view class="total-icon">
								<uni-icons type="help-filled" size="16" color="#FFBE00"
									@click="hintClick(1)"></uni-icons>
							</view>
						</view>
						<view class="mt-12 c-00">￥<text class="bold f-30">{{userAccount.totalMoney}}</text></view>
					</view>
					<view class="positionRelative bg-ff radius-15 w100 p-30 ml-15">
						<view class="c-66 f-28">待返佣</view>
						<view class="mt-12 c-00">￥<text class="bold f-30">{{userAccount.paymentIn}}</text></view>
					</view>
				</view>

				<view class="f-28 flex-s mt-20">
					<view class="positionRelativ bg-ff radius-15 w100 p-30 mr-15">
						<view class="flex-a-c">
							<text class="mr-10 c-66 f-28">已返佣</text>
							<view class="total-icon">
								<uni-icons type="help-filled" size="16" color="#FFBE00"
									@click="hintClick(2)"></uni-icons>
							</view>
						</view>
						<view class="positionRelative mt-12 c-00">￥<text
								class="bold f-30">{{userAccount.paymentOut}}</text></view>
					</view>

					<view class="bg-ff radius-15 w100 p-30 ml-15">
						<view class="c-66 f-28">已提现</view>
						<view class="mt-12 c-00">￥<text class="bold f-30">{{Withdrawable}}</text></view>
					</view>
				</view>
			</view>
		</view> -->

    <view class="flex-self-c t-c mt-24 payMoney">
      <view class="" style="width: 50%">
        <text style="font-size: 40rpx; color: #339378" class="bold">{{ payAmount }}</text>
        <view class="f-24 .c-55 mt-12">
          <text>可提现金额(元)</text>
          <text style="color: #339378" class="bold ml-25" @tap="goWithdrawal">提现></text>
        </view>
      </view>
      <view class="" style="width: 50%; border-left: 2rpx solid lightgray">
        <text style="font-size: 40rpx; color: #339378" class="bold">{{ payingAmount }}</text>
        <view class="f-24 c-55 mt-12">
          <text>提现中的金额(元)</text>
        </view>
      </view>
    </view>
    <!-- 	<view class="flex-s plr-30 mt-20">
			<view class="f-32 c-00 bold">提现记录</view>
			<view class="flex-s">
				<view class="flex-a-c">
					<picker mode="date" @change="startChange">
						<view class=" flex_s ptb-20">
							<view class="f-30 c-66 ml-30">{{startDate || '开始时间'}} 至 </view>
						</view>
					</picker>
					<picker mode="date" @change="endChange">
						<view class="ptb-20">
							<view class="f-30 c-66 ml-20">{{endDate || '结束时间'}}</view>
						</view>
					</picker>
				</view>
				<view>
					<uni-icons class="ml-10" type="right" color="#C7C7C7" size="16"></uni-icons>
				</view>
			</view>
		</view> -->
    <view class="flex mt-65" style="color: #5a5a5a">
      <view :class="statusindex === '0' ? 'statusClass' : ''" @click="allStatus">全部</view>
      <view :class="statusindex === '1' ? 'statusClass' : ''" @click="Statusing">平台打款中</view>
      <view :class="statusindex === '2' ? 'statusClass' : ''" @click="StatusEnd">打款完成</view>
    </view>
    <view v-if="listS.data && listS.data.length > 0">
      <view class="radius-16 mb-20 mt-35" v-for="(item, index) in listS.data" :key="index">
        <view class="bg-ff p-10 sizing">
          <view class="flex-s lh-90" style="line-height: 90rpx; border-bottom: 2rpx solid #dcdcdc">
            <view class="type">
              <view style="margin-left: 20rpx">
                {{
                  item.status === 3
                    ? '提现完成'
                    : item.status === 4
                    ? '提现失败'
                    : item.status === 2
                    ? '提现中'
                    : item.status === 5
                    ? '已退款'
                    : item.status === 10
                    ? '已取消'
                    : item.status === 9
                    ? '提现成功但有退款'
                    : '等待确认'
                }}
              </view>
            </view>
          </view>
          <view class="mtb-25">提现金额(元): {{ item.withdrawAmount }}</view>
          <view class="">提现时间: {{ item.withdrawTime }}</view>
          <view class="mtb-25">收款人姓名: {{ item.userName }}</view>
          <view class="mtb-25">收款人开户行银行账号: {{ item.bankCard }}</view>
        </view>
      </view>
    </view>
    <view v-if="listS.data != undefined && listS.data.length == 0" class="t-c flex-col bg-ff radius-15 mt-35" :style="{ height: useHeight - 30 + 'rpx' }">
      <image :src="imgHost + 'alading/correcting/no_data.png'" class="mb-20 img_s"></image>
      <view style="color: #bdbdbd">暂无数据</view>
    </view>
    <view v-if="no_more && listS.data.length > 0">
      <u-divider text="到底了"></u-divider>
    </view>
    <uni-popup ref="coinPopup" type="center">
      <view class="popup-content">
        <view class="">提现须知</view>

        <view class="webview-container">
          请阅读
          <span @click="goToAgreement" style="color: blue">{{ signTittle }}</span>
          后，点击'同意'按钮
        </view>
        <u-button type="primary" @click="seeKnow" :customStyle="{ width: '300rpx', height: '70rpx' }" shape="circle" text="同意"></u-button>
      </view>
    </uni-popup>
  </view>
</template>

<script>
  const { httpUser } = require('@/util/luch-request/indexUser.js');
  const { $navigationTo, $http } = require('@/util/methods.js');
  export default {
    data() {
      return {
        listS: {},
        page: 1,
        no_more: false,
        type: 1, // 1学习超人 2俱乐部
        imgHost: getApp().globalData.imgsomeHost,
        userAccount: {}, //俱乐部账号金额信息
        totalRevenue: false, // 总收益
        returnedCommission: false, // 已返佣
        totalList: '', // 数据总数
        userinfo: {},
        Withdrawable: '', // 已提现
        startDate: '', // 开始时间
        endDate: '', // 结束时间
        useHeight: 0,
        pointsListEnd: [],
        statusindex: '0',
        userCode: '',
        payAmount: '',
        payingAmount: '',
        categoryList: [
          {
            name: '我的收益',
            key: 1
          },
          {
            name: '历史收益',
            key: 2
          }
        ],
        lineBg: 'https://document.dxznjy.com/course/01c96465d9ea46f69aa97465b1201aef.png',
        c: '',
        signTittle: '',
        agreementUrl: '',
        showWebView: false,
        tableKey: 1,
        app: 0
      };
    },
    onReady() {
      uni.getSystemInfo({
        success: (res) => {
          // 可使用窗口高度，将px转换rpx
          let h = res.windowHeight * (750 / res.windowWidth);
          this.useHeight = h - 950;
        }
      });
    },
    onUnload() {
      // #ifdef APP-PLUS
      if (this.app) {
        plus.runtime.quit();
      }
      // #endif
    },
    onLoad(e) {
      console.log(e);
      if (e.token) {
        this.app = e.app;
        this.$handleTokenFormNative(e);
      }
      this.type = Number(e.type);
      this.userinfo = JSON.parse(e.userinfo);
      this.userCode = e.userCode;
      /* 		this.payAmount=e.payAmount
  	this.payingAmount=e.payingAmount */
    },
    async onShow() {
      await this.fetchMoney();
      this.statusindex = '0';
      this.list();
    },
    onReachBottom() {
      if (this.page >= this.listS.totalPage) {
        this.no_more = true;
        return false;
      }
      this.list(true, ++this.page);
    },
    methods: {
      async getRealName() {
        // 判断用户是否实名认证
        let res = await httpUser.get('mps/user/info/user/code', {
          userCode: this.userCode
        });
        if (res.data.success) {
          // this.signContract = true;
          if (res.data.data.signContractStatus != 1) {
            this.goAuthen();
          } else {
            console.log(this.userCode, 'this.userCode');
            //已实名 去签约
            let res = await $http({
              url: 'flexpay/user/sign-status',
              data: {
                userType: 'Merchant',
                userCode: this.userCode
              }
            });
            if (res.data.signStatus == 1) {
              this.goWithdrawalPages();
            } else {
              //首次展示弹窗
              this.$refs.coinPopup.open();
              this.signTittle = res.data.agreementTitle;
              this.agreementUrl = res.data.agreementUrl;
            }
          }
        }
      },
      goAuthen() {
        uni.navigateTo({
          url: '/splitContent/authen/authen?userCode=' + this.userCode
        });
      },

      async tabsClick(item) {
        this.tableKey = item.key;
        await this.list();
        await this.fetchMoney();
      },
      //点击全部
      allStatus() {
        this.statusindex = '0';
        this.list();
      },
      Statusing() {
        this.statusindex = '1';
        this.list(false, 1, 2);
      },
      StatusEnd() {
        this.statusindex = '2';
        this.list(false, 1, 3);
      },
      async list(isPage, page, status) {
        uni.showLoading({
          title: '加载中'
        });
        let _this = this;
        let parms = {
          userCode: this.userCode,
          pageNum: page || 1,
          pageSize: 10
        };
        if (status) {
          parms.status = status;
        }
        let listUrl = this.tableKey == 1 ? 'zx/user/withdrawFlow' : 'zx/user/withdrawList';
        const res = await $http({
          url: listUrl,
          data: parms
        });
        if (res) {
          _this.totalList = res.data.totalItems;
          if (isPage) {
            let old = _this.listS.data;
            _this.listS.data = [...old, ...res.data.data];
          } else {
            _this.listS = res.data;
          }
        }
        uni.hideLoading();
      },
      //获取余额
      async fetchMoney() {
        uni.showLoading({
          title: '加载中...'
        });

        let incomeUrl = this.tableKey == 1 ? 'zx/user/fps-income' : 'zx/user/income';
        let res = await $http({
          url: incomeUrl,
          data: {
            fieldType: '0',
            userCode: this.userCode
          }
        });
        if (res) {
          this.payAmount = res.data.payAmount;
          this.payingAmount = res.data.payingAmount;

          uni.hideLoading();
        }
      },

      async seeKnow() {
        let res = await this.$httpUser.put(`flexpay/user/sign-time?userCode=${this.userCode}`);
        if (res.success) {
          this.goWithdrawalPages();
        }
      },
      async goWithdrawal() {
        if (this.tableKey == 1) {
          this.getRealName();
        } else {
          this.goWithdrawalPages();
        }
      },
      onPopupChange(e) {
        if (e.show) {
          // 弹窗打开时，显示 web-view
          this.showWebView = true;
        } else {
          // 弹窗关闭时，隐藏 web-view（可选）
          this.showWebView = false;
        }
      },
      goWithdrawalPages() {
        $navigationTo(
          `Personalcenter/vip/Withdrawal?type=${this.type}&userinfo=${JSON.stringify(this.userinfo)}&userCode=${this.userCode}&payAmount=${this.payAmount}&tableKey=${
            this.tableKey
          }`
        );
      },
      goToAgreement() {
        $navigationTo('antiAmnesia/agreement/agreement');
      }
    }
  };
</script>
<style>
  .shake {
    animation-name: slidein;
    animation-duration: 0.5s;
  }

  @keyframes slidein {
    from {
      opacity: 0;
    }

    to {
      opacity: 1;
    }
  }
</style>

<style lang="scss" scoped>
  .f-c {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 20rpx;
  }

  .payMoney {
    width: 684rpx;
    height: 152rpx;
    margin: auto;
    background-color: #f9fcfe;
    border-radius: 8rpx;
    padding: 24rpx;
    box-sizing: border-box;
  }

  .statusClass {
    font-weight: 700;
    border-bottom: 4rpx solid #339378;
  }

  .header-img {
    width: 100%;
    height: 600rpx;
  }

  .icon_img {
    position: absolute;
    top: 80rpx;
    left: 30rpx;
    z-index: 999 !important;
  }

  .page_title {
    position: absolute;
    top: 80upx;
    width: 100%;
    text-align: center;
  }

  .cash-out {
    width: 170rpx;
    height: 60rpx;
    color: #2f8c70;
    font-size: 30rpx;
    text-align: center;
    line-height: 60rpx;
    border-radius: 35rpx;
    background-color: #fff;
  }

  .margin-t520 {
    margin-top: 520rpx;
  }

  .webview-container {
  }

  .total-revenue {
    position: absolute;
    top: 590rpx;
    left: 160rpx;
    color: #333;
    font-size: 28rpx;
    border-radius: 10rpx;
    padding: 8rpx 10rpx;
    background-color: #fff;
    z-index: 9;
    box-shadow: 0rpx 3rpx 10rpx #c7c7c7;
  }

  .returned {
    position: absolute;
    top: 770rpx;
    left: 160rpx;
    color: #333;
    font-size: 28rpx;
    border-radius: 12rpx;
    padding: 8rpx 10rpx;
    background-color: #fff;
    z-index: 9;
    box-shadow: 0rpx 3rpx 10rpx #c7c7c7;
  }

  .treat_pay {
    background-color: #35a8f7;
    height: 36rpx;
    padding: 2rpx 6rpx;
    border-radius: 4rpx;
    line-height: 36rpx;
  }

  .paid {
    background-color: #2dc032;
    height: 36rpx;
    padding: 2rpx 6rpx;
    border-radius: 4rpx;
    line-height: 36rpx;
  }

  .unaudited {
    background-color: #e57126;
    height: 36rpx;
    padding: 2rpx 6rpx;
    border-radius: 4rpx;
    line-height: 36rpx;
  }

  .popup-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-direction: column;
    width: 500rpx;
    height: 300rpx;
    padding: 20rpx;
    border-radius: 20rpx;
    background-color: #f9fcfe;
  }

  .img_s {
    width: 160rpx;
    height: 160rpx;
  }

  /deep/.u-tabs__wrapper__nav__line {
    margin-left: 20rpx !important;
  }
</style>
