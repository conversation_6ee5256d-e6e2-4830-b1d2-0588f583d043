<template>
	<view>
		<scroll-view v-if="classList.data != undefined && classList.data.length != 0" scroll-y="true"
			:scroll-top="scrollTop" :style="{ height: useHeight + 'rpx' }" class="ptb-25"
			@scrolltolower="scrolltolower">
			<view class="detailed" v-for="item in classList.data">
				<view style="font-size: 30rpx; line-height: 42rpx; color: black">学员：{{ item.studentName }}</view>
				<view class="code-view">
					<text>编号: {{ item.studentCode }}</text>
					<text>充值交付学时：{{ item.rechargeHour }}</text>
				</view>
				<view class="line-view"></view>
				<view v-if="item.dispatchStatus == 3" class="border-bottom mb-20">
					<view class="courseInfo">
						<view class="mb-5 mt-5 lh-60">
							<text class="gray_tit">交付中心</text>
							{{ item.deliverName == '' ? '数据暂无' : item.deliverName }}
						</view>
						<view class="mb-5 lh-60">
							<text class="gray_tit">交付小组</text>
							{{ item.teamName == '' ? '数据暂无' : item.teamName }}
						</view>
						<view class="mb-10 lh-60">
							<text class="gray_tit">组长联系方式</text>
							{{ item.teamLeaderMobile == '' ? '数据暂无' : item.teamLeaderMobile }}
						</view>
					</view>
				</view>
				<view class="option-text">
					<view class="btn-view btn-color-1" @click="showOption(item, false)">
						<text v-if="item.isSubmit">查看</text>
						<text v-else>去填写</text>
					</view>
					<view v-if="item.isSubmit && !item.isOneToMany" class="btn-view btn-color-2 ml-30"
						@click="showOption(item, true)">编辑</view>
				</view>
				<view class="green-view" v-if="item.isSubmit">已填写</view>
				<view v-else class="grey-view">未填写</view>
			</view>

			<view v-if="no_more && classList.data != undefined && classList.data.length > 0">
				<u-divider text="到底了"></u-divider>
			</view>
		</scroll-view>

		<view v-if="classList.data == undefined || (classList.data != undefined && classList.data.length == 0)"
			class="t-c flex-col" :style="{ height: useHeight + 'rpx' }">
			<image :src="imgHost + 'alading/correcting/no_data.png'" class="mb-20 img_s" mode="widthFix"></image>
			<view style="color: #bdbdbd">暂无数据</view>
		</view>
	</view>
</template>

<script>
	const {
		$getSceneData,
		$showError,
		$showMsg,
		$http
	} = require('@/util/methods.js');
	import Util from '@/util/util.js';
	const {
		httpUser
	} = require('@/util/luch-request/indexUser.js');
	export default {
		data() {
			return {
				imgHost: getApp().globalData.imgsomeHost,
				svHeight: 50,
				useHeight: 0, //除头部之外高度

				classList: {},

				scrollTop: 0,
				no_more: false,
				page: 1,
				pageSize: 20,
				app: 0
			};
		},

		onLoad(e) {
			console.log(e);
			if (e.token) {
				this.app = e.app;
				this.$handleTokenFormNative(e);
			}
		},
		onReady() {
			let that = this;
			uni.getSystemInfo({
				//调用uni-app接口获取屏幕高度
				success(res) {
					// 可使用窗口高度，将px转换rpx
					let h = res.windowHeight * (750 / res.windowWidth);
					that.useHeight = h - 65;
				}
			});
		},

		onShow() {
			this.page = 1;
			this.clickHandle();
			this.getContactList(false);
		},
		onUnload() {
			// #ifdef APP-PLUS
			if (this.app) {
				plus.runtime.quit();
			}
			// #endif
		},
		methods: {
			async getContactList(isPage) {
				let _this = this;
				uni.showLoading();
				let res = await this.$httpUser.get(
					`deliver/web/student/contact/info/selStudentContactInfoList?pageNum=${_this.page}&pageSize=${_this.pageSize}`
					);
				uni.hideLoading();
				if (res && res.data) {
					if (isPage) {
						let old = _this.classList.data;
						_this.classList.data = [...old, ...res.data.data.data];
					} else {
						_this.classList = res.data.data;
					}
				}
			},
			// 滚动条回到顶部
			clickHandle() {
				this.scrollTop = this.scrollTop === 0 ? 1 : 0;
			},
			scrolltolower() {
				if (this.page >= this.classList.totalPage) {
					this.no_more = true;
					return false;
				}
				++this.page;
				this.getContactList(true);
			},

			//跳轉
			async showOption(item, isEdit) {
				console.log(isEdit);
				// 如果是一对多
				if (item.isOneToMany) {
					let res = await $http({
						// url: 'zx/exp/getInfo',
						url: 'deliver/web/student/contact/info/getStudentContactInfoDetail',
						method: 'get',
						data: {
							id: item.id
							// orderId: '1370337771214626816'
						}
					});
					var token = uni.getStorageSync('token');
					// _this.urlAddress = 'http://***************:9002/#' + res.data.url + `?id=${item.id || '{}'}&token=${token}`;
					let url = res.data.url + `?id=${item.id || '{}'}&token=${token}`;
					this.showWebview = true;
					uni.navigateTo({
						url: '/Recharge/onlineJoinTable/onlineClass?orderId=' +
							item.id +
							'&payStatus=' +
							item.isSubmit +
							'&isEdit=' +
							isEdit +
							'&curriculumId=' +
							item.curriculumIds +
							'&isOneToMany=1' +
							'&url=' +
							encodeURIComponent(url)
					});
					return;
				}
				// 1v1
				uni.navigateTo({
					url: '/Recharge/onlineJoinTable/onlineClass?orderId=' +
						item.id +
						'&payStatus=' +
						item.isSubmit +
						'&isEdit=' +
						isEdit +
						'&curriculumId=' +
						item.curriculumIds +
						'&curriculumIs=' +
						(item.curriculumName == '鼎英语')
				});
			}
		}
	};
</script>

<style lang="scss" scoped>
	page {
		background-color: #f3f8fc;
	}

	.img_s {
		width: 160rpx;
	}

	.detailed {
		background-color: #fff;
		border-radius: 14rpx;
		margin-bottom: 30rpx;
		position: relative;
		padding: 35rpx 30rpx 20rpx 30rpx;
		margin-left: 30rpx;
		margin-right: 30rpx;
	}

	.gray_tit {
		color: #a7a7a7;
		margin-right: 20rpx;
	}

	.border-bottom {
		border-bottom: 1px solid #efefef;
	}

	.courseInfo {
		font-size: 28rpx;
		display: flex;
		width: 550upx;
		flex-direction: column;
		justify-content: flex-start;
		letter-spacing: 2rpx;
		// align-items: center;
	}

	.code-view {
		display: flex;
		justify-content: space-between;
		font-size: 30rpx;
		line-height: 42rpx;
		color: #666666;
		margin-top: 20rpx;
	}

	.line-view {
		margin-bottom: 20rpx;
		margin-top: 35rpx;
		height: 2rpx;
		background-color: #efefef;
	}

	.option-text {
		display: flex;
		align-items: center;
		justify-content: flex-end;
		font-size: 30rpx;
		color: #2e896f;
		line-height: 42rpx;
	}

	.green-view {
		position: absolute;
		right: 30rpx;
		top: 38rpx;
		width: 90rpx;
		height: 36rpx;
		background: #2dc032;
		border-radius: 4rpx;
		border: 1rpx solid #2dc032;
		text-align: center;
		font-size: 26rpx;
		color: #ffffff;
	}

	.grey-view {
		position: absolute;
		right: 30rpx;
		top: 38rpx;
		text-align: center;
		width: 90rpx;
		height: 36rpx;
		background: #c6c6c6;
		border-radius: 4rpx;
		border: 1rpx solid #c6c6c6;
		font-size: 26rpx;
		color: #ffffff;
	}

	.btn-view {
		display: flex;
		align-items: center;
		justify-content: center;
		width: 150rpx;
		height: 60rpx;
		border-radius: 45rpx;
	}

	.btn-color-1 {
		font-size: 30rpx;
		color: #ffffff;
		background: linear-gradient(180deg, #88cfba 0%, #1d755c 100%);
	}

	.btn-color-2 {
		font-size: 30rpx;
		color: #2e896f;
		border: 1rpx solid #2e896f;
	}
</style>