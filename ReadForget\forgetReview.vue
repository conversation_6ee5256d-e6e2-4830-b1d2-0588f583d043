<template>
  <view class="plr-30">
    <view class="bg-h">
      <view class="word-position t-c col-12" style="">
        <view @click="goback">
          <uni-icons type="left" size="24" color="#000"></uni-icons>
        </view>
        <view class="f-26">{{ type == 1 ? '当日关卡预览' : '往期关卡预览' }}</view>
        <view></view>
      </view>
    </view>
    <view class="serach">
      <view style="display: flex; height: 80rpx; align-items: center">
        <view class="searchName">课程名称：</view>
        <view class="searchInput" @click="opne(1)">
          <view :class="search.courseName == '' ? 'placeholder' : 'name'">
            {{ search.courseName ? search.courseName : '请选择课程名称' }}
          </view>
          <uni-icons type="down" size="12" color="#666"></uni-icons>
        </view>
      </view>
      <view style="display: flex; height: 80rpx; align-items: center">
        <view class="searchName">关卡名称：</view>
        <view class="searchInput" @click="opne(2)">
          <view :class="search.checkPointName == '' ? 'placeholder' : 'name'">
            {{ search.checkPointName ? search.checkPointName : '请选择关卡名称' }}
          </view>
          <uni-icons type="down" size="12" color="#666"></uni-icons>
        </view>
      </view>
      <view v-if="type == 2" style="display: flex; height: 80rpx; align-items: center">
        <view class="searchName">日期：</view>
        <view class="searchInput" @click="opne(3)">
          <view :class="search.reviewTimeStr == '' ? 'placeholder' : 'name'">
            {{ search.reviewTimeStr ? search.reviewTimeStr : '请选择日期' }}
          </view>
          <uni-icons type="down" size="12" color="#666"></uni-icons>
        </view>
      </view>
    </view>
    <view class="contain" :style="{ height: useHeight + 'rpx' }">
      <view class="itemTitle">
        <view style="flex: 4; text-align: center">关卡名称</view>
        <view style="flex: 2; text-align: center">轮次</view>
        <view style="flex: 4; text-align: center">
          {{ type == 1 ? '学习时间' : '应复习时间' }}
        </view>
      </view>
      <view v-if="list.length">
        <view class="itemCenter" v-for="item in list" :key="item.id">
          <view style="flex: 4; text-align: center">
            {{ item.checkpointName }}
            <span class="classStunde">{{ item.courseStageName }}</span>
          </view>
          <view style="flex: 2; text-align: center">{{ item.round }}</view>
          <view style="flex: 4; text-align: center">{{ type == 1 ? item.lastStudyTime : item.reviewTime }}</view>
        </view>
      </view>
      <view v-else>
        <u-empty mode="data" :width="400" :height="400" icon="http://cdn.uviewui.com/uview/empty/data.png"></u-empty>
      </view>
    </view>
    <view class="paging">
      <pagePagination
        v-if="total"
        ref="pageChange"
        :total="total"
        :pageSize="search.pageSize"
        :numAround="true"
        :currentPage="search.pageNo"
        size="large"
        @change="change"
      ></pagePagination>
      <u-picker
        v-if="selectorShow"
        :show="selectorShow"
        :immediateChange="true"
        mode="selector"
        :default-selector="[0]"
        :columns="selectorArray"
        keyName="realName"
        confirmColor="#357B71"
        @confirm="confirm"
        @cancel="cancel"
      ></u-picker>
    </view>
  </view>
</template>

<script>
  import pagePagination from './components/page-pagination/components/page-pagination/page-pagination.vue';
  export default {
    components: {
      pagePagination
    },
    data() {
      return {
        type: 1,
        selectorShow: false,
        studentCode: '',
        merchantCode: '',
        list: [],
        useHeight: 0,
        searchType: 1,
        selectorArray: [],
        title: '',
        search: {
          courseName: '',
          pageSize: 10,
          pageNo: 1,
          checkPointName: '',
          reviewTimeStr: ''
        },
        total: 0
      };
    },
    onReady() {
      uni.getSystemInfo({
        success: (res) => {
          // 可使用窗口高度，将px转换rpx
          let h = res.windowHeight * (750 / res.windowWidth);
          if (this.type == 1) {
            this.useHeight = h - 470;
          } else {
            this.useHeight = h - 540;
          }
        }
      });
    },
    onLoad(options) {
      this.studentCode = options.studentCode;
      this.merchantCode = options.merchantCode;
      this.search.type = options.type;
      this.type = options.type;
    },
    onShow() {
      this.init();
    },
    methods: {
      goback() {
        uni.navigateBack();
      },
      change(currentPage, type) {
        this.search.pageNo = currentPage;
        this.init();
      },
      async init() {
        let obj = {
          ...this.search,
          studentCode: this.studentCode,
          merchantId: this.merchantCode
        };
        let { data } = await this.$httpUser.post('znyy/superReadReview/queryReviewWithPage', obj);
        if (data.success) {
          this.list = data.data.data;
          this.total = data.data.totalItems;
        }
      },
      confirm(e) {
        if (this.searchType == 1) {
          this.search.courseName = e.value[0];
        } else if (this.searchType == 2) {
          this.search.checkPointName = e.value[0];
        } else {
          this.search.reviewTimeStr = e.value[0];
        }
        this.search.pageNo = 1;
        this.init();
        this.selectorShow = false;
      },
      cancel() {
        this.selectorShow = false;
        this.selectorArray = [];
      },

      async opne(e) {
        this.searchType = e;
        this.selectorArray = [];
        if (e == 1) {
          let { data } = await this.$httpUser.get('znyy/superReadReview/getReviewCourseName', { studentCode: this.studentCode, merchantCode: this.merchantCode, type: this.type });
          this.selectorArray = [data.data];
        } else if (e == 2) {
          let { data } = await this.$httpUser.get('znyy/superReadReview/getReviewCheckPointName', {
            studentCode: this.studentCode,
            merchantCode: this.merchantCode,
            type: this.type
          });
          this.selectorArray = [data.data];
        } else {
          let { data } = await this.$httpUser.get('znyy/superReadReview/getReviewDate', {
            studentCode: this.studentCode,
            merchantCode: this.merchantCode,
            type: this.type
          });
          this.selectorArray = [data.data];
        }
        this.selectorShow = true;
      }
    }
  };
</script>

<style lang="scss" scoped>
  .contain {
    background-color: #fff;
    margin-top: 20rpx;
    border-radius: 15rpx;
    .itemTitle {
      display: flex;
      align-items: center;
      height: 108rpx;
      font-size: 28rpx;
      font-weight: bold;
    }
    .itemCenter {
      display: flex;
      align-items: center;
      height: 88rpx;
      font-size: 26rpx;
      .classStunde {
        font-size: 12rpx;
        background-color: #357b71;
        color: #fff;
        position: relative;
        top: -10rpx;
      }
    }
  }
  .paging {
    width: 100%;
    text-align: center;
    position: absolute;
    bottom: 80rpx;
    left: 0;
  }
  .word-position {
    position: fixed;
    top: 0;
    left: 0;
    background-color: #f3f8fc;
    // height: 190rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-top: 110rpx;
    box-sizing: border-box;
  }
  .serach {
    margin-top: 180rpx;
    border-radius: 15rpx;
    padding: 30rpx;
    background-color: #fff;
    .searchName {
      width: 150rpx;
      text-align: center;
      font-size: 28rpx;
      color: #555555;
    }
    .searchInput {
      flex: 1;
      margin-left: 10rpx;
      height: 60rpx;
      box-sizing: border-box;
      line-height: 60rpx;
      border-radius: 30rpx;
      border: 2rpx solid #dfdfdf;
      display: flex;
      padding: 0 38rpx;
      justify-content: space-between;
      align-items: center;
      .placeholder {
        font-size: 24rpx;
        color: #a4a4a4;
      }
      .name {
        font-size: 24rpx;
        color: #333333;
      }
    }
  }
</style>
