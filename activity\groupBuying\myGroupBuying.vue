<template>
  <!-- 我的拼团 -->
  <page-meta :page-style="'overflow:' + (rollShow ? 'hidden' : 'visible')"></page-meta>
  <view>
    <u-sticky bgColor="#F9FCFF">
      <view class="pl-25">
        <u-tabs class="status-tabs" :list="statusList" :current="activeIndex" keyName="name" lineWidth="20"
          lineHeight="5" :activeStyle="{ color: '#333333', fontWeight: 'bold', fontSize: '28rpx' }" :inactiveStyle="{
            color: '#5A5A5A ',
            transform: 'scale(1)',
            fontSize: '28rpx'
          }" itemStyle="padding-left:24px; padding-right:25px; line-height: 40px;"
          :lineColor="`url(${lineBg}) 100% 110%`" @click="tabsClick"></u-tabs>
      </view>
    </u-sticky>
    <view class="mlr-30">
      <view class="card">
        <view class="card-item" v-for="(item, index) in dataSource.data" :key="index" @click="handleGo(item)">
          <view class="card-title">
            <view class="left" :class="item.groupInstancesStatus == 0 ? 'fail' : ''">
              {{ groupStatus[item.groupInstancesStatus] }}
            </view>
            <view v-if="item.groupInstancesStatus == 0" class="right">已退款</view>
          </view>
          <view class="card-divider"></view>
          <view class="goods-item">
            <view class="goods-img">
              <image class="wh100" :src="item.piGoodsVo.goodsPicUrl" mode="aspectFill" />
            </view>
            <view class="goods-content">
              <view class="goods-title">{{ item.piGoodsVo.goodsName }}</view>
              <view class="goods-size">{{ item.piGoodsVo.goodsSpec }}</view>
              <view class="goods-price">￥{{ item.groupPrice }}</view>
            </view>
          </view>

          <view class="card-tips">
            <text style="margin-right: 6rpx" v-if="item.groupLeaderId == userId">我是团长</text>
            <text style="margin-right: 6rpx">{{ item.groupSize }}人团</text>
            <text v-if="item.groupInstancesStatus == 1">
              仅剩
              <text class="orange">{{ item.groupOverNum }}</text>
              人完成拼团
            </text>
            <text v-if="item.groupInstancesStatus == 2">已完成拼团</text>
          </view>
        </view>
      </view>
      <view v-if="dataSource.data && dataSource.data.length == 0" class="t-c flex-col noData">
        <image :src="imgHost + 'alading/correcting/no_data.png'" class="mb-20" mode="widthFix"></image>
        <view style="color: #bdbdbd">暂无数据</view>
      </view>
      <view v-if="noMore && dataSource.data && dataSource.data.length > 0">
        <u-divider text="到底了"></u-divider>
      </view>
    </view>
  </view>
</template>

<script>
  const {
    $navigationTo,
    $http
  } = require('@/util/methods.js');
  const {
    httpUser
  } = require('@/util/luch-request/indexUser.js');
  export default {
    data() {
      return {
        imgHost: getApp().globalData.imgsomeHost,
        activeIndex: 0, // 选中的tab索引
        rollShow: false, //禁止滚动穿透
        // 全部 待支付 已支付  已完成 已失效
        statusList: [{
            name: '全部'
          },
          {
            name: '开团中'
          },
          {
            name: '参团中'
          },
          {
            name: '拼购成功'
          }
        ],
        groupStatus: ['拼团失败', '拼团进行中', '拼团成功', '拼团发起中'],
        page: 1,
        noMore: false,
        dataSource: {
          data: [],
          totalPage: 0,
          page: 1,
          totalCount: 0
        },
        lineBg: 'https://document.dxznjy.com/course/01c96465d9ea46f69aa97465b1201aef.png',
        userId: ''
      };
    },
    onLoad(e) {
      if (e.token) {
        this.app = e.app;
        this.$handleTokenFormNative(e);
      }
    },
    onUnload() {
      // #ifdef APP-PLUS
      if (this.app) {
        plus.runtime.quit();
      }
      // #endif
    },
    onShow() {
      this.userId = uni.getStorageSync('user_id');
      this.tabsClick();
    },
    onReachBottom() {
      if (this.page >= this.dataSource.totalPage) {
        this.noMore = true;
        return false;
      }
      this.getDataList(true, ++this.page);
    },

    onPullDownRefresh() {
      this.tabsClick();

      setTimeout(function() {
        uni.stopPullDownRefresh();
      }, 1000);
    },

    methods: {
      // 禁止滚动穿透
      changePopup(e) {
        this.rollShow = e.show;
      },

      showLoading() {
        uni.showLoading({
          title: '加载中',
          mask: true
        });
      },
      // <!-- // 0:待支付 1:待发货 2:已完成 3:已取消 4:退款成功 5:退款中 6:退款失败 7:待收货 8:待评价 9已关闭-->
      tabsClick(e) {
        this.showLoading();
        this.page = 1;

        if (e) {
          this.activeIndex = e.index;
        }

        let name = this.statusList[this.activeIndex].name;

        switch (name) {
          case '全部':
            this.getDataList();
            break;
          case '开团中':
            this.getDataList(false, 1, 1, '1');
            break;
          case '参团中':
            this.getDataList(false, 1, 1, '0');
            break;
          case '拼购成功':
            this.getDataList(false, 1, 2);
            break;
          default:
            break;
        }
      },

      async getDataList(isPage, page, status, isLeader) {
        let _this = this;
        let params = {
          userId: uni.getStorageSync('user_id') ? uni.getStorageSync('user_id') : '',
          pageNum: page || 1,
          pageSize: 20
        };
        // 是否是团长（0,1）
        if (isLeader) {
          params.isLeader = isLeader;
        }
        // (0, "未完成"),(1, "进行中"),(2, "已完成"),(3, "拼团发起中");）
        if (status) {
          params.groupInstancesStatus = status;
        }
        const res = await $http({
          url: 'zx/wap/group/instances/page',
          data: params
        });
        if (res) {
          if (isPage) {
            let old = _this.dataSource.data;
            _this.dataSource.data = [...old, ...res.data.data];
          } else {
            _this.dataSource = res.data;
          }
        }
      },
      handleGo(item) {
        console.log('🚀 ~ handleGo ~ item:', item);
        // 拼团失败
        if (item.groupInstancesStatus == 0) {
          $navigationTo(
            `activity/groupBuying/groupBuyingFail?groupInstanceId=${item.id}&groupActivityId=${item.groupActivityId}`);
        }

        // 拼团进行中
        if (item.groupInstancesStatus == 1) {
          $navigationTo(
            `Coursedetails/productDetils?id=${item.goodsId}&groupActivityId=${item.groupActivityId}&isGroupBuyGood=1&isJoinGroup=1&groupInstanceId=${item.id}`
          );
        }

        // 拼团成功
        if (item.groupInstancesStatus == 2) {
          $navigationTo(
            `Coursedetails/productDetils?id=${item.goodsId}&groupActivityId=${item.groupActivityId}&isGroupBuyGood=1&isJoinGroup=1&groupInstanceId=${item.id}`
          );
        }
      }
    }
  };
</script>

<style lang="scss">
  page {
    height: 100%;
    background: #f9fcff;
  }
</style>
<style lang="scss" scoped>
  .card {
    margin-top: 24rpx;

    .card-item {
      width: 686rpx;
      height: 412rpx;
      box-sizing: border-box;
      padding: 28rpx 24rpx 36rpx;
      background: #ffffff;
      border-radius: 16rpx;
      margin-bottom: 24rpx;

      .card-title {
        display: flex;
        align-items: center;
        justify-content: space-between;

        .left {
          font-family: AlibabaPuHuiTiBold;
          font-weight: bold;
          font-size: 32rpx;
          color: #339378;
          line-height: 44rpx;
        }

        .fail {
          color: #fd9b2a;
        }

        .right {
          font-size: 28rpx;
          color: #9fa0a1;
          line-height: 40rpx;
        }
      }

      .card-divider {
        width: 100%;
        height: 2rpx;
        margin: 24rpx 0;
        border-top: 2rpx solid #f6f7f9;
      }
    }
  }

  .goods-item {
    display: flex;
    align-items: center;
    width: 100%;
    height: 176rpx;
    margin-bottom: 34rpx;

    .goods-img {
      width: 176rpx;
      height: 176rpx;
      margin-right: 26rpx;
    }
  }

  .goods-content {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items: flex-start;
    height: 100%;

    .goods-title {
      width: 100%;
      max-width: 442rpx;
      font-size: 28rpx;
      color: #555555;
      font-weight: bold;
      line-height: 44rpx;
    }

    .goods-size {
      font-size: 28rpx;
      color: #9fa0a1;
      line-height: 44rpx;
    }

    .goods-price {
      color: #ed7d4d;
      font-size: 28rpx;
      color: #ed7d4d;
      line-height: 44rpx;
    }
  }

  .card-tips {
    padding-left: 32rpx;
    font-size: 28rpx;
    color: #555555;
    line-height: 44rpx;

    .orange {
      color: #fd9b2a;
    }
  }

  .noData {
    height: calc(100vh - 200rpx);

    image {
      width: 200rpx;
      height: 200rpx;
    }
  }
</style>