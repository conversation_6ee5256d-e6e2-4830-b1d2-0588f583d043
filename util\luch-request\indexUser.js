import Request from './request';
import Config from '../config.js';
import Util from './util.js';

var httpArray = new Array();

const httpUser = new Request();

httpUser.setConfig((config) => {
  /* cxy设置全局配置 */
  config.baseUrl = uni.getStorageSync('baseUrl'); /* 根域名不同 */
  console.log(config.baseUrl);
  config.header = {
    'content-type': 'application/json;charset=UTF-8',
    'x-www-endorsement': ''
  };
  return config;
});

httpUser.interceptor.request((config, cancel) => {
  /* 请求之前拦截器 */
  Util.checkNetwork();
  console.log(config, 'util');
let a = uni.getStorageSync('baseUrl')||Config.DXHost
if(config.url.indexOf(a)==-1){
	config.url=a+config.url
	console.log(config.url);
}
  var Logintoken = uni.getStorageSync('token');
  // debugger
  // cxy 2021 4.21添加 趣味复习token不同于其它板块（接口调用pad）

  // 门店充值需要的token
  let payToken = uni.getStorageSync('payToken');
  let wxpay = uni.getStorageSync('wxpay');
  // if(payToken){
  // 	Logintoken = payToken
  // }

  var LogintokenReview = uni.getStorageSync('logintokenReview');
  // console.log(uni.getStorageSync('logintokenReview'),'取学生token')
  let pages = getCurrentPages();
  let route = pages[pages.length - 1].route;
  var isReview = false; //是否是趣味复习需要znyy token
  if (route.indexOf('review/funReview') != -1 || route.indexOf('pages/interest/') != -1 || route.indexOf('pages/wordCheck') != -1) {
    if (route.indexOf('pages/interest/orderDetail') != -1) {
      isReview = false;
    } else {
      isReview = true;
    }
  }
  config.header = {
    ...config.header
  };
  config.header['www-cid'] = 'dx_alading_resource';
  var dxSourse = 'ZHEN_XUAN##WX##MINIAPP'; //小程序
  // #ifdef APP-PLUS
  dxSourse = 'ZHEN_XUAN##PHONE##APP'; //app
  // #endif
  config.header['dx-source'] = dxSourse;
  if (wxpay || payToken) {
    if (config.url.indexOf('mps/line/collect/order/unified/collect') > -1) {
      // #ifdef APP-PLUS
      if (config.url.indexOf('mps/line/collect/order/unified/collect/check') > -1) {
        config.header['dx-source'] = 'ZNYY##WX##MINIAPP';
      } else {
        config.header['dx-source'] = dxSourse;
      }
      // #endif
      // #ifdef MP-WEIXIN
      config.header['dx-source'] = 'ZNYY##WX##MINIAPP';
      // #endif
    }
  }
  if (config.url.indexOf('zx/exp/sendRedPacket') > -1) {
    config.header['dx-source'] = dxSourse;
  }
  if (config.url.indexOf('scrm/qywechat/getQrcodeByCode') > -1) {
    config.header['dx-source'] = dxSourse;
    delete config.header['Token'];
    delete config.header['x-www-iap-assertion'];
  }
  // console.log(Logintoken,'登录token')
  // console.log(LogintokenReview,'学生token')
  if (Logintoken != '' && Logintoken != undefined && Logintoken.length > 5) {
    if (LogintokenReview != '' && LogintokenReview != undefined) {
      //如果趣味复习有pad的token则代表在趣味复习板块
      config.header['x-www-iap-assertion'] = LogintokenReview;
    } else {
      config.header['x-www-iap-assertion'] = Logintoken;
    }
    if (config.url.indexOf('scrm/qywechat/getQrcodeByCode') > -1) {
      config.header['dx-source'] = dxSourse;
      delete config.header['Token'];
      delete config.header['x-www-iap-assertion'];
    }
  } else {
    // #ifdef APP-PLUS
    config.header = {
      'www-cid': 'dx_alading_resource',
      'content-type': 'application/json;charset=UTF-8',
      'dx-source': dxSourse
    };
    // #endif
    // #ifdef MP-WEIXIN
    config.header = {
      'www-cid': 'dx_alading_resource',
      'content-type': 'application/json;charset=UTF-8',
      'dx-source': dxSourse
    };
    // #endif
  }

  if (config.url.indexOf('znyy/areas/student/charge/expReward/save') > -1) {
    config.header['dx-source'] = dxSourse;
    config.header['Token'] = Logintoken;
    config.header['x-www-iap-assertion'] = payToken;
  }

  return config;
});

httpUser.interceptor.response((response) => {
  console.log(response, 'util');
  /* 请求之后拦截器 */
  delete httpArray[response.config.hashData];
  if (response.data.status == 1 || response.data.status == 20004 || response.data.code == 20000) {
    return response;
  } else if (response.statusCode == 404 || response.data.status == 10000 || response.data.status == 10003 || response.data.code == 50003) {
    var islogin = uni.getStorageSync('isLogin');
    // uni.setStorageSync('isLogin', false)
    // uni.removeStorageSync("logintoken")
    // if (islogin == ''||islogin==false) {
    // 	uni.navigateTo({
    // 		url: '/pages/login/login'
    // 	});
    // 	uni.setStorageSync('isLogin', 'islogin')
    // }
    return response;
  } else if (response.statusCode == 401 || response.statusCode == 50004) {
    var jump = uni.getStorageSync('jump'); //以下解决多次跳转登录页的重点
    if (!jump) {
      //以下做token失效的操作
      setTimeout(() => {
        uni.navigateTo({
          url: '/Personalcenter/login/login'
        });
      }, 100);
      uni.removeStorage({
        key: 'token'
      });
      uni.setStorageSync('jump', 'true');
    }
  } else {
    if (response.config.url.indexOf('v2/mall/getStudentMerchantList') > -1) {
      if (response.data.message == '需购买趣味复习') {
        console.log('不显示提示');
      } else {
        let message = response.data.message;
        if (message.includes('@eid')) {
          message = message.split('@eid')[0];
        }
        uni.showToast({
          icon: 'none',
          title: response.data.message,
          duration: 2000
        });
      }
    } else {
      let message = response.data.message;
      if (message.includes('@eid')) {
        message = message.split('@eid')[0];
      }
      uni.showModal({
        title: '温馨提示',
        content: message || '服务器错误',
        showCancel: false
      });
    }

    return response;
  }
});
export { httpUser };
