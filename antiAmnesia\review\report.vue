<template>
  <view>
    <image :src="imgHost + '/img/review-bg.png'" class="review-img"></image>
    <view class="from">
      <image src="/static/topreview.png" class="topreview"></image>
      <h3 class="statistics">恭喜你，正确率达</h3>
      <h3 class="percentage">
        {{ rate == undefined ? '' : rate }}
        <text>%</text>
      </h3>
      <view class="accomplish">本场作业完成情况统计</view>
      <view class="report">
        <view class="li">
          <view class="color-ffa423">
            {{ wordcount == undefined ? '' : wordcount }}
            <text>个</text>
          </view>
          <text class="name">需复习</text>
        </view>
        <view class="li">
          <view class="color-007D70">
            {{ worded == undefined ? '' : worded }}
            <text>个</text>
          </view>
          <text class="name">已复习</text>
        </view>
        <view class="li">
          <view class="color-ff0000">
            {{ noword == undefined ? '' : noword }}
            <text>个</text>
          </view>
          <text class="name">未复习</text>
        </view>
      </view>
      <view class="footer clearfix">
        <button class="fl" @click="tohistory()">复习往期</button>
        <button class="fl ml50" @click="toindex()">返回</button>
      </view>
    </view>

    <view class="report_bottom">
      <image :src="imgHostUse + 'interesting/report_bottom.png'" mode=""></image>
      <view class="sunStyle zwyHeightSec" @click="goToInterest">挑战更多关卡</view>
    </view>

    <!-- 选择校区弹窗 -->
    <uni-popup ref="popopChooseSchool" type="center" :maskClick="true" :classBG="''">
      <interesting-dialog
        :title="'温馨提示'"
        :chooseTitle="'选择校区'"
        :array="arraySchool"
        :keyName="'merchantName'"
        :imgUrl="'interesting/intere_iconAddress.png'"
        @chooselist="chooseSchoollist"
        @closeDialog="closeDialog"
      ></interesting-dialog>
    </uni-popup>

    <!-- 没有开通权限 -->
    <uni-popup ref="popopPower" type="center" :maskClick="true" :classBG="''">
      <interesting-index
        :title="'温馨提示'"
        :isRed="true"
        :isReview="false"
        :isSingle="true"
        :txtContent="txtContent"
        @nowBuy="nowBuy()"
        @closeDialog="closeDialog()"
      ></interesting-index>
    </uni-popup>
  </view>
</template>

<script>
  import interestingDialog from '../components/interesting-dialog/index-img.vue';
  import interestingIndex from '../components/interesting-dialog/index.vue';
  export default {
    components: {
      interestingDialog,
      interestingIndex
    },
    data() {
      return {
        imgHost: getApp().globalData.imgHost,
        imgHostUse: getApp().globalData.imguseHost,
        wordcount: 0,
        worded: 0,
        noword: 0,
        rate: 0,
        studentCode: '',

        arraySchool: [],
        txtContent: '您还没有开通使用权限',
        merchantCode: ''
      };
    },
    onLoad: function (option) {
      uni.showLoading({
        title: '加载中'
      });
      this.loadData(option);
      uni.hideLoading();
    },
    methods: {
      async loadData(option) {
        var that = this;
        that.wordcount = option.wordcount; //需要复习
        that.worded = option.worded; //已经复习
        that.noword = that.wordcount - that.worded; //未复习
        that.rate = option.rate;
        that.studentCode = option.studentCode;
      },
      tohistory() {
        uni.navigateTo({
          url: '/antiAmnesia/review/history?studentCode=' + this.studentCode
        });
      },
      toindex() {
        console.log(this.studentCode);
        uni.navigateTo({
          url: '/antiAmnesia/review/index?studentCode=' + this.studentCode
        });
      },
      // 趣味复习
      goToInterest() {
        let that = this;
        that.$httpUser.get('v2/mall/getStudentMerchantList?studentCode=' + that.studentCode).then((result) => {
          if (result.data.success) {
            // 没有购买去购买
            if (result.data.code == 20004) {
              setTimeout(function () {
                that.$refs.popopPower.open();
              }, 500);
              return;
            }
            if (result.data.success) {
              that.arraySchool = result.data.data;
              if (result.data.data.length == 0) {
                that.$util.alter('您还没有学习过课程');
              }
              if (result.data.data.length == 1) {
                that.merchantCode = that.arraySchool[0].merchantCode;
                that.getPadToken();
              } else if (result.data.data.length > 1) {
                that.$refs.popopChooseSchool.open();
              }
            }
          }
        });
      },

      //点击选择校区
      chooseSchoollist(e) {
        console.log('点击选择校区');
        this.merchantCode = this.arraySchool[e.target.value].merchantCode;
        this.closeDialog();
        this.getPadToken();
      },
      //立即购买趣味复习
      nowBuy() {
        console.log('立即购买趣味复习');
        this.closeDialog();
        uni.navigateTo({
          url: `/Personalcenter/interest/orderDetail?studentCode=${this.studentCode}`
        });
      },

      //关闭弹窗
      closeDialog() {
        this.$refs.popopChooseSchool.close();
        this.$refs.popopPower.close();
      },

      // 获取pad token
      getPadToken(scheduleCode) {
        let that = this;
        var token = uni.getStorageSync('token');
        that.$httpUser.get(`new/security/login/student/member/token?memberToken=${token}&studentCode=${that.studentCode}&merchantCode=${that.merchantCode}`).then((res) => {
          if (res.data.success) {
            uni.setStorageSync('logintokenReview', res.data.data.token);
            uni.navigateTo({
              url: `/antiAmnesia/review/funReview?studentCode=${that.studentCode}&merchantCode=${that.merchantCode}`
            });
          } else {
            that.$util.alter(res.data.message);
          }
        });
      }
    }
  };
</script>

<style>
  .color-ffa423 {
    color: #ffa423 !important;
  }

  .color-007D70 {
    color: #007d70 !important;
  }

  .color-ff0000 {
    color: #ff0000 !important;
  }

  .accomplish {
    width: 280rpx;
    height: 38rpx;
    background: #fff4e4;
    margin: 0 auto;
    margin-top: 60rpx;
    color: #ffa423;
    font-size: 26rpx;
    text-align: center;
    line-height: 38rpx;
  }

  .report {
    display: flex;
    align-items: center;
    justify-content: space-between;
    text-align: center;
    margin-top: 80rpx;
  }

  .li {
    border-right: 1px solid #e5e5e5;
    width: 33.333%;
  }

  .name {
    font-size: 28rpx;
    color: #999;
  }

  .li view {
    font-size: 48rpx;
  }

  .li view text {
    font-size: 24rpx !important;
    color: #333;
    margin-left: 5rpx;
  }

  .footer {
    position: absolute;
    top: 840rpx;
    width: 590rpx;
    left: 50%;
    margin-left: -295rpx;
  }

  button {
    width: 270rpx;
    height: 80rpx;
    border-radius: 100rpx;
    font-size: 30rpx;
    background: #ffa423;
    text-align: center;
    line-height: 80rpx;
    color: #fff;
  }

  .ml50 {
    margin-left: 50rpx;
    background: #007d70 !important;
  }

  uni-page-body {
    padding-bottom: 0px !important;
  }

  .review-img {
    position: absolute;
    overflow: hidden;
    z-index: -10;
    background-size: cover;
    pointer-events: none;
    width: 100%;
    height: 100%;
    right: 0px;
    top: 0px;
    bottom: 0px;
    left: 0px;
  }

  .from {
    width: 630rpx;
    height: 780rpx;
    background: #ffffff;
    border-radius: 20rpx;
    position: absolute;
    top: 150rpx;
    left: 50%;
    margin-left: -315rpx;
  }

  .topreview {
    display: block;
    width: 150rpx;
    height: 170rpx;
    margin: 0 auto;
    margin-top: -70rpx;
  }

  .statistics {
    text-align: center;
    font-size: 42rpx;
    margin-top: 90rpx;
    font-weight: normal;
  }

  .percentage {
    font-size: 60rpx;
    color: #ffa202;
    text-align: center;
    margin-top: 30rpx;
    font-weight: normal;
  }

  .percentage text {
    font-size: 40rpx !important;
  }

  .report_bottom {
    margin: 56rpx 90rpx;
    height: 242rpx;
    width: 570rpx;
    border-radius: 12rpx;
    position: absolute;
    top: 1070rpx;
    left: 0;
  }
  .report_bottom image {
    width: 100%;
    height: 100%;
  }
</style>
