<template>
  <view class="study_content_print_page">
    <view class="bg-ff radius-15 plr-30 ptb-40 f-30 c-33">
      <view class="content_hd flex">
        <view style="width: 148upx; text-align: left">{{ studentName }}</view>
        <view style="width: 148upx">{{ typeName }}</view>
        <view style="width: 280upx; text-align: right">{{ submitTime }}</view>
      </view>
      <view class="radio flex" v-if="type === 'Word'">
        <view class="raido_label">打印内容：</view>
        <view style="flex: 1; display: flex">
          <view class="radio_item" v-for="(item, index) in printWayList" :key="item.value" @click="printIndex = index">
            <view class="radio_margin check_btn" :class="index === printIndex ? 'checked' : ''">
              <view v-if="index === printIndex" class="check_circle"></view>
            </view>
            <view class="c-66">{{ item.name }}</view>
          </view>
        </view>
      </view>
      <view class="radio flex">
        <view class="raido_label">下载格式：</view>
        <view style="flex: 1; display: flex">
          <view class="radio_item" v-for="(item, index) in downTypeList" :key="item.value" @click="downTypeIndex = index">
            <view class="radio_margin check_btn" :class="index === downTypeIndex ? 'checked' : ''">
              <view v-if="index === downTypeIndex" class="check_circle"></view>
            </view>
            <view class="c-66">{{ item.name }}</view>
          </view>
        </view>
      </view>
    </view>
    <view class="bg-ff radius-15 p-30 mt-30" :style="{ height: useHeight + 'rpx' }">
      <view class="f-28 radio" v-if="type === 'Word'">
        预览：
        <text>(中英文不支持预览，直接下载即可)</text>
      </view>
      <view class="preview_box">
        <view class="image_class">
          <view v-for="(previewItem, index) in previewImages" :key="index">
            <image :src="previewItem" mode="cover"></image>
          </view>
        </view>
      </view>
      <view class="confirm_btn" @click="confirmPrint">
        <text>一键下载</text>
      </view>
    </view>
  </view>
</template>

<script>
  export default {
    data() {
      return {
        title: '',
        studentName: '',
        studentCode: '',
        type: '',
        printCode: '',
        submitTime: '',
        typeName: '',
        downPdfSrc: '',
        downImgSrc: [],
        previewImages: [], // 预览数据
        printIndex: 0,
        printWayList: [
          {
            value: 'ALL', // 仅type == words时候展示
            name: '全打印',
            show: false
          },
          {
            value: 'EN',
            name: '英文',
            show: true
          },
          {
            value: 'CN',
            name: '中文',
            show: true
          }
        ],
        downTypeIndex: 0,
        downTypeList: [
          // {
          // 	value: 'PNG',
          // 	name: '图片',
          // },
          {
            value: 'ZIP',
            name: 'zip'
          }
        ],

        useHeight: 0, //除头部之外高度
        imgHost: getApp().globalData.imgsomeHost,

        isAllDay: false
      };
    },

    onReady() {
      uni.getSystemInfo({
        success: (res) => {
          // 可使用窗口高度，将px转换rpx
          let h = res.windowHeight * (750 / res.windowWidth);
          this.useHeight = h - 440;
        }
      });
    },

    onLoad(option) {
      var that = this;
      that.isAllDay = option.allDay;
      that.studentName = option.studentName;
      that.studentCode = option.studentCode;
      that.type = option.type;
      that.submitTime = option.submitTime;
      that.printCode = option.printCode;
      that.typeName = option.type === 'Word' ? '单词' : option.type === 'Reading' ? '阅读理解' : '语法';
      that.title = option.title;
      // if(that.isAllDay == 1){
      //     that.getPrintAllContent();
      // }else{
      //     that.getPrintContent();
      // }
      that.getPrintAllContent();
      uni.showLoading({
        title: '加载中...'
      });
    },
    methods: {
      // // 获取打印信息
      // async getPrintContent() {
      // 	let that = this;
      // 	await that.$httpUser.get("znyy/student/print/detail", {
      // 		printCode: that.printCode,
      // 		type: that.type,
      // 		studentName: that.studentName,
      // 	}).then(result => {
      // 		if (result.data != {} && result.data.data.previewImages.length > 0) {
      // 			result.data.data.previewImages.forEach(item => {
      // 				that.previewImages.push(this.getBase64ImageUrl(item));
      // 			})
      // 		}
      // 		setTimeout(function() {
      // 			uni.hideLoading();
      // 		}, 200);
      // 	})
      // },

      async getPrintAllContent() {
        let that = this;
        await that.$httpUser
          .get('znyy/student/print/detail/graduation', {
            printCode: that.printCode,
            wordPrintCode: that.printCode,
            courseContentType: that.type,
            type: that.type,
            preview: true,
            studentName: that.studentName,
            name: that.studentName,
            title: that.title
          })
          .then((result) => {
            if (result.data != {} && result.data.data) {
              if (result.data.data.previewImages.length > 0) {
                let newArry = [];
                newArry = result.data.data.previewImages.slice(0, 1);
                newArry.forEach((item) => {
                  that.previewImages.push(this.getBase64ImageUrl(item));
                });
                // result.data.data.previewImages.forEach(item => {
                // 	that.previewImages.push(this.getBase64ImageUrl(item));
                // })
              } else {
                result.data.data.previewImages.forEach((item) => {
                  that.previewImages.push(this.getBase64ImageUrl(item));
                });
              }
              that.printCode = result.data.data.printCode;
              console.log(that.printCode);
            }
            setTimeout(function () {
              uni.hideLoading();
            }, 200);
          });
      },

      //把base64转换成图片
      getBase64ImageUrl(base64Url) {
        /// 获取到base64Data
        var base64Data = base64Url;
        /// 通过微信小程序自带方法将base64转为二进制去除特殊符号，再转回base64
        base64Data = wx.arrayBufferToBase64(wx.base64ToArrayBuffer(base64Data));
        /// 拼接请求头，data格式可以为image/png或者image/jpeg等，看需求
        const base64ImgUrl = 'data:image/png;base64,' + base64Data;
        /// 得到的base64ImgUrl直接给图片:src使用即可
        return base64ImgUrl;
      },

      // 确认打印
      async confirmPrint() {
        let that = this;
        let requestData = {
          contentType: that.type === 'Word' ? that.printWayList[that.printIndex].value : '',
          fileType: that.downTypeList[that.downTypeIndex].value,
          printCode: that.printCode,
          studentName: that.studentName,
          type: that.type,
          title: that.title,
          day: true
        };
        if (that.isAllDay == 1) {
          requestData.day = true;
        }
        await that.$httpUser.get('znyy/student/print/download/file', requestData).then((result) => {
          if (result.data.success) {
            if (that.downTypeList[that.downTypeIndex].value == 'ZIP') {
              that.downPdfSrc = result.data.data.files[0];
              uni.showModal({
                title: '这是一个外部链接',
                content: '暂不支持下载Zip类文件，可点击复制链接后在浏览器中粘贴查看',
                confirmText: '复制链接',
                success: function (res) {
                  if (res.confirm) {
                    that.copyMessage(that.downPdfSrc);
                  }
                }
              });
            } else {
              that.downImgSrc = result.data.data.files;
              that.downLoadImg();
            }
          }
        });
      },

      // 复制链接(pdf)
      copyMessage(value) {
        uni.setClipboardData({
          data: value,
          success: function (res) {
            uni.getClipboardData({
              success: function (res) {
                uni.showToast({
                  title: '复制成功'
                });
              }
            });
          }
        });
      },

      // 下载地址(图片)
      downLoadImg() {
        let that = this;

        if (that.downImgSrc && that.downImgSrc.length > 0) {
          that.downImgSrc.forEach((item) => {
            // 1.1 调用下载api方法
            console.log(22222);
            uni.downloadFile({
              url: item,
              success: (res) => {
                // 1.2 获取远程图片地址后,将图片地址缓存到本地
                if (res.statusCode === 200) {
                  uni.saveImageToPhotosAlbum({
                    filePath: res.tempFilePath, // 把远程的图片地址及图片保存到本地
                    success: function (res) {
                      // 1.3保存成功后弹框提示保存成功
                      uni.showToast({
                        title: '保存成功',
                        icon: 'none'
                      });
                    },
                    fail: function (res) {
                      // 1.4保存失败给用户弹框提示
                      uni.showToast({
                        title: '保存失败',
                        icon: 'none'
                      });
                      if (res.errMsg == 'saveImageToPhotosAlbum:fail cancel') {
                        return;
                      }
                    }
                  });
                }
              },
              fail(err) {
                console.log(err);
              }
            });
          });
        } else {
          uni.showToast({
            title: '暂无数据',
            icon: 'none'
          });
        }
      }
    }
  };
</script>

<style>
  .study_content_print_page {
    height: 100vh;
    color: rgba(236, 236, 236, 1);
    padding: 0 30upx;
    position: relative;
  }

  .content_hd {
    color: rgba(51, 51, 51, 1);
    font-size: 28upx;
    justify-content: space-between;
    margin-bottom: 40upx;
  }

  .radio {
    color: rgba(102, 102, 102, 1);
    margin-bottom: 40upx;
    display: flex;
  }

  .radio_margin {
    margin-right: 19upx;
  }

  .raido_label {
    width: 180upx;
    color: #333333;
  }

  .radio_item {
    width: 145upx;
    display: flex;
    align-items: center;
    margin-right: 8upx;
  }
  .radio_item:last-child {
    margin: 0;
  }

  .check_btn {
    width: 26upx;
    height: 26upx;
    border-radius: 26upx;
    border: 1upx solid #d3d3d3;
  }

  .checked {
    border: 1upx solid #2e896f !important;
  }

  .check_circle {
    width: 22upx;
    height: 22upx;
    margin: 2upx auto;
    border-radius: 22upx;
    background: #2e896f;
  }

  .confirm_btn {
    position: absolute;
    bottom: 60rpx;
    width: 84.5%;
    height: 90upx;
    background: linear-gradient(180deg, #88cfba 0%, #1d755c 100%);
    border-radius: 45upx;
    line-height: 90upx;
    font-size: 30upx;
    color: #ffffff;
    text-align: center;
  }

  .preview_box {
    color: #999999;
  }

  .image_class {
    overflow-y: scroll;
    width: 614upx;
    height: 650upx;
    border-radius: 14upx;
    border: 1px solid rgba(199, 199, 199, 1);
  }

  .content {
    width: 530rpx;
    height: 300rpx;
    background: #fff;
    border-radius: 12upx;
    padding: 30upx;
    text-align: center;
  }

  .title {
    color: #000000;
  }

  .text {
    margin-top: 40upx;
    color: #666666;
    font-size: 28upx;
  }

  .bottom_btn {
    margin-top: 40upx;
    color: #646464;
  }
</style>
