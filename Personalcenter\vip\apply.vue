<template>
  <view>
    <form @submit="submitS">
      <view class="mlr-30 bg-ff radius-15" :style="{ height: useHeight + 'rpx' }">
        <view class="p-30 flex-dir-row flex-y-c bg-ff">
          <text class="f-28 mr-20 c-66">选择渠道商等级</text>
          <view class="screenitem flex" @click="show = true">
            <view class="f-28">{{ merchantRank || '等级选择' }}</view>
            <image :src="imgHost + 'dxSelect/fourthEdition/xia.png'" class="xiaimg"></image>
          </view>
          <!-- <view class="screenitem flex">
						<picker class="screenPicker" name="merchantRank" :value="merchantRank" @change="bindPickerChange"
							range-key="name" :range="array">
							<view class="f-28">{{ merchantRank || '等级选择'}}</view>
						</picker>
						<image src="/static/user/xia.png" class="xiaimg"></image>
					</view> -->
        </view>
        <view class="plr-30">
          <view class="radius-15 p-20 back-color">
            <textarea placeholder="渠道商申请说明" name="applyReason" class="remark f-28"></textarea>
          </view>
          <button class="submit f-34 t-c c-ff mt-40" form-type="submit">确定提交</button>
        </view>
      </view>
      <u-picker :show="show" :columns="columns" keyName="label" @confirm="confirm" confirmColor="#EA6031" @cancel="cancel" :immediateChange="true"></u-picker>
    </form>
  </view>
</template>

<script>
  const { $http, $showSuccess } = require('@/util/methods.js');
  export default {
    data() {
      return {
        show: false,
        useHeight: 0, //除头部之外高度
        merchantRank: '', // 渠道商等级
        arrayIndex: 0,
        columns: [
          [
            {
              label: 'A'
            },
            {
              label: 'B'
            },
            {
              label: 'C'
            },
            {
              label: 'D'
            },
            {
              label: 'E'
            }
          ]
        ],

        // array: [
        // 	{
        // 		name: 'A'
        // 	}, {
        // 		name: 'B'
        // 	}, {
        // 		name: 'C'
        // 	}, {
        // 		name: 'D'
        // 	}, {
        // 		name: 'E'
        // 	}
        // ],

        imgHost: getApp().globalData.imgsomeHost
      };
    },
    onLoad() {},
    onReady() {
      uni.getSystemInfo({
        success: (res) => {
          // 可使用窗口高度，将px转换rpx
          console.log(res);
          let h = res.windowHeight * (750 / res.windowWidth);
          this.useHeight = h - 30;
        }
      });
    },
    onShow() {},
    methods: {
      confirm(e) {
        this.merchantRank = e.value[0].label;
        this.show = false;
      },
      cancel() {
        this.show = false;
      },
      submitS(e) {
        let _this = this,
          values = e.detail.value;
        console.log(values);
        uni.showModal({
          title: '提示',
          content: '确认提交渠道商申请吗？',
          success: async function (res) {
            if (res.confirm) {
              const resdata = await $http({
                url: 'zx/user/applyMerchant',
                method: 'post',
                data: {
                  applyReason: values.applyReason,
                  merchantRank: _this.merchantRank
                }
              });
              if (resdata) {
                $showSuccess(resdata.message);
                setTimeout(function () {
                  uni.navigateBack();
                }, 2000);
              }
            } else if (res.cancel) {
              console.log('用户点击取消');
            }
          }
        });
      },
      bindPickerChange(e) {
        let index = e.detail.value;
        this.merchantRank = this.array[index].name;
      }
    }
  };
</script>

<style lang="scss">
  .submit {
    background-image: linear-gradient(to bottom, #f09234, #ea6031);
    height: 88upx;
    border-radius: 44upx;
    line-height: 88upx;
  }
  .remark {
    height: 700rpx;
    width: 100%;
  }

  .screenitem {
    width: 340rpx;
    height: 70rpx;
    border: 1rpx solid #c5c5c5;
    border-radius: 35rpx;
    padding: 0 30rpx;

    .screenPicker {
      flex: 1;
    }

    .xiaimg {
      width: 20rpx;
      height: 20rpx;
    }
  }
  .back-color {
    background-color: #f4f4f4;
  }

  /deep/.u-toolbar.data-v-6d25fc6f {
    border-bottom: 1px solid #e0e0e0;
  }

  /deep/.u-picker__view__column__item {
    line-height: 68rpx !important;
    background-color: #f4f4f4 !important;
  }
  /deep/.u-picker__view {
    height: 300rpx !important;
  }

  /deep/.u-picker__view__column.data-v-f45a262e {
    border-radius: 12rpx;
  }

  /deep/.u-popup__content.data-v-52d4ddd1 {
    margin: 0 20rpx 20rpx 20rpx;
    border-radius: 12rpx;
  }
</style>
