<template>
  <view class="plr-30 pb-30">
    <view class="list_box mt-30">
      <view v-if="listKnow.length > 0">
        <uv-list>
          <uv-list-item>
            <view @tap="gotoKnowledge(item)" class="listBorder" v-for="(item, index) in listKnow" :key="index">
              <view class="flex-a-c">
                <text class="p-30">{{ item.checkpointName }}</text>
              </view>
              <view class="flex-a-c">
                <u-icon name="arrow-right"></u-icon>
              </view>
            </view>
          </uv-list-item>
        </uv-list>
      </view>
      <view class="bg-ff radius-15 plr-20 mt-30 t-c flex-col" v-else :style="{ height: useHeight + 'rpx' }" style="position: relative">
        <image src="/static/index/<EMAIL>" mode="widthFix" class="mb-20 img_s"></image>
        <view style="color: #bdbdbd">暂无数据</view>
        <image src="/static/index/<EMAIL>" mode="widthFix" class="mb-20 img_s"></image>
      </view>
    </view>
  </view>
</template>
<script>
  export default {
    data() {
      return {
        studentCode: '',
        courseId: '', // 课程id
        type: '', // 已做未做
        merchantCode: '', // 门店 code
        // 列表数据
        listKnow: [],
        page: {
          total: 1,
          pageSize: 15,
          pageNum: 1
        }
      };
    },

    onLoad(options) {
      if (options.testQuestions == 1) {
        uni.setNavigationBarTitle({
          title: '已做关卡'
        });
      } else {
        uni.setNavigationBarTitle({
          title: '未做关卡'
        });
      }
      this.studentCode = options.studentCode;
      this.courseId = options.courseId;
      this.type = options.testQuestions;
      this.merchantCode = options.merchantCode;
      this.getStageList();
    },
    onShow() {
      if (uni.getStorageSync('dataUpdated')) {
        this.listKnow = [];
        this.getStageList();
        // 清除本地存储中的标识
        uni.setStorageSync('dataUpdated', false);
      }
    },
    onReachBottom() {
      let allTotal = this.page.pageNum * this.page.pageSize;
      console.log('已加载全部数据');
      if (allTotal < this.page.total) {
        // 当前条数小于总条数，则增加请求页数
        this.page.pageNum++;
        this.getStageList(); // 调用加载数据方法
      } else {
        uni.showToast({
          title: '已加载全部数据',
          icon: 'none'
        });
      }
    },

    methods: {
      gotoKnowledge(item) {
        uni.navigateTo({
          url: `/errorBook/doneTestQuestions?type=${item.type}&checkpointId=${item.checkpointId}&courseId=${item.courseId}&merchantCode=${item.merchantCode}&studentCode=${item.studentCode}`
        });
      },

      // 获取关卡列表数据
      async getStageList() {
        try {
          let res = await this.$httpUser.get('znyy/super-read/wrongBook/stageList', {
            studentCode: this.studentCode,
            merchantCode: this.merchantCode,
            courseId: this.courseId,
            type: this.type,
            pageNum: this.page.pageNum,
            pageSize: this.page.pageSize
          });
          this.page.total = res.data.data.totalItems;
          if (this.page.pageNum === 1) {
            // 如果是第一页，清空列表并添加新数据
            this.listKnow = res.data.data;
            if (this.listKnow.length == 0) {
              uni.navigateBack({
                delta: 1
              });
            }
          } else {
            // 否则追加新数据
            this.listKnow = [...this.listKnow, ...res.data.data];
          }
        } catch (error) {
          console.error('获取数据失败:', error);
        }
      }
    }
  };
</script>

<style>
  .list_box {
    border-radius: 14upx;
    background: #ffffff;
    box-shadow: 0rpx 4rpx 16rpx 0rpx #f5f7f9;
    padding: 30upx;
    font-size: 26upx;
    color: rgba(102, 102, 102, 1);
    position: relative;
  }

  /* 弹层宽度 */
  .uv-dp__container {
    height: 150rpx;
    width: 690rpx;
    margin-left: 30rpx;
  }

  button {
    width: 40% !important;
  }

  .listBorder {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-flow: row wrap;
    border-bottom: 2rpx dashed #efefef;
  }
</style>
