<template>
  <view class="plr-30">
    <view class="pt-30 radius-15 mb-20 search">
      <view class="plr-20 flex">
        <view class="bold">搜索合伙人:</view>
        <view class="search-css pl-25">
          <u-icon name="search" class="box-50 search-image" color="#CFCFCF" size="60"></u-icon>
          <input class="search-input f-28 c-55" v-model="searchValue" type="text" @blur="blur" readonly placeholder="请输入用户名称......" />
        </view>
      </view>
      <scroll-view scroll-y="true" @scrolltolower="scrolltable" class="scrollClass mt-45">
        <uniTable ref="table" :loading="loading" border stripe @selection-change="selectionChange" style="overflow-y: auto">
          <uniTr>
            <uniTh style="color: #000; word-break: break-all">合伙人信息</uniTh>
            <uniTh>到期时间</uniTh>
            <uniTh>累计收益(元)</uniTh>
            <uniTh>推荐时间</uniTh>
          </uniTr>

          <uniTr v-for="(value, index) in listS" :key="index">
            <uniTd>
              {{ value.realName }}
              <br />
              {{ value.merchantCode }}
              <br />
              {{ value.phone }}
            </uniTd>
            <uniTd>
              {{ value.expireDate }}
            </uniTd>
            <uniTd>{{ value.totalAmount }}</uniTd>
            <uniTd>{{ value.applyTime }}</uniTd>
          </uniTr>
        </uniTable>
      </scroll-view>
    </view>
  </view>
</template>

<script>
  const { $http } = require('@/util/methods.js');
  import uniTable from './components/uni-table/components/uni-table/uni-table.vue';
  import uniTd from './components/uni-table/components/uni-td/uni-td.vue';
  import uniTr from './components/uni-table/components/uni-tr/uni-tr.vue';
  import uniTh from './components/uni-table/components/uni-th/uni-th.vue';
  export default {
    components: {
      uniTable,
      uniTr,
      uniTd,
      uniTh
    },
    data() {
      return {
        showSort: false,
        searchValue: '', //搜索框
        payStatus: -1,
        listS: [],
        page: 1,
        avaUrl: 'https://document.dxznjy.com/dxSelect/home_avaUrl.png',
        no_more: false,
        useHeight: 0, //除头部之外高度
        imgHost: getApp().globalData.imgsomeHost,
        totalList: '', // 数据总数
        userId: '', // 当前列表id
        finished: false,
        remark: '',
        showClearIcon: false,
        information: {}, // 当前点击的客户列表
        importShow: false,
        activityList: [],
        userCode: '',
        merchantType: ''
      };
    },
    watch: {},
    onLoad(option) {
      if (option != null) {
        this.userCode = option.userCode;
        this.merchantType = option.merchantType;
      }
    },
    onReady() {
      uni.getSystemInfo({
        success: (res) => {
          // 可使用窗口高度，将px转换rpx
          let h = res.windowHeight * (750 / res.windowWidth);
          this.useHeight = h - 285;
        }
      });
    },
    onShow() {
      this.supermanClublist();
    },
    onHide() {
      this.listS = [];
      this.page = 1;
      this.finished = false;
    },
    methods: {
      // 下级合伙人列表
      async supermanClublist(page) {
        uni.showLoading({
          title: '加载中...'
        });
        let _this = this;
        const res = await $http({
          url: 'zx/user/getSubMerchantList',
          data: {
            subType: 1,
            userCode: this.userCode,
            merchantName: _this.searchValue,
            pageNum: page || 1,
            pageSize: 15,
            merchantType: this.merchantType
          }
        });
        if (res.data.list.length === 0) {
          this.finished = true;
        } else {
          this.listS = [...this.listS, ...res.data.list];
        }
        uni.hideLoading();
      },
      blur(e) {
        this.searchValue = e.value;
        this.listS = [];
        this.page = 1;
        this.finished = false;
        this.supermanClublist();
      },
      scrolltable() {
        if (this.finished) {
          return;
        }
        this.page++;
        this.supermanClublist(this.page);
      }
    }
  };
</script>

<style lang="scss" scoped>
  .scrollClass {
    height: calc(100vh - 200rpx);
  }
  .search {
    position: fixed;
    top: 0;
    width: 690rpx;
  }
  /* 搜索框 */
  .search-css {
    display: flex;
    margin-left: 20rpx;
    flex: 1;
    line-height: 80rpx;
    background-color: #fff;

    .search-image {
      vertical-align: middle;
    }

    .search-input {
      display: inline-block;
      height: 80rpx;
      line-height: 80rpx;
      vertical-align: middle;
    }
  }
  .img_s {
    width: 160rpx;
    height: 160rpx;
  }
  .input {
    border: 1px solid #c8c8c8;
    border-radius: 50rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
</style>
