// 拼音法-趣味复习 公用方法
import dayjs from 'dayjs';
// 倒计时执行事件
export const downFun = (that) => {
  // 重置倒计时时间
  that.showDownTime = that.downTimeCount;
  // 倒计时
  that.countdown = setInterval(() => {
    that.showDownTime--;

    if (that.showDownTime < 0) {
      clearInterval(that.countdown);
      that.countdown = null;
      return;
    }
    // 播放 结束 音效
    if (that.showDownTime < 1) {
      that.$playVoice('game_over.mp3', true, that.timbre, that.pronunciationType, that.playType, that.showData.studentCode);
      that.isSubmit = true; // 重置提交状态
      that.markWrongWord();
    }
  }, 1000);
};
// 计算学习时长
/**
 *
 * @param {*} that
 * @param {*} condition 开始或结束计时 / 1-开始 其他-结束
 * @returns
 */
export const countStudyDuration = (that, condition) => {
  // console.log(that.showData, 'showData999999999999');
  if (condition === 1) {
    // 重置倒计时时间
    that.showData.studyStartTime = dayjs(new Date()).format('YYYY-MM-DD HH:mm:ss'); // 学习开始时间
    console.log('学习开始时间', that.showData.studyStartTime);

    return;
  }
  // 计算学习时长
  const time1 = dayjs(that.showData.studyStartTime);
  const time2 = dayjs(new Date());
  that.showData.studyDuration = time2.diff(time1, 'second');
  console.log(that.showData.studyDuration, '学习时长');

  // 重置倒计时时间
  // if (that.studyDurationTimer == null) {
  //   that.studyDurationTimer = setInterval(() => {
  //     that.showData.studyDuration++;
  //     // console.log('计时器', that.showData.studyDuration);
  //   }, 1000);
  // }
};
/**提交保存数据
 *
 * 1.退出页面时 及 2.查看报告 都需要保存数据
 */
export const submitData = async (that, isOpen = true, isSystemBack) => {
  /**
   * 用户没有答题 或者 已经发送过数据 不需要 保存学习记录
   * that.errorList.length <= 0 && that.successList.length <= 0  // 表示用户没有答题（没有答题成功和答题错误的单词）
   * that.isSended == true // 表示已经发送过数据
   */
  if ((that.errorList.length <= 0 && that.successList.length <= 0) || that.isSended) return uni.navigateBack({ delta: 1 });
  // 加载中...
  setTimeout(() => {
    uni.showLoading({
      title: '正在保存中...',
      mask: true
    });
  }, 0);
  // passStatus 0-未完成 1-已完成
  that.showData.passStatus = that.isEnd ? 1 : 0;
  // 保存学习记录
  let res = await that.$httpUser.post(`znyy/pd/mobile/funReview/studyRecordSave`, that.showData);
  that.isSended = true; // 是否发送
  /**
   * isOpen 是否要打开报告
   * false：说明是退出时调用的 =》返回上一页
   * true：说明是查看报告
   */
  if (isOpen) {
    let queryParams = uni.$u.queryParams({
      studentCode: that.showData.studentCode,
      merchantCode: that.showData.merchantCode,
      passType: that.showData.passType, // 关卡类型： 1-听音识词；2-拼拼乐；3-连连看；4-规则大闯关
      recordId: res.data.data // 记录id
    });
    let result = await that.$httpUser.get(`znyy/pd/mobile/funReview/findFunReviewReport${queryParams}`);
    uni.hideLoading(); // 关闭加载中
    that.reportData = JSON.parse(result.data.data.reportInfo);
    that.showPage = false;
    that.$refs.popopPower.open(); // 查看报告弹框
    that.showData.studyDuration = 0; // 重置学习时长
    return;
  }
  // 没结束说明不是查看报告而是返回上一页
  uni.hideLoading(); // 关闭加载中
  //返回按钮
  if (isSystemBack) {
    return;
  }
  uni.navigateBack();
};
// 获取题目数据
export const getNoLevelData = (that) => {
  // 路由参数
  let queryParams = uni.$u.queryParams({
    studentCode: that.showData.studentCode,
    courseCode: that.showData.courseCode,
    merchantCode: that.showData.merchantCode,
    passType: that.showData.passType, // 关卡类型： 1-听音识词；2-拼拼乐；3-连连看；4-规则大闯关
    studyStatus: that.showData.studyStatus // 学习状态： 0-开始学习；1-继续学习；2-再来一轮
  });
  // 获取复习单词
  that.$httpUser.get(`znyy/pd/mobile/funReview/findCourseWord${queryParams}`).then((res) => {
    if (!res.data.success) {
      uni.navigateBack();
      return;
    }
    if (!res.data.data || res.data.data.length == 0) {
      that.$refs.popopPower.open();
      return;
    }
    that.showListData = res.data.data;
    // 开始计时
    countStudyDuration(that, 1);
    // 保存当前关卡信息 - 拿到未学单词的索引
    that.showData.pdFunReviewStudyWordSaveDtoList = []; // 保存当前关卡信息
    that.showListData.forEach((item, index) => {
      // 拿到未学单词的索引
      if (item.studyStatus == 0 && that.qIdIndex === null) that.qIdIndex = index;
      /**
       * 保存当前关卡信息
       * word: 单词
       * wordAudioUrl: 单词音频
       * studyStatus: 学习状态：0-未学习；1-已学习
       *  */
      that.showData.pdFunReviewStudyWordSaveDtoList.push({
        word: item.wordSyllable,
        wordAudioUrl: JSON.stringify({ word: item.wordSyllableAudioUrl, list: item.splitList }),
        // wordAudioUrl: [item.wordSyllableAudioUrl, ...item.splitList],
        studyStatus: item.studyStatus,
        answerStatus: item.answerStatus || 0
      });
    });
    if (that.qIdIndex === null) return that.$refs.popopPower.open();
    // 对答案进行操作
    that.wordExecute();
  });
};
// 获取当前学员设置的语音版本
export const getWordversion = (that) => {
  that.$httpUser
    .get('znyy/course/info', {
      studentCode: that.showData.studentCode
    })
    .then((res) => {
      if (res.data.success) {
        //  console.log(res.data.data)
        let name = res.data.data.voiceModel.split('#');
        that.pronunciationType = name[1];
        that.timbre = name[2];
        that.playType = name[0];
      } else {
        that.$util.alter(res.data.message);
      }
    });
};
// 获取系统信息
export const getHeight = (that) => {
  // 获取系统信息
  const systemInfo = uni.getSystemInfoSync();

  // 获取屏幕高度
  that.screenHeight = systemInfo.windowHeight * 2;
  that.screenWidth = systemInfo.windowWidth * 2;
  // // 打印屏幕高度
  //    console.log(that.screenHeight,"打印屏幕高度");
};
var innerAudioContext; // 音频对象
// 初始化音频对象
innerAudioContext = uni.createInnerAudioContext(); // 音频对象
//播放音频
export const playWord = (that, word, wordList) => {
  if (!word) return that.$util.alter('该单词还没有音频哦');
  // #ifdef MP-WEIXIN
  innerAudioContext.obeyMuteSwitch = false;
  // #endif
  resetAudioContext(true); // 销毁重置
  let currentIndex = 0; // 重置当前播放的音频索引
  // 判断是否有空的音频
  const hasEmptyAudioUrl = () => {
    let hasEmpty = false;
    wordList.forEach((item) => {
      if (!item.wordSyllableAudioUrl || item.wordSyllableAudioUrl.length === 0) {
        uni.showToast({
          title: '该单词无音频哦',
          icon: 'error',
          duration: 2000
        });
        hasEmpty = true;
        return;
      }
    });
    return hasEmpty;
  };
  // 播放下一个音频
  const playNextAudio = () => {
    if (hasEmptyAudioUrl()) {
      resetAudioContext(); // 先重置音频上下文，避免事件冲突
      return;
    }
    resetAudioContext(); // 先重置音频上下文，避免事件冲突
    if (!that.isPlaying) that.isPlaying = true; // 是否还在播放音频/true开始播放
    innerAudioContext.onCanplay(() => {
      innerAudioContext.play();
    });
    if (currentIndex < wordList.length) {
      // console.log('音频播放111', innerAudioContext.src);
      //监听音频进入可以播放状态的事件

      innerAudioContext.src = wordList[currentIndex].wordSyllableAudioUrl;

      innerAudioContext.onEnded(() => {
        currentIndex++;
        playNextAudio(); // 播放下一个音频
      });

      // 触发一次以检查音频是否可播放
      // innerAudioContext.play();
    } else {
      console.log(currentIndex, word);
      // 播放 word 的音频
      innerAudioContext.src = word;

      innerAudioContext.onEnded(() => {
        resetAudioContext();
        currentIndex = 0;
        that.isPlaying = false; // 是否还在播放音频/false结束播放
      });
    }
  };
  innerAudioContext.onError((res) => {
    console.error('音频播放错误', res);
  });
  // 开始播放
  playNextAudio();
};
//播放音频
// playWord(word, wordList) {
//     playWord(this, word, wordList);
//   },
// 重置音频上下文 在每次播放之前先解绑所有事件
export const resetAudioContext = (reset = false) => {
  innerAudioContext.stop();
  // 是否销毁重置
  if (reset) {
    innerAudioContext.destroy(); // 必须销毁
    innerAudioContext = uni.createInnerAudioContext();
  }
  innerAudioContext.offEnded();
  innerAudioContext.offCanplay();
};

// 返回
/**
 *
 * @param {*} that
 * @param {*} type 是否是 普通退出提醒 /1-是 2-否
 * @returns
 */
export const beforeleave = (that, type = 1) => {
  if (that.showPage) {
    resetAudioContext();
    that.showPage = false;
    if (type === 1) {
      that.$refs.popopPowerExit.open();
    } else {
      that.isEnd = true;
      that.$refs.popopPowerIsContinue.open();
    }
    return false;
  }
  console.log('no showPage');

  that.$nextTick(() => {
    setTimeout(() => {
      uni.navigateBack({ delta: 1 });
    }, 300);
  });
};
// 重置
/**
 * 重置
 * @param {*} that // this
 * @param {Number} passType // 关卡类型： 1-听音识词；2-拼拼乐；3-连连看；4-规则大闯关
 */
export const handleReset = (that, passType) => {
  if (passType == 1) {
    that.chooseAnswerIndex = -1; // 当前选中答案索引
    that.qIdIndex = null; // 当前题目索引
  }
  if (passType == 2) {
    that.qIdIndex = null; // 当前题目索引
    that.old.scrollTop = 0; // 重置滚动距离
    that.old.scrollTop2 = 0; // 重置滚动距离
  }
  if (passType == 3) {
    that.remainListData = []; // 本轮剩余题目数据
    that.pageNumber = 1; // 当前页数
  }

  that.showPage = true; // 打开返回保护
  that.errorList = []; // 错误单词
  that.successList = []; // 正确单词
  that.showListData = []; // 当前题目数据
  that.isLoading = false; // 加载中
  that.isEnd = false; // 是否结束
  that.isSended = false; // 未提交
};
