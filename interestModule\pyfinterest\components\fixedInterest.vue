<template>
  <view class="container" :style="{ height: windowHeight + 'rpx' }">
    <view class="word">
      <view style="width: 144rpx; height: 144rpx; margin-right: 10rpx">
        <image src="https://document.dxznjy.com/course/ae33333815ed4628a04f1271d58e13dd.png" style="width: 100%; height: 100%"></image>
      </view>
      <view class="wordTitle">
        {{ word.wordSyllable }}
      </view>
    </view>
    <!-- 拖拽盒子 -->
    <view
      class="drag-box"
      :style="{
        left: dragBox.x + 'px',
        top: dragBox.y + 'px',
        borderColor: isDragging ? '#4CAF50' : '#2196F3'
      }"
      @touchstart="onDragStart"
      @touchmove="onDragMove"
      @touchend="onDragEnd"
    >
      {{ longTypeList[0] }}
    </view>
    <view
      class="drag-box"
      :style="{
        left: dragBox2.x + 'px',
        top: dragBox2.y + 'px',
        borderColor: isDragging2 ? '#4CAF50' : '#2196F3'
      }"
      @touchstart="onDragStart2"
      @touchmove="onDragMove2"
      @touchend="onDragEnd2"
    >
      {{ longTypeList[1] }}
    </view>
    <!-- 静态盒子 -->
    <view class="static-box">
      <view v-for="(item, index) in list" :key="index" style="margin-right: 20rpx" :class="item.isStress ? '' : 'staticitem'">
        {{ item.isStress ? '' : item.text.replace(/\[.*\]$/, '') }}
        <view v-if="item.isStress" style="display: flex">
          <view
            v-for="(box, i) in item.list"
            class="static-boxs"
            :id="'box' + index + '' + i"
            :class="box.class"
            @click="checkOut(index, i, box)"
            :style="{
              backgroundColor: isColliding(index, i) || isColliding2(index, i) ? '#ff0000' : ''
            }"
          >
            <view class="small" v-if="box.check == 1">{{ longTypeList[0] }}</view>
            <view class="small" v-if="box.check == 2">{{ longTypeList[1] }}</view>
            {{ box.text.replace(/\[.*\]$/, '') }}
          </view>
        </view>
      </view>
    </view>
    <view class="rightItems" v-if="isOK">
      <view
        v-for="(item, index) in list"
        :class="item.isStress ? '' : 'rightItem'"
        :style="{
          color: item.isStress ? '' : '#c9c9c9'
        }"
      >
        {{ item.isStress ? '' : item.text.replace(/\[.*\]$/, '') }}
        <view v-if="item.isStress" style="display: flex; flex-wrap: wrap">
          <view
            v-for="(box, i) in item.list"
            class="rightItem"
            :class="box.text === item.LongWord ? 'right' : ''"
            :style="{
              backgroundColor: isColliding(index, i) || isColliding2(index, i) ? '#ff0000' : ''
            }"
          >
            <view class="small" v-if="box.text === item.LongWord">{{ longTypeList[0] }}</view>
            {{ box.text.replace(/\[.*\]$/, '') }}
          </view>
        </view>
      </view>
    </view>
    <view class="btn" @click="btnOK">{{ isOK ? (end ? '提交' : '下一题') : '确定' }}</view>
    <!-- 引导 -->
    <uni-popup ref="guideOne" type="bottom" :maskClick="true" :classBG="''">
      <view style="position: relative" class="test-bg">
        <image mode="aspectFit" style="width: 100%; height: 100%" src="https://document.dxznjy.com/course/dae3ae3ddb9c47198f837c9bdfa5fdc2.png"></image>
        <image class="guide_btn_next" @click="guideNext(1)" src="https://document.dxznjy.com/applet/newInteresting/interesting_guide_next.png"></image>
      </view>
    </uni-popup>
    <uni-popup ref="guideTwo" type="bottom" :maskClick="true" :classBG="''">
      <view style="position: relative" class="test-bg">
        <image mode="aspectFit" style="width: 100%; height: 100%" src="https://document.dxznjy.com/dxSelect/interest/zhongchangyin2.png"></image>
        <image class="guide_btn_next" @click="guideNext(2)" src="https://document.dxznjy.com/applet/newInteresting/interesting_guide_next.png"></image>
      </view>
    </uni-popup>
    <uni-popup ref="guideEnd" type="bottom" :maskClick="true" :classBG="''">
      <view style="position: relative" class="test-bg">
        <image @click="guideClose()" mode="aspectFit" style="width: 100%; height: 100%" src="https://document.dxznjy.com/dxSelect/interest/zhongchangyin.png"></image>
      </view>
    </uni-popup>
  </view>
</template>

<script>
  export default {
    data() {
      return {
        status: 0,
        longTypeList: [],
        dragBox: { x: 109, y: 400, width: 52, height: 47 },
        dragBox2: { x: 216, y: 400, width: 52, height: 47 },
        isDragging: false,
        isDragging2: false,
        startX: 0,
        startY: 0,
        startX2: 0,
        startY2: 0,
        windowWidth: 0,
        windowHeight: 0,
        screenHeight: 0,
        screenWidth: 0,
        isOK: false,

        Guide: uni.getStorageSync('fixedGuide'),
        list: [],
      };
    },
    props: {
      word: {
        type: Object,
        default: {}
      },
      end: {
        type: Boolean,
        default: false
      },
      count: {
        type: Number,
      },
    },
    // onReady() {
    //   let that = this;
    //   uni.getSystemInfo({
    //     success: (res) => {
    //       this.screenHeight = res.windowHeight * 2;
    //       this.screenWidth = res.windowWidth * 2;
    //       that.windowWidth = res.windowWidth;
    //       that.windowHeight = res.windowHeight * (750 / res.windowWidth) - 100;
    //       // 可使用窗口高度，将px转换rpx
    //     }
    //   });
    //   this.list.forEach((e, i) => {
    //     if (e.isStress) {
    //       e.list.forEach((o, index) => {
    //         this.setarea(i, index);
    //       });
    //     }
    //   });
  // },
    watch: {
      count: {
        handler(newVal) {
          this.$emit('setTitle', 5);
          this.list.forEach((e, i) => {
            if (e.isStress) {
              e.list.forEach((o, index) => {
                this.setarea(i, index);
              });
            }
          });
          this.isOK = false;
          this.init();
          this.$emit('goNum', 6);
        },
        immediate: true,
      },
    },
    created() {
      this.$emit('setTitle', 5);
       this.list.forEach((e, i) => {
        if (e.isStress) {
          e.list.forEach((o, index) => {
            this.setarea(i, index);
          });
        }
      });
      this.init();
      this.$emit('goNum', 6);
    },
    mounted() {
      let that = this;
      uni.getSystemInfo({
        success: (res) => {
          this.screenHeight = res.windowHeight * 2;
          this.screenWidth = res.windowWidth * 2;
          that.windowWidth = res.windowWidth;
          that.windowHeight = res.windowHeight * (750 / res.windowWidth) - 100;
          // 可使用窗口高度，将px转换rpx
        }
      });
     

      if (!this.Guide) {
        this.$refs.guideOne.open();
      } else if (this.Guide == 1) {
        this.$refs.guideTwo.open();
      } else if (this.Guide == 2) {
        this.$refs.guideEnd.open();
      }
    },
    methods: {
      guideNext(e) {
        uni.setStorageSync('fixedGuide', e);
        if (e == 1) {
          this.$refs.guideOne.close();
          this.$refs.guideTwo.open();
        } else {
          this.$refs.guideTwo.close();
          this.$refs.guideEnd.open();
        }
      },
      guideClose() {
        uni.setStorageSync('fixedGuide', 3);
        this.$refs.guideEnd.close();
      },
      checkOut(index, i, e) {
        if (this.isOK) return;
        if (e.check) {
          this.list[index].list[i].check = 0;
          this.list[index].list[i].type = 0;
          this.list[index].list[i].class = '';
          this.$forceUpdate();
        }
      },
      addSuffix(arr) {
        const countMap = {};

        // 遍历数组，标记重复元素
        return arr.map((item, index) => {
          // 更新当前元素的出现次数
          countMap[item] = (countMap[item] || 0) + 1;

          // 如果当前元素是重复的（出现次数大于1），则标记次数
          if (countMap[item] > 1) {
            return `${item}[${countMap[item]}]`;
          }
          // 否则直接返回元素
          return item;
        });
      },

      addSuffixToRepeatedSyllables(objects) {
        // 创建一个对象来跟踪每个syllable出现的次数
        const syllableCounts = {};
        return objects.map((obj, index) => {
          const wordSyllable = obj.wordSyllable;
          // 如果syllable已经出现过，则增加计数并添加后缀
          if (syllableCounts[wordSyllable]) {
            syllableCounts[wordSyllable]++;
            return {
              ...obj,
              wordSyllable: `${wordSyllable}[${syllableCounts[wordSyllable]}]`
            };
          } else {
            // 如果是第一次出现，则记录该syllable并设置计数为1
            syllableCounts[wordSyllable] = 1;
            return obj;
          }
        });
      },
      btnOK() {
        if (this.isOK) {
          this.$emit('next', this.status, 6);
        } else {
          let a = true;
          this.list.forEach((e) => {
            if (e.isStress) {
              if (e.list.find((o) => o.type == 1)) {
                a = false;
              }
            }
          });
          if (a)
            return uni.showToast({
              icon: 'none',
              title: '请选择'
            });

          this.isOK = true;
          this.list.forEach((e, i) => {
            if (e.isStress) {
              e.list.forEach((o, p) => {
                if (o.check) {
                  if (o.check === 1 && o.text === e.LongWord) {
                    this.list[i].list[p].class = 'right';
                  } else {
                    this.list[i].list[p].class = 'error';
                  }
                }
                if (o.text === e.LongWord && !o.check) {
                  this.list[i].list[p].class = 'error';
                  this.list[i].list[p].check = 1;
                }
              });
            }
          });
          this.$forceUpdate();
          let temp = true;
          this.list.forEach((e) => {
            if (e.isStress) {
              e.list.forEach((u) => {
                if (u.class == 'error') {
                  temp = false;
                }
              });
            }
          });
          this.status = temp ? 1 : 2;
        }
      },
      init() {
        let arr = this.addSuffixToRepeatedSyllables(this.word.splitList);
        this.list = arr.map((e, i) => {
          return {
            text: e.wordSyllable
          };
        });
        this.word.syllableList.forEach((e) => {
          // this.list.find((o) => o.text === e.wordSyllable).isSyllable = true;
          if (e.wordSyllableType == 3 && e.syllableList.length) {
            this.longTypeList = this.setLangType(e.syllableList[0].wordSyllableType);
            console.log(this.longTypeList);
            console.log(e.syllableList[0].wordSyllableType);
            this.list.find((o) => o.text === e.wordSyllable).isStress = true;
            if (e.syllableList) this.list.find((o) => o.text === e.wordSyllable).list = this.addSuffix(e.wordSyllable.replace(/\[.*\]$/, '').split(''));
            this.list.find((o) => o.text === e.wordSyllable).LongWord = e.syllableList[0].wordSyllable.match(/\(([^)]+)\)/)[1];
          }
        });
        this.$forceUpdate();

        this.list.forEach((e, i) => {
          if (e.isStress) {
            this.list[i].list = e.list.map((p) => {
              return {
                text: p,
                type: 0
              };
            });
          }
        });
        console.log(this.list);
      },
      setLangType(e) {
        let a = e - 0;
        let array = [
          ['a-e', 'a'],
          ['i-e', 'i'],
          ['e-e', 'e'],
          ['o-e', 'o'],
          ['u-e', 'u']
        ];
        switch (a) {
          case 6:
            return array[0];
          case 9:
            return array[1];
          case 10:
            return array[2];
          case 11:
            return array[3];
          default:
            return array[4];
        }
      },
      setarea(e, i) {
        let that = this;
        setTimeout(() => {
          const query = uni.createSelectorQuery().in(this);
          query
            .select('#box' + e + '' + i)
            .boundingClientRect((data) => {
              // let res = JSON.parse(data);
              that.list[e].list[i].x = data.left;
              that.list[e].list[i].y = data.top - 88;
              that.list[e].list[i].width = data.width;
              that.list[e].list[i].height = data.height;
            })
            .exec();
        }, 50);
      },
      onDragStart(e) {
        this.isDragging = true;
        this.startX = e.touches[0].clientX;
        this.startY = e.touches[0].clientY;
      },
      onDragMove(e) {
        if (!this.isDragging || this.isOK) return;
        const deltaX = e.touches[0].clientX - this.startX;
        const deltaY = e.touches[0].clientY - this.startY;
        this.dragBox.x += deltaX;
        this.dragBox.y += deltaY;
        this.startX = e.touches[0].clientX;
        this.startY = e.touches[0].clientY;
        // 限制边界
        this.dragBox.x = Math.max(0, Math.min(this.dragBox.x, this.windowWidth - this.dragBox.width));
        this.dragBox.y = Math.max(0, Math.min(this.dragBox.y, this.windowHeight - this.dragBox.height));
      },
      onDragEnd() {
        if (this.isOK) return;
        this.isDragging = false;
        var temp;
        this.list.forEach((e, i) => {
          if (e.isStress) {
            e.list.forEach((p, u) => {
              if (this.isColliding(i, u)) {
                p.check = 1;
                p.type = 1;
                p.class = 'check';
              }
            });
          }
        });
        setTimeout(() => {
          this.dragBox = { x: 109, y: 400, width: 52, height: 47 };
        }, 200);
      },
      isColliding(index, i) {
        const dragBox = this.dragBox;
        const box = this.list[index].list[i];
        const dragBoxCenterX = dragBox.x + dragBox.width / 2;
        const dragBoxCenterY = dragBox.y + dragBox.height / 2;
        // 判断中心点是否在目标盒子内
        return dragBoxCenterX >= box.x && dragBoxCenterX <= box.x + box.width && dragBoxCenterY >= box.y && dragBoxCenterY <= box.y + box.height;
      },
      onDragStart2(e) {
        this.isDragging2 = true;
        this.startX2 = e.touches[0].clientX;
        this.startY2 = e.touches[0].clientY;
      },
      onDragMove2(e) {
        if (!this.isDragging2 || this.isOK) return;
        const deltaX = e.touches[0].clientX - this.startX2;
        const deltaY = e.touches[0].clientY - this.startY2;
        this.dragBox2.x += deltaX;
        this.dragBox2.y += deltaY;
        this.startX2 = e.touches[0].clientX;
        this.startY2 = e.touches[0].clientY;
        // 限制边界
        this.dragBox2.x = Math.max(0, Math.min(this.dragBox2.x, this.windowWidth - this.dragBox2.width));
        this.dragBox2.y = Math.max(0, Math.min(this.dragBox2.y, this.windowHeight - this.dragBox2.height));
      },
      onDragEnd2() {
        if (this.isOK) return;
        this.isDragging2 = false;
        var temp;
        this.list.forEach((e, i) => {
          if (e.isStress) {
            e.list.forEach((p, u) => {
              if (this.isColliding2(i, u)) {
                p.check = 2;
                p.type = 1;
                p.class = 'check';
              }
            });
          }
        });
        setTimeout(() => {
          this.dragBox2 = { x: 216, y: 400, width: 52, height: 47 };
        }, 200);
      },
      isColliding2(index, i) {
        const dragBox = this.dragBox2;
        const box = this.list[index].list[i];
        const dragBoxCenterX = dragBox.x + dragBox.width / 2;
        const dragBoxCenterY = dragBox.y + dragBox.height / 2;
        // 判断中心点是否在目标盒子内
        return dragBoxCenterX >= box.x && dragBoxCenterX <= box.x + box.width && dragBoxCenterY >= box.y && dragBoxCenterY <= box.y + box.height;
      }
    }
  };
</script>

<style lang="scss" scoped>
  .test-bg {
    padding-top: env(safe-area-inset-top);
    // padding-bottom: env(safe-area-inset-bottom);
    height: 100vh;
    box-sizing: border-box;
  }
  .guide_btn_next {
    position: absolute;
    bottom: 57rpx;
    right: 64rpx;
    width: 269rpx;
    height: 142rpx;
  }

  .guide_btn_close {
    position: absolute;
    bottom: 57rpx;
    right: 64rpx;
    width: 269rpx;
    height: 142rpx;
  }
  .btn {
    width: 632rpx;
    height: 84rpx;
    background: #89c844;
    box-shadow: 0 16rpx 2rpx -2rpx rgba(99, 180, 11, 1);
    border-radius: 42rpx;
    text-align: center;
    line-height: 84rpx;
    position: fixed;
    bottom: 50rpx;
    left: 50%;
    transform: translateX(-50%);
  }
  .word {
    margin-top: 60rpx;
    height: 144rpx;
    padding-left: 32rpx;
    display: flex;
    align-items: center;
    margin-bottom: 90rpx;
  }
  .wordTitle {
    width: 474rpx;
    height: 110rpx;
    padding-left: 14rpx;
    background: url('https://document.dxznjy.com/course/a37e49a58c724f7ca8d2fbd8ce85252d.png') no-repeat;
    background-size: contain;
    text-align: center;
    line-height: 100rpx;
    font-size: 40rpx;
    color: #555555;
  }
  .rightItems {
    position: fixed;
    width: 686rpx;
    max-height: 286rpx;
    overflow-y: auto;
    bottom: 200rpx;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    align-items: center;
    border-radius: 32rpx;
    padding: 20rpx 0;
    background-color: rgba(255, 255, 255, 0.51);
    .nocheck {
      color: #c9c9c9;
    }
    .rightItem {
      position: relative;
      background-color: #fff;
      color: #555555;
      font-size: 36rpx;
      height: 92rpx;
      line-height: 92rpx;
      margin-right: 8rpx;
      border-radius: 16rpx;
      margin-bottom: 10rpx;
      padding: 0 40rpx;
    }

    .right {
      background-color: #4bb051;
      color: #ffffff;
    }
  }
  .container {
    overflow: hidden;
    position: relative;
    width: 100vw;

    /* height: 100vh; */
  }
  .small {
    position: absolute;
    height: 30rpx;
    width: 100%;
    top: 4rpx;
    right: 0;
    line-height: 1;
    padding-right: 8rpx;
    text-align: right;
    font-size: 20rpx;
    color: #555;
  }
  .drag-box {
    position: absolute;
    height: 92rpx;
    width: 102rpx;
    box-sizing: border-box;
    text-align: center;
    line-height: 92rpx;
    border-radius: 24rpx;
    font-size: 36rpx;
    color: #555;
    background-color: #ffffff;
    box-shadow: 0 16rpx 2rpx -2rpx rgba(97, 170, 244, 1);
    // opacity: 0.8;
    z-index: 2;
  }

  .static-box {
    /* height: 80rpx; */
    margin: 40rpx;
    transition: background-color 0.3s;
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    .right {
      background-color: #4bb051 !important;
      box-shadow: 0 16rpx 2rpx -2rpx rgba(57, 141, 61, 1);
      color: #ffffff !important;
    }
  }
  .staticitem {
    position: relative;
    height: 92.11rpx;
    min-width: 100rpx;
    padding: 0 30rpx;
    font-size: 36rpx;
    box-sizing: border-box;
    // border: 2rpx solid #000;
    margin-right: 10rpx;
    text-align: center;
    background-color: #ffffff;
    color: #c9c9c9;
    border-radius: 24rpx;
    line-height: 90rpx;
    box-shadow: 0 16rpx 2rpx -2rpx rgba(97, 170, 244, 1);
    margin-bottom: 32rpx;
  }
  .static-boxs {
    position: relative;
    height: 92.11rpx;
    min-width: 100rpx;
    padding: 0 30rpx;
    box-sizing: border-box;
    border: 2rpx solid transparent;
    font-size: 36rpx;
    // margin-right: 18rpx;
    text-align: center;
    background-color: #ffffff;
    color: #555;
    border-radius: 24rpx;
    line-height: 90rpx;
    box-shadow: 0 16rpx 2rpx -2rpx rgba(97, 170, 244, 1);
    margin-bottom: 32rpx;
  }
  .check {
    border: 2rpx solid #ffc800;
    background-color: #fff7db;
  }

  .error {
    background-color: #ffa332 !important;
    box-shadow: 0 16rpx 2rpx -2rpx rgba(231, 133, 13, 1) !important;
    color: #ffffff !important;
  }
</style>
