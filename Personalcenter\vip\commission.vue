<template>
  <view>
    <u-sticky>
      <view class="flex-dir-row bg-f3 flex-x-s flex-y-s tabs">
        <view class="flex-col col-3" @tap="tab(-1)">
          <text :class="tabindex == -1 ? 'active' : 'unchecked'">全部</text>
          <view v-if="tabindex == -1" class="changing-over mt-15"></view>
        </view>
        <view class="flex-col col-3" @tap="tab(0)">
          <text :class="tabindex == 0 ? 'active' : 'unchecked'">未结算</text>
          <view v-if="tabindex == 0" class="changing-over mt-15"></view>
        </view>
        <view class="flex-col col-3" @tap="tab(1)">
          <text :class="tabindex == 1 ? 'active' : 'unchecked'">已结算</text>
          <view v-if="tabindex == 1" class="changing-over mt-15"></view>
        </view>
      </view>
    </u-sticky>
    <view class="mlr-30" v-for="(item, index) in listS.list" :key="index">
      <view class="plr-30 bg-ff mb-20 radius-20 mt-30">
        <view class="ptb-25 flex order_b">
          <view class="f-30 c-33">订单编号：{{ item.orderNo }}</view>
          <text class="f-26 t-c c-ff unsettled" v-if="item.isOver == 0">未结算</text>
          <text class="f-26 t-c c-ff settled" v-if="item.isOver == 1">已结算</text>
        </view>
        <view class="flex-dir-row pt-40 pb-40 order-b">
          <view class="box-140 radius-50">
            <image :src="item.headPortrait || avaUrl" class="wh100"></image>
          </view>
          <view class="flex-box ml-20 mt-15">
            <view class="flex">
              <view class="f-32">{{ item.nickName || '昵称' }}</view>
              <text class="color_tangerine f-32 bold">+{{ item.amount }}</text>
            </view>
            <view class="mt-30">
              <text class="f-30 c-66 overstepSingle">商品名称：{{ item.courseName }}</text>
            </view>
          </view>
        </view>

        <view class="flex-s ptb-25">
          <view class="">
            <text class="f-30 c-66">消费金额：¥{{ item.orderPrice }}</text>
          </view>
          <view class="">
            <text class="f-28 c-fea" v-if="item.isRefund == 1">已退款：￥{{ item.refundAmount }}</text>
          </view>
        </view>
      </view>
    </view>
    <view v-if="listS.list != undefined && listS.list.length == 0" class="t-c flex-col" :style="{ height: useHeight + 'rpx' }">
      <image :src="imgHost + 'alading/correcting/no_data.png'" class="mb-20 img_s" mode="widthFix"></image>
      <view style="color: #bdbdbd">暂无数据</view>
    </view>
    <view v-if="no_more">
      <u-divider text="到底了"></u-divider>
    </view>
  </view>
</template>

<script>
  import Util from '@/util/util.js';
  const { $http } = require('@/util/methods.js');
  export default {
    data() {
      return {
        tabindex: -1,
        listS: {},
        page: 1,
        no_more: false,
        useHeight: 0, //除头部之外高度
        avaUrl: Util.getCachedPic('https://document.dxznjy.com/dxSelect/home_avaUrl.png', 'home_avaUrl_path'),
        imgHost: getApp().globalData.imgsomeHost,
        type: 1 // 1学习超人 2俱乐部
      };
    },
    onLoad(e) {
      this.type = e.type;
    },
    onReady() {
      uni.getSystemInfo({
        success: (res) => {
          // 可使用窗口高度，将px转换rpx
          let h = res.windowHeight * (750 / res.windowWidth);
          this.useHeight = h - 95;
        }
      });
    },
    onShow() {
      this.list();
    },
    onReachBottom() {
      if (this.page >= this.listS.totalPage) {
        this.no_more = true;
        return false;
      }
      this.list(true, ++this.page);
    },
    methods: {
      tab(e) {
        this.tabindex = e;
        this.page = 1;
        this.no_more = false;
        this.list();
      },
      async list(isPage, page) {
        let _this = this;
        const res = await $http({
          url: 'zx/user/commissionRecord',
          data: {
            type: _this.type,
            isOver: _this.tabindex,
            page: page || 1
          }
        });
        if (res) {
          if (isPage) {
            let old = _this.listS.list;
            _this.listS.list = [...old, ...res.data.list];
          } else {
            _this.listS = res.data;
          }
        }
      }
    }
  };
</script>

<style lang="scss" scoped>
  .changing-over {
    background-color: #2e896f;
    width: 30rpx;
    height: 4rpx;
  }
  .tabs {
    // padding: 30rpx 0;
    width: 100%;
  }

  .tabs text {
    font-size: 30upx;
    color: #666;
  }

  .tabs .active {
    color: #000;
    font-size: 34rpx;
    font-weight: bold;
  }

  .tabs .unchecked {
    color: #666;
    font-size: 32rpx;
  }

  .settled {
    background-color: #2dc032;
    width: 90rpx;
    height: 36rpx;
    padding: 2rpx;
    border-radius: 4rpx;
    line-height: 36rpx;
  }

  .unsettled {
    background-color: #c6c6c6;
    width: 90rpx;
    height: 36rpx;
    padding: 2rpx;
    border-radius: 4rpx;
    line-height: 36rpx;
  }

  .order_b {
    border-bottom: 0.5px dashed #eee;
  }

  .order-b {
    border-bottom: 0.5px solid #eee;
  }

  .img_s {
    width: 160rpx;
  }
</style>
