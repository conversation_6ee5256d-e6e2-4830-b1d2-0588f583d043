<!-- 学习打印页面 -->
<template>
  <view class="study_print_page">
    <view class="bg_box"></view>
    <!-- 学员选择框 -->
    <view class="list_box">
      <view class="student_pick row_flex">
        <view>学员：</view>
        <view class="pick">
          <picker class="btnchange" @change="bindPickerStudent" :value="studentIndex" :range="studentArray"
            range-key="realName" name="grade">
            <view>{{ studentArray[studentIndex].realName }}</view>
          </picker>
          <image src="../../static/index/arrow.png" class="jiantou arrow" mode="widthFix"></image>
        </view>
      </view>
    </view>
    <!-- 学员选择框 -->

    <!-- 下部内容 -->
    <view v-if="detectionList.length > 0">
      <view class="subject" v-for="(item, index) in detectionList" :key="index">
        <view class="subcard" @click="reportFn(item)">
          <view class="title">
            <text>姓名：{{ item.realName || '' }}</text>
            <image src="../../static/index/arrow.png" class="jiantou arrow" mode="widthFix"></image>
          </view>
          <view class="title">
            <text>评测日期：{{ item.endTime || '' }}</text>
          </view>
          <view class="title">
            <text>能力水平：{{ item.title || '' }}</text>
          </view>
        </view>
      </view>
    </view>

    <view v-else class="t-c flex-col mt-30 bg-ff radius-15 mlr-30" :style="{ height: useHeight + 'rpx' }">
      <image src="https://document.dxznjy.com/alading/correcting/no_data.png" class="mb-20" style="width: 160rpx"
        mode="widthFix"></image>
      <view style="color: #bdbdbd">暂无数据</view>
    </view>
  </view>
</template>

<script>
  import {
    uniqueObjectsByPropertyMap
  } from '@/util/util.js';
  export default {
    data() {
      return {
        studentCode: '',
        studentIndex: 0,
        studentArray: [{
          studentCode: '',
          realName: '请选择学员'
        }],
        detectionList: [], // 检测列表
        pageNum: 1, // 当前页
        pageSize: 20, // 页数
        loadingType: 'nodata', //加载更多状态
        urlId: '', //跳转带过去的当前id

        useHeight: 0, //除头部之外高度
        app: 0
      };
    },
    onReady() {
      uni.getSystemInfo({
        success: (res) => {
          // 可使用窗口高度，将px转换rpx
          let h = res.windowHeight * (750 / res.windowWidth);
          this.useHeight = h - 560;
        }
      });
    },
    onLoad(option) {
      var that = this;
      // #ifdef APP-PLUS
      if (option.token) {
        that.app = option.app;
        that.$handleTokenFormNative(option);
        that.studentCode = option.studentCode;
        that.getStudentCode();
      }
      // #endif
      // #ifdef MP-WEIXIN
      if (option != null && option.studentCode != undefined) {
        that.studentCode = option.studentCode;
      } else {
        that.getStudentCode();
      }
      // #endif
    },

    onShow() {
      // 	this.getDataList();
    },

    //加载更多
    onReachBottom() {
      if (this.studentCode != '') {
        this.pageNum++;
        // this.getDataList();
      }
    },
    watch: {
      studentCode(val) {
        if (val != '') {
          this.pageNum = 1;
          this.pageSize = 20;
          this.getDataList(true);
        }
      }
    },
    onUnload() {
      // #ifdef APP-PLUS
      if (this.app) {
        plus.runtime.quit();
      }
      // #endif
    },
    methods: {
      // 选择学员code
      bindPickerStudent(e) {
        this.studentIndex = e.target.value;
        this.studentCode = this.studentArray[this.studentIndex].studentCode;
        this.detectionList = [];

        this.getDataList();
      },
      // 查询studentCode
      async getStudentCode() {
        let result = await this.$httpUser.get('znyy/review/query/my/student');
        if (result != undefined && result != '') {
          if (result.data.data != null) {
            this.studentArray = [{
              studentCode: '',
              realName: '选择学员'
            }];
            if (result.data.data.length > 0) {
              if (result.data.data.length == 1) {
                this.studentArray = [];
                this.index = 0;
                this.studentArray = this.studentArray.concat(result.data.data);
                this.studentCode = this.studentArray[this.index].studentCode;
              } else {
                var that = this;
                that.studentArray = that.studentArray.concat(result.data.data);
              }
            }

            // #ifdef APP-PLUS
            for (let i = 0; i < this.studentArray.length; i++) {
              if (this.studentArray[i].studentCode === this.studentCode) {
                this.studentIndex = i;
                this.studentStyle = true;
                break;
              }
            }
            // #endif
          }
        }
      },
      // 获取学员信息内容
      async getDataList(change) {
        let that = this;
        if (that.studentCode == '') {
          that.detectionList = [];
          return;
        }
        // let res2 = await that.$httpUser.get('znyy/deliver/student/coursePage/' + this.pageNum + '/' + this
        let result = await that.$httpUser
          .get('znyy/areas/student/test/result/studentWordTestReport/' + this.pageNum + '/' + this.pageSize, {
            studentCode: this.studentCode,
            loginName: '',
            realName: '',
            studyRank: '',
            pageNum: that.pageNum,
            pageSize: that.pageSize
          })
          .then((result) => {
            if (result) {
              console.log(that.detectionList, 'that.detectionList');
              if (result.data.data.data.length == 0) {
                that.loadingType = 'nodata';
                uni.showToast({
                  icon: 'none',
                  title: '暂无更多内容了！',
                  duration: 2000
                });
                that.detectionList = [];
              } else {
                if (that.detectionList.length == 0) {
                  that.detectionList = result.data.data.data;
                } else {
                  that.detectionList = that.detectionList.concat(result.data.data.data);
                  that.detectionList = uniqueObjectsByPropertyMap(that.detectionList, 'id');
                }
                that.loadingType = that.pageNum >= Number(result.data.data.totalPage) ? 'nomore' : 'more';
              }
            }
          });
      },

      // 跳转到报告页面
      reportFn(item) {
        uni.navigateTo({
          url: '/parentEnd/report/report?id=' + item.id
        });
      }
    }
  };
</script>

<style lang="scss">
  .study_print_page {
    background-color: #f3f8fc;
    padding-bottom: 60rpx;
  }

  .bg_box {
    width: 690rpx;
    height: 330rpx;
    margin: 0 auto;
    background-image: url('https://document.dxznjy.com/applet/newimages/ciliang.png');
    background-size: cover;
    background-repeat: no-repeat;
  }

  .list_box {
    margin: 30upx 30upx 0upx 26upx;
    border-radius: 14upx;
    background: #ffffff;
    padding: 40upx;
    font-size: 26upx;
    color: rgba(102, 102, 102, 1);
    position: relative;
  }

  .row_flex {
    display: flex;
    align-items: center;
    /* 		justify-content: center; */
  }

  .pick {
    position: relative;
    width: 520upx;
    height: 60upx;
    line-height: 60upx;
    border: 1px solid rgba(199, 199, 199, 1);
    border-radius: 12upx;
    text-align: center;
  }

  .jiantou {
    position: absolute;
    right: 15upx;
    top: 15upx;
  }

  // 下面内容
  .subject {
    margin: 30rpx;
    width: 630rpx;
    height: 225rpx;
    display: flex;
    flex-direction: column;
    justify-content: center;
    line-height: 70rpx;
    background: #ffffff;
    border-radius: 14rpx;
    padding-left: 30rpx;
    padding-right: 30rpx;
    font-size: 30rpx;
    font-family: AlibabaPuHuiTiR;
    color: #333333;
  }

  .subcard {
    position: relative;
  }
</style>