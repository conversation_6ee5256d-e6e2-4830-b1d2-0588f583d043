page {
  --main: #006658;
  --minor: #9fc021;
  --second: #ee77a1;
  --orange: #feab1e;
  --olive: #8dc63f;
  --green: #39b54a;
  --cyan: #1cbbb4;
  --blue: #37b6ff;
  --purple: #6739b6;
  --red: #ff0001;
  --page: #f7f7f7;
  --c-font: #333333;
  --c-sub: #a8a8a8;
  --price: #ed0202;

  /* 浅色 */
  --redLight: #fadbd9;
  --orangeLight: #fde6d2;
  --yellowLight: #fef2ce;
  --oliveLight: #e8f4d9;
  --greenLight: #d7f0db;
  --cyanLight: #d2f1f0;
  --blueLight: #cce6ff;
  --purpleLight: #e1d7f0;
  --mauveLight: #ebd4ef;
  --pinkLight: #f9d7ea;
  --brownLight: #ede1d9;
  --greyLight: #e7ebed;
  /* 渐变色 */
  --gradualRed: linear-gradient(45deg, #f43f3b, #ec008c);
  --gradualOrange: linear-gradient(45deg, #ff9700, #ed1c24);
  --gradualGreen: linear-gradient(45deg, #39b54a, #8dc63f);
  --gradualPurple: linear-gradient(45deg, #9000ff, #5e00ff);
  --gradualPink: linear-gradient(45deg, #ec008c, #6739b6);
  --gradualBlue: linear-gradient(45deg, #0081ff, #1cbbb4);
  /* 阴影透明色 */
  --ShadowSize: 6rpx 6rpx 8rpx;
  --redShadow: rgba(204, 69, 59, 0.2);
  --orangeShadow: rgba(217, 109, 26, 0.2);
  --yellowShadow: rgba(224, 170, 7, 0.2);
  --oliveShadow: rgba(124, 173, 55, 0.2);
  --greenShadow: rgba(48, 156, 63, 0.2);
  --cyanShadow: rgba(28, 187, 180, 0.2);
  --blueShadow: rgba(0, 102, 204, 0.2);
  --purpleShadow: rgba(88, 48, 156, 0.2);
  --mauveShadow: rgba(133, 33, 150, 0.2);
  --pinkShadow: rgba(199, 50, 134, 0.2);
  --brownShadow: rgba(140, 88, 53, 0.2);
  --greyShadow: rgba(114, 130, 138, 0.2);
  --grayShadow: rgba(114, 130, 138, 0.2);
  --blackShadow: rgba(26, 26, 26, 0.2);
  --safe-area-inset-top: 0px;
  --safe-area-inset-right: 0px;
  --safe-area-inset-bottom: 0px;
  --safe-area-inset-left: 0px;
}

@supports (top: constant(safe-area-inset-top)) {
  page {
    --safe-area-inset-top: constant(safe-area-inset-top);
    --safe-area-inset-right: constant(safe-area-inset-right);
    --safe-area-inset-bottom: constant(safe-area-inset-bottom);
    --safe-area-inset-left: constant(safe-area-inset-left);
  }
}

@supports (top: env(safe-area-inset-top)) {
  page {
    --safe-area-inset-top: env(safe-area-inset-top);
    --safe-area-inset-right: env(safe-area-inset-right);
    --safe-area-inset-bottom: env(safe-area-inset-bottom);
    --safe-area-inset-left: env(safe-area-inset-left);
  }
}

/* 底部加载更多样式 */
.no-content {
  width: 100%;
  height: 60vh;
  line-height: 80rpx;
  font-size: 26rpx;
  color: #a8a8a8;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.no-content image {
  width: 300rpx;
  height: auto;
  margin: 0 auto 20rpx;
}

.more {
  width: 100%;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  font-size: 26rpx;
  color: #a8a8a8;
}

.more::before,
.more::after {
  display: inline-block;
  width: 100rpx;
  height: 1px;
  content: '';
  background: #a8a8a8;
}

/* 弹性盒子 */
.flex {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-flow: row wrap;
}

.flex-box {
  flex: 1;
  overflow: hidden;
}

.flex-c {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-s {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.flex-dir-row {
  display: flex;
  flex-direction: row;
}

.flex-dir-col {
  display: flex;
  flex-direction: column;
}

.flex-a-c {
  display: flex;
  align-items: center;
}

.flex-col {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.flex-col-a {
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.flex-x-s {
  justify-content: flex-start;
}

.flex-x-c {
  justify-content: center;
}

.flex-x-b {
  justify-content: space-between;
}

.flex-x-a {
  justify-content: space-around;
}

.flex-f-a {
  display: flex;
  flex-flow: row wrap;
  justify-content: space-between;
}

.flex-e {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.flex-x-e {
  justify-content: flex-end;
}

.flex-y-s {
  display: flex;
  align-items: flex-start;
}

.flex-y-st {
  align-items: stretch;
}

.flex-y-c {
  align-items: center;
}

.flex-y-e {
  display: flex;
  align-items: flex-end;
}

.flex-three {
  box-sizing: border-box;
  flex: 0 0 33.3%;
}

.flex-four {
  box-sizing: border-box;
  flex: 0 0 25%;
}

.flex-five {
  box-sizing: border-box;
  flex: 0 0 20%;
}

.flex-six {
  box-sizing: border-box;
  flex: 0 0 16.6%;
}

.flex-self-s {
  display: flex;
  align-self: flex-start;
}

.flex-self-c {
  display: flex;
  align-self: center;
}

.flex-self-e {
  display: flex;
  align-self: flex-end;
}

.flex-k {
  flex-shrink: 0;
}

.flex-wrap {
  display: flex;
  flex-wrap: wrap;
}

.flex-nowrap {
  display: flex;
  flex-wrap: nowrap;
}

/* order布局 */
.order-0 {
  order: 0;
}

.order-1 {
  order: 1;
}

.order-2 {
  order: 2;
}

.order-3 {
  order: 3;
}

.order-4 {
  order: 4;
}

.order-5 {
  order: 5;
}

.order-6 {
  order: 6;
}

.order-7 {
  order: 7;
}

.order-8 {
  order: 8;
}

.order-9 {
  order: 9;
}

/* 按钮 */
.btn {
  color: #ffffff;
  background: #44c75f;
  border-radius: 10rpx;
  line-height: 1;
}

.btn-xs {
  width: 180rpx;
}

.btn-s {
  width: 270rpx;
}

.btn-m {
  width: 360rpx;
}

.btn-l {
  width: 500rpx;
}

.btn-xl {
  width: 690rpx;
}

.btn-primary {
  background: #007bff;
}

.btn-success {
  background: #44c75f;
}

.btn-error {
  background: #dd524d;
}

.btn-warning {
  background: #f0ad4e;
  border-radius: 10rpx;
  line-height: 1;
  padding: 15rpx 30rpx;
}

.btn-normal {
  display: block;
  margin: 0;
  padding: 0;
  line-height: normal;
  background: none;
  border-radius: 0;
  box-shadow: none;
  border: none;
  text-align: unset;
  overflow: visible;
}

.btn-normal:after {
  border: none;
}

.btn-normal.button-hover {
  color: inherit;
}

/* 弹窗蒙板 */
.mask {
  z-index: 998;
  position: fixed;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  background: rgba(0, 0, 0, 0.6);
}

/* 底部操作栏 */
.footer-fixed {
  z-index: 98;
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  box-shadow: 0 -2px 20rpx 0 rgba(144, 52, 52, 0.1);
  background: #ffffff;
}

/*间隔色彩分割线*/
.line-after::after {
  content: '';
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  height: 10rpx;
  background: linear-gradient(
    45deg,
    #ec5151 0%,
    #ec5151 5%,
    #fff 5%,
    #fff 7.5%,
    #32bdde 7.5%,
    #32bdde 12.5%,
    #fff 12.5%,
    #fff 15%,
    #ec5151 15%,
    #ec5151 20%,
    #fff 20%,
    #fff 22.5%,
    #32bdde 22.5%,
    #32bdde 27.5%,
    #fff 27.5%,
    #fff 30%,
    #ec5151 30%,
    #ec5151 35%,
    #fff 35%,
    #fff 37.5%,
    #32bdde 37.5%,
    #32bdde 42.5%,
    #fff 42.5%,
    #fff 45%,
    #ec5151 45%,
    #ec5151 50%,
    #fff 50%,
    #fff 52.5%,
    #32bdde 52.5%,
    #32bdde 57.5%,
    #fff 57.5%,
    #fff 60%,
    #ec5151 60%,
    #ec5151 65%,
    #fff 65%,
    #fff 67.5%,
    #32bdde 67.5%,
    #32bdde 72.5%,
    #fff 72.5%,
    #fff 75%,
    #ec5151 75%,
    #ec5151 80%,
    #fff 80%,
    #fff 82.5%,
    #32bdde 82.5%,
    #32bdde 87.5%,
    #fff 87.5%,
    #fff 90%,
    #ec5151 90%,
    #ec5151 95%,
    #fff 95%,
    #fff 97.5%,
    #32bdde 97.5%,
    #32bdde 102.5%
  );
}

/* 编辑器样式 */
.editor {
  width: 100%;
  box-sizing: border-box;
  padding: 30rpx;
}

.editor image {
  max-width: 100%;
}

/* 字体样式 */
.t-l {
  text-align: left;
}

.t-c {
  text-align: center;
}

.t-r {
  text-align: right;
}

.bold {
  font-weight: 900;
}

.fixed {
  position: fixed;
}

.relative {
  position: relative;
}

.absolute {
  position: absolute;
}

.h1 {
  font-size: 34rpx;
  font-weight: bold;
}

.h2 {
  font-size: 32rpx;
  font-weight: bold;
}

.line-t {
  text-decoration: line-through;
}

.placeholder {
  color: #a8a8a8;
  font-size: 28rpx;
}

.show,
.block {
  display: block;
}

.hide {
  display: none;
}

.inline {
  display: inline-block;
}

.height {
  height: 120rpx;
}

.height1300 {
  height: 1300rpx;
}

.sizing {
  box-sizing: border-box;
}

.monospace {
  font-family: monospace;
}

.w100 {
  width: 100%;
}

.h100 {
  height: 100%;
}

.shadow {
  box-shadow: 0px 0px 15rpx 0px rgba(0, 0, 0, 0.1);
}

.shadow-t {
  box-shadow: 0 0 10rpx 5rpx rgba(138, 138, 137, 0.1);
}

/* radius */
.radius-3 {
  border-radius: 3rpx;
  overflow: hidden;
}

.radius-5 {
  border-radius: 5rpx;
  overflow: hidden;
}

.radius-6 {
  border-radius: 6rpx;
  overflow: hidden;
}

.radius-8 {
  border-radius: 8rpx;
  overflow: hidden;
}

.radius-10 {
  border-radius: 10rpx;
  overflow: hidden;
}

.radius-12 {
  border-radius: 12rpx;
  overflow: hidden;
}

.radius-15 {
  border-radius: 15rpx;
  overflow: hidden;
}

.radius-16 {
  border-radius: 16rpx;
  overflow: hidden;
}

.radius-20 {
  border-radius: 20rpx;
  overflow: hidden;
}

.radius-25 {
  border-radius: 25rpx;
  overflow: hidden;
}

.radius-30 {
  border-radius: 30rpx;
  overflow: hidden;
}
.radius-35 {
  border-radius: 35rpx;
  overflow: hidden;
}
.radius-50 {
  border-radius: 500rpx;
  overflow: hidden;
}

.radius-all {
  border-radius: 50%;
  overflow: hidden;
}

.radius-allh {
  border-radius: 50%;
}

/*正方形盒子*/
.box-10 {
  width: 10rpx;
  height: 10rpx;
}

.box-30 {
  width: 30rpx;
  height: 30rpx;
}

.box-50 {
  width: 50rpx;
  height: 50rpx;
}

.box-60 {
  width: 60rpx;
  height: 60rpx;
}

.box-70 {
  width: 70rpx;
  height: 70rpx;
}

.box-80 {
  width: 80rpx;
  height: 80rpx;
}

.box-90 {
  width: 90rpx;
  height: 90rpx;
}

.box-100 {
  width: 100rpx;
  height: 100rpx;
}

.box-110 {
  width: 110rpx;
  height: 110rpx;
}

.box-120 {
  width: 120rpx;
  height: 120rpx;
}

.box-136 {
  width: 136rpx;
  height: 136rpx;
}

.box-140 {
  width: 140rpx;
  height: 140rpx;
}

.box-150 {
  width: 150rpx;
  height: 150rpx;
}

.box-160 {
  width: 160rpx;
  height: 160rpx;
}

.box-180 {
  width: 180rpx;
  height: 180rpx;
}

.box-200 {
  width: 200rpx;
  height: 200rpx;
}

.box-210 {
  width: 200rpx;
  height: 210rpx;
}

.box-220 {
  width: 220rpx;
  height: 220rpx;
}

.box-240 {
  width: 240rpx;
  height: 240rpx;
}

.box-250 {
  width: 240rpx;
  height: 250rpx;
}

/* 长方形盒子 */
.boxl-50 {
  width: 100rpx;
  height: 30rpx;
}

.boxl-40 {
  width: 50rpx;
  height: 30rpx;
}

.boxl-72 {
  width: 72rpx;
  height: 34rpx;
}

.boxl-100 {
  width: 100rpx;
  height: 50rpx;
}

.boxl-120 {
  width: 120rpx;
  height: 30rpx;
}

.boxl-200 {
  width: 200rpx;
  height: 54rpx;
}
.boxl-300 {
  width: 300rpx;
  height: 74rpx;
}

.boxl-456 {
  width: 456rpx;
  height: 54rpx;
}

.boxl-650 {
  width: 570rpx;
  height: 96rpx;
}

/* 超出换行 */
.onelist {
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}

.twolist {
  display: -webkit-box;
  word-break: break-all;
  text-overflow: -o-ellipsis-lastline;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.thrlist {
  display: -webkit-box;
  word-break: break-all;
  text-overflow: -o-ellipsis-lastline;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
}

.elli {
  width: 100%;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.elli-2 {
  text-overflow: -o-ellipsis-lastline;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
}

.elli-3 {
  text-overflow: -o-ellipsis-lastline;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  line-clamp: 3;
  -webkit-box-orient: vertical;
}

/* 字体大小 */
.f-72 {
  font-size: 72rpx;
}

.f-70 {
  font-size: 70rpx;
}

.f-60 {
  font-size: 60rpx;
}

.f-58 {
  font-size: 58rpx;
}

.f-56 {
  font-size: 56rpx;
}

.f-54 {
  font-size: 54rpx;
}

.f-52 {
  font-size: 52rpx;
}

.f-50 {
  font-size: 50rpx;
}

.f-48 {
  font-size: 48rpx;
}

.f-46 {
  font-size: 46rpx;
}

.f-44 {
  font-size: 44rpx;
}

.f-42 {
  font-size: 42rpx;
}

.f-40 {
  font-size: 40rpx;
}

.f-38 {
  font-size: 38rpx;
}

.f-36 {
  font-size: 36rpx;
}

.f-34 {
  font-size: 34rpx;
}

.f-32 {
  font-size: 32rpx;
}

.f-30 {
  font-size: 30rpx;
}

.f-28 {
  font-size: 28rpx;
}

.f-26 {
  font-size: 26rpx;
}

.f-24 {
  font-size: 24rpx;
}

.f-22 {
  font-size: 22rpx;
}

.f-20 {
  font-size: 20rpx;
}

.f-18 {
  font-size: 18rpx;
}

.f-16 {
  font-size: 16rpx;
}

.f-14 {
  font-size: 14rpx;
}

/* 字体颜色 */
.c-00 {
  color: #000000;
}

.c-1a {
  color: #1a1a1a;
}

.c-33 {
  color: #333333;
}

.c-44 {
  color: #444444;
}

.c-29 {
  color: #292929;
}

.c-5c {
  color: #5c5c5c;
}

.c-66 {
  color: #666666;
}

.c-6a {
  color: #6a6a6a;
}

.c-55 {
  color: #555555;
}

.c-77 {
  color: #777777;
}

.c-88 {
  color: #888888;
}

.c-99 {
  color: #999999;
}

.c-96 {
  color: #969696;
}

.c-aa {
  color: #aaaaaa;
}

.c-ab {
  color: #ababab;
}

.c-a5 {
  color: #a5a5a5;
}

.c-a8 {
  color: #a8a8a8;
}

.c-c2 {
  color: #c2c2c2;
}

.c-e5 {
  color: #e5e5e5;
}

.c-ff {
  color: #ffffff;
}

.c-f2 {
  color: #f2f2f2;
}

.c-a6 {
  color: #a6a6a7;
}

.c-f0 {
  color: #f01010;
}

.c-a2 {
  color: #ffa200;
}

.c-81 {
  color: #81e2af;
}

.c-9cc {
  color: #9ccef6;
}

.c-m {
  color: var(--main) !important;
}

.c-mr {
  color: var(--minor) !important;
}

.c-s {
  color: var(--second) !important;
}

.c-orange {
  color: var(--orange) !important;
}

.c-olive {
  color: var(--olive) !important;
}

.c-green {
  color: var(--green) !important;
}

.c-cyan {
  color: var(--cyan) !important;
}

.c-blue {
  color: var(--blue) !important;
}

.c-purple {
  color: var(--purple) !important;
}

.c-red {
  color: var(--red) !important;
}

.c-page {
  color: var(--page) !important;
}

.c-font {
  color: var(--font) !important;
}

.c-sub {
  color: var(--sub) !important;
}

.c-price {
  color: var(--price) !important;
}

.c-red {
  color: #ff0000;
}

.c-fa {
  color: #faa314;
}

.c-fea {
  color: #ea6031;
}

.c-fe5 {
  color: #e57126;
}

.c-blue {
  color: #007eff;
}

.c-faa {
  color: #faa115;
}

.c-ffe {
  color: #ffead5;
}

.c-fde {
  color: #dedede;
}

.c-499 {
  color: #499d1c;
}

.c-ebd {
  color: #edbc73;
}

.c-2e8 {
  color: #2e896f;
}

.c-f9c {
  color: #96c4b7;
}

.c-f1a {
  color: #f1a667;
}

.c-f76 {
  color: #7676e9;
}

.c-fba {
  color: #babaf4;
}

.c-fe9 {
  color: #e97676;
}

.c-ff4 {
  color: #f4baba;
}

.c-f1d {
  color: #1d755c;
}

.c-fe5 {
  color: #e57126;
}

.c-f0b {
  color: #00b890;
}

.c-fd3 {
  color: #ff71d3;
}

.c-e8 {
  color: #489e85;
}

.c-588 {
  color: #588c7a;
}

.c-39c {
  color: #39c37b;
}

.correct-answer {
  color: #94e6c7;
}

.incorrect-answer {
  color: #ffaf85;
}

/* 背景色颜色 */
.bg-00 {
  background: #000;
}

.bg-33 {
  background: #333;
}

.bg-23 {
  background: #232221;
}

.bg-66 {
  background: #666;
}

.bg-77 {
  background: #777;
}

.bg-88 {
  background: #888;
}

.bg-96 {
  background: #969696;
}

.bg-99 {
  background: #999;
}

.bg-a8 {
  background: #a8a8a8;
}

.bg-1a {
  background: #1a1a1a;
}

.bg-c2 {
  background: #c2c2c2;
}

.bg-cc {
  background: #cccccc;
}

.bg-dd {
  background: #dddddd;
}

.bg-ee {
  background: #eeeeee;
}

.bg-e5 {
  background: #e5e5e5;
}

.bg-f4 {
  background: #f4f4f4;
}

.bg-f5 {
  background: #f5f5f5;
}

.bg-fa {
  background: #fafafa;
}

.bg-f7 {
  background: #f7f7f7;
}

.bg-f3 {
  background: #f3f8fc;
}

.bg-ff {
  background: #ffffff;
}

.bg-faf8 {
  background: #f5f8fa;
}

.bg-m {
  background: var(--main) !important;
}

.bg-mr {
  background: var(--minor) !important;
}

.bg-s {
  background: var(--second) !important;
}

.bg-orange {
  background: var(--orange) !important;
}

.bg-olive {
  background: var(--olive) !important;
}

.bg-green {
  background: var(--green) !important;
}

.bg-cyan {
  background: var(--cyan) !important;
}

.bg-blue {
  background: var(--blue) !important;
}

.bg-purple {
  background: var(--purple) !important;
}

.bg-red {
  background: var(--red) !important;
}

.bg-page {
  background: var(--page) !important;
}

.bg-font {
  background: var(--font) !important;
}

.bg-sub {
  background: var(--sub) !important;
}

.bg-linear-r {
  background: linear-gradient(to right, var(--main), var(--minor));
}

.bg-linear-b {
  background: linear-gradient(to bottom, var(--main), var(--minor));
}

.bg-a2 {
  background-color: #ffa200;
}

/* 行高 */
.lh-12 {
  line-height: 1.2;
}

.lh-13 {
  line-height: 1.3;
}

.lh-14 {
  line-height: 1.4;
}

.lh-15 {
  line-height: 1.5;
}

.lh-16 {
  line-height: 1.6;
}

.lh-17 {
  line-height: 1.7;
}

.lh-18 {
  line-height: 1.8;
}

.lh-20 {
  line-height: 20rpx;
}

.lh-24 {
  line-height: 24rpx;
}

.lh-28 {
  line-height: 28rpx;
}

.lh-30 {
  line-height: 30rpx;
}

.lh-32 {
  line-height: 32rpx;
}

.lh-36 {
  line-height: 36rpx;
}

.lh-40 {
  line-height: 40rpx;
}

.lh-42 {
  line-height: 42rpx;
}

.lh-44 {
  line-height: 44rpx;
}

.lh-50 {
  line-height: 50rpx;
}

.lh-56 {
  line-height: 56rpx;
}

.lh-60 {
  line-height: 60rpx;
}

.lh-64 {
  line-height: 64rpx;
}

.lh-70 {
  line-height: 70rpx;
}

.lh-72 {
  line-height: 72rpx;
}

.lh-80 {
  line-height: 80rpx;
}

.lh-90 {
  line-height: 90rpx;
}

.lh-96 {
  line-height: 96rpx;
}

.lh-98 {
  line-height: 98rpx;
}

.lh-100 {
  line-height: 100rpx;
}

/* 高度 */
.h-20 {
  height: 20rpx;
}

.h-30 {
  height: 30rpx;
}

.h-40 {
  height: 40rpx;
}

.h-50 {
  height: 50rpx;
}

.h-60 {
  height: 60rpx;
}

.h-70 {
  height: 70rpx;
}

.h-80 {
  height: 80rpx;
}

.h-90 {
  height: 90rpx;
}

.h-100 {
  height: 100rpx;
}

.h-110 {
  height: 110rpx;
}

.h-115 {
  height: 115rpx !important;
}

.h-120 {
  height: 120rpx;
}

.h-130 {
  height: 130rpx;
}

.h-140 {
  height: 140rpx;
}

.h-160 {
  height: 160rpx;
}

/* 内边距 */
.p-5 {
  padding: 5rpx;
}

.p-8 {
  padding: 8rpx;
}

.p-10 {
  padding: 10rpx;
}

.p-12 {
  padding: 12rpx;
}

.p-15 {
  padding: 15rpx;
}

.p-20 {
  padding: 20rpx;
}

.p-25 {
  padding: 25rpx;
}

.p-30 {
  padding: 30rpx;
}

.p-35 {
  padding: 35rpx;
}

.p-40 {
  padding: 40rpx;
}

.p-45 {
  padding: 45rpx;
}

.p-50 {
  padding: 50rpx;
}

.pl-5 {
  padding-left: 5rpx;
}

.pl-8 {
  padding-left: 8rpx;
}

.pl-10 {
  padding-left: 10rpx;
}

.pl-12 {
  padding-left: 12rpx;
}

.pl-15 {
  padding-left: 15rpx;
}
.pl-18 {
  padding-left: 18rpx;
}
.pl-20 {
  padding-left: 20rpx;
}

.pl-25 {
  padding-left: 25rpx;
}

.pl-30 {
  padding-left: 30rpx;
}

.pl-35 {
  padding-left: 35rpx;
}

.pl-40 {
  padding-left: 40rpx;
}

.pl-45 {
  padding-left: 45rpx;
}

.pl-50 {
  padding-left: 50rpx;
}

.pr-5 {
  padding-right: 5rpx;
}

.pr-8 {
  padding-right: 8rpx;
}

.pr-10 {
  padding-right: 10rpx;
}

.pr-12 {
  padding-right: 12rpx;
}

.pr-15 {
  padding-right: 15rpx;
}

.pr-20 {
  padding-right: 20rpx;
}

.pr-25 {
  padding-right: 25rpx;
}

.pr-30 {
  padding-right: 30rpx;
}

.pr-35 {
  padding-right: 35rpx;
}

.pr-40 {
  padding-right: 40rpx;
}

.pr-45 {
  padding-right: 45rpx;
}

.pr-50 {
  padding-right: 50rpx;
}

.pt-5 {
  padding-top: 5rpx;
}

.pt-8 {
  padding-top: 8rpx;
}

.pt-10 {
  padding-top: 10rpx;
}

.pt-12 {
  padding-top: 12rpx;
}

.pt-15 {
  padding-top: 15rpx;
}

.pt-20 {
  padding-top: 20rpx;
}

.pt-24 {
  padding-top: 24rpx;
}

.pt-25 {
  padding-top: 25rpx;
}

.pt-28 {
  padding-top: 28rpx;
}

.pt-30 {
  padding-top: 30rpx;
}

.pt-35 {
  padding-top: 35rpx;
}

.pt-40 {
  padding-top: 40rpx;
}

.pt-45 {
  padding-top: 45rpx;
}

.pt-50 {
  padding-top: 50rpx;
}

.pt-60 {
  padding-top: 60rpx;
}

.pt-70 {
  padding-top: 70rpx;
}

.pt-75 {
  padding-top: 75rpx;
}

.pt-110 {
  padding-top: 110rpx;
}

.pt-140 {
  padding-top: 140rpx;
}

.pb-5 {
  padding-bottom: 5rpx;
}

.pb-8 {
  padding-bottom: 8rpx;
}

.pb-10 {
  padding-bottom: 10rpx;
}

.pb-12 {
  padding-bottom: 12rpx;
}

.pb-15 {
  padding-bottom: 15rpx;
}

.pb-20 {
  padding-bottom: 20rpx;
}

.pb-25 {
  padding-bottom: 25rpx;
}

.pb-30 {
  padding-bottom: 30rpx;
}

.pb-35 {
  padding-bottom: 35rpx;
}

.pb-40 {
  padding-bottom: 40rpx;
}

.pb-45 {
  padding-bottom: 45rpx;
}

.pb-50 {
  padding-bottom: 50rpx;
}

.pb-55 {
  padding-bottom: 55rpx;
}

.pb-70 {
  padding-bottom: 70rpx;
}

.pb-100 {
  padding-bottom: 100rpx;
}

.pb-150 {
  padding-bottom: 150rpx;
}

.ptb-5 {
  padding-top: 5rpx;
  padding-bottom: 5rpx;
}

.ptb-8 {
  padding-top: 8rpx;
  padding-bottom: 8rpx;
}

.ptb-10 {
  padding-top: 10rpx;
  padding-bottom: 10rpx;
}

.ptb-12 {
  padding-top: 12rpx;
  padding-bottom: 12rpx;
}

.ptb-15 {
  padding-top: 15rpx;
  padding-bottom: 15rpx;
}

.ptb-20 {
  padding-top: 20rpx;
  padding-bottom: 20rpx;
}

.ptb-25 {
  padding-top: 25rpx;
  padding-bottom: 25rpx;
}

.ptb-30 {
  padding-top: 30rpx;
  padding-bottom: 30rpx;
}

.ptb-35 {
  padding-top: 35rpx;
  padding-bottom: 35rpx;
}

.ptb-40 {
  padding-top: 40rpx;
  padding-bottom: 40rpx;
}

.ptb-45 {
  padding-top: 45rpx;
  padding-bottom: 45rpx;
}

.ptb-50 {
  padding-top: 50rpx;
  padding-bottom: 50rpx;
}

.ptb-55 {
  padding-top: 55rpx;
  padding-bottom: 55rpx;
}

.ptb-60 {
  padding-top: 60rpx;
  padding-bottom: 60rpx;
}

.plr-5 {
  padding-left: 5rpx;
  padding-right: 5rpx;
}

.plr-8 {
  padding-left: 8rpx;
  padding-right: 8rpx;
}

.plr-10 {
  padding-left: 10rpx;
  padding-right: 10rpx;
}

.plr-12 {
  padding-left: 12rpx;
  padding-right: 12rpx;
}

.plr-15 {
  padding-left: 15rpx;
  padding-right: 15rpx;
}

.plr-20 {
  padding-left: 20rpx;
  padding-right: 20rpx;
}

.plr-25 {
  padding-left: 25rpx;
  padding-right: 25rpx;
}

.plr-30 {
  padding-left: 30rpx;
  padding-right: 30rpx;
}

.plr-32 {
  padding-left: 32rpx;
  padding-right: 32rpx;
}

.plr-35 {
  padding-left: 35rpx;
  padding-right: 35rpx;
}

.plr-40 {
  padding-left: 40rpx;
  padding-right: 40rpx;
}

.plr-45 {
  padding-left: 45rpx;
  padding-right: 45rpx;
}

.plr-50 {
  padding-left: 50rpx;
  padding-right: 50rpx;
}

.plr-60 {
  padding-left: 60rpx;
  padding-right: 60rpx;
}

.plr-80 {
  padding-left: 80rpx;
  padding-right: 80rpx;
}

/* 外边距 */
.m-5 {
  margin: 5rpx;
}

.m-8 {
  margin: 8rpx;
}

.m-10 {
  margin: 10rpx;
}

.m-12 {
  margin: 12rpx;
}

.m-15 {
  margin: 15rpx;
}

.m-20 {
  margin: 20rpx;
}

.m-25 {
  margin: 25rpx;
}

.m-30 {
  margin: 30rpx;
}

.m-35 {
  margin: 35rpx;
}

.m-40 {
  margin: 40rpx;
}

.m-45 {
  margin: 45rpx;
}

.m-50 {
  margin: 50rpx;
}

.ml-5 {
  margin-left: 5rpx;
}

.ml-8 {
  margin-left: 8rpx;
}

.ml-10 {
  margin-left: 10rpx;
}

.ml-12 {
  margin-left: 12rpx;
}

.ml-15 {
  margin-left: 15rpx;
}
.ml-18 {
  margin-left: 18rpx;
}
.ml-20 {
  margin-left: 20rpx;
}

.ml-25 {
  margin-left: 25rpx;
}

.ml-30 {
  margin-left: 30rpx;
}

.ml-35 {
  margin-left: 35rpx;
}

.ml-40 {
  margin-left: 40rpx;
}

.ml-45 {
  margin-left: 45rpx;
}

.ml-50 {
  margin-left: 50rpx;
}

.ml-55 {
  margin-left: 55rpx;
}

.ml-60 {
  margin-left: 60rpx;
}

.mr-5 {
  margin-right: 5rpx;
}

.mr-8 {
  margin-right: 8rpx;
}

.mr-10 {
  margin-right: 10rpx;
}

.mr-12 {
  margin-right: 12rpx;
}

.mr-15 {
  margin-right: 15rpx;
}

.mr-20 {
  margin-right: 20rpx;
}

.mr-25 {
  margin-right: 25rpx;
}

.mr-30 {
  margin-right: 30rpx;
}

.mr-35 {
  margin-right: 35rpx;
}

.mr-40 {
  margin-right: 40rpx;
}

.mr-45 {
  margin-right: 45rpx;
}

.mr-50 {
  margin-right: 50rpx;
}

.mr-55 {
  margin-right: 55rpx;
}

.mr-60 {
  margin-right: 60rpx;
}

.mr-65 {
  margin-right: 65rpx;
}

.mr-70 {
  margin-right: 70rpx;
}

.mr-75 {
  margin-right: 75rpx;
}

.mr-80 {
  margin-right: 80rpx;
}

.mt-5 {
  margin-top: 5rpx;
}

.mt-8 {
  margin-top: 8rpx;
}

.mt-10 {
  margin-top: 10rpx;
}

.mt-12 {
  margin-top: 12rpx;
}

.mt-15 {
  margin-top: 15rpx;
}

.mt-16 {
  margin-top: 16rpx;
}

.mt-20 {
  margin-top: 20rpx;
}

.mt-24 {
  margin-top: 24rpx;
}

.mt-25 {
  margin-top: 25rpx;
}

.mt-28 {
  margin-top: 28rpx;
}

.mt-30 {
  margin-top: 30rpx;
}

.mt-35 {
  margin-top: 35rpx;
}

.mt-40 {
  margin-top: 40rpx;
}

.mt-45 {
  margin-top: 45rpx;
}

.mt-50 {
  margin-top: 50rpx;
}

.mt-55 {
  margin-top: 55rpx;
}

.mt-60 {
  margin-top: 60rpx;
}

.mt-65 {
  margin-top: 65rpx;
}

.mt-70 {
  margin-top: 70rpx;
}

.mt-75 {
  margin-top: 75rpx;
}

.mt-80 {
  margin-top: 80rpx;
}

.mt-200 {
  margin-top: 200rpx;
}

.mt-500 {
  margin-top: 500rpx;
}

.mt-700 {
  margin-top: 700rpx;
}

.mt-850 {
  margin-top: 850rpx;
}

.mb-5 {
  margin-bottom: 5rpx;
}

.mb-8 {
  margin-bottom: 8rpx;
}

.mb-10 {
  margin-bottom: 10rpx;
}

.mb-12 {
  margin-bottom: 12rpx;
}

.mb-18 {
  margin-bottom: 18rpx;
}

.mb-15 {
  margin-bottom: 15rpx;
}
.mb-20 {
  margin-bottom: 20rpx;
}

.mb-25 {
  margin-bottom: 25rpx;
}

.mb-30 {
  margin-bottom: 30rpx;
}

.mb-35 {
  margin-bottom: 35rpx;
}

.mb-40 {
  margin-bottom: 40rpx;
}

.mb-45 {
  margin-bottom: 45rpx;
}

.mb-50 {
  margin-bottom: 50rpx;
}

.mb-65 {
  margin-bottom: 65rpx;
}

.mb-100 {
  margin-bottom: 100rpx;
}

.mb-150 {
  margin-bottom: 150rpx;
}

.mtb-5 {
  margin-top: 5rpx;
  margin-bottom: 5rpx;
}

.mtb-8 {
  margin-top: 8rpx;
  margin-bottom: 8rpx;
}

.mtb-10 {
  margin-top: 10rpx;
  margin-bottom: 10rpx;
}

.mtb-12 {
  margin-top: 12rpx;
  margin-bottom: 12rpx;
}

.mtb-15 {
  margin-top: 15rpx;
  margin-bottom: 15rpx;
}

.mtb-20 {
  margin-top: 20rpx;
  margin-bottom: 20rpx;
}

.mtb-25 {
  margin-top: 25rpx;
  margin-bottom: 25rpx;
}

.mtb-30 {
  margin-top: 30rpx;
  margin-bottom: 30rpx;
}

.mtb-35 {
  margin-top: 35rpx;
  margin-bottom: 35rpx;
}

.mtb-40 {
  margin-top: 40rpx;
  margin-bottom: 40rpx;
}

.mtb-45 {
  margin-top: 45rpx;
  margin-bottom: 45rpx;
}

.mtb-50 {
  margin-top: 50rpx;
  margin-bottom: 50rpx;
}

.mtb-55 {
  margin-top: 55rpx;
  margin-bottom: 55rpx;
}

.mtb-60 {
  margin-top: 60rpx;
  margin-bottom: 60rpx;
}

.mlr-5 {
  margin-left: 5rpx;
  margin-right: 5rpx;
}

.mlr-8 {
  margin-left: 8rpx;
  margin-right: 8rpx;
}

.mlr-10 {
  margin-left: 10rpx;
  margin-right: 10rpx;
}

.mlr-12 {
  margin-left: 12rpx;
  margin-right: 12rpx;
}

.mlr-15 {
  margin-left: 15rpx;
  margin-right: 15rpx;
}

.mlr-20 {
  margin-left: 20rpx;
  margin-right: 20rpx;
}

.mlr-25 {
  margin-left: 25rpx;
  margin-right: 25rpx;
}

.mlr-30 {
  margin-left: 30rpx;
  margin-right: 30rpx;
}

.mlr-35 {
  margin-left: 35rpx;
  margin-right: 35rpx;
}

.mlr-40 {
  margin-left: 40rpx;
  margin-right: 40rpx;
}

.mlr-45 {
  margin-left: 45rpx;
  margin-right: 45rpx;
}

.mlr-50 {
  margin-left: 50rpx;
  margin-right: 50rpx;
}

.mlr-55 {
  margin-left: 55rpx;
  margin-right: 55rpx;
}

.mlr-60 {
  margin-left: 60rpx;
  margin-right: 60rpx;
}

/* 线条&分割线 */
.hr-2 {
  width: 100%;
  height: 2rpx;
}

.hr-4 {
  width: 100%;
  height: 4rpx;
}

.hr-6 {
  width: 100%;
  height: 6rpx;
}

.hr-8 {
  width: 100%;
  height: 8rpx;
}

.hr-10 {
  width: 100%;
  height: 10rpx;
}

.hr-15 {
  width: 100%;
  height: 15rpx;
}

.hr-20 {
  width: 100%;
  height: 20rpx;
}

.hr-25 {
  width: 100%;
  height: 25rpx;
}

.hr-30 {
  width: 100%;
  height: 30rpx;
}

.hr-35 {
  width: 100%;
  height: 35rpx;
}

.hr-40 {
  width: 100%;
  height: 40rpx;
}

.b-t {
  border-top: 1px solid #eee;
}

.b-b {
  border-bottom: 1px solid #eee;
}

.b-db {
  border-bottom: 1px dashed #eee;
}

.b-l {
  border-left: 1px solid #eee;
}

.b-r {
  border-right: 1px solid #eee;
}

.b-g {
  border-right: 1px solid #d2fce6;
}

.b-b {
  border-right: 1px solid #e6f4ff;
}

.bd-a5 {
  border: 1px solid #a5a5a5 !important;
}

.bd-cc {
  border: 1px solid #cccccc !important;
}

.bd-dd {
  border: 1px solid #dddddd !important;
}

.bd-ee {
  border: 1px solid #eeeeee !important;
}

.bd-ff {
  border: 1px solid #ffffff !important;
}

.bd-m {
  border: 1px solid var(--main) !important;
}

.bd-mr {
  border: 1px solid var(--minor) !important;
}

.bd-s {
  border: 1px solid var(--second) !important;
}

.bd-orange {
  border: 1px solid var(--orange) !important;
}

.bd-olive {
  border: 1px solid var(--olive) !important;
}

.bd-green {
  border: 1px solid var(--green) !important;
}

.bd-cyan {
  border: 1px solid var(--cyan) !important;
}

.bd-blue {
  border: 1px solid var(--blue) !important;
}

.bd-purple {
  border: 1px solid var(--purple) !important;
}

.bd-red {
  border: 1px solid var(--red) !important;
}

.bd-font {
  border: 1px solid var(--font) !important;
}

.bd-sub {
  border: 1px solid var(--sub) !important;
}

.wh100 {
  width: 100%;
  height: 100%;
}

.col-1 {
  width: 8.33%;
}

.col-2 {
  width: 16.67%;
}

.col-3 {
  width: 25%;
}

.col-4 {
  width: 33.33%;
}

.col-5 {
  width: 41.67%;
}

.col-6 {
  width: 50%;
}

.col-7 {
  width: 58.33%;
}

.col-8 {
  width: 66.67%;
}

.col-9 {
  width: 75%;
}

.col-10 {
  width: 83.33%;
}

.col-11 {
  width: 91.67%;
}

.col-12 {
  width: 100%;
}

/* 透明度 */
.op-2 {
  opacity: 0.2;
}

.op-4 {
  opacity: 0.4;
}

.op-6 {
  opacity: 0.6;
}

/* 去除button */
button::after {
  border: none;
}

button {
  background-color: transparent;
  padding-left: 0;
  padding-right: 0;
  margin-left: 0;
  margin-right: 0;
  line-height: inherit;
  /* width:unset!important */
}

button {
  border-radius: 0;
}

/* 合同管理页面提交按钮样式 */
.nextstep {
  width: 586rpx;
  height: 80rpx;
  background-image: linear-gradient(to bottom, #88cfba, #1d755c);
  text-align: center;
  line-height: 80rpx;
  color: #fff;
  border-radius: 45rpx;
  position: absolute;
  bottom: 35rpx;
}

/* 文字换行展示 */
.t-w {
  box-sizing: border-box;
  word-break: break-all;
}

/* 知识图谱颜色标记圆 */
.color-mark {
  display: flex;
  align-items: center;
  margin-right: 10px;
  /* 根据需要调整间距 */
}

.color-mark::before {
  content: '';
  display: inline-block;
  width: 10px;
  /* 圆的直径 */
  height: 10px;
  border-radius: 50%;
  margin-right: 5px;
  /* 圆和文本之间的间距 */
}

/* 知识图谱学生头像昵称区域 */
.student-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 80%;
  /* 使学生信息区域占满一半高度 */
}

/* 知识图谱头像附近的圆形 */
.circle-base {
  display: inline-block;
  width: 70px;
  /* 圆的直径 */
}

.colorNumber-mark {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 70px;
  line-height: 55px;
  border-radius: 50%;
  padding: 15rpx 0;
  font-size: 24rpx;
}

/* 知识图谱间距 */
.mlc-180 {
  margin-left: -180%;
}

.mrc-180 {
  margin-right: -180%;
}

/* 学情报告 */
.color-report {
  display: flex;
  align-items: center;
  margin-right: 10px;
  /* 根据需要调整间距 */
}

.color-report::before {
  content: '';
  display: inline-block;
  width: 5px;
  /* 圆的直径 */
  height: 5px;
  border-radius: 50%;
  margin-right: 5px;
  /* 圆和文本之间的间距 */
}
