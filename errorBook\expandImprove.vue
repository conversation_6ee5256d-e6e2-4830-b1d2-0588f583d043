<template>
  <view class="expandContent pl-25 pr-25 pt-20 bg-ff">
    <view class="BtnArea h-80 lh-80">
      <view class="submitButton mr-15 t-c c-ff radius-35" @click="$noMultipleClicks(correctAnswer)">
        <text class="f-26">提交</text>
      </view>
    </view>
    <!-- <view class="questionArea"> -->
    <viewQuestion ref="viewQuestion" v-if="expandImproveData" :item="expandImproveData" :index="0" :status="3" @checked="onChecked"></viewQuestion>
    <!-- </view> -->
  </view>
</template>

<script>
  import viewQuestion from '@/errorBook/components/viewQuestion.vue';
  export default {
    data() {
      return {
        noClick: true, //防抖
        expandImproveData: {},
        checkedAnswer: '',
        questionBankId: '',
        studentCode: ''
      };
    },
    components: {
      viewQuestion
    },
    onLoad(options) {
      this.expandImproveData = JSON.parse(decodeURIComponent(options.expandImproveData));
      this.questionBankId = this.expandImproveData.questionBankId;
      this.studentCode = options.studentCode;
    },
    methods: {
      onChecked(val) {
        this.checkedAnswer = val;
      },
      correctAnswer() {
        if (this.checkedAnswer === '') {
          uni.showToast({
            title: '请选择题目答案',
            icon: 'none'
          });
          return;
        }
        const uploadImages = this.$refs.viewQuestion.getUploadImages();
        let obj = {
          questionBankId: this.questionBankId,
          studentCode: this.studentCode,
          myAnswerImage: uploadImages,
          myAnswer: this.checkedAnswer
        };
        this.$httpUser.post('dyf/math/wap/correctionNoteBook/commit', obj).then((res) => {
          if (res.data.success) {
            uni.redirectTo({
              url: `/errorBook/correctAnswer?data=${encodeURIComponent(JSON.stringify(res.data.data))}&studentCode=${this.studentCode}`
            });
          }
        });
      }
    }
  };
</script>

<style>
  .expandContent {
    height: 100vh;
    box-sizing: border-box;
  }
  .BtnArea {
    height: 80rpx;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    background-color: #fbfbfb;
  }
  .submitButton {
    width: 196rpx;
    height: 69rpx;
    background: #428a6f;
    line-height: 66rpx;
  }
  .questionArea {
    padding: 0 35rpx 35rpx;
    box-sizing: border-box;
  }
  .question {
    width: 650rpx;
    font-weight: 400;
    font-size: 30rpx;
    color: #333333;
    line-height: 42rpx;
  }
  .option {
    width: 626rpx;
    height: 90rpx;
    line-height: 90rpx;
    border-radius: 20rpx;
    padding-left: 33rpx;
    box-sizing: border-box;
    border: 1rpx solid #efeff0;
  }
  .componentMargin {
    margin-left: -35rpx;
    margin-right: -35rpx;
  }
</style>
