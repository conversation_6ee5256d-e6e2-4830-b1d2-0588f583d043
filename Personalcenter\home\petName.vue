<template>
  <view class="plr-30">
    <view class="p-30 bg-ff radius-15 flex-s">
      <input type="nickname" @blur="onblur" @input="oninput" @focus="onfocus" v-model="nickName" placeholder="请输入昵称" maxlength="6" />
      <uni-icons v-if="showClearIcon" type="clear" size="24" color="#CCC" @click="clearInput"></uni-icons>
    </view>
    <view class="f-28 c-66 mt-30">提示：限制6个字符</view>

    <button class="info_sure" @click="userEdit">完成</button>
  </view>
</template>

<script>
  const { $http, $showMsg } = require('@/util/methods.js');
  export default {
    data() {
      return {
        nickName: '',
        userinfo: {},
        showClearIcon: true
      };
    },
    onShow() {
      this.homeData();
    },
    methods: {
      async homeData() {
        let _this = this;
        const res = await $http({
          url: 'zx/user/userInfoNew'
        });
        if (res) {
          _this.userinfo = res.data;
          _this.nickName = _this.userinfo.nickName;
        }
      },
      async userEdit() {
        if (this.nickName == '') {
          this.$util.alter('请输入昵称');
          return;
        }

        let _this = this;
        const res = await $http({
          url: 'zx/user/updateNickName',
          method: 'post',
          data: {
            headPortrait: '',
            nickName: _this.nickName
          }
        });
        $showMsg(res.message);
        setTimeout(function () {
          uni.navigateBack();
        }, 1500);
      },

      onblur(e) {
        console.log(e.detail.value);
        this.nickName = e.detail.value;
        if (e.detail.value != '') {
          this.showClearIcon = true;
        } else {
          this.showClearIcon = false;
        }
      },
      oninput(e) {
        console.log(e);
        this.nickName = e.detail.value;
        if (e.detail.value.length > 0) {
          this.showClearIcon = true;
        } else {
          this.showClearIcon = false;
        }
      },

      onfocus(e) {
        // debugger
        console.log(e);
      },

      clearInput() {
        this.nickName = '';
        this.showClearIcon = false;
      }
    }
  };
</script>

<style lang="scss" scoped>
  .info_sure {
    color: #fff;
    width: 600upx;
    height: 80upx;
    margin: 60upx auto;
    text-align: center;
    line-height: 80upx;
    border-radius: 45upx;
    background-image: linear-gradient(to bottom, #88cfba, #1d755c);
  }
</style>
