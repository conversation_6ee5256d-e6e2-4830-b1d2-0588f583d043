<template>
  <view style="min-height: 100vh; background-color: #fff">
    <!-- 这里是状态栏 -->
    <view class="status_bar">
      <!-- <image class="status_bar_image" :src="imgHost+'alading/correcting/login_bg1.png'" mode=""></image> -->
    </view>
    <uni-nav-bar class="nav-bar" color="#000" left-icon="left" title="手机号验证码登录" :border="false" @clickLeft="back" />
    <view class="wechatapp">
      <image class="header" :src="imgHost + 'dxSelect/login-bgc.png'" mode="widthFix"></image>
    </view>

    <view class="login_btn_group1">
      <view class="login-box">
        <form id="#nform">
          <view class="phone-input">
            <image style="width: 28rpx; height: 29rpx" :src="imgHost + 'dxSelect/fourthEdition/icon_login_user.png'"></image>
            <input
              style="margin-left: 16rpx"
              type="number"
              :value="mobile"
              name="mobile"
              placeholder="请输入手机号"
              placeholder-class="phClass"
              class="input"
              maxlength="11"
              @input="changeInput($event)"
            />
          </view>

          <view class="code-input">
            <image style="width: 28rpx; height: 30rpx" :src="imgHost + 'dxSelect/fourthEdition/icon_login_code.png'"></image>
            <input type="number" v-model="smsCode" name="smsCode" placeholder="请输入验证码" placeholder-class="phClass" class="input" maxlength="6" />
            <view v-if="show" class="verification" @tap.stop="GetsmsCode">获取验证码</view>
            <view v-else class="verification sendout">已发送（{{ count }}S）</view>
          </view>
        </form>
      </view>

      <view class="tip">
        <label class="radio" style="display: inline-block; transform: scale(0.6)">
          <radio value="r1" :checked="isChecked" color="#1D755C" @click="changeischecked" />
        </label>
        我已阅读并同意
        <text @click="goweburl('https://document.dxznjy.com/applet/agreeon/useragreement.html')">《用户服务协议》、</text>
        <text @click="goweburl('https://document.dxznjy.com/applet/agreeon/privacypolicy.html')">《隐私政策》</text>
      </view>

      <button class="phone-btn" @tap.stop="getUserInfo">登录</button>
    </view>
    <!-- 验证数字弹窗 -->

    <!-- <uni-popup ref="popup" border-radius="10px 10px 0 0"> -->
    <view v-if="codeIsShow" style="height: 100vh; width: 100vw; background-color: rgba(0, 0, 0, 0.75); position: fixed; z-index: 199; top: 0; left: 0" @click.prevent="closePopup">
      <view class="code" @click.stop="clear">
        <view style="display: flex; align-items: center; height: 80rpx; padding: 0 48rpx; justify-content: space-between">
          <view style="font-weight: bold; font-size: 26rpx">请输入图形验证码</view>
          <view @click.stop="closePopup">
            <uni-icons type="clear" size="28" color="#B1B1B1"></uni-icons>
          </view>
        </view>
        <view>
          <pt-images-verification
            :uuid="uuid"
            :imgHeight="codeObj.imgHeight"
            :imgWidth="codeObj.imgWidth"
            :left="codeObj.top"
            :bgImg="codeObj.bgImg"
            :maskImg="codeObj.maskImg"
            @refresh="refresh"
            @success="success"
          ></pt-images-verification>
        </view>
      </view>
    </view>
    <!-- </uni-popup> -->
  </view>
</template>

<script>
  import Superman from '@/common/superman.js';
  import UniNavBar from '@/components/uni-nav-bar/uni-nav-bar.vue';
  import ptImagesVerification from '../components/pt-images-verification.vue';
  export default {
    components: {
      UniNavBar,
      ptImagesVerification
    },
    data() {
      return {
        codeObj: {
          bgImg: '',
          maskImg: '',
          imgHeight: '',
          imgWidth: '',
          top: 0 // 凹陷区距离背景图左边的距离
        },
        codeIsShow: false,
        roleArray: [],
        roleSec: '',
        roleindex: 0,
        imgHost: getApp().globalData.imgHost,
        mobile: '',
        smsCode: '',
        MemberToken: '',
        wxCode: '',
        userInfo: {},
        count: 60,
        show: true, // 是否显示倒计时
        showBuyMember: false,
        showParentVipBuyMember: false,
        pwd: '',
        codeBtn: {
          text: '获取验证码',
          waitingCode: false,
          count: this.seconds
        },
        // isRegist: false, // 是否是注册
        isChecked: false,
        lastactive: false,
        isLogin: 'islogin', //是否登陆
        encryptedData: '',
        iv: '',
        code: '',
        token: '',
        userId: '',
        firstLogin: false,
        count: '',
        imgHost: getApp().globalData.imgsomeHost,
        numPic: '',
        Verification: '',
        randomNumber: '',
        uuid: ''
      };
    },
    watch: {
      mobile(val) {
        if (val.length == 11) {
          this.lastactive = true;
        } else {
          this.lastactive = false;
        }
      },
      smsCode(val) {
        if (val.length == 6) {
          this.lastactive = true;
        } else {
          this.lastactive = false;
        }
      }
    },

    onLoad() {
      setTimeout(() => {
        // this.$refs.hi.set('831')
      });
    },
    onShow() {},

    methods: {
      refresh() {
        // this.closePopup();
        this.getNum();
      },
      success(e) {
        this.closePopup();
        this.confirm(e);
      },
      closePopup() {
        // if (this.stop) return;
        this.codeIsShow = false;
      },
      clear(e) {},
      generateUUID() {
        var d = new Date().getTime(); //Timestamp
        // var d2 = (performance && performance.now && performance.now() * 1000) || 0; //Time in microseconds since page-load or 0 if unsupported
        return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
          var r = Math.random() * 16; //random number between 0 and 16
          // if (d > 0) {
          //Use timestamp until depleted
          r = (d + r) % 16 | 0;
          d = Math.floor(d / 16);
          // } else {
          // log
          // //Use microseconds since page-load if supported
          // r = (d2 + r) % 16 | 0;
          // d2 = Math.floor(d2 / 16);
          // }
          return (c === 'x' ? r : (r & 0x3) | 0x8).toString(16);
        });
      },
      getNum() {
        let that = this;
        return new Promise((resolve, reject) => {
          this.uuid = this.generateUUID();
          that.$httpUser.get('new/security/captcha/image/slide?uuid=' + that.uuid).then((res) => {
            if (res.data.success) {
              let a = res.data.data;
              that.codeObj.maskImg = 'data:image/png;base64,' + a.cutImage;
              that.codeObj.bgImg = 'data:image/png;base64,' + a.oriImage;
              that.codeObj.imgWidth = a.cutImageWidth / 3.2 + '%';
              that.codeObj.imgHeight = a.cutImageHeight / 1.6 + '%';
              // that.codeObj.left = a.ypos;
              that.codeObj.top = a.ypos / 1.6 + 1;
              resolve();
            }
          });
        });
      },
      confirm(e) {
        let that = this;

        that.$httpUser.post('zx/common/checkRegister?mobile=' + that.mobile).then((res) => {
          if (res.data.status == 1) {
            that.$httpUser.post('new/security/sms/' + that.mobile + '?code=' + e + '&uuid=' + that.uuid).then((res) => {
              if (res.data.success) {
                this.$util.alter('发送成功,请注意查收');
                this.downFun(); // 倒计时执行事件
              } else {
                this.$util.alter(res.data.message);
              }
            });
          } else {
            that.$util.alter(res.data.message);
          }
        });
      },

      back() {
        uni.navigateBack();
      },
      //input输入
      changeInput(e) {
        console.log(e);
        this.mobile = e.target.value;
      },
      //验证表单
      validateForm: function () {
        if (this.mobile == '') {
          this.$util.alter('手机号码不能为空');
          return false;
        }
        if (this.mobile.length != 11) {
          this.$util.alter('手机号码必须是11位');
          return false;
        }
        if (this.smsCode == '') {
          this.$util.alter('验证不能为空');
          return false;
        }
        if (this.smsCode.length != 6) {
          this.$util.alter('验证码必须是6位');
          return false;
        }
        if (!this.isChecked) {
          this.$util.alter('请阅读并勾选下方协议');
          return false;
        }

        return true;
      },

      finishedOne(val) {
        this.smsCode = val;
        console.log(this.smsCode);
      },

      //发送手机校验码
      async GetsmsCode(e) {
        var that = this;
        if (that.mobile.length != 11) {
          that.$util.alter('请输入登录手机号');
          return false;
        }
        await this.getNum();
        this.codeIsShow = true;
      },

      // 倒计时执行事件
      downFun() {
        let TIME_COUNT = 60;
        if (!this.timer) {
          this.count = TIME_COUNT;
          this.show = false;
          this.timer = setInterval(() => {
            if (this.count > 0 && this.count <= TIME_COUNT) {
              this.count--;
            } else {
              this.show = true;
              clearInterval(this.timer);
              this.timer = null;
            }
          }, 1000);
        }
      },

      // 登录获取用户信息
      getUserInfo() {
        if (!this.validateForm()) {
          return false;
        }
        this.getkey();
      },

      //wx 获取key
      getkey() {
        console.log('4444444');
        var that = this;
        uni.login({
          provider: 'weixin',
          success: (res) => {
            that.code = res.code;
            if (that.code) {
              that.$httpUser
                .get('zx/common/decodeWechat', {
                  code: that.code,
                  mobile: that.mobile,
                  shareId: uni.getStorageSync('referee_id') || ''
                })
                .then((request) => {
                  let key = request.data.data.key;
                  if (key) {
                    that.getLogin(key);
                    //埋点-登录按钮
                    // #ifdef MP-WEIXIN
                    getApp().sensors.track('loginClick', {
                      name: '登录按钮'
                    });
                    // #endif
                  } else {
                    that.$util.alter(res.data.message);
                  }
                });
            }
          },
          fail: (err) => {
            console.log(err);
          }
        });
      },

      //wx 登录
      getLogin(key) {
        var that = this;
        uni.showLoading();
        that.$httpUser
          .get('zx/common/ifFirstLogin', {
            mobile: this.mobile
          })
          .then((res) => {
            this.firstLogin = res.data.data;
          });
        that.$httpUser
          .get('new/security/login/mini', {
            wxCode: key,
            username: that.mobile,
            role: '9',
            smsCode: that.smsCode
          })
          .then((res) => {
            console.log(res);
            uni.hideLoading();
            if (res.data.success) {
              uni.setStorageSync('token', res.data.data.token);
              that.loginHandle();
            } else {
              that.$util.alter(res.data.message);
            }
          });
      },
      getLocalIPAddress() {
        return new Promise((resolve, reject) => {
          wx.getLocalIPAddress({
            success(res) {
              resolve(res.localip);
            },
            fail(err) {
              reject(err);
            }
          });
        });
      },
      async loginHandle() {
        let that = this;
        try {
          that.localip = await that.getLocalIPAddress();
        } catch (e) {
          //TODO handle the exception
        }

        let userinfo = await Superman.getUserInfo();
        let date = new Date();
        let year = date.getFullYear();
        let month = (date.getMonth() + 1).toString().padStart(2, '0');
        let day = date.getDate().toString().padStart(2, '0');
        date = `${year}-${month}-${day}`;
        date = new Date(date);
        const date2 = new Date(userinfo.expireTime);
        const diffTime = date2 - date;
        const diffDays = diffTime / (1000 * 60 * 60 * 24);
        /** 家长会员过期 */
        if (userinfo.parentMemberEndTime) {
          const vipDate2 = new Date(userinfo.parentMemberEndTime);
          const vipDiffTime = vipDate2 - date;
          const vipDiffDays = vipDiffTime / (1000 * 60 * 60 * 24);
          if (vipDiffDays <= 30) {
            this.showParentVipBuyMember = true;
            uni.setStorageSync('showParentVipBuyMember', this.showParentVipBuyMember);
          } else {
            this.showParentVipBuyMember = false;
            uni.setStorageSync('showParentVipBuyMember', this.showParentVipBuyMember);
          }
        }
        if (diffDays <= 30) {
          this.showBuyMember = true;
          uni.setStorageSync('showBuyMember', this.showBuyMember);
        } else {
          this.showBuyMember = false;
          uni.setStorageSync('showBuyMember', this.showBuyMember);
        }
        uni.setStorageSync('user_id', userinfo.userId);
        uni.setStorageSync('merchantCode', userinfo.merchantCode);
        uni.setStorageSync('identityType', userinfo.identityType);
        uni.setStorageSync('parentMemberType', userinfo.parentMemberType);
        let shareid = uni.getStorageSync('shareId') ? uni.getStorageSync('shareId') : '';
        let activityid = uni.getStorageSync('activityId') ? uni.getStorageSync('activityId') : '';
        uni.setStorageSync('phone', userinfo.mobile);
        uni.setStorageSync('localip', that.localip);
        that.$util.alter('登录成功');
        uni.removeStorageSync('current');
        uni.navigateBack({
          delta: 2
        });
        console.log(this.firstLogin, '是否第一次登陆');
        console.log(shareid, '2222');
        if (shareid === '' || this.firstLogin === false) {
          return;
        }
        console.log(activityid, '活动id');

        console.log(shareid, '2222');
        that.$httpUser.post('zx/wap/invite/saveInviteData', {
          userId: shareid,
          activityId: activityid,
          type: '1',
          inviteeOpenId: userinfo.openId,
          inviteeNickName: userinfo.nickName,
          inviteePhone: userinfo.mobile,
          ipAddress: that.localip
        });
        console.log(shareid, '邀请活动');
        console.log(userinfo.mobile, '邀请活动');
        console.log(userinfo.nickName, '邀请活动');
        console.log(userinfo.openId, '邀请活动');
        console.log(that.localip, '邀请活动ip');
      },

      // 存userCode
      // getuserId() {
      // 	let that = this;
      // 	httpUser.get('v2/mall/user/index', {
      // 		timestamp: new Date().getTime()
      // 	}).then(res => {
      // 		if (res.data.data.mobile != undefined) {
      // 			that.myinfo = res.data.data;
      // 			// console.log(that.myinfo);
      // 			that.isLogin = true;
      // 			uni.setStorageSync('userCode', that.myinfo.memberCode);
      // 		}
      // 		uni.navigateBack({
      // 			delta: 2
      // 		});
      // 	})
      // },

      // 同意已阅读用户服务协议
      changeischecked() {
        this.isChecked = !this.isChecked;
        if (this.mobile.length == 11 && this.smsCode.length == 6 && this.isChecked) {
          this.lastactive = true;
        } else {
          this.lastactive = false;
        }
      },

      // 跳转H5用户服务协议，隐私政策
      goweburl(url) {
        uni.navigateTo({
          url: `/pages/index/web?url=${url}`
        });
      }
    }
  };
</script>

<style lang="scss" scoped>
  .container {
    // text-align: center;
    padding: 0 80rpx;
  }
  .code {
    position: absolute;
    left: 50%;
    top: 50%;
    width: 96vw;
    background-color: #fff;
    transform: translate(-50%, -50%);
    padding: 20rpx 0 40rpx;
    border-radius: 28rpx;
  }
  .numVerification {
    padding: 20rpx;
    display: flex;
    justify-content: space-around;
    align-items: center;
    flex-direction: column;
    width: 600rpx;
    height: 600rpx;
    background-color: #fff; /* 红色 */
    border-radius: 20rpx;
  }
  .numPicBox {
    width: 400rpx;
    height: 200rpx;
  }
  .numPic {
    width: 400rpx;
    height: 200rpx;
  }
  .inputBox {
    background-color: #999999;
    width: 400rpx;
    height: 50rpx;
    border: 1rpx solid #999999;
  }
  .inputBox {
    width: 300rpx;
    height: 80rpx;
    border-radius: 10rpx;
  }
  .uni-input {
    width: 300rpx;
    height: 80rpx;
    border-radius: 10rpx;
  }
  .footer_box {
    width: 200rpx;
    height: 80rpx;
    border-radius: 30rpx;
    display: flex;
    justify-content: center;
    align-items: center;
    background: linear-gradient(180deg, #88cfba 0%, #1d755c 100%);
  }

  .wechatapp {
    margin-top: 160rpx;
    margin-bottom: 100rpx;
  }

  .wechatapp .header {
    width: 100%;
    margin: 0rpx auto 0;
  }

  .auth-title {
    color: #585858;
    font-size: 40rpx;
    margin-bottom: 40rpx;
  }

  .title {
    color: #000;
    font-weight: 700;
    font-size: 40rpx;
  }

  .tip {
    display: flex;
    align-items: center;
    font-size: 24rpx;
    text-align: center;
    color: #999999;
  }

  .login-top {
    display: block;
    width: 240rpx;
    height: 240rpx;
    margin: 0 auto !important;
  }

  .login-box {
    padding-bottom: 30rpx;
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr=#7Fd2f9f6, endColorstr=#7Fd2f9f6);
    border-radius: 10rpx;
  }

  .phone-input {
    background: #fff;
    border-radius: 45rpx;
    height: 90rpx;
    font-size: 28rpx;
    color: #000;
    display: flex;
    padding-left: 20rpx;
    align-items: center;
    border: 3rpx solid #ececea;
  }

  .code-input {
    background: #fff;
    border-radius: 45rpx;
    font-size: 28rpx;
    color: #000;
    height: 90rpx;
    display: flex;
    justify-content: space-between;
    padding: 0 20rpx;
    margin-top: 30rpx;
    align-items: center;
    border: 3rpx solid #ececea;
  }

  .verification {
    color: #ea6531;
    font-size: 26rpx;
    padding-left: 18rpx;
    border-left: 1px solid #c8c8c8;
    float: right;
  }

  .sendout {
    width: 180rpx;
    color: #999999;
  }

  /deep/.phone-btn {
    width: 100%;
    height: 90rpx;
    line-height: 90rpx;
    border-radius: 45rpx;
    font-size: 30rpx;
    margin-top: 76rpx;
    color: #fff;
    background: linear-gradient(180deg, #88cfba 0%, #1d755c 100%);
  }

  // .phClass {
  // 	color: #999999;
  // 	font-size: 20rpx;
  // }

  .auth-subtitle {
    color: #888;
    margin-bottom: 88rpx;
  }

  .login-btn {
    border: none;
    height: 88rpx;
    line-height: 88rpx;
    background: #04be01;
    /* #ifdef MP-ALIPAY */
    background: #1890ff;
    /* #endif */
    color: #fff;
    font-size: 11pt;
    border-radius: 999rpx;
  }

  .login-btn::after {
    display: none;
  }

  .login-btn.button-hover {
    box-shadow: inset 0 5rpx 30rpx rgba(0, 0, 0, 0.15);
  }

  .login-cancle {
    text-align: center;
    border: none;
    height: 88rpx;
    line-height: 88rpx;
    color: #a8a8a8;
    font-size: 11pt;
    border-radius: 999rpx;
  }

  .btn_flex {
    display: flex;
    justify-content: space-around;
    margin-top: 750rpx;
  }

  /deep/.card-btn {
    width: 100%;
    height: 80rpx;
    line-height: 80rpx;
    border-radius: 40rpx;
    font-size: 30rpx;
    padding-left: 14rpx;
    padding-right: 14rpx;
    color: #fff;
    background-color: #28c445;
  }

  /deep/.wx-btn {
    width: 35%;
    height: 80rpx;
    line-height: 80rpx;
    border-radius: 10rpx;
    font-size: 32rpx;
    padding-left: 14rpx;
    padding-right: 14rpx;
    color: #fff;
    background-color: #169bd5;
  }

  .nav-bar {
    position: fixed;
    width: 100%;
    z-index: 999;
  }

  /deep/.uni-nav-bar-text {
    font-size: 34rpx !important;
    font-weight: bold;
  }

  /deep/.uni-navbar__header {
    background-color: transparent !important;
  }

  /deep/.uni-navbar__content {
    background-color: transparent !important;
  }
</style>
