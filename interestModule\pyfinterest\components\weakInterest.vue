<template>
  <view class="container" :style="{ height: windowHeight + 'rpx' }">
    <!-- 拖拽盒子 -->
    <view class="word">
      <view style="width: 144rpx; height: 144rpx; margin-right: 10rpx">
        <image src="https://document.dxznjy.com/course/ae33333815ed4628a04f1271d58e13dd.png" style="width: 100%; height: 100%"></image>
      </view>
      <view class="wordTitle">
        {{ word.wordSyllable }}
      </view>
    </view>
    <view
      class="drag-box"
      :style="{
        left: dragBox.x + 'px',
        top: dragBox.y + 'px',
        borderColor: isDragging ? '#4CAF50' : '#2196F3'
      }"
      @touchstart="onDragStart"
      @touchmove="onDragMove"
      @touchend="onDragEnd"
    >
      {{ darg[0] }}
    </view>
    <view
      class="drag-box"
      :style="{
        left: dragBox2.x + 'px',
        top: dragBox2.y + 'px',
        borderColor: isDragging2 ? '#4CAF50' : '#2196F3'
      }"
      @touchstart="onDragStart2"
      @touchmove="onDragMove2"
      @touchend="onDragEnd2"
    >
      {{ darg[1] }}
    </view>
    <!-- 静态盒子 -->
    <view class="static-box" id="postion" :style="{ justifyContent: right ? 'start' : '' }">
      <view
        v-for="(box, index) in weakList"
        :key="index"
        class="static-boxs"
        @click="checkOut(index, box)"
        :class="box.class"
        :id="!box.disabled ? 'box' + index : ''"
        :style="{
          color: box.disabled ? '#c9c9c9' : '#000',
          backgroundColor: isColliding(index) || isColliding2(index) ? '#ff0000' : ''
        }"
      >
        <view class="small" v-if="box.check == 'ur'">{{ darg[0] }}</view>
        <view class="small" v-if="box.check == 'i'">{{ darg[1] }}</view>
        <view class="Stress" v-if="box.isStress"></view>
        <view class="SecondaryStress" v-if="box.isSecondaryStress"></view>
        {{ box.text.replace(/\[.*\]$/, '') }}
      </view>
    </view>
    <view class="rightItems" v-if="isOK">
      <view class="rightItem" v-for="(item, index) in weakList" :class="!item.disabled ? 'right' : 'nocheck'">
        {{ item.text.replace(/\[.*\]$/, '') }}
        <view class="small" v-if="!item.disabled">{{ item.weak }}</view>
        <view class="Stress" v-if="item.isStress"></view>
        <view class="SecondaryStress" v-if="item.isSecondaryStress"></view>
      </view>
    </view>
    <view class="btn" @click="btnOK">{{ isOK ? (end ? '提交' : '下一题') : '确定' }}</view>
    <!-- 引导 -->
    <uni-popup ref="guideOne" type="bottom" :maskClick="true" :classBG="''">
      <view style="position: relative" class="test-bg">
        <image mode="aspectFit" style="width: 100%; height: 100%" src="https://document.dxznjy.com/course/851a77654520488f89507ee38267b0d8.png"></image>
        <image class="guide_btn_next" @click="guideNext(1)" src="https://document.dxznjy.com/applet/newInteresting/interesting_guide_next.png"></image>
      </view>
    </uni-popup>
    <uni-popup ref="guideTwo" type="bottom" :maskClick="true" :classBG="''">
      <view style="position: relative" class="test-bg">
        <image mode="aspectFit" style="width: 100%; height: 100%" src="https://document.dxznjy.com/course/1a5417eb27ec43179b2b1546ae47f6ca.png"></image>
        <image class="guide_btn_next" @click="guideNext(2)" src="https://document.dxznjy.com/applet/newInteresting/interesting_guide_next.png"></image>
      </view>
    </uni-popup>
    <uni-popup ref="guideEnd" type="bottom" :maskClick="true" :classBG="''">
      <view style="position: relative" class="test-bg">
        <image mode="aspectFit" style="width: 100%; height: 100%" @click="guideClose()" src="https://document.dxznjy.com/course/de2f802dba1c47ba96da9886978bd25d.png"></image>
      </view>
    </uni-popup>
  </view>
</template>

<script>
  export default {
    data() {
      return {
        weakList: [],
        darg: ['ur', 'i'],
        dragBox: { x: 109, y: 400, width: 52, height: 47 },
        dragBox2: { x: 216, y: 400, width: 52, height: 47 },
        isDragging: false,
        isDragging2: false,
        screenHeight: 0,
        screenWidth: 0,
        startX: 0,
        startY: 0,
        startX2: 0,
        startY2: 0,
        windowWidth: 0,
        windowHeight: 0,
        status: 0,
        isOK: false,
        right: false,
        Guide: uni.getStorageSync('weakGuide'),
        list: [],
      };
    },
    props: {
      word: {
        type: Object,
        default: {}
      },
      end: {
        type: Boolean,
        default: false
      },
      count: {
        type: Number,
      }
    },
    // onReady() {
    //   let that = this;

    //   uni.getSystemInfo({
    //     success: (res) => {
    //       this.screenHeight = res.windowHeight * 2;
    //       this.screenWidth = res.windowWidth * 2;
    //       that.windowWidth = res.windowWidth;
    //       that.windowHeight = res.windowHeight * (750 / res.windowWidth) - 180;
    //       // 可使用窗口高度，将px转换rpx
    //     }
    //   });
    //   this.weakList.forEach((e, i) => {
    //     if (!e.disabled) {
    //       this.setarea(i);
    //     }
    //   });
    // },
    watch: {
      count: {
        handler(newVal) {
          this.$emit('setTitle', 4);
          this.weakList.forEach((e, i) => {
            if (!e.disabled) {
              this.setarea(i);
            }
          });
          this.isOK = false;
          this.init();
          this.goNext();
        },
        immediate: true,
      },
    },
    created() {
      this.$emit('setTitle', 4);
      this.weakList.forEach((e, i) => {
        if (!e.disabled) {
          this.setarea(i);
        }
      });
      this.init();
      this.goNext();
    },
    mounted() {
      let that = this;

      uni.getSystemInfo({
        success: (res) => {
          this.screenHeight = res.windowHeight * 2;
          this.screenWidth = res.windowWidth * 2;
          that.windowWidth = res.windowWidth;
          that.windowHeight = res.windowHeight * (750 / res.windowWidth) - 180;
          // 可使用窗口高度，将px转换rpx
        }
      });
      

      if (!this.Guide) {
        this.$refs.guideOne.open();
      } else if (this.Guide == 1) {
        this.$refs.guideTwo.open();
      } else if (this.Guide == 2) {
        this.$refs.guideEnd.open();
      }
    },
    methods: {
      guideNext(e) {
        uni.setStorageSync('weakGuide', e);
        if (e == 1) {
          this.$refs.guideOne.close();
          this.$refs.guideTwo.open();
        } else {
          this.$refs.guideTwo.close();
          this.$refs.guideEnd.open();
        }
      },
      guideClose() {
        uni.setStorageSync('weakGuide', 3);
        this.$refs.guideEnd.close();
      },
      goNext() {
        if (this.word.syllableList.find((e) => e.wordSyllableType == 3 && e.syllableList.length)) {
          this.goNum = 5;
        } else {
          this.goNum = 6;
          console.log(3333);
          this.$emit('goNum', this.goNum);
        }
      },
      btnOK() {
        if (this.isOK) {
          this.$emit('next', this.status, this.goNum);
        } else {
          if (!this.weakList.find((e) => e.type == 1)) {
            return uni.showToast({
              icon: 'none',
              title: '请选择'
            });
          }
          this.isOK = true;
          this.weakList.forEach((e) => {
            if (!e.disabled && e.check === e.weak && e.type == 1) {
              e.type = 2;
              e.class = 'right';
            }
            if (!e.disabled && e.check != e.weak && e.type == 1) {
              e.type = 3;
              e.class = 'error';
            }
            if (!e.disabled && e.type == 0) {
              e.type = 3;
              e.class = 'error';
            }
          });
          this.$forceUpdate();
          if (this.weakList.find((e) => e.type == 3)) {
            this.status = 0;
          } else {
            this.status = 1;
          }
        }
      },
      checkOut(i, e) {
        if (e.disabled) return;
        e.check = '';
        e.type = 0;
        e.class = '';
        this.$forceUpdate();
      },
      init() {
        this.weakList = this.addSuffixToRepeatedSyllables(this.word.splitList).map((e) => {
          return {
            text: e.wordSyllable,
            disabled: true
          };
        });
        this.weakList.forEach((e, i) => {
          this.word.syllableList.forEach((p, o) => {
            if (p.wordSyllable === e.text && p.wordSyllableType == 3) {
              this.weakList[i].isStress = true;
            }
            if (p.wordSyllable === e.text && p.wordSyllableType == 7) {
              this.weakList[i].isSecondaryStress = true;
            }
            if (p.wordSyllable === e.text && p.wordSyllableType == 2 && p.syllableList.length) {
              this.weakList[i].disabled = false;
              this.weakList[i].type = 0;
              if (p.syllableList[0].wordSyllableType == 5 || p.syllableList[0].wordSyllableType === '0') {
                this.weakList[i].weak = 'ur';
              }
              if (p.syllableList[0].wordSyllableType == 8) {
                this.weakList[i].weak = 'i';
              }
            }
          });
        });
      },
      addSuffixToRepeatedSyllables(objects) {
        // 创建一个对象来跟踪每个syllable出现的次数
        const syllableCounts = {};
        return objects.map((obj, index) => {
          const wordSyllable = obj.wordSyllable;
          // 如果syllable已经出现过，则增加计数并添加后缀
          if (syllableCounts[wordSyllable]) {
            syllableCounts[wordSyllable]++;
            return {
              ...obj,
              wordSyllable: `${wordSyllable}[${syllableCounts[wordSyllable]}]`
            };
          } else {
            // 如果是第一次出现，则记录该syllable并设置计数为1
            syllableCounts[wordSyllable] = 1;
            return obj;
          }
        });
      },
      setarea(e) {
        let that = this;
        setTimeout(() => {
          const query = uni.createSelectorQuery().in(this);
          query
            .select('#box' + e)
            .boundingClientRect((data) => {
              console.log(data);
              // let res = JSON.parse(data);
              that.weakList[e].x = data.left;
              that.weakList[e].y = data.top - 88;
              that.weakList[e].width = data.width;
              that.weakList[e].height = data.height;
            })
            .exec();
        }, 50);
       
      },
      onDragStart(e) {
        this.isDragging = true;
        this.startX = e.touches[0].clientX;
        this.startY = e.touches[0].clientY;
      },
      onDragMove(e) {
        if (!this.isDragging || this.isOK) return;
        const deltaX = e.touches[0].clientX - this.startX;
        const deltaY = e.touches[0].clientY - this.startY;
        this.dragBox.x += deltaX;
        this.dragBox.y += deltaY;
        this.startX = e.touches[0].clientX;
        this.startY = e.touches[0].clientY;
        // 限制边界
        this.dragBox.x = Math.max(0, Math.min(this.dragBox.x, this.windowWidth - this.dragBox.width));
        this.dragBox.y = Math.max(0, Math.min(this.dragBox.y, this.windowHeight - this.dragBox.height));
      },
      onDragEnd() {
        this.isDragging = false;
        this.weakList.forEach((e, i) => {
          if (!e.disabled) {
            if (this.isColliding(i)) {
              e.check = 'ur';
              e.type = 1;
              e.class = 'check';
            }
          }
        });
        setTimeout(() => {
          this.dragBox = { x: 109, y: 400, width: 52, height: 47 };
        }, 200);
      },
      isColliding(index) {
        const dragBox = this.dragBox;
        const box = this.weakList[index];
        const dragBoxCenterX = dragBox.x + dragBox.width / 2;
        const dragBoxCenterY = dragBox.y + dragBox.height / 2;
        // 判断中心点是否在目标盒子内
        return dragBoxCenterX >= box.x && dragBoxCenterX <= box.x + box.width && dragBoxCenterY >= box.y && dragBoxCenterY <= box.y + box.height;
      },
      onDragStart2(e) {
        this.isDragging2 = true;
        this.startX2 = e.touches[0].clientX;
        this.startY2 = e.touches[0].clientY;
      },
      onDragMove2(e) {
        if (!this.isDragging2 || this.isOK) return;
        const deltaX = e.touches[0].clientX - this.startX2;
        const deltaY = e.touches[0].clientY - this.startY2;
        this.dragBox2.x += deltaX;
        this.dragBox2.y += deltaY;
        this.startX2 = e.touches[0].clientX;
        this.startY2 = e.touches[0].clientY;
        // 限制边界
        this.dragBox2.x = Math.max(0, Math.min(this.dragBox2.x, this.windowWidth - this.dragBox2.width));
        this.dragBox2.y = Math.max(0, Math.min(this.dragBox2.y, this.windowHeight - this.dragBox2.height));
      },
      onDragEnd2() {
        if (this.isOK) return;
        this.isDragging2 = false;
        this.weakList.forEach((e, i) => {
          if (!e.disabled) {
            if (this.isColliding2(i)) {
              e.check = 'i';
              e.type = 1;
              e.class = 'check';
            }
          }
        });
        console.log(this.weakList);
        setTimeout(() => {
          this.dragBox2 = { x: 216, y: 400, width: 52, height: 47 };
        }, 200);
      },
      isColliding2(index) {
        const dragBox = this.dragBox2;
        const box = this.weakList[index];
        console.log(dragBox, box, 'isislolo');
        const dragBoxCenterX = dragBox.x + dragBox.width / 2;
        const dragBoxCenterY = dragBox.y + dragBox.height / 2;
        // 判断中心点是否在目标盒子内
        return dragBoxCenterX >= box.x && dragBoxCenterX <= box.x + box.width && dragBoxCenterY >= box.y && dragBoxCenterY <= box.y + box.height;
      }
    }
  };
</script>

<style scoped lang="scss">
  .test-bg {
    padding-top: env(safe-area-inset-top);
    // padding-bottom: env(safe-area-inset-bottom);
    height: 100vh;
    box-sizing: border-box;
  }
  .SecondaryStress {
    position: absolute;
    width: 4rpx;
    height: 22rpx;
    line-height: 1;
    background-color: #555555;
    bottom: 14rpx;
    left: 22rpx;
  }
  .btn {
    width: 632rpx;
    height: 84rpx;
    background: #89c844;
    box-shadow: 0 16rpx 2rpx -2rpx rgba(99, 180, 11, 1);
    border-radius: 42rpx;
    text-align: center;
    line-height: 84rpx;
    position: fixed;
    bottom: 50rpx;
    left: 50%;
    transform: translateX(-50%);
  }
  .nocheck {
    color: #c9c9c9 !important;
  }
  .container {
    overflow: hidden;
    position: relative;
    width: 100vw;
    /* height: 100vh; */
  }
  .guide_btn_next {
    position: absolute;
    bottom: 57rpx;
    right: 64rpx;
    width: 269rpx;
    height: 142rpx;
  }

  .guide_btn_close {
    position: absolute;
    bottom: 57rpx;
    right: 64rpx;
    width: 269rpx;
    height: 142rpx;
  }
  .Stress {
    position: absolute;
    width: 4rpx;
    height: 22rpx;
    line-height: 1;
    background-color: #555555;
    top: 10rpx;
    left: 22rpx;
  }
  .word {
    margin-top: 60rpx;
    height: 144rpx;
    padding-left: 32rpx;
    display: flex;
    align-items: center;
    margin-bottom: 90rpx;
  }
  .wordTitle {
    width: 474rpx;
    height: 110rpx;
    padding-left: 14rpx;
    background: url('https://document.dxznjy.com/course/a37e49a58c724f7ca8d2fbd8ce85252d.png') no-repeat;
    background-size: contain;
    text-align: center;
    line-height: 100rpx;
    font-size: 40rpx;
    color: #555555;
  }
  .rightItems {
    position: fixed;
    width: 686rpx;
    bottom: 200rpx;
    max-height: 286rpx;
    overflow-y: auto;
    left: 50%;
    transform: translateX(-50%);
    // height: 190rpx;
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    align-items: center;
    border-radius: 32rpx;
    padding: 20rpx 0;
    background-color: rgba(255, 255, 255, 0.51);
    .rightItem {
      position: relative;
      background-color: #fff;
      color: #555555;
      font-size: 36rpx;
      height: 92rpx;
      line-height: 92rpx;
      margin-right: 8rpx;
      border-radius: 16rpx;
      margin-bottom: 10rpx;
      padding: 0 40rpx;
    }

    .right {
      background-color: #4bb051;
      color: #ffffff;
    }
  }

  .drag-box {
    position: absolute;
    height: 92rpx;
    width: 102rpx;
    box-sizing: border-box;
    text-align: center;
    line-height: 92rpx;
    border-radius: 24rpx;
    background-color: #ffffff;
    box-shadow: 0 16rpx 2rpx -2rpx rgba(97, 170, 244, 1);
    z-index: 2;
  }
  .static-box {
    transition: background-color 0.3s;
    display: flex;
    padding: 0 32rpx;
    flex-wrap: wrap;
    justify-content: center;
    .right {
      background-color: #4bb051;
      box-shadow: 0 16rpx 2rpx -2rpx rgba(57, 141, 61, 1);
      color: #ffffff !important;
    }
  }

  .static-boxs {
    position: relative;
    height: 92.11rpx;
    min-width: 100rpx;
    padding: 0 30rpx;
    box-sizing: border-box;
    font-size: 36rpx;
    border: 2rpx solid transparent;
    // border: 2rpx solid #000;
    margin-right: 18rpx;
    text-align: center;
    background-color: #ffffff;
    color: #555555;
    border-radius: 24rpx;
    line-height: 90rpx;
    box-shadow: 0 16rpx 2rpx -2rpx rgba(97, 170, 244, 1);
    margin-bottom: 32rpx;
    // &:nth-child(6n) {
    //   margin-right: 0;
    // }
  }
  .small {
    position: absolute;
    height: 30rpx;
    width: 100%;
    top: 6rpx;
    right: 8rpx;
    line-height: 1;
    padding-right: 8rpx;
    text-align: right;
    font-size: 24rpx;
    color: #555;
  }
  .check {
    border: 2rpx solid #ffc800;
    background-color: #fff7db;
  }

  .error {
    background-color: #ffa332;
    box-shadow: 0 16rpx 2rpx -2rpx rgba(231, 133, 13, 1);
    color: #ffffff !important;
  }
</style>
