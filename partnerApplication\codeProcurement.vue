<template>
	<view style="height: 100vh;background-color:#F5F8FA;">
		<view class="flex codeTop plr-35 f-28">
			<view class="">
				<view class="mt-28 mb-15">
					剩余合伙人码：{{partnerInvitationCodeNum}}
				</view>
				<view class="" v-if="merchantType=='6'">
					剩余俱乐部码：{{clubInvitationCodeNum}}
				</view>
			</view>
			<view style="color: #339378;" @tap="goBuyProcurement">
				采购邀请码
			</view>
		</view>
		<view class="detailTop">
			<view :class="detailsIndex===0?'details':''" @tap="clickCode(0)">
				全部
			</view>
			<view :class="detailsIndex===1?'details':''" @tap="clickCode(1)">
				待付款
			</view>
			<view :class="detailsIndex===2?'details':''" @tap="clickCode(2)">
				已完成
			</view>
		</view>
		<view>
			<view class="f-28 p-20" v-if="pointsList.data&&pointsList.data.length>0">
				<view class=" radius-16 mb-20" v-for="(item,index) in pointsList.data" :key="index">
					<view class="bg-ff p-10 sizing">
						<view class="flex-s lh-90" style="line-height: 90rpx;border-bottom: 2rpx solid #d0d0d0;">
							<view class="type">
								{{item.id}}
							</view>
							<view class="f-24 typeStatus t-c">
								<text v-if="item.status==1"
									class="f-26 t-c radius-8 tobe_paid button_right_css">未支付</text>
								<text v-if="item.status==2" class="f-26 radius-8 t-c paid button_right_css">支付中</text>
								<text v-if="item.status==3"
									class="f-26 radius-8 t-c button_right_css completed">已支付</text>
								<text v-if="item.status==4" class="c-ff f-26 t-c button_right_css refunded">支付失败</text>
							</view>
						</view>
						<view class="mtb-25">
							采购类型: {{item.procureType}}
						</view>
						<view class="">
							订购内容: {{item.procureContent}}
						</view>
						<view class="mtb-25">
							数量(个): {{item.procureNum}}
						</view>
						<view class="">
							订购单价(元): {{item.amount}}
						</view>
						<view class="mtb-25">
							总金额(元): {{item.totalAmount}}
						</view>
					</view>
				</view>
			</view>
			<view v-else class="curriculum_css_no pt-30 pb-55 f-28">
				<image class="curriculum_image"
					src="https://document.dxznjy.com/course/ac587707bf314badadb28a158852c77d.png"></image>
				<view class="c-66 f-24 mtb-25">暂无明细</view>
			</view>
			<view v-if="no_more && pointsList.data != undefined && pointsList.data.length>0">
				<u-divider text="到底了"></u-divider>
			</view>
		</view>
	</view>
	</view>
</template>

<script>
	const {
		$http,
	} = require("@/util/methods.js")
	export default {
		data() {
			return {
				detailsIndex: 0,
				page: 1,
				no_more: false,
				merchantType: '',
				clubInvitationCodeNum: '',
				partnerInvitationCodeNum: '',
				pointsList: {},
				type: '',
				userCode: ''
			}
		},
		onLoad(option) {
			if (option != null) {
				this.merchantType = option.merchantType
				this.type = option.type
				this.userCode = option.userCode
			}
		},
		onShow() {
			this.detailsIndex = 0
			this.fetchinvitaCode()
			this.fetchProcureList()
		},
		onReachBottom() {
			if (this.detailsIndex == 0) {
				if (this.page >= this.pointsList.totalPages) {
					this.no_more = true
					return false;
				}
				this.fetchProcureList(true, ++this.page);
			}
			if (this.detailsIndex == 1) {
				if (this.page >= this.pointsList.totalPage) {
					this.no_more = true
					return false;
				}
				this.fetchProcureList(true, ++this.page, 1);
			}
			if (this.detailsIndex == 2) {
				if (this.page >= this.pointsList.totalPage) {
					this.no_more = true
					return false;
				}
				this.fetchProcureList(true, ++this.page, 3);
			}

		},
		methods: {
			//获取剩余邀请码
			async fetchinvitaCode() {
				const res = await $http({
					url: 'zxAdminCourse/web/invitation/codeNum',
					data: {
						merchantCode: this.userCode
					}
				})
				if (res) {
					this.partnerInvitationCodeNum = res.data.partnerInvitationCodeNum
					this.clubInvitationCodeNum = res.data.clubInvitationCodeNum
				}
			},
			// 获取采购单列表
			async fetchProcureList(isPage, page, status) {
				let parms = {
					pageNum: page || 1,
					pageSize: 10,
					merchantCode: this.userCode
				}
				if (this.merchantType == '5') {
					parms.merchantType = 2
				}
				if (this.merchantType == '6') {
					parms.merchantType = 3
				}
				if (status) {
					parms.status = status
				}
				const res = await $http({
					url: 'zxAdminCourse/web/invitation/purchaseList',
					data: parms
				})
				if (res) {
					if (isPage) {
						let old = this.pointsList.data;
						this.pointsList.data = [...old, ...res.data.data]
					} else {
						this.pointsList = res.data
					}
				}
			},
			clickCode(index) {
				this.detailsIndex = index
				if (this.detailsIndex == 0) {
					this.fetchProcureList()
				}
				if (this.detailsIndex == 1) {
					this.fetchProcureList(false, 1, 1)
				}
				if (this.detailsIndex == 2) {
					this.fetchProcureList(false, 1, 3)
				}
			},
			goBuyProcurement() {
				uni.navigateTo({
					url: '/clubApplication/buyProcurement?type=' + this.type + '&userCode=' + this.userCode
				})
			}
		}
	}
</script>

<style lang="scss" scoped>
	.codeTop {
		width: 100%;
		height: 150rpx;
		background-color: #F9FCFE;
		box-sizing: border-box;
	}

	.details {
		border-bottom: 4rpx solid #339378;
		font-weight: 700;
	}

	.pointsBox {
		height: 96rpx;
		background-color: #F9FCFE;
		display: flex;
		align-items: center;
	}

	.detailTop {
		margin: 20rpx 0;
		display: flex;
		justify-content: space-evenly;
	}

	.type {
		display: flex;
	}

	.bgf {
		width: 376rpx;
		height: 112rpx;
		margin: 0 auto;
		background: url('https://document.dxznjy.com/course/1e6afed624714ef4825a7dba609ccd58.png') no-repeat;
		background-size: 100%;
	}

	.curriculum_css_no {
		position: relative;
		width: 710rpx;
		margin: auto;
		margin-top: 400rpx;
		text-align: center;

		.curriculum_image {
			width: 122rpx;
			height: 114rpx;
			display: block;
			margin: 16rpx auto;
		}

		.curriculum_title {
			text-align: left;
		}
	}
/* 
	.typeStatus {
		width: 90rpx;
		line-height: 40rpx;
		background: rgba(255, 221, 167, 0.15);
		border-radius: 8rpx;
		border: 2rpx solid #FFDDA7;
		color: #FD9B2A;
	} */

	.tobe_paid {
		width: 90rpx;
		line-height: 40rpx;
		background: rgba(255, 221, 167, 0.15);
		border-radius: 8rpx;
		border: 2rpx solid #FFDDA7;
		color: #FD9B2A;
	}

	.paid {
		background-color: rgba(53, 168, 247, 0.15);
		width: 90rpx;
		line-height: 40rpx;
		border-radius: 8rpx;
		border: 2rpx solid #35A8F7;
		color: #35A8F7;
	}

	.completed {
	background-color:rgba(129, 226, 175, 0.15);
		width: 90rpx;
		line-height: 40rpx;
		border-radius: 8rpx;
		border: 2rpx solid #81E2AF;
		color: #81E2AF;
	}

	.refunded {
		background-color: rgba(56, 151, 145, 0.15);
		width: 90rpx;
		line-height: 40rpx;
		border-radius: 8rpx;
		border: 2rpx solid #389791;
		color: #389791;
	}
</style>