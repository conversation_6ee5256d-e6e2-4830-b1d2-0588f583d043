<template>
  <view>
    <u-navbar :title="titleNavbar" @rightClick="rightClick" :autoBack="true"></u-navbar>
    <view class="plr-30 pb-30 list_box">
      <!-- 讲义区域 -->
      <view v-if="listKnow.length > 0">
        <view v-for="(item, index) in listKnow" :key="index">
          <!-- 一级标题 -->
          <view class="bold f-36 m-10">{{ item.topic }}</view>
          <view class="bold f-28 m-10 ml-40" style="color: #555">{{ item.content }}</view>
        </view>
      </view>
      <view class="bg-ff radius-15 plr-20 mt-30 t-c flex-col" v-else :style="{ height: useHeight + 'rpx' }" style="position: relative">
        <image src="/static/index/<EMAIL>" mode="widthFix" class="mb-20 img_s"></image>
        <view style="color: #bdbdbd">暂无数据</view>
        <image src="/static/index/<EMAIL>" mode="widthFix" class="mb-20 img_s"></image>
      </view>
      <view class="bottom-buttons">
        <u-button shape="circle" @click="nextTopicHandle" :disabled="isNextButtonDisabled">下一个知识点</u-button>
        <u-button shape="circle" color="#428A6F" @click="viewTopicHandle">提交</u-button>
      </view>
    </view>
  </view>
</template>
<script>
  export default {
    data() {
      return {
        titleNavbar: '',
        startTime: 0, // 记录进入页面的时间
        //   knowledgeId: '',
        studentCode: '',
        listKnow: [],
        timePassed: 0, // 记录经过的时间（秒）
        nextKnowledgeId: '',
        reveiwId: '',
        isNextButtonDisabled: false,
        knowledgeName: '',
        idKnowledgeArray: [],
        hanoutId: '', // 讲义id
        nextHanoutId: '', // 下一条讲义id
        currentKnowledgeId: '', // 当前进度 id
        nextCurrentKnowledgeId: '', // 下一个当前进度 id
        hanoutReportId: '' // 讲义报告id
      };
    },
    onLoad(option) {
      this.studentCode = option.studentCode;
      // this.knowledgeId = option.knowledgeId
      this.reveiwId = option.id;
      this.getHandoutList();
      console.log(option.id, 'option.id');
      this.startTime = new Date().getTime(); // 记录进入页面的时间
    },
    methods: {
      async getHandoutList() {
        await this.$httpUser
          .get('dyf/wap/applet/knowledgeNote', {
            studentCode: this.studentCode,
            id: this.reveiwId
          })
          .then((res) => {
            let listData = JSON.parse(res.data.data.notes);
            this.titleNavbar = res.data.data.knowledgeName;
            this.listKnow = listData.noteData.content;
            this.currentKnowledgeId = res.data.data.id; // 当前进度 id
            this.nextKnowledgeId = res.data.data.nextProgressId;
            this.hanoutReportId = res.data.data.nextProgressId;
            this.hanoutId = res.data.data.knowledgeId;
            // 检查 nextKnowledgeId 是否存在
            if (!res.data.data.nextProgressId) {
              this.isNextButtonDisabled = true;
            }
          })
          .catch((error) => {
            this.$util.alter('讲义数据为空');
          });
      },
      async getHandoutListNext() {
        console.log('hanoutReportId', this.hanoutReportId);
        await this.$httpUser
          .get('dyf/wap/applet/knowledgeNote', {
            studentCode: this.studentCode,
            id: this.hanoutReportId
          })
          .then((res) => {
            let listData = JSON.parse(res.data.data.notes);
            this.listKnow = listData.noteData.content;
            this.titleNavbar = res.data.data.knowledgeName;
            this.nextCurrentKnowledgeId = res.data.data.id; // 当前进度 id
            this.nextHanoutId = res.data.data.knowledgeId;
            this.hanoutReportId = res.data.data.nextProgressId;
            // 检查 nextKnowledgeId 是否存在
            if (!res.data.data.nextProgressId) {
              this.isNextButtonDisabled = true;
            }
            this.knowledgeName = res.data.data.knowledgeName;
            const endTime = new Date().getTime();
            const currentTimePassed = (endTime - this.startTime) / 1000;
            const newItem = {
              id: this.nextCurrentKnowledgeId,
              useTime: currentTimePassed,
              knowledgeId: this.nextHanoutId
            };
            this.idKnowledgeArray.push(newItem);
          });
      },
      viewTopicHandle() {
        const endTime = new Date().getTime();
        this.timePassed = (endTime - this.startTime) / 1000; // 计算经过的时间（秒）

        const newItem = {
          id: this.currentKnowledgeId,
          useTime: this.timePassed,
          knowledgeId: this.hanoutId
        };
        this.idKnowledgeArray.push(newItem);

        const params = {
          studentCode: this.studentCode,
          idList: this.idKnowledgeArray
        };
        console.log(params, '复习数据');
        this.$httpUser.post(`dyf/wap/applet/antiForgettingSubmit`, params).then((res) => {
          if (res.data.data === null) {
            this.$util.alter('系统异常');
            return;
          }
          const knowledgeId = res.data.data;
          uni
            .redirectTo({
              url: `/antiAmnesia/antiForgetting/grammarReport?studentCode=${this.studentCode}&title=${this.knowledgeName}&reveiwId=${this.reveiwId}&hanoutId=${knowledgeId}`
            })
            .catch((error) => {
              console.error('Navigation error:', error);
            });
        });
      },
      nextTopicHandle() {
        this.getHandoutListNext();
      },
      formatTime(seconds) {
        // 将秒数转换为小时、分钟和秒
        // const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        const secs = seconds % 60;

        // 格式化小时、分钟和秒
        // const formattedHours = hours < 10 ? '0' + hours : hours;
        const formattedMinutes = minutes < 10 ? '0' + minutes : minutes;
        const formattedSeconds = secs < 10 ? '0' + secs.toFixed(3) : secs.toFixed(3);

        // 返回格式化后的时间
        return `${formattedMinutes}:${formattedSeconds}`;
      },
      rightClick() {
        uni.navigateBack({
          delta: 1
        });
      }
    }
  };
</script>
<style scoped>
  .list_box {
    border-radius: 14upx;
    background: #ffffff;
    box-shadow: 0rpx 4rpx 16rpx 0rpx #f5f7f9;
    padding: 30upx;
    padding-bottom: 80rpx;
    margin: 200rpx 20rpx 20rpx 20rpx;
    font-size: 26upx;
    color: rgba(102, 102, 102, 1);
    position: relative;
    height: 1250rpx;
    overflow-y: auto;
  }

  .bottom-buttons {
    position: fixed;
    bottom: 30upx;
    width: 85%;
    display: flex;
    justify-content: space-around;
    align-items: center;
    z-index: 999;
    gap: 20rpx;
  }
</style>
