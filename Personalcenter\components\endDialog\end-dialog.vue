<template>
	<view>
		<view class="dialogBG">
			<view class="reviewCard_box">
				<view class="cartoom_image">
					<image src="https://document.dxznjy.com/applet/interesting/cartoon_head_image.png" mode=""></image>
				</view>
				<uni-card class="reviewCard">
					<view class="review_close" v-if="showCancel">
						<image src="https://document.dxznjy.com/dxSelect/image/review_close_icon.png" mode="" @click="closeDialog()"></image>
					</view>
					<view class="reviewTitle">
						<view class="review_t1">{{tipContent}}</view>
					</view>


					<view class="review_btn">
						<view class="reviewCancle" @click="confirm()">确定</view>
						<view v-if="showCancel" class="reviewConfirm" @click="cancel()">取消</view>
					</view>
				</uni-card>
			</view>
		</view>
	</view>

</template>

<script>
	export default {
		props: {
			tipContent: {
				type: String,
				default: "退出后将不保存当前复习记录，是否确认退出？"
			},
			showCancel: {
				type: Boolean,
				default: true
			},
		},
		data() {
			return {

			}
		},
		methods: {
			//确认
			confirm() {
				this.$emit('confirm')
			},
			//取消
			cancel() {
				this.closeDialog();
			},
			//关闭弹窗
			closeDialog() {
				this.$emit('closeDialog')
			}
		}
	}
</script>

<style>
	.dialogBG {
		width: 100%;
		height: 100%;
	}

	/* 21天结束复习弹窗样式 */
	.reviewCard_box {
		width: 540rpx;
		height: 669rpx;
		position: relative;
	}

	.reviewCard_box image {
		width: 100%;
		height: 100%;
	}

	.reviewCard {
		width: 540rpx;
		height: 508rpx;
		margin-top: 40rpx;
	}

	.cartoom_image {
		width: 312rpx;
		height: 323rpx;
		position: absolute;
		top: -161rpx;
		left: 114rpx;
		z-index: 2;
	}

	.review_close {
		width: 80rpx;
		height: 80rpx;
		float: right;
	}

	.reviewTitle {
		margin-top: 131rpx;
		width: 100%;
		text-align: center;
	}

	.review_t1 {
		font-size: 36rpx;
		font-family: 'syhtB';
	}

	.review_t2 {
		font-size: 30rpx;
		font-family: 'syhtR';
		color: #A1A1A1;
		margin-top: 22rpx;
	}

	.review_btn {
		width: 450rpx;
		display: flex;
		margin: 41rpx auto 38rpx auto;
		justify-content: center;
	}

	.review_btn view {
		width: 210rpx;
		height: 80rpx;
		display: inline-flex;
		border-radius: 40rpx;
		border: 1rpx solid #116254;
		font-size: 36rpx;
		font-family: 'syhtM';
		justify-content: center;
		line-height: 80rpx;
		margin: 0 20rpx;
	}

	.reviewConfirm {
		background: #116254;
		color: #FFFFFF;
	}

	.reviewCancle {
		color: #116254;
		background: #FFFFFF;
	}
</style>
