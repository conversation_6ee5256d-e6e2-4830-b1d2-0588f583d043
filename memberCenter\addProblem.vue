<template>
	<view class="bg-ff problem_content_add">
		<view class="plr-32 f-24 problem_content_css">
			<textarea v-model="problemInfo.content"  placeholder="大胆提问,开启探索知识的大门..." ></textarea  >
		</view>
		<view class="file_state mt-35 plr-32  pt-35 f-28 c-55">
			<span>分享状态：</span>
			<span>公开</span>
		</view>
		<view @click="addProblem" class='problem_btn f-28 c-ff'>我要提问</view>
	</view>
</template>

<script>
import Config from '@/util/config.js'
const {
		$navigationTo,
		$getSceneData,
		$showError,
		$showMsg,
		$http
	} = require("@/util/methods.js")
	export default {
		data() {
			return {
				problemInfo:{},
				addProblemFalse:false,
			}
		},
		onLoad(e){
			this.problemInfo.topicType=e.codeType
			this.problemInfo.isPublic=1
			this.problemInfo.userId=uni.getStorageSync('user_id')?uni.getStorageSync('user_id'):''
		},
		methods:{
			async addProblem(){
				if(!this.problemInfo.content){
					$showMsg('请输入你的提问');
					return
				}
				if(this.addProblemFalse){
					return
				}
				this.addProblemFalse=true
				this.auditContent()
			},
			async auditContent(){
				let _this = this
				const res = await $http({
					url: 'zx/common/auditContent',
					method: 'POST',
					showLoading:true,
					data: {
						content:this.problemInfo.content,
						scene :4,
					}
				})
				if(res){
					if(res.data=='pass'){
						let _this = this
						const res = await $http({
							url: 'zx/wap/CultureCircle/release',
							showLoading:true,
							method: 'POST',
							data: this.problemInfo
						})
						if(res){
							this.getCultureCircle()
							uni.navigateBack({
								delta: 1
							});
							uni.setStorageSync('showType','addProblem')
							this.addProblemFalse=false
						}
					}else if(res.data=='risk'){
						$showMsg('您提出的问题有风险');
					}
				}
			},
			async getCultureCircle(){
			    const res = await $http({
			    	url: 'zx/wap/badge/gain?userId='+uni.getStorageSync('user_id')+'&eventType=QUESTION',
					showLoading:true,
					method: 'POST',
			    	data: {}
			    })
				if(res){
					console.log(res)
				}
			},
		}

	}
</script>
<style lang="scss" scoped>
	.problem_content_add{
		height: 100vh;
		position: relative;
		.problem_btn{
			width: 686rpx;
			background-color: #339378;
			border-radius: 38rpx;
			height: 74rpx;
			line-height: 74rpx;
			position: absolute;
			text-align: center;
			left:32rpx;
			bottom:40rpx;
		}
	}
	.flex_css_pic{
		display: flex;
		justify-content: flex-start;
	}
	.file_item_css{
		margin-right: 10rpx;
		.file_image_css{
			width: 160rpx;
			height: 160rpx;
		}
	}
	.file_state{
		border-top: 1rpx solid #ECF0F4;
	}
	.problem_content_css{
		padding-top: 48rpx;
		textarea{
			color:#555;
			height: 208rpx;
			font-size: 28rpx;
			width:680rpx;
		}
	}
</style>