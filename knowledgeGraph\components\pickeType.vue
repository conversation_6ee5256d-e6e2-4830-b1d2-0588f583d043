<template>
  <view class="picker-container">
    <!-- 显示选择器触发区域 -->
    <view class="pickerStyle" @click="openPicker">
      {{ showSubjectName }}
      <uni-icons type="down" style="color: #555; margin-left: 10rpx"></uni-icons>
    </view>

    <!-- 底部弹出选择器 -->
    <uni-popup ref="popup" type="bottom" :is-mask-click="false">
      <view class="picker-popup">
        <!-- 头部操作栏 -->
        <view class="popup-header">
          <text class="popup-button cancel" @click="closePicker">取消</text>
          <text class="popup-button confirm" @click="confirmSelection">确定</text>
        </view>

        <!-- 选择器内容 -->
        <picker-view v-if="visible" :indicator-style="indicatorStyle" :value="currentIndex" @change="handlePickerChange" class="picker-view">
          <picker-view-column>
            <view class="picker-item" v-for="(item, index) in options" :key="index">
              {{ item.enName || item.subjectName || item.desc }}
            </view>
          </picker-view-column>
        </picker-view>
      </view>
    </uni-popup>
  </view>
</template>

<script>
  export default {
    name: 'PickeType',
    props: {
      // 显示的主题名称（如"课程大类"）
      showSubjectName: {
        type: String,
        default: '请选择'
      },
      // 选项列表
      options: {
        type: Array,
        default: () => []
      },
      // 默认选中索引
      defaultValue: {
        type: [Number, Array],
        default: 0
      },
      // 是否可以通过父组件触发打开
      triggerOpen: {
        type: Boolean,
        default: false
      }
    },
    data() {
      return {
        visible: false,
        currentIndex: [0], // 当前选中索引，保持数组形式兼容多列
        selectedItem: null, // 当前选中项
        indicatorStyle: `height: 50px;`, // 选择器中间选中框样式
        isInternalChange: false // 标识是否是内部变化
      };
    },
    computed: {
      // 显示的文本
      selectedText() {
        if (!this.selectedItem) return '';
        return this.selectedItem.presentation || this.selectedItem.label || this.selectedItem.name;
      }
    },
    watch: {
      // 监听options变化重置选择
      options(newVal) {
        if (newVal.length > 0) {
          this.currentIndex = [0];
          this.selectedItem = newVal[0];
        }
      },
      // 监听父组件触发展开
      triggerOpen(newVal) {
        if (newVal) {
          this.openPicker();
          this.$emit('update:triggerOpen', false);
        }
      },
      // 监听默认值变化
      defaultValue: {
        immediate: true,
        handler(newVal) {
          if (!this.isInternalChange && newVal !== undefined) {
            const index = Array.isArray(newVal) ? newVal[0] : newVal;
            if (index >= 0 && index < this.options.length) {
              this.currentIndex = [index];
              this.selectedItem = this.options[index];
            }
          }
          this.isInternalChange = false;
        }
      }
    },
    methods: {
      // 打开选择器
      openPicker() {
        this.visible = true;
        this.$refs.popup.open();
        this.$emit('open');
      },

      // 关闭选择器
      closePicker() {
        this.visible = false;
        this.$refs.popup.close();
        this.$emit('close');
      },

      // 确认选择
      confirmSelection() {
        this.closePicker();
        if (this.selectedItem) {
          console.log(this.selectedItem);
          this.$emit('change', this.selectedItem.value || this.selectedItem.id);
          this.$emit('confirm', this.selectedItem);
        }
      },

      // 选择器变化事件
      handlePickerChange(e) {
        const index = e.detail.value[0];
        if (index >= 0 && index < this.options.length) {
          this.currentIndex = [index];
          this.selectedItem = this.options[index];
          this.isInternalChange = true;
          this.$emit('picker-change', this.selectedItem, index);
        }
      },

      // 暴露给父组件的方法：手动打开选择器
      show() {
        this.openPicker();
      },

      // 暴露给父组件的方法：手动关闭选择器
      hide() {
        this.closePicker();
      },

      // 暴露给父组件的方法：重置选择
      reset() {
        this.currentIndex = [0];
        this.selectedItem = this.options[0];
        this.$emit('change', this.selectedItem?.value || '');
        this.$emit('reset');
      },

      // 暴露给父组件的方法：设置选中项
      setSelectedIndex(index) {
        if (index >= 0 && index < this.options.length) {
          this.currentIndex = [index];
          this.selectedItem = this.options[index];
        }
      }
    }
  };
</script>

<style scoped>
  .pickerStyle {
    color: #666;
    font-size: 28rpx;
    text-align: center;
  }

  .picker-trigger {
    display: flex;
    align-items: center;
    padding: 12rpx 20rpx;
    border-radius: 8rpx;
    background-color: #f7f7f7;
    color: #333;
    font-size: 28rpx;
  }

  .picker-label {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    max-width: 200rpx;
  }

  .picker-popup {
    background-color: #fff;
    border-radius: 24rpx 24rpx 0 0;
    padding-bottom: env(safe-area-inset-bottom);
  }

  .popup-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20rpx 30rpx;
    border-bottom: 1rpx solid #eee;
  }

  .popup-title {
    font-size: 32rpx;
    font-weight: bold;
    color: #333;
  }

  .popup-button {
    font-size: 28rpx;
    padding: 10rpx 20rpx;
  }

  .cancel {
    color: #999;
  }

  .confirm {
    color: #007aff;
  }

  .picker-view {
    height: 400rpx;
    width: 100%;
  }

  .picker-item {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 50px;
    font-size: 32rpx;
    color: #333;
  }
</style>
