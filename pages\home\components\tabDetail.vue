<template>
  <view class="detailHeader">
    <view class="bg-ff radius-15 pt-40" style="min-height: 300rpx">
      <view class="study_service mb-40 bold f-28 c-33" @click="showParentLIst">
        <image src="https://document.dxznjy.com/course/ecabc2a484da422886b5efdd087c731e.png" class="back_img"></image>
        {{ title }}
      </view>
      <view class="templateItem bg-ff radius-15" v-if="title == '抗遗忘选择'">
        <view class="template" @click="forgetone(1, '21天抗遗忘')">
          <image src="https://document.dxznjy.com/course/81f0fece219e4514a96bf5d34acd566d.png" class="w44"></image>
          <view>21天抗遗忘</view>
        </view>
        <view class="template">
          <view @click="forgetone(2, '拼音法抗遗忘')">
            <image src="https://document.dxznjy.com/course/b9234b83a7e34151811cdfdb96576795.png" class="w44"></image>
            <view>拼音法抗遗忘</view>
          </view>
        </view>
        <view class="template" @click="forgetone(3, '新阅读理解抗遗忘')">
          <image src="https://document.dxznjy.com/course/a3b14f6bede04600bd78807239e466aa.png" class="w44"></image>
          <view>新阅读理解抗遗忘</view>
        </view>
      </view>
      <view class="templateItem bg-ff radius-15" v-if="title == '检测报告'">
        <view class="template" @click="useVocabulary(1, '词汇量检测报告')">
          <image src="https://document.dxznjy.com/course/5a2c847d6fd34d8d81e11e2c9b31d507.png" class="w44"></image>
          <view>词汇量检测报告</view>
        </view>
        <view class="template" @click="useVocabulary(2, '听写检测报告')">
          <image src="https://document.dxznjy.com/dxSelect/fourthEdition/icon_chlbg.png" class="w44"></image>
          <view>听写检测报告</view>
        </view>
      </view>

      <view class="templateItem bg-ff radius-15" v-if="title == '趣味复习'">
        <view class="template" @click="useInterest(1, '鼎英语趣味复习')">
          <image src="https://document.dxznjy.com/course/120f25af38e1456e822e40ab23189a36.png" class="w44"></image>
          <view>鼎英语趣味复习</view>
        </view>
        <view class="template" @click="useInterest(2, '拼音法趣味复习')">
          <image src="https://document.dxznjy.com/course/8474fcc7d9414195b78bcb77ab11a27c.png" class="w44"></image>
          <view>拼音法趣味复习</view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
  export default {
    data() {
      return {};
    },
    props: {
      title: {
        type: String,
        required: true
      }
    },
    onShow() {},
    methods: {
      showParentLIst() {
        this.$emit('updateState');
      },
      forgetone(id, name) {
        // #ifdef MP-WEIXIN
        getApp().sensors.track('studyServiceItemClick', {
          name: name
        });
        // #endif
        this.$emit('useForget', id);
      },
      useVocabulary(id, name) {
        // #ifdef MP-WEIXIN
        getApp().sensors.track('studyServiceItemClick', {
          name: name
        });
        // #endif
        this.$emit('useVocabulary', id);
      },
      useInterest(id, name) {
        // #ifdef MP-WEIXIN
        getApp().sensors.track('studyServiceItemClick', {
          name: name
        });
        // #endif
        this.$emit('useInterest', id);
      }
    }
  };
</script>

<style lang="scss" scoped>
  .aa {
    height: 50vh;
  }

  /deep/ .u-toolbar.data-v-55c89db1 {
    border-bottom: 1px solid #e0e0e0;
  }

  /deep/ .u-line-1 {
    line-height: 68rpx !important;
    background-color: #f4f4f4 !important;
  }

  /deep/ .u-picker__view {
    height: 440rpx !important;
  }

  /deep/ .u-picker__view__column.data-v-f45a262e {
    border-radius: 12rpx;
  }

  /deep/ .u-popup__content.data-v-3a231fda {
    border-radius: 12rpx;
    margin: 0 20rpx 20rpx 20rpx;
  }

  /deep/ .u-picker__view__column__item {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    font-size: 30rpx !important;
    margin-left: 10rpx;
  }

  /deep/ .u-toolbar__title.data-v-6d25fc6f {
    color: #303133;
    padding: 0 60rpx;
    font-size: 16px;
    font-weight: 700;
    flex: 1;
    text-align: center;
    background-color: #ffffff !important;
  }

  /deep/ .u-toolbar.data-v-6d25fc6f {
    height: 42px;
    width: 96%;
    display: flex;
    margin: 0 auto;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #e0e0e0;
    padding: 20rpx 0;
    box-sizing: b;
  }

  .banner_indicator_style {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    position: absolute;
    bottom: -40rpx;
    left: 50%;
    width: 48rpx;
    height: 8rpx;
    background-color: #fae1c5;
    transform: translateX(-50%);
    border-radius: 4rpx;

    .indicator_style_css {
      height: 8rpx;
      border-radius: 4rpx;
    }

    .active_current {
      height: 8rpx;
      background-color: #fd9b2a;
      border-radius: 4rpx;
    }
  }

  .page_title {
    position: absolute;
    top: 80upx;
    width: 100%;
    text-align: center;
  }

  .study_service {
    padding-left: 24rpx;
  }

  .Topbar {
    width: 100%;
    position: absolute;
    top: 0;
    left: 0;
  }

  .box-48 {
    width: 48rpx;
    height: 48rpx;
  }

  .box-140 {
    width: 140rpx;
    height: 140rpx;
  }

  .Topbar {
    width: 100%;
    position: absolute;
    top: 0;
    left: 0;
  }
  .cricle {
    position: absolute;
    width: 36upx;
    height: 36upx;
    background-color: #ff0000;
    border-radius: 20upx;
    top: 0upx;
    right: 20upx;
    color: #fff;
    text-align: center;
    line-height: 36upx;
    font-size: 24upx;
  }

  page {
    background-color: #f7f7f7;
  }

  .home_bg {
    height: 160rpx;
  }

  .icon_img {
    position: absolute;
    top: 80rpx;
    left: 30rpx;
    z-index: 999 !important;
  }

  .box-40 {
    width: 40upx;
  }

  .currency {
    margin-bottom: 35rpx;
  }

  /deep/ .mlr-20 {
    margin-right: 0 !important;
  }

  .box {
    position: absolute;
    right: -5rpx;
  }

  /deep/ .data-v-26a60cd2 {
    padding: 0;
  }

  /deep/ .wid36 {
    width: 39upx;
    height: 39upx;
  }

  .home_con {
    position: relative;
    // margin-top: 20rpx;
    border-radius: 20upx 20upx 0 0;
    // padding: 30upx;
  }

  /deep/ .swiper-box {
    height: 100rpx !important;
  }

  /deep/ .data-v-1fff95d2 {
    height: 100rpx !important;
  }

  /deep/ .u-badge--not-dot.data-v-662d25bf {
    padding: 2rpx 6rpx;
  }

  // 学习进度
  .plan {
    position: absolute;
    top: 0;
    border-radius: 30rpx;
    z-index: 9;
    color: #000;
    height: 240rpx;
    width: 92%;
  }

  .title {
    position: relative;
    font-size: 32rpx;
    font-family: Microsoft YaHei;
    padding: 30rpx 0 0 0;
    margin-left: 30rpx;
    margin-right: 30rpx;
  }

  .title-line {
    height: 1rpx;
    background-color: #eeeeee;
    margin-top: 60rpx;
  }

  .title-name {
    position: absolute;
    left: 0;
  }

  .title-merchant {
    position: absolute;
    right: 0;
  }

  .hour {
    padding: 20rpx 30rpx 0 30rpx;
    color: #000;
  }

  .time {
    font-size: 26rpx;
    margin-bottom: 20rpx;
  }
  .back_img {
    width: 25rpx;
    height: 25rpx;
    margin-right: 10rpx;
  }
  .w44 {
    width: 44upx;
    height: 44upx;
  }

  /deep/ .uni-swiper__warp swiper {
    height: 296rpx !important;
  }

  .flex_s {
    display: flex;
  }

  .border-t {
    border-top: 1px dashed #eee;
  }

  .shike-img {
    position: absolute;
    width: 45rpx;
    height: 45rpx;
    top: 320rpx;
    left: 210rpx;
  }

  .templateItem {
    width: 100%;
    text-align: center;
    display: flex;
    align-content: flex-start;
    flex-flow: row wrap;
    font-size: 24rpx;
  }

  .template {
    flex: 0 0 33.33%;
    margin-bottom: 50rpx;
    font-size: 24rpx;
  }

  /* 弹窗样式 */
  .dialogBG {
    width: 100%;
    /* height: 100%; */
  }

  /* 21天结束复习弹窗样式 */
  .reviewCard_box {
    width: 670rpx;
    /* height: 560rpx; */
    position: relative;
  }

  .reviewCard_box image {
    width: 100%;
    height: 100%;
  }

  .reviewCard {
    position: relative;
    width: 100%;
    height: 100%;
    background: #ffffff;
    color: #000;
    border-radius: 24upx;
    padding: 50upx 55upx;
    box-sizing: border-box;
  }

  .cartoom_image {
    width: 420rpx;
    position: absolute;
    top: -250rpx;
    left: 145rpx;
    z-index: -1;
  }

  .review_close {
    position: absolute;
    top: 20rpx;
    right: 20rpx;
    z-index: 1;
  }

  .reviewTitle {
    width: 100%;
    text-align: center;
    font-size: 34upx;
    display: flex;
    justify-content: center;
  }

  .dialogContent {
    box-sizing: border-box;
    font-size: 32upx;
    line-height: 45upx;
    text-align: center;
    margin-top: 40rpx;
  }

  .review_btn {
    width: 240upx;
    height: 80upx;
    background: linear-gradient(180deg, #88cfba 0%, #1d755c 100%);
    border-radius: 45upx;
    font-size: 30upx;
    color: #ffffff;
    line-height: 80upx;
    margin: 60rpx auto 0 auto;
    justify-content: center;
    text-align: center;
  }

  .addclass {
    width: 100%;
    height: 70rpx;
    line-height: 70rpx;
    font-size: 30rpx;
    color: #2e896f;
    border: 1px solid #2e896f;
    border-radius: 35rpx;
  }

  .not-selected {
    width: 100%;
    height: 70rpx;
    line-height: 70rpx;
    font-size: 30rpx;
    color: #000;
    border: 1px solid #c8c8c8;
    border-radius: 35rpx;
  }

  .scroll-Y {
    height: 440rpx;
  }

  .btn_orange {
    background: linear-gradient(to bottom, #88cfba, #1d755c);
    color: #fff !important;
    height: 60rpx;
    width: 150rpx;
    font-size: 28rpx;
    line-height: 60rpx;
    border-radius: 45rpx;
    border: none !important;
  }

  /deep/ .uni-swiper__dots-box {
    bottom: 75rpx !important;
  }

  /deep/ .uni-swiper__dots-item {
    width: 6rpx !important;
    height: 6rpx !important;
  }

  .header-img {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    bottom: 0;
    width: 690rpx;
    height: 200rpx;
  }

  .hint-img {
    width: 24rpx;
    height: 24rpx;
    position: absolute;
    top: -10rpx;
    right: -10rpx;
  }

  .distance {
    right: -25rpx;
  }

  .course-img {
    margin-left: 30rpx;
    margin-right: 30rpx;
    height: 254rpx;
  }

  // 21天抗遗忘
  /* 底部按钮样式 */
  .mask-footer {
    margin-top: 20px;
    display: flex;
    justify-content: space-around;
  }

  .mask-footer button {
    width: 250rpx;
    height: 80rpx;
    font-size: 30rpx;
    border-radius: 45rpx;
  }

  .confirm-button {
    display: flex;
    justify-content: space-around;
    align-items: center;
    /* 文本垂直居中对齐 */
    background: #2e896f;
    color: #ffffff !important;
  }

  .cancel-button {
    display: flex;
    justify-content: space-around;
    align-items: center;
    /* 文本垂直居中对齐 */
    color: #2e896f !important;
    border: 1rpx solid #2e896f !important;
  }

  .empty-img {
    width: 114rpx;
    height: 107rpx;
  }

  .empty-content {
    z-index: 9;
    width: 100%;
    display: flex;
    margin-bottom: 30rpx !important;
    padding-top: 18rpx;
    flex-direction: column;
    /* 垂直布局，子视图按列排列 */
    justify-content: center;
    /* 水平居中 */
    align-items: center;
    /* 垂直居中 */
  }

  .header_css {
    background: url('https://document.dxznjy.com/course/7cf268d57fba49739ee8514bfe813b8f.png') no-repeat;
    background-size: 100%;
    width: 750rpx;
    height: 460rpx;
    padding-top: 40rpx;

    .box-128 {
      width: 128rpx;
      height: 128rpx;
    }

    .identity_css {
      color: #006f57;
      line-height: 34rpx;
      padding: 1rpx 40rpx;
      display: inline-block;
      background-color: #dfffe4;
    }

    .center_css {
      width: 270rpx;
      margin-left: 2rpx;
    }

    .content_right {
      height: 60rpx;
      margin-left: 150rpx;

      .integral_css {
        vertical-align: middle;
        display: block;
        margin-right: 18rpx;
      }
    }

    .content_main_css {
      width: 686rpx;
      margin: 0 auto;
      height: 254rpx;
      margin-bottom: 74rpx;
    }

    .content_main_css_item {
      width: 638rpx !important;
      border-radius: 24rpx;
      margin: 0 auto;
      padding: 18rpx 24rpx;
      height: 219rpx !important;
    }

    .no_study_time {
      background-color: #f6fcff;
      border-radius: 16rpx;
      width: 636rpx;
      text-align: center;
    }

    .study_time {
      display: flex;
      justify-content: space-around;
      align-items: center;
      background-color: #f6fcff;
      padding-top: 20rpx;
      padding-bottom: 32rpx;
      margin-top: 24rpx;

      .study_time_item {
        width: 33%;
        text-align: center;
        position: relative;

        .green_css {
          color: #339378;
          line-height: 54rpx;
          display: inline-block;
        }
      }

      .study_time_item::after {
        content: ' ';
        height: 48rpx;
        border-right: 2rpx solid #ebf2f6;
        position: absolute;
        top: 30rpx;
        right: 0;
      }

      .study_time_item:last-child::after {
        display: none;
      }
    }
  }
</style>
