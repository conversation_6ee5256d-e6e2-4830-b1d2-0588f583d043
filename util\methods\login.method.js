const { $showMsg } = require("./prompt")

/**
 * 获取code
 * @returns code
 */
const $login = () => {
  return new Promise((resolve,reject) => {
    uni.login({
      success(res){
        resolve(res.code)
      },
      fail(err){
        $showMsg("登录失败")
      }
    })
  })
}

/**
 * 获取用户信息
 * @returns 用户的信息
 */
const $getUserInfo = () =>{
  return new Promise((resolve,reject) => {
    uni.getUserInfo({
      lang: zh_CN,
      success(res){
        resolve(res)
      },
      fail(err){
        $showMsg("获取用户信息失败")
      }
    })
  })
}

module.exports = {
  $login,
  $getUserInfo
}