<template>
	<view>
		<view class="input" type="text" placeholder="请选择职业类别">
			<picker class="pickerList" mode="multiSelector" :range="newCategotyDataList" :value="multiIndex"
				range-key="name" @change="bindPickerChange" @columnchange="pickerColumnchange">
				<view class="">{{select}}</view>
			</picker>
		</view>
		<u-picker :show="show" ref="uPicker" :columns="newCategotyDataList" :defaultIndex="multiIndex" keyName="name"
			@change="changeHandler" @confirm="confirm"></u-picker>
	</view>
</template>

<script>
	const {
		$http
	} = require('@/util/methods.js')
	export default {
		data() {
			return {
				show: true,
				multiIndex: [0, 0, 0],
				newCategotyDataList: [
					[],
					[]
				],
				categoryArr: [{
						name: "一般行业",
						value: "A",
						child: [{
							name: "机关团体公司行号",
							value: "A01",
						}]
					},
					{
						name: "农牧业",
						value: "B",
						child: [{
								name: "农业",
								value: "B01"
							},
							{
								name: "畜牧业",
								value: "B02"
							}
						]
					}, {
						name: "测试行业",
						value: "A",
						child: [{
							name: "测试行号",
							value: "A01",
						}]
					},
				],
				select: "请选择职业类别",
				selectObj: {}
			};
		},
		onLoad() {},
		onShow() {
			for (let i = 0; i < this.categoryArr.length; i++) {
				let obj = {
					name: this.categoryArr[i].name,
					value: this.categoryArr[i].value
				}
				this.newCategotyDataList[0].push(obj);
			}
			for (let i = 0; i < this.categoryArr[0].child.length; i++) {
				let obj = {
					name: this.categoryArr[0].child[i].name,
					value: this.categoryArr[0].child[i].value
				}
				this.newCategotyDataList[1].push(obj);
			}
			console.log(this.newCategotyDataList)
		},
		methods: {
			confirm(e) {
				console.log('confirm', e)
			},
			changeHandler(e) {
				console.log(e)
				const {
					columnIndex,
					value,
					values, // values为当前变化列的数组内容
					index,
					// 微信小程序无法将picker实例传出来，只能通过ref操作
					picker = this.$refs.uPicker
				} = e
				if (columnIndex === 0) {
					this.newCategotyDataList[1] = this.categoryArr[index].child.map((item, index) => {
						// console.log(item)
						return {
							name: item.name,
							value: item.value
						}

					})
					picker.setColumnValues(1, this.newCategotyDataList[1])
					console.log(this.newCategotyDataList)
				}
			},
			pickerColumnchange(e) {
				console.log(e)
				// 第几列滑动
				// console.log(e.detail.column);
				// 第几列滑动的下标
				// console.log(e.detail.value)
				// 第一列滑动
				if (e.detail.column === 0) {
					this.multiIndex[0] = e.detail.value
					// console.log('第一列滑动');
					// this.newCategotyDataList[1] = [];
					this.newCategotyDataList[1] = this.categoryArr[this.multiIndex[0]].child.map((item, index) => {
						// console.log(item)
						return {
							name: item.name,
							value: item.value
						}

					})
					this.multiIndex.splice(1, 1, 0)
					this.multiIndex.splice(2, 1, 0)
				}

			},
			bindPickerChange(e) {
				console.log(e)
				this.multiIndex = e.detail.value;
				// 数组内的下标
				// console.log(this.multiIndex);
				// 获取一级类目
				console.log(this.newCategotyDataList[0][this.multiIndex[0]])
				// 获取二级类目
				console.log(this.newCategotyDataList[1][this.multiIndex[1]])
			},
		}

	}
</script>

<style>
</style>
