<template>
  <view>
    <web-view ref="webView" :src="webViewSrc"></web-view>
  </view>
</template>

<script>
  const { isTest179, isTest189 } = require('@/util/config');
  export default {
    data() {
      return {
        // url: 'http://***************:9000/',
        // memberId: '925041717',
        // app: '2',
        // token:
        //   '***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************'
        memberId: '',
        app: '',
        token: ''
      };
    },
    computed: {
      webViewSrc() {
        let baseDomain = 'classh5'; // 默认使用 classh5

        if (isTest179) {
          baseDomain = 'dl';
        } else if (isTest189) {
          baseDomain = 'onetomany';
        }

        return `https://${baseDomain}.ngrok.dxznjy.com/unibest/#/pages/knowGraph/index?memberId=${this.memberId}&token=${this.token}`;
      }
    },
    onLoad(e) {
      //
      // #ifdef APP-PLUS
      if (e.token) {
        this.app = e.app;
        this.memberId = e.memberId;
        this.token = e.token;
        this.$handleTokenFormNative(e); // 从app获取token
      }
      // #endif
    },
    onUnload() {
      // #ifdef APP-PLUS
      if (this.app) {
        plus.runtime.quit();
      }
      // #endif
    },
    methods: {}
  };
</script>
