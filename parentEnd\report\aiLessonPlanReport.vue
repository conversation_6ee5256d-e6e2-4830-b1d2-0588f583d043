<template>
  <page-meta :page-style="'overflow:' + (showModal ? 'hidden' : 'visible')"></page-meta>
  <view class="report-container" v-show="!isLoading">
    <view v-if="isShow">
      <page-container :show="isShow" :overlay="false" @beforeleave="beforeleave"></page-container>
    </view>
    <view class="p-20">
      <view class="header" @click="goBack">
        <uni-icons type="left" size="28" color="#000"></uni-icons>
      </view>
      <view class="" style="height: 240rpx">
        <!-- <view class="toastTop">下拉刷新数据</view> -->
      </view>
      <!-- 基本信息 -->
      <view id="module1">
        <view class="base-info-1" v-if="reportData.studentType == 1 || (reportData.studentType == 2 && reportData.grade != 9 && reportData.grade != 12)">
          <view class="bottom">
            <view class="tag">
              <image src="https://document.dxznjy.com/automation/1747623096000" style="height: 67rpx" mode="heightFix"></image>
            </view>
            <view class="content">
              <view class="p-30">
                <view>
                  <view class="item">
                    <text style="color: #939393">学员姓名：</text>
                    <text style="color: #939393">{{ reportData.studentName || '' }}</text>
                  </view>
                  <view class="flex item">
                    <view class="">
                      <text style="color: #939393">年级：</text>
                      <text style="color: #939393">{{ getGrade(reportData.grade) || '' }}</text>
                    </view>
                    <view class="">
                      <text style="color: #939393">学员编号：</text>
                      <text style="color: #21504b; font-weight: 600">{{ reportData.studentCode || '' }}</text>
                    </view>
                  </view>
                  <view class="item">
                    <text style="color: #939393">试课词库：</text>
                    <text style="color: #21504b; font-weight: 600">{{ reportData.studyBooks || '' }}</text>
                  </view>
                  <view class="item">
                    <text style="color: #939393">体验课学习效率：</text>
                    <text style="color: #939393">{{ `${reportData.memoryTime}分钟记住 ${reportData.memoryNum} 个单词` }}</text>
                  </view>
                  <view class="flex item">
                    <view class="">
                      <text style="color: #939393">英语成绩：</text>
                      <text style="color: #939393">{{ reportData.englishScore || '' }}</text>
                    </view>
                    <view class="">
                      <text style="color: #939393">数学成绩：</text>
                      <text style="color: #939393">{{ reportData.mathScore || '' }}</text>
                    </view>
                  </view>
                  <view class="item">
                    <text style="color: #939393">语文成绩：</text>
                    <text style="color: #939393">{{ reportData.chineseScore || '' }}</text>
                  </view>
                  <view class="item">
                    <text style="color: #939393">体验抗遗忘：</text>
                    <text style="color: #21504b; font-weight: 600">{{ reportData.resistForgetAcc || '' }}</text>
                  </view>
                  <view class="item">
                    <text style="color: #939393">试学时间：</text>
                    <text style="color: #21504b; font-weight: 600">{{ reportData.tryLearnTime ? reportData.tryLearnTime.split(' ')[0] : '' }}</text>
                  </view>
                </view>
              </view>
            </view>
            <view class="charts-box">
              <view class="container">
                <view class="chart">
                  <view class="title-top">现实掌握词汇：{{ dataList[0] }} 词</view>
                  <!-- 柱子1 -->
                  <view class="bar-container">
                    <view class="value-text">{{ dataList[1] }}</view>
                    <view class="bar" :style="{ height: toBarHeight(dataList[1]) }"></view>
                  </view>

                  <!-- 柱子2 -->
                  <view class="bar-container">
                    <view class="value-text">{{ dataList[0] }}</view>
                    <view class="bar" :style="{ height: toBarHeight(dataList[0]) }"></view>
                  </view>

                  <view class="baseline"></view>
                </view>

                <view class="labels">
                  <view class="label">{{ reportData.vocabularyLevel }}</view>
                  <view class="label">现实掌握词汇量</view>
                </view>

                <view class="title">试课信息对比图</view>
              </view>
            </view>
            <view class="plr-30" style="background-color: #f5fffa"></view>
          </view>
        </view>
        <view class="base-info" v-else>
          <view class="top-title">
            <view class="left">距离考试还有</view>
            <view class="day">DAY</view>
            <view class="right">{{ reportData.examRemainingTime || 0 }}</view>
          </view>
          <view class="bottom">
            <view class="tag">
              <image src="https://document.dxznjy.com/automation/1747623096000" style="height: 67rpx" mode="heightFix"></image>
            </view>
            <view class="content">
              <view class="p-30">
                <view>
                  <view class="item">
                    <text style="color: #939393">学员姓名：</text>
                    <text style="color: #939393">{{ reportData.studentName || '' }}</text>
                  </view>
                  <view class="flex item">
                    <view class="">
                      <text style="color: #939393">年级：</text>
                      <text style="color: #939393">{{ getGrade(reportData.grade) || '' }}</text>
                    </view>
                    <view class="">
                      <text style="color: #939393">学员编号：</text>
                      <text style="color: #21504b; font-weight: 600">{{ reportData.studentCode || '' }}</text>
                    </view>
                  </view>
                  <view class="item">
                    <text style="color: #939393">试课词库：</text>
                    <text style="color: #21504b; font-weight: 600">{{ reportData.studyBooks || '' }}</text>
                  </view>
                  <view class="item">
                    <text style="color: #939393">体验课学习效率：</text>
                    <text style="color: #939393">{{ `${reportData.memoryTime}分钟记住 ${reportData.memoryNum} 个单词` }}</text>
                  </view>
                  <view class="flex item">
                    <view class="">
                      <text style="color: #939393">英语成绩：</text>
                      <text style="color: #939393">{{ reportData.englishScore || '' }}</text>
                    </view>
                    <view class="">
                      <text style="color: #939393">数学成绩：</text>
                      <text style="color: #939393">{{ reportData.mathScore || '' }}</text>
                    </view>
                  </view>
                  <view class="item">
                    <text style="color: #939393">语文成绩：</text>
                    <text style="color: #939393">{{ reportData.chineseScore || '' }}</text>
                  </view>
                  <view class="item">
                    <text style="color: #939393">体验抗遗忘：</text>
                    <text style="color: #21504b; font-weight: 600">{{ reportData.resistForgetAcc || '' }}</text>
                  </view>
                  <view class="item">
                    <text style="color: #939393">试学时间：</text>
                    <text style="color: #21504b; font-weight: 600">{{ reportData.tryLearnTime ? reportData.tryLearnTime.split(' ')[0] : '' }}</text>
                  </view>
                </view>
              </view>
            </view>
            <view class="charts-box">
              <view class="container">
                <view class="chart">
                  <view class="title-top">现实掌握词汇：{{ dataList[0] }} 词</view>
                  <!-- 柱子1 -->
                  <view class="bar-container">
                    <view class="value-text">{{ dataList[1] }}</view>
                    <view class="bar" :style="{ height: toBarHeight(dataList[1]) }"></view>
                  </view>

                  <!-- 柱子2 -->
                  <view class="bar-container">
                    <view class="value-text">{{ dataList[0] }}</view>
                    <view class="bar" :style="{ height: toBarHeight(dataList[0]) }"></view>
                  </view>

                  <view class="baseline"></view>
                </view>

                <view class="labels">
                  <view class="label">{{ reportData.vocabularyLevel }}</view>
                  <view class="label">现实掌握词汇量</view>
                </view>

                <view class="title">试课信息对比图</view>
              </view>
            </view>
            <view class="plr-30" style="background-color: #f5fffa"></view>
          </view>
        </view>
      </view>
      <!-- 课程规划 -->
      <view class="" id="module2">
        <view class="now-study" id="module2">
          <view class="top-title">课程规划</view>
          <view class="bottom">
            <view class="bottom-title break-all mlr-20">总目标：{{ reportData.goalDesc }}</view>
            <view class="content">
              <view class="button-container">
                <view class="button">教材版本{{ reportData.courseEdition && `《${reportData.courseEdition}》` }}</view>
              </view>
              <view class="content-item" v-for="(item, index) in reportData.stageInfoDtoList" :key="index">
                <view class="tag">
                  <view class="point">
                    <view class="point-item"></view>
                  </view>
                  <view class="title">阶段{{ item.stage }}</view>
                </view>
                <view class="item-box">
                  <view class="stage-title">时段任务-{{ item.timeTask }}</view>
                  <view class="stage-section">
                    <view class="stage-label break-all mlr-20">阶段目标：{{ item.stageTarget }}</view>
                    <view class="stage-value"></view>
                  </view>
                  <view class="stage-section">
                    <view class="stage-label">建议课时：{{ item.stageHour && `${item.stageHour}小时` }}</view>
                    <view class="stage-value"></view>
                  </view>
                  <view class="stage-section" v-if="item.wordCourse">
                    <view class="stage-label">单词速记：</view>
                    <view class="stage-value">{{ item.wordCourse }}</view>
                  </view>
                  <view class="stage-section" v-if="item.grammarCourse">
                    <view class="stage-label">语法渗透：</view>
                    <view class="stage-value">{{ item.grammarCourse }}</view>
                  </view>
                  <view class="stage-section" v-if="item.readCourse">
                    <view class="stage-label">超级阅读：</view>
                    <view class="stage-value">{{ item.readCourse }}</view>
                  </view>
                  <view class="stage-section" v-if="item.hearingCourse">
                    <view class="stage-label">全能听力：</view>
                    <view class="stage-value">{{ item.hearingCourse }}</view>
                  </view>
                </view>
              </view>
              <view class="p-20 f-28 noteBg m-12">
                <view style="color: #6cae8e">备注：我们只学不会的，时长是按基础低的学生概算的，如学生实力强我们用不到这么多课时，课时多退少补。</view>
                <view style="color: #6cae8e">阶段课时范围：根据学员的学习效率，学习习惯等综合判断对应阶段的建议课时范围。实际安排将随学习进度灵活调整，确保高效提升。</view>
              </view>
            </view>
          </view>
        </view>
      </view>
      <!-- 高效学习计划表 -->
      <view class="" id="module3">
        <view class="forget-word">
          <view class="top-title"></view>
          <view class="bottom">
            <view class="content">
              <view class="p-15">
                <view class="plan-header">
                  <view class="plan-title">上课规划</view>
                  <view class="plan-subtitle mlr-20">{{ reportData.classPlan || '' }}</view>
                </view>
                <view class="plan-header">
                  <view class="plan-title">复习规划</view>
                  <view class="plan-subtitle mlr-20">{{ reportData.reviewPlan || '' }}</view>
                </view>

                <view class="plan-content">
                  <view class="title">记忆力-艾宾浩斯记忆曲线</view>
                  <view class="description">艾宾浩斯遗忘曲线告诉我们：复习很重要，但是合理安排复习时间更重要，正确的时间点进行复习会使学习事半功倍！</view>
                  <image src="https://document.dxznjy.com/course/1747649053000" style="width: 620rpx" mode="widthFix"></image>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 课程服务 -->
      <view class="table-data" id="module4">
        <view class="top-title"></view>
        <view class="bottom">
          <view class="content">
            <image src="https://document.dxznjy.com/course/1747649012000" style="width: 100%; height: 747rpx" mode="aspectFit"></image>
          </view>
        </view>
      </view>

      <view class="footer">
        <view class="footer-btn">
          <image src="https://document.dxznjy.com/automation/1747623120000" style="width: 327rpx" mode="widthFix" @click="chooseType"></image>
          <!-- #ifdef APP-PLUS -->
          <button class="" hover-class="none" @click="appHandleShare">
            <image src="https://document.dxznjy.com/automation/1747623130000" style="width: 327rpx" mode="widthFix"></image>
          </button>
          <!-- #endif -->
          <!-- #ifdef MP-WEIXIN -->
          <button class="" hover-class="none" open-type="share" @click="handleShare">
            <image src="https://document.dxznjy.com/automation/1747623130000" style="width: 327rpx" mode="widthFix"></image>
          </button>
          <!-- #endif -->
        </view>
      </view>
    </view>
    <lessonSurveyModal ref="lessonSurveyModal" :showModal="showModal" @confirm="confirmStar" @closeDialog="closeDialog"></lessonSurveyModal>
    <uni-popup ref="choosePlanPopup" type="center">
      <view class="shareCode choosePlan">
        <view class="activityRule" style="font-size: 32rpx">请选择下载课程规划单模式</view>
        <view class="review_close" @click="closeChoosePlan">
          <uni-icons type="clear" size="26" color="#E2E1E1"></uni-icons>
        </view>
        <view class="rule">
          <view class="select-btn t-c" :class="{ selected: selectedOption === 'plan' }" @click="selectOption('plan')">WORD</view>
          <view class="select-btn t-c" :class="{ selected: selectedOption === 'report' }" @click="selectOption('report')">PDF</view>
        </view>
        <view class="popupEnd">
          <button class="closePopup" @click="closeChoosePlan">取消</button>
          <button class="comfirmChoose" @click="navigatePlan">确认</button>
        </view>
      </view>
    </uni-popup>
  </view>
  <view v-show="isLoading" class="loading-container">
    <view class="loading-text">数据加载中...</view>
  </view>
</template>

<script>
import qiunDataCharts from '../components/qiun-data-charts/components/qiun-data-charts/qiun-data-charts.vue';
import lessonSurveyModal from '../components/lessonSurveyModal.vue';
import Config from '@/util/config.js';
export default {
  components: {
    qiunDataCharts,
    lessonSurveyModal
  },
  data() {
    return {
      radarChartData: {},
      radarOptions: {},
      ringChartData: {},
      ringOptions: {},
      arcbarChartData: {},
      arcbarOptions: {},
      tableData: [],
      reviewId: '',
      showData: {},
      oldShowData: {},
      studyCountData: [],
      wordData: [],
      isHistory: false,
      studentCode: '',
      isShow: true,
      richText: '',
      richTexts: [],
      forgetData: [],
      noDownload: false,
      isLoading: true,
      toastShow: false,
      showModal: false,
      isBack: false,
      moduleIds: ['module1', 'module2', 'module3', 'module4'],
      isCapturing: false,
      screenshotPaths: [],
      canvasContext: null,
      platform: '',
      dataList: [], // 接口返回的动态数值，如词汇量
      reportData: {},
      gradeNameArr: '一年级_二年级_三年级_四年级_五年级_六年级_初一_初二_初三_高一_高二_高三_大一_大二_大三_大四_其他_幼儿园'.split('_'),
      link: '',
      isShowLesson: false,
      selectedOption: '',
      startX: 0,
      startY: 0,
      threshold: 80, // 触发滑动的最小距离（px）
      edgeThreshold: 30, // 屏幕边缘触发范围
      merchantCode: ''
    };
  },
  onLoad(options) {
    let that = this;
    this.isLoading = false;
    // this.getReviewReport(1375111633716006913, false);
    // console.log(123);
    // this.$refs.choosePlanPopup.open();
    if (options && options.id) {
      this.studentCode = options.studentCode;
      this.reviewId = options.id;
      this.isBack = options.isBack;
      this.isShowLesson = options.isShow;
      this.merchantCode = options.merchantCode;
      if (options.link) {
        uni.showModal({
          title: '这是一个外部链接',
          content: '小程序暂不支持永久下载pdf类文件，可点击复制链接后在浏览器中粘贴查看',
          confirmText: '复制链接',
          success: function (res) {
            if (res.confirm) {
              that.copyMessage(options.link);
            }
          }
        });
      }
      // 模拟请求接口
      setTimeout(() => {
        this.getReviewReport(this.reviewId, false);
        // this.$forceUpdate();
      }, 500);
      // this.toastShow = true;
      if (options.studentCode) {
        setTimeout(() => {
          this.getQuestionStatus();
        }, 3000);
      }
    }
  },
  onShow() {
    let that = this;
    let link = uni.getStorageSync('webData');
    that.link = link;
    if (that.link) {
      uni.showModal({
        title: '这是一个外部链接',
        content: '小程序暂不支持永久下载pdf类文件，可点击复制链接后在浏览器中粘贴查看',
        confirmText: '复制链接',
        success: function (res) {
          if (res.confirm) {
            that.copyMessage(that.link);
          }
        }
      });
    }
  },
  onHide() {
    uni.removeStorageSync('webData');
  },
  onUnload() {
    // #ifdef APP-PLUS
    window.onWebviewReturn = null;
    // #endif
    uni.removeStorageSync('webData');
  },
  onShareAppMessage() {
    return {
      title: 'AI课时规划单',
      imageUrl: '', //分享封面
      path: `/parentEnd/report/aiLessonPlanReport?id=${this.reviewId}&history=1&noDownload=1&studentCode=${this.studentCode}&isShow=false`
    };
  },
  onReady() {
    // 初始化canvas上下文
    this.canvasContext = uni.createCanvasContext('screenshotCanvas', this);
  },
  onBackPress() {
    this.goBack(); // 调用自定义返回逻辑
    return true; // 消耗事件，阻止默认返回行为
  },
  methods: {
    async downLoadWord() {
      await this.getReviewReport(this.reviewId, true);
      let that = this;
      let reportData = this.reportData;
      let arr = {};
      let obj1 = {
        content: `英语作为全球通用语言，词汇量的积累是能力提升的核心基石。系统的单词学习不仅能全面夯实听说读写各项技能，更能为孩子构建可持续发展的语言竞争力，在学业赛道上建立先发优势。从语言习得规律来看，基础词汇量是突破日常交流的门槛，而高阶词汇的掌握则是实现深度阅读的关键。在国内升学体系中，英语词汇的扎实程度直接影响考试表现：中学阶段的英语测评中，词汇应用能力在试卷分值中占据相当比例，尤其在中高考等关键节点，更是决定成绩层级的重要因素。可以说，单词功底是孩子应对国内英语应试与实现能力进阶的双重保障。综合孩子的学习情况、学习习惯来看，我们为您定制了一份专属的学习规划报告，旨在帮助孩子在英语学习的道路上少走弯路，更快实现学业目标。 报告内容主要包括以下几大模块：
                学习现状分析：基于孩子的词汇量检测结果、体验课学习习惯及学习效率、在校成绩及学习版本等多维度，结合AI技术对学习潜力的影响因素进行深入分析，明确孩子的学习优势与改进方向。
                 学习目标与规划：根据孩子的现有水平、目标情况及所处时间段（学期或寒暑假），为您量身定制个性化的学习规划方案。规划内容涵盖课时规划、学习时间分配、首月课表、周抗遗忘时间等关键环节建议。 
                学习效果评估：规划中还包含了长期的学习建议与效果评估，通过定期学习反馈和评估结果，确保孩子的学习进展符合预期目标。 
                学习过程支持：在学习规划实施过程中，我们将定期与孩子进行学习沟通，帮助其掌握学习技巧，调整学习策略，确保学习效果最大化。
                通过这份专属的学习规划报告，我们将帮助孩子在英语学习的道路上更有方向、更有效率，助您在学海无涯中找到通往高分的捷径。 注：学习过程中若需变动，可按照具体情况和阶段测试结果，进行时间调整及对应的课程安排调整。
                ${reportData.aiStudentCoursePlanResultDto.englishImportantResult ? reportData.aiStudentCoursePlanResultDto.englishImportantResult : ''}
                ${reportData.aiStudentCoursePlanResultDto.englishLevelResult ? reportData.aiStudentCoursePlanResultDto.englishLevelResult : ''}
                ${reportData.aiStudentCoursePlanResultDto.englishMemoryResult ? reportData.aiStudentCoursePlanResultDto.englishMemoryResult : ''}`
      };
      let obj2 = {
        content: ` 本学习规划方案基于学员实际学习时长、当前学习水平及 80 万 +学员学习轨迹大数据分析制定，深度聚焦英语语言学习四大核心模块（单词速记、语法渗透、超级阅读、全能听力），构建小升初 - 中考 -高考三大学段的阶梯式课程矩阵。 方案通过「同步衔接课程 +考纲强化课程」双轨设计，精准匹配各学段学业目标与考试要求，结合艾宾浩斯记忆曲线与自适应学习算法，助力学员在有限时间内实现语言能力与应试成绩的双重突破
          备注：我们只学不会的，时长是按基础低的学生概算的，如学生实力强我们用不到这么多课时，课时多退少补。
          阶段课时范围：根据学员的学习效率，学习习惯等综合判断对应阶段的建议课时范围。实际安排将随学习进度灵活调整，确保高效提升。
          ${reportData.aiStudentCoursePlanResultDto.englishPlanResult ? reportData.aiStudentCoursePlanResultDto.englishPlanResult : ''}
          单词速记：筑牢语言根基的底层工程，构建全学段词库网络，实现「音 - 形 - 义 - 复」四维解码。渗透语法：构建语言逻辑的神经网络，提升做题能力，实现「语法知识→解题能力→语言输出」的三阶跃迁。
          渗透语法：构建语言逻辑的神经网络，提升做题能力，实现「语法知识→解题能力→语言输出」的三阶跃迁。
          超级阅读：培养学术思维的核心阵地，达到快速提升效果，打造「解决实际应用能力 + 跨文化理解 + 批判性思维」的三维能力模型。
          全能听力：突破语言输入的最后一公里，构建「语音识别→语义理解→语境推理」的闭环能力链以上四大模块学习中，口语全模块渗透，通过「输入（听读）- 内化（学练）-输出（说写）」的完整学习链路确保效果`
      };
      let obj3 = {
        content: `家长/合伙人您好！ 艾宾浩斯遗忘曲线显示，知识 24 小时内遗忘最快。每周 3 次及以上的高频学习，能让知识快速扎根，孩子英语听说能力提升幅度比低频学习高 40%！及时复习同样关键，及时的复习能让知识吸收率能提升 60%。​ 短期高频学习像闯关游戏，轻松高效；战线拉长则像重新 “开荒”，易疲惫且效果差。建议保证上课频率，助力孩子高效学习！
          https://document.dxznjy.com/course/ceea76d2738144c5b1b9ce0c5fbfae23.png`
      };
      let obj4 = {
        content: `https://document.dxznjy.com/course/5d3010978c1b411b88f981998e3c4cce.png
          ${reportData.aiStudentCoursePlanResultDto.englishStudyResult ? reportData.aiStudentCoursePlanResultDto.englishStudyResult : ''}`
      };
      arr = {
        0: obj1,
        1: obj2,
        2: obj3,
        3: obj4
      };
      let json = JSON.stringify(arr);
      this.$httpUser
        .post('znyy/wap/student-lesson-plan/ai-word', {
          planCode: this.reviewId,
          word: json
        })
        .then((res) => {
          if (res && res.data.success) {
            let link = res.data.data;
            uni.showModal({
              title: '这是一个外部链接',
              content: '小程序暂不支持永久下载word类文件，可点击复制链接后在浏览器中粘贴查看',
              confirmText: '复制链接',
              success: function (res) {
                if (res.confirm) {
                  that.copyMessage(link);
                }
              }
            });
          }
        });
    },
    chooseType() {
      this.$refs.choosePlanPopup.open();
    },
    navigatePlan() {
      if (this.selectedOption == '') {
        uni.showToast({
          title: '请先选择',
          icon: 'none'
        });
        return;
      }
      this.$refs.choosePlanPopup.close();
      if (this.selectedOption == 'plan') {
        //TODO
        this.downLoadWord();
      } else {
        this.captureModules();
      }
      this.selectedOption = '';
    },
    closeChoosePlan() {
      this.$refs.choosePlanPopup.close();
      this.selectedOption = '';
    },
    selectOption(option) {
      this.selectedOption = option;
    },
    copyMessage(value) {
      // #ifdef MP-WEIXIN
      uni.setClipboardData({
        data: value,
        success: function (res) {
          uni.getClipboardData({
            success: function (res) {
              uni.showToast({
                title: '复制成功'
              });
            }
          });
        }
      });
      // #endif
      // #ifndef MP-WEIXIN
      uni.setClipboardData({
        data: value,
        success: function () {
          uni.showToast({
            title: '复制成功',
            icon: 'success'
          });
        },
        fail: function () {
          uni.showToast({
            title: '复制失败',
            icon: 'none'
          });
        }
      });
      // #endif
    },
    captureModules() {
      // let token =
      //   '****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************';
      let token = uni.getStorageSync('token');
      // let url = `https://kx.ngrok.dxznjy.com/pages/aiLessonPlanReport?id=${this.reviewId}&studentCode=${this.studentCode}&token=${token}`;
      let url = `https://kx.ngrok.dxznjy.com/#/?id=${this.reviewId}&studentCode=${this.studentCode}&token=${token}&isBack=${this.isBack}`;
      uni.navigateTo({ url: `/pages/index/web?url=${encodeURIComponent(url)}` });
    },
    getGrade(val) {
      if (val) {
        val -= 1;
        let index = 0;
        if (val <= 0) {
          index = 0;
        } else if (val >= this.gradeNameArr.length) {
          index = this.gradeNameArr.length - 1;
        } else {
          index = val;
        }
        return this.gradeNameArr[index];
      }
      return this.gradeNameArr[this.gradeNameArr.length - 1];
    },
    // 将真实数值转换为柱子的高度（rpx）
    toBarHeight(val) {
      const maxVal = 1000;
      const maxHeight = 240; // 最大柱子高度，单位 rpx
      const h = Math.round((val / maxVal) * maxHeight);
      return `${h}rpx`;
    },
    confirmFn() {
      this.toastShow = false;
    },
    beforeleave() {
      let that = this;
      that.isShow = false; //这个很重要，一定要先把弹框删除掉
      that.goBack();
    },
    goBack() {
      console.log(this.isBack, '返回数据');
      console.log(this.isBack == 'true', '返回数据');
      if (this.isBack == 'true') {
        // #ifdef APP-PLUS
        uni.navigateTo({
          url: `/lessonPlan/planReportList?merchantCode=${this.merchantCode}`
        });
        // #endif
        // #ifdef MP-WEIXIN
        uni.navigateBack();
        // #endif
      } else {
        uni.navigateTo({
          url: `/Trialclass/recommend?merchantCode=${this.merchantCode}`
        });
      }
    },
    goUrl(url) {
      getApp().sensors.track('aiReviewReportClick', {
        name: '往期复习'
      });
      uni.navigateTo({
        url: url
      });
    },
    handleShare() {
      getApp().sensors.track('aiLessonPlanReportClick', {
        name: '分享报告'
      });
    },
    appHandleShare() {
      const platform = uni.getSystemInfoSync().platform;
      let shareInfo = {
        title: '分享报告',
        imageUrl: '',
        path: `/parentEnd/report/aiLessonPlanReport?id=${this.reviewId}&history=1&noDownload=1&studentCode=${this.studentCode}&isShow=false`
      };
      if (platform === 'ios') {
        shareInfo.imageUrl = 'https://document.dxznjy.com/dxSelect/8e90eadf-3d59-4130-b00f-cfe5f9339540.png';
      }
      uni.$appShare(shareInfo, 2);
      plus.runtime.quit();
    },
    async getReviewReport(id, isAI) {
      let that = this;
      uni.showLoading({
        title: '加载中',
        duration: 1500
      });
      let result = await this.$httpUser.get('znyy/wap/student-lesson-plan/report', {
        planCode: id,
        isAi: isAI
      });
      console.log(result, '1111111111111111111111');
      if (result && result.data.success) {
        let data = result.data.data;
        this.reportData = data;
        let arr = [data.wordLevel ? data.wordLevel : 0, data.expWords ? data.expWords : 0];
        this.dataList = arr;
      }
      this.isLoading = false;
    },
    async getQuestionStatus() {
      let res = await this.$httpUser.get('znyy/word/review/queryQuestionnaire', {
        studentCode: this.studentCode,
        type: 2
      });
      if (res && res.data.success) {
        if (this.isShowLesson == 'false' || res.data.data) {
          return;
        } else {
          setTimeout(() => {
            this.showModal = true;
          }, 4000);
        }
      }
    },
    confirmStar(e) {
      let that = this;
      let obj = {};
      obj.studentCode = that.studentCode;
      obj.questionnaireContent = JSON.stringify(e);
      obj.type = 2;
      that.$httpUser.post('znyy/word/review/saveQuestionnaire', obj).then((res) => {
        if (res && res.data.success) {
          uni.showToast({
            title: '感谢您的参与',
            icon: 'none'
          });
          setTimeout(() => {
            that.showModal = false;
          }, 1000);
        }
      });
    },
    closeDialog() {
      this.showModal = false;
    }
  }
};
</script>

<style lang="less">
page {
  height: 100%;
  background-color: #3ab77f;
}
.header {
  position: absolute;
  top: 100rpx;
  left: 30rpx;
}
.report-container {
  padding: 20rpx;
  background: url('https://document.dxznjy.com/course/1747634835000') no-repeat;
  background-size: contain;
  // background-size: cover;
  // background-position: top;
  // background: linear-gradient(180deg, #edfdf4 0%, #96e8ca 100%);
  height: auto;
  min-height: 1000rpx;
  .toastTop {
    font-size: 16rpx;
    color: #c0c0c0;
    text-align: center;
  }
}
.fixText {
  font-size: 26rpx;
  text-indent: 52rpx;
  color: #555;
  margin: 30rpx auto;
  background-color: #e9fdf5;
  border-radius: 12rpx;
  padding: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
  line-height: 1.6;
  text-align: justify;
  p {
    margin-bottom: 10rpx;
  }
}
.charts-box {
  // width: 641rpx;
  // height: 300rpx;
  border-radius: 50rpx;
  margin: 20rpx auto;
  .container {
    background-color: #f0fef8;
    padding: 40rpx 0;
    text-align: center;
    font-family: sans-serif;
  }

  .chart {
    display: flex;
    // justify-content: center;
    justify-content: space-around;
    align-items: flex-end;
    height: 300rpx;
    // gap: 100rpx;
    margin-bottom: 30rpx;
    position: relative;
  }
  .title-top {
    position: absolute;
    top: 0;
    text-align: center;
  }
  .bar-container {
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  /* 顶部数值 */
  .value-text {
    font-size: 24rpx;
    color: #333;
    margin-bottom: 10rpx;
  }

  /* 柱子 */
  .bar {
    width: 40rpx;
    border-radius: 20rpx 20rpx 0 0;
    background: linear-gradient(to top, #8be2bd, #f3f3f3);
    transition: height 0.4s ease;
  }

  /* 底部横线 */
  .baseline {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 2rpx;
    background-color: #ccc;
  }

  /* x轴标签 */
  .labels {
    display: flex;
    // justify-content: center;
    justify-content: space-around;
    // gap: 100rpx;
    font-size: 24rpx;
    color: #666;
    margin-bottom: 20rpx;
  }

  .label {
    // width: 120rpx;
    text-align: center;
  }

  /* 主标题 */
  .title {
    font-size: 28rpx;
    font-weight: bold;
    color: #333;
  }

  .title::before {
    content: '● ';
    color: #8be2bd;
  }
}
.base-info {
  // background-color: #fff;
  background-image: url('https://document.dxznjy.com/course/1747645744000');
  background-repeat: no-repeat;
  // background: url('https://document.dxznjy.com/automation/1747622914000') no-repeat;
  // background-size: cover;
  background-size: cover;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  min-height: 1200rpx;
  border-radius: 50rpx;
  padding-bottom: 20rpx;
  .top-title {
    display: flex;
    align-items: center;
    justify-content: space-around;
    height: 100rpx;
    line-height: 100rpx;
    margin-top: 10rpx;
    .day {
      position: absolute;
      right: 100rpx;
      color: #f4f4e5;
      font-size: 110rpx;
      font-weight: bold;
      opacity: 1;
      z-index: 0;
    }
    .right {
      font-size: 69rpx;
      color: #f37c00;
      margin-right: 68rpx;
      font-weight: 700;
      z-index: 88;
    }
    .left {
      font-size: 28rpx;
      color: #555555;
    }
  }
  .bottom {
    // background-color: red;
    margin-top: 28rpx;
    // padding-top: 46rpx;
    border-radius: 50rpx;

    flex: 1;
    position: relative;
    .tag {
      position: absolute;
      top: 90rpx;
      left: -10rpx;
    }
    .content {
      width: 641rpx;
      min-height: 685rpx;
      background: #ffffff;
      border-radius: 24rpx;
      border: 5rpx solid #dcf6eb;
      opacity: 0.9;
      margin: 180rpx auto 0;
      .item {
        font-size: 28rpx;
        margin-bottom: 15rpx;
        border-bottom: 1px dashed #979797;
        padding: 20rpx 0;
      }
    }
  }
}
.base-info-1 {
  background-image: url('https://document.dxznjy.com/course/2e736510453648f59b44ffb2b20aea42.png');
  background-repeat: no-repeat;
  // background: url('https://document.dxznjy.com/automation/1747622914000') no-repeat;
  background-size: contain;
  background-color: #f0fef8;
  // background-size: contain;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  min-height: 1200rpx;
  border-radius: 50rpx;
  padding-bottom: 20rpx;

  .bottom {
    border-radius: 50rpx;
    flex: 1;
    position: relative;
    .tag {
      position: absolute;
      top: 28rpx;
      left: -10rpx;
    }
    .content {
      width: 641rpx;
      min-height: 685rpx;
      background: #ffffff;
      border-radius: 24rpx;
      border: 5rpx solid #dcf6eb;
      opacity: 0.9;
      margin: 136rpx auto 0;
      .item {
        font-size: 28rpx;
        margin-bottom: 15rpx;
        border-bottom: 1px dashed #979797;
        padding: 20rpx 0;
      }
    }
  }
}
.now-study {
  background: url('https://document.dxznjy.com/course/1747645744000') no-repeat;
  background-size: cover;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  min-height: 1200rpx;
  border-radius: 50rpx;
  margin-top: 48rpx;
  .top-title {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100rpx;
    line-height: 100rpx;
    font-size: 36rpx;
    color: #016934;
    font-weight: 700;
  }
  .bottom {
    margin-top: 46rpx;
    border-radius: 50rpx;
    flex: 1;
    .bottom-title {
      font-size: 32rpx;
      font-weight: 700;
      color: #000000;
      text-align: center;
      padding: 35rpx 0 0 0;
      margin-top: 20rpx;
    }
    .content {
      width: 641rpx;
      min-height: 685rpx;
      background: #ffffff;
      border-radius: 24rpx;
      border: 5rpx solid #dcf6eb;
      opacity: 0.9;
      margin: 30rpx auto;
      .button-container {
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 30rpx 0;
      }
      .button {
        background: #006833;
        border-radius: 38rpx;
        color: #fff;
        text-align: center;
        padding: 20rpx 40rpx;
        font-size: 28rpx;
      }
      .content-item {
        margin-top: 30rpx;
        .tag {
          width: 641rpx;
          height: 75rpx;
          background: linear-gradient(270deg, #fefffe 0%, #dcf6eb 100%);
          display: flex;
          align-items: center;
          .point {
            width: 27rpx;
            height: 27rpx;
            border-radius: 50%;
            border: 1rpx solid #5bc28a;
            display: flex;
            justify-content: center;
            align-items: center;
            margin: 0 14rpx;
            .point-item {
              width: 12rpx;
              height: 12rpx;
              border-radius: 50%;
              background: #60c58b;
            }
          }
          .title {
            font-size: 32rpx;
            color: #000000;
            font-weight: 600;
          }
        }
        .item-box {
          background: #f6fffa;
          border-radius: 18rpx;
          box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
          padding-top: 28rpx;
          margin: 0 auto 24rpx auto;
          width: 95%;
        }
        .stage-title {
          font-size: 30rpx;
          color: #1a7f5a;
          font-weight: bold;
          margin-bottom: 18rpx;
        }
        .stage-section {
          // display: flex;
          margin-bottom: 12rpx;
          align-items: flex-start;
        }
        .stage-label {
          color: #1a7f5a;
          font-size: 26rpx;
          // min-width: 120rpx;
          font-weight: 700;
          margin: 20rpx 0;
        }
        .stage-label::before {
          content: '● ';
          color: #8be2bd;
        }
        .stage-value {
          margin: 20rpx 0;
          color: #a3a2a2;
          font-size: 26rpx;
          flex: 1;
          word-break: break-all;
        }
      }
    }
    .noteBg {
      background-color: #e8fdf4;
      border-radius: 15rpx;
    }
  }
}

.forget-word {
  background: url('https://document.dxznjy.com/automation/1747622810000') no-repeat;
  background-size: cover;
  // background-size: contain;
  min-height: 1000rpx;
  border-radius: 50rpx;
  margin-top: 48rpx;
  .top-title {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100rpx;
    line-height: 100rpx;
    font-size: 36rpx;
    color: #016934;
    font-weight: 700;
  }
  .bottom {
    border-radius: 50rpx;
    display: flex;
    justify-content: center;
    .content {
      width: 641rpx;
      min-height: 685rpx;
      background: #f5fffa;
      border-radius: 24rpx;
      border: 5rpx solid #dcf6eb;
      opacity: 0.9;
      margin-left: 9rpx;
      .plan-header {
        text-align: left;
        margin-bottom: 40rpx;
        .plan-title {
          width: 205rpx;
          height: 56rpx;
          line-height: 56rpx;
          background: #016936;
          border-radius: 13rpx 20rpx 20rpx 0rpx;
          font-size: 32rpx;
          color: #ffffff;
          padding-left: 20rpx;
          font-weight: 700;
          margin: 16rpx 0;
        }
        .plan-subtitle {
          font-size: 28rpx;
          color: #555555;
        }
      }
      .plan-content {
        .title {
          font-size: 32rpx;
          color: #000000;
          font-weight: bold;
          text-align: center;
        }
        .description {
          font-size: 28rpx;
          color: #666666;
          margin-top: 35rpx;
          line-height: 42rpx;
          text-align: left;
        }
      }
    }
  }
}

.table-data {
  background: url('https://document.dxznjy.com/automation/1747622854000') no-repeat;
  // background-size: cover;
  background-size: contain;
  min-height: 1000rpx;
  border-radius: 50rpx;
  margin-top: 48rpx;
  .top-title {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100rpx;
    line-height: 100rpx;
    font-size: 36rpx;
    color: #016934;
    font-weight: 700;
  }
  .bottom {
    border-radius: 50rpx;
    display: flex;
    justify-content: center;
    .content {
      width: 641rpx;
      min-height: 685rpx;
      background: #ffffff;
      border-radius: 24rpx;
      border: 5rpx solid #dcf6eb;
      opacity: 0.9;
      margin-left: 9rpx;
      .service-header {
        text-align: center;
        margin-bottom: 40rpx;
        .service-title {
          font-size: 32rpx;
          color: #016934;
          font-weight: bold;
          margin-bottom: 10rpx;
        }
        .service-subtitle {
          font-size: 24rpx;
          color: #666;
        }
      }
      .service-content {
      }
    }
  }
}

.table-container {
  margin-top: 4rpx;
  // padding: 20rpx;
  padding: 20rpx;
  background: #fff;
  border-radius: 12rpx;
  overflow-y: auto;
  // height: 450rpx;
  max-height: 450rpx;
  min-height: 150rpx;
}

.table-title {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
  text-align: left;
}

.table-row {
  display: flex;
  // padding: 20rpx 0;
  color: #21504b;
  height: 60rpx;
  line-height: 60rpx;
  &:nth-child(even) {
    background-color: #fff;
  }
  &:nth-child(odd) {
    background-color: #f9fbfe;
  }
  // border: 1rpx solid #eee;
  // border-bottom: none;
}
.break-all {
  word-break: break-all; /* 强制在任意位置换行 */
  overflow-wrap: anywhere;
  white-space: normal; /* 允许正常换行 */
}

.col-word,
.col-number,
.col-word1,
.col-number1 {
  flex: 1; /* 单词列宽度占比更大 */
  text-align: center;
  // padding-left: 20rpx;
  // border-right: 1rpx solid #eee;
  font-size: 24rpx;
  word-wrap: inherit;
  white-space: nowrap; /* 不换行 */
  overflow: hidden; /* 隐藏超出部分 */
  text-overflow: ellipsis; /* 超出部分显示省略号 */
}
.col-number:last-child {
  border-right: none;
}
/* 最后一行去除底部边框 */
// .table-row:last-child {
//   border-bottom: 1rpx solid #eee;
// }
.footer {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 40rpx;
  image {
    width: 686rpx;
    height: 96rpx;
  }
  .footer-btn {
    width: 100%;
    display: flex;
    justify-content: space-around;
    align-items: center;
  }
  .btn {
    width: 264rpx;
    height: 82rpx;
    border-radius: 50rpx;
    line-height: 82rpx;
    text-align: center;
    box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.1), 0px 1px 3px rgba(0, 0, 0, 0.08);
    transition: all 0.2s ease;
    &:active {
      box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.2), 0px 1px 3px rgba(0, 0, 0, 0.15);
      transform: translateY(2px);
    }
  }
  .cancel {
    background: #04be87;
    color: #fff;
  }
  .confirm {
    background: #428a6f;
    color: #fff;
  }
}
.suggest {
  // width: 662rpx;
  // height: 316rpx;
  // max-height: 300rpx;
  min-height: 150rpx;
  background: #fafffd;
  border-radius: 0rpx 40rpx 0rpx 40rpx;
  font-size: 28rpx;
  color: #21504b;
  line-height: 44rpx;
  margin-top: 40rpx;
  border-radius: 20rpx;
  .suggest-box {
    height: 260rpx;
    overflow-y: auto;
    .suggest-item {
      // text-indent: 2rem;
      display: flex;
      align-items: flex-start;
      justify-content: space-between;
      margin-bottom: 10rpx;
    }
    .point {
      width: 16rpx;
      height: 16rpx;
      background: #04be87;
      margin: 10rpx 10rpx 0 0;
    }
  }
  .left-btn {
    width: 236rpx;
    height: 62rpx;
    background: #04be87;
    border-radius: 34rpx;
    line-height: 62rpx;
    text-align: center;
    font-size: 32rpx;
    font-weight: bold;
    color: #fff;
  }
}
.nodata {
  display: flex;
  flex-direction: column;
  justify-content: space-around;
  align-items: center;
  color: #009e70;
  font-size: 28rpx;
  font-weight: 700;
  padding: 20rpx 0;
}
.modal-title {
  font-size: 28rpx;
  color: #555555;
  margin-bottom: 10rpx;
}
.confirmButton1 {
  display: flex;
  justify-content: space-around;
  align-items: center;
  .btn {
    width: 510rpx;
    height: 82rpx;
    background: #428a6f;
    border-radius: 12rpx;
    line-height: 82rpx;
    text-align: center;
  }
  .confirm {
    background: #428a6f;
    color: #fff;
  }
}
.capture-btn {
  width: 327rpx;
  height: 82rpx;
  background: #428a6f;
  border-radius: 41rpx;
  color: #fff;
  font-size: 28rpx;
  line-height: 82rpx;
  text-align: center;
  margin: 0 20rpx;

  &:active {
    opacity: 0.8;
  }
}
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  background-color: #edfdf4;
  .loading-text {
    font-size: 32rpx;
    color: #555;
    font-weight: 500;
  }
}
//弹窗
.shareCode,
.choosePlan {
  position: relative;
  background: #ffffff;
  color: #000;
  padding-top: 50upx;
  box-sizing: border-box;
  overflow: hidden;
  width: 90vw;
}
.choosePlan {
  border-radius: 22rpx;
}

.activityRule {
  text-align: center;
  font-weight: bold;
  position: absolute;
  top: 40rpx;
  left: 0;
  width: 100%;
  background: white;
  z-index: 10;
}

.review_close {
  position: absolute;
  /* 固定在右上角 */
  top: 40rpx;
  right: 20rpx;
  z-index: 10;
  /* 确保在上层 */
}

.rule {
  padding: 20rpx;
  line-height: 50rpx;
  margin-top: 60rpx;
  /* 确保不被固定元素遮挡 */
  overflow-y: auto;
  /* 允许滚动 */
  height: calc(100% - 40px);
  /* 适应滚动区域的高度 */
}
.select-btn {
  width: 90%;
  height: 96rpx;
  line-height: 96rpx;
  margin: 0 auto;
  margin-bottom: 24rpx;
  border: 2rpx solid #e6e6e6;
  color: #636363;
  font-size: 28rpx;
  border-radius: 10rpx;
}

/* 选中状态 */
.selected {
  background: #f7fff9;
  border: 2rpx solid #d0e3dd;
  color: #287e66;
}
.popupEnd {
  height: 130rpx;
  display: flex;
  justify-content: space-evenly;
}
.comfirmChoose,
.closePopup {
  width: 45%;
  height: 92rpx;
  line-height: 92rpx;
  border-radius: 46rpx;
  font-size: 32rpx;
}
.comfirmChoose {
  background-color: #428a6f;
  color: #fff;
}
.closePopup {
  border: 2rpx solid #428a6f;
  color: #428a6f;
}
</style>
