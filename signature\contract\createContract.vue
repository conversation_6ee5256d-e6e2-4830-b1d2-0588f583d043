<template>
  <view class="ctxt plr-30 bg-ff f-28">
    <view class="bg-ff radius-20 pt-30" style="height: 100vh">
      <uni-forms ref="baseForm" :rules="rules" :modelValue="baseFormData">
        <!-- name表单域的属性名，在使用校验规则时必填 -->
        <uni-forms-item required name="qsfjs">
          <view class="titleleft f-30">
            <text>签署方角色</text>
          </view>
          <view class="">
            <radio-group @change="radioChange">
              <label class="radio">
                <radio value="1" checked="true" style="transform: scale(0.7)" />
                企业
              </label>
              <label class="radio">
                <radio value="2" style="transform: scale(0.7)" />
                个人
              </label>
            </radio-group>
          </view>
        </uni-forms-item>
        <uni-forms-item required name="htmb">
          <view class="titleleft f-30">
            <text>合同模版</text>
          </view>
          <view class="r_box">
            <picker @change="bindPickerChange" :value="modelIndex" :range="modelList" range-key="templateName" class="uni-input">
              <view class="contractExample">{{ modelList[modelIndex] && modelList[modelIndex].templateName ? modelList[modelIndex].templateName : '暂无模板' }}</view>
            </picker>
          </view>
        </uni-forms-item>
        <uni-forms-item required name="qsrName">
          <view class="titleleft f-30">
            <text>{{ baseFormData.signerType == 1 ? '企业名称' : '姓名' }}</text>
          </view>
          <view class="r_box">
            <input
              placeholder-style="color:#999;"
              maxlength="30"
              v-model="baseFormData.signerName"
              class="flex-box f-30 uni-input"
              :placeholder="baseFormData.signerType == 1 ? '请输入企业名称' : '请输入姓名'"
            />
          </view>
        </uni-forms-item>
        <uni-forms-item required name="qsrPhone" v-if="baseFormData.signerType == 1">
          <view class="titleleft f-30">
            <text>经办人姓名</text>
          </view>
          <view class="r_box">
            <input placeholder-style="color:#999" maxlength="11" v-model="baseFormData.transactorName" class="flex-box f-30 uni-input" placeholder="请输入经办人姓名" />
          </view>
        </uni-forms-item>
        <uni-forms-item required name="qsrPhone">
          <view class="titleleft f-30">
            <text>签署人手机号</text>
          </view>
          <view class="r_box">
            <input placeholder-style="color:#999" maxlength="11" v-model="baseFormData.signerPhone" class="flex-box f-30 uni-input" placeholder="请输入签署人手机号" />
            <view style="color: red" class="f-22">*该手机号会收到签署短信，请确保手机号正确。</view>
          </view>
        </uni-forms-item>
      </uni-forms>
    </view>
    <view class="m-45">
      <button class="nextstep" form-type="submit" @click="submit('baseForm')" :disabled="createdStatus">创建合同</button>
    </view>
  </view>
</template>

<script>
  import uniForms from '../components/uni-forms/components/uni-forms/uni-forms.vue';
  import uniFormsItem from '../components/uni-forms/components/uni-forms-item/uni-forms-item.vue';
  export default {
    components: {
      uniForms,
      uniFormsItem
    },
    data() {
      return {
        createdStatus: false,
        // 基础表单数据
        baseFormData: {
          signerType: 1,
          contractTemplateId: '',
          signerName: '',
          signerPhone: '',
          transactorName: '',
          userId: uni.getStorageSync('user_id') ? uni.getStorageSync('user_id') : ''
        },
        modelIndex: 0,
        modelList: [],
        authType: 0,
        // 校验规则
        rules: {
          signerName: {
            rules: [
              {
                required: true,
                errorMessage: '名称不能为空'
              }
            ]
          },
          signerPhone: {
            rules: [
              {
                required: true,
                errorMessage: '手机号不能为空'
              }
            ]
          }
        }
      };
    },
    onLoad(option) {
      if (option != null) {
        this.authType = option.authType || 0;
      }
      this.fetchsSelect();
    },
    methods: {
      //获取模板
      async fetchsSelect() {
        await this.$httpUser
          .get('zx/wap/contract/template/selectItems', {
            creatorType: this.authType,
            signerType: this.baseFormData.signerType
          })
          .then((res) => {
            if (res.data.data.length > 0) {
              this.modelList = res.data.data;
              this.baseFormData.contractTemplateId = this.modelList[0].id;
            } else {
            }
          });
      },
      bindPickerChange(e) {
        this.modelIndex = Number(e.target.value);
        this.baseFormData.contractTemplateId = this.modelList[Number(e.target.value)].id;
      },
      submit(ref) {
        this.$refs[ref]
          .validate()
          .then(async (res) => {
            if (this.baseFormData.signerType == 1) {
              if (this.baseFormData.transactorName == '') {
                this.$util.alter('请输入经办人姓名');
                return false;
              }
            }
            // 加手机号验证规则
            let reg = /^[1][3,4,5,7,8,9][0-9]{9}$/; //正则表达式定义手机号正确格式
            if (!reg.test(this.baseFormData.signerPhone)) {
              uni.showToast({
                title: '请输入正确的手机号',
                icon: 'none'
              });
              return;
            }
            uni.showLoading({
              title: '创建中...'
            });
            this.createdStatus = true;
            await this.$httpUser.post('zx/wap/contract/create', this.baseFormData).then((res) => {
              if (res.data.data) {
                let contractTimer = setTimeout(() => {
                  this.$httpUser
                    .get('zx/wap/contract/signUrl', {
                      contractId: res.data.data.contractId,
                      userId: uni.getStorageSync('user_id') ? uni.getStorageSync('user_id') : ''
                    })
                    .then((res) => {
                      if (res.data.data) {
                        clearTimeout(contractTimer);
                        uni.hideLoading();
                        this.createdStatus = false;
                        uni.navigateTo({
                          url: '/signature/contract/signingPage?url=' + encodeURIComponent(JSON.stringify(res.data.data.signUrl))
                        });
                      } else {
                        uni.showToast({
                          title: '创建失败'
                        });
                        uni.hideLoading();
                        uni.navigateBack();
                      }
                    });
                }, 4000);
              } else {
                this.createdStatus = false;
                uni.hideLoading();
                /* uni.showToast({
						title:res.data.message
					}) */
              }
            });
            /* 	uni.showToast({
							title: `校验通过`
						})
						uni.redirectTo({
							url: '/signature/contract/cResult'
						}) */
          })
          .catch((err) => {
            console.log('err', err);
          });
      },
      radioChange(val) {
        this.modelIndex = 0;
        this.baseFormData.signerType = val.detail.value;
        this.baseFormData.signerName = '';
        this.baseFormData.signerPhone = '';
        this.baseFormData.transactorName = '';
        this.fetchsSelect();
      }
    }
  };
</script>

<style scoped>
  ::v-deep .uni-data-checklist .checklist-group .checklist-box.is--default.is-checked .radio__inner .radio__inner-icon {
    background-color: #248067;
  }

  ::v-deep .uni-data-checklist .checklist-group .checklist-box.is--default.is-checked .radio__inner {
    border-color: #248067;
  }

  ::v-deep .uni-data-checklist .checklist-group .checklist-box.is--default.is-checked .checklist-text {
    color: #383838;
  }

  ::v-deep .uni-data-checklist .checklist-group {
    float: right;
  }

  ::v-deep .uni-forms-item__content {
    display: flex;
    line-height: 36px;
  }
  .contractExample {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .titleleft {
    width: 30%;
  }

  .r_box {
    width: 70%;
  }

  .uni-input {
    height: 64rpx;
    padding-left: 32rpx;
    background-color: #f3f8fc;
  }
</style>
