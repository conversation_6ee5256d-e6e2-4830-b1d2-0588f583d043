<!-- 超人码出货处理中 -->
<template>
  <view class="plr-30">
    <view class="bg-ff plr-30 pb-40 radius-15 positionRelative" :style="{ height: useHeight + 'rpx' }">
      <view class="ptb-30 c-00 f-30">
        <view class="mb-12 flex-s">
          <view class="f-30 mb-10">
            {{ orderInfo.remark }}
          </view>
          <view class="classPayStatus_btn" style="background-color: #e57126">处理中</view>
        </view>
        <view class="flex-s f-28 c-33">{{ orderInfo.createdTime }}</view>
      </view>

      <view class="count_down positionAbsolute" style="bottom: 30rpx; width: 84.5%" @click="getSell">
        <view>通过</view>
      </view>
    </view>
  </view>
</template>

<script>
  const { $http } = require('@/util/methods.js');
  import dayjs from 'dayjs';
  export default {
    data() {
      return {
        useHeight: 0, //除头部之外高度
        svHeight: 50,
        orderInfo: '',
        countdown: '' // 倒计时
      };
    },

    onLoad(e) {
      this.orderInfo = JSON.parse(decodeURIComponent(e.orderInfo));
    },
    onReady() {
      let that = this;
      uni.getSystemInfo({
        //调用uni-app接口获取屏幕高度
        success(res) {
          // 可使用窗口高度，将px转换rpx
          let h = res.windowHeight * (750 / res.windowWidth);
          that.useHeight = h - 65;
        }
      });
    },

    onShow() {
      this.getCountdown();
    },

    methods: {
      // 倒计时处理
      getCountdown() {
        let dateTime = dayjs().unix() * 1000;
        console.log(dateTime);
        let setTime = dayjs(this.orderInfo.expireTime).unix() * 1000;
        console.log(setTime);
        this.countdown = setTime - dateTime;
      },

      // 出货审核申请
      async getSell() {
        if (this.orderInfo.dealStatus == 0) {
          this.$util.alter('您的超人码不足,请先申购超人码');
          return;
        }
        const res = await $http({
          url: 'zx/invitation/code/auditApplyOutCode',
          data: {
            id: this.orderInfo.id
          }
        });
        if (res) {
          uni.navigateBack();
          // this.$util.alter('审核成功');
          // setTimeout(() => {
          // 	uni.navigateBack()
          // }, 1000)
          // uni.navigateBack()
        } else {
          this.$util.alter('出货失败啦，请稍后再试');
        }
      }
    }
  };
</script>

<style lang="scss" scoped>
  page {
    background-color: #fff;
  }

  .count_down {
    color: #fff;
    height: 90rpx;
    padding: 0 25rpx;
    line-height: 90rpx;
    text-align: center;
    border-radius: 45rpx;
    background: linear-gradient(to bottom, #88cfba, #1d755c);

    /deep/ .u-count-down__text {
      color: #fff !important;
      padding: 0 10rpx !important;
    }
  }

  .prompt {
    color: #e57126;
  }

  .time {
    /deep/ .u-count-down__text {
      color: #e57126 !important;
    }
  }
</style>
