<template>
  <view>
    <view class="big" v-if="data">
      <image :src="imgHost + 'dxSelect/fourthEdition/GHBG.png'" class="ghbg"></image>
      <view class="uesr">
        <image :src="data.avatar" class="avater"></image>
        <!-- <view class="avater"></view> -->
        <view class="name">{{ data.desc }}</view>
        <view class="type">{{ data.type[0] }}</view>
        <view class="text">
          {{ data.congratulate }}
        </view>
        <view class="title">
          <view class="line"></view>
          <view class="name1">类型分布图</view>
        </view>
        <view class="circle">
          <image :src="imgHost + 'dxSelect/fourthEdition/<EMAIL>'" class="fenbutu"></image>
          <view class="one bie">{{ data.type[0] }}</view>
          <view class="two bie">{{ data.type[1] }}</view>
          <view class="three bie">{{ data.type[2] }}</view>
        </view>
      </view>
      <view class="suggestion">
        <view class="headline">
          <span style="font-size: 40rpx; color: #359479; margin-right: 8rpx">01</span>
          教育沟通建议
        </view>

        <view class="paragraph" v-for="(item, index) in data.communication" :key="index">
          {{ item }}
        </view>
        <!-- <view class="paragraph">
					营造一个有序而温馨的家庭环境吧！既要像稳固的大本营，给孩子安全感，也要教会他们适应生活的即兴演出。强调规则的重要性，但更要培养孩子灵活应对变化的能力。
				</view> -->
        <view class="bonus" @click="govideo">
          <image src="https://document.dxznjy.com/course/30c24d002e94409c88347eabe868cfac.jpg" mode="" style="width: 100%; height: 100%"></image>
        </view>
      </view>
      <!--  style="background-image: url('../../static/pic_zhong.png');" -->
      <view class="project">
        <view class="headline">
          <span style="font-size: 40rpx; color: #359479; margin-right: 8rpx">02</span>
          成长规划
        </view>
        <view class="headlinet">1.学科规划</view>
        <view class="paragraph" v-for="item in data.disciplinaryPlan" :key="item">
          {{ item }}
        </view>
        <view class="course">
          <view style="position: relative" v-for="(item, index) in data.disciplinaryGoods" :key="index">
            <helang-waterfall
              width="300rpx"
              :item="item"
              :share="false"
              :identityType="4"
              @click="skintap('Coursedetails/productDetils?id=' + item.goodsId)"
              tag="right"
              :itemStyle="true"
              :index="index"
            ></helang-waterfall>
            <view class="play" v-if="item.goodsVideoUrl" @click="paly(item.goodsVideoUrl)">
              <image :src="imgHost + '/dxSelect/fourthEdition/icon_play_opacity.png'" style="width: 100%; height: 100%" mode=""></image>
            </view>
          </view>
        </view>
        <view class="headlinet">2.能力规划</view>
        <view class="paragraph" v-for="item in data.abilityPlan" :key="item">
          {{ item }}
        </view>
        <view class="course">
          <view style="position: relative" v-for="(item, index) in data.abilityGoods" :key="index">
            <helang-waterfall
              width="300rpx"
              :item="item"
              :share="false"
              @click="skintap('Coursedetails/productDetils?id=' + item.goodsId)"
              tag="right"
              :index="index"
            ></helang-waterfall>
            <view class="play" @click="paly(item.goodsVideoUrl)" v-if="item.goodsVideoUrl">
              <image :src="imgHost + '/dxSelect/fourthEdition/icon_play_opacity.png'" style="width: 100%; height: 100%" mode=""></image>
            </view>
          </view>
        </view>
        <view class="headlinet">3.升学规划</view>
        <view class="paragraph" v-for="item in data.furtherEducationPlan" :key="item">
          {{ item }}
        </view>
      </view>
    </view>
    <!-- 	<view class="" @click="btn">
			立即分享
		</view> -->
    <uni-popup ref="videoPopup" type="center">
      <!-- <video id="myVideo" class="video_css_content" :src="videoSrc" controls></video> -->
      <view class="video_css_content">
        <polyv-player
          id="polyv-player-id"
          @loadedmetadata="bindloadedmetadata"
          :autoplay="false"
          :playerId="playerIdcont"
          :vid="videoSrc"
          width="100%"
          height="100%"
          :ts="ts"
          :sign="sign"
        ></polyv-player>
      </view>
    </uni-popup>
    <uni-popup ref="sharePopup" type="bottom" style="padding: 0">
      <view class="shareCard">
        <view class="review_close" @click="closeDialog">
          <uni-icons type="clear" size="26" color="#B1B1B1"></uni-icons>
        </view>
        <view class="displayflexbetween mt-30 ptb-30" style="justify-content: space-around">
          <view class="t-c" @click="posterShare">
            <image :src="imgHost + 'dxSelect/share_img.png'" class="shareIcon" mode=""></image>
          </view>
          <view class="t-c">
            <button open-type="share" class="fillButton">
              <image :src="imgHost + 'dxSelect/share_lj.png'" class="shareIcon" mode=""></image>
            </button>
          </view>
        </view>
        <view class="bd-ee"></view>
      </view>
    </uni-popup>
  </view>
</template>

<script>
  const { $http, $navigationTo } = require('@/util/methods.js');
  import helangWaterfall from '@/components/helang-waterfall/helang-waterfall';
  const MD5 = require('../../util/md5.js');
  let secretkey = 'Jkk4ml1Of8';
  let vid = '1723c885636f2f7f4c603f898bdcd983_1';
  let ts = new Date().getTime();
  let sign = '';
  export default {
    data() {
      return {
        imgHost: getApp().globalData.imgsomeHost,
        id: '',
        data: null,
        videoSrc: null,
        vid: vid,
        ts: ts,
        sign: sign,
        //保利威视频
        playerIdcont: 'polyvPlayercont'
      };
    },
    onShareAppMessage() {
      return {
        title: this.data.desc + '的学业生涯报告',
        imageUrl: '', //分享封面
        path: '/Personalcenter/Career/planning?id=' + this.id
      };
    },
    components: {
      helangWaterfall
    },
    onLoad(options) {
      this.id = options.id;
      console.log(options);
    },
    onShow() {
      this.init();
    },
    methods: {
      paly(url) {
        this.videoSrc = url;
        setTimeout(() => {
          this.getVideo(url);
        }, 100);
        this.$refs.videoPopup.open();
      },
      getVideo(e) {
        let _this = this;
        let polyvPlayerContext = _this.selectComponent('#polyv-player-id');
        const ts = new Date().getTime();
        const sign = MD5.md5(`${secretkey}${e}${ts}`);
        polyvPlayerContext.changeVid({
          // vid: _this.videoSrc,
          vid: e,
          ts,
          sign
        });
      },
      govideo() {
        uni.navigateTo({
          url: '/Coursedetails/culturalType/familyCulture'
        });
      },
      btn() {
        this.$refs.sharePopup.open();
      },
      bindloadedmetadata() {
        let polyvPlayerContext = this.selectComponent('#polyv-player-id');
        polyvPlayerContext.switchQuality(1);
      },
      skintap(url) {
        if (!uni.getStorageSync('token')) {
          uni.navigateTo({
            url: '/Personalcenter/login/login'
          });
        } else {
          $navigationTo(url);
        }
      },

      async init() {
        if (this.data) return;
        uni.showLoading({
          title: '加载中...'
        });
        console.log(this.id);
        let res = await $http({
          url: 'zx/career/student/getReport?id=' + this.id
        });
        this.data = res.data;
        uni.hideLoading();
      }
    }
  };
</script>

<style lang="scss" scoped>
  .video_css_content {
    width: 600rpx;
    height: 450rpx;
  }
  .big {
    position: relative;
    background: linear-gradient(#339378, #fff);
    overflow: hidden;

    .ghbg {
      position: absolute;
      height: 84rpx;
      width: 334rpx;
      top: 70rpx;
      right: 62rpx;
      z-index: 1;
    }

    .headline {
      width: 466rpx;
      height: 108rpx;
      line-height: 108rpx;
      padding-left: 24rpx;
      background: linear-gradient(to right, #c9f1e4, #fff);
      border-radius: 16rpx 0rpx 0rpx 0rpx;
      font-size: 32rpx;
      font-weight: bold;
      color: #333333;
      // opacity: 0.5;
    }

    .paragraph {
      font-size: 28rpx;
      line-height: 42rpx;
      text-indent: 4ch;
      color: #555555;
      margin: 30rpx 24rpx;
    }

    .course {
      display: flex;
      margin: 0 24rpx 32rpx;
      flex-wrap: wrap;
      justify-content: space-between;

      .play {
        position: absolute;
        width: 152rpx;
        height: 92rpx;
        top: 66rpx;
        left: 50%;
        transform: translateX(-50%);
      }
    }

    .project {
      width: 686rpx;
      // height: 1034rpx;
      border-radius: 16rpx;
      background-color: #fff;
      // background: linear-gradient(#f5fffc, #fff);
      overflow: hidden;
      margin: 0 auto 46rpx;

      .headlinet {
        height: 40rpx;
        line-height: 40rpx;
        color: #359479;
        font-size: 28rpx;
        font-weight: bold;
        margin: 32rpx 0 24rpx 24rpx;
      }
    }

    .suggestion {
      width: 686rpx;
      // height: 1034rpx;
      border-radius: 16rpx;
      background-color: #fff;
      overflow: hidden;
      margin: 0 auto 24rpx;

      .bonus {
        width: 640rpx;
        height: 480rpx;
        // background: #D8D8D8;
        overflow: hidden;
        border-radius: 28rpx;
        margin: 0 auto 44rpx;
      }
    }

    .uesr {
      box-sizing: border-box;
      position: relative;
      z-index: 2;
      width: 686rpx;
      height: 808rpx;
      border-radius: 16rpx;
      background: linear-gradient(#f5fffc, #fff);
      margin: 138rpx auto 24rpx;
      padding: 20rpx 24rpx;

      .avater {
        position: absolute;
        width: 180rpx;
        height: 180rpx;
        top: -32rpx;
        border-radius: 180rpx;
        overflow: hidden;
      }

      .name {
        font-weight: bold;
        padding-left: 200rpx;
        height: 44rpx;
        line-height: 44rpx;
        color: #333333;
        font-size: 32rpx;
        font-family: AlibabaPuHuiTi_2_85_Bold;
        margin-bottom: 14rpx;
      }

      .type {
        width: 204rpx;
        height: 58rpx;
        margin-left: 200rpx;
        line-height: 58rpx;
        color: #fff;
        background-color: #33cb9e;
        border-radius: 30rpx;
        font-size: 32rpx;
        font-weight: bold;
        text-align: center;
        margin-bottom: 36rpx;
      }

      .text {
        font-size: 28rpx;
        text-indent: 4ch;
        color: #555555;
        line-height: 42rpx;
      }

      .title {
        align-items: center;
        margin: 40rpx 0;
        height: 44rpx;
        display: flex;

        .line {
          width: 10rpx;
          height: 32rpx;
          background-color: #339378;
          margin-right: 6rpx;
        }

        .name1 {
          font-weight: bold;
          height: 44rpx;
          line-height: 44rpx;
          color: #333333;
          font-size: 32rpx;
          font-family: AlibabaPuHuiTi_2_85_Bold;
        }
      }

      .circle {
        position: relative;
        width: 532rpx;
        height: 288rpx;
        margin: 0 auto;

        .fenbutu {
          width: 100%;
          height: 100%;
        }

        .bie {
          position: absolute;
          height: 44rpx;
          width: 96rpx;
          line-height: 44rpx;
          font-weight: bold;
          font-family: AlibabaPuHuiTi_2_85_Bold;
        }

        .one {
          color: #339378;
          left: 222rpx;
          top: 86rpx;
        }

        .two {
          color: #e07a21;
          left: 92rpx;
          top: 160rpx;
        }

        .three {
          color: #1c2f56;
          left: 350rpx;
          top: 150rpx;
        }
      }
    }
  }
</style>
