<template>
  <view class="">
    <image
      src="https://document.dxznjy.com/dxSelect/b476d1dd-16b3-4c4c-a474-b446d9bec9f5.png"
      mode="widthFix"
      v-if="showAddStage"
      @click="updateFirst"
      style="width: 100vw; height: 100vh"
    ></image>
    <image
      src="https://document.dxznjy.com/dxSelect/b85598ae-df35-4b62-a8b6-1e0bd57f5cc9.png"
      mode="widthFix"
      v-else-if="showClassHourImg"
      @click="closeClassHourImg"
      style="width: 100vw; height: 100vh"
    ></image>
    <view class="bg-ff" v-else>
      <view class="" v-for="(stage, stageIndex) in stages" :key="stageIndex">
        <view class="stagePlanHead plr-32">
          <view class="stageTitleLeft">
            阶段
            <text>{{ stageChineseList[stageIndex] }}</text>
            目标
          </view>
          <view class="flex-self-c">
            <view class="stageTitleRight" @click="targetSuggest" v-if="stageIndex === 0">阶段规划建议</view>
            <view class="ml-20" @click="deleteStage(stageIndex)">
              <image src="https://document.dxznjy.com/dxSelect/e8d50f7d-ed1a-4689-b340-3cd1288e1ed4.png" mode="widthFix" style="width: 30rpx"></image>
            </view>
          </view>
        </view>

        <view class="plr-20 pb-40 radius-15 positionRelative">
          <form id="#nform">
            <view class="information ptb-35">
              <view style="width: 32%" class="f-30 bold">
                <text style="color: red">*</text>
                阶段目标：
              </view>
              <view class="phone-input">
                <input
                  @input="(e) => inputNumber(e, stageIndex)"
                  placeholder-style="font-size:28rpx;color:#c6c6c6;"
                  v-model="stages[stageIndex].studentCourseStageDtoList.stageTarget"
                  name="number"
                  class="input c-00 form-input"
                  placeholder="请输入阶段目标规划"
                />
              </view>
            </view>
            <view class="timeline" v-if="stages[stageIndex].isComfirmDetail">
              <view class="timelineBg"></view>
              <image
                src="https://document.dxznjy.com/dxSelect/095b4f5f-4de7-443b-bd10-1f63d7997f7e.png"
                mode="widthFix"
                class="updateButton"
                @click="updateData(stageIndex)"
              ></image>
              <view class="" v-if="stages[stageIndex].type1List.length">
                <block>
                  <view class="pb-30 pt-24 pr-25 mb-15 timelineBag1">
                    <!-- 章节标题行 -->
                    <view class="timeline-item">
                      <view class="dot-wrapper">
                        <view class="headDot"></view>
                        <view class="dot-line dot-line-down"></view>
                      </view>
                      <view class="headerTitle">单词速记</view>
                    </view>
                    <!-- 该章节的条目 -->
                    <view class="timeline-group">
                      <view class="timeline-item" v-for="(item, index) in stages[stageIndex].type1List" :key="index">
                        <view class="dot-wrapper">
                          <view class="dot-line dot-line-up"></view>
                          <view class="dot"></view>
                          <view class="dot-line dot-line-down"></view>
                        </view>
                        <view class="content-box">
                          {{ item.courseName }}
                        </view>
                      </view>
                    </view>
                  </view>
                </block>
              </view>
              <block v-for="(group, gIndex) in stages[stageIndex].otherTypeList" :key="gIndex">
                <view :class="!stages[stageIndex].type1List.length && gIndex === 0 ? 'timelineBag1' : 'timelineBag'" class="pb-30 pt-24 pr-25 mb-15">
                  <!-- 章节标题行 -->
                  <view class="timeline-item">
                    <view class="dot-wrapper">
                      <view class="headDot"></view>
                      <view class="dot-line dot-line-down"></view>
                    </view>
                    <view class="headerTitle">
                      {{ getCourseTypeLabel(group.courseType) }}
                    </view>
                  </view>

                  <!-- 该章节的条目 -->
                  <view class="timeline-group">
                    <view class="timeline-item" v-for="(item, index) in group.data" :key="index">
                      <view class="dot-wrapper">
                        <view class="dot-line dot-line-up"></view>
                        <view class="dot"></view>
                        <view class="dot-line dot-line-down"></view>
                      </view>
                      <view class="content-box">
                        {{ item.courseName }}
                      </view>
                    </view>
                  </view>
                </view>
              </block>
            </view>
            <view class="information ptb-35" v-if="!stages[stageIndex].isComfirmDetail">
              <view style="width: 32%" class="f-30 bold">
                <text style="color: red">*</text>
                规划课程：
              </view>
              <view class="phone-button borderGreen">
                <button class="ckickDetail" @click="ckickDetailPopup(stageIndex)">点击规划阶段课程详情</button>
              </view>
            </view>
            <view class="information ptb-35">
              <view style="width: 32%" class="f-30 bold">
                <text style="color: red">*</text>
                建议课时：
              </view>
              <view class="calculation calculation1">
                <view class="phone-input">
                  <input
                    @input="(e) => inputHour(e, stageIndex)"
                    v-model="stages[stageIndex].stageHour"
                    placeholder-style="font-size:28rpx;color:#c6c6c6;"
                    name="trialname"
                    placeholder="请输入建议课时"
                    class="input c-00 form-input"
                  />
                </view>
                <text class="watchCalculation ml-25" @click="intelligentHour(stageIndex)">智能课时推荐</text>
              </view>
            </view>
            <view class="information ptb-35">
              <view style="width: 32%" class="f-30 bold">
                <text style="color: red">*</text>
                时段任务：
              </view>
              <view class="phone-input">
                <input
                  @input="(e) => timeTask(e, stageIndex)"
                  v-model="stages[stageIndex].studentCourseStageDtoList.timeTask"
                  placeholder-style="font-size:28rpx;color:#c6c6c6;"
                  name="trialname"
                  placeholder="例如:春季学段、秋季学段......"
                  class="input c-00 form-input"
                />
              </view>
            </view>
          </form>
        </view>
      </view>
      <view class="addInfoBox plr-32">
        <view class="addInfo" @click="addStage" :class="isAdd ? '' : 'disable'">+新增阶段规划</view>
      </view>
      <view class="plr-20 pt-24 radius-15 infoWhite">
        <view class="information ptb-35">
          <view style="width: 32%" class="f-30 bold">
            <text style="color: red">*</text>
            上课规划：
          </view>
          <view class="phone-input">
            <input
              @input="(e) => attendClass(e)"
              v-model="reportData.classPlan"
              placeholder-style="font-size:28rpx;color:#c6c6c6;"
              name="trialname"
              placeholder="例如:每周2~3小时,暑假......"
              class="input c-00 form-input"
              @focus="showSuggestPopup"
            />
          </view>
        </view>
        <view class="information ptb-35">
          <view style="width: 32%" class="f-30 bold">
            <text style="color: red">*</text>
            复习规划：
          </view>
          <view class="phone-input">
            <input
              @input="(e) => reviewPlanClass(e)"
              v-model="reportData.reviewPlan"
              placeholder-style="font-size:28rpx;color:#c6c6c6;"
              name="trialname"
              placeholder="例如:每天1对1抗遗忘复习"
              class="input c-00 form-input"
              @focus="showSuggestPopup"
            />
          </view>
        </view>
      </view>
      <view class="calculation mlr-30 flex-x-e mb-40">
        <text class="watchCalculation" @click="openlessonPopup">上课及复习规划建议</text>
      </view>

      <view class="bottom-wrapper" :style="{ marginTop: stages.length > 1 ? '0' : '220rpx' }">
        <view class="reportAdd" @click="reporting">生成报告</view>
      </view>

      <!-- 阶段规划建议弹窗-->
      <uni-popup ref="targetPopup" type="bottom" :is-mask-click="false">
        <view class="ptb-20 infoPopup infoBack">
          <view class="infoTitle mtb-20">学员课时阶段规划建议</view>
          <view class="popupIcon" @click="closeInfo">
            <uni-icons type="clear" size="28" color="#B1B1B1"></uni-icons>
          </view>
          <view class="plr-32 suggestText">
            <view class="mb-25" style="color: #a2a6a4">总目标设定后，规划课程建议分多个阶段，依据总目标根据以下人群分层展开:</view>
            <view class="mb-25">
              <text style="color: #339378">1.K12普通学生：</text>
              按照学生能力分别制定不同阶段目标，建议分为：打地基、同步学、超前学。
            </view>
            <view class="mb-25">
              <text style="color: #339378">2.K12两三人群（初三/高三）：</text>
              以考纲为主，时间充裕者可以先打地基。
            </view>
            <view class="mb-25">
              <text style="color: #339378">3.K12考证需求人群：</text>
              KET/PET/FCE，可以先规划词汇模块。
            </view>
            <view class="mb-25">
              <text style="color: #339378">4.出国需求人群：</text>
              托福/雅思，可以先规划词汇模块。
            </view>
          </view>
        </view>
      </uni-popup>
      <!-- 规划说明建议弹窗-->
      <uni-popup ref="detailPopup" type="bottom" :is-mask-click="false">
        <view class="infoPopup suggestHeight">
          <!-- 标题区 -->
          <view class="popupHeader">
            <view class="infoTitle">规划阶段课程详情</view>
            <view class="popupIcon" @click="closeDetailPopup">
              <uni-icons type="clear" size="28" color="#B1B1B1"></uni-icons>
            </view>
          </view>
          <!-- 正文区 -->
          <view class="popupBody">
            <!-- 横向 tabs -->
            <view class="courseNameWrapper">
              <!-- 固定空白区 -->
              <view class="tab-static" />
              <!-- 可滚动区 -->
              <scroll-view scroll-x class="courseName">
                <view class="tab-wrapper">
                  <text v-for="(item, index) in tabs" :key="index" :class="['tab-item', activeTabIndex === index ? 'active' : '']" @click="selectTab(index)">
                    {{ item.label }}
                  </text>
                </view>
              </scroll-view>
            </view>
            <!-- 下方两栏 -->
            <view class="courseGradeBox">
              <!-- 左侧固定宽度，竖向滚动 -->
              <scroll-view scroll-y class="courseGrade">
                <view class="grade-wrapper pb-50">
                  <text v-for="(item, index) in courseGrade" :key="index" :class="['grade-item', activeGradeIndex === index ? 'active' : '']" @click="selectGrade(index)">
                    {{ item.label }}
                  </text>
                </view>
              </scroll-view>
              <!-- 右侧内容区 -->
              <view class="courseGradeChoose">
                <view class="courseDetailWrapper">
                  <view
                    v-for="(detail, index) in stages[this.stageIndex].courseDetail"
                    :key="index"
                    class="courseDetail pt-24 plr-15 pb-70"
                    :class="{ selected: isCourseSelected(index) }"
                    @click="toggleCourse(index)"
                  >
                    {{ detail.courseName }} ({{ detail.courseHour }}) 小时
                  </view>
                </view>
              </view>
            </view>
          </view>
          <view class="popupEnd">
            <button class="closePopup" @click="closeDetailPopup">取消</button>
            <button class="comfirmChoose" @click="comfirmDetailPopup">确认</button>
          </view>
        </view>
      </uni-popup>
      <!-- 生成课时规划进度弹窗-->
      <uni-popup ref="progressPopup" type="bottom" :is-mask-click="false">
        <view class="ptb-20 infoPopup generateReport">
          <view class="popupIcon" @click="closeInfo">
            <uni-icons type="clear" size="28" color="#00DFAA"></uni-icons>
          </view>
          <view class="plr-32 suggestText t-c">
            <image src="https://document.dxznjy.com/dxSelect/07737707-4921-41b2-8c45-b64488092819.png" mode="widthFix" style="width: 147rpx"></image>
            <view class="mb-25" style="color: #fff">已加载35%</view>
            <view class="mb-25" style="color: #fff">正在为您智能生成课时规划，请稍后......</view>
          </view>
        </view>
      </uni-popup>
      <!-- 课时规划弹窗-->
      <uni-popup ref="exclusivePopup" type="bottom" :is-mask-click="false">
        <view class="ptb-20 infoPopup exclusiveBg">
          <view class="exclusiveTitle mt-20 mb-40">{{ studentName }}学员的专属课时规划(阶段{{ stageChineseList[stageIndex] }})</view>
          <view class="popupIcon" @click="closeExclusivePopup(stageIndex)" style="z-index: 100">
            <uni-icons type="clear" size="28" color="#FFF"></uni-icons>
          </view>
          d
          <view class="plr-32 exclusiveContent pt-40 plr-20">
            <!-- 只渲染一次 courseType == 1 固定结构，但 courseName 可多条 -->
            <view v-if="stages[stageIndex].type1List.length">
              <view class="mb-35 flex-y-s flex-y-c">
                <image src="https://document.dxznjy.com/dxSelect/6d40efa9-68b2-4538-bbfa-b8e60b122f6e.png" mode="widthFix" style="width: 56rpx" />
                <view class="bold ml-15">单词速记</view>
              </view>

              <!-- 多个课程名称 -->
              <view v-for="(item, index) in stages[stageIndex].type1List" :key="index" style="color: #59bf95" class="mb-35">{{ item.courseName }}({{ item.wordTotal }}词)</view>

              <view style="color: #bdbdbd" class="mb-35">重复词数:{{ list.repeatedWords }}词</view>
              <view class="exclusiveBox ptb-25 mb-35 pl-25">
                <view class="tetxtColor mb-15">体验课学习效率:{{ list.memoryTime }}分钟{{ list.memoryNum }}个单词</view>
                <view class="tetxtColor mb-15">单词建议课时({{ list.wordHour }}小时)</view>
                <view class="tetxtColor">({{ list.wordHour }}小时)=({{ formulaString }} )/({{ list.memoryNum }}词X60分钟/{{ list.memoryTime }}分钟)</view>
              </view>
            </view>
            <view v-for="(item, index) in stages[stageIndex].otherTypeList" :key="index">
              <view class="mb-35 flex-y-s flex-y-c">
                <image
                  :src="
                    item.courseType == 2
                      ? 'https://document.dxznjy.com/dxSelect/d6c10501-6f9c-456d-873c-9752677eb6b1.png'
                      : item.courseType == 3
                      ? 'https://document.dxznjy.com/dxSelect/dadd1580-df72-48a9-9d23-7b0571810829.png'
                      : item.courseType == 4
                      ? 'https://document.dxznjy.com/dxSelect/7027bfdb-e96c-4c61-9d42-f4b9af1a256d.png'
                      : ''
                  "
                  mode="widthFix"
                  style="width: 56rpx"
                />
                <view class="bold ml-15">
                  {{ item.courseType == 2 ? '渗透语法' : item.courseType == 3 ? '超级阅读' : item.courseType == 4 ? '全能听力' : '' }}
                </view>
              </view>

              <view v-for="(dataItem, dataIndex) in item.data" :key="dataIndex">
                <view class="mb-35">
                  学习内容：
                  <text style="color: #59bf95">{{ dataItem.courseName }}</text>
                </view>
                <view class="exclusiveBox mb-35 ptb-25 pl-25 tetxtColor">建议课时:({{ dataItem.courseHour }}个小时)</view>
              </view>
            </view>
            <view class="mb-35 flex-y-s flex-y-c">
              <image src="https://document.dxznjy.com/dxSelect/ab933436-299f-451b-a174-d08beadc6bb5.png" mode="widthFix" style="width: 56rpx"></image>
              <view class="bold ml-15">总课时汇总</view>
            </view>
            <view class="exclusiveBox ptb-25 mb-35 pl-25">
              <view class="tetxtColor mb-15">阶段{{ stageChineseList[stageIndex] }}建议课时:</view>
              <view class="tetxtColor">{{ list.stageHour }}小时 = {{ stageHourFormula }}</view>
            </view>
            <view class="popupEnd">
              <button class="closePopup" @click="closeExclusivePopup(stageIndex)">取消</button>
              <button class="comfirmChoose" @click="closeExclusivePopup(stageIndex)">确认</button>
            </view>
          </view>
        </view>
      </uni-popup>
      <!-- 上课复习规划建议弹窗-->
      <uni-popup ref="lessonSuggestPopup" type="bottom" :is-mask-click="false">
        <view class="ptb-20 infoPopup infoBack">
          <view class="infoTitle mtb-20">上课及复习规划建议</view>
          <view class="popupIcon" @click="closeLessonSuggestPopup">
            <uni-icons type="clear" size="28" color="#B1B1B1"></uni-icons>
          </view>
          <view class="plr-32 suggestText">
            <view class="mb-25">
              <text style="color: #339378">合伙人您好！</text>
            </view>
            <view class="mb-25" style="text-indent: 2em">
              艾宾浩斯遗忘曲线显示，知识24小时内遗忘最快。每周3次及以上的高频学习，能让知识快速扎根，孩子英语听说能力提升幅度比低频学习高40%！
            </view>
            <view class="mb-25" style="text-indent: 2em">
              及时复习同样关键，及时的复习能让知识吸收率能提升60%。短期高频学习像闯关游戏，轻松高效；战线拉长则像重新“开荒”，易疲意且效果差。建议保证上课频率，助力孩子高效学习！
            </view>
          </view>
        </view>
      </uni-popup>
    </view>
  </view>
</template>

<script>
  const { $http } = require('@/util/methods.js');
  const SENSITIVE_WORDS = [
    '保证',
    '提分',
    '通关',
    '必过',
    '第一',
    '满分',
    '万能',
    '独家',
    '奇迹',
    '顶尖',
    '铁定',
    '肯定',
    '速成',
    '国家级',
    '专家',
    '名师',
    '学霸',
    '领先',
    '绝无仅有',
    '空前绝后',
    '提高成绩',
    '提升分数',
    '高分',
    '抢分',
    '应试',
    '押题',
    '幼升小',
    '小升初',
    '中考',
    '高考',
    '升学率',
    '录取率',
    '真题',
    '状元',
    '超前学习',
    '领跑新学期',
    '超越学校课程',
    '保证提分',
    '一次性通过',
    '短期突破高分',
    '包过',
    '保过',
    '班级排名',
    '年级排名',
    '学校排名',
    '尖子生',
    '名校毕业教师',
    '命题人',
    '阅卷人',
    '原价',
    '现价',
    '限时优惠',
    '分期付款',
    '贷款',
    '独家教材',
    '国家级奖项',
    '100%通过率',
    '再不努力就晚了',
    '落后于同龄人',
    '新政影响',
    '学员成功案例',
    '状元学员分享',
    '最佳',
    '顶级',
    '唯一'
  ];
  export default {
    data() {
      return {
        isFalg: false,
        tabs: [],
        courseGrade: [],
        courseDetail: [],
        selectedCourses: [],
        type1List: [],
        otherTypeList: [],
        activeTabIndex: 0,
        activeGradeIndex: 0,
        studentCode: '',
        memoryTime: '',
        memoryNum: '',
        studentName: '',
        courseEdition: '',
        stageIndex: 0,
        stage: 0,
        list: [],
        stageHour: 0,
        reportData: { studentCourseStageDtoList: [{ stageTarget: '', stage: 1 }] }, // 报告数据
        showSuggestStatus: false,
        showTargetPopup: false,
        isAdd: true,
        stages: [
          // 初始一个阶段
          {
            studentCourseStageDtoList: {
              stageTarget: '',
              stageHour: '',
              timeTask: ''
            },
            list: [], // 时间线列表
            isComfirmDetail: false,
            courseDetailData: {},
            type1List: [],
            otherTypeList: [],
            courseDetail: [],
            stageHour: '',
            selectedCourses: []
          }
        ],
        courseTypeMap: {
          单词速记: 1,
          渗透语法: 2,
          超级阅读: 3,
          全能听力: 4
        },
        showAddStage: false,
        isShowAddStage: false,
        showClassHourImg: false,
        isShowClassHourImg: false,
        sensitiveReg: new RegExp(`(${SENSITIVE_WORDS.join('|')})`, 'gi'), // 敏感词正则
        planCode: ''
      };
    },
    computed: {
      stageChineseList() {
        return ['一', '二', '三', '四', '五'];
      },
      wordTotals() {
        return (this.stages[this.stageIndex].type1List || []).map((item) => item.wordTotal || 0);
      },
      // 总词数
      totalWords() {
        return this.wordTotals.reduce((sum, val) => sum + val, 0);
      },
      // 公式字符串
      formulaString() {
        const repeated = this.list.repeatedWords || 0;
        const addPart = this.wordTotals.join(' + ');
        return `${addPart} - ${repeated}`;
      },
      stageHourFormula() {
        const parts = [];

        // 只有 type1List 有内容才显示 “单词”
        if ((this.stages[this.stageIndex].type1List || []).length > 0) {
          parts.push(`${this.list.wordHour || 0}小时（单词）`);
        }
        const others = (this.stages[this.stageIndex].otherTypeList || []).map((group) => {
          const totalHours = group.data.reduce((sum, item) => sum + (parseFloat(item.courseHour) || 0), 0);
          const label = group.courseType === 2 ? '语法' : group.courseType === 3 ? '阅读' : group.courseType === 4 ? '听力' : '';
          return `${totalHours}小时（${label}）`;
        });
        return [...parts, ...others].join(' + ');
      }
    },
    onLoad(e) {
      this.studentCode = e.studentCode;
      this.memoryNum = e.memoryNum;
      this.memoryTime = e.memoryTime;
      this.courseEdition = e.courseEdition;
      this.studentName = e.studentName;
      this.planCode = e.planCode;
    },
    onReady() {
      let that = this;
      uni.getSystemInfo({
        //调用uni-app接口获取屏幕高度
        success(res) {
          // 可使用窗口高度，将px转换rpx
          let h = res.windowHeight * (750 / res.windowWidth);
          that.useHeight = h - 65;
        }
      });
    },
    async onShow() {
      const cacheEdition = uni.getStorageSync('courseEdition');
      const cacheStages = uni.getStorageSync('lessonStagePlann');
      const cacheList = uni.getStorageSync('lessonStageList');
      const stageHour = uni.getStorageSync('stageHour');
      const classPlanReport = uni.getStorageSync('classPlanReport');
      if (cacheEdition && cacheEdition === this.courseEdition && cacheStages) {
        // 使用缓存
        this.stages = cacheStages;
        this.list = cacheList;
        this.stageHour = stageHour;
        this.reportData = classPlanReport;
      }
      await this.fetchCourseStage();
      await this.fetchCourseType();
      this.fetchTargetPopup();
    },
    onHide() {
      this.storageInfo();
    },
    onUnload() {
      this.storageInfo();
    },
    watch: {
      stages: {
        deep: true,
        handler(val) {
          const stage0 = val[0];
          if (!stage0) return;

          const { studentCourseStageDtoList, isComfirmDetail, stageHour } = stage0;

          if (studentCourseStageDtoList?.stageTarget && isComfirmDetail && studentCourseStageDtoList?.timeTask && stageHour) {
            this.getVa();
          }
        }
      }
    },
    methods: {
      updateFirst() {
        this.showAddStage = false;
      },
      closeClassHourImg() {
        this.showClassHourImg = false;
      },
      // 获取课程阶段
      async fetchCourseStage() {
        try {
          let result = await this.$httpUser.get('znyy/bvstatus/CourseStage');
          if (result && result.data && result.data.data) {
            this.courseGrade = result.data.data;
          }
        } catch (err) {
          console.error('fetchCourseStage 错误：', err);
        }
      },
      async fetchCourseType() {
        try {
          let result = await this.$httpUser.get('znyy/bvstatus/PlanCourseType');
          if (result && result.data && result.data.data) {
            this.tabs = result.data.data;
          }
        } catch (err) {
          console.error('fetchCourseType 错误：', err);
        }
      },
      // 获取默认课程详情inputHour
      async fetchCourseDetail(courseType, stage) {
        let result = await this.$httpUser.get('znyy/wap/student-lesson-plan/stage-info/select-course', {
          stage: stage,
          courseType: courseType,
          courseEdition: this.courseEdition
          // courseEdition: '人教版'
        });
        if (result.data.code == 20000) {
          this.stages[this.stageIndex].courseDetail = result.data.data;
        }
      },
      getCourseTypeLabel(type) {
        const map = {
          2: '渗透语法',
          3: '超级阅读',
          4: '全能听力'
        };
        return map[type] || '';
      },
      getCourseTypeByTabIndex(index) {
        // 假设 tab 的顺序是固定的：单词速记、渗透语法、超级阅读、全能听力
        const mapping = {
          0: 1, // 单词速记
          1: 2, // 渗透语法
          2: 3, // 超级阅读
          3: 4 // 全能听力
        };
        return mapping[index] || 0;
      },
      isCourseSelected(index) {
        const course = this.stages[this.stageIndex].courseDetail[index];
        const courseType = this.getCourseTypeByTabIndex(this.activeTabIndex);
        return this.selectedCourses.some((item) => item.courseType === courseType && item.id === course.id && item.courseCode === course.coursePlanCode);
      },
      getCurrentKey() {
        const tabKey = this.tabs[this.activeTabIndex]?.idx;
        const gradeKey = this.courseGrade[this.activeGradeIndex]?.value;
        return `${tabKey}_${gradeKey}`;
      },
      getVa() {
        const coursePlanLength = uni.getStorageSync('coursePlanLength');
        if (coursePlanLength < 3 && !this.isShowAddStage) {
          this.showAddStage = true;
          this.isShowAddStage = true;
          return;
        }
      },
      toggleCourse(index) {
        const course = this.stages[this.stageIndex].courseDetail[index];
        const label = this.tabs[this.activeTabIndex].label;
        const courseType = this.courseTypeMap[label] || 0; // 默认 0 以防 label 不匹配
        const courseName = course.courseName;

        const exists = this.selectedCourses.some((item) => item.courseType === courseType && item.id === course.id && item.courseCode === course.coursePlanCode);

        if (!exists) {
          this.selectedCourses.push({
            courseType,
            courseName,
            courseCode: course.coursePlanCode,
            linkCourseCode: course.courseCode,
            courseHour: course.courseHour,
            id: course.id,
            courseEdition: course.courseEdition
          });
        } else {
          // 如果重复点击取消选择
          this.selectedCourses = this.selectedCourses.filter((item) => !(item.courseType === courseType && item.id === course.id && item.courseCode === course.coursePlanCode));
        }
      },
      addStage() {
        if (this.stages.length >= 5) {
          this.isAdd = false;
          uni.showToast({
            title: '最多添加5个阶段',
            icon: 'none'
          });
          return;
        }
        const valid = this.checkRoportInfo(true);
        if (!valid) return;

        // 通过校验，新增阶段
        this.stages.push({
          studentCourseStageDtoList: {
            stageTarget: '',
            stageHour: '',
            timeTask: ''
          },
          list: [], // 时间线列表
          isComfirmDetail: false,
          courseDetailData: {},
          type1List: [],
          otherTypeList: [],
          courseDetail: [],
          stageHour: '',
          selectedCourses: []
        });
        this.reportData.studentCourseStageDtoList.push({
          stage: this.reportData.studentCourseStageDtoList.length + 1,
          stageTarget: ''
        });
      },
      inputNumber(e, index) {
        this.reportData.studentCourseStageDtoList[index].stageTarget = e.detail.value;
      },
      inputHour(e, index) {
        let val = e.detail.value;
        val = val.replace(/小时/g, '');
        this.reportData.studentCourseStageDtoList[index].stageHour = val;
      },
      timeTask(e, index) {
        this.reportData.studentCourseStageDtoList[index].timeTask = e.detail.value;
      },
      attendClass(e) {
        this.reportData.classPlan = e.detail.value;
      },
      reviewPlanClass(e) {
        this.reportData.reviewPlan = e.detail.value;
      },
      targetSuggest() {
        this.$refs.targetPopup.open();
      },
      closeInfo() {
        this.$refs.targetPopup.close();
      },
      async ckickDetailPopup(stageIndex) {
        this.stage = stageIndex + 1;
        this.stageIndex = stageIndex;
        await this.fetchCourseDetail(this.tabs[this.activeTabIndex].idx, this.courseGrade[this.activeGradeIndex].value);
        this.$refs.detailPopup.open();
      },
      closeDetailPopup() {
        this.$refs.detailPopup.close();
        this.activeTabIndex = 0;
        this.activeGradeIndex = 0;
        this.selectedCourses = [];
      },
      async comfirmDetailPopup() {
        if (this.selectedCourses.length == 0) {
          uni.showToast({
            title: '当前未选中任何课程，请至少选择一个课程进行规划',
            icon: 'none'
          });
          return;
        }
        this.$refs.detailPopup.close();
        this.$refs.progressPopup.open();
        const res = await $http({
          url: 'znyy/wap/student-lesson-plan/stage-info/hour',
          method: 'POST',
          data: {
            studentCode: this.studentCode,
            memoryTime: this.memoryTime,
            memoryNum: this.memoryNum,
            stage: this.stage,
            planCode: this.planCode,
            studentCourseStageHourPlanCos: this.selectedCourses
          }
        });
        if (res.code == 20000) {
          this.$refs.progressPopup.close();
          this.$refs.exclusivePopup.open();
          this.stages[this.stageIndex].courseDetailData = res.data;
          this.stages[this.stageIndex].isComfirmDetail = true;
          this.activeTabIndex = 0;
          this.activeGradeIndex = 0;
          this.stages[this.stageIndex].selectedCourses = [...this.selectedCourses];
          this.selectedCourses = [];
          this.fetchCourseDetail(this.tabs[this.activeTabIndex].idx, this.courseGrade[this.activeGradeIndex].value);
          // 找到当前阶段对应的新数据
          var newStageItem = this.stages[this.stageIndex].courseDetailData.studentCourseStageDtoList.find((item) => item.stage === this.stage);

          if (newStageItem) {
            // 先检查 reportData 中是否已有相同阶段
            const index = this.reportData.studentCourseStageDtoList.findIndex((item) => {
              return item.stage == this.stage;
            });
            if (index !== -1) {
              const originalItem = this.reportData.studentCourseStageDtoList[index];
              newStageItem = {
                ...newStageItem, // 保留新数据的其他属性
                timeTask: originalItem.timeTask, // 保留原数据的 timeTask
                stageTarget: originalItem.stageTarget // 保留原数据的 stageTarget
              };
              this.reportData.studentCourseStageDtoList.splice(index, 1, newStageItem);
            } else {
              this.reportData.studentCourseStageDtoList.push(newStageItem);
            }
          }
          const stageItem = this.stages[this.stageIndex].courseDetailData.studentCourseStageDtoList.find((item) => item.stage === this.stage);

          if (stageItem) {
            // 保留外层 memoryNum、memoryTime 等字段
            const externalData = {
              memoryNum: this.stages[this.stageIndex].courseDetailData.memoryNum,
              memoryTime: this.stages[this.stageIndex].courseDetailData.memoryTime,
              planCode: this.planCode,
              studentCode: this.stages[this.stageIndex].courseDetailData.studentCode
            };

            // 合并外层数据和 stageItem（深拷贝避免污染原数据）
            const newStageItem = {
              ...externalData,
              ...JSON.parse(JSON.stringify(stageItem))
            };

            // 拆分处理列表
            const grouped = {};
            (newStageItem.studentCourseStageHourPlanList || []).forEach((item) => {
              const type = item.courseType;
              if (!grouped[type]) {
                grouped[type] = [];
              }
              grouped[type].push(item);
            });

            // 单词速记 type == 1 的
            this.stages[this.stageIndex].type1List = grouped[1] || [];

            this.stages[this.stageIndex].otherTypeList = Object.keys(grouped)
              .filter((type) => type !== '1')
              .map((type) => ({
                courseType: Number(type),
                data: grouped[type] // 将相同类型的所有数据放入 data 数组
              }));
            this.stageHour = newStageItem.stageHour || 0; // 确保非空
            // 保存最终合并后的数据
            this.list = newStageItem;
          }
        } else {
          this.$refs.progressPopup.close();
          uni.showToast({
            title: res.message,
            icon: 'none'
          });
        }
      },
      // 删除阶段
      deleteStage(index) {
        // 检查是否只剩一个阶段
        if (this.stages.length <= 1 || this.reportData.studentCourseStageDtoList.length <= 1) {
          uni.showToast({ title: '至少保留一个阶段', icon: 'none' });
          return;
        }
        uni.showModal({
          title: '温馨提示',
          content: '是否确认删除阶段',
          success: (res) => {
            if (res.confirm) {
              this.comfirmDeleteStage(index);
            } else if (res.cancel) {
              console.log('用户点击取消');
            }
          }
        });
      },
      comfirmDeleteStage(index) {
        this.stages.splice(index, 1);
        this.reportData.studentCourseStageDtoList.splice(index, 1);
        // 重新编号剩下的阶段
        this.reportData.studentCourseStageDtoList.forEach((item, newIndex) => {
          item.stage = newIndex + 1;
        });

        // 创建新的 reportData 引用，强制触发响应式更新
        this.reportData = {
          ...this.reportData,
          studentCourseStageDtoList: [...this.reportData.studentCourseStageDtoList]
        };
        // 重置当前选中的阶段索引
        if (this.stageIndex >= index) {
          this.stageIndex = Math.max(0, this.stages.length - 1);
        }
        this.stageIndex = 0;

        // 更新 stages 中的引用（使用 Vue.set 确保响应式）
        this.stages.forEach((stage, stageIndex) => {
          if (this.reportData.studentCourseStageDtoList[stageIndex]) {
            this.$set(stage, 'studentCourseStageDtoList', this.reportData.studentCourseStageDtoList[stageIndex]);
          }
        });
      },
      checkRoportInfo(isAdd) {
        for (let i = 0; i < this.stages.length; i++) {
          const stage = this.stages[i].studentCourseStageDtoList;
          if (!stage.stageTarget || !this.stages[i].stageHour || !stage.timeTask || !this.stages[i].isComfirmDetail) {
            if (isAdd) {
              uni.showToast({
                title: '请将上一个规划填写完整后，再来规划吧~',
                icon: 'none'
              });
              return false;
            } else {
              uni.showToast({
                title: `请先完整填写第${i + 1}阶段的信息`,
                icon: 'none'
              });
              return false;
            }
          }
          if (stage.stageTarget.match(this.sensitiveReg)) {
            if (isAdd) {
              uni.showToast({
                title: '当前阶段目标包含敏感词，请修改',
                icon: 'none'
              });
              return false;
            } else {
              uni.showToast({
                title: `第${i + 1}阶段阶段目标包含敏感词，请修改`,
                icon: 'none'
              });
              return false;
            }
          }
          if (stage.timeTask.match(this.sensitiveReg)) {
            if (isAdd) {
              uni.showToast({
                title: '当前阶段时段任务包含敏感词，请修改',
                icon: 'none'
              });
              return false;
            } else {
              uni.showToast({
                title: `第${i + 1}阶段时段任务包含敏感词，请修改`,
                icon: 'none'
              });
              return false;
            }
          }
        }
        return true;
      },
      // 生成报告
      async reporting() {
        if (this.isFalg) return;
        const valid = this.checkRoportInfo(false);
        if (!valid) return;
        if (this.reportData.classPlan == '' || this.reportData.classPlan == 'undefined' || this.reportData.classPlan == null) {
          uni.showToast({
            title: '请填写上课规划',
            icon: 'none'
          });
          return;
        }
        if (this.reportData.reviewPlan == '' || this.reportData.reviewPlan == 'undefined' || this.reportData.reviewPlan == null) {
          uni.showToast({
            title: '请填写复习规划',
            icon: 'none'
          });
          return;
        }
        this.reportData.memoryTime = this.list.memoryTime;
        this.reportData.memoryNum = this.list.memoryNum;
        this.reportData.planCode = this.planCode;
        this.reportData.studentCode = this.studentCode;
        this.isFalg = true;
        console.log(this.reportData, '提交报告');
        const res = await $http({
          url: 'znyy/wap/student-lesson-plan/generate-report',
          method: 'POST',
          data: this.reportData
        });
        if (res.code == 20000) {
          this.isFalg = false;
          uni.navigateTo({
            url: `/parentEnd/report/aiLessonPlanReport?id=${this.planCode}&isBack=false&studentCode=${this.studentCode}&isShow=true`
          });
        } else {
          this.isFalg = false;
        }
      },
      updateData(stageIndex) {
        this.stageIndex = stageIndex;
        this.selectedCourses = [...(this.stages[stageIndex].selectedCourses || [])];
        this.activeTabIndex = 0;
        this.activeGradeIndex = 0;
        this.$refs.detailPopup.open();
      },
      // 回显课时推荐
      intelligentHour(stageIndex) {
        this.stageIndex = stageIndex;
        if (this.stages[stageIndex].type1List.length == 0 && this.stages[stageIndex].otherTypeList.length == 0) {
          uni.showToast({
            title: '请规划好课程后再来查看哦~',
            icon: 'none'
          });
          return;
        }
        this.$refs.exclusivePopup.open();
      },
      calculateStageHourRange(stageIndex) {
        const min = Math.floor(this.stageHour * 0.9); // 下浮10%
        const max = Math.ceil(this.stageHour * 1.1); // 上浮10%
        this.stages[stageIndex].stageHour = `${min}~${max}小时`; // 格式化为字符串
      },
      closeExclusivePopup(stageIndex) {
        this.calculateStageHourRange(stageIndex);
        this.stages[stageIndex].studentCourseStageDtoList.studentCourseStageHourPlanList = this.list.studentCourseStageHourPlanList;
        this.$refs.exclusivePopup.close();
        this.onTextareaFocus();
      },
      onTextareaFocus() {
        const coursePlanLength = uni.getStorageSync('coursePlanLength');
        if (coursePlanLength < 3 && !this.isShowClassHourImg) {
          this.showClassHourImg = true;
          this.isShowClassHourImg = true;
        }
      },
      closeLessonSuggestPopup() {
        this.$refs.lessonSuggestPopup.close();
      },
      showSuggestPopup() {
        const coursePlanLength = uni.getStorageSync('coursePlanLength');
        if (coursePlanLength < 3 && !this.showSuggestStatus) {
          this.$refs.lessonSuggestPopup.open();
          this.showSuggestStatus = true;
        }
      },
      fetchTargetPopup() {
        const coursePlanLength = uni.getStorageSync('coursePlanLength');
        if (coursePlanLength < 3 && !this.showTargetPopup) {
          this.$refs.targetPopup.open();
          this.showTargetPopup = true;
        }
      },
      openlessonPopup() {
        this.$refs.lessonSuggestPopup.open();
      },
      selectTab(index) {
        this.activeTabIndex = index;
        this.fetchCourseDetail(this.tabs[this.activeTabIndex].idx, this.courseGrade[this.activeGradeIndex].value);
      },
      selectGrade(index) {
        this.activeGradeIndex = index;
        this.fetchCourseDetail(this.tabs[this.activeTabIndex].idx, this.courseGrade[this.activeGradeIndex].value);
      },
      // 缓存 信息
      storageInfo() {
        uni.setStorageSync('lessonStagePlann', this.stages);
        uni.setStorageSync('courseEdition', this.courseEdition);
        uni.setStorageSync('lessonStageList', this.list);
        uni.setStorageSync('stageHour', this.stageHour);
        uni.setStorageSync('classPlanReport', this.reportData);
      }
    }
  };
</script>

<style lang="scss" scoped>
  page {
    background-color: #fff;
  }
  .stagePlanHead {
    height: 100rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: linear-gradient(to right, #e7fae8 0%, #f7fafd 50%);
    .stageTitleLeft {
      font-size: 28rpx;
      color: #339378;
      font-weight: bold;
    }
    .stageTitleRight {
      font-size: 24rpx;
      color: #4ea18a;
    }
  }
  .positionRelative {
    background-color: #f7fafd;
  }
  .addInfoBox {
    height: 64rpx;
    display: flex;
    justify-content: flex-end;
    background: linear-gradient(to bottom, #f7fafd 0%, #fff 80%);
    .addInfo {
      width: 238rpx;
      border-radius: 8rpx;
      border: 2rpx solid #f2f8f2;
      font-size: 28rpx;
      line-height: 64rpx;
      text-align: center;
      background: #fcfffc;
      color: #339378;
    }
    .addInfo.disable {
      position: relative;
      pointer-events: none; /* 禁止点击 */
      opacity: 0.6; /* 让按钮淡一些 */
    }

    /* 白色遮罩层 */
    .addInfo.disable::after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-color: rgba(255, 255, 255, 0.6); /* 半透明白色 */
      z-index: 1;
      border-radius: 8rpx; /* 若按钮带圆角可加 */
    }
  }

  .information {
    display: flex;
    justify-content: space-between;
    align-items: center;

    /deep/.uni-icons {
      color: #fff !important;
    }
  }

  .infoPopup {
    background-color: #fff;
    position: relative;
    .popupIcon {
      position: absolute;
      top: 30rpx;
      right: 35rpx;
    }
    .suggestText {
      font-size: 28rpx;
      line-height: 46rpx;
    }
  }
  .infoBack {
    background-image: url('https://document.dxznjy.com/dxSelect/8e1c33bd-0d65-4466-85f0-9c7268d2fc42.png');
    background-size: 100% 100%;
  }
  .generateReport {
    background-image: url('https://document.dxznjy.com/dxSelect/632c7442-90d9-453b-b0d4-9e866fb5a936.png');
    background-size: 100% 100%;
    border-radius: 26rpx 26rpx 0 0;
  }
  .exclusiveBg {
    background-color: #f9f9f9;
    position: relative;
    overflow: hidden;
    border-radius: 16rpx 16rpx 0 0;
    z-index: 1;
    max-height: 80vh;
    overflow-y: auto;
  }

  .exclusiveBg::before {
    content: '';
    display: block;
    width: 100%;
    padding-top: 56.25%; /* 背景图高宽比 */
    background-image: url('https://document.dxznjy.com/dxSelect/c89def57-5fab-4477-bc9f-98fb2b8e1dbe.png');
    background-size: 100% auto;
    background-repeat: no-repeat;
    background-position: top center;
    position: absolute;
    top: 0;
    left: 0;
    z-index: 0;
  }
  .exclusiveTitle {
    color: #fff;
    position: relative; // 保证在背景图上层
    z-index: 1;
  }
  .exclusiveBox {
    background-color: #fafafa;
    border-radius: 16rpx;
  }
  .infoTitle,
  .exclusiveTitle {
    text-align: center;
    font-weight: bold;
    font-size: 32rpx;
  }
  .exclusiveContent {
    width: 85%;
    margin: 0 auto;
    background-color: #fff;
    border-radius: 24rpx;
    position: relative;
    z-index: 1;
    padding: 32rpx;
    box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.1);
  }

  .phone-input,
  .phone-button {
    background: #fff;
    border-radius: 8rpx;
    flex: 1;
    height: 64rpx;
    font-size: 28rpx;
    color: #c6c6c6;
    border: 1rpx solid #e4e5e7;
  }
  .phone-input {
    padding-left: 30rpx;
  }
  .calculation {
    display: flex;
    .watchCalculation {
      font-size: 24rpx;
      color: #339378;
      line-height: 64rpx;
    }
  }
  .calculation1 {
    width: 68%;
  }
  .borderGreen {
    background-color: #fcfffc;
    border-radius: 8rpx;
    border: 2rpx solid #f2f8f2;
  }
  .borderG {
    border: 2rpx solid #f3f8fc;
  }
  .ckickDetail {
    font-size: 28rpx;
    color: #339378;
    line-height: 64rpx;
  }
  .bottom-wrapper {
    background-color: #fff;
    width: 100%;
    display: flex;
    justify-content: center;
    padding-bottom: 50rpx; // 控制与底部的距离
  }

  .reportAdd {
    width: 90%;
    line-height: 74rpx;
    background: #339378;
    border-radius: 38rpx;
    color: #fff;
    font-size: 28rpx;
    text-align: center;
  }
  /* 弹窗主体，自动根据内容撑开，高度过大时内部滚动 */
  .suggestHeight {
    /* 如果内容过多，可滚动 */
    max-height: 90vh;
    display: flex;
    flex-direction: column;
    background: #fff;
    border-top-left-radius: 20rpx;
    border-top-right-radius: 20rpx;
    overflow: hidden;
  }

  /* 标题区 */
  .popupHeader {
    line-height: 110rpx;
  }

  /* 正文区，超出时滚动 */
  .popupBody {
    padding: 0 30rpx 30rpx;
  }

  /* —— 横向 tab 区 —— */
  /* 包裹器：固定高度，定位内部滚动区域 */
  .courseNameWrapper {
    position: relative;
    width: 100%;
    height: 104rpx;
    background: #f9f9f9;
    box-sizing: border-box;
    overflow-x: auto;
    border-bottom: 1rpx solid #e7e7e7;
  }

  /* 固定空白区 */
  .tab-static {
    position: absolute;
    left: 0;
    top: 0;
    width: 140rpx;
    height: 100%;
    /* 可填背景色或留白 */
  }

  /* 真正滚动的区域 */
  .courseName {
    position: absolute;
    left: 140rpx;
    right: 0;
    top: 0;
    bottom: 0;
    overflow-x: auto;
    white-space: nowrap;
  }

  /* 内部 wrapper，放置 tab-item */
  .tab-wrapper {
    display: inline-flex;
    height: 100%;
    align-items: center;
  }

  /* 单个 tab 样式 */
  .tab-item {
    padding: 0 30rpx;
    line-height: 104rpx;
    font-size: 28rpx;
  }
  .tab-item.active {
    background-color: #ebf2fa;
    border-radius: 12rpx;
  }

  /* —— 下方两栏布局 —— */
  .courseGradeBox {
    display: flex;
  }

  /* 左侧：固定宽度，竖向滚动 */
  .courseGrade {
    width: 140rpx;
    height: 100%;
    background: #f5f5f5;
    overflow-y: scroll;
  }
  .grade-wrapper {
    display: flex;
    flex-direction: column;
  }
  .grade-item {
    text-align: center;
    padding: 20rpx 0;
    font-size: 28rpx;
    margin-bottom: 20rpx;
  }
  .grade-item.active {
    background-color: #ebf2fa;
    border-radius: 12rpx;
  }

  .courseGradeChoose {
    padding: 20rpx;
    overflow-x: auto;
    flex: 1;
  }

  .courseDetailWrapper {
    display: flex;
    flex-wrap: wrap; /* 支持换行 */
    gap: 20rpx; /* 元素间距 */
    min-width: 100%; /* 保证可以横向滚动 */
  }

  .courseDetail {
    width: calc((100% - 60rpx) / 4); /* 每行 4 个，减去间距 */
    border: 1rpx solid #dfdfdf;
    padding: 20rpx;
    border-radius: 12rpx;
    text-align: center;
    background-color: #fff;
    transition: all 0.2s;
  }

  .courseDetail.selected {
    background-color: #ecfbec;
    color: #4a9f87;
    border-color: #ecfbec;
  }
  .popupEnd {
    height: 130rpx;
    display: flex;
    justify-content: space-evenly;
  }
  .comfirmChoose,
  .closePopup {
    width: 45%;
    height: 92rpx;
    line-height: 92rpx;
    border-radius: 46rpx;
    font-size: 32rpx;
  }
  .comfirmChoose {
    background-color: #428a6f;
    color: #fff;
  }
  .closePopup {
    border: 2rpx solid #428a6f;
    color: #428a6f;
  }
  .timeline {
    padding: 20rpx;
    position: relative;
  }
  .timelineBg {
    background-image: url('https://document.dxznjy.com/dxSelect/cbdb9f05-b03b-43a3-93b0-fa2eba9fb2d5.png');
    background-repeat: no-repeat;
    /* 宽度撑满，高度等比例 */
    background-size: 80% auto;
    height: 50rpx;
  }
  .updateButton {
    position: absolute;
    right: 5rpx;
    top: 20rpx;
    width: 198rpx;
  }
  .timelineBag {
    border-radius: 16rpx;
    background-color: #f2f7fb;
  }
  .timelineBag1 {
    background-color: #f2f7fb;
  }

  .timeline-item .dot-wrapper {
    position: relative;
    width: 40rpx;
    align-self: stretch;
  }

  .headerTitle {
    flex: 1;
    margin-left: 20rpx; /* 与 header-text margin-left 保持一致 */
    font-size: 28rpx;
    line-height: 1.4;
  }

  /* 4. 章节内容组，左边对齐 */
  .timeline-group {
    margin: 0 0 40rpx 0;
  }

  /* 条目同之前 */
  .timeline-item {
    display: flex;
    align-items: stretch; /* 左右同高 */
    margin-bottom: 20rpx;
  }

  /* 浅绿色圆点 */
  .dot,
  .headDot {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 16rpx;
    height: 16rpx;
    border-radius: 50%;
    transform: translate(-50%, -50%);
    z-index: 1;
  }
  .dot {
    background-color: #d8f3e9;
  }
  .headDot {
    background-color: #339378;
  }

  /* 公共虚线 */
  .dot-line {
    position: absolute;
    left: 50%;
    width: 2rpx;
    background-image: linear-gradient(to bottom, #e5e4e4 33%, rgba(0, 0, 0, 0) 0%);
    background-repeat: repeat-y;
    background-size: 2rpx 23rpx;
    transform: translateX(-50%);
  }

  /* 上半段：从容器顶到圆心 */
  .dot-line-up {
    top: 0;
    bottom: 50%;
  }

  /* 下半段：从圆心到底部 */
  .dot-line-down {
    top: 50%;
    bottom: 0;
  }

  /* 白框内容 */
  .content-box {
    flex: 1;
    background-color: #fff;
    border-radius: 8rpx;
    padding: 34rpx;
    margin-left: 20rpx; /* 与 header-text margin-left 保持一致 */
    box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.05);
    font-size: 28rpx;
    line-height: 1.4;
  }
  .infoWhite {
    background-color: #fff;
  }
  .tetxtColor {
    color: #7e7d81;
  }
  .form-input {
    flex: 1;
    height: 100%;
    font-size: 28rpx;
    border: none;
    background: transparent;
  }
</style>
