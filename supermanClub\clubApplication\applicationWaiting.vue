<template>
	<view class="plr-30">
		<view class="nav-bar">
			<uni-nav-bar color="#000" left-icon="left" :title="type==3?'俱乐部申请':'升级'" :border="false" @clickLeft="back"  backgroundColor="#f3f8fc" />
		</view>
		
		<view class="plr-30 bg-ff radius-15 f-30 positionRelative" :style="{height: useHeight+'rpx'}">
			<view class="waiting-img">
				<image :src="imgHost+'dxSelect/three/icon/jlb-icon.png'" class="img_icon"></image>
				<view class="waiting-text">申请中，请耐心等待...</view>
			</view>
			
			<view v-if="type==3" class="prompt">您加入{{infolist.level!=4?(infolist.level==5?'B2':'B3'):'B1'}}俱乐部的申请正在处理中，请密切关注小程序的变化</view>
			<view v-if="type==4"  class="prompt">您升级成为{{infolist.level!=4?(infolist.level==5?'B2':'B3'):'B1'}}俱乐部的申请正在处理中，请密切关注小程序的变化</view>
			<view class="btn" @click="cancellation">取消申请</view>
		</view>
		
		
		<!-- 取消申请提示 -->
		<uni-popup ref="contactPopup" type="center">
			<view class="dialogBG">
				<view class="reviewCard_box positionRelative">
					<view class="">
						<image :src="dialog_iconUrl" class="cartoom_image"></image>
					</view>
					<view class="reviewCard positionRelative">
						<view class="close-icon">
							<uni-icons type="clear" size="30" color="#b1b1b1" @click="closeDialog"></uni-icons>
						</view>
						<view class="t-c mt-20">
							<image :src="imgHost+'dxSelect/three/icon/prompt-icon.png'" class="prompt-img"></image>
						</view>
						
						<view v-if="infolist.type==1" class="reviewTitles">确定取消加入{{infolist.level!=4?(infolist.level==5?'B2':'B3'):'B1'}}俱乐部申请吗？</view>
						<view v-if="infolist.type==2" class="reviewTitles">确定取消升级{{infolist.level!=4?(infolist.level==5?'B2':'B3'):'B1'}}俱乐部申请吗？</view>
						
						<view class="flex-s mt-55">
							<view class="review_btn" @click="getWithdraw">确定</view>
							<view class="close_btn" @click="closeDialog">取消</view>
						</view>
					</view>
				</view>
			</view>
		</uni-popup>
	</view>
</template>

<script>
	const { $navigationTo, $http } = require("@/util/methods.js")
	import Util from '@/util/util.js'
	export default {
		data() {
			return {
				type:3, // 3俱乐部申请 4升级
				useHeight: 0, //除头部之外高度
				imgHost: getApp().globalData.imgsomeHost,
				infolist:{},
				list:{}, //用户信息
                
                dialog_iconUrl:Util.getCachedPic("https://document.dxznjy.com/dxSelect/dialog_icon.png","dialog_icon_path"),
			};
		},
		onLoad(options) {
			this.type = options.type;
			this.infolist = JSON.parse(options.list);
		},
		onReady() {
		    uni.getSystemInfo({
		        success: (res) => {
		            // 可使用窗口高度，将px转换rpx
		            let h = (res.windowHeight * (750 / res.windowWidth));
		            this.useHeight = h -170;
		        }
		    })
		},
		onShow() {},
		methods:{
			back(){
				uni.navigateBack()
			},
			
			async cancellation(){
				await this.getUserIfApply();
				if(this.list.type ==0){
					this.$util.alter('该申请已通过 请刷新页面');
					return;
				}
				this.$refs.contactPopup.open();
			},
			
			//关闭弹窗
			closeDialog() {
				this.$refs.contactPopup.close();
			},
			
			// 取消俱乐部申请或升级
			async getWithdraw() {
				uni.showLoading();
				const res = await $http({
					url: 'zx/merchant/cancelMerchant',
					method:'post'
				})
				uni.hideLoading();
				if (res) {
					this.$refs.contactPopup.close();
					this.$util.alter(this.type==3?'取消申请成功':'取消升级成功')
					setTimeout(()=>{
						uni.navigateBack();
					},500)
					console.log('0888888888888')
					console.log(res)
					console.log('0888888888888')
				}else{
					this.$util.alter('出错了，请稍后再试')
				}
			},
			
			// 判断登陆用户是否已经有申请俱乐部或者升级俱乐部
			async getUserIfApply(){
				const res=await $http({
					url:'zx/merchant/getUserIfApplyOrUpMerchant'
				})
				if(res.status==1){
					this.list = res.data;
					console.log('111111111111111');
					console.log(res);
					console.log(this.list);
					console.log('111111111111111');
				}
			},
		}
	}
</script>

<style lang="scss" scoped>
	.nav-bar{
		margin-top: 50rpx;
	}
	
	.waiting-img{
		margin-top: 240rpx;
		text-align: center;
		padding-bottom: 100rpx;
		border-bottom: 1rpx solid #EFEFEF;
	}
	
	.img_icon{
		width: 162rpx;
		height: 152rpx;
	}
	
	.waiting-text{
		color: #2E896F;
		margin-top: 20rpx;
	}
	
	.prompt{
		margin-top: 100rpx;
		line-height: 50rpx;
	}
	
	.btn{
		position: absolute;
		bottom: 50rpx;
		height: 90rpx;
		width: 630rpx;
		text-align: center;
		line-height: 90rpx;
		border-radius: 50rpx;
		background: linear-gradient(to bottom,#88CFBA,#1D755C);
	}
	
	
	/* 21天结束复习弹窗样式 */
	.reviewCard_box {
		width: 670rpx;
		position: relative;
	}
	.reviewCard {
		position: relative;
		width: 100%;
		height: 100%;
		background: #FFFFFF;
		color: #000;
		border-radius: 24upx;
		padding: 50upx 60upx;
		box-sizing: border-box;
	}
	
	.close-icon{
		position: absolute;
		top: 20rpx;
		right: 20rpx;
	}
	
	.prompt-img{
		width: 130rpx;
		height: 130rpx;
	}
	
	.cartoom_image {
		width: 420rpx;
		height: 272rpx;
		position: absolute;
		top: -255rpx;
		left: 145rpx;
		z-index: -1;
	}
	
	.reviewTitles {
		font-size: 32rpx;
		margin-top: 30rpx;
		text-align: center;
	}
	
	.review_btn {
		width: 250upx;
		height: 80upx;
		background: linear-gradient(180deg, #88CFBA 0%, #1D755C 100%);
		border-radius: 45upx;
		font-size: 30upx;
		color: #FFFFFF;
		line-height: 80upx;
		justify-content: center;
		text-align: center;
	}
	
	.close_btn {
		width: 250upx;
		height: 80upx;
		color: #2E896F;
		font-size: 30upx;
		line-height: 80upx;
		text-align: center;
		border-radius: 45upx;
		box-sizing: border-box;
		border: 1px solid #2E896F;
	}
	.close-icon{
		position: absolute;
		top: 20rpx;
		right: 20rpx;
	}
</style>
