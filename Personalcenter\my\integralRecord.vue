<template>
  <view class="bg-ff height_css">
    <view class="integral_content_css">
      <view class="f-40">我的积分</view>
      <view class="price_css mt-8">{{ totalCredit }}</view>
    </view>
    <view class="plr-32 integral_list">
      <view class="flexbox" v-for="item in integralList" :key="item.id">
        <view>
          <view class="lh-40 c-55 f-28">{{ item.taskDesc }}</view>
          <view class="lh-36 time_css f-24 mt-8">{{ item.createdTime }}</view>
        </view>
        <view class="integral_css">+{{ item.credit }}</view>
      </view>
    </view>
  </view>
</template>

<script>
  const { $http } = require('@/util/methods.js');
  export default {
    data() {
      return {
        integralList: [],
        totalCredit: uni.getStorageSync('totalCredit')
      };
    },
    created() {
      //
      this.getDetailList();
    },
    methods: {
      async getDetailList() {
        let userId = uni.getStorageSync('user_id') ? uni.getStorageSync('user_id') : '';
        const res = await $http({
          url: 'zx/wap/credit/detail/page',
          data: {
            userId: uni.getStorageSync('user_id') ? uni.getStorageSync('user_id') : '',
            pageNum: 1,
            pageSize: 15
          }
        });
        if (res) {
          this.integralList = res.data.data;
        }
      }
    }
  };
</script>

<style lang="scss" scoped>
  .height_css {
    height: 100vh;
  }
  .integral_content_css {
    line-height: 54rpx;
    background: url('https://document.dxznjy.com/course/ed4a21725c724c5f9d6840c85be6e462.png') no-repeat;
    background-size: 100%;
    padding-top: 52rpx;
    padding-bottom: 24rpx;
    color: #13392f;
    padding-left: 42rpx;
    .price_css {
      font-size: 64rpx;
      font-weight: 900;
    }
  }
  .integral_list {
    height: calc(100% - 200rpx);
    overflow-y: scroll;
  }
  .flexbox {
    padding-bottom: 32rpx;
    padding-top: 32rpx;
    border-bottom: 2rpx solid #f5f5f5;
    .time_css {
      color: #b1b1b1;
    }
  }
  .integral_css {
    color: #5dc99f;
    font-size: 32rpx;
    line-height: 44rpx;
  }
</style>
