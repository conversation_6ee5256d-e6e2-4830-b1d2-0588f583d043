<template>
  <view class="plr-30 pb-30">
    <view class="flex-c" style="padding-top: 60%;">
      <button class="switch-button" @click="showCompletedQuestions">已做试题</button>
    </view>
    <view class="flex-c" style="padding-top: 10%;">
      <button class="switch-button" @click="showUncompletedQuestions">未做试题</button>
    </view>
  </view>
</template>
<script>
export default {
  data() {
    return {
      // 学员
      studentData: [],
      selectedStudentCode: '',
      selectedStudentName: '',
      // 类型
      pickTypeData: [],
      selectedPickTypeId: '',
      selectedPickTypeName: '',
      PhaseStudentId: '',
      PhaseStudentName: '',
      pickGraData: [],
      GraPickTypeId: '',
      GraPickTypeName: '',
      // 类型选择显示与隐藏
      showPhase: false,
      type: null,
      topicQues: false,
    }
  },

  onLoad(options) {
    this.selectedStudentCode = options.studentCode
    this.PhaseStudentId = options.phase
    this.GraPickTypeId = options.knowledgeId
  },
  methods: {
    showCompletedQuestions() {
      // 显示已做试题的逻辑
      this.type = 1 // 设置标识符为已做试题
      uni.navigateTo({
        url: `/errorBook/detailsPage?studentCode=${this.selectedStudentCode}&phase=${this.PhaseStudentId}&knowledgeId=${this.GraPickTypeId}&type=${this.type}`,
      })
    },
    showUncompletedQuestions() {
      // 显示未做试题的逻辑
      this.type = 0 // 设置标识符为未做试题
      uni.navigateTo({
        url: `/errorBook/detailsPage?studentCode=${this.selectedStudentCode}&phase=${this.PhaseStudentId}&knowledgeId=${this.GraPickTypeId}&type=${this.type}`,
      })
    },
  },
}
</script>

<style>
/* 弹层宽度 */
.uv-dp__container {
  height: 150rpx;
  width: 690rpx;
  margin-left: 30rpx;
}

button {
  width: 100% !important;
}



.switch-button {
  width: 100%;
  /* height: 80rpx; */
  /* margin-bottom: 100rpx; */
  background-color: #fafffd;
  color: #555;
  border: 1px solid #428a6f;
  border-radius: 35rpx;
  text-align: center;
  line-height: 80rpx;
}

.popupButtons >>> button {
  width: 100% !important;
  height: 82rpx !important;
  /* margin: 20rpx; */
  margin-top: 20rpx;
}
</style>
