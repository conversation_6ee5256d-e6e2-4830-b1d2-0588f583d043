<template>
  <view class="bg-ff page_main">
    <view class="pb-35 home_page_css">
      <view class="flex-a-c flex-x-b plr-32 home_content_css">
        <view>
          <view class="f-40 lh-56 bold">{{ homepageInfo.userName || '' }}</view>
          <view class="f-28 lh-42 c-55 mt-5">
            {{ homepageInfo.address || '' }}
          </view>
        </view>
        <image class="home_page_image radius-all" :src="homepageInfo.headPhoto ? homepageInfo.headPhoto : avaUrl"></image>
      </view>
      <view class="flex-a-c flex-x-s mt-30 number_content_css">
        <view>
          <view class="c-9A">获赞</view>
          <view class="mt-8 bold">{{ homepageInfo.thumbs || 0 }}</view>
        </view>
        <view class="mlr-25">
          <view class="c-9A">成长值</view>
          <view class="mt-8 bold">{{ homepageInfo.growthValue || 0 }}</view>
        </view>
      </view>
    </view>
    <view @tap="getRecord" class="member_add_number flex-a-c flex-x-b">
      <view class="f-24 lh-42 ml-25">点击查看成长值明细</view>
      <u-icon name="arrow-right" color="#339378" size="28"></u-icon>
    </view>
    <view class="tab_content_cc pt-40">
      <view class="flex-a-c flex-x-s ptb-35 plr-30">
        <view class="f-32 c-55 tabs_item_css" v-for="(item, index) in categoryList" @click="tabsClick({ index: index })" :key="item.key">
          <view :class="tabsCurrent == index ? 'active_tabs_css' : ''">
            <view>
              <view class="tabs_name">{{ item.name }}</view>
              <view class="line_tabs_css"></view>
            </view>
          </view>
        </view>
      </view>
      <view v-if="identityType != 4">
        <!-- 2024-11-6 紧急修改 购买超级会员修改成购买家长会员 隐藏 -->
        <view v-if="false" class="no_member_position" @click="getMember">
          <view>开通会员解锁文化中心全部权益</view>
          <view class="no_member_css f-28 c-ff bold">开通会员</view>
        </view>
      </view>
      <view v-else>
        <view v-if="tabsCurrent == 0">
          <!-- 	<view  v-for="item in releaseSelecte" class="mt-40 pb-20 plr-32 pt-30 border_color">
						<releaseItem releaseType="1" @getRelease="getRelease" :releaseStyle="releaseStyle" :key="item.id" :releaseInfo="item"></releaseItem>
					</view> -->
          <view v-for="item in tableData" :key="item.id" class="mt-40 pb-20 pt-15 plr-32 border_color">
            <releaseItem releaseType="3" @showImage="showImage" :myAvaUrl="avaUrl" @getRelease="getRelease" :releaseStyle="releaseStyle" :releaseInfo="item"></releaseItem>
          </view>
        </view>
        <view v-if="tabsCurrent == 1">
          <u-tabs
            :list="problemList"
            :current="problemCurrent"
            keyName="name"
            lineWidth="0"
            lineHeight="0"
            :activeStyle="{ color: '#333333', fontWeight: 'bold', fontSize: '28rpx' }"
            :inactiveStyle="{ color: '#5A5A5A ', transform: 'scale(1)', fontSize: '28rpx' }"
            itemStyle="padding-left:56px; padding-right: 75px; height: 34px;"
            :lineColor="`url(${lineBg}) 100% 110%`"
            @click="problemClick"
          ></u-tabs>
          <view class="wen_content_style plr-32">
            <view v-if="problemCurrent == 0">
              <view v-for="item in problemData" :key="item.id" class="mt-24">
                <problemItem problemType="1" :showAnswer="true" :problemInfo="item" :problemStyle="problemStyle"></problemItem>
              </view>
            </view>
            <view v-if="problemCurrent == 1">
              <view v-for="item in problemData" :key="item.id" class="mt-24">
                <problemItem problemType="1" :showAnswer="true" :problemInfo="item" :problemStyle="problemStyle"></problemItem>
              </view>
            </view>
          </view>
        </view>
        <view
          v-if="
            (tabsCurrent == 0 && tableData.length == 0) ||
            (tabsCurrent == 1 && problemCurrent == 1 && problemData.length == 0) ||
            (tabsCurrent == 1 && problemCurrent == 0 && problemData.length == 0)
          "
        >
          <emptyPage></emptyPage>
        </view>
      </view>
    </view>
    <growthPopup ref="growthPopupRefs"></growthPopup>
  </view>
</template>

<script>
  const { $navigationTo, $getSceneData, $showError, $http } = require('@/util/methods.js');
  import problemItem from './components/problemItem.vue';
  import releaseItem from './components/releaseItem.vue';
  import growthPopup from './components/growthPopup.vue';
  import emptyPage from './components/emptyPage.vue';
  export default {
    components: { problemItem, releaseItem, growthPopup, emptyPage },
    data() {
      return {
        categoryList: [
          { name: '瞬间', key: 2 },
          { name: '问答', key: 1 }
        ],
        tabsCurrent: 0,
        avaUrl: uni.getStorageSync('headPortrait') ? uni.getStorageSync('headPortrait') : 'https://document.dxznjy.com/dxSelect/home_avaUrl.png',
        lineBg: 'https://document.dxznjy.com/course/01c96465d9ea46f69aa97465b1201aef.png',
        // identityType:uni.getStorageSync('identityType'),
        identityType: uni.getStorageSync('identityType'),
        // nickName
        //问题
        problemData: [],
        problemStyle: {
          leftImageWidth: '60rpx'
        },
        tableData: [],
        releaseStyle: {
          leftImageWidth: '82rpx'
        },
        homepageInfo: {},
        //问答
        problemList: [
          { name: '我的问答', key: 2 },
          { name: '我的提问', key: 1 }
        ],
        problemCurrent: 0,
        page: 1,
        showImageType: false,
        infoLists: {}
      };
    },
    onShow() {
      this.page = 1;
      if (this.showImageType) {
        this.showImageType = false;
        return;
      }
      this.getPersonInfo();
      if (this.tabsCurrent == 1) {
        this.getProblem(this.problemCurrent);
      }
    },
    onReachBottom() {
      if (this.page * 10 >= this.infoLists.totalItems) {
        return false;
      }
      if (this.tabsCurrent == 0) {
        this.getPersonInfo(true, ++this.page);
      } else if (this.tabsCurrent == 1) {
        this.getProblem(this.problemCurrent, true, ++this.page);
      }
    },
    methods: {
      async getPersonInfo(isPage, page) {
        const res = await $http({
          url: 'zx/wap/CultureCircle/person',
          data: {
            pageNum: page || 1,
            pageSize: 10,
            userId: uni.getStorageSync('user_id') ? uni.getStorageSync('user_id') : ''
          }
        });
        if (res) {
          this.homepageInfo = res.data;
          // this.tableData=res.data.circleListVos||[]
          this.infoLists = res.data.circleListVos;
          if (isPage) {
            this.tableData = [...this.tableData, ...res.data.circleListVos.data];
          } else {
            this.tableData = res.data.circleListVos.data || [];
          }
        }
      },
      showImage(list, index) {
        this.showImageType = true;
        let photoList = list.map((item) => {
          return item.url;
        });
        uni.previewImage({
          urls: photoList,
          current: index,
          indicator: 'default'
        });
      },
      tabsClick(e) {
        this.page = 1;
        this.tabsCurrent = e.index;
        if (this.tabsCurrent == 1) {
          this.getProblem(this.problemCurrent);
        } else {
          this.getPersonInfo();
        }
      },
      async getProblem(type, isPage, page) {
        const res = await $http({
          url: 'zx/wap/CultureCircle/getQuestion',
          data: {
            type: type == 0 ? 1 : 0,
            pageNum: page || 1,
            pageSize: 10,
            userId: uni.getStorageSync('user_id') ? uni.getStorageSync('user_id') : ''
          }
        });
        if (res) {
          if (!res.data) {
            return;
          }
          this.infoLists = res.data;
          if (isPage) {
            this.problemData = [...this.problemData, ...res.data.data];
          } else {
            this.problemData = res.data.data || [];
          }
        }
      },
      getMember() {
        $navigationTo('Personalcenter/my/nomyEquity?type=2');
      },
      problemClick(e) {
        if (e.index != this.problemCurrent) {
          this.problemData = [];
        }
        this.problemCurrent = e.index;
        this.getProblem(this.problemCurrent);
      },
      getRecord() {
        uni.navigateTo({
          url: '/memberCenter/detail/growUpRecord'
        });
      }
    }
  };
</script>

<style lang="scss" scoped>
  .home_page_css {
    background: url('https://document.dxznjy.com/course/f01f45f731ad43a9a787a532830bd40a.png') no-repeat;
    background-size: 100%;
    background-position-y: 140rpx;
    .home_content_css {
      padding-top: 80rpx;
    }
    .number_content_css {
      padding-left: 32rpx;
    }
  }
  .member_add_number {
    color: #339378;
    width: 292rpx;
    height: 48rpx;
    background-color: #ecfcf7;
    padding-right: 28rpx;
    position: absolute;
    right: 0;
    top: 265rpx;
  }
  .tab_content_cc {
    background: linear-gradient(180deg, #e5f1ee 0%, #ffffff 20%, #ffffff 100%);
    box-shadow: 0rpx -4rpx 16rpx 2rpx rgba(211, 232, 226, 0.48);
    border-radius: 40rpx 40rpx 0rpx 0rpx;
    min-height: 100vh;
    .no_member_position {
      margin-top: 332rpx;
      text-align: center;
      .no_member_css {
        width: 360rpx;
        height: 96rpx;
        background: #339378;
        box-shadow: 0rpx 4rpx 8rpx 0rpx #36a586;
        border-radius: 48rpx;
        line-height: 96rpx;
        margin: 0 auto;
        margin-top: 32rpx;
      }
    }
  }
  .home_page_image {
    width: 134rpx;
    height: 134rpx;
  }
  .table_list_css {
    display: flex;
    .time_left_css {
      width: 160rpx;
    }
    .file_image_css {
      width: 160rpx;
      height: 160rpx;
    }
  }
  .border_color {
    border-bottom: 1rpx solid #ecf0f4;
  }
  .tabs_item_css {
    margin-right: 96rpx;
    .active_tabs_css {
      font-weight: bold;
      color: #333;
      // position: relative;
      .tabs_name {
        z-index: 1;
      }
      .line_tabs_css {
        width: 100%;
        height: 8rpx;
        border-radius: 8rpx;
        background-color: #28a781;
        margin-top: -10rpx;
        // position: absolute;
        // bottom:3rpx;
        z-index: 0;
      }
    }
  }
  .wen_content_style {
    background: #f8f8f8;
    border-radius: 40rpx 40rpx 0rpx 0rpx;
    height: 100%;
  }
  // .tabs_item_css:first-child{
  // 	padding-right: 54rpx;
  // 	position: relative;
  // }
  // .tabs_item_css:first-child::after{
  // 	content: "";
  // 	display: inline-block;
  // 	height: 28rpx;
  // 	border-right: 1rpx solid #D7DDE3;
  // 	position: absolute;
  // 	right:0;
  // 	top:10rpx;
  // }
  // .tabs_item_css:last-child{
  // 	padding-left: 54rpx;
  // }
  .c-9A {
    color: #9a9a9a;
  }
</style>
