<template>
  <view
    class="container"
    @click="
      showMenu = false;
      showPaste = false;
    "
  >
    <view class="record" @click="jumpRecords">最近通话</view>
    <view :style="'height: ' + contentHeight + 'rpx'">
      <!-- 显示拨号输入 -->
      <view class="display" style="position: relative" @click.stop="showCopyPopup">
        <view class="phone-number">{{ phoneNumber }}</view>
        <view class="phone_line"></view>
        <!-- 自定义气泡菜单 -->
        <view v-if="showMenu" class="bubble-menu">
          <view class="bubble-arrow"></view>
          <view class="bubble-option" @click.stop="copyPhoneNumber">拷贝</view>
          <view class="divider"></view>
          <view class="bubble-option" @click.stop="pastePhoneNumber">粘贴</view>
        </view>
        <!-- 自定义气泡菜单 -->
        <view v-if="showPaste" class="bubble-menu">
          <view class="bubble_arrow_aste"></view>
          <view class="bubble-option" @click.stop="pastePhoneNumber">粘贴</view>
        </view>
      </view>
      <view class="content">
        <!-- 拨号键盘 -->
        <view class="dialer">
          <view class="row" v-for="(row, rowIndex) in keypad" :key="rowIndex">
            <view v-for="(key, keyIndex) in row" :key="keyIndex" class="dial-key" @click="handleKeyPress(key.number)">
              <button class="key_item" :style="'width:' + circleWidth + 'rpc' + '; height:' + circleWidth + 'rpx'">
                <text class="key-number">{{ key.number }}</text>
                <text class="key-letters">{{ key.letters }}</text>
              </button>
            </view>
          </view>
        </view>
      </view>
    </view>
    <view style="display: flex; justify-content: center; align-items: center; width: 100%">
      <!-- 操作按钮 -->
      <view class="actions">
        <view class="call-btn" @click="makeCall">
          <view style="margin-right: 10rpx">隐私号码拨打</view>
          <u-icon name="phone-fill" color="#FFF" size="42"></u-icon>
        </view>
        <!-- <button class="delete-btn" @click="deleteLast">⌫</button> -->
        <view class="delete-btn" @click="deleteLast">
          <view class="delete-icon">×</view>
        </view>
      </view>
    </view>
    <!-- 弹框 -->
    <uni-popup ref="comfirmPoup" type="center">
      <view class="phoneCard">
        <view class="pb-20">将使用该号码加密呼出</view>
        <view class="my_phone bold pb-20">{{ phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2') }}</view>
        <view class="modify_text pb-20" @click="modifyPoup">非本机号请修改 ></view>
        <view class="pb-20" style="display: flex; justify-content: space-between">
          <view class="com_btn cancel_btn" @click="cancelFn('comfirmPoup')">取消</view>
          <view class="com_btn call_btn" @click="callMiddlePhone">立即呼叫</view>
        </view>
        <view class="tip">为保证服务质量，您的通话可能会被录音</view>
      </view>
    </uni-popup>
    <uni-popup ref="phoneModifyPoup" type="center" :mask-click="false">
      <view class="modifyCard">
        <view class="modify_top">
          <view class="bold pb-50">输入本机号码</view>
          <view class="phoneInput"><input placeholder="请输入本机号码" v-model.trim="myPhone" /></view>
        </view>
        <view class="btn_all">
          <view class="cancel_btn" @click="cancelFn('phoneModifyPoup')">取消</view>
          <view class="call_btn" @click="callMiddlePhone">立即呼叫</view>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script>
  export default {
    data() {
      return {
        phoneNumber: '', // 存储输入的电话号码
        keypad: [
          [
            { number: '1', letters: ` ` },
            { number: '2', letters: 'ABC' },
            { number: '3', letters: 'DEF' }
          ],
          [
            { number: '4', letters: 'GHI' },
            { number: '5', letters: 'JKL' },
            { number: '6', letters: 'MNO' }
          ],
          [
            { number: '7', letters: 'PQRS' },
            { number: '8', letters: 'TUV' },
            { number: '9', letters: 'WXYZ' }
          ],
          [
            { number: '*', letters: '' },
            { number: '0', letters: '+' },
            { number: '#', letters: '' }
          ]
        ],
        phone: uni.getStorageSync('myCallphone') ? uni.getStorageSync('myCallphone') : uni.getStorageSync('phone') ? uni.getStorageSync('phone') : '',
        myPhone: '', // 存储本机号码
        showMenu: false, // 控制气泡菜单的显示与隐藏
        showPaste: false, // 控制粘贴按钮的显示与隐藏
        contentHeight: 0, // 存储内容区域的高度
        circleWidth: 120 // 默认圆形按钮的宽度
      };
    },
    onShow() {
      console.log('页面显示');
      const systemInfo = uni.getSystemInfoSync();
      // const menuButtonInfo = uni.getMenuButtonBoundingClientRect();
      const screenHeightPx = systemInfo.screenHeight; // 屏幕高度（px）
      const screenWidthPx = systemInfo.screenWidth; // 屏幕宽度（px）
      const statusBarHeight = systemInfo.statusBarHeight; // 状态栏高度（px）
      const navBarHeight = (50 - statusBarHeight) * 2 + 80; // 计算导航栏高度
      const availableHeightPx = screenHeightPx - navBarHeight; // 可用高度（减去导航栏）
      const availableHeightRpx = availableHeightPx * (750 / screenWidthPx); // 换算成 rpx
      const screenWidthrPx = screenWidthPx * (750 / screenWidthPx); // 换算成 rpx
      this.contentHeight = availableHeightRpx - 250; // 减去拨号键盘的高度
      let a = this.contentHeight * 0.8 * 0.2;
      let b = screenWidthrPx * 0.7 * 0.28;
      console.log(`拨号键盘高度：${a}rpx`);
      console.log(`拨号键盘宽度：${b}rpx`);
      if (a - b > 0) {
        this.circleWidth = Math.floor(b);
      } else {
        this.circleWidth = Math.floor(a);
      }
      console.log(`屏幕高度：${screenHeightPx}px`);
      console.log(`导航栏高度：${navBarHeight}px`);
      console.log(`去掉导航栏后高度：${availableHeightPx}px (${availableHeightRpx}rpx)`);
      console.log(`圆形按钮的宽度：${this.circleWidth}rpx`);
      console.log(`屏幕高度：${screenHeightPx}px，换算成 rpx：${availableHeightRpx}rpx`);
      // 页面加载时，清空电话号码
      //   this.phoneNumber = '';
    },
   onUnload() {
      // #ifdef APP-PLUS
      if (this.app) {
        plus.runtime.quit();
      }
      // #endif
    },
    onLoad(e) {
      console.log('页面加载');
      // 页面加载时，清空电话号码
      if (e.token) {
        this.app = e.app;
        this.$handleTokenFormNative(e);
        this.userId = uni.getStorageSync('user_id') ? uni.getStorageSync('user_id') : '';
      }
    },
    methods: {
      // 处理按键输入
      handleKeyPress(key) {
        // if (this.phoneNumber.length < 15) {
        //   this.phoneNumber += key;
        // }
        this.phoneNumber += key;
      },

      // 删除最后一位
      deleteLast() {
        this.phoneNumber = this.phoneNumber.slice(0, -1);
      },

      // 拨打电话
      makeCall() {
        if (!this.phoneNumber) {
          uni.showToast({ title: '请输入号码', icon: 'none' });
          return;
        }
        this.phone = uni.getStorageSync('myCallphone') ? uni.getStorageSync('myCallphone') : uni.getStorageSync('phone') ? uni.getStorageSync('phone') : '';
        this.$refs.comfirmPoup.open(); // 打开手机号确认弹窗
      },

      //   获取虚拟号并拨打
      callMiddlePhone() {
        let phoneA = '';
        // 如果本机号码不为空，则使用本机号码，否则使用存储的号码
        if (this.myPhone) {
          phoneA = this.myPhone;
          uni.setStorageSync('myCallphone', phoneA); // 将本机号码存储到本地
        } else {
          phoneA = this.phone;
        }
        uni.setStorageSync('myCallphone', phoneA); // 将本机号码存储到本地
        let data = {
          userMobile: phoneA,
          customerMobile: this.phoneNumber,
          userId: uni.getStorageSync('user_id') ? uni.getStorageSync('user_id') : ''
        };
        console.log(phoneA, this.phoneNumber, '本机号码');
        this.$httpUser.post('zx/privacy/bind?userMobile=' + phoneA + '&customerMobile=' + this.phoneNumber + '&userId=' + data.userId).then((res) => {
          console.log(res, '获取虚拟号码成功');
          if (res.data.status !== 1) {
            uni.showToast({ title: res.data.message, icon: 'none' });
            return;
          }
          uni.makePhoneCall({
            phoneNumber: res.data.data.secretNo,
            success: () => {
              console.log('拨号成功', this.phoneNumber);
              setTimeout(() => {
                //   uni.navigateBack({ delta: 1 });
                //     uni.redirectTo({
                //       url: '/middlePage/callHistory'
                //     });
                this.phoneNumber = ''; // 清空电话号码
              }, 2000);
            },
            fail: (err) => {
              uni.showToast({ title: '拨打失败', icon: 'none' });
              console.error('拨打电话失败：', err);
            },
            complete: () => {
              console.log('拨打电话完成');
              this.$refs.comfirmPoup.close(); // 关闭手机号确认弹窗
              this.$refs.phoneModifyPoup.close(); // 关闭手机号修改弹窗
            }
          });
        });
      },
      jumpRecords() {
        uni.navigateTo({
          url: '/middlePage/callHistory'
        });
      },
      cancelFn(ref) {
        this.$refs[ref].close(); // 关闭手机号修改弹窗
      },
      modifyPoup() {
        this.myPhone = '';
        this.$refs.comfirmPoup.close(); // 关闭手机号确认弹窗
        this.$refs.phoneModifyPoup.open(); // 打开手机号修改弹窗
      },
      //   复制粘贴
      showCopyPopup() {
        if (this.phoneNumber) {
          this.showMenu = !this.showMenu; // 显示气泡菜单
        } else {
          this.showPaste = !this.showPaste; // 显示粘贴按钮
        }
      },
      // 复制号码
      copyPhoneNumber() {
        uni.setClipboardData({
          data: this.phoneNumber
          //   success: () => wx.showToast({ title: '已复制', icon: 'success' })
        });
        this.showMenu = false;
      },

      // 粘贴号码
      pastePhoneNumber() {
        uni.getClipboardData({
          success: (res) => {
            this.phoneNumber = res.data.trim(); // 去除前后空格
            // wx.showToast({ title: '已粘贴', icon: 'success' });
          }
        });
        this.showMenu = false;
        this.showPaste = false; // 隐藏粘贴按钮
      }
    }
  };
</script>

<style lang="scss" scoped>
  /* 布局容器 */
  .container {
    height: 100vh;
    padding-bottom: 100rpx;
    box-sizing: border-box;
    background: #fff;
  }
  .content {
    height: 80%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-end;
    box-sizing: border-box;
  }
  /* 最近通话 */
  .record {
    height: 100rpx;
    color: #4095e5;
    text-align: right;
    line-height: 100rpx;
    font-size: 32rpx;
    margin-right: 32rpx;
    /* box-shadow: 0rpx 8rpx 12rpx rgba(0, 0, 0, 0.1); */
  }
  /* 显示输入的电话号码 */
  .display {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 20%;
    /* background: white; */
    /* border-radius: 20rpx; */
    text-align: center;
    line-height: 1;
    font-size: 48rpx;
    font-weight: bold;
    padding-bottom: 30rpx;
    box-sizing: border-box;

    /* box-shadow: 0rpx 8rpx 12rpx rgba(0, 0, 0, 0.1); */
  }

  .phone-number {
    width: 80%;
    height: 60%;
    color: #333;
    /* overflow: scroll; */
    white-space: nowrap;
    overflow: hidden; /* 隐藏溢出内容 */
    text-overflow: ellipsis; /* 超出用省略号代替 */
    direction: rtl; /* 让文本靠右显示，自动省略左边 */
    line-height: 1;
    unicode-bidi: plaintext;
  }
  .phone_line {
    width: 60%;
    height: 2rpx;
    border-bottom: 1rpx solid rgba(221, 219, 219, 0.2);
  }

  /* 拨号键盘 */
  .dialer {
    width: 70%;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: space-between;
  }

  .row {
    display: flex;
    justify-content: space-between;
    width: 100%;
    height: 20%;
  }

  .dial-key {
    width: 28%;
    height: 100%;
    // margin: 18rpx;
    font-size: 48rpx;
    font-weight: bold;
    border: none;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    .key_item {
      // width: min(100%, 100%); /* 取小盒子最短的边作为圆的直径 */
      // height: min(100%, 100%);
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      background-color: #e5e5e5; /* 浅灰色 */
      border-radius: 50%;
      aspect-ratio: 1 / 1; /* 保持正方形 */
      box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
      transition: all 0.2s ease-in-out;
    }
  }
  .key-number {
    font-size: 48rpx;
    font-weight: bold;
  }

  .key-letters {
    font-size: 24rpx;
    color: #666;
  }

  /* 操作按钮 */
  .actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 40rpx;
    width: 80%;
  }
  .delete-btn {
    width: 72rpx;
    height: 64rpx;
    background-color: #e0e0e0; /* 浅灰色 */
    border-radius: 0 6rpx 6rpx 0;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    cursor: pointer;
    box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
    // transition: all 0.2s ease-in-out;
  }

  .delete-btn::before {
    content: '';
    position: absolute;
    left: -28rpx;
    top: 0;
    width: 28rpx;
    height: 100%;
    background-color: #e0e0e0;
    clip-path: polygon(100% 0, 0 50%, 100% 100%);
  }

  .delete-icon {
    font-size: 38rpx;
    font-weight: bold;
    color: #333;
    z-index: 1;
  }

  //   .delete-btn:active {
  //     transform: scale(0.9);
  //     box-shadow: 0 1rpx 3rpx rgba(0, 0, 0, 0.2);
  //   }

  .call-btn {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 440rpx;
    height: 100rpx;
    font-size: 42rpx;
    font-weight: bold;
    background: #34c85a;
    color: white;
    border-radius: 50rpx;
    line-height: 100rpx;
  }
  .phoneCard {
    position: relative;
    width: 100%;
    height: 100%;
    background: #ffffff;
    color: #000;
    border-radius: 24upx;
    padding: 45upx 65upx;
    box-sizing: border-box;
    text-align: center;
    .my_phone {
      font-size: 48rpx;
    }
    .modify_text {
      color: #4095e5;
    }
    .cancel_btn {
      border: 2rpx solid #bbb;
    }
    .call_btn {
      color: #fff;
      background: #158954;
    }
  }
  .modifyCard {
    position: relative;
    width: 100%;
    height: 100%;
    background: #ffffff;
    border-radius: 24upx;
    color: #000;
    box-sizing: border-box;
    text-align: center;
    .modify_top {
      padding: 45upx 65upx;
      .phoneInput {
        width: 480rpx;
        height: 68rpx;
        border-bottom: 2rpx solid #bbb;
        margin-bottom: 24rpx;
        font-size: 38rpx;
        line-height: 68rpx;
      }
    }
    .btn_all {
      display: flex;
      justify-content: space-between;
      height: 88rpx;
      line-height: 88rpx;
      border-radius: 0 0 10rpx 10rpx;
      border-top: 2rpx solid #bbb;
      .cancel_btn {
        width: 50%;
        border-right: 2rpx solid #bbb;
      }
      .call_btn {
        width: 50%;
        color: #158954;
      }
    }
  }

  .com_btn {
    width: 220rpx;
    height: 68rpx;
    border-radius: 10rpx;
    font-size: 30rpx;
    font-size: 30rpx;
    color: #333;
    line-height: 68rpx;
    text-align: center;
  }
  .tip {
    font-size: 28rpx;
    color: #9a9a9a;
  }
  .bubble-menu {
    position: absolute;
    bottom: 0rpx;
    height: 69rpx;
    line-height: 69rpx;
    background: #fdfcfc;
    border-radius: 10rpx;
    box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.15);
    display: flex;
    z-index: 1000;
  }

  .bubble-option {
    font-size: 24rpx;
    color: #333;
    padding: 0 28rpx;
  }

  .divider {
    width: 1px;
    background: #ddd;
  }
  .bubble-arrow {
    z-index: 1009;
    position: absolute;
    top: -18rpx;
    left: 50%;
    transform: translateX(-50%);
    width: 0;
    height: 0;
    border-left: 18rpx solid transparent;
    border-right: 18rpx solid transparent;
    border-bottom: 18rpx solid #fdfcfc;
    // filter: drop-shadow(-2rpx 2rpx 10rpx rgba(0, 0, 0, 0.15)) drop-shadow(2rpx 2rpx 10rpx rgba(0, 0, 0, 0.15)); /* 左右两边阴影 */
  }
  .bubble_arrow_aste {
    position: absolute;
    top: -14rpx;
    left: 50%;
    transform: translateX(-50%);
    width: 0;
    height: 0;
    border-left: 14rpx solid transparent;
    border-right: 14rpx solid transparent;
    border-bottom: 14rpx solid white;
  }
</style>
