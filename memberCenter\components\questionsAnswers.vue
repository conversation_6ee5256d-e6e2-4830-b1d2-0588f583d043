<template>
  <view class="bg-ff mt-24 radius-16 pb-30">
    <view class="flex-x-s flex-a-c plr-32 pt-24">
      <view class="question_answers f-24 c-55">发布于{{ questionsAnswersInfo.createTime }}</view>
      <image
        v-if="questionsAnswersInfo.likeFlag && !showLike"
        @click="getThumb(1)"
        class="release_image ml-35"
        src="https://document.dxznjy.com/course/277db746077545ee980467f2ec7a50db.png"
      ></image>
      <image v-else-if="!showLike" class="release_image ml-35" @click="getThumb(2)" src="https://document.dxznjy.com/course/4eaf055301bc472a949c5b4e15792cf4.png"></image>
      <view v-if="!showLike" class="ml-8 f-24 release_text">{{ questionsAnswersInfo.likeCount }}</view>
    </view>
    <view class="flex-x-s flex-y-s mt-24 pl-35">
      <view class="header_css positionRelative">
        <image class="header_css radius-all" :src="questionsAnswersInfo.userHeadPortrait || avaUrl"></image>
        <image class="positionAbsolute da_icon_css" src="https://document.dxznjy.com/course/b5cf5b0b3f2b493cb1998fe2eca8f823.png"></image>
      </view>
      <view class="f-28 c-55 ml-25 pr-10">{{ questionsAnswersInfo.questionText }}</view>
    </view>
    <view v-if="questionsType == 1" class="flex-x-s flex-y-s mt-30 pl-35">
      <view class="header_css positionRelative">
        <image class="header_css radius-all" :src="questionsAnswersInfo.replyUserHeadPortrait || avaUrl"></image>
        <image class="positionAbsolute da_icon_css" src="https://document.dxznjy.com/course/3fbe7257487d4fdc9be292d520e0e0c3.png"></image>
      </view>
      <view class="ml-25">
        <!-- @click="hearAnswers" -->
        <view v-if="questionsAnswersInfo.answerType == 1" class="audio_style_css pl-35">
          <span class="f-24 c-55">60”</span>
        </view>
        <view v-else-if="questionsAnswersInfo.answerType == 0" class="flex-x-b flex-y-e f-24 c-ab lh-44">
          <view class="pr-10" :class="showAnswers ? 'answerText_style' : 'answerText_css'">{{ questionsAnswersInfo.answerText }}</view>
          <view @click="changeAnswers" class="f-28 bold color_css">{{ showAnswers ? '收起' : '展开' }}</view>
        </view>
        <view v-else-if="questionsAnswersInfo.answerType == 2" class="f-24 c-ab lh-44 pt-15">暂未回答</view>
      </view>
    </view>
    <view v-if="questionsType == 2" class="flex-x-s flex-y-s mt-30 pl-35">
      <view class="header_css positionRelative">
        <image class="header_css radius-all" :src="headerUrl"></image>
        <image class="positionAbsolute da_icon_css" src="https://document.dxznjy.com/course/3fbe7257487d4fdc9be292d520e0e0c3.png"></image>
      </view>

      <view class="ml-15 flex-y-s flex-x-s temporary_info">
        <view v-if="questionsAnswersInfo.answerType == 1" style="width: 306rpx" @click="playRadius" class="audio_style_css pl-35">
          <span class="f-24 c-55">60”</span>
        </view>
        <view style="width: 420rpx" v-else>
          <view class="f-28 lh-40 pt-15 pb-45 textarea_style_css">
            <u--textarea placeholder="请输入你的回答" maxlength="500" autoHeight border="none" v-model="answerText"></u--textarea>
            <view class="textarem_number f-24 c-55">{{ answerText.length }}/500</view>
          </view>
          <!-- <textarea class="f-28 textarea_css lh-40 pt-15"  placeholder="请输入你的回答" ></textarea> -->
        </view>
        <!-- <view @click="resetVideo" class="btn_css left_btn_css f-28  ml-8">重发</view> -->
        <view class="pt-24 ml-8">
          <view @click="releaseAnswers" class="btn_css right_btn_css f-28">发布</view>
        </view>
      </view>
      <!-- 	<view v-else  @longpress="onStartRecoder"  @touchend="onEndRecoder"  class="ml-15">
				<view class="radius_btn f-24 c-55">按住说话</view>
			</view> -->
    </view>
  </view>
</template>

<script>
  const { $http, $showMsg } = require('@/util/methods.js');
  const recorderManager = uni.getRecorderManager();
  const innerAudioContext = uni.createInnerAudioContext();
  innerAudioContext.autoplay = true;
  export default {
    props: ['questionsType', 'questionsAnswersInfo', 'itemIndex', 'showLike'],
    data() {
      return {
        answerText: '',
        showAnswers: false,
        identityType: uni.getStorageSync('identityType'),
        avaUrl: 'https://document.dxznjy.com/dxSelect/home_avaUrl.png',
        headerUrl: uni.getStorageSync('headPortrait') ? uni.getStorageSync('headPortrait') : 'https://document.dxznjy.com/dxSelect/home_avaUrl.png'
      };
    },
    watch: {
      answerText: {
        handler(val) {
          if (val.length >= 500) {
            this.answerText = val.substr(0, 500);
          }
        },
        immediate: true,
        deep: true
      }
    },
    created() {
      let self = this;
      recorderManager.onStop(function (res) {
        self.questionsAnswersInfo.voicePath = res.tempFilePath;
        self.$emit('addRadius', res.tempFilePath, this.itemIndex);
        // self.$set(self.questionsAnswersInfo,'voicePath',res.tempFilePath)
      });
    },
    methods: {
      hearAnswers() {
        this.$emit('hearAnswers', this.questionsAnswersInfo);
      },
      onStartRecoder() {
        recorderManager.start();
      },
      onEndRecoder() {
        recorderManager.stop();
      },
      getThumb(key) {
        this.$emit('addThumb', this.questionsAnswersInfo, key);
      },
      //从新录制
      resetVideo() {
        this.questionsAnswersInfo.voicePath = '';
      },
      playRadius() {
        if (this.questionsAnswersInfo.voicePath) {
          innerAudioContext.src = this.questionsAnswersInfo.voicePath;
          innerAudioContext.play();
        }
      },
      async changeAnswers() {
        if (this.identityType != 4 && !this.questionsAnswersInfo.lookAnswerFlag) {
          this.$emit('buyAnswers', this.questionsAnswersInfo);
          return;
        }
        if (this.identityType == 4 || this.questionsAnswersInfo.lookAnswerFlag) {
          this.showAnswers = !this.showAnswers;
        }
      },
      async releaseAnswers() {
        if (!this.answerText) {
          $showMsg('请输入问题的回答');
          return;
        }
        if (this.answerText.length <= 100) {
          $showMsg('输入问题的回答字符要超过100');
          return;
        }
        this.auditContent();
      },
      async auditContent() {
        const res = await $http({
          url: 'zx/common/auditContent',
          method: 'POST',
          showLoading: true,
          data: {
            content: this.answerText,
            scene: 2
          }
        });
        if (res) {
          if (res.data == 'pass') {
            let _this = this;
            const res = await $http({
              url: 'zx/wap/qa/saveAnswer',
              method: 'POST',
              data: {
                id: this.questionsAnswersInfo.id,
                answerText: this.answerText,
                answerType: 0
              }
            });
            if (res) {
              this.answerText = '';
              _this.$emit('refreshInfo');
            }
          } else if (res.data == 'risk') {
            $showMsg('您回答的内容有风险');
          }
        }
      }
    }
  };
</script>

<style lang="scss" scoped>
  .question_answers {
    width: 580rpx;
  }
  .release_image {
    width: 32rpx;
    height: 32rpx;
  }
  .header_css {
    width: 64rpx;
    height: 64rpx;
  }
  .answerText_css {
    width: 480rpx;
    height: 40rpx;
    overflow: hidden;
  }
  .answerText_style {
    width: 480rpx;
    word-break: break-all;
  }
  .color_css {
    color: #28a781;
  }
  .radius_btn {
    width: 336rpx;
    height: 64rpx;
    background: url('https://document.dxznjy.com/course/66686ca498e8439ab7b95256afd4973d.png') no-repeat;
    background-size: 100%;
    text-align: center;
    line-height: 60rpx;
  }
  .audio_style_css {
    width: 372rpx;
    height: 48rpx;
    border: 2rpx solid #e4f5f2;
    background-color: #eff6f4;
    border-radius: 24rpx;
  }
  .da_icon_css {
    width: 40rpx;
    height: 40rpx;
    bottom: 0;
    right: -4rpx;
  }
  .temporary_info {
    .btn_css {
      width: 106rpx;
      height: 48rpx;
      text-align: center;
      line-height: 48rpx;
    }
    .left_btn_css {
      color: #fe771d;
      background: url('https://document.dxznjy.com/course/d927f9c4b16a4162ae81b8de5ff3a370.png') no-repeat;
      background-size: 100%;
    }
    .right_btn_css {
      color: #339378;
      background: url('https://document.dxznjy.com/course/b0e2f12a9595444dbf5193187a9ef480.png') no-repeat;
      background-size: 100%;
      border-radius: 6rpx;
    }
    /deep/.u-textarea {
      min-height: 100rpx;
      width: 420rpx;
      border: none;
    }
    .textarea_style_css {
      position: relative;
      border: 1rpx solid #e1e1e1;
      border-radius: 8rpx;
      .textarem_number {
        position: absolute;
        bottom: 0;
        right: 10rpx;
      }
    }
  }
</style>
