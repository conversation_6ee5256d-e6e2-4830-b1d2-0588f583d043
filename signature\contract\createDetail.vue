<template>
  <view class="ctxt plr-30 bg-ff">
    <view class="plr-30 bg-ff radius-20 ptb-30">
      确认订单
      <view class="m-45">
        <button class="nextstep" form-type="submit" @click="submit('baseForm')" :disabled="disabled">下一步</button>
      </view>
    </view>
  </view>
</template>

<script>
  export default {
    data() {
      return {};
    },
    methods: {
      name() {}
    }
  };
</script>

<style scoped></style>
