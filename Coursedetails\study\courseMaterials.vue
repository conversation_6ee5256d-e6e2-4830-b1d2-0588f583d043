<template>
  <view class="study_materials_conent">
    <!-- <view class="study_materials_tabs">
      <view class="tab_box">
        <view :class="currentComponent === 'LearningMaterials' ? 'tabs_check' : 'tabs_item'" @click="currentComponent = 'LearningMaterials'">学习资料</view>
        <view v-if="currentComponent === 'LearningMaterials'" class="tab_line"></view>
      </view>
      <view class="tab_box">
        <view :class="currentComponent === 'VideoMaterials' ? 'tabs_check' : 'tabs_item'" @click="currentComponent = 'VideoMaterials'">视频资料</view>
        <view v-if="currentComponent === 'VideoMaterials'" class="tab_line"></view>
      </view>
    </view> -->
    <DownloadProfile ref="downloadProfileRef" :onloadInfo="onloadInfo" />
  </view>
</template>

<script>
  // 先引入你的组件
  import DownloadProfile from './downloadProfile.vue';

  export default {
    data() {
      return {
        onloadInfo: {}
      };
    },
    components: {
      DownloadProfile
    },
    onLoad(e) {
      this.onloadInfo = e;
      console.log('🚀 ~ onLoad ~ onloadInfo:', this.onloadInfo);
    },
    onShow() {
      this.$refs.downloadProfileRef && this.$refs.downloadProfileRef.refreshData();
    }
  };
</script>

<style lang="scss" scoped>
  .study_materials_conent {
    box-sizing: border-box;
    // padding: 0 30rpx;
    .study_materials_tabs {
      position: relative;
      box-sizing: border-box;
      display: flex;
      justify-content: space-between;
      height: 106rpx;
      font-size: 26rpx;
      margin-bottom: 20rpx;
      // padding: 28rpx 163rpx 10rpx 163rpx;
      padding: 28rpx 202rpx 10rpx 163rpx;
      background-color: #fff;
    }
    .tab_box {
      position: relative;
    }
    .tabs_item {
      box-sizing: border-box;
      height: 42rpx;
      margin-top: 6rpx;
      line-height: 37rpx;
      vertical-align: top;
    }
    .tabs_check {
      height: 42rpx;
      font-weight: 600;
      font-size: 30rpx;
      color: #333333;
      line-height: 42rpx;
    }
    .tab_line {
      position: absolute;
      bottom: 0rpx;
      left: 35rpx;
      width: 50rpx;
      height: 10rpx;
      background: #3eaa8c;
      border-radius: 5rpx;
      text-align: center;
    }
  }
</style>
