<template>
  <view class="audio_container" :class="next ? 'audio_contain' : 'audio_contain1'">
    <!-- video标签 -->
    <video id="videoPlayer" ref="videoPlayer" :src="src" class="videoPlay" @ended="onended" @loadedmetadata="onCanplay" @timeupdate="onTimeUpdate" @error="errorFn"></video>
    <view>
      <slider
        :backgroundColor="backgroundColor"
        :activeColor="activeColor"
        @change="handleSliderChange"
        :value="sliderIndex"
        :max="maxSliderIndex"
        block-color="#343434"
        block-size="16"
      />
    </view>
    <view style="padding: 0rpx 15rpx 0rpx 15rpx; display: block">
      <view style="float: left; font-size: 20rpx; color: #848484">
        {{ currentTimeText }}
      </view>
      <view style="float: right; font-size: 20rpx; color: #848484">
        {{ totalTimeText }}
      </view>
    </view>
    <view>
      <view style="margin-top: 70rpx">
        <view class="uniGrid">
          <view class="uni-grid-icon" style="font-size: 24rpx; width: 64rpx" @tap="handleChageSpeed">
            <view>{{ playSpeed }}x</view>
            <view>倍速</view>
          </view>
          <view v-if="last" class="uni-grid-icon" @click="handleLast">
            <image src="https://document.dxznjy.com/course/791823450e3f4ce2bdfbf581bca1aed1.png" style="width: 24rpx; height: 28rpx; top: 6rpx; margin-bottom: 12rpx"></image>
            <view>上一题</view>
          </view>
          <view class="uni-grid-icon">
            <image
              @tap="handleChangeAudioState"
              v-show="!isPlaying"
              src="https://document.dxznjy.com/course/eb47fe46b019447ea155ac225151ad9b.png"
              style="width: 64rpx; height: 64rpx; top: 6rpx"
            ></image>
            <image
              @tap="handleChangeAudioState"
              v-show="isPlaying"
              src="https://document.dxznjy.com/course/48f45cf3734d464ea0b3fae9189dc148.png"
              style="width: 64rpx; height: 64rpx; top: 6rpx"
            ></image>
          </view>
          <view v-if="next" class="uni-grid-icon" @click="handleNext">
            <image src="https://document.dxznjy.com/course/8ff1bfcb339c44d19048d53a3a1c596c.png" style="width: 24rpx; height: 28rpx; top: 6rpx; margin-bottom: 12rpx"></image>
            <view>下一题</view>
          </view>
          <view v-if="original" style="font-size: 24rpx" class="uni-grid-icon" @click="handleTranslate">
            <view>A</view>
            <view>原文</view>
          </view>
          <view v-else class="uni-grid-icon" style="opacity: 0">
            <view>A</view>
            <view>原文</view>
          </view>
        </view>
        <slot name="footer"></slot>
      </view>
    </view>
  </view>
</template>
<script>
  export default {
    name: 'my-video',
    //audioPlay开始播放
    //audioPause停止播放
    //audioEnd音频自然播放结束事件
    //audioCanplay音频进入可以播放状态，但不保证后面可以流畅播放
    //change播放状态改变 返回值false停止播放 true开始播放
    emits: ['audioPlay', 'audioPause', 'audioEnd', 'audioCanplay', 'change'],
    props: {
      //标题文字
      title: {
        type: String,
        default: ''
      },
      //标题默认字体大小
      titleFontSize: {
        type: Number,
        default: 35
      },
      //标题文字颜色
      titleColor: {
        type: String,
        default: '#303030'
      },
      //标题背景色
      titleBackgroundColor: {
        type: String,
        default: 'white'
      },
      //标题是否滚动
      titleScroll: {
        type: Boolean,
        default: false
      },
      //标题滚动速度
      titleScrollSpeed: {
        type: Number,
        default: 100
      },

      subTitle: {
        type: String,
        default: ''
      },
      subTitleColor: {
        type: String,
        default: '#6C7996'
      },
      subTitleFontSize: {
        type: String,
        default: '30rpx'
      },
      original: {
        type: Boolean,
        default: false
      },
      autoplay: {
        type: Boolean,
        default: false
      },
      //滑块左侧已选择部分的线条颜色
      activeColor: {
        type: String,
        default: '#7C7C7C'
      },
      //滑块右侧背景条的颜色
      backgroundColor: {
        type: String,
        default: '#E5E5E5'
      },
      next: {
        type: Boolean,
        default: false
      },
      last: {
        type: Boolean,
        default: false
      },
      //音频地址
      src: {
        type: [String, Array],
        default: ''
      },
      totalTime: {
        type: String,
        default: 0
      },
      //是否倒计时
      isCountDown: {
        type: Boolean,
        default: false
      },

      //音乐封面
      audioCover: {
        type: String,
        default: ''
      },
      //是否显示收藏按钮
      isCollectBtn: {
        type: Boolean,
        default: false
      },
      //是否显示分享按钮
      isShareBtn: {
        type: Boolean,
        default: false
      }
    },
    data() {
      return {
        totalTimeText: '00:00', //视频总长度文字
        currentTimeText: '00:00:00', //视频已播放长度文字

        isPlaying: false, //播放状态

        sliderIndex: 0, //滑块当前值
        maxSliderIndex: 100, //滑块最大值

        IsReadyPlay: false, //是否已经准备好可以播放了

        isLoop: false, //是否循环播放

        speedValue: ['0.8', '1.0', '1.25', '1.5'],
        speedValueIndex: 1,
        playSpeed: '1.0', //播放倍速 可取值：0.5/0.8/1.0/1.25/1.5/2.0

        stringObject: (data) => {
          return typeof data;
        },
        innerAudioContext: null,
        // 后台音频控制器
        backAudio: null
      };
    },
    //   onReady() {

    //     // 获取 video 对象

    //   },
    mounted() {
      this.innerAudioContext = uni.createVideoContext('videoPlayer', this);
      console.log('this.innerAudioContext123333', this.innerAudioContext);
      console.log('this.2222222222222222', this.totalTime);
      this.totalTimeText = this.getFormateTime(this.totalTime);
      this.innerAudioContext.src = this.src;
    },
    onLoad() {
      this.innerAudioContext.src = this.src;

      // this.innerAudioContext.src = 'https://dx-applet-mall.oss-cn-shanghai.aliyuncs.com/videos/1735114196000.mp3';
    },

    methods: {
      handleLast() {
        this.$emit('last');
      },
      handleNext() {
        this.$emit('next');
      },
      handleTranslate() {
        this.$emit('translate');
      },
      //音频进入可以播放状态，但不保证后面可以流畅播放
      onCanplay(e) {
        this.IsReadyPlay = true;
        let duration = e.detail.duration;

        this.$emit('audioCanplay');
        this.totalTimeText = this.getFormateTime(duration);
        console.log('分钟', this.totalTimeText);
        this.maxSliderIndex = parseFloat(duration).toFixed(2);
      },
      // 音频播放事件处理函数 (持续触发) - 获取当前播放时长
      onTimeUpdate(e) {
        this.sliderIndex = parseFloat((e.detail.currentTime / this.totalTime) * 100).toFixed(2);
        this.currentTimeText = this.getFormateTime(e.detail.currentTime);

        //如果已经播放到结尾,回到音频开始
        if (this.sliderIndex == this.maxSliderIndex) {
          let prevState = this.isPlaying;
          this.sliderIndex = 0;
          this.changePlayProgress(0);
          this.audioPause();

          //如果开启循环播放，且结束前还是播放状态
          if (this.isLoop && prevState) {
            this.audioPlay();
          }
        }
      },

      //销毁innerAudioContext()实例
      audioDestroy() {
        if (this.innerAudioContext) {
          this.innerAudioContext = null;
          this.isPlaying = false;
        }
      },
      //点击变更播放状态
      handleChangeAudioState() {
        console.log(this.isPlaying, 'this.isPlaying点击播放');
        console.log(this.isPlaying && !this.innerAudioContext.paused, 'this.1111111111111111111111111111111111');
        if (this.isPlaying && !this.innerAudioContext.paused) {
          this.audioPause();
        } else {
          this.audioPlay();
        }
      },
      //开始播放
      audioPlay() {
        console.log('this.isPlaying开始播放');
        try {
          console.log('放', this.innerAudioContext);
          this.innerAudioContext.play();
          this.isPlaying = true;
          this.$emit('audioPlay');

          this.$emit('change', {
            state: true
          });
        } catch (e) {
          console.log(e);
          //TODO handle the exception
        }
      },
      //播放结束
      onended() {
        this.isPlaying = false;
        this.innerAudioContext.seek(0);
        this.innerAudioContext.pause();
      },
      //暂停播放
      audioPause() {
        this.innerAudioContext.pause();
        this.isPlaying = false;

        this.$emit('audioPause');
        this.$emit('change', {
          state: false
        });
      },
      //变更滑块位置
      handleSliderChange(e) {
        this.changePlayProgress(e.detail ? e.detail.value : e);
      },
      //更改播放倍速
      handleChageSpeed() {
        let paly = this.isPlaying;
        //获取播放倍速列表长度
        let speedCount = this.speedValue.length;
        //如果当前是最大倍速，从-1开始
        if (this.speedValueIndex == speedCount - 1) {
          this.speedValueIndex = -1;
        }
        //最新倍速序号
        this.speedValueIndex += 1;
        //获取最新倍速文字

        this.playSpeed = this.speedValue[this.speedValueIndex];
        //暂停播放
        this.audioPause();
        //变更播放倍速
        this.innerAudioContext.playbackRate(this.speedValue[this.speedValueIndex] - 0);
        //开始播放
        if (paly) {
          this.audioPlay();
        }
      },
      //快退15秒
      handleFastRewind() {
        if (this.IsReadyPlay) {
          let value = parseInt(this.sliderIndex) - 15;
          this.changePlayProgress(value >= 0 ? value : 0);
        }
      },
      //快进15秒
      handleFastForward() {
        if (this.IsReadyPlay) {
          let value = parseInt(this.sliderIndex) + 15;
          this.changePlayProgress(value <= this.maxSliderIndex ? value : this.maxSliderIndex);
        }
      },
      //开启循环播放
      handleLoopPlay() {
        this.isLoop = !this.isLoop;
        if (this.isLoop) {
          uni.showToast({
            title: '已开启循环播放',
            duration: 1000
          });
        } else {
          uni.showToast({
            title: '取消循环播放',
            duration: 1000
          });
        }
      },
      //更改播放进度
      changePlayProgress(value) {
        let that = this;
        let val = parseInt((this.totalTime * value) / 100);
        setTimeout(() => {
          // console.log(that.innerAudioContext);
          // that.innerAudioContext.pause();
          that.innerAudioContext.seek(val);
          // that.innerAudioContext.play();
        }, 200);

        this.sliderIndex = value;
        this.currentTimeText = this.getFormateTime(val);
      },
      //秒转换为00:00:00
      getFormateTime(time) {
        let ms = time * 1000; // 1485000毫秒
        let date = new Date(ms);

        // 注意这里是使用的getUTCHours()方法，转换成UTC(协调世界时)时间的小时
        let hour = date.getUTCHours();
        // let hour = date.getHours(); 如果直接使用getHours()方法，则得到的时分秒格式会多出来8个小时（在国内开发基本都是使用的是东八区时间），getHours()方法会把当前的时区给加上。
        let minute = date.getMinutes();
        let second = date.getSeconds();

        let formatTime = `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}:${second.toString().padStart(2, '0')}`;

        return formatTime;
      },
      handleCollec() {
        this.$emit('audioCollec');
      },
      handleShare() {
        this.$emit('audioShare');
      },
      errorFn() {
        this.$emit('change', {
          state: false
        });
        console.log(error);
        this.audioPause();
        this.$emit('error');
      }
    },
    onUnload() {
      this.audioDestroy();
    },
    onHide() {
      this.audioDestroy();
    },
    beforeDestroy() {
      this.audioDestroy();
    }
  };
</script>

<style lang="scss" scoped>
  .videoPlay {
    position: fixed;
    top: -999px;
    left: 0;
    width: 300rpx;
    height: 300rpx;
  }
  .uniGrid {
    display: flex;
    justify-content: space-evenly;
  }
  .audio_contain {
    position: fixed;
    left: 0;
    width: 750rpx;
    height: 340rpx;
    bottom: 0;
  }
  .audio_contain1 {
    position: fixed;
    z-index: 1000;
    width: 750rpx;
    height: 280rpx;
    left: 0;
    bottom: 0;
  }
  .audio_container {
    background-color: #fff;
    box-sizing: border-box;
    // box-shadow: 0 0 10rpx #c3c3c3;
    padding: 15rpx 15rpx 40rpx 15rpx;

    .audio-title {
      font-size: 28rpx;
    }

    .uni-noticebar {
      padding: 0px;
    }

    .audio-subTitle {
      width: 100%;
      text-align: left;
      font-size: 40rpx;
      color: blue;
    }

    .speed-text {
      position: absolute;
      top: 0rpx;
      left: 30rpx;
      right: 0;
      color: #475266;
      font-size: 16rpx;
      font-weight: 600;
    }

    .uni-grid-icon {
      position: relative;
      text-align: center;
      color: #939393;
      font-size: 20rpx;
    }
  }
</style>
