<template>
  <page-meta :page-style="'overflow:' + (isShowDialog ? 'hidden' : 'visible')"></page-meta>
  <view>
    <view class="meet-title w100">
      <view class="positionAbsolute back" @click="gotoBack">
        <uni-icons type="back" size="25"></uni-icons>
      </view>
      <view class="t-c f-32" style="margin: 110rpx 0 30rpx">会议中心</view>
    </view>
    <view v-if="!courseId" style="margin-top: 200rpx">
      <view v-if="infoLists.list && infoLists.list.length > 0">
        <view class="courseList plr-30">
          <mettingWaterfall :value="infoLists.list" id-key="courseId">
            <template v-slot:left="{ leftList }">
              <view v-for="(item, index) in leftList" :key="item.courseId" class="courseItemWrapper">
                <view class="courseItem radius-20 pb-10 positionRelative" @tap.stop="reLaunchTo('/meeting/meetIndex?id=' + item.courseId, item.courseId)">
                  <view class="courseimg relative">
                    <image :src="item.courseImage" class="wh100" mode="widthFix"></image>
                  </view>
                  <view class="plr-30 mtb-20">
                    <view class="bold f-30">{{ item.courseName }}</view>
                    <view class="color_red font12 mtb-15 displayflex displayflexbetween">
                      <view>
                        会员价
                        <span class="bold f-34">￥{{ item.memberPrice }}</span>
                      </view>
                    </view>
                    <view class="displayflex color_grey f-24" style="justify-content: space-between">
                      <view>
                        原价
                        <text style="text-decoration: line-through">￥{{ item.originalPrice }}</text>
                      </view>
                      <view>{{ item.studyNumber }}+人付款</view>
                    </view>
                  </view>
                </view>
              </view>
            </template>

            <template v-slot:right="{ rightList }">
              <view v-for="(item, index) in rightList" :key="item.courseId" class="courseItemWrapper">
                <view class="courseItem radius-20 pb-10 positionRelative" @tap.stop="reLaunchTo('/meeting/meetIndex?id=' + item.courseId, item.courseId)">
                  <view class="courseimg relative">
                    <image :src="item.courseImage" class="wh100" mode="widthFix"></image>
                  </view>
                  <view class="plr-30 mtb-20">
                    <view class="bold f-30">{{ item.courseName }}</view>
                    <view class="color_red font12 mtb-15 displayflex displayflexbetween">
                      <view>
                        会员价
                        <span class="bold f-34">￥{{ item.memberPrice }}</span>
                      </view>
                    </view>
                    <view class="displayflex color_grey f-24" style="justify-content: space-between">
                      <view>
                        原价
                        <text style="text-decoration: line-through">￥{{ item.originalPrice }}</text>
                      </view>
                      <view>{{ item.studyNumber }}+人付款</view>
                    </view>
                  </view>
                </view>
              </view>
            </template>
          </mettingWaterfall>
          <view v-if="no_more && infoLists.list.length > 0" style="width: 100%; text-align: center">
            <u-divider text="到底了"></u-divider>
          </view>
        </view>
      </view>

      <view v-if="JSON.stringify(infoLists) == '{}'" class="loading-container">
        <view class="loading-spinner"></view>
      </view>

      <view v-if="infoLists.list && infoLists.list.length == 0" class="t-c flex-col" :style="{ height: useHeight + 'rpx' }">
        <image :src="imgHost + 'alading/correcting/no_data.png'" class="mb-20 img_s" mode="widthFix"></image>
        <view style="color: #bdbdbd">暂无会议数据</view>
      </view>
    </view>

    <view v-else style="margin-top: 70px">
      <view class="col-12">
        <view style="position: relative">
          <!-- <image style="position: absolute;" :src="imgHost+'dxSelect/fourthEdition/meeting_new-bgc.png'" class="header-img"></image> -->
          <image class="header-img-child" :src="getMeetBanner()"></image>
          <!-- <image class="header-o-img-child" :src="imgHost+'dxSelect/fourthEdition/meeting_o-bgc.png'"></image> -->
        </view>
        <!-- <view class="page_title bold c-00">会议中心</view> -->
      </view>
      <view class="list-content">
        <view class="flex-s meeting-content-border" @click="changeMeeting">
          <view class="meeting-name-bold">
            {{ shopdetail ? shopdetail.courseName : '请选择会议' }}
          </view>
          <image class="w44" :src="imgHost + 'dxSelect/fourthEdition/meeting_icon_change.png'"></image>
        </view>
        <view class="f-26 c-66 meeting-list-bg" :style="{ paddingBottom: useHeight - 1232 + 'rpx' }">
          <view class="templateItem">
            <view class="template" @click="skintap('pages/index/courseDetail?sid=' + shopdetail.courseId)">
              <image :src="imgHost + 'dxSelect/fourthEdition/meet_introduce.png'" class="w44"></image>
              <view>大会介绍</view>
            </view>
            <view class="template" @click="skintap('meeting/meetH5?title=大会日程&id=' + shopdetail.courseId)">
              <image :src="imgHost + 'dxSelect/fourthEdition/meet_time.png'" class="w44"></image>
              <view>大会日程</view>
            </view>
            <view class="template" @click="skintap('meeting/meetH5?title=专题报告&id=' + shopdetail.courseId)">
              <image :src="imgHost + 'dxSelect/fourthEdition/meet_report.png'" class="w44"></image>
              <view>专题报告</view>
            </view>
          </view>
          <view class="templateItem">
            <view class="template" @tap="skintap('meeting/meetWeb?id=' + shopdetail.courseId)">
              <image :src="imgHost + 'dxSelect/fourthEdition/meet_pic.png'" class="w44"></image>
              <view>照片直播</view>
            </view>
            <view class="template" @tap="skintap('meeting/meetH5?title=酒店详情&id=' + shopdetail.courseId)">
              <image :src="imgHost + 'dxSelect/fourthEdition/meet_hotle.png'" class="w44"></image>
              <view>酒店详情</view>
            </view>
            <view class="template" @tap="skintap('meeting/meetH5?title=合作伙伴&id=' + shopdetail.courseId)">
              <image :src="imgHost + 'dxSelect/fourthEdition/meet_friend.png'" class="w44"></image>
              <view>合作伙伴</view>
            </view>
          </view>
          <view class="templateItem">
            <view class="template" @tap="btnOption(false)" v-if="isWaitVerify">
              <view class="w50" style="width: 100%; display: flex; justify-content: center; align-items: center">
                <image :src="imgHost + 'dxSelect/fourthEdition/meeting_icon_code.png'" class="w38"></image>
              </view>
              <view>我的核销码</view>
            </view>
            <view class="template" @tap="scanCode()" v-if="userinfo && userinfo.checkUser == 1">
              <view class="w50" style="width: 100%; display: flex; justify-content: center; align-items: center">
                <image :src="imgHost + 'dxSelect/fourthEdition/meeting_icon_saoma.png'" class="w38"></image>
              </view>
              <view>扫码验券</view>
            </view>
            <view class="template" @tap="navigationToPage('meeting/meetingList?isNoTitle=false')">
              <image :src="imgHost + 'dxSelect/fourthEdition/meet_more.png'" class="w44"></image>
              <view>更多会议</view>
            </view>
            <view class="template" v-if="!isWaitVerify || !(userinfo && userinfo.checkUser == 1)" style="opacity: 0">
              <image class="w44"></image>
              <view>占位占位</view>
            </view>
          </view>
        </view>
        <!-- 底部栏 -->
        <view class="fixed_b flex">
          <!-- <button class="pl-30 pr-20 flex-c" open-type="contact" hover-class="none">
            <image :src="imgHost + 'dxSelect/tab_kf.png'" class="w42" mode=""></image>
            <text class="f-24 c-99 ml-10">客服</text>
          </button> -->
          <!-- #ifdef MP-WEIXIN -->
          <navigator url="plugin://qiyukf/chat" class="pl-30 pr-20 flex-c" hover-class="none">
            <image :src="imgHost + 'dxSelect/tab_kf.png'" class="w42" mode=""></image>
            <text class="f-24 c-99 ml-10">客服</text>
          </navigator>
          <button class="pr-20 flex-c" hover-class="none" @click="posterShare">
            <image :src="imgHost + 'dxSelect/fourthEdition/meeting_share_poster.png'" class="w42" mode=""></image>
            <text class="f-24 c-99 ml-10">海报分享</text>
          </button>
          <button class="pr-20 flex-c" hover-class="none" open-type="share">
            <image :src="imgHost + 'dxSelect/fourthEdition/meeting_share_link.png'" class="w42" mode=""></image>
            <text class="f-24 c-99 ml-10">链接分享</text>
          </button>

          <!-- #endif -->
          <!-- #ifdef APP-PLUS -->
          <navigator class="pl-30 pr-20 flex-c" hover-class="none" @click="kfAppHandle">
            <image :src="imgHost + 'dxSelect/tab_kf.png'" class="w42" mode=""></image>
            <text class="f-24 c-99 ml-10">客服</text>
          </navigator>
          <button class="pr-20 flex-c" hover-class="none" @click="linkShareApp(true)">
            <image :src="imgHost + 'dxSelect/fourthEdition/meeting_share_link.png'" class="w42" mode=""></image>
            <text class="f-24 c-99 ml-10">复制链接</text>
          </button>
          <button class="pr-20 flex-c" hover-class="none" @click="linkShareApp(false)">
            <image :src="imgHost + 'dxSelect/weiimg.png'" class="w42" mode=""></image>
            <text class="f-24 c-99 ml-10">微信分享</text>
          </button>
          <!-- #endif -->
          <view class="mr-30 buy_btn" @tap="btnOption(true)">立即购买</view>
        </view>
      </view>
    </view>

    <uni-popup ref="meetListPopup" type="center" @change="changeShow">
      <view class="dialogBG">
        <view class="reviewCard_box positionRelative">
          <view class="cartoom_image">
            <image src="https://document.dxznjy.com/dxSelect/dialog_icon.png" mode="widthFix"></image>
          </view>
          <view class="review_close" @click="closeChoseMeeting">
            <uni-icons type="clear" size="28" color="#B1B1B1"></uni-icons>
          </view>
          <view class="reviewCard">
            <view class="reviewTitle bold pb-30">请选择大会</view>
            <scroll-view :scroll-top="scrollTop" scroll-y="true" style="height: 440rpx">
              <view class="dialogContent" v-for="(item, index) in meetingList.list" :class="meetCurrent == index ? 'selected' : 'not-selected'" @click="chooseMeeting(item, index)">
                <text style="display: inline-block; width: 400rpx; white-space: nowrap; overflow: hidden; text-overflow: ellipsis">
                  {{ item.courseName }}
                </text>
              </view>
            </scroll-view>
            <view style="margin: 40rpx auto 0 auto; display: flex; justify-content: center">
              <view class="sure_btn" @click="confirmMeeting()">确定</view>
            </view>
          </view>
        </view>
      </view>
    </uni-popup>

    <!-- 体验课 -->
    <uni-popup ref="experiencePopup" type="bottom" @change="changeShow">
      <view class="content-popup plr-30" v-if="shopdetail">
        <view class="icon-clear" @click="closeDialog">
          <uni-icons type="clear" size="30" color="#B1B1B1"></uni-icons>
        </view>
        <view class="">
          <view class="flex-a-c">
            <view class="box-180 radius-15" v-if="shopdetail.bannerImages">
              <image :src="shopdetail.bannerImages[0]" class="wh100"></image>
            </view>
            <view class="flex-box ml-20 mt-10">
              <view class="f-32 bold mb-20">{{ shopdetail.courseName }}</view>
              <view class="flex-a-c">
                <view class="f-26 color_tangerine">
                  ￥
                  <text class="bold f-30 c-fea">{{ shopdetail.originalPrice }}</text>
                </view>
                <view class="amount">实付款：{{ cartTotalPrice ? cartTotalPrice : shopdetail.memberPrice }}</view>
              </view>
            </view>
          </view>

          <view class="f-30 mt-20 flex-s bg-ff radius-15 p-30">
            <view class="">数量</view>
            <uni-number-box :min="1" :max="500" v-model="value" @change="changeValue" />
          </view>
        </view>

        <view class="">
          <view class="plr-30 pb-30 bg-ff radius-15 mtb-20">
            <view class="ptb-20 f-32 bold">订单备注</view>
            <view class="p-30 bg-f7">
              <textarea placeholder="若购买多个，请备注所有参会人真实姓名" placeholder-style="color:#999" @input="inputtext" class="remark f-30"></textarea>
            </view>
          </view>

          <view class="bg-ff f-30 radius-15 plr-30">
            <form id="#nform">
              <view class="information">
                <view style="width: 260rpx">
                  <span class="redtext">*</span>
                  参会人姓名：
                </view>
                <view class="phone-input">
                  <input
                    type="text"
                    :adjust-position="true"
                    v-model="trialclassStudent"
                    name="trialname"
                    placeholder="请输入真实的参会人姓名"
                    class="input"
                    style="color: #000 !important; font-size: 13px !important; width: 174px !important; -webkit-appearance: none !important; appearance: none !important"
                  />
                </view>
              </view>
              <view class="information">
                <view style="width: 260rpx">
                  <span class="redtext">*</span>
                  联系方式：
                </view>
                <view class="phone-input">
                  <input
                    type="number"
                    :adjust-position="true"
                    v-model="parentMobile"
                    name="number"
                    :focus="parentFocuse"
                    placeholder="请输入联系方式"
                    class="input"
                    maxlength="11"
                    style="color: #000 !important; font-size: 13px !important; width: 174px !important; -webkit-appearance: none !important; appearance: none !important"
                  />
                </view>
                <view v-if="parentMobile" @click="changeFouce('parentFocuse')">
                  <image src="https://document.dxznjy.com/dxSelect/image/edit_icon.png" style="width: 30upx; height: 30upx"></image>
                </view>
              </view>
              <view class="information">
                <view style="width: 260rpx">
                  <span class="redtext">*</span>
                  推荐人姓名：
                </view>
                <view class="phone-input">
                  <input
                    type="text"
                    v-model="recommendInfo.name"
                    name="trialname"
                    :focus="referrerFocuse"
                    placeholder="请输入真实的推荐人姓名"
                    class="input"
                    style="color: #000 !important; font-size: 13px !important; width: 174px !important; -webkit-appearance: none !important; appearance: none !important"
                  />
                </view>
                <view v-if="recommendInfo.name" @click="changeFouce('referrerFocuse')">
                  <image src="https://document.dxznjy.com/dxSelect/image/edit_icon.png" style="width: 30upx; height: 30upx"></image>
                </view>
              </view>
              <view class="information">
                <view style="width: 260rpx">
                  <span class="redtext">*</span>
                  推荐人电话：
                </view>
                <view class="phone-input">
                  <input
                    type="text"
                    v-model="recommendInfo.mobile"
                    :focus="referrerMobileFocuse"
                    placeholder="请输入真实的推荐人号码"
                    class="input"
                    style="color: #000 !important; font-size: 13px !important; width: 174px !important; -webkit-appearance: none !important"
                  />
                </view>
                <view v-if="recommendInfo.mobile" @click="changeFouce('referrerMobileFocuse')">
                  <image src="https://document.dxznjy.com/dxSelect/image/edit_icon.png" style="width: 30upx; height: 30upx"></image>
                </view>
              </view>

              <view class="font14 tipText pb-30">
                <image src="https://document.dxznjy.com/dxSelect/three/icon/prompt-icon.png" style="width: 30upx; height: 30upx"></image>
                <text class="ml-20 c-66">推荐人没有填无</text>
              </view>

              <view class="font12 pb-30" v-if="shopdetail.qrCodeImage">
                <text class="c-66">支付成功后记得先点击返回按钮，添加二维码哦，不要直接退出，否则会影响你的权益呢</text>
              </view>
            </form>
          </view>
          <view class="plr-30 flex mt-40">
            <view class="paybtn f-32 c-ff" @click="coursePay">
              立即支付
              <text class="ml-10">￥{{ cartTotalPrice ? cartTotalPrice : shopdetail.memberPrice }}</text>
            </view>
          </view>
        </view>
      </view>
    </uni-popup>

    <u-toast ref="uToast"></u-toast>
  </view>
</template>

<script>
  const { httpUser } = require('@/util/luch-request/indexUser.js');
  const { $navigationTo, $showMsg, $http } = require('@/util/methods.js');
  import Util from '@/util/util.js';
  import Config from '@/util/config.js';
  import mettingWaterfall from '../components/metting-waterfall/metting-waterfall.vue';
  export default {
    components: {
      mettingWaterfall
    },
    data() {
      return {
        payInfo: {},
        flag1: false,
        imgHost: getApp().globalData.imgsomeHost,
        screenHeight: '', // 屏幕高度
        useHeight: '', // 屏幕高度

        scrollTop: 0,

        meetingList: [],
        meetCurrent: -1,

        shopdetail: null,

        meetingOrderNo: '',
        isWaitVerify: false,

        courseId: '',
        sceneId: '',

        flag: false,
        disabled: false,

        shareId: '', //分享id
        invitationInfo: {}, // 邀请信息

        recommendInfo: {
          //推荐人信息
          mobile: '无',
          name: ''
        },
        value: 1,
        buyNum: 1, // 课程购买数量
        trialclassStudent: '', //试课学员姓名
        parentMobile: '',
        addressInfo: {
          address: '',
          buyerName: '',
          buyerPhone: '',
          addressId: ''
        },
        remark: '',
        cartTotalPrice: 0,

        parentFocuse: false,
        referrerFocuse: false,
        referrerMobileFocuse: false,

        userinfo: {},
        params: {
          type: 'default',
          message: '支付成功',
          duration: 3000,
          url: ''
        },
        shareType: 1,

        infoLists: {},
        page: 1,
        no_more: false,
        closePage: true,
        isShowDialog: false,
        activityInfo: {},
        app: 0,
        removeListener: null,
        isJumpNext: false,
        isOnLoadId: false
      };
    },

    //链接分享有id
    //海报分享无id
    //普通跳转无id

    async onLoad(e) {
      this.closePage = true;
      if (e.token) {
        this.app = e.app;
        this.$handleTokenFormNative(e);
        // #ifdef APP-PLUS
        this.removeListener = uni.$addAppPayGlobalEvtListener(this.sucees, this.fail, this.payInfo.orderId, this.flag1);
        // #endif
      }
      await this.getAllMeeting();
      console.log(e, '会议中心');
      if (e.id) {
        this.isOnLoadId = true;
        //链接分享
        this.courseId = e.id;
        this.sceneId = e.scene;
        let data = {
          userId: this.sceneId,
          invitationCode: '',
          secCode: ''
        };
        uni.setStorageSync('invitationInfo', data);
      } else {
        this.sceneId = uni.getStorageSync('user_id');
        this.courseId = uni.getStorageSync('course_id');
        if (this.courseId) {
          //海报分享
          let data = {
            userId: this.sceneId,
            invitationCode: '',
            secCode: ''
          };
          uni.setStorageSync('invitationInfo', data);
        } else {
          this.clickHandle();
          this.course();
          //普通跳转 old
          // this.courseId = this.getNowShowMeeting();
          // this.sceneId = e.scene;
        }
      }

      uni.removeStorageSync('course_id');

      // 活动邀请
      if (e.activityId && e.shareUserCode) {
        this.activityInfo = {
          activityId: e.activityId,
          shareUserCode: e.shareUserCode
        };
      }

      await this.getInvitation();
      if (this.shareId != undefined && this.shareId != '') {
        this.getMobile();
      }
      if (Config.curUseApp == 'zx') {
        this.params.url = '/splitContent/order/order';
      } else {
        this.params.url = '/splitContent/order/helpOrder?meeting=1&app=2';
      }
      this.getMeetingIsBuy();
      this.getMeetingDetails();
    },

    onReady() {
      this.getHeight();
      uni.getSystemInfo({
        success: (res) => {
          // 可使用窗口高度，将px转换rpx
          let h = res.windowHeight * (750 / res.windowWidth);
          this.useHeight = h;
        }
      });
    },
    beforeDestroy() {
      // #ifdef APP-PLUS
      if (this.removeListener) {
        this.removeListener();
      }
      // #endif
    },
    onShow() {
      this.closePage = true;
      if (this.flag1) {
        // #ifdef MP-WEIXIN
        uni.$tlpayResult(this.sucees, this.fail, this.payInfo.orderId);
        // #endif
      }
      let token = uni.getStorageSync('token');
      if (token) {
        this.getMeetingIsBuy();
        this.userData();
      } else {
        //  console.log('会议中心---onshow--login')
      }
    },

    watch: {
      trialclassStudent(value) {
        let _this = this;
        if (value.length > 4) {
          value = value.slice(0, 5);
          uni.showToast({
            title: '学员姓名不可超过五个汉字',
            icon: 'none'
          });
        }
        _this.$nextTick(function () {
          _this.trialclassStudent = value;
        });
      },

      recommendInfoName(value) {
        let _this = this;
        if (value.length > 4) {
          value = value.slice(0, 5);
          uni.showToast({
            title: '推荐人姓名不可超过五个汉字',
            icon: 'none'
          });
        }
        _this.$nextTick(function () {
          _this.recommendInfo.name = value;
        });
      }
    },

    onShareAppMessage(res) {
      return {
        title: '叮，你的好友敲了你一下，赶紧过来看看',
        imageUrl: this.shopdetail ? (this.shopdetail.shareCardUrl ? this.shopdetail.shareCardUrl : this.shopdetail.bannerImages[0]) : '',
        path: `/meeting/meetIndex?scene=${uni.getStorageSync('user_id')}&type=${this.shareType}&id=${this.shopdetail ? this.shopdetail.courseId : ''}`
      };
    },

    onUnload() {
      // #ifdef APP-PLUS
      if (this.app && !this.isJumpNext) {
        plus.runtime.quit();
      }
      // #endif
    },
    onHide() {
      this.closePage = false;
    },
    methods: {
      gotoBack() {
        if (this.courseId == '' || this.isOnLoadId) {
          uni.navigateBack();
        } else {
          this.courseId = '';
        }
      },
      sucees() {
        this.flag1 = false;
        this.handleSavePayRecord();
        this.redirectToOrderIndex();
      },
      fail() {
        this.flag1 = false;
      },
      fails() {
        uni.showToast({
          title: '支付失败',
          icon: 'none',
          duration: 2000
        });
        this.flag1 = false;
      },
      changeShow(e) {
        //  console.log(e);
        this.isShowDialog = e.show;
      },
      getMeetBanner() {
        if (this.shopdetail && this.shopdetail.bannerImages != null && this.shopdetail.bannerImages.length != 0) {
          return this.shopdetail.bannerImages[0];
        }
      },
      getHeight() {
        // 获取系统信息
        const systemInfo = uni.getSystemInfoSync();
        // 获取屏幕高度
        this.screenHeight = systemInfo.windowHeight;
        this.screenWidth = systemInfo.windowWidth - 60;
      },

      clickHandle() {
        this.scrollTop = this.scrollTop === 0 ? 1 : 0;
      },
      scrolltolower() {
        //  console.log(this.page , this.infoLists.totalPage);
        if (this.page >= this.infoLists.totalPage) {
          this.no_more = true;
          return false;
        }
        this.course(true, ++this.page);
      },

      // 点击修改按钮
      changeFouce(ele) {
        this[ele] = true;
      },

      async userData() {
        let _this = this;
        const res = await $http({
          url: 'zx/user/userInfoNew'
        });
        if (res) {
          _this.userinfo = res.data;
          _this.parentMobile = res.data.mobile;
        }
      },

      //获取所有会议 curChoseMeet
      async getAllMeeting(isPage, page) {
        page = page || 1;
        let _this = this;
        const res = await $http({
          url: 'zx/course/courseList',
          data: {
            indexShow: 0,
            cityCode: '',
            cateId: '',
            page: page,
            pageSize: 30,
            cateType: 2
          }
        });
        if (res) {
          if (isPage) {
            let old = _this.meetingList.list;
            _this.meetingList.list = [...old, ...res.data.list];
          } else {
            _this.meetingList = res.data;
          }
          // if (page < _this.meetingList.totalPage) {
          //   _this.getAllMeeting(true, ++page);
          // }
        }
      },

      async getMeetingDetails() {
        let _this = this;
        if (_this.courseId) {
          const res = await $http({
            url: 'zx/course/courseDetail',
            data: {
              courseId: _this.courseId
            }
          });
          if (res) {
            _this.shopdetail = res.data;
          }
        }
      },

      //是否购买
      async getMeetingIsBuy() {
        let token = uni.getStorageSync('token');
        if (token) {
          let _this = this;
          if (_this.courseId) {
            const res = await $http({
              url: 'zx/order/selIsBuyMeetingByUser',
              data: {
                courseId: _this.courseId
              }
            });
            if (res && res.data) {
              _this.meetingOrderNo = res.data;
              _this.isWaitVerify = true;
            } else {
              _this.isWaitVerify = false;
            }
          }
        }
      },

      getNowShowMeeting() {
        const currentTime = new Date();
        let closestTime;
        let closeMeeting;
        for (let i = 0; i < this.meetingList.list.length; i++) {
          if (!this.meetingList.list[i].endBuyTime) {
            continue;
          }
          const time = new Date(this.meetingList.list[i].endBuyTime);
          if (time > currentTime) {
            // 检查是否时间大于当前时间
            // 如果最接近时间还未设置，或者当前时间与已保存的时间更接近
            if (!closestTime || time - currentTime < closestTime - currentTime) {
              closestTime = time;
              closeMeeting = this.meetingList.list[i];
            }
          }
        }
        if (!closeMeeting) {
          for (let i = 0; i < this.meetingList.list.length; i++) {
            if (this.meetingList.list[i].endBuyTime == '') {
              closeMeeting = this.meetingList.list[i];
              break;
            }
          }
        }
        if (!closeMeeting && this.meetingList.list.length > 0) {
          closeMeeting = this.meetingList.list[0];
        }
        return closeMeeting ? closeMeeting.courseId : '';
      },

      changeMeeting() {
        // if (!uni.getStorageSync('token')) {
        //   $navigationTo('Personalcenter/login/login');
        //   return;
        // }
        this.$refs.meetListPopup.open();
      },
      closeChoseMeeting() {
        this.$refs.meetListPopup.close();
      },
      confirmMeeting() {
        if (this.meetCurrent == -1) {
          uni.showToast({
            title: '请选择会议',
            icon: 'none'
          });
          return;
        }
        this.courseId = this.meetingList.list[this.meetCurrent].courseId;
        this.getMeetingIsBuy();
        this.getMeetingDetails();
        this.recommendInfo = {
          mobile: '无',
          name: ''
        };
        this.shareId = '';
        this.trialclassStudent = '';
        this.$refs.meetListPopup.close();
      },
      chooseMeeting(item, index) {
        this.meetCurrent = index;
      },

      linkShareApp(isCopy) {
        var image = this.shopdetail.bannerImages.length > 0 ? this.shopdetail.bannerImages[0] : 0;
        let shareInfo = {
          title: '叮，你的好友敲了你一下，赶紧过来看看',
          imageUrl: this.shopdetail ? (this.shopdetail.shareCardUrl ? this.shopdetail.shareCardUrl : image) : '',
          path: `/meeting/meetIndex?scene=${uni.getStorageSync('user_id')}&type=${this.shareType}&id=${this.shopdetail ? this.shopdetail.courseId : ''}`
        };
        if (isCopy) {
          this.$copyFunc(shareInfo.path);
        } else {
          uni.$appShare(shareInfo, 2);
          plus.runtime.quit();
        }
      },
      // 点击海报
      posterShare() {
        uni.navigateTo({
          url: `/splitContent/poster/index?type=${this.shareType}&id=${this.courseId}&isMeeting=true`
        });
      },

      btnOption(isBuy) {
        if (!uni.getStorageSync('token')) {
          $navigationTo('Personalcenter/login/login');
          return;
        }
        if (!isBuy) {
          this.getVerifyList();
          // this.skintap('meeting/myVerifyList?courseId=' + this.courseId);
        } else {
          if (!this.shopdetail) {
            uni.showToast({
              icon: 'none',
              title: '请选择会议'
            });
            return;
          }
          //立即购买
          this.gotoBuy();
        }
      },

      //产品要求一个的时候直接进入待核销界面，多个进入核销列表
      async getVerifyList() {
        let _this = this;
        const res = await $http({
          url: 'zx/order/selMeetingQRList',
          data: {
            courseId: _this.courseId
          }
        });
        //  console.log(res.data);
        if (res && res.data) {
          if (res.data.length == 1) {
            if (this.closePage) {
              _this.skintap('meeting/meetVerifyInfo?isType=0&meetingQrId=' + res.data[0].meetingQrId + '&courseId=' + _this.courseId);
            }
          } else {
            if (this.closePage) {
              _this.skintap('meeting/myVerifyList?courseId=' + _this.courseId);
            }
          }
        }
      },
      inputtext(e) {
        this.remark = e.detail.value;
      },

      gotoBuy() {
        this.$refs.experiencePopup.open();
      },
      closeDialog() {
        this.$refs.experiencePopup.close();
        this.value = 1;
        let result = [
          {
            buyNum: Number(this.value),
            courseId: this.courseId
          }
        ];
        this.updateTotalPrice(result);
      },
      async changeValue(e) {
        let _this = this;
        _this.buyNum = e;
        let result = [
          {
            buyNum: Number(_this.buyNum),
            courseId: this.courseId
          }
        ];
        this.updateTotalPrice(result);
      },

      // 更新价格
      async updateTotalPrice(result) {
        let _this = this,
          arr = [];
        const res = await $http({
          url: 'zx/course/calculatePrice',
          method: 'post',
          data: {
            courseAndNumDto: result,
            currencyNumber: 0
          }
        });
        if (res) {
          _this.cartTotalPrice = res.data.payPrice;
        }
      },

      skintap(url) {
        if (!uni.getStorageSync('token')) {
          $navigationTo('Personalcenter/login/login');
        } else {
          if (!this.courseId) {
            uni.showToast({
              title: '请选择会议',
              icon: 'none'
            });
            return;
          }
          $navigationTo(url);
        }
      },
      navigationToPage(url) {
        if (!uni.getStorageSync('token')) {
          $navigationTo('Personalcenter/login/login');
        } else {
          $navigationTo(url);
        }
      },

      reLaunchTo(url, courseId) {
        // if (!uni.getStorageSync('token')) {
        // 	uni.navigateTo({
        // 		url: '/Personalcenter/login/login'
        // 	})
        //      return
        // }
        // #ifdef MP-WEIXIN
        uni.reLaunch({
          url: url
        });
        // #endif
        // #ifdef APP-PLUS
        this.courseId = courseId;
        this.getMeetingIsBuy();
        this.getMeetingDetails();
        // #endif
      },

      async course(isPage, page) {
        let _this = this;
        const res = await $http({
          url: 'zx/course/courseList',
          data: {
            indexShow: 0,
            cityCode: '',
            cateId: '',
            page: page || 1,
            pageSize: 30,
            cateType: 2
          }
        });
        if (res) {
          if (isPage) {
            let old = _this.infoLists.list;
            _this.infoLists.list = [...old, ...res.data.list];
          } else {
            _this.infoLists = res.data;
          }
          this.$forceUpdate();
        }
      },

      scanCode() {
        if (!uni.getStorageSync('token')) {
          $navigationTo('Personalcenter/login/login');
          return;
        }
        if (this.userinfo.checkUser == 0) {
          uni.showToast({
            title: '您没有核销权限',
            icon: 'none'
          });
          return;
        }
        let _this = this;
        // 允许从相机和相册扫码
        uni.scanCode({
          success: function (res) {
            //  console.log(res.scanType);
            //  console.log(res.result);
            if (res.scanType == 'QR_CODE' && res.result.startsWith('zx#')) {
              let code = res.result.substr(3, res.result.length);
              _this.skintap('meeting/meetVerifyInfo?isType=1&meetingQrId=' + code + '&courseId=' + _this.courseId);
            } else {
              uni.showToast({
                icon: 'error',
                title: '无效二维码'
              });
            }
          }
        });
      },

      // 课程支付
      async coursePay() {
        let _this = this;
        if (_this.flag) {
          return false;
        }
        uni.showLoading();
        let result = {
          buyNum: Number(_this.buyNum),
          courseId: this.courseId
        };
        if (_this.shopdetail.courseLabel == 1) {
          if (!_this.trialclassStudent) {
            $showMsg('请输入姓名');
            _this.flag = false;
            return false;
          }

          if (!Util.isMobile(_this.parentMobile)) {
            $showMsg('请输入正确的联系方式');
            _this.flag = false;
            return false;
          }

          // 推荐人信息
          if (!_this.recommendInfo.name) {
            $showMsg('请输入正确的推荐人姓名,如无推荐人则填写‘无’');
            _this.flag = false;
            return false;
          }
          if (!_this.recommendInfo.mobile) {
            $showMsg('请输入正确的推荐人手机号,如无推荐人则填写‘无’');
            _this.flag = false;
            return false;
          }
          if (_this.recommendInfo.mobile != '无') {
            if (!Util.isMobile(_this.recommendInfo.mobile)) {
              $showMsg('请输入正确的推荐人手机号');
              _this.flag = false;
              return false;
            }
          }
        }
        _this.flag = true;
        let url = '',
          params = {};
        url = 'zx/course/placeOrderCourse';
        params.addressInfo = _this.addressInfo;
        params.currencyNumber = _this.userinfo.currencyNumber;
        params.discountsAmount = 0;
        params.remark = _this.remark;
        params.courseAndNumDto = result;
        params.shareId = _this.shareId;
        if (_this.recommendInfo != null) {
          params.referrerName = _this.recommendInfo.name;
          params.referrerPhone = _this.recommendInfo.mobile;
        }
        params.studentName = _this.trialclassStudent;
        params.parentsMobile = _this.parentMobile;

        const res = await $http({
          url: url,
          method: 'post',
          data: params
        });
        if (res) {
          _this.orderId = res.data.orderId;
          // needPay 1需要支付  0不需要支付
          if (res.data.needPay == 1) {
            _this.payBtn(res.data.applyPayDto);
          } else {
            this.handleSavePayRecord();
            _this.successorder(res.data.orderId);
          }
        } else {
          _this.flag = false;
        }
      },

      handleSavePayRecord() {
        if (this.activityInfo.activityId && this.activityInfo.shareUserCode) {
          $http({
            url: 'zx/wap/activity/pay/record/save',
            method: 'post',
            data: {
              activityId: this.activityInfo.activityId,
              shareUserCode: this.activityInfo.shareUserCode,
              type: 2,
              orderId: this.orderId
            }
          });
        }
      },
      async payBtn(data) {
        // #ifdef APP-PLUS
        if (this.userinfo.openId == '') {
          let payCodeData = await httpUser.get('zx/user/getPaymentUserCode?mobile=');
          let payCode = payCodeData.data.data;
          data.userCode = payCode;
        }
        // #endif
        let _this = this;
        _this.flag = false;
        let resdata = await httpUser.post('mps/line/collect/order/unified/collect', data);
        let res = resdata.data.data;
        _this.disabled = false;
        uni.hideLoading();
        if (res) {
          if (res.openAllinPayMini) {
            this.flag1 = true;
            this.payInfo = res;
            // #ifdef APP-PLUS
            uni.$appPayTlian(res, 'wxpay');
            // #endif
            // #ifdef MP-WEIXIN
            uni.$payTlian(res);
            // #endif
          } else {
            uni.requestPayment({
              provider: 'wxpay',
              timeStamp: res.payInfo.timeStamp,
              nonceStr: res.payInfo.nonceStr,
              package: res.payInfo.packageX,
              signType: res.payInfo.signType,
              paySign: res.payInfo.paySign,
              success: function (ress) {
                //  console.log('支付成功');
                // _this.successpay(id);
                _this.redirectToOrderIndex();
              },
              fail: function (err) {
                uni.showToast({
                  title: '支付失败'
                });
                setTimeout(function () {
                  if (Config.curUseApp == 'zx') {
                    uni.redirectTo({
                      url: '/splitContent/order/order'
                    });
                  } else {
                    uni.navigateTo({
                      url: '/splitContent/order/helpOrder?meeting=1&app=2'
                    });
                  }
                }, 1500);
              }
            });
          }
        }
      },

      async successorder(orderId) {
        let _this = this;
        const resdata = await $http({
          url: 'zx/course/orderPayNoMoney/' + orderId,
          data: {}
        });
        if (resdata) {
          _this.redirectToOrderIndex();
        }
      },
      redirectToOrderIndex() {
        let _this = this;
        _this.flag = false;
        this.isJumpNext = true;
        if (_this.shopdetail.qrCodeImage) {
          uni.navigateTo({
            url: `/Personalcenter/my/meetingServiceQRcode?qrCodeImage=${_this.shopdetail.qrCodeImage}`
          });
        } else {
          _this.params.url &&
            uni.navigateTo({
              url: _this.params.url
            });
        }
      },

      // 初始获取分享人信息
      async getInvitation() {
        let _this = this;
        _this.invitationInfo = uni.getStorageSync('invitationInfo');
        //  console.log('///invitationInfo////////')
        //  console.log(_this.invitationInfo)
        let shardInfo = uni.getStorageSync('shardInfo');
        _this.recommendInfo.name = shardInfo.nickName;
        _this.recommendInfo.mobile = shardInfo.mobile;
        if (_this.invitationInfo) {
          _this.shareId = _this.invitationInfo.userId;
          //  console.log('分享人信息')
          //  console.log(_this.shareId)
          // 取过值之后立马清除本地缓存
          uni.removeStorageSync('invitationInfo');
          uni.removeStorageSync('shardInfo');
        }
      },
      // 根据分享id获取推荐人信息
      async getMobile() {
        let that = this;
        const res = await $http({
          url: 'zx/exp/getReferrerMobile',
          data: {
            shareId: that.shareId
          }
        });
        if (res) {
          that.recommendInfo = res.data;
          if (that.recommendInfo == null) {
            that.recommendInfo = {
              mobile: '无',
              name: ''
            };
          } else {
            if (!that.recommendInfo.name) {
              that.recommendInfo.name = '无';
            }
            if (!that.recommendInfo.mobile) {
              that.recommendInfo.mobile = '无';
            }
          }
        }
      },
      kfAppHandle() {
        uni.$customerService();
      }
    }
  };
</script>

<style lang="scss" scoped>
  input::-webkit-input-placeholder {
    font-size: 13px;
  }

  .input {
    width: 170px;
  }

  .page_title {
    position: absolute;
    top: 110upx;
    width: 100%;
    text-align: center;
  }

  .share-icon {
    position: absolute;
    top: 290upx;
    right: 60rpx;
  }

  .buy_btn {
    text-align: center;
    background: linear-gradient(180deg, #88cfba 0%, #1d755c 100%);
    border-radius: 35rpx;
    width: 200rpx;
    height: 70rpx;
    color: #ffffff;
    line-height: 70rpx;
    font-size: 32rpx;
  }

  .buy_btn_1 {
    text-align: center;
    background-color: #ea6031;
    border-radius: 35rpx;
    width: 200rpx;
    height: 70rpx;
    color: #ffffff;
    line-height: 70rpx;
    font-size: 32rpx;
  }

  .review_close {
    position: absolute;
    top: 20rpx;
    right: 20rpx;
    z-index: 1;
  }

  .reviewTitle {
    width: 100%;
    text-align: center;
    font-size: 34upx;
    display: flex;
    justify-content: center;
  }

  .cancel-text {
    width: 100%;
    text-align: center;
    font-size: 30rpx;
  }

  .header-img {
    width: 100%;
    height: 780rpx;
  }

  .header-img-child {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    height: 400rpx;
    width: 95%;
    top: 30rpx;
    border-radius: 10rpx;
  }

  .header-o-img-child {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    height: 150rpx;
    width: 95%;
    top: 580rpx;
  }

  .list-content {
    position: absolute;
    top: 480rpx;
  }

  .w44 {
    width: 44rpx;
    height: 44rpx;
  }

  .w38 {
    width: 34rpx;
    height: 34rpx;
  }

  .w42 {
    width: 42rpx;
    height: 42rpx;
  }

  .w50 {
    width: 50rpx;
    height: 50rpx;
  }

  .meeting-content-border {
    margin-left: 49rpx;
    margin-right: 49rpx;
    padding: 0 17rpx 40rpx 11rpx;
    border-bottom: 1rpx solid #e6e6e6;
  }

  .meeting-name-bold {
    color: #000000;
    line-height: 44rpx;
    font-size: 32rpx;
    font-weight: bold;
  }

  .meeting-list-bg {
    width: 710rpx;
    padding: 50rpx 20rpx 0 20rpx;
    background-color: #fafbfd;
    margin-bottom: 113rpx; //详情页面下拉加载到底部无法保持
  }

  .reviewCard_box {
    width: 670rpx;
    position: relative;
  }

  .reviewCard_box image {
    width: 100%;
    height: 100%;
  }

  .cartoom_image {
    width: 420rpx;
    position: absolute;
    top: -265rpx;
    left: 145rpx;
    z-index: -1;
  }

  .reviewCard {
    position: relative;
    width: 100%;
    height: 100%;
    background: #ffffff;
    color: #000;
    border-radius: 24upx;
    padding: 50upx 55upx;
    box-sizing: border-box;
  }

  .review_close {
    position: absolute;
    top: 20rpx;
    right: 20rpx;
    z-index: 1;
  }

  .dialogContent {
    box-sizing: border-box;
    font-size: 32upx;
    line-height: 45upx;
    text-align: center;
    margin-bottom: 40rpx;
  }

  .sure_btn {
    width: 250rpx;
    height: 80rpx;
    background: linear-gradient(180deg, #88cfba 0%, #1d755c 100%);
    border-radius: 45rpx;
    font-size: 30rpx;
    color: #ffffff;
    line-height: 80rpx;
    text-align: center;
  }

  .selected {
    width: 100%;
    height: 70rpx;
    line-height: 70rpx;
    font-size: 30rpx;
    color: #2e896f;
    border: 1px solid #2e896f;
    border-radius: 35rpx;
  }

  .not-selected {
    width: 100%;
    height: 70rpx;
    line-height: 70rpx;
    font-size: 30rpx;
    color: #000;
    border: 1px solid #c8c8c8;
    border-radius: 35rpx;
  }

  .fixed_b {
    position: fixed;
    left: 0;
    bottom: 0;
    width: 100%;
    padding: 20upx 0 20rpx 0;
    background-color: #fff;
    border-bottom: 1rpx solid #e6e6e6;
    box-shadow: 0 0 10upx 0 rgba(0, 0, 0, 0.1);
  }

  // 体验课
  .information {
    height: 86upx;
    display: flex;
    align-items: center;
    border-bottom: 1px solid #efefef;
  }

  .content-popup {
    max-height: 90vh;
    overflow-y: scroll;
    padding-top: 20rpx;
    padding-bottom: 40rpx;
    background-color: #f3f8fc;
    border-top-left-radius: 45rpx;
    border-top-right-radius: 45rpx;
  }

  .icon-clear {
    display: flex;
    justify-content: flex-end;
  }

  .remark {
    height: 100rpx;
    width: 100%;
    overflow: scroll;
  }

  .phone-input {
    color: #999;
    border: none;
    display: flex;
    height: 70rpx;
    font-size: 28rpx;
    align-items: center;
  }

  .amount {
    background-color: #ea6031;
    border-radius: 40rpx;
    padding: 8rpx 15rpx;
    margin-left: 20rpx;
    font-size: 28rpx;
    color: #fff;
  }

  .redtext {
    color: red;
    margin-right: 4rpx;
  }

  .input_tip {
    font-weight: normal;
    font-size: 22upx;
    // text-align: center;
    color: grey;
    margin-left: 30upx;
  }

  .tipText {
    padding-top: 30upx;
    display: flex;
    align-items: center;
  }

  .paybtn {
    width: 100%;
    height: 90rpx;
    line-height: 90rpx;
    text-align: center;
    border-radius: 45rpx;
    background-image: linear-gradient(to bottom, #88cfba, #1d755c);
  }

  // 步进器
  /deep/.uni-numbox__minus {
    width: 60rpx;
    height: 60rpx;
    padding: 0 !important;
    border-radius: 50% !important;
    background-color: #e0e0e0 !important;
  }

  /deep/.uni-numbox__value {
    margin: 0 !important;
    height: 60rpx !important;
    background-color: #f7f7f7 !important;
  }

  /deep/.uni-numbox__plus {
    width: 60rpx;
    height: 60rpx;
    padding: 0 !important;
    border-radius: 50% !important;
    background-color: #e0e0e0 !important;
  }

  /deep/ .uni-numbox {
    height: 60rpx;
    background-color: #f7f7f7 !important;
    border-radius: 60rpx !important;
  }

  /deep/ .uni-numbox__value {
    width: 60rpx !important;
  }

  .courseList {
  }

  .courseItem {
    width: 330upx;
    border-radius: 20upx;
    background-color: #fff;
    margin-bottom: 30rpx;
  }

  .img_s {
    width: 160rpx;
  }

  .templateItem {
    width: 100%;
    text-align: center;
    display: flex;
    align-content: flex-start;
    flex-flow: row wrap;
  }

  .template {
    flex: 0 0 33.33%;
    margin-bottom: 50rpx;
  }

  ////loading
  .loading-container {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100vh;
  }

  .loading-spinner {
    width: 50px;
    height: 50px;
    border: 5px solid #f3f3f3;
    border-top: 5px solid #2e896f;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }

  .back {
    left: 10rpx;
    top: 106rpx;
  }

  .meet-title {
    height: 180rpx !important;
    background-color: #fff;
    z-index: 999;
    position: fixed;
    top: 0;
  }

  @keyframes spin {
    0% {
      transform: rotate(0deg);
    }

    100% {
      transform: rotate(360deg);
    }
  }
</style>
