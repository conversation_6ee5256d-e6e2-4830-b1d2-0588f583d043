<template>
  <view class="postBox">
    <image :src="src" mode="" @longpress="keepImg"></image>
  </view>
</template>

<script>
  export default {
    data() {
      return {
        src: "",
      };
    },
    onLoad(options) {
      this.src = options.url;
    },
    methods: {
      keepImg() {
        let that = this;
        uni.downloadFile({
          //下载文件资源到本地,返回文件的本地临时路径
          url: that.src, //网络图片路径
          success: (res) => {
            var imageUrl = res.tempFilePath; //临时文件路径
            uni.saveImageToPhotosAlbum({
              //保存图片到系统相册
              filePath: imageUrl,
              success: (res) => {
                //  console.log('图片保存成功');
                uni.showToast({
                  title: "保存成功",
                  icon: "none",
                  position: "bottom",
                });
              },
              fail: (err) => {
                uni.showToast({
                  title: "保存失败请重新保存",
                  icon: "none",
                  position: "bottom",
                });
              },
            });
          },
        });
      },
    },
    // onShareAppMessage(res) {
    // 	if (res.from === 'button') { // 来自页面内分享按钮
    // 		//  console.log(res.target)
    // 	}
    // 	return {
    // 		title: '自定义分享标题',
    // 		path: '/pages/test/test?id=123'
    // 	}
    // }
  };
</script>

<style>
  page {
    width: 100%;
    height: 100%;
  }

  .postBox {
    width: 100%;
    height: 100%;
    background: linear-gradient(180deg, #20d0bc 0%, #2dafdf 100%);
  }

  image {
    width: 100%;
    height: 1300rpx;
  }
</style>
