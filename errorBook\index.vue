<template>
  <view class="mt-50">
    <view class="plr-30">
      <!-- 学员 -->
      <view class="flex-wrap f-30 c-00 h60 lh-60 pb-30">
        <view class="flex-dir-col">学员：</view>
        <view class="pick f-28 c-99">
          <picker v-if="studentData != null && studentData.length > 0" @change="bindPickerChange" :value="selectedStudentCode" :range="studentData.map((item) => item.realName)">
            <view v-if="selectedStudentCode !== ''">{{ selectedStudentName }}</view>
            <view v-else>选择学员</view>
            <uni-icons type="forward" size="16" color="#C8C8C8" class="pick_right"></uni-icons>
          </picker>
          <view v-else>选择学员</view>
        </view>
      </view>
      <!-- 类型 -->
      <view class="flex-wrap f-30 c-00 mt-40 h60 lh-60 pb-30">
        <view class="flex-dir-col">类型：</view>
        <view class="pick f-28 c-99">
          <picker @change="bindPickerType" :value="selectedPickTypeId" :range="pickTypeData.map((item) => item.name)">
            <view v-if="selectedPickTypeName !== ''">{{ selectedPickTypeName }}</view>
            <view v-else>选择类型</view>
            <uni-icons type="forward" size="16" color="#C8C8C8" class="pick_right"></uni-icons>
          </picker>
        </view>
      </view>
      <!-- 阶段 -->
      <view v-if="!isSuperReading" class="flex-wrap f-30 c-00 mt-40 h60 lh-60 pb-30">
        <view class="flex-dir-col">阶段：</view>
        <view class="pick f-28 c-99">
          <picker v-if="buttonPhase.length" @change="bindPickerPhase" :value="PhaseStudentId" :range="buttonPhase.map((item) => item.name)">
            <view v-if="PhaseStudentId !== ''">{{ PhaseStudentName }}</view>
            <view v-else>选择阶段</view>
            <uni-icons type="forward" size="16" color="#C8C8C8" class="pick_right"></uni-icons>
          </picker>
          <view v-else class="picker-placeholder">无数据</view>
        </view>
      </view>
      <!-- 知识点 -->
      <view v-if="selectedPickTypeId !== '2' && !isSuperReading" class="flex-wrap f-30 c-00 mt-40 h60 lh-60 pb-30">
        <view class="flex-dir-col">知识点：</view>
        <view class="pick f-28 c-99">
          <picker v-if="pickGraData.length" @change="bindPickerGra" :value="GraPickTypeId" :range="pickGraData.map((item) => item.name)">
            <view v-if="GraPickTypeName !== ''">{{ GraPickTypeName }}</view>
            <view v-else>选择知识点</view>
            <uni-icons type="forward" size="16" color="#C8C8C8" class="pick_right"></uni-icons>
          </picker>
          <view v-else-if="PhaseStudentId === ''" class="picker-placeholder">请先选择阶段</view>
          <view v-else class="picker-placeholder">无数据</view>
        </view>
      </view>
      <!-- 课程名称 -->
      <view v-if="isSuperReading" class="flex-wrap f-30 c-00 mt-40 h60 lh-60 pb-30">
        <view class="flex-dir-col">课程名称：</view>
        <view class="pick f-28 c-99" @click="getCourse">
          <picker @change="changeCourse" v-if="courseList.length != 0 && selectedStudentCode" :value="courseId" range-key="courseName" :range="courseList">
            <view v-if="courseList.length && courseName" class="ellipsis">{{ courseName }}</view>
            <view v-else>请选择课程</view>
            <uni-icons type="forward" size="16" color="#C8C8C8" class="pick_right"></uni-icons>
          </picker>
          <view v-else-if="courseList.length == 0 && selectedStudentCode">该学员没有学习的课程</view>
          <view v-else>请先选择学员</view>
        </view>
      </view>
      <!-- 试题分类 -->
      <view v-if="isSuperReading" class="flex-wrap f-30 c-00 mt-40 h60 lh-60 pb-30">
        <view class="flex-dir-col">试题分类：</view>
        <view class="pick f-28 c-99">
          <picker v-if="classificationList.length" range-key="name" @change="changeClassification" :value="classificationId" :range="classificationList">
            <view v-if="classificationName !== ''">{{ classificationName }}</view>
            <view v-else>请选择试题分类</view>
            <uni-icons type="forward" size="16" color="#C8C8C8" class="pick_right"></uni-icons>
          </picker>
        </view>
      </view>
    </view>
    <view class="bottomButton">
      <u-button shape="circle" :plain="true" color="#428A6F" @click="closeHandle">重置</u-button>
      <u-button shape="circle" class="leftButton" color="#428A6F" @click="confirmHandle">确定</u-button>
    </view>
    <!-- 选择校区弹窗 -->
    <uni-popup ref="popopChooseSchool" type="center" @change="change">
      <view class="dialogBG">
        <view class="reviewCard_box positionRelative">
          <view class="cartoom_image">
            <image src="https://document.dxznjy.com/dxSelect/dialog_icon.png" mode="widthFix"></image>
          </view>
          <view class="review_close" @click="closeDialog">
            <uni-icons type="clear" size="28" color="#B1B1B1"></uni-icons>
          </view>
          <view class="reviewCard">
            <view class="reviewTitle bold pb-20">选择校区</view>
            <scroll-view scroll-y="true" class="scroll-Y">
              <view
                class="dialogContent"
                @click="chooseSchoollist(item, index)"
                v-for="(item, index) in arraySchool"
                :key="item.merchantCode"
                :class="isactive == index ? 'addclass' : 'not-selected'"
              >
                {{ item.merchantName }}
              </view>
            </scroll-view>
            <view class="review_btn" @click="confirmSchool()">确定</view>
          </view>
        </view>
      </view>
    </uni-popup>
  </view>
</template>
<script>
  export default {
    data() {
      return {
        title: 'picker',
        isactive: -1, // 选中索引
        // 学员
        studentData: [],
        courseName: '',
        courseList: [],
        classificationName: '',
        // 试题分类
        classificationList: [
          {
            id: 0,
            name: '未做关卡'
          },
          {
            id: 1,
            name: '已做关卡'
          }
        ],
        arraySchool: [],
        classificationId: '',
        selectedStudentCode: '',
        selectedStudentName: '',
        // 类型
        pickTypeData: [
          { id: '0', name: '语法' },
          { id: '1', name: '新阅读理解' },
          { id: '2', name: '数学' }
        ],
        selectedPickTypeId: '',
        selectedPickTypeName: '',
        buttonPhase: [], // 阶段数据
        PhaseStudentId: '',
        PhaseStudentName: '',
        pickGraData: [],
        GraPickTypeId: '',
        GraPickTypeName: '',
        // 筛选结果
        activeName: 'phase',
        showPoup: false,
        titlePoup: '选择',
        phaseArrow: false,
        gramArrow: false,
        knowArrow: false,
        buttonData: [],
        isSuperReading: false,
        courseId: '',
        categoryId: '',
        memberId: '', //当前用户membercode
        merchantCode: '',
        rollShow: false, // 禁止滚动穿透
        checkPoint: '',
        // 课程大类ID
        curriculumId: ''
      };
    },
    computed: {
      dropItem(name) {
        // this.show = true;
        return (name) => {
          const result = {};
          const find = this.result.find((item) => item.name === name);
          if (find) {
            result.label = find.label;
            result.value = find.value;
          } else {
            result.label = this[name].label;
            result.value = this[name].value;
          }
          return result;
        };
      },
      // 获取当前下拉筛选项
      currentDropItem() {
        return this[this.activeName];
      }
    },
    onPageScroll() {
      // 滚动后及时更新位置
      this.$refs.dropDown.init();
    },
    onUnload() {
      // #ifdef APP-PLUS
      if (this.app) {
        plus.runtime.quit();
      }
      // #endif
    },
    onLoad(option) {
      // this.studentCode = '625029993';
      // #ifdef APP-PLUS
      this.app = option.app;
      this.studentCode = option.studentCode;
      this.merchantCode = option.merchantCode;
      this.memberId = option.memberId;
      this.getCourseName();
      this.$handleTokenFormNative(option);
      // #endif
      // #ifdef MP-WEIXIN
      this.memberId = option.memberId;
      // #endif

      // uni.setStorageSync(
      //   'token',
      //   '****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************'
      // );
      // this.studentCode = option.studentCode;
      // this.merchantCode = 'A0001';
      // this.memberId = '921105151';
      // console.log('token', token);
      this.studentList();
      // 调用数学接口，获取课程大类id
      this.getMathsCategoryId();
    },
    methods: {
      // 获取课程大类
      getMathsCategoryId() {
        this.$httpUser.get('dyf/math/wap/common/curriculumList').then((res) => {
          this.curriculumId = res.data.data[1].id;
        });
      },
      //点击选择校区
      chooseSchoollist(item, index) {
        this.isactive = index;
        this.merchantCode = item.merchantCode;
        this.getStageList();
      },
      // 禁止滚动穿透
      change(e) {
        this.rollShow = e.show;
      },
      async getStageList() {
        let res = await this.$httpUser.get('znyy/super-read/wrongBook/stageList', {
          studentCode: this.selectedStudentCode,
          merchantCode: this.merchantCode,
          courseId: this.courseId,
          type: this.classificationId,
          pageNum: 1,
          pageSize: 15
        });
        this.checkPoint = res.data.data.length;
      },
      confirmSchool() {
        if (this.checkPoint === 0) {
          let contentshowToast = this.classificationName == '未做关卡' ? '该门店下暂无数据' : '请在未做试题答题后再来查看';
          uni.showToast({ title: contentshowToast, icon: 'none' });
        } else {
          if (this.merchantCode === '') return uni.showToast({ title: '请选择一个校区', icon: 'none' });
          uni.navigateTo({
            url: `/errorBook/doneCheckpoint?studentCode=${this.selectedStudentCode}&testQuestions=${this.classificationId}&courseId=${this.courseId}&merchantCode=${this.merchantCode}`
          });
        }
        this.closeDialog();
      },
      //关闭弹窗
      closeDialog() {
        this.isactive = -1;
        this.merchantCode = '';
        this.$refs.popopChooseSchool.close();
      },
      showMoreHandle() {
        uni.navigateTo({
          url: `/errorBook/questiPage?studentCode=${this.selectedStudentCode}&phase=${this.PhaseStudentId}&knowledgeId=${this.GraPickTypeId}`
        });
      },
      validateFilters() {
        if (this.selectedPickTypeId === '0' && (!this.selectedStudentCode || !this.PhaseStudentId || !this.GraPickTypeId)) {
          return '请选中所有下拉框';
        }
        if (this.selectedPickTypeId === '1' && (!this.selectedStudentCode || !this.classificationId || !this.courseId)) {
          return '请选中所有下拉框';
        }
        // 数学
        if (this.selectedPickTypeId === '2' && (!this.selectedStudentCode || !this.selectedPickTypeId || !this.PhaseStudentId)) {
          return '请选中所有下拉框';
        }
        return '';
      },
      async confirmHandle() {
        const validationError = this.validateFilters();
        if (validationError) {
          uni.showToast({ title: validationError, icon: 'none' });
          return;
        }
        if (this.selectedPickTypeId === '0') {
          uni.navigateTo({
            url: `/errorBook/questionPage?studentCode=${this.selectedStudentCode}&phase=${this.PhaseStudentId}&knowledgeId=${this.GraPickTypeId}`
          });
        } else if (this.selectedPickTypeId === '2') {
          uni.navigateTo({
            url: `/errorBook/mathsErrorBook?studentCode=${this.selectedStudentCode}&subjectId=${this.selectedPickTypeId}&phase=${this.PhaseStudentId}`
          });
          let data = {
            studentCode: this.selectedStudentCode,
            // disciplineId: '1365386256284794880',
            PhaseStudentId: this.PhaseStudentId,
            curriculumId: this.curriculumId
          };
          uni.setStorageSync('selectInfo', data);
          return;
        } else {
          let res = await this.$httpUser.get('znyy/super-read/wrongBook/merchant-name', {
            studentCode: this.selectedStudentCode,
            courseId: this.courseId,
            type: this.classificationId
          });
          let contentshowToast = this.classificationName == '未做关卡' ? '还没有错题哦' : '请在未做试题答题后再来查看';
          this.arraySchool = res.data.data;
          if (res.data.data.length == 1) {
            this.merchantCode = res.data.data[0].merchantCode;
            await this.getStageList();
            if (!this.checkPoint) {
              uni.showToast({ title: contentshowToast, icon: 'none' });
            } else {
              uni.navigateTo({
                url: `/errorBook/doneCheckpoint?studentCode=${this.selectedStudentCode}&testQuestions=${this.classificationId}&courseId=${this.courseId}&merchantCode=${this.merchantCode}`
              });
            }
          } else if (res.data.data.length == 0) {
            uni.showToast({
              title: contentshowToast,
              icon: 'none'
            });
          } else {
            this.$refs.popopChooseSchool.open();
          }
        }
      },
      closeHandle() {
        // 重置类型数据信息
        this.selectedStudentCode = '';
        this.selectedPickTypeName = '';
        // 清空阶段id
        this.selectedPickTypeId = '';
        this.PhaseStudentId = '';
        this.GraPickTypeName = '';
        this.pickGraData = [];
        this.isSuperReading = false;
        this.classificationName = '';
        this.classificationId = '';
        this.categoryId = '';
        this.courseName = '';
        this.courseId = '';
      },

      studentList() {
        this.$httpUser
          .get(`znyy/course/queryStudentList/1/10`, {
            memberId: this.memberId
          })
          .then((res) => {
            this.studentData = res.data.data.data;
            //  #ifdef APP-PLUS
            for (let i = 0; i < this.studentData.length; i++) {
              if (this.studentData[i].studentCode == this.studentCode) {
                this.selectedStudentCode = this.studentData[i].studentCode;
                this.selectedStudentName = this.studentData[i].realName;
              }
            }
            // #endif
          });
      },
      grammarList() {
        this.$httpUser.get('dyf/wap/applet/queryAll').then((res) => {
          const dictData = res.data.data;
          this.buttonPhase = dictData.grammar_phase.map((item) => {
            return {
              name: item.dictLabel,
              id: item.dictValue
            };
          });
        });
      },
      getCourseName() {
        this.$httpUser.get('znyy/super-read/wrongBook/course-name', { studentCode: this.studentCode }).then((res) => {
          this.courseList = res.data.data;
        });
      },
      bindPickerChange(e) {
        if (this.studentData.length) {
          this.selectedStudentCode = this.studentData[e.detail.value].studentCode;
          this.selectedStudentName = this.studentData[e.detail.value].realName;
          if (this.selectedStudentCode !== '') {
            this.$httpUser.get('znyy/super-read/wrongBook/course-name', { studentCode: this.selectedStudentCode }).then((res) => {
              this.courseList = res.data.data;
            });
          }
        }
      },
      bindPickerType(e) {
        console.log('🚀 ~ bindPickerType ~ e:', e.detail.value);
        if (this.pickTypeData.length) {
          if (this.pickTypeData[e.detail.value].id == 1) {
            this.selectedPickTypeId = this.pickTypeData[e.detail.value].id;
            this.selectedPickTypeName = this.pickTypeData[e.detail.value].name;
            this.isSuperReading = true;
            this.pickGraData = [];
            this.buttonPhase = [];
            this.PhaseStudentName = '';
            this.PhaseStudentId = '';
          } else if (this.pickTypeData[e.detail.value].id == 2) {
            // 数学类型
            this.selectedPickTypeId = this.pickTypeData[e.detail.value].id;
            this.selectedPickTypeName = this.pickTypeData[e.detail.value].name;
            this.isSuperReading = false;
            this.buttonPhase = [];
            this.PhaseStudentName = '';
            this.PhaseStudentId = '';
            // 调用数学阶段的接口----
            this.$httpUser
              .get('dyf/math/wap/common/selectTree', {
                curriculumId: this.curriculumId,
                nodeLevel: 2,
                subjectName: '数学'
              })
              .then((res) => {
                this.buttonPhase = res.data.data[0].childList.map((item) => {
                  return {
                    name: item.nodeName,
                    id: item.id
                  };
                });
              });
          } else {
            this.isSuperReading = false;
            this.grammarList();
            this.selectedPickTypeId = this.pickTypeData[e.detail.value].id;
            this.selectedPickTypeName = this.pickTypeData[e.detail.value].name;
            this.buttonPhase = [];
            this.PhaseStudentName = '';
            this.PhaseStudentId = '';
          }
        }
      },
      bindPickerPhase(e) {
        this.pickGraData = [];
        this.GraPickTypeId = '';
        this.GraPickTypeName = '';
        if (this.buttonPhase.length) {
          this.PhaseStudentId = this.buttonPhase[e.detail.value].id;
          this.PhaseStudentName = this.buttonPhase[e.detail.value].name;
        }
        this.$httpUser.get('dyf/wap/applet/knowledgeOptionsList', { knowledgeFlag: true, phase: this.PhaseStudentId }).then((res) => {
          this.pickGraData = res.data.data.map((item) => {
            return {
              name: item.name,
              id: item.id
            };
          });
        });
      },
      bindPickerGra(e) {
        if (this.pickGraData.length) {
          this.GraPickTypeId = this.pickGraData[e.detail.value].id;
          this.GraPickTypeName = this.pickGraData[e.detail.value].name;
        }
      },
      getCourse() {
        if (this.selectedStudentCode == '') return uni.showToast({ title: '您还未选择学生~', icon: 'none' });
      },
      changeCourse(e) {
        this.courseId = this.courseList[e.detail.value].courseId;
        this.courseName = this.courseList[e.detail.value].courseName;
      },

      changeClassification(e) {
        this.classificationId = e.detail.value;
        // #ifdef APP-PLUS
        this.classificationId = this.classificationId + '';
        // #endif
        this.classificationName = this.classificationList[e.detail.value].name;
      }
    }
  };
</script>

<style>
  .list_box {
    border-radius: 14upx;
    background: #ffffff;
    box-shadow: 0rpx 4rpx 16rpx 0rpx #f5f7f9;
    padding: 30upx;
    font-size: 26upx;
    color: rgba(102, 102, 102, 1);
    position: relative;
  }

  .iconB {
    width: 154rpx;
    height: 76rpx;
    background: rgba(225, 225, 225, 0.15);
    border-radius: 46rpx;
    border: 2rpx solid #e1e1e1;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-right: 10rpx;
  }

  .pick {
    flex: 1;
    height: 60upx;
    line-height: 60upx;
    border: 1px solid #c8c8c8;
    border-radius: 20upx;
    text-align: center;
    padding: 0 30upx;
    position: relative;
  }

  .pick_right {
    position: absolute;
    right: 20upx;
    top: -5upx;
  }

  /* 弹层宽度 */
  .uv-dp__container {
    height: 150rpx;
    width: 690rpx;
    margin-left: 30rpx;
  }
  /* 弹窗样式 */
  .dialogBG {
    width: 100%;
    /* height: 100%; */
  }

  .reviewCard_box image {
    width: 100%;
    height: 100%;
  }

  .reviewCard {
    position: relative;
    width: 100%;
    height: 100%;
    background: #ffffff;
    color: #000;
    border-radius: 24upx;
    padding: 50upx 55upx;
    box-sizing: border-box;
  }

  .cartoom_image {
    width: 420rpx;
    position: absolute;
    top: -250rpx;
    left: 58rpx;
    z-index: -1;
  }

  .review_close {
    position: absolute;
    top: 20rpx;
    right: 20rpx;
    z-index: 1;
  }

  .reviewTitle {
    width: 100%;
    text-align: center;
    font-size: 34upx;
    display: flex;
    justify-content: center;
  }

  .dialogContent {
    box-sizing: border-box;
    font-size: 32upx;
    line-height: 45upx;
    text-align: center;
    margin-top: 40rpx;
    padding: 0 15rpx;
  }

  .review_btn {
    width: 240upx;
    height: 80upx;
    background: linear-gradient(180deg, #88cfba 0%, #1d755c 100%);
    border-radius: 45upx;
    font-size: 30upx;
    color: #ffffff;
    line-height: 80upx;
    margin: 60rpx auto 0 auto;
    justify-content: center;
    text-align: center;
  }
  .addclass {
    width: 100%;
    height: 70rpx;
    line-height: 70rpx;
    font-size: 30rpx;
    color: #ea6531;
    border: 1px solid #ea6531;
    border-radius: 35rpx;
  }

  .not-selected {
    width: 100%;
    height: 70rpx;
    line-height: 70rpx;
    font-size: 30rpx;
    color: #000;
    border: 1px solid #c8c8c8;
    border-radius: 35rpx;
  }

  .scroll-Y {
    /* height: 440rpx; */
    height: auto;
  }
  .ellipsis {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    max-width: 220px;
  }

  .bottomButton {
    width: 100%;
    display: flex;
    position: fixed;
    bottom: 0;
    margin-bottom: 15rpx;
    padding-bottom: var(--safe-area-inset-bottom);
  }
</style>
