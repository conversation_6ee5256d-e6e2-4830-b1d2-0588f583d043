<template>
  <page-meta :page-style="'overflow:' + (rollShow ? 'hidden' : 'visible')"></page-meta>
  <view class="loggingContent">
    <view class="statistics" v-if="wordCount != 0">
      <text>文章字数: {{ wordCount }}字</text>
      <!--      <text>-->
      <!--        文章字数:-->
      <!--        <u-count-to :startVal="0" :duration="wordCount * 100" fontSize="16px" :endVal="wordCount"></u-count-to>-->
      <!--        字-->
      <!--      </text>-->
    </view>
    <view class="mainBody">
      <view class="titleWrap">
        <text class="title">{{ articleTitle }}</text>
      </view>
      <view class="article" v-html="textContent"></view>
      <view class="article" v-html="translateContent"></view>
    </view>
    <view class="determine" @click="back()">返回首页</view>
  </view>
</template>

<script>
  const { $http } = require('@/util/methods.js');

  export default {
    data() {
      return {
        textContent: '',
        articleTitle: '',
        translateContent: '',
        wordSelect: [],
        courseStage: null,
        // 流式输出
        controller: null,
        wordCount: 0, // 单词计数
        intervalId: null
      };
    },
    onLoad(option) {
      this.wordSelect = JSON.parse(option.wordSelect);
      this.courseStage = option.courseStage;
      this.getAiTextMain();
    },
    onHide() {
      if (this.intervalId) {
        clearInterval(this.intervalId);
      }
    },
    onShow() {
      if (this.intervalId) {
        clearInterval(this.intervalId);
      }
    },
    methods: {
      // ai生成短文 流式输出不能分段，换个方法
      /* uni.showLoading({
         title: '正在生成'
       });
       const requestTask = uni.request({
         url: `${DXHost}ai/super/read/wisdom/generate-article?courseStage=${this.courseStage}&wordBookList=${this.wordSelect.toString()}`,
         header: {
           'x-www-iap-assertion': uni.getStorageSync('token'),
           'content-Type': 'application/json'
         },
         timeout: 15000,
         // responseType: 'stream',
         method: 'GET',
         enableChunked: true, // 配置这里
         success: (response) => {
           console.log('请求成功:', response);
           uni.hideLoading();
         },
         fail: (error) => {
           console.error('请求失败:', error);
         }
       });

       requestTask.onHeadersReceived(function (res) {
         console.log('接收到头部:', res);
       });

       let accumulatedText = ''; // 累加文本，反正分段返回只展示最后一段文本 去除前缀之后的
       requestTask.onChunkReceived((res) => {
         console.log(res.data)
         let title, context, translate = '';
         const uint8Array = new Uint8Array(res.data);
         let text = String.fromCharCode.apply(null, uint8Array);
         text = decodeURIComponent(escape(text));
         accumulatedText += text; // 累加接收到的文本
         this.wordCount = accumulatedText.split(' ').length;
         this.updateText(accumulatedText); // 更新 UI 或者存储完整数据
       });*/
      async getAiTextMain() {
        uni.showLoading({
          title: '正在生成'
        });
        let res = await $http({
          url: 'ai/super/read/wisdom/generate-article',
          method: 'get',
          data: {
            courseStage: this.courseStage,
            wordBookList: this.wordSelect.toString()
          }
        });

        if (res) {
          uni.hideLoading();
          console.log(res);
          let title = '';
          let content = '';
          let translate = '';
          const match = res.data.match(/标题\s*([\s\S]*?)文章\s*([\s\S]*?)翻译\s*([\s\S]*)/);
          if (match) {
            title = match[1].trim(); // 提取标题
            content = match[2].trim(); // 提取内容
            translate = match[3].trim(); // 提取翻译
          }
          // 逐字显示标题并实时统计单词数
          await this.updateText(title, 'title');

          // 逐字显示内容并实时统计单词数
          await this.updateText(content, 'content');

          // 逐字显示翻译（翻译不影响单词总数）
          await this.updateText(translate, 'translate');
        }
      },
      updateText(newText, type) {
        return new Promise((resolve) => {
          let text = '';
          let index = 0;
          let wordBuffer = ''; // 用于缓冲单词字符
          this.intervalId = setInterval(() => {
            if (index < newText.length) {
              const char = newText.charAt(index++);
              text += char;
              // 应用首行缩进逻辑
              if (type === 'content') {
                this.textContent = this.formatIndentedText(text); // 格式化文本
              } else if (type === 'title') {
                this.articleTitle = text;
              } else if (type === 'translate') {
                this.translateContent = text;
              }

              // 判断是否是单词字符
              if (/\w/.test(char)) {
                wordBuffer += char; // 拼接单词字符
              } else if (wordBuffer.length > 0) {
                // 单词结束，更新 wordCount
                this.wordCount++;
                wordBuffer = ''; // 清空缓冲区
              }
            } else {
              // 处理缓冲区中最后的单词
              if (wordBuffer.length > 0) {
                this.wordCount++;
              }
              this.sethighlight();
              clearInterval(this.intervalId); // 清除定时器
              resolve(); // 执行完成后调用 resolve
            }
          }, 50); // 每50毫秒添加一个字符
        });
      },
      formatIndentedText(text) {
        // 在开头和每个 <br> 后的段落添加首行缩进
        return text
          .split(/(<br\s*\/?>)/g) // 按 <br> 标签分段
          .map((segment, index, array) => {
            // 忽略纯 <br> 标签本身
            if (segment.trim() === '<br>' || segment.trim() === '<br/>') {
              return segment; // 保留 <br> 标签
            }

            // 对开头段落和 <br> 后的段落添加首行缩进
            if (index === 0 || (index > 0 && array[index - 1].trim().match(/<br\s*\/?>/))) {
              return `<span style="text-indent: 2em;display: inline-block">${segment.trim()}</span>`;
            }
            // 其他段落保持原样
            return segment.trim();
          })
          .join('');
      },

      // 高亮显示选中文本
      sethighlight() {
        this.textContent = this.textContent.replace(/<\/?span[^>]*>/gi, '');
        this.wordSelect.forEach((item, index) => {
          const regex = new RegExp(`(${this.wordSelect[index]})`, 'gi');
          this.textContent = this.textContent.replace(regex, `<span style="color: #339378;">${this.wordSelect[index]}</span>`);
        });
        this.textContent = this.formatIndentedText(this.textContent);
        // 返回格式化的 HTML 字符串
        return [
          {
            name: 'div',
            children: [
              {
                type: 'html',
                name: 'span',
                attrs: {
                  style: 'color: #339378;'
                },
                children: [
                  {
                    type: 'text',
                    text: this.textContent
                  }
                ]
              }
            ]
          }
        ];
      },
      back() {
        uni.navigateBack();
        if (this.intervalId) {
          clearInterval(this.intervalId);
          this.intervalId = null;
        }
      }
    },
    beforeDestroy() {
      // 在组件销毁之前清除定时器
      if (this.intervalId) {
        clearInterval(this.intervalId);
      }
    }
  };
</script>

<style lang="scss">
  page {
    height: 100vh;
    padding: 0;
  }

  .loggingContent {
    width: 100%;
    padding: 24rpx 32rpx 0 32rpx;
    box-sizing: border-box;
    height: 100%;
    background-color: #fff;

    .statistics {
      text-align: right;
      font-size: 16px;
      color: #555555;
      padding-bottom: 32rpx;
    }

    .mainBody {
      .titleWrap {
        text-align: center;
        margin-bottom: 24rpx;

        .title {
          font-size: 16px;
          font-weight: bold;
        }
      }

      height: 1180rpx;
      overflow-y: scroll;

      .article {
        font-size: 16px;
        color: #555555;
        line-height: 24px;
        padding: 0 20rpx;
      }
    }
  }

  .determine {
    width: 586upx;
    height: 80upx;
    background-color: #339378;
    border-radius: 45upx;
    position: absolute;
    bottom: 60upx;
    color: #fff;
    line-height: 80upx;
    font-size: 30upx;
    left: 80upx;
    text-align: center;
  }
</style>
