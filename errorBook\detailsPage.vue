<template>
  <view>
    <u-navbar :title="titleNavbar" @rightClick="rightClick" :autoBack="true"></u-navbar>

    <view class="plr-30 pb-100 mt-200">
      <!-- <view class="button-container" v-if="topicQues">
                <button class="switch-button" @click="showCompletedQuestions">已做试题</button>
                <button class="switch-button" @click="showUncompletedQuestions">未做试题</button>
            </view> -->
      <view plr-30 pb-30 mt-200 class="list_box mt-30" v-if="questionList.length > 0">
        <view class="p-20 mb-30 bd-ee f-28" @click="goDetail(item)" v-for="item in questionList" :key="item.id">
          <view class="c-00 text-overflow">
            <text v-for="(part, index) in convertQuestionData(item.questionName)" :key="index">
              {{ part.text }}
              <text v-if="part.text === ''" style="text-decoration: underline; width: 120rpx">
                {{ '&nbsp;&nbsp;&nbsp;&nbsp;' }}
              </text>
            </text>
          </view>
          <view class="mt-20 c-66">做题时间：{{ item.updateTime }}</view>
        </view>
      </view>
      <view class="bg-ff radius-15 plr-20 mt-30 t-c flex-col" v-else :style="{ height: useHeight + 'rpx' }" style="position: relative">
        <image src="/static/index/<EMAIL>" mode="widthFix" class="mb-20 img_s"></image>
        <view style="color: #bdbdbd">暂无数据</view>
        <image src="/static/index/<EMAIL>" mode="widthFix" class="mb-20 img_s"></image>
      </view>
    </view>
    <view class="title pagings">
      <page-pagination class="m-25" ref="pageChange" :total="page.total" :pageSize="page.pageSize" :numAround="true" size="large" @change="change"></page-pagination>
    </view>
  </view>
</template>

<script>
  export default {
    data() {
      return {
        page: {
          total: 1,
          pageSize: 15,
          pageNum: 1
        },
        titleNavbar: '',
        questionList: [],
        topicQues: true,
        type: null,
        knowledgeId: '',
        phase: '',
        studentCode: '',
        distinguish: ''
      };
    },
    onLoad(options) {
      this.phase = options.phase;
      this.studentCode = options.studentCode;
      this.titleNavbar = options.type == 1 ? '已做试题' : '未做试题';
      this.knowledgeId = options.knowledgeId;
      this.type = options.type;
      // this.getGraQuestion()
    },
    onShow() {
      this.questionList = [];
      this.page.pageNum = 1;
      this.getGraQuestion();
    },
    onReachBottom() {
      let allTotal = this.page.pageNum * this.page.pageSize;
      if (allTotal < this.page.total) {
        //当前条数小于总条数 则增加请求页数
        this.page.pageNum++;
        this.getGraQuestion(); //调用加载数据方法
      } else {
        console.log('已加载全部数据');
        uni.showToast({
          title: '已加载全部数据',
          icon: 'none'
        });
      }
    },
    methods: {
      convertQuestionData(question) {
        const parts = question.split('##');
        const result = [];
        parts.forEach((part, index) => {
          result.push({ text: part });
          if (index < parts.length - 1) {
            result.push({ text: '', index: index });
            this.underlineIndex++;
          }
        });

        return result;
      },
      goDetail(item) {
        console.log(item);
        if (this.type == 1) {
          uni.navigateTo({
            url: `/errorBook/topicPaged?type=${this.type}&knowledgeId=${this.knowledgeId}&phase=${this.phase}&studentCode=${this.studentCode}&id=${item.id}&category=${item.category}`
          });
        } else {
          uni.navigateTo({
            url: `/errorBook/topicPage?type=${this.type}&knowledgeId=${this.knowledgeId}&phase=${this.phase}&studentCode=${this.studentCode}&id=${item.id}&category=${item.category}`
          });
        }
      },

      getGraQuestion() {
        this.$httpUser
          .get('dyf/wap/applet/wrongBookPage', {
            pageNum: this.page.pageNum,
            pageSize: this.page.pageSize,
            type: this.type,
            phase: this.phase,
            studentCode: this.studentCode,
            knowledgeId: this.knowledgeId
          })
          .then((res) => {
            this.page.total = res.data.data.totalItems;
            const newList = res.data.data.data;
            this.questionList.push(...newList);
          });
        this.topicQues = false;
      },
      change(pageNum, type) {
        this.page.pageNum = pageNum;
        if (this.type == 1) {
          this.getTodayGra();
        } else {
          this.getBeforeGra();
        }
      },
      truncateText(text) {
        const lines = text.split('\n');
        const firstTwoLines = lines.slice(0, 2).join('\n');
        if (lines.length > 2) {
          return firstTwoLines + '...';
        }
        return firstTwoLines;
      }
    }
  };
</script>

<style>
  .list_box {
    border-radius: 14rpx;
    background: #ffffff;
    box-shadow: 0rpx 4rpx 16rpx 0rpx #f5f7f9;
    padding: 30rpx;
    font-size: 26rpx;
    color: rgba(102, 102, 102, 1);
    position: relative;
  }

  .button-container {
    display: flex;
    flex-direction: column;
    align-items: stretch;
    justify-content: center;
    transform: translateY(300%);
    margin-bottom: 20rpx;
    width: 1500rpx;
    margin-left: 50rpx;
  }

  .switch-button {
    width: 100%;
    height: 80rpx;
    margin-bottom: 20rpx;
    background-color: #fafffd;
    color: #555;
    border: 1px solid #428a6f;
    border-radius: 35rpx;
    text-align: center;
    line-height: 80rpx;
  }

  .text-overflow {
    text-overflow: ellipsis;
    display: -webkit-box;
    display: box;
    -webkit-box-orient: vertical;
    box-orient: vertical;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    overflow: hidden;
  }

  .pick {
    flex: 1;
    height: 60rpx;
    line-height: 60rpx;
    border: 1px solid #c8c8c8;
    border-radius: 20rpx;
    text-align: center;
    padding: 0 30rpx;
    position: relative;
  }

  /* 弹层宽度 */
  .uv-dp__container {
    height: 150rpx;
    width: 690rpx;
    margin-left: 30rpx;
  }

  button {
    width: 40% !important;
  }

  .pagings {
    position: fixed;
    bottom: 0;
    width: 100%;
    background-color: #fff;
    border-top: 1px solid #eee;
    display: flex;
    justify-content: center;
  }

  .popupButtons >>> button {
    width: 100% !important;
    height: 82rpx !important;
    margin-top: 20rpx;
  }

  .list_box.p-20.mb-30.bd-ee:last-child {
    margin-bottom: 50rpx;
  }
</style>
