<template>
  <view class="plr-30 pb-30">
    <view class="p-30 bg-ff radius-15" v-if="false">
      <block>
        <view class="flex_s">
          <view class="box-180 radius-10">
            <image class="w100 h100" :src="detailsList.bannerImages != undefined ? detailsList.bannerImages[0] : ''"></image>
          </view>
          <view class="ml-20 f-32">
            <view class="bold mb-20">{{ detailsList.courseName }}</view>
            <view class="f-26 color_tangerine mb-20">
              ¥
              <text class="f-32 bold">{{ detailsList.coursePrice }}</text>
            </view>
            <view class="f-32">x1</view>
          </view>
        </view>
      </block>
    </view>

    <view class="shop_content mt-30 p-30" v-if="false">
      <view class="title">商品介绍</view>
      <view class="bg-f7 p-30 radius-15">
        <text class="introduce">鼎英语课程体验卷,每个用户只可使用一次,购买后可获得一节鼎英语的体验课程</text>
      </view>
    </view>

    <view class="order mt-30">
      <view class="mb-40">
        <text class="f-32 c-00 bold">订单详情</text>
        <!-- <text style="float: right;">{{detailsList.createTime}}</text> -->
      </view>
      <view class="mb-40">订单号：{{ detailsList.orderNo }}</view>
      <view class="mb-40">试课人名称：{{ detailsList.expName }}</view>
      <view class="mb-40">试课人性别：{{ detailsList.sex == 1 ? '男' : '女' }}</view>
      <view class="mb-40">联系方式：{{ detailsList.expPhone }}</view>
      <view class="mb-40" style="display: flex;text-align: center;">
      <!-- 试课状态： -->
          <!-- <text
                    :class="'redbagText'+detailsList.status">{{detailsList.status!=0?(detailsList.status==1?'待试课':'已试课'):'待提交'}}</text> -->
        <text>试课状态：</text>
      <text :class="'redbagText'+detailsList.status" v-if="statusText!='已建群'">{{statusText}}</text>
      <text class="redbagText4" v-if="statusText=='已建群'">{{statusText}}</text>
      </view class="mb-40">
      <view class="mb-40">创建时间：{{ detailsList.createTime }}</view>
      <view :class="detailsList.parentFeedback ? 'details' : ''">
        试课报告：
        <text style="color: #439286" @tap.stop="goResult(detailsList.status)">查看详情</text>
      </view>
      <view v-if="detailsList.parentFeedback != ''">
        <view class="mb-40" style="display: flex">
          家长满意度：
          <uni-rate :disabled="true" :size="19" :value="detailsList.parentScore" activeColor="#FFD400" />
        </view>
        <view class="mb-40 lh-50">
          家长反馈：
          <text class="content_wrap">{{ detailsList.parentFeedback }}</text>  
        </view>
        <view class="mb-40" style="display: flex">
          推荐人评分：
          <uni-rate :disabled="true" :size="19" :value="detailsList.referrerScore" activeColor="#FFD400" />
        </view>
        <view class="lh-50">
          推荐人评价：
          <text class="content_wrap">{{ detailsList.referrerFeedback }}</text>
        </view>
      </view>
    </view>

    <view class="shop_content p-30 mt-30" v-if="detailsList.status == 2 && detailsList.referrerFeedback == ''">
      <view class="mb-20 f-30">
        请您对本次试课满意度打分：
        <view class="mt-40">
          <uni-rate @change="onChange" :readonly="true" :size="19" :value="score" activeColor="#FFD400" />
        </view>
      </view>
      <view class="bg-f7 radius-15 p-30">
        <u--textarea v-model="feedback" :value="feedback" placeholder="写点反馈吧" placeholderStyle="color:#999;" height="240"></u--textarea>
      </view>
    </view>

    <view class="mt-30 plr-50" v-if="detailsList.status == 2 && detailsList.referrerFeedback == ''">
      <button class="phone-btn" @click="getFeedback()">提交反馈</button>
    </view>
  </view>
</template>
<script>
const { $showError, $showMsg, $http } = require('@/util/methods.js');
export default {
  data() {
    return {
      feedback: '', // 学习意愿
      ph: '', // 窗口高度
      svHeight: 0, // 暂无数据距离底边距离
      detailsList: [],
      score: 0, // 满意度
      anonymity: '',
      statusText: ''
    };
  },
  onReady() {
    let that = this;
    uni.getSystemInfo({
      //调用uni-app接口获取屏幕高度
      success(res) {
        that._data.pH = res.windowHeight; //windoHeight为窗口高度
        let titleH = uni.createSelectorQuery().select('.tips'); //想要获取高度的元素名（class/id）
        titleH
          .boundingClientRect((data) => {
            let pH = that._data.pH;
            that._data.svHeight = pH - (data != null ? data.top : 0); //计算高度：元素高度=窗口高度-元素距离顶部的距离（data.top）
          })
          .exec();
      }
    });
  },
  onShow() {},
  onLoad(e) {
    console.log(this.id);
    this.getReferrer(e.id);
    console.log(e, '=================');
    this.statusText = e.statusText ? e.statusText : '';
  },

  methods: {
    // 试课反馈下拉框
    choose(e) {
      this.recall = e;
    },

    goResult(val) {
      uni.navigateTo({
        url: '/Trialclass/result?expId=' + this.detailsList.expId
      });
    },
    onChange(e) {
      this.score = e.value;
      console.log(this.score);
    },
    //推荐试课详情
    async getReferrer(id) {
      let _this = this;
      uni.showLoading();
      const res = await $http({
        url: 'zx/exp/getReferrerInfoVo',
        data: {
          id: id
        }
      });
      uni.hideLoading();
      console.log(res);
      if (res) {
        _this.detailsList = res.data;
      }
    },
    //试课详情反馈
    async getFeedback() {
        
      let _this = this;
      console.log(_this.feedback);
      const res = await $http({
        url: 'deliver/app/feedback/referrerFeedback?expId=' + _this.detailsList.expId + '&referrerScore=' + _this.score + '&referrerFeedback=' + encodeURI(_this.feedback),
        method: 'POST'
      });
      console.log(res);
      if (res) {
        uni.navigateBack();
      }
    }
  }
};
</script>

<style lang="scss">
.flex_s {
  display: flex;
  align-items: center;
}

.box-160 {
  width: 140rpx;
  height: 140rpx;
}

.shop_content {
  background-color: #fff;
  border-radius: 14rpx;
}

.title {
  font-size: 32rpx;
  color: #000;
  margin-bottom: 17rpx;
  font-weight: 700;
}

.introduce {
  color: #333;
  font-size: 30rpx;
  line-height: 45rpx;
}

.details {
  margin-bottom: 40rpx;
}

.order {
  background-color: #fff;
  padding: 40rpx 30rpx;
  font-size: 30rpx;
  border-radius: 14rpx;
  color: #333;
}

/deep/.u-textarea__field {
  font-size: 28rpx !important;
}

.u-textarea {
  background-color: #f7f7f7 !important;
  padding: 0 !important;
}

.tips {
  position: relative;
  width: 100%;
}

/deep/.phone-btn {
  width: 100%;
  height: 90rpx;
  line-height: 90rpx;
  border-radius: 45rpx;
  font-size: 30rpx;
  color: #fff;
  background: linear-gradient(to bottom, #88cfba, #1d755c);
}

.content_wrap {
  white-space: pre-wrap;
}

text {
  word-wrap: break-word;
  word-break: normal;
}

/deep/.u-border {
  border: none !important;
}

.redbagText0 {
  color: #fff;
  border-radius: 4rpx;
  padding: 3rpx 7rpx;
  font-size: 26rpx;
  background-color: #e57126;
}

.redbagText1 {
  color: #fff;
  border-radius: 4rpx;
  padding: 3rpx 7rpx;
  font-size: 26rpx;
  background-color: #439286;
}

.redbagText2 {
  color: #fff;
  border-radius: 4rpx;
  padding: 3rpx 7rpx;
  font-size: 26rpx;
  background-color: #2dc032;
}
.redbagText3 {
  color: #fff;
  border-radius: 4rpx;
  padding: 3rpx 7rpx;
  font-size: 26rpx;
  background-color: rgb(143, 129, 254);
}
.redbagText4 {
  color: #fff;
  border-radius: 4rpx;
  padding: 3rpx 7rpx;
  font-size: 26rpx;
  background-color: rgb(253, 132, 66)
}
</style>
