import Util from '../util.js';
const { $showMsg } = require('./prompt');
import { httpUser } from '../luch-request/indexUser.js'; // 全局挂载引入
import { checkLogin, checkPhone, tabBarLinks, throttle, QQMapWXKey } from '../publicVariable';
/**
 * 验证的登录和手机号授权
 * @param {Function} callback 回调的方法
 */
const $confirmLogin = (callback) => {
  let that = this;
  let user_id = uni.getStorageSync('user_id') || false;
  let getPhone = uni.getStorageSync('getPhone') || false;

  if (!user_id && checkLogin) {
    uni.showModal({
      content: '请先授权后操作！',
      success: (res) => {
        if (res.confirm) {
          uni.navigateTo({
            url: '/pages/account/authorize/authorize'
          });
        }
      }
    });
    return false;
  }

  if (!getPhone && checkPhone) {
    uni.showModal({
      content: '请先授权手机号后操作！',
      success: (res) => {
        if (res.confirm) {
          uni.navigateTo({
            url: '/pages/account/phone/phone'
          });
        }
      }
    });
    return false;
  }

  callback && callback();
  return true;
};

/**
 * 跳转方法
 * @param {String || Object} e 跳转的url或者节点对象
 */
const $navigationTo = (e) => {
  console.log(e);
  let that = this;
  let url;
  let check = e.currentTarget?.dataset.check || false;
  if (check) {
    let confirmLogin = $confirmLogin();
    if (!confirmLogin) {
      return false;
    }
  } else {
    if (typeof e == 'string') {
      url = e;
    } else {
      url = e.currentTarget.dataset.url;
    }
  }
  if (!url || url.length == 0) {
    console.log('路径为空');
    return false;
  }
  console.log(tabBarLinks.indexOf(url));
  // tabBar页面
  if (tabBarLinks.indexOf(url) > -1) {
    uni.reLaunch({
      url: '/' + url
    });
  } else {
    // 普通页面
    uni.navigateTo({
      url: '/' + url
    });
  }
};

/**
 * 节流器
 * @param {Function} fn  回调的方法
 * @param {Number} timeout 节流器的间隔时间
 */
const $singleClick = (fn, timeout = 200) => {
  let that = this;
  if (throttle) {
    fn();
  } else {
    return false;
  }
  throttle = false;
  setTimeout(() => {
    throttle = true;
  }, timeout);
};

/**
 * 支付
 * @param {Object} option 调用支付的数据
 */
const $wxPayment = (options) => {
  return new Promise((resolve, reject) => {
    uni.requestPayment({
      timeStamp: options.timeStamp,
      nonceStr: options.nonceStr,
      package: 'prepay_id=' + options.prepay_id,
      signType: 'MD5',
      paySign: options.paySign,
      success(res) {
        resolve(res);
      },
      fail(err) {
        $showMsg('支付失败');
      }
    });
  });
};
/**
 * 通联支付
 * @param {Object} res 调用支付的数据
 */
const $payTlian = (res) => {
  let params = {
    appid: res.payInfo.appid,
    body: res.payInfo.body,
    cusid: res.payInfo.cusid,
    innerappid: res.payInfo.innerappid,
    notify_url: res.payInfo.notify_url,
    orgid: res.payInfo.orgid,
    paytype: res.payInfo.paytype,
    randomstr: res.payInfo.randomstr,
    reqsn: res.payInfo.reqsn,
    sign: res.payInfo.sign,
    signtype: res.payInfo.signtype,
    trxamt: res.payInfo.trxamt,
    validtime: res.payInfo.validtime,
    version: res.payInfo.version
  };
  uni.openEmbeddedMiniProgram({
    appId: 'wxef277996acc166c3',
    extraData: params
    // envVersion:'develop'
  });
};
/**
 * 支付返回
 * @param {Object} option 调用支付的数据
 */
const $tlpayResult = (fn, fn1, a) => {
  let options = uni.getEnterOptionsSync();
  let url = 'mps/order?orderId=' + a;
  if (options.scene == '1038' && options.referrerInfo.appId == 'wxef277996acc166c3') {
    // 代表从收银台小程序返回
    let extraData = options.referrerInfo.extraData;
    if (!extraData) {
      uni.showLoading();
      httpUser.get(url).then((res) => {
        if (res.data) {
          if (res.data.data && res.data.data.status == 3) {
            uni.showToast({
              icon: 'none',
              title: '支付成功'
            });
            fn();
          } else {
            uni.showToast({
              icon: 'none',
              title: '支付失败'
            });
            fn1();
          }
          uni.hideLoading();
        }
      });
      // "当前通过物理按键返回，未接收到返参，建议自行查询交易结果";
    } else {
      if (extraData.code == 'success') {
        uni.showToast({
          icon: 'none',
          title: '支付成功'
        });
        fn();
        // "支付成功";
      } else if (extraData.code == 'cancel') {
        fn1('cancel');
        uni.showToast({
          icon: 'none',
          title: '支付取消'
        });
        // "支付已取消";
      } else {
        uni.showToast({
          icon: 'none',
          title: '支付失败'
        });
        fn1();

        // "支付失败：" + extraData.errmsg;
      }
    }
  }
};

/**
 * app通联支付
 * @param {Object} res 调用支付的数据
 */
// #ifdef APP-PLUS
var zxUniModule = uni.requireNativePlugin('ZXUniModule'); // 获取 module
// #endif
const $appPayTlian = (res, payType) => {
  let params = {
    cusid: res.payInfo.cusid,
    appid: res.payInfo.appid,
    orgid: res.payInfo.orgid,
    version: res.payInfo.version,
    trxamt: res.payInfo.trxamt,
    reqsn: res.payInfo.reqsn,
    notify_url: res.payInfo.notify_url,
    body: res.payInfo.body,
    validtime: res.payInfo.validtime,
    randomstr: res.payInfo.randomstr,
    paytype: res.payInfo.paytype,
    sign: res.payInfo.sign,
    innerappid: res.payInfo.innerappid,
    signtype: res.payInfo.signtype
  };
  zxUniModule.openTLPayFunc(JSON.stringify(params), payType);
};
const $addAppPayGlobalEvtListener = (successFunc, failFunc, orderId) => {
  // #ifdef APP-PLUS
  let url = 'mps/order?orderId=' + orderId;
  function payCallBack(e) {
    console.log('---appPayCallBack--23333333');
    console.log(e);
    // uni.showToast({
    //   icon: 'none',
    //   title: JSON.stringify(e)
    // });
    if (!e) {
      // "当前通过物理按键返回，未接收到返参，建议自行查询交易结果";
      uni.showLoading();
      httpUser.get(url).then((res) => {
        if (res.data) {
          if (res.data.data && res.data.data.status == 3) {
            uni.showToast({
              icon: 'none',
              title: '支付成功'
            });
            successFunc();
          } else {
            uni.showToast({
              icon: 'none',
              title: '支付失败'
            });
            failFunc();
          }
          uni.hideLoading();
        }
      });
    } else {
      if (e.errorCode == 'success') {
        uni.showToast({
          icon: 'none',
          title: '支付成功'
        });
        successFunc();
      } else if (e.errorCode.indexOf('cancel') != -1) {
        failFunc('cancel');
        uni.showToast({
          icon: 'none',
          title: '支付取消'
        });
      } else {
        uni.showToast({
          icon: 'none',
          title: '支付失败'
        });
        failFunc();
      }
    }
  }
  plus.globalEvent.addEventListener('appPayCallBack', payCallBack);

  // 返回解除监听的方法
  return function removeEventListener() {
    plus.globalEvent.removeEventListener('appPayCallBack', payCallBack);
  };
  // #endif
};

const $appUseCoupon = (path, evtName) => {
  zxUniModule.appUseCoupon(path, evtName);
};
/// 2分享web  1分享小程序
const $appShare = (params, shareType) => {
  console.log(JSON.stringify(params), shareType, 'share');
  zxUniModule.appShare(params, shareType);
};

//打开其他小程序  
const $openOtherMini = (params) => {
  zxUniModule.openMiniProgram(params);
};

const $customerService = () => {
  zxUniModule.customerService();
};

const $goZxGoods = () => {
  zxUniModule.goZxGoods();
};
/**
 * 获取用户的地址
 */
// const $getloacltion = () => {
//   return new Promise((resolve,reject) => {
//     uni.getLocation({
//       type: 'gcj02',
//       success(res){
//         resolve(res)
//       },
//       fail(err){
//         $showMsg(err.errMsg)
//       }
//     })
//   })
// }

/**
 * 逆解析地址
 * @param {Object} params 包含经纬度的对象
 * @returns {Object}
 */
const $getAddress = (params) => {
  return new Promise((resolve, reject) => {
    let qqmapsdk = new QQMapWX({
      key: QQMapWXKey
    });
    const latitude = params.latitude;
    const longitude = params.longitude;
    qqmapsdk.reverseGeocoder({
      location: {
        latitude: latitude,
        longitude: longitude
      },
      success(res) {
        if (res.message === 'query ok') {
          uni.setStorageSync('address_info', res.result);
          uni.setStorageSync('user_address', res.result.ad_info.city);
          resolve(res);
        } else {
          uni.showToast({
            title: '获取地址失败',
            icon: 'none',
            mask: true
          });
          reject();
        }
      },
      fail(err) {
        $showMsg(err.errMsg);
      }
    });
  });
};

/**
 * 倒计时
 * @param {Array} end_arr 需要倒计时的时间戳,s
 * @returns {Object} 格式化后的倒计时数组
 */
const $countDown = (end_arr) => {
  let that = this;
  // 获取当前时间，同时得到活动结束时间数组
  let newTime = new Date().getTime() / 1000;
  let count_arr = [];
  end_arr.length &&
    end_arr.map((v) => {
      let time = endTime - newTime;
      if (time > 0) {
        // 获取天、时、分、秒
        let day = parseInt(time / (60 * 60 * 24));
        let hou = parseInt((time % (60 * 60 * 24)) / 3600);
        let min = parseInt(((time % (60 * 60 * 24)) % 3600) / 60);
        let sec = parseInt(((time % (60 * 60 * 24)) % 3600) % 60);
        count_arr.push({
          day: $numFormat(day),
          hou: $numFormat(hou),
          min: $numFormat(min),
          sec: $numFormat(sec)
        });
      } else {
        count_arr.push({
          day: '00',
          hou: '00',
          min: '00',
          sec: '00'
        });
      }
    });
  return count_arr;
};

/**
 * 格式化数字
 * @param {String} param 将数字格式化为两位数
 */
const $numFormat = (param) => {
  return param < 10 ? '0' + param : param;
};

/**
 * 处理大数字
 * @param {Number} num 处理大数字的显示
 * @param {Number} digit 保留的位数,默认一位
 */
const $setBigNum = (num, digit = 1) => {
  let that = this;
  if (num > 1000000) {
    num = (num / 1000000).toFixed(digit) + 'M';
  } else if (num > 10000) {
    num = (num / 10000).toFixed(digit) + 'W';
  } else {
    num = num;
  }
  return num;
};

/**
 * @param {Number} d 角度转弧度
 */
const $getRad = (d) => {
  return (d * Math.PI) / 180.0;
};

/**
 * 计算两个经纬度之间的直线距离
 * @param {Object} position1 地址一的经纬度
 * @param {Object} position2 地址二的经纬度
 */
const $getDistance = (position1, position2) => {
  let that = this;
  let lat1 = position1.lat;
  let lng1 = position1.lng;
  let lat2 = position2.lat;
  let lng2 = position2.lng;
  let EARTH_RADIUS = 6378137.0; //单位M
  let PI = Math.PI;
  let radLat1 = $getRad(lat1);
  let radLat2 = $getRad(lat2);
  let a = radLat1 - radLat2;
  let b = $getRad(lng1) - $getRad(lng2);

  let s = 2 * Math.asin(Math.sqrt(Math.pow(Math.sin(a / 2), 2) + Math.cos(radLat1) * Math.cos(radLat2) * Math.pow(Math.sin(b / 2), 2)));
  s = s * EARTH_RADIUS;
  s = Math.round(s * 10000) / 10000.0;

  return s;
};

/**
 * 解析二维码scene
 * @param {String} query 解析二维码等进入方式的scene值
 * @returns {Object} 返回对象
 */
const $getSceneData = (query) => {
  return query.scene ? Util.scene_decode(query.scene) : {};
};

/**
 * 处理分享参数
 * @param {Object}} params 拼接分享的参数,默认拼接上分享者的id
 * @returns {String} 返回拼接的字符串
 */
const $getShareUrlParams = (params) => {
  let that = this;
  return Util.urlEncode(
    Object.assign(
      {
        invitor_id: uni.getStorageSync('user_id') || 0
      },
      params
    )
  );
};

const $formatTimeTwo = (number, format) => {
  var formateArr = ['Y', 'M', 'D', 'h', 'm', 's'];
  var returnArr = [];
  var date = new Date(number);
  returnArr.push(date.getFullYear());
  returnArr.push(formatNumber(date.getMonth() + 1));
  returnArr.push(formatNumber(date.getDate()));
  returnArr.push(formatNumber(date.getHours()));
  returnArr.push(formatNumber(date.getMinutes()));
  returnArr.push(formatNumber(date.getSeconds()));
  for (var i in returnArr) {
    format = format.replace(formateArr[i], returnArr[i]);
  }
  return format;
};

const formatNumber = (n) => {
  n = n.toString();
  return n[1] ? n : '0' + n;
};
const $list = [
  {
    problem: '您的角色是......',
    type: 'radio',
    answer: [
      {
        lable: '爸爸',
        id: '1',
        url: 'dxSelect/fourthEdition/zx/<EMAIL>'
      },
      {
        lable: '妈妈',
        id: '2',
        url: 'dxSelect/fourthEdition/zx/<EMAIL>'
      },
      {
        lable: '爷爷/姥爷',
        id: '3',
        url: 'dxSelect/fourthEdition/zx/<EMAIL>'
      },
      {
        lable: '奶奶/姥姥',
        id: '4',
        url: 'dxSelect/fourthEdition/zx/<EMAIL>'
      },
      {
        lable: '其他',
        id: '5',
        url: 'dxSelect/fourthEdition/zx/btn_qita.png'
      }
    ]
  },
  {
    problem: '您的行业是......',
    type: 'radio',
    answer: [
      {
        lable: '政府',
        id: '1',
        url: 'dxSelect/fourthEdition/zx/<EMAIL>'
      },
      {
        lable: '互联网',
        id: '2',
        url: 'dxSelect/fourthEdition/zx/<EMAIL>'
      },
      {
        lable: '教育',
        id: '3',
        url: 'dxSelect/fourthEdition/zx/btn_jiaoyu.png'
      },
      {
        lable: '金融',
        id: '4',
        url: 'dxSelect/fourthEdition/zx/btn_jinrong.png'
      },
      {
        lable: '娱乐',
        id: '5',
        url: 'dxSelect/fourthEdition/zx/btn_yule.png'
      },
      {
        lable: '医疗',
        id: '6',
        url: 'dxSelect/fourthEdition/zx/btn_yiliao.png'
      },
      {
        lable: '房地产',
        id: '7',
        url: 'dxSelect/fourthEdition/zx/btn_fangdichan.png'
      },
      {
        lable: '服务',
        id: '8',
        url: 'dxSelect/fourthEdition/zx/btn_fuwu.png'
      },
      {
        lable: '自由职业',
        id: '9',
        url: 'dxSelect/fourthEdition/zx/btn_ziyou.png'
      },
      {
        lable: '全职带娃',
        id: '10',
        url: 'dxSelect/fourthEdition/zx/btn_quanzhi.png'
      },
      {
        lable: '其他',
        id: '11',
        url: 'dxSelect/fourthEdition/zx/btn_qita.png'
      }
    ]
  },
  {
    problem: '介绍一下您的孩子',
    type: 'filling',
    answer: [
      {
        lable: '男孩',
        id: '0',
        url: 'dxSelect/fourthEdition/zx/btn_nan.png'
      },
      {
        lable: '女孩',
        id: '1',
        url: 'dxSelect/fourthEdition/zx/btn_nv.png'
      }
    ]
  },
  {
    problem: '您孩子的成绩......',
    placeholder: '为了更加精确的规划，请仔细填写成绩',
    answer: []
  },
  {
    problem: '您的孩子有哪些特点......',
    placeholder: '选择孩子最突出的3个特点',
    type: 'checked',
    answer: [
      {
        lable: '诚实坚定',
        id: '1',
        url: 'dxSelect/fourthEdition/zx/btn_chengshi.png'
      },
      {
        lable: '助人为乐',
        id: '2',
        url: 'dxSelect/fourthEdition/zx/btn_zhuren.png'
      },
      {
        lable: '积极进取',
        id: '3',
        url: 'dxSelect/fourthEdition/zx/btn_jiji.png'
      },
      {
        lable: '情感丰富',
        id: '4',
        url: 'dxSelect/fourthEdition/zx/btn_qinggan.png'
      },
      {
        lable: '聪明机灵',
        id: '5',
        url: 'dxSelect/fourthEdition/zx/btn_congming.png'
      },
      {
        lable: '谨慎可靠',
        id: '6',
        url: 'dxSelect/fourthEdition/zx/btn_jingshen.png'
      },
      {
        lable: '乐观开朗',
        id: '7',
        url: 'dxSelect/fourthEdition/zx/btn_leguan.png'
      },
      {
        lable: '勇敢大方',
        id: '8',
        url: 'dxSelect/fourthEdition/zx/btn_yonggan.png'
      },
      {
        lable: '安静内向',
        id: '9',
        url: 'dxSelect/fourthEdition/zx/btn_anjing.png'
      }
    ]
  },
  {
    problem: '您最关注孩子哪些能力培养......',
    type: 'checked',
    answer: [
      {
        lable: '专注力',
        id: '1',
        url: 'dxSelect/fourthEdition/zx/btn_zhuanzhu.png'
      },
      {
        lable: '记忆力',
        id: '2',
        url: 'dxSelect/fourthEdition/zx/btn_jiyi.png'
      },
      {
        lable: '创新能力',
        id: '3',
        url: 'dxSelect/fourthEdition/zx/btn_chuangxin.png'
      },
      {
        lable: '沟通能力',
        id: '4',
        url: 'dxSelect/fourthEdition/zx/btn_goutong.png'
      },
      {
        lable: '抗压能力',
        id: '5',
        url: 'dxSelect/fourthEdition/zx/btn_kangya.png'
      },
      {
        lable: '独立能力',
        id: '6',
        url: 'dxSelect/fourthEdition/zx/btn_duli.png'
      }
    ]
  },
  {
    problem: '孩子的教育规划，您最关注的是......',
    type: 'checked',
    answer: [
      {
        lable: '幼小衔接',
        id: '1',
        url: 'dxSelect/fourthEdition/zx/btn_youxiaox.png'
      },
      {
        lable: '高考升学规划',
        id: '2',
        url: 'dxSelect/fourthEdition/zx/btn_gaokao.png'
      },
      {
        lable: '思维',
        id: '3',
        url: 'dxSelect/fourthEdition/zx/btn_si.png'
      },
      {
        lable: '英语',
        id: '4',
        url: 'dxSelect/fourthEdition/zx/btn_yingyu.png'
      },
      {
        lable: '学习提分',
        id: '5',
        url: 'dxSelect/fourthEdition/zx/btn_xuexi2.png'
      },
      {
        lable: '学习能力提升',
        id: '6',
        url: 'dxSelect/fourthEdition/zx/btn_xuexi.png'
      }
    ]
  }
];
module.exports = {
  $confirmLogin,
  $list,
  $navigationTo,
  $singleClick,
  $wxPayment,
  //   $getloacltion,
  $getAddress,
  $countDown,
  $numFormat,
  $setBigNum,
  $getDistance,
  $getSceneData,
  $getShareUrlParams,
  $formatTimeTwo,
  $tlpayResult,
  $payTlian,
  $appPayTlian,
  $addAppPayGlobalEvtListener,
  $appUseCoupon,
  $appShare,
  $openOtherMini,
  $customerService,
  $goZxGoods
};
