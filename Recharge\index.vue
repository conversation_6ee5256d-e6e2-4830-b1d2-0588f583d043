<template>
  <page-meta :page-style="'overflow:' + (show ? 'hidden' : 'visible')"></page-meta>
  <view class="">
    <view class="box-bg">
      <view class="positionAbsolute" style="left: 10rpx; top: 106rpx">
        <uni-icons type="left" size="22" color="#fff" @click="skintap('pages/home/<USER>/index')"></uni-icons>
      </view>
      <view style="margin: 110rpx 0 30rpx" class="t-c c-ff f-34">商户收款平台</view>
    </view>
    <view class="top-bgc">
      <view class="flex-s">
        <view class="c-fde f-30" v-if="schoolType != 3">
          门店剩余学时：
          <text class="c-ff">{{ classhour }}</text>
        </view>
        <view v-else></view>
        <view class="flex-a-c f-30">
          <!-- <view class="centertext-right c-f2 mr-30">充值记录</view> -->
          <view class="centertext-right c-f2" @click="skintap('Recharge/lessonDetails')">学时详情</view>
        </view>
      </view>
      <!-- 搜索框 -->
      <view class="mt-40 flex-s bg-ff radius-15 p-25">
        <view class="flex-a-c flex-box">
          <image :src="imgHost + 'dxSelect/recharge/search-icon.png'" class="search-icon mr-15"></image>
          <input v-model="searchVlaue" @blur="onblur" class="uni-input" placeholder="请输入家长联系方式或学员编号" />
        </view>
        <text class="search-text" @click="openSelect">搜索</text>
      </view>
    </view>
    <!-- 底部卡片 -->
    <view>
      <view class="single p-30">
        <view class="content plr-30 f-30 bg-ff">
          <form id="#nform">
            <view class="information ptb-20 border-bottom">
              <view style="width: 54%">学员姓名：</view>
              <view class="phone-input" :class="{ 'disabled-input': disabled }">
                <input style="width: 100%" v-model="studentName" name="studentName" placeholder="请输入家长联系方式或学员编号"
                  class="input c-66" disabled confirm-type="search" />
              </view>
            </view>
            <view class="information ptb-20 border-bottom">
              <view style="width: 54%">充值帐户：</view>
              <view class="phone-input" :class="{ 'disabled-input': disabled }">
                <input type="number" style="width: 100%" v-model="charging.studentCode" name="studentCode"
                  placeholder="请输入家长联系方式或学员编号" class="input c-66" disabled />
                <!-- <input type="number" style="width: 100%;" v-model="studentCode" name="studentCode"
									placeholder="请输入家长联系方式或学员编号" class="input c-66" maxlength="11" :disabled="disabled"/> -->
              </view>
            </view>
            <view class="information ptb-20 border-bottom" v-if="schoolType != 3">
              <view style="width: 54%">充值类型：</view>
              <view class="phone-input">
                <uni-data-select class="uni-select w100" v-model="value" :localdata="range" @change="selectType"
                  placeholder="请选择"></uni-data-select>
              </view>
            </view>
            <view v-if="value == 0">
              <view class="flex-a-c ptb-20 border-bottom" v-if="schoolType == 3">
                <view style="width: 38.2%">交付方式：</view>
                <uni-data-select v-model="deliverMode" :localdata="deliverOptions" @change="modeChage"
                  style="width: 180rpx"></uni-data-select>
              </view>
              <view class="flex-a-c ptb-20 border-bottom" v-if="schoolType == 3">
                <view style="width: 38.2%">学段：</view>
                <uni-data-select v-if="deliverMode" v-model="level" :localdata="levelOptionsList" @change="levelChage"
                  style="width: 180rpx"></uni-data-select>
                <view class="flex-s ptb-15" style="justify-content: space-between; flex: 1" v-if="!deliverMode"
                  @click="levelChoose">
                  <view style="color: #6a6a6a" class="f-28 mr-55">请选择</view>
                  <uni-icons type="bottom" size="20" color="#999"></uni-icons>
                </view>
              </view>
              <view class="information ptb-20 border-bottom">
                <view style="width: 54%">充值学时：</view>
                <view class="phone-input">
                  <input type="number" v-model="charging.courseLength" name="mobile" placeholder="请输入"
                    class="input c-00" maxlength="11" @blur="coreseblur" />
                  <text class="c-00">节</text>
                </view>
              </view>
              <view class="information ptb-20 border-bottom" v-if="schoolType == 3">
                <view style="width: 54%">充值交付学时：</view>
                <view class="phone-input">
                  <input type="number" v-model="charging.deliverCourseLength" name="mobile" placeholder="请输入"
                    class="input c-00" maxlength="11" @blur="deliverblur" />
                  <!-- <input type="number" v-model="charging.deliverCourseLength" name="mobile"
										v-if="selfShow" placeholder="请输入" class="input c-00" maxlength="11"
										@blur="deliverblur" disabled /> -->
                  <text class="c-00">节</text>
                </view>
              </view>

              <view class="ptb-20 border-bottom" v-if="schoolType == 3">
                <view class="information ">
                  <view style="width: 54%">学时单价：</view>
                  <view class="phone-input">
                    <input v-model="charging.coursePrice" type="number" placeholder="输入学时数量自动带出" class="input c-00 w100"
                      disabled />
                  </view>
                </view>
                <view class="title">
                  温馨提示:课时单价中10.5元为学员运营费，非合伙人收益
                </view>
              </view>

              <view class="ptb-20 border-bottom" v-if="schoolType != 3">
                <view class="information  ">
                  <view style="width: 54%">学时单价：</view>
                  <view class="phone-input">
                    <input v-model="charging.coursePrice" type="number" placeholder="请输入" class="input c-00 w100"
                      @blur="blurPrice" />
                  </view>
                </view>
                <view class="title">
                  温馨提示:课时单价中10.5元为学员运营费，非合伙人收益
                </view>
              </view>

              <view class="information ptb-20 border-bottom" v-if="schoolType == 3">
                <view style="width: 54%">交付学时单价：</view>
                <view class="phone-input">
                  <input v-model="charging.deliverCoursePrice" type="number" placeholder="输入交付学时数量自动带出"
                    class="input c-00 w100" disabled />
                </view>
              </view>
              <view class="information ptb-20 border-bottom">
                <view style="width: 54%">合计金额：</view>
                <view class="phone-input" :class="charging.totalPrice ? 'c-fea' : ''">
                  {{ charging.totalPrice || '输入充值学时自动带出' }}
                  <!-- <input type="number" style="width: 100%;" placeholder="输入学时与学时单价自动带出" class="input c-fea" maxlength="11"
										:disabled="disabled"  /> -->
                </view>
              </view>
            </view>

            <view v-if="value == 1">
              <view class="flex-a-c ptb-40 border-bottom">
                <view style="width: 38.2%">分类：</view>
                <view class="c-66">鼎英语</view>
              </view>

              <view class="flex flex-x-b ptb-20 border-bottom">
                <view>课程：</view>
                <uni-data-select v-model="index" :localdata="courselist" @change="changeClass"
                  style="width: 120rpx"></uni-data-select>
                <uni-data-select v-model="val" :localdata="classdetailList" @change="changeSubject"
                  style="width: 300rpx"></uni-data-select>
              </view>

              <view class="flex-x-b ptb-40 border-bottom">
                <view>包含的开通课程：</view>
                <view class="c-99 course" v-for="(item, index) in openClassList" :key="index">
                  {{ '课程' + Number(index + 1) + ':' + item.courseName }}
                </view>
              </view>

              <view class="ptb-40 border-bottom f-30 flex-s">
                <view>合计金额：</view>
                <view>
                  <text class="mr-30 c-fea">{{ totalPrice ? totalPrice : '' }}</text>
                  元
                </view>
              </view>
            </view>
            <view class="information ptb-20" v-if="value == 0">
              <view style="width: 54%">充值说明：</view>
              <view class="phone-input">
                <input v-model="charging.remark" name="mobile" placeholder="请输入" class="input c-00" maxlength="20" />
              </view>
            </view>

            <view class="ptb-40 flex-s" v-if="value == 1">
              <view style="width: 54%">课程结算方式：</view>
              <view class="c-66">不限制学时</view>
            </view>
          </form>
        </view>

        <!-- 选择支付方式卡片 -->
        <view class="bg-ff ptb-40 radius-15 plr-30 f-30" v-if="value == 0 || value == 1">
          <!-- <view class="payment-title" v-if="schoolType==3">
						选择支付方式
					</view> -->
          <!-- <view class="logo-bottom" v-if="schoolType==3">
						<view class="logo-content">
							<image src="https://document.dxznjy.com/dxSelect/image/weiimg.png" class="logo-img"></image>
							<text>微信</text>
						</view>
						<view>
							<image v-if="radioWeChat==1" :src="imgHost+'dxSelect/recharge/payment-icon1.png'"
								class="img-icon" @click="anmendRadio(1)"></image>
							<image v-else :src="imgHost+'dxSelect/recharge/payment-icon2.png'" class="img-icon"
								@click="anmendRadio(1)"></image>
						</view>
					</view> -->
          <!-- <view class="logo-bottom">
						<view class="logo-content">
							<image src="https://document.dxznjy.com/dxSelect/image/Alipay.png" class="logo-img"></image>
							<text>支付宝</text>
						</view>
						<view>
							<image v-if="radioAlipay==1" :src="imgHost+'dxSelect/recharge/payment-icon1.png'" class="img-icon" @click="anmendRadio(2)"></image>
							<image v-else :src="imgHost+'dxSelect/recharge/payment-icon2.png'" class="img-icon" @click="anmendRadio(2)"></image>
						</view>
					</view> -->

          <view class="mt-80">
            <button class="phone-btn" @click="getPayExpReward">确定</button>
            <!-- <view v-show="showInput">
							<input type="password" @focus="showKeyboard" @blur="hideKeyboard" />
						</view> -->
          </view>
        </view>
      </view>
    </view>
    <!-- 选择学员弹窗 -->
    <uni-popup ref="popup" type="center" @change="change">
      <view class="dialogBG">
        <view class="reviewCard_box positionRelative">
          <view class="cartoom_image">
            <image :src="dialog_iconUrl" mode="widthFix"></image>
          </view>
          <view class="review_close" @click="closeDialog">
            <uni-icons type="clear" size="28" color="#B1B1B1"></uni-icons>
          </view>
          <view class="reviewCard">
            <view class="reviewTitle bold">选择学员</view>
            <view class="dialogContent" v-for="(item, index) in studentList" @click="chooseStudent(item, index)"
              :class="isactive == index ? 'addclass' : 'not-selected'">
              {{ item.realName }}
              <text style="margin-left: 20rpx">({{ item.studentCode }})</text>
            </view>
            <!-- 底部确定和取消按钮 -->
            <view class="mask-footer">
              <button class="confirm-button mask-left" @click="confirm()">确定</button>
              <button class="cancel-button mask-right" @click="cancellation()">取消</button>
            </view>
          </view>
        </view>
      </view>
    </uni-popup>
    <!-- 交易密码弹窗 -->
    <paypass ref="yspay" @cancel="cancelpay" @submit="submitpay"></paypass>
    <!-- 充值成功弹窗 -->
    <uni-popup ref="successFn" type="center" @change="change">
      <view class="dialogBG">
        <view class="reviewCard_box positionRelative">
          <view class="successFn-img">
            <image src="https://document.dxznjy.com/applet/newimages/xiezi.png"></image>
          </view>
          <view class="review_close" @click="closeDialog">
            <uni-icons type="clear" size="28" color="#B1B1B1"></uni-icons>
          </view>
          <view class="reviewCard">
            <view class="reviewTitle bold">充值成功</view>
            <view class="successFnContent">【鼎校智能英语】家长您好！学员王五五已在鼎校经开区门店成功购买鼎英语15学时，若您还未添加课程学管师，请扫描下方二维码添加</view>
            <view class="drcode"></view>
            <view class="successFnbtn">
              <button @click="goUrlcoll">保存相册并分享给家长</button>
            </view>
          </view>
        </view>
      </view>
    </uni-popup>

    <!-- 没有学员提示 -->
    <uni-popup ref="notifyPopup" type="top" mask-background-color="rgba(0,0,0,0)">
      <view class="plr-60">
        <view class="t-c bg-ff flex-c ptb-25 radius-50 mt-80 notify">
          <image :src="imgHost + 'dxSelect/recharge/hint.png'" class="hint-icon"></image>
          <view class="f-34 ml-15">暂无学员，请重新搜索</view>
        </view>
      </view>
    </uni-popup>

    <!-- 试课奖励充值 -->
    <uni-popup ref="rewardPopup" type="center">
      <view class="dialogBG">
        <view class="reviewCard_box positionRelative">
          <view class="cartoom_image">
            <image :src="dialog_iconUrl" mode="widthFix"></image>
          </view>
          <view class="review_close" @click="closeDxnDialog">
            <uni-icons type="clear" size="30" color="#B1B1B1"></uni-icons>
          </view>
          <view class="reviewCard t-c">
            <view class="reviewTitle bold pb-20">支付试课奖励</view>
            <view class="mt-40">由于该学员有试课并且是首次充值，</view>
            <view>
              需
              <text class="c-fea mt-15">支付{{ amount }}元</text>
              的试课奖励，才能充值~
            </view>
            <view class="flex-c mt-20">
              支付状态：
              <view class="waitPay c-ff f-26">等待支付</view>
            </view>
            <view class="flex-s mt-50">
              <view class="review_btn" @click="changePay">立即支付</view>
              <view class="close_btn" @click="closeDxnDialog">取消</view>
            </view>
          </view>
        </view>
      </view>
    </uni-popup>

    <uni-popup ref="successPopup" type="center" mask-background-color="rgba(0,0,0,0)">
      <view class="flex-c success_content">
        <view class="bg-ff radius-50 ptb-30 plr-40 flex-c success_shadow" style="width: 540rpx">
          <image :src="imgHost + 'dxSelect/recharge/pay-success.png'" class="green_yes"></image>
          <text class="c-33 f-32 ml-20">试课奖励充值成功</text>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script>
  import {
    $tlpayResult
  } from '@/util/methods/common.js';
  import Util from '@/util/util.js';
  import paypass from './components/ys-paypass/ys-paypass.vue';
  const {
    $navigationTo,
    $getSceneData,
    $showError,
    $showMsg,
    $http
  } = require('@/util/methods.js');
  const {
    httpUser
  } = require('@/util/luch-request/indexUser.js');
  export default {
    components: {
      paypass
    },
    data() {
      return {
        payInfo: {},
        flag1: false,
        title: 'Hello',
        showInput: false,
        // 充值类型
        value: 0,
        range: [{
            value: 0,
            text: '充值软件使用费'
          },
          {
            value: 1,
            text: '充值课程包'
          }
        ],
        show: false, // 禁止穿透
        isactive: -1, // 选中索引
        studentCode: '',
        studentName: '', // 页面选中学员姓名
        studentList: [], // 学员列表
        StudentCodeKey: '',
        realName: '', // 弹窗点击选中的学员
        disabled: false, //输入框禁用

        radioWeChat: '',
        radioAlipay: '',
        imgHost: getApp().globalData.imgsomeHost,
        payInfo: {},
        flag1: false,
        index: 0,
        val: 0,
        searchVlaue: '', // 搜索内容
        querylist: '', // 查询列表

        classhour: 0, // 门店剩余学时
        codeShow: false, // 默认输入框搜索的不是学员code
        courselist: [], // 课程分类
        classdetailList: [],
        openClassList: {}, // 开通课程

        subjectList: '',
        schoolType: '', // 3新门店 其余的老门店

        level: '',
        periodlist: [], // 学段列表
        levelOptionsList: [{
            value: 1,
            text: '小学'
          },
          {
            value: 2,
            text: '初中'
          },
          {
            value: 3,
            text: '高中'
          }
        ],

        deliverMode: '',
        deliverOptions: [{
            value: 'CENTER',
            text: '集中交付'
          }
          // { value: "SELF", text: "自行交付" },
        ],
        chooseClass: '', // 选择课程
        // 充值信息
        charging: {
          course: '', // 门店剩余学时
          level: '', //学段
          studentCode: '', // 充值账户
          courseLength: '', // 充值学时
          coursePrice: '', // 学时单价
          sumCoursePrice: '', // 学时总价
          deliverCourseLength: '', // 交付学时
          deliverCoursePrice: '', // 交付学时单价
          sumDeliverPrice: '', // 交付学时总价
          totalPrice: '', // 总金额
          remark: '', // 充值说明
          deliverMode: '', //
          toAccountCourse: '', // 充值学时金额
          selfDeliverCourseLength: '' //
        },
        type: '', // 支付类型

        lessonPackage: {
          categoryId: ['0'],
          packageType: 1,
          productId: '', // 课程
          skuId: '', // 详细课程
          studentCode: ''
        },
        totalPrice: '', // 学时包总金额
        selfShow: false, // 是否是自行交付
        flag: false,
        amount: '', // 充值试课奖励金额
        isFirsyPay: false,

        dialog_iconUrl: Util.getCachedPic('https://document.dxznjy.com/dxSelect/dialog_icon.png', 'dialog_icon_path'),
        app: 0,
        userinfo: {},
        removeListener: null
      };
    },
    onLoad(options) {
      // options = {
      //   schoolType: 3,
      //   merchantCode: '*********',
      //   paytoken:
      //     'eyJraWQiOiJjNTMyYjQ0MC1mNTRiLTRjNGQtYWIyNC1hNGMxNzdlOGQwYjgiLCJhbGciOiJSUzI1NiJ9.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.SWc5GgKXEsqE8w-mD43osMEQrAYTJoYyMQeGk9DwYzbpQ-F-Yp5PYYNxlggSRzENAC8oHI95grjsiuG8eANG-a47T7WKn3wQafGPt_fdBXy-4oYm9j_F62J7y0t-1X99V_Jx-Hj6m24ZqSlHlZ8QsG2PooKTAG7GH193mCXyoHfaPjH4Day4rzyYH2Mg9ka9HTOlXwtHIcdvOI5qHn8NVHnHX60sMPb5-iBzBrkOhYwrrC8D4mBvOREx-_P4JkSq5cokcTLArQ-Zsjt6xN62ZBO2MKxjR5Ap3UjaQbVWuyUpQkaABeGsSkk2EQhtYvV-yjENVRfsgpSv3pr6T6x0dg',
      //   app: 2,
      //   token:
      //     '**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************',
      //   identityType: 0,
      //   userId: '1293315282270294016',
      //   userCode: '921120999',
      //   phone: '18956036397'
      // };
      console.log('触发onLoad');
      if (options.token) {
        this.app = options.app;
        this.$handleTokenFormNative(options);
        // // #ifdef APP-PLUS
        // this.removeListener = uni.$addAppPayGlobalEvtListener(this.sucees, this.fail, this.payInfo.orderId, this.flag1);
        // // #endif
      }
      this.getUserInfoData();
      this.schoolType = options.schoolType;
    },

    //监听页面离开的话，下面两个必须同时使用，因为离开页面会有两种情况
    onHide() {
      uni.removeStorage({
        key: 'payToken',
        success: (res) => {
          console.log('删除payToken');
        }
      });
    },
    //监听离开页面
    onUnload() {
      // #ifdef APP-PLUS
      if (this.app) {
        plus.runtime.quit();
      }
      // #endif
      uni.removeStorage({
        key: 'payToken',
        success: (res) => {
          console.log('删除payToken');
        }
      });
    }, //监听页面销毁
    beforeDestroy() {
      // #ifdef APP-PLUS
      if (this.removeListener) {
        this.removeListener();
      }
      // #endif
    },
    async onShow() {
      if (this.flag1) {
        // #ifdef MP-WEIXIN
        uni.$tlpayResult(this.sucees, this.fail, this.payInfo.orderId);
        // #endif
      }
      // this.$refs.rewardPopup.open();
      await this.getPayToken();
      let clearData = uni.getStorageSync('clearData');
      if (clearData) {
        await this.blank();
      }
      uni.removeStorage({
        key: 'clearData',
        success: (res) => {
          console.log('删除clearData');
        }
      });
      this.getStoreClassHour();
      // this.getNewsStore();
      this.getCourse();
      this.newPartner();
      // this.getPeriod();
    },

    methods: {
      // 判断是否是新合伙人
      async newPartner() {
        let res = await $http({
          url: 'znyy/school/currentAdmin'
        });
        // console.log(res,22222222);
        if (res.data.closeSelfDeliver) {
          this.deliverOptions = [{
            value: 'CENTER',
            text: '集中交付'
          }];
        } else {
          this.deliverOptions = [{
              value: 'CENTER',
              text: '集中交付'
            },
            {
              value: 'SELF',
              text: '自行交付'
            }
          ];
        }
      },
      sucees() {
        this.flag1 = false;
        this.$refs.successPopup.open();
		this.blank(); // 清除页面填写信息
		let that = this;
		setTimeout(() => {
			that.$refs.successPopup.close();
		}, 3000)
      },
      fail() {
        this.flag1 = false;
      },
      fails() {
        uni.showToast({
          title: '支付失败',
          icon: 'none',
          duration: 2000
        });
        this.flag1 = false;
      },
      blank() {
        this.level = '';
        this.deliverMode = '';
        this.charging.course = '';
        this.charging.level = '';
        this.charging.studentCode = '';
        this.charging.courseLength = '';
        this.charging.coursePrice = '';
        this.charging.sumCoursePrice = '';
        this.charging.deliverCourseLength = '';
        this.charging.deliverCoursePrice = '';
        this.charging.sumDeliverPrice = '';
        this.charging.totalPrice = '';
        this.charging.remark = '';
        this.charging.deliverMode = '';
        this.charging.toAccountCourse = '';
        this.charging.selfDeliverCourseLength = '';
        this.index = 0;
        this.val = 0;

        this.searchVlaue = '';
        this.studentName = '';
        this.disabled = false;

        this.lessonPackage.productId = '';

        this.lessonPackage.skuId = '';
        this.lessonPackage.studentCode = '';
        this.totalPrice = '';
        this.value = 0;
      },
      // 点击返回上一页
      skintap(url) {
        $navigationTo(url);
      },
      async getPayToken() {
        let token = uni.getStorageSync('token');
        let data = {
          memberToken: token
        };
        let res = await this.$httpUser.get('new/security/login/school/member/token', data);
        console.log(res);
        uni.setStorageSync('payToken', res.data.data.token);
      },
      // 门店剩余学时
      async getStoreClassHour() {
        let res = await $http({
          url: 'znyy/areas/student/get/account',
          method: 'get'
        });
        if (res) {
          this.classhour = res.data.course;
          this.charging.course = res.data.course;
        }
      },

      // 查询学员
      async openSelect() {
        let that = this;
        if (that.searchVlaue == '') {
          that.$util.alter('搜索信息不能为空');
          return;
        }
        // /znyy/areas/student/getStudentCourseHours?value=***********&merchantCode=*********
        uni.showLoading();
        let res = await $http({
          url: 'znyy/areas/student/getStudentInfo',
          method: 'get',
          data: {
            value: that.searchVlaue
          }
        });
        uni.hideLoading();
        if (res.data.length > 0) {
          that.studentList = res.data;
          if (that.studentList.length > 1) {
            this.$refs.popup.open();
          } else {
            this.charging.studentCode = this.studentList[0].studentCode;
            this.lessonPackage.studentCode = this.studentList[0].studentCode;
            this.studentName = this.studentList[0].realName;
            this.disabled = true; //输入框禁用
            this.isDisabled = !this.isDisabled; //输入框变成灰色
            this.getPeriod();
            console.log(11111);
            this.getIsFirstPay();
            // this.getStudentCourseHours(this.studentList[0].studentCode,this.studentList[0].memberCode)
          }
        } else {
          this.$refs.notifyPopup.open();
          setTimeout(() => {
            this.$refs.notifyPopup.close();
          }, 1500);
        }
      },
      // async getStudentCourseHours(searchVlaue,memberCode){
      // 	let that = this
      // 	let res = await $http({
      // 		url: 'znyy/areas/student/getStudentCourseHours?value='+searchVlaue+'&merchantCode='+memberCode,
      // 		method: 'get',
      // 		data: {}
      // 	})
      // 	if(res){
      // 		// if(res.data[0].haveCourseHours){
      // 		// 	this.haveCourseHours=res.data[0].haveCourseHours-0||100
      // 		// 	this.oldDivliverPirce=(res.data[0].deliverCoursePrice-0)||100
      // 		// }else {
      // 		// 	this.haveCourseHours=0
      // 		// }
      // 	}
      // },

      onblur(e) {},
      // 选择学员
      chooseStudent(item, index) {
        this.isactive = index;
      },
      // 选择学员弹窗确定按钮
      async confirm() {
        if (this.isactive == -1) {
          this.$util.alter('请先选择学员');
          return;
        }
        if (this.studentName != '') {
          this.level = '';
          this.deliverMode = '';
          this.charging.course = '';
          this.charging.level = '';
          this.charging.studentCode = '';
          this.charging.courseLength = '';
          this.charging.coursePrice = '';
          this.charging.sumCoursePrice = '';
          this.charging.deliverCourseLength = '';
          this.charging.deliverCoursePrice = '';
          this.charging.sumDeliverPrice = '';
          this.charging.totalPrice = '';
          this.charging.remark = '';
          this.charging.deliverMode = '';
          this.charging.toAccountCourse = '';
          this.charging.selfDeliverCourseLength = '';
          this.index = 0;
          this.val = 0;

          this.studentName = '';
          this.disabled = false;

          this.lessonPackage.productId = '';

          this.lessonPackage.skuId = '';
          this.lessonPackage.studentCode = '';
          this.totalPrice = '';
          this.value = 0;
        }

        this.studentName = this.studentList[this.isactive].realName;
        this.charging.studentCode = this.studentList[this.isactive].studentCode;
        this.lessonPackage.studentCode = this.studentList[this.isactive].studentCode;
        // this.getStudentCourseHours(this.studentList[this.isactive].studentCode,this.studentList[this.isactive].memberCode)
        this.disabled = true; //输入框禁用
        this.isDisabled = !this.isDisabled; //输入框变成灰色
        this.$refs.popup.close();
        this.getPeriod();
        this.isactive = -1;
        this.getIsFirstPay();
      },

      // 课程分类
      async getCourse() {
        this.courselist = [];
        let res = await $http({
          url: 'znyy/course/product/by/categoryId',
          method: 'get',
          data: {
            categoryId: 1
          }
        });
        if (res) {
          res.data.forEach((item) => {
            let d = {
              text: item.productName,
              value: item.id
            };
            this.courselist.push(d);
          });
        }
      },

      async getLesson(val) {
        this.openClassList = [];
        this.classdetailList = [];
        let res = await $http({
          url: 'znyy/course/product/sku',
          method: 'get',
          data: {
            productId: val
          }
        });
        if (res) {
          this.subjectList = res.data;
          res.data.forEach((item) => {
            let d = {
              text: item.name,
              value: item.id
            };
            this.classdetailList.push(d);
          });
        }
      },

      //查询
      nameSearch(e) {
        // if (this.studentName.trim() || this.studentName == '') {
        // 	this.loadData()
        // }
        // if (this.trialname.trim() || this.trialname == '') {
        // 	this.loadData()
        // }
      },
      // 是否需要收款选中
      radioChange(e) {
        if ((this.studentName && this.studentName.trim() !== '') || !this.studentName) {
          this.loadData();
        }

        if ((this.trialname && this.trialname.trim() !== '') || !this.trialname) {
          this.loadData();
        }
      },
      // 取消按钮
      cancellation() {
        this.$refs.popup.close();
      },
      // 充值类型选择
      selectType(e) {
        if (e == 1) {
          this.getCourse();
        }
        this.level = '';
        this.deliverMode = '';
        this.charging.level = '';
        this.charging.courseLength = '';
        this.charging.coursePrice = '';
        this.charging.sumCoursePrice = '';
        this.charging.deliverCourseLength = '';
        this.charging.deliverCoursePrice = '';
        this.charging.sumDeliverPrice = '';
        this.charging.totalPrice = '';
        this.charging.remark = '';
        this.charging.deliverMode = '';
        this.charging.toAccountCourse = '';
        this.charging.selfDeliverCourseLength = '';
        this.index = 0;
        this.val = 0;
        this.lessonPackage.productId = '';
        this.lessonPackage.skuId = '';
        this.lessonPackage.studentCode = this.charging.studentCode;
        this.totalPrice = '';
      },

      // 选择课程
      changeClass(e) {
        this.val = '';
        this.lessonPackage.productId = '';
        this.lessonPackage.skuId = '';
        this.totalPrice = '';
        this.openClassList = [];
        if (e != '') {
          this.getLesson(e);
          this.lessonPackage.productId = e;
        } else {
          this.openClassList = [];
          this.classdetailList = [];
        }
      },

      // 课程选择
      changeSubject(e) {
        console.log(e);
        this.lessonPackage.skuId = e;
        this.totalPrice = '';
        this.openClassList = [];
        for (let i = 0; i < this.subjectList.length; i++) {
          if (e == this.subjectList[i].id) {
            this.openClassList = this.subjectList[i].openAttributeList;
            this.totalPrice = Number.parseFloat(this.subjectList[i].price) / 100.0;
          }
        }
      },

      // 禁止穿透滚动
      change(e) {
        this.show = e.show;
      },
      // 选择学员关闭图标
      closeDialog() {
        this.$refs.popup.close();
        this.$refs.successFn.close();
        this.isactive = -1;
        this.charging.studentCode = '';
        this.studentName = '';
      },

      // 选择学段
      changePeriod(e) {
        this.levelOptionsList.forEach((item) => {
          if (e == item.value) {
            this.chooseClass = item;
          }
        });
        this.charging.coursePrice = e.split('#')[1]; //获取#/之后的字符串
        // this.charging.deliverCoursePrice = this.chooseClass.ext;
        this.charging.level = e.split('#')[0];
      },

      modeChage(item) {
        if (!this.charging.studentCode) {
          this.$util.alter('请先选择充值账户');
          this.deliverMode = '';
          if (this.charging.deliverMode == 'SELF') {
            this.selfShow = true;
            this.saveShowFillBtn(this.isFirsyPay && false);
          } else {
            this.selfShow = false;
            this.saveShowFillBtn(this.isFirsyPay && true);
          }
          return;
        }
        if (item == '') {
          this.$nextTick(() => {
            this.level = '';
            this.deliverMode = '';
            this.charging.level = '';
            this.charging.courseLength = '';
            this.charging.coursePrice = '';
            this.charging.sumCoursePrice = '';
            this.charging.deliverCourseLength = '';
            this.charging.deliverCoursePrice = '';
            this.charging.sumDeliverPrice = '';
            this.charging.totalPrice = '';
            this.charging.deliverMode = '';
            this.charging.toAccountCourse = '';
            this.charging.selfDeliverCourseLength = '';
          });
          this.$forceUpdate();
        }
        this.charging.deliverMode = item;
        if (this.charging.deliverMode == 'SELF') {
          this.selfShow = true;
          this.saveShowFillBtn(this.isFirsyPay && false);
        } else {
          this.selfShow = false;
          this.saveShowFillBtn(this.isFirsyPay && true);
        }
        this.levelChage(this.level);
        // e.detail.value
        if (this.charging.courseLength) {
          this.coreseblur({
            detail: {
              value: this.charging.courseLength
            }
          });
        }
      },

      levelChoose() {
        this.$util.alter('请先选择交付方式');
        this.level = '';
        this.charging.level = '';
        this.charging.courseLength = '';
        this.charging.coursePrice = '';
        this.charging.sumCoursePrice = '';
        this.charging.deliverCourseLength = '';
        this.charging.deliverCoursePrice = '';
        this.charging.sumDeliverPrice = '';
        this.charging.totalPrice = '';
        this.charging.deliverMode = '';
        this.charging.toAccountCourse = '';
        this.charging.selfDeliverCourseLength = '';
      },
      levelChage(item) {
        if (item == '') {
          this.level = '';
          this.charging.level = '';
          this.charging.courseLength = '';
          this.charging.coursePrice = '';
          this.charging.sumCoursePrice = '';
          this.charging.deliverCourseLength = '';
          this.charging.deliverCoursePrice = '';
          this.charging.sumDeliverPrice = '';
          this.charging.totalPrice = '';
          this.charging.toAccountCourse = '';
          this.charging.selfDeliverCourseLength = '';
        }
        let val = item;
        if (this.level && this.charging.courseLength) {
          this.getPrice();
        }
      },
      getPrice() {
        var obj;
        this.periodlist.forEach((e) => {
          if (this.charging.courseLength >= e.startNum - 0) {
            if (this.charging.courseLength <= e.endNum - 0) {
              obj = e;
            }
          }
        });
        if (!obj) {
          if (this.charging.courseLength <= this.periodlist[0].startNum - 0) {
            obj = this.periodlist[0];
          } else {
            obj = this.periodlist[this.periodlist.length - 1];
          }
        }
        const info = obj.selectResultList[this.level - 1];
        const values = info.value.split('#');
        this.charging.level = values[0];
        this.charging.coursePrice = values[1];
        const deliverConfig = JSON.parse(info.ext);
        if (this.deliverMode == 'SELF') {
          let deliverCoursePrice = deliverConfig.SELF / 100;
          this.charging.deliverCoursePrice = deliverCoursePrice;
        } else {
          let deliverCoursePrice = deliverConfig.CENTER / 100;
          this.charging.deliverCoursePrice = deliverCoursePrice;
        }
        if (this.charging.deliverCourseLength > 0 || this.charging.deliverCourseLength == '0') {
          let price = Number(this.charging.coursePrice * this.charging.courseLength) + Number(this.charging
            .deliverCoursePrice * this.charging.deliverCourseLength);
          this.charging.totalPrice = price.toFixed(2);
        }
      },

      // 老门店
      blurPrice(e) {
        this.charging.sumCoursePrice = (this.charging.courseLength * e.detail.value).toFixed(2);
        this.charging.totalPrice = this.charging.sumCoursePrice;
      },

      // 学时充值
      coreseblur(e) {
        let num = e.detail.value;
        this.charging.toAccountCourse = Number(num);
        if (this.schoolType == 3) {
          this.charging.sumCoursePrice = (num * Number(this.charging.coursePrice)).toFixed(2);
          if (this.schoolType != 3) {
            this.charging.totalPrice = this.charging.sumCoursePrice.toFixed(2);
          } else {
            // if (this.deliverMode == 'SELF') {
            // 	this.charging.deliverCourseLength = num;
            // 	this.charging.sumDeliverPrice = (this.charging.deliverCourseLength * this.charging
            // 		.deliverCoursePrice);
            // 	let price = Number(this.charging.sumCoursePrice) + Number(this.charging.sumDeliverPrice)
            // 	this.charging.totalPrice = price.toFixed(2);
            // }
          }
        }
        if (this.level && this.charging.courseLength) {
          this.getPrice();
        }
      },
      deliverblur(e) {
        let num = e.detail.value;
        this.charging.sumDeliverPrice = (num * Number(this.charging.deliverCoursePrice)).toFixed(2);
        let price = Number(this.charging.sumCoursePrice) + Number(this.charging.sumDeliverPrice);
        this.charging.totalPrice = price.toFixed(2);
        console.log(this.charging.totalPrice);
        if (this.level && this.charging.courseLength) {
          this.getPrice();
        }
      },

      // 清空搜索框
      clear() {},
      cancelpay() {},
      // 支付输入密码后
      submitpay(ex) {
        console.log('支付密码为：' + ex.value);
        this.$refs.successFn.open('center');
      },

      anmendRadio(value) {
        console.log(value);
        this.radioWeChat = 1;
        this.type = 1;
      },

      // 判断新老门店
      // async getNewsStore() {
      // 	let res = await $http({
      // 		url: 'znyy/school/currentAdmin'
      // 	})
      // 	if (res) {
      // 		this.schoolType = res.data.schoolType;
      // 	}
      // },

      // 获取学段和学时单价
      async getPeriod() {
        let res = await $http({
          url: 'znyy/bSysConfig/list/course/level/price',
          method: 'get'
        });
        console.log(res);
        if (res) {
          this.periodlist = res.data;
        }
      },

      //是否第一次充值
      async getIsFirstPay(orderNo) {
        let that = this;
        let res = await $http({
          url: 'deliver/web/student/contact/info/getIsFirstRecharge',
          method: 'get',
          data: {
            studentCode: that.charging.studentCode
          }
        });
        if (res && res.data != null) {
          that.isFirsyPay = res.data;
        } else {
          that.isFirsyPay = false;
        }
        that.saveShowFillBtn(that.isFirsyPay);
      },

      saveShowFillBtn(boolval) {
        let that = this;
        let isFirsyPayData = uni.getStorageSync('isFirsyPay');
        if (isFirsyPayData) {
          for (let i = 0; i < isFirsyPayData.length; i++) {
            if (isFirsyPayData[i].studentCode == that.charging.studentCode) {
              isFirsyPayData[i].isFirsyPay = boolval;
              uni.setStorageSync('isFirsyPay', isFirsyPayData);
              return;
            }
          }
          let data = {
            studentCode: that.charging.studentCode,
            isFirsyPay: boolval
          };
          isFirsyPayData.push(data);
          uni.setStorageSync('isFirsyPay', isFirsyPayData);
        } else {
          let data = {
            studentCode: that.charging.studentCode,
            isFirsyPay: boolval
          };
          uni.setStorageSync('isFirsyPay', [data]);
        }
      },

      //  充值
      async determine() {
        let that = this;
        if (that.flag) {
          return;
        }
        that.flag = true;
        uni.showLoading();
        if (that.value == 0) {
          if (this.schoolType == 3) {
            this.charging.selfDeliverCourseLength = this.charging.deliverCourseLength;
          }
          if (this.charging.deliverMode != 'SELF') {
            this.charging.selfDeliverCourseLength = 0;
          }
          let res = await $http({
            url: 'znyy/areas/student/charge/course/save',
            method: 'post',
            data: that.charging
          });
          this.flag = false;
          uni.hideLoading();
          if (res.success) {
            if (res.data.needLineCollect) {
              let orderId = res.data.lineCollectInfo.sourceOrderId;
              let selfShow = this.charging.deliverMode == 'CENTER' ? false : true;
              uni.navigateTo({
                url: `/Recharge/Collection/Collection?orderId=${orderId}&schoolType=${that.schoolType}&status=${that.value}&isSelf=${selfShow}`
              });
            } else {
              // uni.navigateTo({
              //   url: `/Recharge/paySuccess?info=${JSON.stringify(this.charging)}&name=${that.studentName}&schoolType=${that.schoolType}&status=${that.value}`
              // });
              //直接跳转到正式课购买成功页面
              uni.redirectTo({
                url: '/Coursedetails/tips/lessonTips?offStatus=true'
              });
            }
          } else {
            uni.showToast({
              icon: 'none',
              title: res.message
            });
          }
        } else {
          let res = await $http({
            url: 'znyy/course/product/buy',
            method: 'post',
            data: that.lessonPackage
          });
          this.flag = false;
          uni.hideLoading();
          if (res.data != null) {
            let orderId = res.data.sourceOrderId;
            uni.navigateTo({
              url: `/Recharge/Collection/Collection?orderId=${orderId}&status=${this.value}`
            });
          }
        }
      },

      //关闭弹窗
      closeDxnDialog() {
        this.$refs.rewardPopup.close();
      },

      //判断是否需要支付试课奖励
      async getPayExpReward() {
        let that = this;
        if (that.flag) {
          return;
        }
        that.flag = true;
        if (that.charging.studentCode == '') {
          that.$util.alter('充值账户不能为空');
          that.flag = false;
          return;
        }
        // that.getSave();
        if (that.value != 0 && that.value != 1) {
          that.$util.alter('请选择充值类型');
          that.flag = false;
          return;
        }
        if (that.value == 0) {
          // if (that.charging.courseLength == "") {
          // 	that.$util.alter('请输入充值学时');
          // 	that.flag = false;
          // 	return;
          // }
          if (that.schoolType == 3) {
            if (!that.deliverMode) {
              that.$util.alter('请先选择交付方式');
              that.flag = false;
              return;
            }
            if (that.charging.level == '') {
              that.$util.alter('请选择学段');
              that.flag = false;
              return;
            }
            if (that.charging.deliverCourseLength == '') {
              that.$util.alter('请输入充值交付学时');
              that.flag = false;
              return;
            }

            // if (that.radioWeChat == '') {
            // 	that.$util.alter('请选择支付方式');
            // 	return;
            // }
          }
        } else {
          if (that.index == '') {
            that.$util.alter('请选择充值课程');
            that.flag = false;
            return;
          }
          if (that.val == '') {
            that.$util.alter('请选择充值课程');
            that.flag = false;
            return;
          }
        }
        uni.showLoading();
        let res = await $http({
          url: 'deliver/web/experience/getPayExpReward',
          method: 'GET',
          data: {
            studentCode: that.value == 0 ? that.charging.studentCode : that.lessonPackage.studentCode,
            // curriculumId: '1244777039673577472',// 线上环境
			curriculumId: '1222597307584499712' // 测试环境
          }
        });
        uni.hideLoading();
        if (res) {
          if (res.data.needCharge) {
            that.flag = false;
            that.amount = res.data.amount;
            that.$refs.rewardPopup.open();
          } else {
            that.flag = false;
            that.determine();
          }
        } else {
          console.log('接口请求失败');
          that.flag = false;
        }
      },

      // 试课奖励支付
      async changePay() {
        uni.showLoading();
        let data = {
          studentCode: this.value == 0 ? this.charging.studentCode : this.lessonPackage.studentCode,
          amount: this.amount,
          // curriculumId: '1244777039673577472'
		  curriculumId: '1222597307584499712' // 测试环境
        };
        let res = await httpUser.post('znyy/areas/student/charge/expReward/save', data);
        uni.hideLoading();
        if (res) {
			// #ifdef APP-PLUS
			// 添加试课充值支付成功监听
			this.removeListener = uni.$addAppPayGlobalEvtListener(this.sucees, this.fail, this.payInfo.orderId, this.flag1);
			// #endif
          this.$refs.rewardPopup.close();
          this.payBtn(res.data.data.lineCollectInfo);
        } else {
          this.$util.alter(res.data.message);
        }
      },

      async payBtn(data) {
        // #ifdef APP-PLUS
        if (this.userinfo.openId == '') {
          let payCodeData = await httpUser.get('zx/user/getPaymentUserCode?mobile=');
          let payCode = payCodeData.data.data;
          data.userCode = payCode;
        }
        // #endif
        let _this = this;
        uni.showLoading();
        let resdata = await httpUser.post('mps/line/collect/order/unified/collect/check', data);
        let res = resdata.data.data;
        _this.disabled = false;

        uni.hideLoading();
        if (res) {
          if (res.openAllinPayMini) {
            this.flag1 = true;
            this.payInfo = res;
            // #ifdef APP-PLUS
            uni.$appPayTlian(res, 'wxpay');
            // #endif
            // #ifdef MP-WEIXIN
            uni.$payTlian(res);
            // #endif
          } else {
            uni.requestPayment({
              provider: 'wxpay',
              timeStamp: res.payInfo.timeStamp,
              nonceStr: res.payInfo.nonceStr,
              package: res.payInfo.packageX,
              signType: res.payInfo.signType,
              paySign: res.payInfo.paySign,
              success: function(res) {
                console.log('支付成功');
                _this.$refs.successPopup.open();
                setTimeout(() => {
                  _this.$refs.successPopup.close();
                }, 2000);
              },
              fail: function(err) {
                uni.showToast({
                  title: '支付失败'
                });
              }
            });
          }
        }
      },
      async getUserInfoData() {
        let _this = this;
        const res = await $http({
          url: 'zx/user/userInfoNew'
        });
        if (res) {
          _this.userinfo = res.data;
        }
      },
      goUrlcoll() {}

      // // 学时充值
      // async getSave() {
      // 	let res = await $http({
      // 		url: 'znyy/areas/student/charge/course/save',
      // 		method: 'post',
      // 		data: this.charging
      // 	})
      // 	console.log(res)
      // 	if (res.data != null) {
      // 		let orderId = res.data.lineCollectInfo.sourceOrderId;
      // 		uni.navigateTo({
      // 			url: `/Recharge/Collection/Collection?orderId=${orderId}&schoolType=${this.schoolType}`
      // 		})
      // 	}
      // },

      // // 充值学时包
      // async paylessonPackage() {
      // 	let res = await $http({
      // 		url: 'znyy/course/product/buy',
      // 		method: 'post',
      // 		data: this.lessonPackage
      // 	})
      // 	console.log(res)
      // 	if (res.data != null) {
      // 		// let orderId = res.data.lineCollectInfo.sourceOrderId;
      // 		// uni.navigateTo({
      // 		// 	url: `/Recharge/Collection/Collection?orderId=${orderId}&schoolType=${this.schoolType}`
      // 		// })
      // 	}
      // }
    }
  };
</script>

<style lang="scss" scoped>
  .box-bg {
    height: 80px;
    z-index: 999;
    position: fixed;
    top: 0;
    width: 100%;
    background: linear-gradient(to bottom, #2f8c70, #3f967c);
  }

  // 顶部背景
  .top-bgc {
    padding: 190rpx 30rpx 0 30rpx;
    height: 462upx;
    background: linear-gradient(180deg, #2f8c70 0%, #82beac 100%);

    .top-title {
      margin-top: 50rpx;
      margin-bottom: 70rpx;
      display: flex;
      align-items: center;

      .page_title {
        font-size: 34rpx;
        font-family: AlibabaPuHuiTiM;
        color: #ffffff;
        margin-left: 200rpx;
      }
    }

    // 中间文字
    .centertext-left {
      line-height: 60rpx;
    }

    .centertext-right {
      width: 150rpx;
      height: 60rpx;
      text-align: center;
      line-height: 60rpx;
      border-radius: 30rpx;
      background: rgba(255, 255, 255, 0.2);
    }

    // 搜索框
    /deep/.uni-searchbar {
      padding: 0 !important;
    }

    /deep/.uni-searchbar__box {
      height: 90rpx;
      padding: 20rpx 30rpx !important;
      justify-content: start;
      border-radius: 14rpx !important;
    }

    .uni-search-bar .uni-input {
      text-align: left;
      /* 设置文字左对齐 */
    }

    .search-icon {
      width: 32rpx;
      height: 32rpx;
    }

    .uni-input {
      width: 100%;
      font-size: 30rpx !important;
    }

    .search-text {
      font-size: 30rpx;
      font-family: AlibabaPuHuiTiR;
      color: #1d755c;
      border-left: 1rpx solid #efefef;
      padding-left: 30rpx;
    }
  }

  /deep/.uni-searchbar__box-icon-search {
    padding: 0 !important;
  }

  /deep/.uni-icons {
    font-size: 42rpx !important;
  }

  // 充值成功弹窗样式

  /* 弹窗样式 */
  .dialogBG {
    width: 100%;
    /* height: 100%; */
  }

  /* 21天结束复习弹窗样式 */
  .reviewCard_box {
    width: 670rpx;
    /* height: 560rpx; */
    position: relative;
  }

  .reviewCard_box image {
    width: 100%;
    height: 100%;
  }

  .reviewCard {
    position: relative;
    width: 100%;
    height: 100%;
    background: #ffffff;
    color: #000;
    border-radius: 24upx;
    padding: 50upx 55upx;
    box-sizing: border-box;
  }

  .successFn-img {
    position: absolute;
    left: 160rpx;
    top: -298rpx;
    width: 300rpx;
    height: 300rpx;
    z-index: 9;
  }

  .successFnContent {
    font-size: 30rpx;
    font-family: AlibabaPuHuiTiR;
    color: #333333;
    line-height: 50rpx;
    margin-top: 40rpx;
    margin-bottom: 40rpx;
  }

  .cartoom_image {
    width: 420rpx;
    position: absolute;
    top: -250rpx;
    left: 145rpx;
    z-index: -1;
  }

  .review_close {
    position: absolute;
    top: 20rpx;
    right: 20rpx;
    z-index: 1;
  }

  .reviewTitle {
    width: 100%;
    text-align: center;
    font-size: 34upx;
    display: flex;
    justify-content: center;
  }

  .dialogContent {
    box-sizing: border-box;
    font-size: 32upx;
    line-height: 45upx;
    text-align: center;
    margin-top: 40rpx;
  }

  // 二维码
  .drcode {
    margin: 0 auto;
    background-color: pink;
    width: 300rpx;
    height: 298rpx;
  }

  .successFnbtn {
    margin: 0 auto;
    margin-top: 50rpx;
    text-align: center;
    width: 450rpx;
    height: 80rpx;
    line-height: 80rpx;
    background: linear-gradient(180deg, #88cfba 0%, #1d755c 100%);
    border-radius: 45rpx;
  }

  .successFnbtn button {
    font-size: 30rpx;
    font-family: AlibabaPuHuiTiR;
    color: #ffffff;
  }

  /* 底部确定和取消按钮 */
  .mask-footer {
    margin: 0 auto;
    display: flex;
    width: 530rpx;
    justify-content: space-between;
    margin-top: 20px;
  }

  .mask-footer button {
    width: 250rpx;
    height: 80rpx;
    line-height: 80rpx;
    border-radius: 45rpx;
    border: 1rpx solid #2e896f;
    font-size: 30rpx;
  }

  .mask-left {
    background: linear-gradient(180deg, #88cfba 0%, #1d755c 100%);
    color: #ffffff;
  }

  .mask-right {
    color: #2e896f;
  }

  /* 底部确定和取消按钮 */

  .addclass {
    width: 100%;
    height: 70rpx;
    line-height: 70rpx;
    font-size: 30rpx;
    color: #2e896f;
    border: 1px solid #2e896f;
    border-radius: 35rpx;
  }

  .not-selected {
    width: 100%;
    height: 70rpx;
    line-height: 70rpx;
    font-size: 30rpx;
    color: #000;
    border: 1px solid #c8c8c8;
    border-radius: 35rpx;
  }

  // 底部内容
  .single {
    z-index: 1;
    margin-top: -210rpx;
    background-color: #f3f8fc;
    border-top-left-radius: 15rpx;
    border-top-right-radius: 15rpx;
  }

  .content {
    border-radius: 14rpx;
    margin-bottom: 30rpx;
  }

  .information {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .border-bottom {
    border-bottom: 1px solid #efefef;
  }

  .phone-input {
    background: #fff;
    border-radius: 8rpx;
    width: 100%;
    height: 70rpx;
    font-size: 28rpx;
    color: #999999;
    display: flex;
    justify-content: space-between;
    padding-left: 30rpx;
    align-items: center;
  }

  .c-fea {
    color: #ea6031;
  }

  /* 禁用状态下的样式 */
  .disabled-input {
    background-color: #f4f4f4;
  }

  .name-input {
    background: #fff;
    border-radius: 8rpx;
    font-size: 28rpx;
    color: #999999;
    height: 70rpx;
    display: flex;
    justify-content: space-between;
    padding: 0 30rpx;
    margin-top: 30rpx;
    align-items: center;
    border: 3rpx solid #ececea;
  }

  .uni-list-cell-db {
    background: #fff;
    border-radius: 8rpx;
    width: 100%;
    height: 70rpx;
    font-size: 28rpx;
    color: #999999;
    display: flex;
    padding-left: 30rpx;
    align-items: center;
    border: 3rpx solid #ececea;
  }

  .tips {
    position: relative;
    width: 100%;
  }

  /deep/.phone-btn {
    line-height: 90rpx;
    // width: 650rpx;
    height: 90rpx;
    font-size: 30rpx;
    font-family: AlibabaPuHuiTiR;
    color: #ffffff;
    background: linear-gradient(180deg, #88cfba 0%, #1d755c 100%);
    border-radius: 45rpx;
    z-index: 9;
  }

  .detailed {
    background-color: #fff;
    padding: 30rpx;
    border-radius: 14rpx;
    margin-bottom: 30rpx;
  }

  .infopic {
    width: 100rpx;
    height: 100rpx;
    margin-right: 10rpx;
    border-radius: 50%;
    // background-color: pink;
  }

  .refferList_item_top {
    display: flex;
    width: 100%;
    padding-bottom: 32upx;
    border-bottom: 1px solid #eeeeee;
  }

  .refferList_item_bottom {
    width: 100%;
    margin-top: 20upx;
    display: flex;
  }

  .personl {
    font-size: 28rpx;
    display: flex;
    width: 500upx;
    justify-content: space-between;
    align-items: center;
  }

  .nameEllipsis {
    width: 300rpx;
    /* 定好宽度 */
    overflow: hidden;
    width-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
    font-size: 30upx;
  }

  .Collection {
    height: 150rpx;

    .Collection-top {
      display: flex;
      align-items: center;
      height: 100rpx;
    }

    .Collection-bootom {
      display: flex;
      align-items: center;
      margin-bottom: 30rpx;

      .selectFn {
        font-size: 26rpx;
        font-family: AlibabaPuHuiTiR;
        color: #999999;
        margin-left: 10rpx;
      }
    }
  }

  /deep/.btn {
    padding: 3rpx 10rpx;
    border-radius: 4rpx;
    font-size: 26rpx;
    line-height: 36rpx;
  }

  .stay_btn {
    background-color: #439286;
    color: #fff;
    padding: 3rpx 10rpx;
    border-radius: 4rpx;
    font-size: 26rpx;
    line-height: 36rpx;
  }

  .wh100 {
    width: 100rpx;
    height: 100rpx;
    background-color: palegoldenrod;
    border-radius: 50%;
    margin-right: 30rpx;
  }

  .img_s {
    width: 160rpx;
  }

  .classPayStatus_btn {
    display: inline-block;
    padding: 2upx 6upx;
    text-align: center;
    border-radius: 4upx;
    color: #fff;
    font-size: 24upx;
    height: 35upx;
    line-height: 35upx;
  }

  .btntrial {
    width: 180rpx;
    height: 60rpx;
    line-height: 60rpx;
    border-radius: 35rpx;
    font-size: 30rpx;
    text-align: center;
    color: #ea6031;
    border: 1px solid #ea6031;
  }

  .btntrial1 {
    width: 180rpx;
    height: 60rpx;
    line-height: 60rpx;
    border-radius: 35rpx;
    font-size: 30rpx;
    text-align: center;
    color: #fff;
    background: #2e896f;
  }

  .redbagIcon {
    width: 44upx;
    // height: 42upx;
    margin-right: 10upx;
  }

  .redbagText1 {
    font-size: 26upx;
    color: #ea6031;
  }

  .redbagText2 {
    font-size: 26upx;
    color: #f7931e;
  }

  .redbagText3 {
    font-size: 26upx;
    color: #999999;
  }

  /deep/.uni-select {
    padding: 0 !important;
    border: 0 !important;
  }

  /deep/.uni-select__input-placeholder {
    font-size: 28rpx !important;
  }

  // 选择支付方式样式
  .payment-title {
    margin-bottom: 40rpx;
    font-size: 30rpx;
    font-family: AlibabaPuHuiTiR;
    color: #000000;
  }

  .title {
    color: rgba(207, 183, 161, 1);
    font-size: 25rpx;
  }

  .logo-bottom {
    display: flex;
    height: 90rpx;
    justify-content: space-between;
    align-items: center;

    .logo-content {
      display: flex;
      align-items: center;
    }

    .logo-img {
      width: 60rpx;
      height: 60rpx;
      margin-right: 20rpx;
    }
  }

  .img-icon {
    width: 40rpx;
    height: 40rpx;
  }

  .notify {
    box-shadow: 0rpx 0rpx 20rpx #e0e0e0;
  }

  .hint-icon {
    width: 38rpx;
    height: 38rpx;
  }

  .course {
    color: #666;
    height: 60rpx;
    width: 500rpx;
    font-size: 26rpx;
    margin-top: 35rpx;
    line-height: 60rpx;
    padding-left: 24rpx;
    border-radius: 6rpx;
    background-color: #f5f5f5;
  }

  .review_btn {
    width: 250upx;
    height: 80upx;
    background: linear-gradient(180deg, #88cfba 0%, #1d755c 100%);
    border-radius: 45upx;
    font-size: 30upx;
    color: #ffffff;
    line-height: 80upx;
    justify-content: center;
    text-align: center;
  }

  .close_btn {
    width: 250upx;
    height: 80upx;
    color: #2e896f;
    font-size: 30upx;
    line-height: 80upx;
    text-align: center;
    border-radius: 45upx;
    box-sizing: border-box;
    border: 1px solid #2e896f;
  }

  .border_b {
    border-bottom: 1px solid #eee;
  }

  .fe9 {
    color: #e9e9e9;
  }

  .waitPay {
    padding: 4rpx 7rpx;
    background-color: #0398ef;
    border-radius: 8rpx;
    text-align: center;
  }

  .success_content {
    margin-top: 80rpx;
  }

  .success_shadow {
    box-shadow: 0 0 10rpx 0 rgba(0, 0, 0, 0.2);
  }

  .green_yes {
    width: 40rpx;
    height: 40rpx;
  }
</style>