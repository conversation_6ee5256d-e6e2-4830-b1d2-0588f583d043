<template>
  <view>
    <web-view :webview-styles="webviewStyles" :src="url"></web-view>
  </view>
</template>

<script>
  const { $http } = require('@/util/methods.js');
  export default {
    data() {
      return {
        url: '',
        webviewStyles: {
          progress: {
            color: '#FF3333'
          }
        }
      };
    },
    onLoad: function (option) {
      console.log(option);
      if (option.id) {
        this.getMeetingDetails(option.id);
      }
    },
    methods: {
      async getMeetingDetails(id) {
        let _this = this;
        const res = await $http({
          url: 'zx/course/courseDetail',
          data: {
            courseId: id
          }
        });
        if (res) {
          this.url = res.data.meetingUrl;
        }
      }
    }
  };
</script>

<style></style>
