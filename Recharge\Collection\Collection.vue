<template>
  <view>
    <!-- 中间白色卡片 -->
    <view class="center-card plr-30 pb-30">
      <view class="bg-ff p-30 radius-15">
        <!-- 学员信息部分 -->
        <view class="studentinfo" v-if="!isCode">
          <view class="studentinfo-title">鼎校智能教育</view>
          <view class="studentinfo-text">
            <text>学员姓名：</text>
            <text>{{ paylist.studentName }}</text>
          </view>
          <view class="studentinfo-text">
            <text>充值类型：</text>
            <text>{{ paylist.type == 1 ? '充值软件使用费' : '充值课程包' }}</text>
          </view>
          <view class="studentinfo-text" v-if="paylist.type == 2">
            <text>充值课程包：</text>
            <text>{{ paylist.packageName }}</text>
          </view>
          <view class="studentinfo-text" v-if="paylist.type == 1">
            <text>充值学时：</text>
            <text>{{ paylist.courseNum }}节</text>
          </view>
          <view class="studentinfo-text" v-if="schoolType == 3 && paylist.type == 1">
            <text>充值交付学时：</text>
            <text>{{ isSelf == 'false' ? paylist.deliverNum : paylist.selfDeliverNum }}节</text>
          </view>
          <view class="studentinfo-text">
            <text>支付金额：</text>
            <text>¥{{ paylist.price }}</text>
          </view>
          <view class="studentinfo-text">
            <text>充值说明：</text>
            <text class="lh-50">{{ paylist.description }}</text>
          </view>
        </view>
        <view class="studentinfo" v-if="isCode">
          <view class="studentinfo-text">
            <text>充值名称：</text>
            <text>{{ remark }}</text>
          </view>
          <view class="studentinfo-text">
            <text>支付金额：</text>
            <text>¥{{ codePrice / 100 }}</text>
          </view>
        </view>
        <!-- 二维码部分 -->
        <view class="dqcode">
          <!-- 图片 -->
          <view class="dqcode-img t-c">
            <view class="img-photo">
              <img v-if="isCode" :src="curImg" alt="" v-else style="width: 100%; height: 100%" />
              <uqrcode
                v-else
                ref="uqrcode"
                canvas-id="qrcode"
                :value="imgCode"
                :options="{ margin: 10, foregroundImageSrc: 'https://document.dxznjy.com/dxSelect/wexin_code.png' }"
              ></uqrcode>

              <!-- <ayQrcode ref="qrcode" qrcode_id="qrcode" :modal="modal_qr" :url="imgCode" @hideQrcode="hideQrcode" :height="185" :width="185"/> -->
            </view>
          </view>
          <!-- 文字 -->
          <view class="dqcode-text t-c">
            <view class="text-content">请使用微信“扫一扫”扫码支付</view>
            <!-- <view class="text-content">
							<text>请在</text>
							<text class="time">2023-05-07 11:34:02</text>
							<text>前完成支付</text>
						</view> -->
            <view class="text-content">
              <text>支付状态：</text>
              <!-- <text class="success">支付成功</text> -->
              <text class="wait">等待支付</text>
            </view>
          </view>
          <view class="mt-30" v-if="!isCode">
            <view class="c-33 f-30 bold">自行支付</view>
            <view class="mt-30 pay_btn" @click="getOrderCreate">立即支付</view>
          </view>
          <!-- 微信支付底部按钮 -->
          <view class="mt-40">
            <view class="c-33 f-30 bold" v-if="!isCode">家长支付</view>
            <view class="wexin-footer-btn">
              <!-- #ifdef MP-WEIXIN -->
              <button open-type="share" class="buttonFn btnleft" v-if="!isCode">
                <image :src="imgHost + 'dxSelect/image/link.png'" class="link-img"></image>
                <text>链接分享</text>
              </button>
              <!-- #endif -->
              <!-- #ifdef APP-PLUS -->
              <button @click="linkShareApp" class="buttonFn btnleft" v-if="!isCode">
                <image :src="imgHost + 'dxSelect/image/link.png'" class="link-img"></image>
                <text>链接分享</text>
              </button>
              <!-- #endif -->
              <view class="buttonFn btnleft" @click="save" v-if="!isCode">
                <text>保存收钱码</text>
              </view>
              <view class="buttonFn btnleft" @click="saveApplication" style="margin: 0 auto" v-if="isCode">
                <text>保存收钱码</text>
              </view>
            </view>
          </view>
          <!-- 支付宝支付底部按钮 -->
          <!-- 	<view class="aplay-footer-btn">
						<view class="buttonFn" @click="saveImage">
							<text>保存收钱码</text>
						</view>
					</view> -->
        </view>
      </view>
    </view>

    <!-- 保存收款码提示框 -->
    <uni-popup ref="notifyPopup" type="top" mask-background-color="rgba(0,0,0,0)">
      <view class="plr-60">
        <view class="t-c bg-ff flex-c ptb-25 radius-50 mt-80 notify">
          <image :src="imgHost + 'dxSelect/image/success.png'" class="hint-icon"></image>
          <view class="f-34 ml-15">保存收款码成功</view>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script>
  import uqrcode from '../components/Sansnn-uQRCode/components/uqrcode/uqrcode.vue';
  // import UniQrcode from 'uniapp-qrcode'
  // import ayQrcode from "@/components/ay-qrcode/ay-qrcode.vue"
  const { $navigationTo, $http } = require('@/util/methods.js');
  const { httpUser } = require('@/util/luch-request/indexUser.js');
  export default {
    components: {
      uqrcode
      // ayQrcode
    },
    data() {
      return {
        payInfo: {},
        flag1: false,
        imgHost: getApp().globalData.imgsomeHost,
        imgUrl: 'https://document.dxznjy.com/dxSelect/image/link.png',
        orderId: '', // 订单id
        schoolType: 3, // 3新门店 其他老门店
        codeImg: '', // 二維碼
        shareImg: 'https://document.dxznjy.com/dxSelect/recharge/share_link.jpg',
        shareContent: {},
        paylist: {}, // 支付信息
        orderlist: {}, // 订单支付信息

        //二维码相关参数
        modal_qr: false,
        imgCode: '', // 要生成的二维码值
        status: 0, // 0学时  1学时包

        isSelf: false, //是否是自行交付
        isCode: true, //是否是邀请码完款
        codePrice: '',
        codeToken: '',
        remark: '',
        curImg: '',
        userinfo: {},
        removeListener: null,
        app: 0
      };
    },
    // onReady() {

    // 	// 获取uQRCode实例
    // 	var qr = new UQRCode();
    // 	// 设置二维码内容
    // 	qr.data = this.imgCode;
    // 	// 设置二维码大小，必须与canvas设置的宽高一致
    // 	qr.size = 200;

    // 	// 设置二维码前景图，可以是路径
    // 	qr.foregroundImageSrc = "https://document.dxznjy.com/dxSelect/wxpay.png"
    // 	// 调用制作二维码方法
    // 	qr.make();
    // 	// 获取canvas上下文
    // 	var canvasContext = uni.createCanvasContext('qrcode', this); // 如果是组件，this必须传入
    // 	// 设置uQRCode实例的canvas上下文
    // 	qr.canvasContext = canvasContext;
    // 	// 调用绘制方法将二维码图案绘制到canvas上
    // 	qr.drawCanvas();
    // },
    onUnload() {
      // #ifdef APP-PLUS
      if (this.app) {
        plus.runtime.quit();
      }
      // #endif
    },
    onLoad(options) {
      this.app = options.app;
      if (options.type != undefined) {
        this.isCode = true;
        this.codePrice = options.codePrice;
        this.orderId = options.orderId;
        this.remark = options.remark;
        this.codeToken = options.codeToken;
        this.payCode();
      } else {
        this.isCode = false;
        this.orderId = options.orderId;
        this.schoolType = options.schoolType;
        this.status = options.status;
        this.isSelf = options.isSelf;
        this.imgCode = 'https://document.dxznjy.com/zxRecharge?orderId=' + this.orderId;
        this.getOrderInfo();
      }
      this.getUserInfoData();
      // this.getOrderQrCode()
      // #ifdef APP-PLUS
      this.removeListener = uni.$addAppPayGlobalEvtListener(this.sucees, this.fail, this.payInfo.orderId, this.flag1);
      // #endif
    },
    beforeDestroy() {
      // #ifdef APP-PLUS
      if (this.removeListener) {
        this.removeListener();
      }
      // #endif
    },
    onShow() {
      uni.setStorageSync('wxpay', true);
      // this.$refs.notifyPopup.open();
      uni.removeStorage({
        key: 'payToken',
        success: (res) => {
          console.log('删除payToken');
        }
      });
      if (this.flag1) {
        // #ifdef MP-WEIXIN
        uni.$tlpayResult(this.sucees, this.fail, this.payInfo.orderId);
        // #endif
      }
    },
    onShareAppMessage(res) {
      this.shareContent.type = 4;
      let url = `/pages/beingShared/index?scene=${uni.getStorageSync('user_id')}&type=${this.shareContent.type}&id=${this.orderId}`;
      setTimeout(() => {
        this.$refs.sharePopup.close();
      }, 2000);
      return {
        title: '叮，你的好友敲了你一下，赶紧过来看看',
        imageUrl: this.shareImg,
        path: `/pages/beingShared/index?scene=${uni.getStorageSync('user_id')}&type=${this.shareContent.type}&id=${this.orderId}`
      };
    },
    methods: {
      linkShareApp() {
        this.shareContent.type = 4;
        setTimeout(() => {
          this.$refs.sharePopup.close();
        }, 2000);
        let shareInfo = {
          title: '叮，你的好友敲了你一下，赶紧过来看看',
          imageUrl: this.shareImg,
          path: `/pages/beingShared/index?scene=${uni.getStorageSync('user_id')}&type=${this.shareContent.type}&id=${this.orderId}`
        };
        uni.$appShare(shareInfo, 2);
        plus.runtime.quit();
      },
      sucees() {
        this.flag1 = false;
        // uni.redirectTo({
        //   url: `/Recharge/paySuccess?orderId=${this.orderId}&schoolType=${this.schoolType}&status=${this.value}&studentCode=${this.paylist.studentCode}&deliverLen=${this.paylist.deliverNum}`
        // });
		uni.redirectTo({
		  url: '/Coursedetails/tips/lessonTips?offStatus=true'
		});
      },
      fail() {
        this.flag1 = false;
      },
      fails() {
        uni.showToast({
          title: '支付失败',
          icon: 'none',
          duration: 2000
        });
        this.flag1 = false;
      },
      // 顶部返回上一页箭头
      goBack() {
        // console.log(1111111111);
        uni.navigateBack();
      },

      save() {
        // this.$refs.qrcode.saveImage()
        this.$refs.uqrcode.save({
          success: () => {
            uni.showToast({
              icon: 'success',
              title: '保存成功'
            });
          }
        });
      },

      saveImage() {
        let that = this;
        uni.getImageInfo({
          src: that.imgUrl,
          success: function (image) {
            uni.saveImageToPhotosAlbum({
              filePath: image.path,
              success: function () {
                // uni.showToast({
                // 	title: '图片保存成功'
                // });
                that.$refs.notifyPopup.open();
                setTimeout(() => {
                  that.$refs.notifyPopup.close();
                }, 2000);
              },
              fail: function () {
                uni.showModal({
                  title: '图片保存失败',
                  content: '请确认是否已开启授权',
                  confirmText: '开启授权',
                  success(res) {
                    if (res.confirm) {
                      uni.openSetting({
                        success(settingdata) {
                          if (settingdata.authSetting['scope.writePhotosAlbum']) {
                            uni.showToast({
                              title: '授权成功，请重试哦~',
                              icon: 'none'
                            });
                          } else {
                            uni.showToast({
                              title: '请确定已打开保存权限',
                              icon: 'none'
                            });
                          }
                        }
                      });
                    }
                  }
                });
              }
            });
          },
          fail() {}
        });
      },

      //获取充值订单二维码
      // async getOrderQrCode(){
      // 	let res = await $http({
      // 		url: 'zx/user/getOrderQrCode',
      // 		method: 'get',
      // 		data: {
      // 			orderId:this.orderId,
      // 			type:this.type
      // 		}
      // 	})
      // 	if(res){
      // 		this.codeImg = res.data;
      // 		this.getSceneValue();
      // 	}
      // },
      // 根据二维码scene获取具体参数
      // async getSceneValue(){
      // 	let res = await $http({
      // 		url: 'zx/user/getSceneValue'
      // 	})
      // 	console.log(res)
      // 	if(res){
      // 		this.codeImg = res.data;
      // 	}
      // },
      // 获取订单信息
      async getOrderInfo() {
        console.log(this.imgCode, '111111');
        // this.$refs.qrcode.getImg(this.imgCode);
        // this.showQrcode();//一加载生成二维码
        let res = await $http({
          url: 'znyy/school/recharge/getRechargeOrderInfo',
          method: 'get',
          data: {
            orderId: this.orderId
          }
        });

        console.log(res);
        if (res) {
          this.paylist = res.data;
        } else {
          this.$util.alter(res.data.message);
        }
      },

      // 获取订单支付信息
      async getOrderCreate() {
        // uni.showLoading()

        let res = await $http({
          url: 'znyy/school/recharge/getRechargeLineOrderCreateDto',
          method: 'get',
          data: {
            orderId: this.orderId
          }
        });
        // uni.hideLoading();
        console.log(res);
        if (res) {
          this.orderlist = res.data;
          this.payBtn(this.orderlist);
        } else {
          this.$util.alter(res.data.message);
        }
      },

      async payBtn(data) {
        // #ifdef APP-PLUS
        if (this.userinfo.openId == '') {
          let payCodeData = await httpUser.get('zx/user/getPaymentUserCode?mobile=');
          let payCode = payCodeData.data.data;
          data.userCode = payCode;
        }
        // #endif
        let _this = this;
        uni.showLoading();
        let resdata = await httpUser.post('mps/line/collect/order/unified/collect/check', data);
        let res = resdata.data.data;
        _this.disabled = false;
        uni.hideLoading();
        if (res) {
          if (res.openAllinPayMini) {
            this.flag1 = true;
            this.payInfo = res;
            // #ifdef APP-PLUS
            uni.$appPayTlian(res, 'wxpay');
            // #endif
            // #ifdef MP-WEIXIN
            uni.$payTlian(res);
            // #endif
          } else {
            uni.requestPayment({
              provider: 'wxpay',
              timeStamp: res.payInfo.timeStamp,
              nonceStr: res.payInfo.nonceStr,
              package: res.payInfo.packageX,
              signType: res.payInfo.signType,
              paySign: res.payInfo.paySign,
              success: function (res) {
                console.log('支付成功');
                // uni.redirectTo({
                //   url: `/Recharge/paySuccess?orderId=${_this.orderId}&schoolType=${_this.schoolType}&status=${this.value}&studentCode=${_this.paylist.studentCode}&deliverLen=${_this.paylist.deliverNum}`
                // });
				uni.redirectTo({
				  url: '/Coursedetails/tips/lessonTips?offStatus=true'
				});
              },
              fail: function (err) {
                uni.showToast({
                  title: '支付失败'
                });
				//这里不用跳到其他页面
                // uni.redirectTo({
                // 	url: `/Recharge/paySuccess?orderId=${_this.orderId}&schoolType=${_this.schoolType}&status=${this.value}`
                // })
              }
            });
          }
        }
      },

      async payCode() {
        let res = await $http({
          url: 'zx/user/share/getQrCodeUrl',
          method: 'get',
          data: {
            page: 'Recharge/payment/payment',
            scnen: 'orderId=' + this.orderId + '&a=1'
          }
        });
        if (res) {
          this.curImg = res.data;
        }
      },
      saveApplication() {
        uni.downloadFile({
          url: this.curImg,
          success: (res) => {
            if (res.statusCode === 200) {
              uni.saveImageToPhotosAlbum({
                //保存图片到系统相册。
                filePath: res.tempFilePath, //图片文件路径
                success: function () {
                  uni.showToast({
                    title: '图片保存成功',
                    icon: 'success'
                  });
                },
                fail: function (e) {
                  uni.showToast({
                    title: '图片保存失败',
                    icon: 'none'
                  });
                }
              });
            }
          }
        });
      },

      // 展示二维码
      showQrcode() {
        let _this = this;
        this.modal_qr = true;
        // uni.showLoading()
        setTimeout(function () {
          // uni.hideLoading()
          _this.$refs.qrcode.crtQrCode();
        }, 100);
      },

      //传入组件的方法
      hideQrcode() {
        this.modal_qr = false;
      },
      async getUserInfoData() {
        let _this = this;
        const res = await $http({
          url: 'zx/user/userInfoNew'
        });
        if (res) {
          _this.userinfo = res.data;
        }
      }
    }
  };
</script>

<style lang="scss" scoped>
  page {
    padding: 60rpx 30rpx 30rpx 30rpx;

    // 顶部文字和返回箭头
    .top-title {
      position: fixed;
      padding-top: 80rpx;
      padding-bottom: 30rpx;
      width: 100%;
      display: flex;
      align-items: center;
      background-color: #f3f8fc;

      .page_title {
        font-size: 34rpx;
        color: #000000;
        margin-left: 40%;
      }
    }

    // 中间白色卡片
    .center-card {
      // 学员信息部分
      .studentinfo {
        // margin-top: 30rpx;
        // border-radius: 14rpx;

        .studentinfo-title {
          font-size: 32rpx;
          font-family: AlibabaPuHuiTiM;
          color: #000000;
          font-weight: bold;
        }

        .studentinfo-text {
          margin-top: 30rpx;
        }
      }
    }

    // 二维码部分
    .dqcode {
      // text-align: center;

      // 图片
      .dqcode-img {
        margin: 0 auto;
        width: 430rpx;
        height: 430rpx;
        border-radius: 16rpx;
        // border: 2rpx solid #D9D9D9;
        margin-top: 50rpx;
        margin-bottom: 10rpx;

        .img-photo {
          margin: 30rpx auto;
          position: relative;
          width: 370rpx;
          height: 370rpx;
          // background-color: pink;

          .logo-img {
            position: absolute;
            left: 50%;
            top: 50%;
            transform: translate(-50%, -50%);
            width: 100%;
            height: 100%;
          }
        }
      }
    }

    // 文字
    .dqcode-text {
      text-align: center;
      font-size: 30rpx;
      font-family: AlibabaPuHuiTiR;
      color: #303133;
      line-height: 50rpx;

      .time {
        color: #ea6031;
      }

      // 支付成功
      .success {
        width: 114rpx;
        height: 36rpx;
        background: #2dc032;
        border-radius: 4rpx;
        font-size: 26rpx;
        font-family: AlibabaPuHuiTiR;
        color: #ffffff;
        line-height: 35rpx;
      }

      // 等待支付
      .wait {
        width: 114rpx;
        height: 36rpx;
        background: #0398ef;
        border-radius: 4rpx;
        font-size: 26rpx;
        font-family: AlibabaPuHuiTiR;
        color: #ffffff;
        line-height: 35rpx;
        padding: 3rpx 7rpx;
      }
    }

    // 微信支付底部按钮
    .wexin-footer-btn {
      display: flex;
      justify-content: space-between;
      margin-top: 30rpx;

      .buttonFn {
        border-radius: 45rpx;
        width: 290rpx;
        height: 80rpx;
        line-height: 80rpx;
        text-align: center;
        font-size: 30rpx;
        font-family: AlibabaPuHuiTiR;
      }

      .btnleft {
        border: 1rpx solid #2e896f;
        color: #2e896f;
        display: flex;
        align-items: center;
        justify-content: center;
        box-sizing: border-box;
      }

      .btnright {
        background: linear-gradient(180deg, #88cfba 0%, #1d755c 100%);
        color: #ffffff;
      }

      .link-img {
        width: 42rpx;
        height: 42rpx;
        margin-right: 14rpx;
      }
    }

    // 支付宝支付底部按钮
    .aplay-footer-btn {
      margin-top: 80rpx;

      .buttonFn {
        margin: 0 auto;
        border-radius: 45rpx;
        width: 650rpx;
        height: 80rpx;
        line-height: 80rpx;
        text-align: center;
        font-size: 30rpx;
        font-family: AlibabaPuHuiTiR;
        background: linear-gradient(180deg, #88cfba 0%, #1d755c 100%);
        color: #ffffff;
      }
    }

    // 保存收款码提示框
    .save {
      position: fixed;
      top: 90rpx;
      left: 50%;
      transform: translateX(-50%);
      width: 623rpx;
      height: 100rpx;
      line-height: 100rpx;
      display: flex;
      justify-content: center;
      align-items: center;
      background: #ffffff;
      box-shadow: 1rpx -2rpx 20rpx 1rpx rgba(0, 0, 0, 0.12);
      border-radius: 50rpx;

      .save-img {
        width: 38rpx;
        height: 38rpx;
        margin-right: 17rpx;
      }

      .save-text {
        font-size: 34rpx;
        font-family: AlibabaPuHuiTiR;
        color: #161616;
      }
    }
  }

  /deep/.pay_btn {
    height: 80rpx;
    width: 300rpx;
    color: #ffffff;
    font-size: 30rpx;
    text-align: center;
    line-height: 80rpx;
    border-radius: 45rpx;
    background: linear-gradient(180deg, #88cfba 0%, #1d755c 100%);
  }

  .img_s {
    width: 160rpx;
    height: 160rpx;
  }

  .notify {
    box-shadow: 0rpx 0rpx 20rpx #e0e0e0;
  }

  .hint-icon {
    width: 38rpx;
    height: 38rpx;
  }
</style>

<style>
  .page {
    /* #ifndef APP-NVUE */
    display: flex;
    flex-direction: column;
    /* #endif */
    align-items: center;
  }

  .input {
    /* #ifndef APP-NVUE */
    display: flex;
    flex-direction: column;
    /* #endif */
    width: 320px;
    margin: 30px 0;
    padding: 10px;
    background-color: rgba(0, 0, 0, 0.05);
    border-radius: 4px;
  }

  .qrcode-box {
    /* #ifndef APP-NVUE */
    display: flex;
    flex-direction: column;
    /* #endif */
    align-items: center;
    margin-bottom: 30px;
    padding: 0 30px;
  }

  .qrcode {
    padding: 16px;
    background-color: #ffffff;
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.1);
    border-radius: 2px;
    overflow: hidden;
  }

  .msg {
    margin-top: 15px;
    font-size: 14px;
    color: #9a9b9c;
  }

  .btns {
    /* #ifndef APP-NVUE */
    display: flex;
    /* #endif */
    flex-direction: row;
  }

  .btn {
    margin: 0 10px;
  }
</style>
