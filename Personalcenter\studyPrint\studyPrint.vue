<!-- 学习打印页面 -->
<template>
  <view class="study_print_page plr-30 pb-30">
    <view class="bg_box"></view>
    <view class="list_box mt-30">
      <view class="query_row f-30 c-00">
        <view class="row_flex">
          <view>学员：</view>
          <view class="pick f-28 c-99">
            <picker class="btnchange" @change="bindPickerStudent" :value="studentIndex" :range="studentArray" range-key="realName" name="grade">
              <view :class="studentStyle ? 'c-00' : ''">{{ studentArray[studentIndex].realName }}</view>

              <uni-icons type="forward" size="16" color="#C8C8C8" class="pick_right"></uni-icons>
            </picker>
          </view>
        </view>
        <view class="row_flex mt-30">
          <view>类型：</view>
          <view class="pick f-28 c-99">
            <picker class="btnchange" @change="bindPickerType" :value="typeIndex" :range="typeArray" range-key="label" name="grade">
              <view :class="courseType ? 'c-00' : ''">{{ typeArray[typeIndex].label }}</view>
              <uni-icons type="forward" size="16" color="#C8C8C8" class="pick_right"></uni-icons>
            </picker>
          </view>
        </view>
      </view>
    </view>

    <view class="list_data bg-ff radius-15 plr-30 f-30 c-33" v-if="printList.length > 0 && typeArray[typeIndex].value !== 'NewGrammar'">
      <view v-for="(item, index) in printList" :key="item.id">
        <view v-if="typeArray[typeIndex].value != 'Word'" class="flex-box b-b flex printList_item" @click="goContent(index, 0)">
          <view class="flex-c border-b" style="height: 100%">
            <view class="f-28 c-22" style="width: 160upx; height: 100%">{{ item.studentName }}</view>
            <view class="f-28 c-22" style="width: 148upx; height: 100%">{{ typeArray[typeIndex].label }}</view>
            <view class="f-28 c-22" style="width: 300upx; height: 100%">{{ item.submitTime }}</view>
            <image :src="imgHost + 'dxSelect/image/right.png'" class="arrow" mode="widthFix"></image>
          </view>
        </view>
        <view v-else class="">
          <view class="card border-b">
            <view class="flex-a">
              <view class="f-30" style="color: #333333; width: 50%; text-align: left">{{ item.title }}</view>
              <view class="f-30" style="color: #333333; width: 50%; text-align: center">{{ item.submitTime }}</view>
            </view>
            <view class="flex-a">
              <view class="flex-a">
                <view class="f-30" style="width: 100rpx; color: #2e896f; border-right: #2e896f 1px solid; text-align: left; line-height: 30rpx" @click="goContent(index, 1)">
                  全天
                </view>
                <view class="f-30" style="width: 100rpx; color: #2e896f; text-align: right" @click="goContent(index, 0)">本次</view>
              </view>
              <view class="">
                <view class="f-30" style="color: #2e896f" @click="goContent(index, 2)">结业单词</view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
    <view v-else-if="printList.length > 0 && typeArray[typeIndex].value == 'NewGrammar'" class="list_data bg-ff radius-15 plr-30 f-30 c-33">
      <view class="flex-c pt-30 pb-30" v-for="(item, index) in printList" :key="index" @click="goContentGra(item)">
        <view class="f-28 c-22" style="width: 148upx">{{ item.studentName }}</view>
        <view class="f-28 c-22" style="width: 148upx">{{ item.printType }}</view>
        <view class="f-28 c-22" style="width: 300upx">{{ item.createTime }}</view>
        <u-icon name="arrow-right" size="28"></u-icon>
      </view>
    </view>
    <view v-else class="t-c flex-col mt-30 bg-ff radius-15" :style="{ height: useHeight + 'rpx' }">
      <image :src="imgHost + 'alading/correcting/no_data.png'" class="mb-20 img_s" mode="widthFix"></image>
      <view style="color: #bdbdbd">暂无数据</view>
    </view>
  </view>
</template>
<script>
  export default {
    data() {
      return {
        studentCode: '',
        type: '',
        studentIndex: 0,
        typeIndex: 0,
        studentArray: [
          {
            studentCode: '',
            realName: '请选择学员'
          }
        ],
        typeArray: [
          {
            value: '',
            label: '请选择类型'
          },
          {
            value: 'Word',
            label: '单词'
          },
          {
            value: 'Reading',
            label: '阅读理解'
          },
          {
            value: 'Grammar',
            label: '语法'
          },
          {
            value: 'NewGrammar',
            label: '新版语法'
          },
          {
            value: 'SuperRead',
            label: '新阅读理解'
          }
        ],
        printList: [], // 课件列表
        pageNum: 1, // 当前页
        pageSize: 20, // 页数
        loadingType: 'nodata', //加载更多状态
        studentStyle: false,
        courseType: false,
        useHeight: 0, //除头部之外高度
        imgHost: getApp().globalData.imgsomeHost,
        app: 0
      };
    },
    onLoad(option) {
      var that = this;
      // option.studentCode = '625029993';
      // #ifdef MP-WEIXIN
      if (option != null && option.studentCode != undefined) {
        that.studentCode = option.studentCode;
      } else {
        this.getStudentCode();
      }
      // #endif
      // #ifdef APP-PLUS
      console.log(option.token);
      // if (option.token) {
      this.app = option.app;
      this.studentCode = option.studentCode;
      this.getStudentCode();
      this.$handleTokenFormNative(option);
      // }
      // #endif
    },

    onReady() {
      uni.getSystemInfo({
        success: (res) => {
          // 可使用窗口高度，将px转换rpx
          let h = res.windowHeight * (750 / res.windowWidth);
          this.useHeight = h - 650;
        }
      });
    },

    onShow() {},

    //加载更多
    onReachBottom() {
      if (this.studentCode != '' && this.type != '') {
        this.pageNum++;
        if (this.type == 'NewGrammar') {
          this.getNewGrammar();
        } else {
          this.getDataList(true);
        }
      }
    },
    onUnload() {
      // #ifdef APP-PLUS
      if (this.app) {
        plus.runtime.quit();
      }
      // #endif
    },
    mounted() {
      // this.getStudentCode()
    },
    methods: {
      // 选择学员code
      bindPickerStudent(e) {
        console.log(e);
        this.studentStyle = true;
        this.studentIndex = e.target.value;
        if (this.studentCode == this.studentArray[this.studentIndex].studentCode) {
          return;
        }
        this.studentCode = this.studentArray[this.studentIndex].studentCode;
        this.printList = [];
      },
      // 选择类型
      bindPickerType(e) {
        this.courseType = true;
        this.typeIndex = e.target.value;
        if (this.type == this.typeArray[this.typeIndex].value) {
          return;
        }
        this.printList = [];
        this.type = this.typeArray[this.typeIndex].value;
        if (this.type == 'NewGrammar') {
          this.getNewGrammar();
        }
      },
      getNewGrammar() {
		  uni.showLoading();
		  let that = this;
        this.$httpUser
          .get('dyf/wap/applet/printPage', {
            studentCode: this.studentCode,
            pageNum: this.pageNum,
            pageSize: this.pageSize
          })
          .then((result) => {
            // this.printList = result.data.data.data

            if (result.data.data.data.length > 0) {
              const newList = result.data.data.data;
              that.printList.push(...newList);
            } else {
              that.loadingType = 'nomore';
			  uni.showToast({
			    icon: 'none',
			    title: '暂无更多内容了！',
			    duration: 2000
			  });
            }
          }).finally(() => {
			  uni.hideLoading();
		  });
      },

      // 查询studentCode
      async getStudentCode() {
        let result = await this.$httpUser.get('znyy/review/query/my/student');
        if (result != undefined && result != '') {
          if (result.data.data != null) {
            // #ifdef MP-WEIXIN
            this.studentArray = [
              {
                studentCode: '',
                realName: '选择学员'
              }
            ];
            // #endif
            if (result.data.data.length > 0) {
              if (result.data.data.length == 1) {
                this.studentArray = [];
                this.index = 0;
                this.studentStyle = true;
                this.studentArray = this.studentArray.concat(result.data.data);
                if (this.studentCode == '') {
                  this.studentCode = this.studentArray[this.index].studentCode;
                }
              } else {
                var that = this;
                that.studentArray = that.studentArray.concat(result.data.data);
              }
            }
            // #ifdef APP-PLUS
            for (let i = 0; i < this.studentArray.length; i++) {
              if (this.studentArray[i].studentCode === this.studentCode) {
                console.log(this.studentArray[i].loginName, '1');
                console.log(this.studentCode, '2');
                this.studentIndex = i;
                console.log(this.studentIndex, '3');
                console.log(this.studentArray[this.studentIndex].realName, '4');
                this.studentStyle = true;
                break;
              }
            }
            // #endif
          }
        }
      },

      // 获取学习内容
      async getDataList(change) {
        console.log(change, '学习内容打印');
        let that = this;
        // that.printList = [];
        uni.showLoading();
        await that.$httpUser
          .get('znyy/student/print', {
            studentCode: that.studentCode,
            type: that.type,
            pageNum: that.pageNum,
            pageSize: that.pageSize
          })
          .then((result) => {
            uni.hideLoading();
            if (result) {
              if (result.data.data.data.length == 0) {
                that.loadingType = 'nodata';
                uni.showToast({
                  icon: 'none',
                  title: '暂无更多内容了！',
                  duration: 2000
                });
              } else {
                if (result.data.data.data.length > 0) {
                  that.printList = that.printList.concat(result.data.data.data);
                }
                that.loadingType = that.pageNum >= Number(result.data.data.totalPage) ? 'nomore' : 'more';
              }
            }
          });
      },

      // 跳转打印详情 语法
      goContentGra(item) {
        console.log(item);
        // console.log(item.id, '打印语法')
        uni.navigateTo({
          url: `/Personalcenter/studyPrint/studyContentPrint?id=${item.id}&studentName=${item.studentName}&studentCode=${item.studentCode}&type=${this.type}`
        });
      },
      // 跳转打印详情
      goContent(index, isAll) {
        let studentName = this.printList[index].studentName;
        let type = this.printList[index].type;
        let printCode = this.printList[index].printCode;
        let submitTime = this.printList[index].submitTime;
        let title = this.printList[index].title;
        let url = '';
        if (isAll == 0 || isAll == 1) {
          uni.navigateTo({
            url: `/Personalcenter/studyPrint/studyContentPrint?studentName=${studentName}&studentCode=${this.studentCode}&type=${
              type === 'Handouts' ? 'Grammar' : type
            }&printCode=${printCode}&submitTime=${submitTime}&allDay=${isAll}`
          });
        } else {
          uni.navigateTo({
            url: `/Personalcenter/studyPrint/studyZip?studentName=${studentName}&studentCode=${this.studentCode}&type=${
              type === 'Handouts' ? 'Grammar' : type
            }&printCode=${printCode}&submitTime=${submitTime}&allDay=${isAll}&title=${title}`
          });
        }
      }
    },

    watch: {
      studentCode(val) {
        if (val != '' && this.type != '') {
          this.pageNum = 1;
          this.pageSize = 20;
          if (this.type == 'NewGrammar') {
            this.getNewGrammar();
          } else {
            this.getDataList(true);
          }
        }
      },
      type(val) {
        if (val != '' && this.studentCode != '') {
          this.pageNum = 1;
          this.pageSize = 20;
          if (this.type == 'NewGrammar') {
            // this.getNewGrammar()
          } else {
            this.getDataList(true);
          }
        }
      }
    }
  };
</script>

<style>
  .study_print_page {
    background-color: #f3f8fc;
  }

  .bg_box {
    width: 690upx;
    height: 332upx;
    background-image: url(http://document.dxznjy.com/dxSelect/studyPrint_img.png);
    background-size: cover;
    background-repeat: no-repeat;
  }

  .list_box {
    border-radius: 14upx;
    background: #ffffff;
    box-shadow: 0 -10rpx 25rpx 15rpx rgba(0, 0, 0, 0.12);
    padding: 30upx;
    font-size: 26upx;
    color: rgba(102, 102, 102, 1);
    position: relative;
  }

  .query_row {
    display: flex;
    flex-wrap: wrap;
  }

  .row_flex {
    display: flex;
    justify-content: start;
    width: 100%;
    align-items: center;
  }

  .pick {
    flex: 1;
    /* width: 500upx; */
    height: 70upx;
    line-height: 70upx;
    border: 1px solid #c8c8c8;
    border-radius: 36upx;
    text-align: left;
    padding: 0 30upx;
    box-sizing: border-box;
    position: relative;
  }

  .list_data {
    overflow-y: scroll;
    margin-top: 30rpx;
  }

  .arrow {
    width: 14rpx;
    margin-left: 5upx;
  }

  .pick_right {
    position: absolute;
    right: 20upx;
    top: 0;
  }

  .printList_item {
    height: 110upx;
    line-height: 110upx;
    border-bottom: 1upx solid #efefef;
  }

  .printList_item:last-child {
    border: none;
  }

  .border-b {
    border-bottom: 1px solid #efefef;
  }

  .img_s {
    width: 160rpx;
  }

  .flex-a {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 10rpx;
  }

  .card {
    padding: 25rpx 15rpx;
  }
</style>
1
