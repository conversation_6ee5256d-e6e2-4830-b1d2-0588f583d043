<template>
  <view class="contain" :style="{ height: useHeight + 'rpx' }">
    <view class="items" v-for="item in list" :key="item.id" @click="goListen(item)">
      <view>{{ item.listeningName }}</view>
      <uni-icons type="right" size="14" color="#339378"></uni-icons>
    </view>
  </view>
</template>

<script>
  export default {
    data() {
      return {
        list: [],
        query: {},
        useHeight: 0
      };
    },
    methods: {
      async init() {
        let { data } = await this.$httpUser.get('dyf/zxListening/wap/getZxListeningReviewList', this.query);
        if (data.success) {
          this.list = data.data;
          if (this.list.length == 0) {
            uni.navigateBack();
          }
        }
      },
      goListen(e) {
        if (this.query.status == 0) {
          uni.navigateTo({
            url: `/Listen/answer?studentCode=${this.query.studentCode}&id=${e.materialsId}&title=${e.listeningName}`
          });
        } else {
          uni.navigateTo({
            url: `/Listen/reveiewAnswer?studentCode=${this.query.studentCode}&id=${e.materialsId}&title=${e.listeningName}`
          });
        }
      }
    },
    onReady() {
      uni.getSystemInfo({
        success: (res) => {
          // 可使用窗口高度，将px转换rpx
          this.useHeight = res.windowHeight * (750 / res.windowWidth);
        }
      });
    },
    onShow() {
      this.init();
    },
    onLoad(options) {
      this.query = JSON.parse(options.query);
    }
  };
</script>

<style scoped lang="scss">
  .contain {
    background-color: #fff;
    padding: 32rpx;
    box-sizing: border-box;
    overflow-y: auto;
    .items {
      height: 72rpx;
      background: #f5f8fa;
      border-radius: 8rpx;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding-left: 32rpx;
      padding-right: 18rpx;
      font-size: 28rpx;
      color: #555555;
      margin-bottom: 32rpx;
    }
  }
</style>
