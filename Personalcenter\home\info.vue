<template>
  <view class="plr-30">
    <view class="bg-ff radius-15 plr-30" :style="{ height: useHeight + 'rpx' }">
      <view class="flex info ptb-30">
        <view class="label f-30 c-33">头像</view>
        <view class="flex-a-c">
          <!-- 统一调用 chooseAvatar 方法，兼容 App 和小程序 -->
          <button class="avatar-wrapper" @click="chooseAvatar">
            <image class="header-image" :src="headPortrait || 'https://document.dxznjy.com/dxSelect/home_avaUrl.png'"></image>
          </button>
          <uni-icons type="right" size="20" color="#999999"></uni-icons>
        </view>
      </view>
      <view class="flex info">
        <view class="label f-30 c-33">昵称</view>
        <view class="flex-a-c" @click="goUrl">
          <text class="mr-10">{{ userinfo.nickName }}</text>
          <uni-icons type="right" size="20" color="#999999"></uni-icons>
        </view>
        <!-- <input type="nickname" class="t-r flex-box" @blur="getName" @input="inputName" :value="nickName" placeholder="请输入昵称" maxlength='10'/> -->
      </view>

      <view class="flex info">
        <view class="label f-30 c-33">手机号</view>
        <view class="">{{ userinfo.mobile }}</view>
      </view>
      <button class="info_sure" @click="$noMultipleClicks(userEdit)">确定</button>
    </view>
  </view>
</template>

<script>
  const { $http, $showMsg } = require('@/util/methods.js');
  export default {
    name: 'webview',
    data() {
      return {
        useHeight: 0,
        noClick: true, //防抖
        userinfo: {},
        headPortrait: '',
        avaUrl: '',
        app: 0,
        baseUrl: uni.getStorageSync('baseUrl')
      };
    },
    onLoad(e) {
      console.log(e);
      if (e.token) {
        this.app = e.app;
        this.$handleTokenFormNative(e);
      }
    },
    onReady() {
      uni.getSystemInfo({
        success: (res) => {
          // 可使用窗口高度，将px转换rpx
          let h = res.windowHeight * (750 / res.windowWidth);
          this.useHeight = h - 30;
        }
      });
    },
    onShow() {
      this.homeData();
      // this.setting();
    },
    onUnload() {
      // #ifdef APP-PLUS
      if (this.app) {
        plus.runtime.quit();
      }
      // #endif
    },
    methods: {
      async homeData() {
        let _this = this;
        const res = await $http({
          url: 'zx/user/userInfoNew'
        });
        if (res) {
          _this.userinfo = res.data;
          _this.headPortrait = _this.userinfo.headPortrait;
        }
      },
      // 统一选择头像入口
      chooseAvatar() {
        // #ifdef APP-PLUS
        uni.chooseImage({
          count: 1,
          sourceType: ['album', 'camera'],
          success: (chooseRes) => {
            const filePath = chooseRes.tempFilePaths[0];
            this.uploadAvatar(filePath);
          }
        });
        // #endif
        // #ifdef MP-WEIXIN
        uni.chooseAvatar({
          success: (e) => {
            const filePath = e.detail.avatarUrl;
            this.uploadAvatar(filePath);
          }
        });
        // #endif
      },
      // 上传并更新头像
      uploadAvatar(filePath) {
        uni.showLoading({ title: '上传中...' });
        uni.uploadFile({
          url: `${this.baseUrl}zx/common/uploadFile`,
          filePath,
          name: 'file',
          header: { Token: uni.getStorageSync('token') },
          success: (res) => {
            const data = JSON.parse(res.data);
            if (data.status === 1) {
              this.headPortrait = data.data.fileUrl;
            } else {
              uni.showToast({ title: data.message, icon: 'none' });
            }
          },
          fail: (err) => {
            $showMsg(err.errMsg);
          },
          complete: () => {
            uni.hideLoading();
          }
        });
      },
      async userEdit() {
        let _this = this;
        const res = await $http({
          url: 'zx/user/updateNickName',
          method: 'post',
          data: {
            headPortrait: _this.headPortrait,
            nickName: _this.userinfo.nickName
          }
        });
        uni.setStorageSync('nickName', _this.userinfo.nickName);
        uni.setStorageSync('avaUrl', _this.headPortrait);
        $showMsg(res.message);
        setTimeout(function () {
          uni.navigateBack();
        }, 2000);
      },
      // async setting() {
      // 	let _this = this
      // 	const res = await $http({
      // 		url: 'zx/setting/getSysSetting',
      // 	})
      // 	if (res) {
      // 		_this.pageShow = true;
      // 		// _this.studyCentre = res.data.studyCentre
      // 	}
      // },
      getName(e) {
        console.log(e.detail.value);
        this.userinfo.nickName = e.detail.value;
      },
      inputName(e) {
        console.log(e.detail.value);
        this.userinfo.nickName = e.detail.value;
      },
      onChooseAvatar(e) {
        let _this = this;
        uni.uploadFile({
          url: `${this.baseUrl}zx/common/uploadFile`,
          filePath: e.detail.avatarUrl,
          name: 'file',
          header: {
            Token: uni.getStorageSync('token')
          },
          success: function (res) {
            let data = JSON.parse(res.data);
            console.log(data);
            if (data.status == 1) {
              _this.headPortrait = data.data.fileUrl;
            } else {
              uni.showToast({
                title: data.message,
                icon: 'none'
              });
            }
          },
          fail: function (err) {
            $showMsg(err.errMsg);
          },
          complete: function (res) {
            uni.hideLoading();
          }
        });
      },

      goUrl() {
        uni.navigateTo({ url: '/Personalcenter/home/<USER>' });
      }
    }
  };
</script>

<style lang="scss" scoped>
  .info_sure {
    width: 600upx;
    height: 80upx;
    background-image: linear-gradient(to bottom, #88cfba, #1d755c);
    margin: 60upx auto;
    text-align: center;
    line-height: 80upx;
    color: #fff;
    border-radius: 45upx;
  }

  .info {
    display: flex;
    flex-direction: row;
    align-items: center;
    height: 120rpx;
    border-bottom: 1rpx solid #eee;
  }

  .avatar-wrapper {
    width: 100rpx;
    height: 100rpx;
    margin-right: 10rpx;
  }

  .header-image {
    width: 100rpx;
    height: 100rpx;
    border-radius: 50%;
  }
</style>
