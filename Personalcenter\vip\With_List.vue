<template>
  <view class="plr-30">
    <u-sticky>
      <!-- <view class="flex-dir-row bg-f3 flex-x-s flex-y-s tabs mt-30">
				<view class="flex-col col-2" @tap="tab(-1)">
					<text :class="tabindex==-1?'active':'unchecked'">全部</text>
					<view v-if="tabindex==-1" class="changing-over mt-15"></view>
				</view>
				<view class="flex-col col-3" @tap="tab(0)">
					<text :class="tabindex==0?'active':'unchecked'">未审核</text>
					<view v-if="tabindex==0" class="changing-over mt-15"></view>
				</view>
				<view class="flex-col col-4" @tap="tab(1)">
					<text :class="tabindex==1?'active':'unchecked'">通过待打款</text>
					<view v-if="tabindex==1" class="changing-over mt-15"></view>
				</view>
				<view class="flex-col col-3" @tap="tab(2)">
					<text :class="tabindex==2?'active':'unchecked'">未通过</text>
					<view v-if="tabindex==2" class="changing-over mt-15"></view>
				</view>
				<view class="flex-col col-3" @tap="tab(3)" v-if="type==1">
					<text :class="tabindex==3?'active':'unchecked'">已打款</text>
					<view v-if="tabindex==3" class="changing-over mt-15"></view>
				</view>
			</view> -->
      <view class="bg-ff p-30 flex_s f-30 c-66 radius-15 choose_time">
        <!-- <image src="/static/index/time-icon.png" class="time-icon"></image> -->
        <!-- <uni-datetime-picker type="daterange" @change="change"/> -->
        <!-- <picker mode="date" fields="month" @change="bindDateChange" bindchange="changeDate">
					<view class="pb-30 f-30 c-00">
						{{date}}
						<uni-icons class="ml-10" type="bottom" color="#c7c7c7" size="16"></uni-icons>
					</view>
				</picker> -->
        <view class="time-border flex-s plr-30">
          <view class="flex_s">
            <picker mode="date" @change="startChange">
              <view class="flex_s ptb-20">
                <image src="/static/index/time-icon.png" class="time-icon"></image>
                <view class="f-30 c-00 ml-30">{{ startDate || '开始时间' }} -</view>
              </view>
            </picker>
            <picker mode="date" @change="endChange">
              <view class="ptb-20">
                <view class="f-30 c-00 ml-30">{{ endDate || '结束时间' }}</view>
              </view>
            </picker>
          </view>
          <view>
            <uni-icons v-if="startDate == '' && endDate == ''" class="ml-10" type="right" color="#C7C7C7" size="16"></uni-icons>
            <uni-icons v-if="startDate != '' || endDate != ''" class="ml-10" type="closeempty" color="#C7C7C7" size="16" @click="cleartime"></uni-icons>
          </view>
        </view>

        <!-- <view class="icon">
					<u-icon name="arrow-right" color="#C7C7C7"></u-icon>
				</view> -->
      </view>
    </u-sticky>
    <view class="bg-ff mt-30 p-30 radius-15" v-for="(item, index) in listS.list" :key="index" @click="skintap('Personalcenter/vip/WithDetails?id=' + item.withdrawId)">
      <view class="flex-s">
        <view>
          提现:
          <text class="f-36 bold color_tangerine">+{{ item.withdrawAmount }}元</text>
        </view>
        <view class="flex-dir-col flex-x-e f-26 c-ff">
          <view class="unaudited" v-if="item.verifyResult == 0">提现中</view>
          <view class="treat_pay" v-if="item.verifyResult == 1">提现成功</view>
          <view class="unpaid" v-if="item.verifyResult == 2">提现失败</view>
        </view>
      </view>
      <view class="f-30 c-66 mt-20">{{ item.createdTime }}</view>
    </view>
    <view v-if="listS.list != undefined && listS.list.length == 0" class="t-c flex-col" :style="{ height: useHeight + 'rpx' }">
      <image :src="imgHost + 'alading/correcting/no_data.png'" class="mb-20 img_s" mode="widthFix"></image>
      <view style="color: #bdbdbd">暂无数据</view>
    </view>
    <view v-if="no_more && listS.list.length > 0">
      <u-divider text="到底了"></u-divider>
    </view>
  </view>
</template>

<script>
  const { $navigationTo, $http } = require('@/util/methods.js');
  export default {
    data() {
      return {
        imgHost: getApp().globalData.imgsomeHost,
        tabindex: -1,
        listS: {},
        page: 1,
        no_more: false,
        useHeight: 0, //除头部之外高度
        altitudeShow: false, // 判断屏幕高度
        type: 1, // 1学习超人 2俱乐部
        // date:"",
        startDate: '', // 开始时间
        endDate: '' // 结束时间
      };
    },
    onLoad(e) {
      this.type = e.type;
    },
    onReady() {
      uni.getSystemInfo({
        success: (res) => {
          // 可使用窗口高度，将px转换rpx
          let h = res.windowHeight * (750 / res.windowWidth);
          this.useHeight = h - 140;
        }
      });
    },
    onShow() {
      this.list();
    },
    onReachBottom() {
      if (this.page >= this.listS.totalPage) {
        this.no_more = true;
        return false;
      }
      this.list(true, ++this.page);
    },
    methods: {
      // 日期选择
      // change(e){
      // 	console.log(e)
      // 	setTimeout(() => {
      // 		this.startDate = e[0];
      // 		this.endDate = e[1];
      // 		this.list()
      // 	}, 500)
      // },

      startChange(e) {
        console.log(e);
        this.startDate = e.detail.value;
        if (this.startDate != '' && this.endDate != '') {
          if (this.startDate > this.endDate) {
            this.$util.alter('开始时间不能大于结束时间');
            return;
          }
          const result = this.isTimeSpanGreaterThanOneYear(this.startDate, this.endDate);
          if (result) {
            this.$util.alter('时间跨度不能大于一年哦');
            return;
          }
          this.list();
        }
      },

      endChange(e) {
        this.endDate = e.detail.value;
        if (this.startDate != '' && this.endDate != '') {
          if (this.startDate > this.endDate) {
            this.$util.alter('开始时间不能大于结束时间');
            return;
          }
          const result = this.isTimeSpanGreaterThanOneYear(this.startDate, this.endDate);
          if (result) {
            this.$util.alter('时间跨度不能大于一年哦');
            return;
          }
          this.list();
        } else {
          this.$util.alter('请选择开始时间');
        }
      },

      isTimeSpanGreaterThanOneYear(date1, date2) {
        const oneYearMilliseconds = 365 * 24 * 60 * 60 * 1000; // 一年的毫秒数
        // 转换为时间戳进行比较
        const timestamp1 = new Date(date1).getTime();
        const timestamp2 = new Date(date2).getTime();

        // 计算时间跨度
        const timeSpan = Math.abs(timestamp2 - timestamp1);

        // 比较时间跨度是否大于一年的毫秒数
        return timeSpan > oneYearMilliseconds;
      },

      cleartime() {
        this.startDate = '';
        this.endDate = '';
        this.list();
      },
      // tab(e) {
      //     this.tabindex = e;
      // 	this.page = 1
      // 	this.no_more = false
      // 	this.list()
      // },
      // reason(e) {
      // 	uni.showModal({
      // 		title: '理由',
      // 		content: e,
      // 		showCancel: false,
      // 		success: function(res) {
      // 			if (res.confirm) {
      // 				console.log('用户点击确定');
      // 			} else if (res.cancel) {
      // 				console.log('用户点击取消');
      // 			}
      // 		}
      // 	});
      // },
      async list(isPage, page) {
        let _this = this;
        const res = await $http({
          url: 'zx/user/userWithdrawApplyList',
          data: {
            startDate: _this.startDate,
            endDate: _this.endDate,
            type: _this.type,
            pageSize: 10,
            page: page || 1
          }
        });
        if (res) {
          if (isPage) {
            let old = _this.listS.list;
            _this.listS.list = [...old, ...res.data.list];
          } else {
            _this.listS = res.data;
          }
        }
      },

      skintap(url) {
        $navigationTo(url);
      }
    }
  };
</script>

<style lang="scss" scoped>
  .choose_time {
    position: relative;
    width: 92%;
  }

  .icon {
    position: absolute;
    right: 55rpx;
  }

  .flex_s {
    display: flex;
    align-items: center;
  }

  .time-icon {
    width: 30rpx;
    height: 30rpx;
    z-index: 9;
  }

  // /deep/ .uni-date-x{
  // 	margin-left: 40rpx;
  // 	width: 90%;
  // }

  // /deep/.u-calendar-month__days__day__select__buttom-info {
  //     bottom: 5rpx !important;
  // }

  // /deep/.uni-date-x--border{
  // 	border-radius: 45rpx !important;
  // }

  // /deep/.uni-date-x{
  // 	background-color: transparent !important;
  // }

  // /deep/.uni-date-x{
  // 	flex: inherit !important;
  // }

  // /deep/.uni-date__x-input{
  // 	margin-right: 30rpx !important;
  // }
  // /deep/.range-separator{
  // 	margin-right: 30rpx !important;
  // }

  .changing-over {
    background-color: #2e896f;
    width: 30rpx;
    height: 4rpx;
  }
  .tabs {
    // padding: 30rpx 0;
    width: 100%;
  }

  .tabs text {
    font-size: 30upx;
    color: #666;
  }

  .tabs .active {
    color: #000;
    font-size: 34rpx;
    font-weight: bold;
  }

  .tabs .unchecked {
    color: #666;
    font-size: 32rpx;
  }

  .unpaid {
    background-color: #e57126;
    height: 36rpx;
    padding: 2rpx 6rpx;
    border-radius: 4rpx;
    line-height: 36rpx;
  }

  .treat_pay {
    background-color: #35a8f7;
    height: 36rpx;
    padding: 2rpx 6rpx;
    border-radius: 4rpx;
    line-height: 36rpx;
  }

  .paid {
    background-color: #2dc032;
    height: 36rpx;
    padding: 2rpx 6rpx;
    border-radius: 4rpx;
    line-height: 36rpx;
  }

  .unaudited {
    background-color: #c6c6c6;
    height: 36rpx;
    padding: 2rpx 6rpx;
    border-radius: 4rpx;
    line-height: 36rpx;
  }

  .border_top {
    border-bottom: 1px dashed #eee;
  }

  .img_s {
    width: 160rpx;
  }

  .time-border {
    border: 1rpx solid #c8c8c8;
    border-radius: 45rpx;
    width: 568rpx;
  }
</style>
