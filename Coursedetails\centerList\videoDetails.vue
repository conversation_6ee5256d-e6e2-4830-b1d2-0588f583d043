<template>
  <view class="wrap">
    <!-- <u-radio-group
      v-model="radiovalue"
      placement="row"
      @change="groupChange"
    >
      <u-radio
        v-for="(item, index) in radiolist1"
        :key="index"
        :label="item.name"
        :name="item.name"
        @change="radioChange"
      >
      </u-radio>
    </u-radio-group> -->

    <!-- <video class="videoSrC" src="http://www.example.com/video.mp4"></video> -->
    <view class="video_css">
      <polyv-player
        v-if="wifiShow"
        width="100"
        isAllowSeek="no"
        @timeUpdate="handleTimeUpdate"
        @playing="bindplaying"
        @loadedmetadata="bindloadedmetadata"
        @ended="bindEnded"
        :autoplay="false"
        :playerId="playerIdcont"
        height="100"
        :ts="ts"
        :sign="sign"
        id="polyv_player"
        :vid="videoUrl"
      ></polyv-player>
      <view v-else class="no_wifi_css">
        <view class="wifi_title">非wifi网络播放将会产生流量费用，是否继续播放？</view>
        <button class="ban_css" @click="playVideo" type="default">继续播放</button>
      </view>
    </view>

    <h2 class="course_title">{{ courseName || '' }}</h2>
    <h2 v-if="(learningStatus === '3' && !isFeedback) || isLook" @tap="courseFeedback" class="course_feedback">课程反馈</h2>
    <h2 v-if="isFeedback" class="course_feedback">已反馈</h2>

    <u-popup :closeOnClickOverlay="false" :show="showModify" mode="center" :closeable="true" @close="showModify = false" :customStyle="modifyPopupStyle">
      <view class="modifyContent">
        <view class="title">课程反馈</view>
        <view style="display: flex; align-items: flex-start">
          <uni-icons color="#d9501b" type="star-filled" size="10"></uni-icons>
          <h2 class="titleText">您觉得这节课程对您是否有帮助,是否达到了学习预期？</h2>
        </view>
        <view style="margin-top: 25rpx">
          <u-radio-group size="30" labelSize="30" v-model="radiovalue" placement="row">
            <u-radio size="30" labelSize="30" v-for="(item, index) in radiolist1" :key="index" :label="item.name" :name="item.value" @change="radioChange"></u-radio>
          </u-radio-group>
        </view>
        <h2 class="proposal">您对我们还有什么其他的建议或意见吗？</h2>
        <u--textarea count v-model="remark" maxlength="200" placeholder="请输入您的建议或意见" height="140rpx" border="none" :customStyle="textareaStyle"></u--textarea>
        <view class="buttons">
          <view @click="comfirm">提交</view>
        </view>
      </view>
    </u-popup>
  </view>
</template>

<script>
  const { $http } = require('@/util/methods.js');
  let vid = '';
  let ts = new Date().getTime();
  let sign = '';
  export default {
    components: {
      // uniDataCheckbox,
    },
    data() {
      return {
        remark: '',
        courseName: '',
        courseId: '',
        // 课时id
        id: '',
        vid: vid,
        ts: ts,
        sign: sign,
        videoUrl: '',
        playerIdcont: 'polyvPlayercont',
        learningExpectation: '',
        isLook: false,
        isFeedback: null,
        learningStatus: '',
        wifiShow: true,
        radiolist1: [
          {
            name: '非常好',
            value: 1,
            disabled: false
          },
          {
            name: '较好',
            value: 2,
            disabled: false
          },
          {
            name: '一般',
            value: 3,
            disabled: false
          },
          {
            name: '不太好',
            value: 4,
            disabled: false
          },
          {
            name: '较差',
            value: 5,
            disabled: false
          }
        ],

        radiolist: [
          { text: '优秀', value: 1 },
          { text: '良好', value: 2 },
          { text: '薄弱', value: 3 }
        ],
        radiovalue: '',
        showModify: false,
        modifyPopupStyle: {
          width: '624rpx',
          height: '774rpx',
          padding: '48rpx',
          'box-sizing': 'border-box',
          'border-radius': '24rpx'
        },
        textareaStyle: {
          background: '#ffffff',
          height: '140rpx',
          border: '2rpx solid #e5e5e5',
          'margin-top': '27rpx',
          'border-radius': '16rpx'
        },
        isLastLesson: false,
        isExamPass: false
      };
    },
    onLoad(option) {
      console.log(option, '视频详情');
      const { isLastLesson, isExamPass } = option;
      this.isLastLesson = Boolean(isLastLesson === 'false' ? false : true) || false;
      this.isExamPass = Boolean(isExamPass === 'false' ? false : true) || false;

      this.courseName = option.name;
      this.id = option.id;
      this.courseId = option.courseId;
      this.videoUrl = option.filePath;
      this.isFeedback = Boolean(option.isFeedback === 'false' ? false : true);
      console.log(this.isFeedback, 'this.isFeedback----');

      this.learningStatus = option.learningStatus;
      uni.getNetworkType({
        success: (res) => {
          console.log(res);
          if (res.networkType == 'wifi') {
            this.wifiShow = true;
          } else {
            this.wifiShow = false;
          }
        }
      });
      this.getLearningStatus(2);
      // this.atu()
    },
    onUnload() {
      this.stopAudio();
      console.log('停止视频播放--------');
    },
    methods: {
      courseFeedback() {
        this.showModify = true;
      },
      // 生成证书
      async createCert() {
        let certificateInfo = JSON.parse(uni.getStorageSync('certificateInfo'));
        let certificateRole = uni.getStorageSync('certificateRole');
        const params = {
          courseId: this.courseId,
          mobile: certificateInfo.mobile,
          realName: certificateInfo.userName,
          roleTag: certificateRole
        };
        await $http({
          url: 'train/web/certificate/createCertificate',
          data: params
        });
      },
      bindEnded() {
        this.isLook = true;
        this.getLearningStatus(3);
        console.log('视频播放结束');
      },
      async getLearningStatus(num) {
        const res = await $http({
          url: 'train/web/exam/training/lesson_progress',
          method: 'POST',
          data: {
            userId: uni.getStorageSync('bvAdminId') ? uni.getStorageSync('bvAdminId') : '',
            // courseId: this.courseId,
            learningStatus: num,
            lessonId: this.id,
            roleTag: uni.getStorageSync('roleTag') ? uni.getStorageSync('roleTag') : ''
          }
        });
        // 视屏播放结束，若此课时为当前课程最后一课时，且课程无需考试或考试已通过，则调用生成证书接口
        if (num == 3 && this.isLastLesson && this.isExamPass) {
          this.createCert();
        }
      },
      stopAudio() {
        let polyvPlayerContext = this.selectComponent('#polyv_player');
        if (polyvPlayerContext) {
          polyvPlayerContext.pause(); // 停止视频播放
        }
      },
      bindloadedmetadata() {
        let polyvPlayerContext = this.selectComponent('#polyv_player');
        polyvPlayerContext.switchQuality(1);
      },
      bindplaying() {
        console.log('开始开始开始');
      },
      playVideo() {
        this.wifiShow = true;
      },
      // atu() {
      //     let polyvPlayerContext = this.selectComponent('#polyv_player');
      //     if (polyvPlayerContext) {
      //         const ts = new Date().getTime();
      //         const sign = MD5.md5(`${secretkey}${vid}${ts}`);
      //         polyvPlayerContext.changeVid({
      //             vid: this.videoUrl,
      //             ts,
      //             sign
      //         });
      //     } else {
      //         console.log('Polyv player context is null');
      //     }
      // },
      async comfirm() {
        const res = await $http({
          url: 'train/web/exam/feedback/add',
          method: 'POST',
          data: {
            userId: uni.getStorageSync('bvAdminId') ? uni.getStorageSync('bvAdminId') : '',
            learningExpectation: this.learningExpectation,
            courseId: this.courseId,
            courseLessonId: this.id,
            userRoleTag: uni.getStorageSync('roleTag') ? uni.getStorageSync('roleTag') : '',
            userName: uni.getStorageSync('nickName') ? uni.getStorageSync('nickName') : '',
            learningOpinion: this.remark
          }
        });
        if (res.code !== 20000) return uni.showToast({ title: res.message, icon: 'error' });
        uni.showToast({ title: '反馈成功', icon: 'success' });
        this.showModify = false;
        uni.navigateBack();
        //   this.showModify = false
      },
      radioChange(row) {
        console.log(row, '55555');
        //   console.log(this.radiovalue,'radiovalue');
        this.learningExpectation = row;
      }
    }
  };
</script>

<style scoped lang="scss">
  .videoSrC {
    width: 100%;
  }

  .no_wifi_css {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    background-color: #000;
    font-size: 24rpx;
    color: #fff;
    text-align: center;

    .wifi_title {
      width: 376rpx;
      margin: 0 auto;
      padding-top: 132rpx;
      line-height: 34rpx;
    }

    .ban_css {
      width: 168rpx;
      height: 56rpx;
      border-radius: 8rpx;
      background-color: #45d670;
      margin: 0 auto;
      font-size: 24rpx;
      color: #fff;
      line-height: 55rpx;
      margin-top: 34rpx;
    }
  }

  .video_css {
    position: relative;
    height: 422rpx;
    background-color: #e4e4e4;
  }

  .course_title {
    padding: 22rpx 32rpx;
    color: #333333;
    font-size: 30rpx;
  }

  .course_feedback {
    padding: 22rpx 32rpx;
    color: #489981;
    font-size: 28rpx;
  }

  .modifyContent {
    width: 100%;

    .title {
      width: 100%;
      color: #11372e;
      text-align: center;
      margin-bottom: 20rpx;
    }
  }

  .buttons {
    display: flex;
    margin-top: 50rpx;
    // margin-bottom: 20rpx;

    & > view {
      flex: 1;
      height: 72rpx;
      line-height: 70rpx;
      text-align: center;
      border-radius: 240rpx;
      font-size: 28rpx;
      font-weight: 500;
      color: #ffffff;
      background-color: #418a6f;
    }

    // & > view:nth-of-type(1) {
    //   margin-right: 24rpx;
    //   border: 2rpx solid #999999;
    //   color: #999999;
    // }
  }

  .titleText {
    color: #555555;
    font-size: 28rpx;
    font-weight: bold;
  }

  .proposal {
    margin-top: 40rpx;
    color: #555555;
    font-size: 28rpx;
    font-weight: bold;
  }

  ::v-deep .u-radio {
    margin-top: 26rpx !important;
  }

  ::v-deep .u-radio-group--column {
    display: flex !important;
    flex-direction: none !important;
    flex-wrap: wrap !important;
    justify-content: space-around !important;
    // background: red !important;
  }

  ::v-deep .u-radio-group--row {
    display: flex !important;
    flex-direction: row !important;
    flex-wrap: wrap !important;
    justify-content: space-between !important;
  }

  ::v-deep .uicon-close {
    font-size: 40rpx !important;
  }

  // ::v-deep .u-radio-group{
  // 	background-color: red !important;
  // 	font-size: 40rpx !important;
  // 	height: 200rpx;
  // 	width: 200rpx;
  // }
</style>
