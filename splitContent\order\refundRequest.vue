<template>
	<view class="plr-30 positionRelative" :style="{height:useHeight-50 + 'rpx'}">
		<view class="plr-30 ptb-35 bg-ff radius-15">
			<view class="bold f-32 mb-30">申请退款说明</view>
			<view class="textarea positionRelative">
				<textarea placeholder="必填,请详细填写申请说明" placeholder-style="color:#999" maxlength="150" 
				style="width: 100%;height: 100%;" @input="sumfontnum"/>
				<view class="words c-99">{{fontNum}}/150</view>
			</view>
		</view>
		
		<view class="bg-ff plr-30 ptb-35 radius-15 mt-30">
			<view class="flex-s border-b pb-30">
				<view>退款数量</view>
				<view class="flex-a-c flex-x-e">
					<uni-icons type="minus" size="26" color="#C6C6C6" @click="reduce"></uni-icons>
					<input class="uni-input" v-model="changeValue" @blur="onblur"/>
					<uni-icons type="plus" size="26" color="#2E896F" @click="add"></uni-icons>
				</view>
			</view>
			
			<view class="flex-s border-b ptb-30">
				<view>退款金额</view>
				<view class="">￥1000</view>
			</view>
			
			<view class="flex-a-c pt-30">
				<view>联系电话：</view>
				<view class="flex-a-c ml-30">
					<input ref="inputRefs" type="number" :value="phone" @input="phoneInput" placeholder="请输入" maxlength="11"/>
					<uni-icons v-if="showClearIcon" type="clear" size="24" color="#C6C6C6" @click="clearInput" class="clear-icon"></uni-icons>
				</view>
			</view>
		</view>
		<view class="positionAbsolute apply-for">
			<view class="btn" @click="goChoose">提交申请</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				fontNum:0, // 文本域字数
				assess:"",
				changeValue:1, // 退款数量
				phone:"182225511698",
				showClearIcon: true,
				useHeight:0
			};
		},
		onReady() {
			uni.getSystemInfo({
				success: (res) => {
					// 可使用窗口高度，将px转换rpx
					this.useHeight = (res.windowHeight * (750 / res.windowWidth));
				}
			})
		},
		methods:{
			// 限制文本框字数
			sumfontnum(e) {
				console.log(e)
				this.fontNum = e.detail.value.length;
				this.assess = e.detail.value;
			},
			
			onblur(e){
				this.changeValue = e.detail.value;
			},
			
			reduce(){
				if(this.changeValue>1){
					this.changeValue --
				}
			},
			
			add(){
				this.changeValue ++
			},
			
			
			phoneInput(e){
				this.phone = e.detail.value;
				if (e.detail.value.length > 0) {
					this.showClearIcon = true;
				} else {
					this.showClearIcon = false;
				}
			},
			
			clearInput(){
				this.phone = '';
				this.showClearIcon = false;
			},
		}
	}
</script>

<style lang="scss" scoped>
	.textarea{
		// width: 100%;
		height: 470rpx;
		padding: 30rpx;
		font-size: 30rpx;
		border-radius: 15rpx;
		background-color: #f7f7f7;
	}
	
	.words{
		position: absolute;
		bottom: 30rpx;
		right: 30rpx;
	}
	
	.uni-input{
		width: 15%;
		margin: 0 10rpx;
		text-align: center;
	}
	
	.border-b{
		border-bottom: 1px solid #EFEFEF;
	}
	
	.clear-icon{
		margin-left: 30rpx;
	}
	
	
	.apply-for{
		bottom: 0;
		width: 91.4%;
		padding: 0 30rpx;
	}
	
	.btn{
		color: #fff;
		width: 630rpx;
		height: 90rpx;
		font-size: 30rpx;
		line-height: 90rpx;
		text-align: center;
		border-radius: 45rpx;
		background-image: linear-gradient(to bottom, #88CFBA, #1D755C);
	}
</style>
