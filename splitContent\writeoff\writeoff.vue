<template>
	<view>
		<view class="m-30">
			<view class="plr-30 ptb-20 radius-20 mb-20 bg-ff">
				<view class=" ptb-10  flex-dir-row " v-for="(item,index) in detail_s.orderCourseList" :key="index">
					<view class="box-160 radius-10 mr-30">
						<image :src="item.courseImage" class="wh100"></image>
					</view>
					<view class="flex-box">
						<view class="f-28 c-11 bold elli-2 mb-30">{{ item.courseName }}</view>
						<view class="flex">
							<text class="f-28 c-11 bold">￥{{ item.coursePrice }}</text>
							<text class="c-88">X{{ item.buyNumber }}</text>
						</view>
					</view>
				</view>
			</view>
			<view class="bg-ff pt-30 plr-30 radius-20">
				<view class="flex pb-30 f-28">
					<text class="c-66">创建时间</text>
					<text class="c-33">{{ detail_s.createdTime }}</text>
				</view>
				<view class="flex pb-30 f-28" v-if="detail_s.payStatus!=0">
					<text class="c-66">支付时间</text>
					<text class="c-33">{{ detail_s.payTime }}</text>
				</view>
				<view class="flex pb-30 f-28">
					<text class="c-66">订单编号</text>
					<text class="c-33">{{ detail_s.orderNo }}</text>
				</view>
			</view>
		</view>
		<view class="flexedbb">
			<view class="p-30">
				<button class="submit f-30" @tap="code">确认核销</button>
			</view>
		</view>
	</view>
</template>

<script>
	const {
		$http
	} = require('@/util/methods.js')
	export default {
		data() {
			return {
				pageShow: true,
				tabindex: 0,
				id: null,
				detail_s: null,
				codeimg: null
			}
		},
		onLoad(e) {
			console.log(e)
			this.id = e.scene
		},
		onShow() {
			this.orderdetail()
		},
		methods: {
			async orderdetail() {
				let _this = this
				const res = await $http({
					url: 'zx/order/userCourseOrderDetail',
					data: {
						orderId: _this.id
					}
				})
				if (res) {
					_this.detail_s = res.data
				}
			},
			async code() {
				let _this = this
				uni.showModal({
					title: '提示',
					content: '确认核销吗？',
					success: async function(res) {
						if (res.confirm) {
							const resdata = await $http({
								url: 'zx/order/orderCheck',
								data: {
									orderId: _this.id,
								}
							})
							if (resdata) {
								$showError(resdata.message)
								setTimeout(function() {
									_this.orderdetail();
								}, 2000)
							}
						} else if (res.cancel) {
							console.log('用户点击取消');
						}
					}
				});
			},
			// 状态切换
			changetab(index) {
				this.tabindex = index
			},
		}
	}
</script>

<style>
	.codebg {
		width: 296upx;
		height: 296upx;
	}

	.submit {
		width: 100%;
		height: 88rpx;
		border-radius: 44rpx;
		line-height: 88rpx;
		color: #fff;
		text-align: center;
		background-color: #006658;
	}

	.flexedbb {
		width: 100%;
		position: fixed;
		left: 0;
		bottom: 14rpx;
	}

	.codeimg {
		position: absolute;
		width: 260upx;
		height: 260upx;
		top: 18upx;
		left: 18upx;
	}
</style>
