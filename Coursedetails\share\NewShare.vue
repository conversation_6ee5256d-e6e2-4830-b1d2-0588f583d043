<template>
  <view style="height: 1600prx; background-color: #2e896f">
    <view v-if="switchstate">
      <image ref="why" :src="path" mode="widthFix" style="width: 100%" :show-menu-by-longpress="true"></image>

      <l-painter isCanvasToTempFilePath ref="painter" @success="path = $event" custom-style="position: fixed; left: 200%" css="width: 750rpx; padding-bottom: 40rpx">
        <l-painter-image
          src="https://document.dxznjy.com/app/images/zhujiaoduan/feedback_bg.png"
          css="position: absolute;top:30rpx;object-fit: contain;width: 100%;"
        ></l-painter-image>
        <l-painter-view css="position: absolute;top:190rpx;z-index:2;width:100%;text-align:center;color:#FFFFFF;font-size:34rpx;font-weight: bold;">
          <l-painter-text text="学习反馈" />
        </l-painter-view>
        <l-painter-view css="position: relative;margin-top: 320rpx; padding: 32rpx;box-sizing: border-box; background: #fff;border:30rpx solid #2e896f;">
          <!-- 学习反馈  -->
          <l-painter-view>
            <l-painter-text
              v-if="backdetails.curriculumName === '鼎学能'"
              :text="'课程类型：' + backdetails.curriculumName"
              css="font-size: 30rpx;display: block; margin-bottom: 30rpx; "
            />
            <l-painter-text :text="'日期：' + backdetails.dateTime" css="font-size: 30rpx;display: block; margin-bottom: 30rpx; " />
            <l-painter-text :text="'姓名：' + backdetails.studentName" css="font-size: 30rpx;display: block; margin-bottom: 30rpx; " />
            <l-painter-text :text="'年级：' + backdetails.gradeName" css="font-size: 30rpx;display: block; margin-bottom: 30rpx; " />
            <l-painter-text :text="'学员编号：' + backdetails.studentCode" css="font-size: 30rpx;display: block; margin-bottom: 30rpx; " />
            <l-painter-text v-if="backdetails.curriculumName !== '珠心算'" :text="'时间：' + backdetails.studyTime" css="font-size: 30rpx;display: block; margin-bottom: 30rpx; " />

            <l-painter-view>
              <l-painter-text
                v-if="backdetails.curriculumName === '拼音法' || backdetails.curriculumName === '珠心算' || backdetails.curriculumName === '拼音法（高年级）'"
                :text="'实际时间：' + backdetails.actualStart + '~' + backdetails.actualEnd"
                css="font-size: 30rpx;display: block; margin-bottom: 30rpx; "
              />
              <l-painter-text
                v-if="backdetails.curriculumName === '拼音法' || backdetails.curriculumName === '拼音法（高年级）'"
                :text="'学习学时：' + backdetails.studyHour + '小时'"
                css="font-size: 30rpx;display: block; margin-bottom: 30rpx; "
              />
              <l-painter-text
                v-if="backdetails.curriculumName === '拼音法' || backdetails.curriculumName === '拼音法（高年级）'"
                :text="'所学课程类型：' + backdetails.curriculumName + backdetails.extendProperty.courseTypeName"
                css="font-size: 30rpx;display: block; margin-bottom: 30rpx; "
              />
              <l-painter-text
                v-if="backdetails.curriculumName === '拼音法' || backdetails.curriculumName === '拼音法（高年级）'"
                :text="'所学课程名称：' + backdetails.extendProperty.courseNames"
                css="font-size: 30rpx;display: block; margin-bottom: 30rpx; "
              />
              <l-painter-text
                v-if="backdetails.curriculumName === '拼音法' || backdetails.curriculumName === '拼音法（高年级）'"
                :text="'学习元辅音个数：' + backdetails.extendProperty.consonantCounts"
                css="font-size: 30rpx;display: block; margin-bottom: 30rpx; "
              />
              <l-painter-text
                v-if="backdetails.curriculumName === '拼音法' || backdetails.curriculumName === '拼音法（高年级）'"
                :text="'学习音节个数：' + backdetails.extendProperty.syllableCounts"
                css="font-size: 30rpx;display: block; margin-bottom: 30rpx; "
              />
              <l-painter-text
                v-if="backdetails.curriculumName === '拼音法' || backdetails.curriculumName === '拼音法（高年级）'"
                :text="'学习单词个数：' + backdetails.extendProperty.wordCounts"
                css="font-size: 30rpx;display: block; margin-bottom: 30rpx; "
              />
            </l-painter-view>
            <l-painter-view v-if="backdetails.curriculumName === '鼎学能'">
              <l-painter-text :text="'上课用时：' + secondsToTimeFormat(backdetails.extendProperty.costTime)" css="font-size: 30rpx;display: block; margin-bottom: 30rpx; " />
              <l-painter-text :text="'已购鼎学能课时：' + backdetails.extendProperty.haveCourseTime + '小时'" css="font-size: 30rpx;display: block; margin-bottom: 30rpx; " />
              <l-painter-text :text="'剩余鼎学能课时：' + backdetails.extendProperty.surplusCourseTime + '小时'" css="font-size: 30rpx;display: block; margin-bottom: 30rpx; " />
              <l-painter-view css="margin-bottom: 24rpx;">
                <l-painter-text :text="'突出项：'" css=" font-size: 30rpx; display: block; margin-bottom: 16rpx; " />
                <l-painter-text :text="'' + backdetails.extendProperty.highLights" css="line-height: 40rpx; font-weight: 600; font-size: 30rpx; color: #2F896F;" />
              </l-painter-view>
              <l-painter-view css="margin-bottom: 24rpx;">
                <l-painter-text :text="'推荐提升项：'" css=" display: block; line-height: 40rpx;font-size: 30rpx; margin-bottom: 16rpx; " />
                <l-painter-text :text="'' + backdetails.extendProperty.shortComing" css="font-size: 30rpx; font-weight: 600;color: #EC7A52;" />
              </l-painter-view>
              <l-painter-view>
                <l-painter-text
                  :text="'正确题数：' + backdetails.extendProperty.correctNums"
                  css="text-align: right; font-size: 30rpx; margin-right: 16rpx; line-height: 40rpx; color: rgba(51,51,51,1);margin-bottom: 30rpx;"
                />
              </l-painter-view>
              <l-painter-view>
                <l-painter-text
                  :text="'错误题数：' + backdetails.extendProperty.errorNums"
                  css="text-align: right; font-size: 30rpx;margin-right: 16rpx; line-height: 40rpx; color: rgba(51,51,51,1);margin-bottom: 30rpx;"
                />
              </l-painter-view>
              <!--雷达图-->
              <l-painter-view :style="{ display: isShowRadar ? 'block' : 'none' }">
                <l-painter-view class="qiun-charts">
                  <canvas canvas-id="canvasRadar" id="canvasRadar" class="charts" ref="canvasRadar"></canvas>
                </l-painter-view>
              </l-painter-view>
              <l-painter-view>
                <!-- <l-painter-image :src="backdetails.imageUrl" css="object-fit: contain;width: 100%;"></l-painter-image> -->
                <l-painter-image :src="backdetails.imageUrl1" css="object-fit: contain;width: 100%;"></l-painter-image>
              </l-painter-view>
            </l-painter-view>
          </l-painter-view>

          <l-painter-view>
            <l-painter-text text="教练评语：" css="font-size: 30rpx;display: block;margin-bottom: 30rpx;line-height: 1.8em" />
            <l-painter-view css="padding: 30rpx;box-sizing: border-box;background: rgba(153, 153, 153, 0.08);border-radius: 8rpx;width: 630rpx;min-height:481rpx;">
              <l-painter-text :text="backdetails.feedback" css="font-size: 30rpx;display: block;line-height: 1.8em" />
            </l-painter-view>
          </l-painter-view>
        </l-painter-view>
      </l-painter>
    </view>
    <view v-else>
      <image :src="path" mode="widthFix" style="width: 100%"></image>
      <l-painter
        isCanvasToTempFilePath
        ref="painter"
        @success="path = $event"
        custom-style="position: fixed; left: 200%"
        css="background-color: #5b5cff;width: 750rpx; padding-bottom: 40rpx;"
      >
        <l-painter-view css="position: relative;object-fit:fill;height:100%;background-image:url(https://document.dxznjy.com/applet/jiazhang/bgc.png)">
          <l-painter-view css="position: fixed;">
            <l-painter-image src="https://document.dxznjy.com/alading/pic/logo.png" css="margin-left: 50rpx; margin-top: 50rpx; width: 115rpx;" />

            <l-painter-view css="margin-top: 50rpx; padding-left: 20rpx;">
              <l-painter-text
                :text="totallist.studentName + '的学习报告' || ' ' + '的学习报告'"
                css="opacity: 0.7; padding-bottom: 10rpx; color: #fff; font-size: 48rpx; display: block; margin-left: 30rpx;"
              />
              <l-painter-text text="Study Report" css="opacity: 0.7;color: #fff; font-size: 30rpx; margin-left: 30rpx;" />
            </l-painter-view>
          </l-painter-view>

          <l-painter-view
            css="position: relative;margin-left: 25rpx; margin-top: 363rpx; padding: 32rpx; box-sizing: border-box; background: #fff; border-radius: 16rpx; width: 700rpx;"
          >
            <l-painter-image
              src="https://document.dxznjy.com/alading/pic/cattle.png"
              css="position: absolute;right:0rpx;top:-260rpx;object-fit: contain;width: 158rpx;"
            ></l-painter-image>
            <l-painter-view
              css="position: absolute;border-radius: 10rpx;top:-90rpx;left:-40rpx;width: 200rpx;height:70rpx;background: linear-gradient(,#F3C717 100%, #FEE506 100%)"
            >
              <l-painter-text text="学习详情" css="box-shadow: #999;margin-left: 35rpx;line-height: 70rpx;font-size: 32rpx;color:#fff;text-align: center;"></l-painter-text>
            </l-painter-view>
            <l-painter-view css="margin-top: 32rpx; color: #000;line-height: 1em;">
              <l-painter-text :text="'姓名：' + totallist.studentName || ''" css="font-size: 30rpx;display: block; margin-bottom: 30rpx; " />
              <l-painter-text :text="'年级：' + totallist.gradeName || ''" css="font-size: 30rpx;display: block; margin-bottom: 30rpx; " />
              <l-painter-text :text="'所学内容：' + totallist.studyBooks || ''" css="font-size: 30rpx;display: block; margin-bottom: 30rpx; " />
              <l-painter-text :text="'学习进度：' + totallist.learnSchedule + '%' || '' + '%'" css="font-size: 30rpx;display: block; margin-bottom: 30rpx; " />
              <l-painter-text :text="'复习词汇：' + totallist.reviewWords + '个' || '' + '个'" css="font-size: 30rpx;display: block; margin-bottom: 30rpx; " />
              <l-painter-text :text="'复习遗忘词汇：' + totallist.forgetWords + '个' || '' + '个'" css="font-size: 30rpx;display: block; margin-bottom: 30rpx; " />
              <l-painter-text :text="'复习遗忘率：' + totallist.forgetRate + '%' || '' + '%'" css="font-size: 30rpx;display: block; margin-bottom: 30rpx; " />
              <l-painter-text :text="'学新词汇：' + totallist.newWords + '个' || '' + '个'" css="font-size: 30rpx;display: block; margin-bottom: 30rpx; " />
              <l-painter-text :text="'学新遗忘词汇：' + totallist.newForget + '个' || '' + '个'" css="font-size: 30rpx;display: block; margin-bottom: 30rpx; " />
              <l-painter-text :text="'学新遗忘率：' + totallist.newForgetRate + '%' || '' + '%'" css="font-size: 30rpx;display: block;  " />
            </l-painter-view>
          </l-painter-view>

          <!-- <l-painter-view
            css="position: relative;margin-left: 20rpx; margin-top: 60rpx;  margin-bottom: 40rpx; padding: 80rpx 38rpx 40rpx 44rpx; box-sizing: border-box; background: #fff; border-radius: 16rpx; width: 710rpx;"
          >
            <l-painter-view
              css="position: absolute;border-radius: 10rpx;top:-140rpx;left:-55rpx;width: 200rpx;height:70rpx;background: linear-gradient(,#F3C717 100%, #FEE506 100%)"
            >
              <l-painter-text text="学习详情" css="box-shadow: #999;margin-left: 35rpx;line-height: 70rpx;font-size: 32rpx;color:#fff;text-align: center;"></l-painter-text>
            </l-painter-view>
            <l-painter-text css="color: #000; line-height: 1.8em; font-size: 32rpx; display: block; box-sizing: border-box" text="鼎校甄选小程序"></l-painter-text>
            <l-painter-text css="color: #999; line-height: 1.8em; font-size: 26rpx; width: 488rpx;" text="还在等什么？试一下就知道了，成为学霸没有想象中的那么难"></l-painter-text>
            <l-painter-image src="https://document.dxznjy.com/dxSelect/three/icon/code.png" css="position: absolute;width: 140rpx;bottom: 42rpx;right: 30rpx;" />
          </l-painter-view> -->
        </l-painter-view>
      </l-painter>
    </view>

    <u-popup :show="show" mode="bottom" :round="10" :closeable="true" :safeAreaInsetBottom="false" @close="close">
      <view class="plr-30 flex pt-60 pb-40">
        <view class="flex-col flex-box" @tap="appShareOption(1)">
          <image src="https://document.dxznjy.com/dxSelect/image/icon_weixin1.png" class="box-100"></image>
          <text class="f-22 c-44 mt-10">微信好友</text>
        </view>
        <view class="flex-col flex-box" @tap="appShareOption(2)">
          <image src="https://document.dxznjy.com/dxSelect/image/icon_weixin2.png" class="box-100"></image>
          <text class="f-22 c-44 mt-10">朋友圈</text>
        </view>
        <view class="flex-col flex-box" @click="appSaveOption()">
          <image src="https://document.dxznjy.com/dxSelect/image/icon_save.png" class="box-100"></image>
          <text class="f-22 c-44 mt-10">保存本地</text>
        </view>
      </view>
    </u-popup>
  </view>
</template>

<script>
  import lPainterView from '../../uni_modules/lime-painter/components/l-painter-view/l-painter-view.vue';
  import { DXHost } from '@/util/config.js';
  import uCharts from '@/components/u-charts/js/u-charts.js';
  var _self;
  var canvaRadar = null;
  export default {
    components: { lPainterView },
    data() {
      return {
        switchstate: true, // 日总结
        data: {}, // 日总结数据
        backdetails: {}, // 反馈详情数据
        totallist: {}, // 课程反馈总
        type: '', // 反馈类型
        revirelist: {}, // 复习反馈详情数据
        path: '', // 生成海报
        show: false,
        imgHost: getApp().globalData.imgsomeHost,
        Radar: {
          categories: [],
          series: [
            {
              name: '类型A',
              backgroundColor: 'rgba(51,153,255,0.2)', // 区域填充颜色
              borderColor: '#0066CC', // 数据线条颜色
              borderWidth: 2, // 数据线条宽度
              data: []
            }
          ]
        },
        maxValues: [],
        cWidth: '',
        cHeight: '',
        pixelRatio: 1,
        isShowRadar: true
      };
    },
    onLoad(option) {
      _self = this;
      this.cWidth = uni.upx2px(750);
      this.cHeight = uni.upx2px(500);
      console.log(option);
      uni.showLoading({
        title: ''
      });
      if ('true' === option.state) {
        this.getFeedback(option); // 日总结
      } else {
        this.getTotalback(option); // 总总结
      }
    },
    onShow() {
      // this.showRadar('canvasRadar', this.Radar);
    },
    methods: {
      showRadar(canvasId, chartData, max) {
        let maxValueArr = max; // 每个维度的最大值
        let rawData = chartData.series[0].data; // 原始数据
        let normalizedData = rawData.map((val, index) => (val / maxValueArr[index]) * 100); // 归一化到 [0, 100]
        this.Radar.series[0].data = normalizedData;
        console.log('normalizedData', normalizedData);
        console.log('this.Radar', this.Radar);
        canvaRadar = new uCharts({
          $this: _self,
          canvasId: canvasId,
          type: 'radar',
          fontSize: 13,
          legend: false,
          pixelRatio: _self.pixelRatio,
          animation: true,
          dataLabel: true,
          categories: this.Radar.categories,
          series: this.Radar.series,
          width: _self.cWidth * _self.pixelRatio,
          height: _self.cHeight * _self.pixelRatio,
          color: '#292626',
          dataPointShape: false,
          extra: {
            radar: {
              gridColor: '#7ee578', //网格的颜色
              labelColor: '#292626', //文本颜色
              gridCount: 5, //雷达网格数
              // max: 200 //雷达数值的最大值
              max: 100,
              labelPointShow: false
            }
          }
        });

        setTimeout(() => {
          _self.isShowRadar = false; // 绘制完成后隐藏
          uni.canvasToTempFilePath({
            canvasId: 'canvasRadar',
            success: function (res) {
              uni.uploadFile({
                url: DXHost + 'zxAdminCourse/common/uploadFile', // 你的上传服务器地址
                filePath: res.tempFilePath,
                name: 'file', // 这里根据服务器要求设置
                formData: {
                  user: 'test' // 其他要传的参数
                },
                success: function (uploadFileRes) {
                  console.log('uploadFileRes', uploadFileRes);
                  _self.$set(_self.backdetails, 'imageUrl1', JSON.parse(uploadFileRes.data).data.fileUrl);
                },
                fail: function (uploadFileErr) {
                  // reject(uploadFileErr);
                },
                complete: function () {
                  uni.hideLoading();
                  console.log('backdetails.formalRadaImg', _self.backdetails.imageUrl1);
                }
              });
            }
          });
        }, 1000);
      },
      // 课程反馈详情日
      async getFeedback(option) {
        let res = await this.$httpUser.get('deliver/app/parent/getFeedbackInfo', {
          id: option.id,
          type: 1
        });
        if (res.data.success) {
          this.backdetails = res.data.data;
          if (this.backdetails.curriculumName === '鼎学能') {
            this.formalInit();
          } else {
            uni.hideLoading();
          }
        } else {
          uni.hideLoading();
        }
        this.switchstate = true;
      },

      // 课程反馈详情总
      async getTotalback(option) {
        let res = await this.$httpUser.get('deliver/app/parent/getTotalStatistics', {
          id: option.id,
          planId: option.planId,
          type: 1
        });
        if (res.data.success) {
          this.totallist = res.data.data;
        }
        this.switchstate = false;
        uni.hideLoading();
      },

      // 生成海报
      longPress() {
        //长按保存
        uni.showLoading();
        if (this.path !== '') {
          uni.hideLoading();
          // #ifdef MP-WEIXIN
          uni.authorize({
            scope: 'scope.writePhotosAlbum',
            success: () => {
              uni.saveImageToPhotosAlbum({
                filePath: this.path,
                success: () => {
                  uni.showModal({
                    title: '保存成功',
                    content: '图片已成功保存到相册，快去分享到您的圈子吧',
                    showCancel: false
                  });
                }
              });
            },
            fail() {
              uni.showModal({
                title: '保存失败',
                content: '您没有授权，无法保存到相册',
                showCancel: false
              });
            }
          });
          // #endif

          // #ifdef APP-PLUS
          this.openAppOption();
          // #endif
          uni.hideLoading();
        } else {
          uni.showModal({
            title: '提示',
            content: '生成海报失败,请重试',
            showCancel: false
          });
        }
      },
      openAppOption() {
        this.show = true;
      },
      close() {
        this.show = false;
      },
      //type 1好友  2朋友圈
      appShareOption(type) {
        uni.share({
          provider: 'weixin',
          scene: type === 1 ? 'WXSceneSession' : 'WXSceneTimeline',
          type: 2,
          imageUrl: this.path,
          success: function (res) {
            console.log('success:' + JSON.stringify(res));
            uni.showToast({
              icon: 'none',
              title: '分享成功'
            });
          },
          fail: function (err) {
            console.log('fail:' + JSON.stringify(err));
            uni.showToast({
              icon: 'none',
              title: '分享失败'
            });
          }
        });
      },
      appSaveOption() {
        this.close();
        uni.saveImageToPhotosAlbum({
          filePath: this.path,
          success: function () {
            uni.showModal({
              title: '保存成功',
              content: '图片已成功保存到相册，快去分享到您的圈子吧',
              showCancel: false
            });
          }
        });
      },
      //数值转化为时分秒，入参为秒
      secondsToTimeFormat(seconds) {
        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        const remainingSeconds = seconds % 60;
        const formattedHours = String(hours).padStart(2, '0');
        const formattedMinutes = String(minutes).padStart(2, '0');
        const formattedSeconds = String(remainingSeconds).padStart(2, '0');
        return `${formattedHours}:${formattedMinutes}:${formattedSeconds}`;
      },
      //正式课雷达图数据处理
      async formalInit() {
        if (!this.backdetails) return;
        let indicator = [];
        let dataValue = [];
        let max = null;
        this.backdetails.extendProperty.resultSubjectList.map((el) => {
          max = el.sum;
          indicator.push({ text: el.subjectName, max: el.sum });
          dataValue.push(el.score);
          this.Radar.categories.unshift(el.subjectName);
          this.maxValues.unshift(el.sum);
          this.Radar.series[0].data.unshift(el.score);
        });
        this.Radar.series[0].color = '#72df8b';
        this.Radar.series[0].backgroundColor = '#72df8b';
        this.Radar.series[0].opacity = 0.7;
        if (this.Radar.categories.length < 5) {
          let tmp = 5 - this.Radar.categories.length;
          for (let i = 0; i < tmp; i++) {
            this.Radar.categories.unshift('');
            this.Radar.series[0].data.unshift(0);
            this.maxValues.unshift(1);
          }
        }
        console.log('ppppp1ppp', this.Radar, this.maxValues);
        setTimeout(() => {
          this.showRadar('canvasRadar', this.Radar, this.maxValues);
        }, 500);
      },
      //雷达图初始化
      async init(chartRef, dataValue, Intention) {
        console.log('pppp123', dataValue, Intention);
        let option = {
          radar: [
            {
              indicator: Intention,
              axisName: {
                color: 'rgba(41, 38, 38, 1)'
                // align: 'center'
              },
              splitArea: {
                // 坐标轴在 grid 区域中的分隔区域，默认不显示。
                show: true,
                areaStyle: {
                  // 分隔区域的样式设置。
                  color: ['rgba(211, 245, 206,1)']
                  // 分隔区域颜色。分隔区域会按数组中颜色的顺序依次循环设置颜色。默认是一个深浅的间隔色。
                }
              },
              axisLine: {
                show: true,
                lineStyle: {
                  color: 'rgba(29, 176, 36, 1)'
                }
              },
              splitLine: {
                show: true,
                lineStyle: {
                  color: 'rgba(95, 224, 89, 1)' //分割线颜色
                }
              },
              radius: 80
            }
          ],
          series: [
            {
              type: 'radar',
              data: [
                {
                  value: dataValue,
                  name: 'A Software'
                }
              ],

              symbol: 'none',
              lineStyle: {
                color: 'rgba(95, 224, 89, 1)',
                width: 0.5
              },
              areaStyle: {
                color: 'rgba(72, 214, 111,1)'
              }
            }
          ]
        };
        // chart 图表实例不能存在data里
        console.log('ppppp112', this.$refs[chartRef]);
        if (this.$refs[chartRef]) {
          let chart = null;
          chart = await this.$refs[chartRef].init(echarts);
          console.log('ppppp123', chart);
          await chart.setOption(option);
          let res = this.$refs.formalChartRef;
          console.log('res', res);
          // let url = await this.saveImg(res);
          // console.log('url', url, res);
          let _this = this;
          // return;
          setTimeout(() => {
            res.canvasToTempFilePath({
              success: function (res) {
                uni.uploadFile({
                  url: DXHost + 'zxAdminCourse/common/uploadFile', // 你的上传服务器地址
                  filePath: res.tempFilePath,
                  name: 'file', // 这里根据服务器要求设置
                  formData: {
                    user: 'test' // 其他要传的参数
                  },
                  success: function (uploadFileRes) {
                    console.log('uploadFileRes', uploadFileRes);
                    _this.$set(_this.backdetails, 'imageUrl', JSON.parse(uploadFileRes.data).data.fileUrl);
                  },
                  fail: function (uploadFileErr) {
                    // reject(uploadFileErr);
                  },
                  complete: function () {
                    console.log('backdetails.formalRadaImg', _this.backdetails.imageUrl);
                  }
                });
              }
            });
          }, 1000);
          // this.$set(this.backdetails, 'imageUrl', this.saveImg(this.$refs));
        }
      }
    }
  };
</script>

<style lang="scss" scoped>
  .over-formalChartRef {
    background-color: #b1b1b1;
    position: absolute;
    top: 1150rpx;
    left: 0rpx;
    width: 100%;
    height: 500rpx;
    opacity: 0;
  }
  .bgc_pic {
    position: fixed;
    width: 100%;
    // background-image: url(../../static/pic/bgc.png);
  }

  .logo {
    position: fixed;
    top: 30rpx;
    left: 50rpx;
    height: 50rpx;
    width: 100rpx;
    z-index: 1;
  }

  .split {
    padding-top: 320rpx;
  }

  .card {
    position: relative;
    width: 650rpx;
    background-color: #fff;
    border-radius: 40rpx;
    padding: 50rpx 30rpx 30rpx 20rpx;
    margin: auto;
    z-index: 99;
  }

  .cattle {
    position: absolute;
    left: 490rpx;
    top: -195rpx;
    height: 200rpx;
    width: 160rpx;
  }

  .text-content {
    font-size: 30rpx;
    margin-top: 20rpx;
    margin-left: 20rpx;
  }

  .teacher {
    border-top: 1px dashed #999;
    padding-top: 20rpx;
  }

  .title {
    position: absolute;
    top: -30rpx;
    width: 200rpx;
    height: 70rpx;
    color: #fff;
    line-height: 70rpx;
    text-align: center;
    font-size: 34rpx;
    background-color: #f9d60e;
    border-radius: 10rpx;
    box-shadow: 0 0 10rpx 6rpx rgba(0, 0, 0, 0.2);
    z-index: 1;
  }

  .details {
    position: relative;
    width: 650rpx;
    background-color: #fff;
    border-radius: 40rpx;
    padding: 50rpx 30rpx 30rpx 20rpx;
    margin: auto;
    margin-top: 60rpx;
  }

  .head {
    position: absolute;
    top: -30rpx;
    width: 200rpx;
    height: 70rpx;
    color: #fff;
    line-height: 70rpx;
    text-align: center;
    font-size: 34rpx;
    background-color: #f9d60e;
    border-radius: 10rpx;
    box-shadow: 0 0 10rpx 6rpx rgba(0, 0, 0, 0.2);
    z-index: 1;
  }

  .content {
    display: flex;

    .left {
      margin-top: 20rpx;
    }

    .rubric {
      font-weight: 700;
    }

    .substance {
      margin-top: 10rpx;
      font-size: 26rpx;
      color: #999;
    }

    .right {
      width: 140rpx;
      height: 140rpx;
      min-width: 140rpx;
      min-height: 140rpx;
      background-color: #d8d8d8;
    }

    /deep/.code {
      width: 140rpx;
      height: 140rpx;
      min-width: 140rpx;
      min-height: 140rpx;
    }
  }

  .data-v-bc55c48e {
    text-align: center;
  }
  .qiun-charts {
    width: 750upx;
    height: 500upx;
    background-color: #ffffff;
  }
  .charts {
    width: 750upx;
    height: 500upx;
    background-color: #ffffff;
  }
</style>
