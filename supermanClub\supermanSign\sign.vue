<!-- 超人俱乐部码 -->
<template>
  <view class="plr-30 pb-30">
    <view class="flex ptb-40 bg-ff plr-30 radius-15">
      <view class="flex_s f-28 flex-wrap t-c">
        <view class="w100 mb-10 c-66">总数</view>
        <view class="f-34 w100">{{ codeNumber.totalCodeNum || 0 }}</view>
      </view>

      <view class="flex_s f-28 flex-wrap t-c">
        <view class="w100 mb-10 c-66">剩余</view>
        <view class="f-34 w100">{{ codeNumber.haveCodeNum || 0 }}</view>
      </view>

      <view class="flex_s f-28 flex-wrap t-c">
        <view class="w100 mb-10 c-66">已出</view>
        <view class="f-34 w100">{{ codeNumber.useCodeNum || 0 }}</view>
      </view>
    </view>

    <view class="flex ptb-40 bg-ff radius-15 mtb-30" style="justify-content: space-around">
      <view class="flex_s f-28 flex-wrap t-c wid226" @click="skintap('supermanClub/supermanSign/stockGoods')">
        <view class="f-34 w100">
          <image :src="imgHost + 'dxSelect/sign_jh.png'" class="img_s" mode="widthFix"></image>
        </view>
        <view class="w100 mt-5 c-66">进货</view>
      </view>

      <view class="flex_s f-28 flex-wrap t-c wid226" @click="skintap('supermanClub/supermanSign/shipment')">
        <view class="f-34 w100">
          <image :src="imgHost + 'dxSelect/sign_ch.png'" class="img_s" mode="widthFix"></image>
        </view>
        <view class="w100 mt-5 c-66">出货</view>
      </view>

      <view class="flex_s f-28 flex-wrap t-c wid226" @click="posterShare">
        <view class="f-34 w100">
          <image :src="imgHost + 'dxSelect/sign_recommend.png'" class="img_s" mode="widthFix"></image>
        </view>
        <view class="w100 mt-5 c-66">推荐</view>
      </view>
    </view>

    <!-- 待处理 -->
    <view class="pt-40 bg-ff plr-30 radius-15 mb-30">
      <view class="flex-s b-b pb-30">
        <view class="f-30 c-00 fontWeight">待处理</view>
        <!-- 后续需要添加判断，待处理没有更多数据的时候不显示更多 -->
        <view class="flex-c c-99" @click="goMore()">
          <text class="mr-14 f-28">更多</text>
          <uni-icons type="right" size="16" color="#999"></uni-icons>
        </view>
      </view>

      <view class="listbox" v-for="(item, index) in pendingProcessList" :key="index">
        <view class="list b-db">
          <view class="ptb-30 c-00 f-30">
            <view class="mb-12">{{ item.remark }}</view>
            <view class="flex-s f-28">
              <view class="">{{ item.createdTime }}</view>
              <view class="common_btn common_btn_orange" @click="toPage(item)">去处理</view>
            </view>
          </view>
        </view>
      </view>

      <view v-if="pendingProcessList.length == 0" class="t-c flex-col" style="height: 320upx">
        <image :src="imgHost + 'alading/correcting/no_data.png'" class="mb-20 wid120" mode="widthFix"></image>
        <view style="color: #bdbdbd" class="f-30">暂无数据</view>
      </view>
    </view>

    <view class="pt-40 bg-ff plr-30 radius-15">
      <view class="b-b flex-s pb-30">
        <!-- <view class="pb-30 f-30 c-00" @click="show=true">
					{{date}}
					<uni-icons class="ml-10" type="bottom" color="#c7c7c7" size="16"></uni-icons>
				</view> -->
        <!-- <u-datetime-picker ref="datetimePicker" :show="show" v-model="dateTime" mode="year-month" :immediateChange ='true'></u-datetime-picker> -->
        <picker mode="date" fields="month" :start="startDate" :end="endDate" @change="bindDateChange" bindchange="changeDate">
          <view class="f-30 c-00">
            {{ date }}
            <uni-icons class="ml-10" type="bottom" color="#c7c7c7" size="16"></uni-icons>
          </view>
        </picker>

        <picker @change="bindPickerChange" :value="selectShow ? index : index - 1" :range="array">
          <text class="c-66 f-28">{{ selectShow ? '请选择' : array[index - 1] }}</text>
          <uni-icons class="ml-10" type="right" color="#c999" size="16"></uni-icons>
        </picker>
      </view>

      <scroll-view :scroll-top="scrollTop" scroll-y="true" class="scroll-Y">
        <view class="listbox" v-for="(item, index) in listS.list" :key="index" @click="handelClick(item)">
          <view class="list">
            <view class="ptb-30 c-00 f-30 b-db">
              <view class="mb-12 flex-s">
                <view class="f-30 mb-10">
                  {{ item.remark }}
                </view>
                <view :class="'classPayStatus_btn' + item.dealStatus" class="c-ff f-26 plr-8 ptb-5 radius-5">
                  {{ item.dealStatus != 4 ? (item.dealStatus == 3 ? '已完成' : '处理中') : ' 已取消' }}
                </view>
              </view>
              <view class="flex-s f-28 c-33">{{ item.createdTime }}</view>
            </view>
          </view>
        </view>
      </scroll-view>

      <view v-if="listS.list != undefined && listS.list.length == 0" class="t-c flex-col" :style="{ height: useHeight + 'rpx' }">
        <image :src="imgHost + 'alading/correcting/no_data.png'" class="mb-20 wid120" mode="widthFix"></image>
        <view style="color: #bdbdbd" class="f-30">暂无数据</view>
      </view>
      <view v-if="no_more && listS.list != undefined && listS.list.length > 0">
        <u-divider text="到底了"></u-divider>
      </view>
    </view>
  </view>
</template>

<script>
  const { $navigationTo, $showError, $http } = require('@/util/methods.js');
  const Util = require('@/util/util.js');
  import dayjs from 'dayjs';
  export default {
    data() {
      const currentDate = Util.getDate({
        format: true
      });
      return {
        useHeight: 0, //除头部之外高度
        imgHost: getApp().globalData.imgsomeHost,
        date: currentDate, // 展示时间
        dateTime: '', // 传入时间
        pendingProcessList: [], //待处理列表
        show: false, // 时间弹框显示
        mealLists: [], // 课程详情
        listS: {}, //出后进货列表
        page: 1,
        no_more: false,
        codeNumber: '', // 超人码数量
        scrollTop: '',
        userinfo: {},
        selectShow: true, // 是否展示请选择字符
        array: ['进货', '出货', '充码', '扣码', '出码'],
        index: 0, // 进出货列表选择
        passStatus: '' // 进出货状态
      };
    },
    onLoad() {},
    onReady() {
      uni.getSystemInfo({
        success: (res) => {
          // 可使用窗口高度，将px转换rpx
          let h = res.windowHeight * (750 / res.windowWidth);
          this.useHeight = h - 1010;
        }
      });
    },

    onShow() {
      this.meal();
      this.getSupermanNum();
      this.getStocklist();
      this.getlistData();
      this.homeData();
    },

    onReachBottom() {
      if (this.page >= this.listS.totalPage) {
        this.no_more = true;
        return false;
      }
      this.getlistData(true, ++this.page);
    },
    methods: {
      startDate() {},
      endDate() {},

      // 获取首页信息
      async homeData() {
        let _this = this;
        const res = await $http({
          url: 'zx/user/userInfoNew'
        });
        if (res) {
          _this.userinfo = res.data;
        }
      },
      // 点击推荐
      posterShare() {
        let type = 2;
        uni.navigateTo({
          url: `/splitContent/poster/index?type=${type}&id=${this.mealLists[0].mealId}`
        });
      },

      async meal() {
        let _this = this;
        const res = await $http({
          url: 'zx/meal/mealList',
          data: {
            indexShow: 0,
            cityCode: '',
            page: 1,
            pageSize: 20
          }
        });
        if (res) {
          _this.mealLists = res.data.list;
        }
      },

      bindDateChange: function (e) {
        this.date = Util.getDateChinese(e.detail.value);
        // this.date = dayjs(e.detail.value).format('YYYY-MM');
        this.dateTime = e.detail.value;
        this.getlistData();
      },

      skintap(url) {
        $navigationTo(url);
      },

      // 待处理进货申请列表点击
      handelClick(item) {
        console.log(item);
        if (item.dealStatus != 4 && item.dealStatus != 3) {
          if (this.userinfo.merchantCode != item.enterMerchantCode) {
            return;
          }
          uni.navigateTo({
            url: `/supermanClub/supermanSign/stockprocessing?orderInfo=${encodeURIComponent(JSON.stringify(item))}`
          });
        } else if (item.dealStatus == 3) {
          uni.navigateTo({
            url: `/supermanClub/supermanSign/purchaseCodesList?orderInfo=${encodeURIComponent(JSON.stringify(item))}`
          });
        }
      },

      toPage(item) {
        uni.navigateTo({
          url: `/supermanClub/supermanSign/processing?orderInfo=${encodeURIComponent(JSON.stringify(item))}`
        });
      },
      //更多
      goMore() {
        if (this.pendingProcessList.length == 0) {
          this.$util.alter('暂无数据');
          return;
        }
        uni.navigateTo({
          url: '/supermanClub/supermanSign/treatHandleShipment'
        });
      },

      // 获取超人码数量
      async getSupermanNum() {
        const res = await $http({
          url: 'zx/invitation/code/getUserCodeNum'
        });

        if (res) {
          this.codeNumber = res.data;
        }
      },

      // 待处理进货列表
      async getStocklist() {
        let _this = this;
        const request = await $http({
          url: 'zx/invitation/code/outCodeApplyDealPage',
          data: {
            page: 1,
            pageSize: 2
          }
        });
        if (request) {
          _this.pendingProcessList = request.data.list;
          // if (isPage) {
          // 	let old = _this.listS.list
          // 	_this.listS.list = [...old, ...res.data.list]
          // } else {
          // 	_this.listS = res.data
          // }
        }
      },

      // 进货出货记录列表
      async getlistData(isPage, page) {
        let _this = this;
        let time = dayjs().unix();
        let month = dayjs.unix(time).format('YYYY-MM');
        // console.log(month)
        if (_this.dateTime == '') {
          _this.dateTime = month;
        }
        const res = await $http({
          url: 'zx/invitation/code/outCodeApplyPage',
          data: {
            month: _this.dateTime,
            page: 1,
            pageSize: 10,
            type: _this.index == 0 ? '' : _this.index
          }
        });
        // console.log(res)
        if (res) {
          if (isPage) {
            let old = _this.listS.list;
            _this.listS.list = [...old, ...res.data.list];
          } else {
            _this.listS = res.data;
          }
        }
      },

      // 进出货列表状态
      bindPickerChange(e) {
        console.log('picker发送选择改变，携带值为', e.detail.value);
        this.index = Number(e.detail.value) + 1;
        console.log(this.index);
        this.selectShow = false;
        this.getlistData();
      }
    }
  };
</script>

<style lang="scss">
  .label {
    background-image: linear-gradient(to right, #feefd5, #e7bd7b);
    padding: 0 20rpx;
    height: 40rpx;
    line-height: 40rpx;
    border-radius: 20rpx 0;
    color: #886a34;
    font-size: 24rpx;
  }

  .screenitem {
    width: 150rpx;
    height: 60rpx;
    border: 1rpx solid #c8c8c8;
    border-radius: 35rpx;
    padding: 0 30rpx;

    .screenPicker {
      flex: 1;
    }

    .xiaimg {
      width: 20rpx;
      height: 20rpx;
    }
  }

  .flex_s {
    display: flex;
    align-items: center;
  }

  .img_s {
    width: 42rpx;
    height: 42rpx;
  }

  .line-after {
    width: 1upx;
    height: 60upx;
    background-color: #d2d2d2;
  }

  .sortBox {
    width: 220upx;
    background: #ffffff;
    border: 1upx solid #cfc8c8;
    position: absolute;
    top: 100upx;
    left: 30upx;
  }

  .sortItem {
    width: 100%;
    height: 80upx;
    text-align: center;
    line-height: 80upx;
    font-size: 30upx;
    color: #666666;
    background-color: #fff;
  }

  .sortItem.active {
    background: #f4f4f4;
    color: #000000;
    font-weight: bold;
  }

  .wid226 {
    width: 226upx;
    margin: 0 auto;
    border-right: 1upx solid #d2d2d2;
  }
  .wid226:last-child {
    border: none;
  }

  .wid120 {
    width: 120upx;
    height: 120upx;
  }

  .classPayStatus_btn0 {
    width: 80rpx;
    background-color: #e57126;
  }

  .classPayStatus_btn1 {
    width: 80rpx;
    background-color: #e57126;
  }

  .classPayStatus_btn2 {
    width: 80rpx;
    background-color: #e57126;
  }

  .classPayStatus_btn3 {
    width: 80rpx;
    background-color: #2dc032;
  }

  .classPayStatus_btn4 {
    width: 80rpx;
    background-color: #c6c6c6;
  }

  // .img_s { width: 22upx;height: 28upx; }

  .list:last-child {
    border: none;
  }
</style>
