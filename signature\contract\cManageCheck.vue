<template>
  <web-view :src="url"></web-view>
</template>

<script>
  export default {
    data() {
      return {
        url: ''
      };
    },
    onLoad(option) {
      console.log(option);
      if (option != null && option.url != undefined) {
        this.url = JSON.parse(decodeURIComponent(option.url));
      } else {
        this.fetchUrl();
      }
    },
    methods: {
      async fetchUrl() {
        await this.$httpUser.get('zx/wap/esign/auth/authUrl').then((res) => {
          if (res.data.data) {
            this.url = res.data.data.authUrl;
          } else {
            uni.navigateBack();
          }
        });
      }
    }
  };
</script>

<style></style>
