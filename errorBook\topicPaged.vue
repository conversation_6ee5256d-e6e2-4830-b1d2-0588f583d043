<template>
  <view>
    <!-- <u-navbar :title="titleNavbar" @rightClick="rightClick" :autoBack="true"> </u-navbar> -->
    <view class="plr-30 pb-30 list_box">
      <view class="flex-s time-container">
        <u-line-progress v-if="progressShow" :percentage="timePercentage" :round="false" :showText="false" height="54rpx" activeColor="#31cf91"></u-line-progress>
        <text v-if="progressShow" class="formattedTime">
          {{ formattedTime }}
        </text>
      </view>

      <view class="pb-45 ml-40">
        <view class="f-28 mt-40">
          <text class="iconBox" v-if="questionCategory == 1">知识点</text>
          <text class="iconBox" v-if="questionCategory == 2">语法点</text>
          <text class="iconBox" v-if="questionCategory == 3">结业检测</text>
          <text v-if="!distinguish">
            <text v-for="(part, index) in questionData" :key="index">
              {{ part.text }}
              <text v-if="part.text === ''" @click="handleUnderlineClick(part.index)" style="text-decoration: underline; width: 120rpx">
                {{ answerBlankUnder[part.index] || '&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' }}
              </text>
            </text>
          </text>
          <text v-else>
            <text v-for="(part, index) in question" :key="index">
              {{ part.text }}
              <text v-if="part.text === ''" style="text-decoration: underline; width: 120rpx">
                {{ '&nbsp;&nbsp;&nbsp;&nbsp;' }}
              </text>
            </text>
          </text>
        </view>
      </view>
      <!-- 选择区域 -->
      <view class="flex-dir-col mt-30 ml-40" v-if="distinguish">
        <text
          class="boxl-650 bd-ee radius-15 lh-96 pl-20 mb-30"
          @click="rightHandle(index)"
          v-for="(item, index) in optionsList"
          :key="index"
          :class="{
            activeGreen: activeGreen === item.optionSort,
            wrongOption: answer !== String.fromCharCode(65 + item.optionSort) && index === selectedOption
          }"
        >
          {{ String.fromCharCode(65 + item.optionSort) }}.{{ item.optionContent }}
        </text>
      </view>

      <!-- 解析区域 -->
      <view v-if="answerKey && distinguish">
        <view class="flex-x-a flex-c mt-30 bg-fa h-130 radius-15">
          <view>
            <text class="flex-wrap flex-x-c c-e8">{{ answer }}</text>
            <text class="c-a6 f-24">正确答案</text>
          </view>
          <view v-if="selectedOption === -1">
            <text class="flex-wrap flex-x-c c-e8" :class="answer === selectedAnswer ? '' : 'wrongText'">{{ selectedAnswer == '' ? '空' : selectedAnswer }}</text>
            <text class="c-a6 f-24">你的答案</text>
          </view>
          <view v-else>
            <text class="flex-wrap flex-x-c c-e8" :class="selectedOption >= 0 && selectedOption !== activeGreen ? 'wrongText' : ''">
              {{ String.fromCharCode(65 + selectedAnswer) }}
            </text>
            <text class="c-a6 f-24">你的答案</text>
          </view>
        </view>
        <view class="f-28 mt-40 ml-30">
          <text class="flex-wrap c-00 mb-10">解析：</text>
          <text>{{ questionDescription }}</text>
        </view>
      </view>
      <view v-if="answerBlank && !distinguish">
        <view class="flex-col-a mt-30 p-20 bg-fa radius-15">
          <view class="flex-f-a pb-20" v-for="(item, index) in answerBlankRight" :key="index">
            <text class="c-a6 f-24">{{ index + 1 }}. 正确答案</text>
            <text class="c-e8">{{ item }}</text>
            <text class="c-a6 f-24">你的答案</text>
            <text :class="{ 'correct-answer': isCorrectAnswer(index), 'incorrect-answer': !isCorrectAnswer(index) }">
              {{ answerBlankUnder[index] }}
            </text>
          </view>
        </view>
        <view class="f-28 mt-40 ml-30">
          <text class="flex-wrap c-00 mb-10">解析：</text>
          <text>{{ questionDescription }}</text>
        </view>
      </view>
    </view>
    <view class="bottom-buttons">
      <u-button shape="circle mr-40" :plain="true" color="#428A6F" @click="viewKnowHandle">查看知识点</u-button>

      <template v-if="distinguish || (!distinguish && nextButtonShow) || isDone">
        <u-button shape="circle" color="#428A6F" v-if="!isDone" @click="nextTopicHandle">下一题</u-button>
        <u-button shape="circle" color="#428A6F" v-if="isDone" @click="redoTopic">重做</u-button>
      </template>
      <!-- v-if="!distinguish && viewShow && !nextButtonShow && !isDone" -->
      <u-button shape="circle" color="#428A6F" v-else @click="viewHandle">查看解析</u-button>
    </view>
    <!-- 弹层区域 -->
    <view class="flex">
      <u-popup ref="popup" :show="showBlank" @close="closeBlank" :round="40" mode="center" closeable="true" :safeAreaInsetBottom="false" :customStyle="customStyle">
        <!-- <text class="t-c mb-100">填空{{ underlineIndexClicked + 1 }}</text> -->
        <text class="t-c mt-20">在下方输入正确的答案{{ underlineIndexClicked + 1 }}</text>
        <u--input
          placeholder="请输入你的答案"
          :customStyle="{ paddingLeft: '100px', height: '50px', marginRight: '20px', marginLeft: '20px' }"
          border="surround"
          v-model="selectedAnswer"
        ></u--input>
        <view class="popupButtons">
          <u-button shape="circle" color="#428A6F" @click="confirmBlank">确定</u-button>
          <u-button shape="circle mr-40" :plain="true" color="#428A6F" @click="closeBlank">取消</u-button>
        </view>
      </u-popup>
    </view>
  </view>
</template>
<script>
  export default {
    data() {
      return {
        distinguish: null, // 区分是选择还是填空
        showBlank: false, // 填空弹层
        customStyle: {
          width: '80%',
          height: 'auto',
          backgroundColor: '#fff',
          padding: '20rpx 40rpx',
          borderRadius: '40rpx',
          boxShadow: '0 0 20rpx 0 rgba(0, 0, 0, 0.2)'
        },
        valueBlank: '', // 填空答案
        titleNavbar: '', // 自定义导航栏
        remainingTime: 0, // 用户停留时间
        totalTime: 0, // 后台配置的总时间
        progressTotal: 0, // 进度条展示时间
        answerKey: true, // 选择解析区域
        answerBlank: true, // 填空解析区域
        viewShow: true, // 查看解析按钮
        activeGreen: -1, // 动态样式，初始化为 -1 表示未选中
        activeBlank: 0, // 动态样式，初始化为 0
        selectedOption: -1, // 记录选中的错误选项
        progressShow: false, // 倒计时
        timerId: null, // 倒计时
        selectedAnswer: '', // 记录用户选择的答案
        rightOptionList: '',
        optionsList: [],
        answer: '', // 正确答案
        answerBlankRight: [], // 正确答案
        isClicked: true, // 判断是否点击过
        question: '',
        questionData: [], // 转换后的数据
        underlineIndex: 0, // 下划线索引
        underlineIndexClicked: 0,
        answerBlankUnder: [], // 填空答案
        nextButtonShow: false, // 下一步按钮显示
        isDone: true, // 是否完成
        questionId: '', // 题目id
        questionErrorId: '', // 知识点题目id
        type: '', // 1 已做 0 未做
        questionCategory: '', // 题目类型
        questionDescription: '', // 题目解析
        nextQuestionId: '', // 下一题id
        isTimerPaused: false,
        disableInput: false, // 控制输入框是否禁用
        knowledgeId: '', // 知识点id
        phase: '' // 阶段
      };
    },
    computed: {
      timePercentage() {
        // 计算剩余时间占总时间的百分比
        const passedTime = this.totalTime - this.remainingTime;
        return 100 - (passedTime / this.totalTime) * 100;
      },
      formattedTime() {
        let hours = Math.floor(this.progressTotal / 3600);
        let minutes = Math.floor((this.progressTotal % 3600) / 60);
        let seconds = this.progressTotal % 60;

        return `${this.pad(hours)}:${this.pad(minutes)}:${this.pad(seconds)}`;
      }
    },
    async onLoad(options) {
      this.studentCode = options.studentCode;
      this.type = options.type;
      this.questionId = options.id;
      this.knowledgeId = options.knowledgeId;
      this.phase = options.phase;
      await this.getQuesTion(() => {});
      this.titleNavbar = options.type !== 0 ? '错题' : '已做';
    },
    onShow() {
      if (this.isTimerPaused) {
        // 继续计时
        this.startTimer();
        this.isTimerPaused = false;
      }
    },
    methods: {
      // 设置默认答案的方法
      setDefaultAnswer() {
        this.isClicked = true; // 点击后设置为已点击状态
        if (this.distinguish) {
          // 区分题目类型为选择题时
          this.selectedOption = -1; // 设置选项的默认答案
          this.selectedAnswer = '';
          this.activeGreen = -1;
          this.answerKey = true;
        } else {
          // 区分题目类型为填空题时
          for (let i = 0; i < this.underlineIndex; i++) {
            if (!this.answerBlankUnder[i]) {
              this.answerBlankUnder[i] = '空'; // 设置填空的默认答案
            }
          }
          this.nextButtonShow = true;
          // 禁止填空题输入
          this.disableInput = true;

          this.answerBlank = true;
        }
        uni.showToast({
          title: '做题时间结束',
          icon: 'none'
        });
        setTimeout(() => {
          uni.hideToast();
        }, 2000);
      },
      getQuesTion(cb) {
        this.$httpUser
          .get('dyf/wap/applet/wrongBook', {
            type: this.type,
            // studentCode: '6231217888',
            studentCode: this.studentCode,
            id: this.questionId,
            knowledgeId: this.knowledgeId,
            phase: this.phase
          })
          .then((res) => {
            this.questionErrorId = res.data.data.id;
            this.totalTime = res.data.data.time;
            this.progressTotal = this.totalTime;
            this.distinguish = res.data.data.questionType == '1' ? true : false;
            this.questionCategory = res.data.data.questionCategory;
            this.optionsList = res.data.data.optionList;
            this.answer = res.data.data.rightOptionList[0];
            this.answerBlankRight = res.data.data.rightOptionList;
            this.selectedAnswer = res.data.data.studentAnswerList !== null ? res.data.data.studentAnswerList[0] : '空';
            this.answerBlankUnder = res.data.data.studentAnswerList === null ? ['空'] : res.data.data.studentAnswerList;
            if (res.data.data.questionType === '1') {
              this.question = this.convertQuestionData(res.data.data.questionName);
            } else if (res.data.data.questionType === '2') {
              this.questionData = this.convertQuestionData(res.data.data.questionName);
            }
            this.questionDescription = res.data.data.questionDescription;
            this.nextQuestionId = res.data.data.nextQuestionId;
            cb && cb();
          });
      },
      updateQuestionState(data) {
        this.questionErrorId = data.id;
        this.totalTime = data.time;
        this.progressTotal = this.totalTime;
        this.distinguish = data.questionType === '1';
        this.questionCategory = data.questionCategory;
        this.optionsList = data.optionList;
        this.answer = data.rightOptionList[0];
        this.answerBlankRight = data.rightOptionList;
        this.selectedAnswer = data.studentAnswerList !== null ? data.studentAnswerList?.[0] : '空';
        this.answerBlankUnder = data.studentAnswerList || ['空'];
        this.question = data.questionType === '1' ? this.convertQuestionData(data.questionName) : '';
        this.questionData = data.questionType === '2' ? this.convertQuestionData(data.questionName) : '';
        this.questionDescription = data.questionDescription;
        this.nextQuestionId = data.nextQuestionId;
      },
      getQuesNextTion() {
        this.underlineIndex = 0;
        this.remainingTime = 0;
        if (!this.nextQuestionId) {
          uni.showToast({
            title: '没有下一题了',
            icon: 'none'
          });
          this.isDone = false;
          setTimeout(() => {
            uni.navigateBack({
              delta: 1
            });
          }, 300);
          return;
        }
        this.$httpUser
          .get('dyf/wap/applet/wrongBook', {
            type: this.type,
            // studentCode: '6231217888',
            studentCode: this.studentCode,
            id: this.nextQuestionId,
            knowledgeId: this.knowledgeId,
            phase: this.phase
          })
          .then((res) => {
            // 更新所有相关状态
            this.updateQuestionState(res.data.data);
          })
          .catch(() => {
            this.viewShow = true;
            this.nextButtonShow = false;
            this.isClicked = false;
            this.answerKey = false;
            this.answerBlank = false;
            this.answerBlankUnder = [];
            this.selectedAnswer = '';
          });
        this.answerKey = true;
        this.answerBlank = true;
        this.progressShow = false;
        this.isDone = true;
        this.isClicked = true;
        this.activeGreen = -1;
        this.selectedOption = -1;
      },
      // 转换题目格式
      convertQuestionData(question) {
        const parts = question.split('##');
        let result = [];
        parts.forEach((part, index) => {
          result.push({ text: part });
          if (index < parts.length - 1) {
            result.push({ text: '', index: index });
            this.underlineIndex++;
          }
        });
        result = result.filter((e) => e.index || e.text || e.index == 0);
        console.log(result);
        return result;
      },
      handleUnderlineClick(index) {
        this.selectedAnswer = '';
        if (this.disableInput) {
          // 时间结束后不允许打开输入框
          uni.showToast({
            title: '做题时间结束，不能继续填写',
            icon: 'none'
          });
          return;
        }
        console.log(index, 2222);
        this.underlineIndexClicked = index;
        if (this.isDone) {
          this.showBlank = false;
        } else {
          this.showBlank = true;
        }
      },
      isCorrectAnswer(index) {
        if (this.answerBlankUnder == null) {
          return false;
        }
        return this.answerBlankUnder[index] === this.answerBlankRight[index];
      },
      // 查看解析
      viewHandle() {
        // 检查 answerBlankUnder 是否与 underlineIndex 的长度一致
        if (this.answerBlankUnder.length !== this.underlineIndex) {
          uni.showToast({
            title: '请填写所有填空',
            icon: 'none'
          });
          return;
        }
        // 检查每一个填空项是否都已填写
        let allAnswersFilled = true;
        this.isTimerPaused = true;

        for (let i = 0; i < this.underlineIndex; i++) {
          const userAnswer = this.answerBlankUnder[i];
          if (!userAnswer || userAnswer.trim() === '') {
            allAnswersFilled = false;
            break; // 只要有一个答案未填写，直接退出循环
          }
        }

        if (!allAnswersFilled) {
          uni.showToast({
            title: '请确保所有答案都已输入',
            icon: 'none'
          });
          return;
        }

        // 显示解析和下一题按钮
        this.viewShow = false;
        this.answerBlank = true;
        this.nextButtonShow = true;
      },

      // 查看知识点跳转
      viewKnowHandle() {
        // 暂停倒计时
        this.isTimerPaused = true;
        clearInterval(this.timerId);
        uni.navigateTo({
          // 传 id
          url: `/errorBook/knowledgeReview?id=${this.questionErrorId}`
        });
      },
      redoTopic() {
        clearInterval(this.timerId);
        this.answerKey = false;
        this.answerBlank = false;
        this.progressShow = true;
        this.isDone = false;
        if (this.distinguish) {
          this.isClicked = false;
        }
        this.answerBlankUnder = [];
        // this.remainingTime = new Date().getTime() % 60
        // 重新启动计时器
        this.startTimer();
      },
      // 点击下一题
      nextTopicHandle() {
        if (!this.isClicked) {
          uni.showToast({
            title: '请先选择答案',
            icon: 'none'
          });
          return;
        }
        let answerList = [];
        if (this.distinguish) {
          // 选择题
          answerList.push(String.fromCharCode(65 + this.selectedAnswer));
        } else {
          // 填空题
          answerList = this.answerBlankUnder;
        }
        this.$httpUser
          .post('dyf/wap/applet/wrongBookStatus', {
            id: this.questionErrorId,
            answerList: answerList
          })
          .then((res) => {
            this.getQuesNextTion();
            this.disableInput = false;
          });
      },
      // 点击正确选项
      rightHandle(index) {
        if (!this.isClicked) {
          // 只有在未点击时才能进行操作
          this.answerKey = true;
          this.progressShow = false;
          this.selectedAnswer = this.optionsList[index].optionSort;
          if (this.optionsList[index].optionSort === this.answer) {
            this.activeGreen = index;
          } else {
            this.selectedOption = index;
            this.activeGreen = this.optionsList.findIndex((item) => String.fromCharCode(65 + item.optionSort) === this.answer);
          }
          this.isClicked = true; // 点击后设置为已点击状态
        }
      },

      confirmBlank() {
        if (this.disableInput) {
          // 时间结束后不允许更改答案
          return;
        }

        const inputValue = this.selectedAnswer;

        // const processedValue = inputValue.replace(/\s+/g, ''); // 去除所有空格
        if (inputValue === '') {
          // 校验处理后的输入框值
          uni.showToast({
            title: '请填写答案',
            icon: 'none'
          });
          return;
        }
        console.log(inputValue, 22222);
        if (inputValue === this.answerBlankRight) {
          this.activeBlank = 1;
        } else {
          this.activeBlank = 0;
        }
        console.log(inputValue, 33333);
        console.log(this.underlineIndexClicked);

        // 检查是否存在对应下划线索引的元素，如果没有则添加
        if (!this.answerBlankUnder[this.underlineIndexClicked]) {
          this.answerBlankUnder[this.underlineIndexClicked] = inputValue;
        } else {
          // 如果已经存在，则更新其值
          this.answerBlankUnder[this.underlineIndexClicked] = inputValue;
        }
        console.log(this.answerBlankUnder, 44444);
        this.selectedAnswer = inputValue;
        this.showBlank = false;
        this.progressShow = false;
        this.selectedAnswer = '';
      },
      closeBlank() {
        this.showBlank = false;
        this.selectedAnswer = '';
      },
      startTimer() {
        this.timerId = setInterval(() => {
          if (this.remainingTime < this.totalTime) {
            this.remainingTime++;
          } else {
            this.remainingTime = this.totalTime;
          }
          // 将 progressTotal 从毫秒转换为秒进行递减
          this.progressTotal = Math.max(0, this.progressTotal - 1);
          if (this.progressTotal === 0) {
            clearInterval(this.timerId);
            this.setDefaultAnswer();
            // this.nextTopicHandle()
          }
        }, 1000);
      },
      pad(number) {
        return number < 10 ? '0' + number : number;
      }
    },
    beforeDestroy() {
      // 组件销毁前清除计时器
      clearInterval(this.timerId);
    }
  };
</script>
<style scoped>
  .list_box {
    border-radius: 14upx;
    height: 1250upx;
    background: #ffffff;
    box-shadow: 0rpx 4rpx 16rpx 0rpx #f5f7f9;
    padding: 30upx;
    margin: 20upx;
    /* margin-top: 200upx; */
    font-size: 26upx;
    color: rgba(102, 102, 102, 1);
    position: relative;
  }

  .iconBox {
    width: 72rpx;
    height: 34rpx;
    line-height: 34rpx;
    border: 2rpx solid #81e2af;
    border-radius: 8rpx;
    color: #81e2af;
    margin-right: 10rpx;
  }

  ::v-deep .u-input--radius {
    margin: 80rpx 40rpx;
  }
  .time-container {
    position: relative;
  }

  .formattedTime {
    position: absolute;
    top: 47%;
    left: 50%;
    transform: translate(-50%, -50%);
  }

  .bottom-buttons {
    position: fixed;
    bottom: 30upx;
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    /* margin:0  20upx; */
    z-index: 999;
    /* 确保按钮在其他内容之上 */
  }

  .bottom-buttons >>> button {
    width: 300rpx !important;
    height: 82rpx !important;
  }

  .popupButtons {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .popupButtons >>> button {
    width: 100% !important;
    height: 82rpx !important;
    margin: 20rpx;
  }

  ::v-deep .u-border {
    background-color: #f4f7f9;
    border-style: none !important;
    margin-bottom: 100rpx;
  }

  ::v-deep .u-input__content {
    height: 70rpx !important;
  }

  /* 点击选项按钮变色 */
  .activeGreen {
    border: 2rpx solid #94e6c7;
    color: #94e6c7;
    background: rgba(148, 230, 199, 0.15);
  }

  /* 错误选项样式 */
  .wrongOption {
    border: 2rpx solid #ffaf85;
    color: #ffaf85;
    background: rgba(255, 172, 129, 0.1);
  }

  .wrongText {
    color: #ffaf85;
  }

  .u-input {
    flex: none;
  }
  .blank-space {
    width: 50rpx; /* 调整为你需要的宽度 */
    display: inline-block;
  }
</style>
