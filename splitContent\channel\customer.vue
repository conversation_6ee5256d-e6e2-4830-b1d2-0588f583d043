<template>
    <view class="plr-30">
        <view class="flex ptb-40 bg-ff plr-30 radius-15">
            <view class="flex_s f-30">
                <text class="c-66">状态：</text>
                <view class="screenitem flex">
                    <picker class="screenPicker" @change="bindPickerChange" range-key="name" :value="arrayIndex"
                        :range="array">
                        <view class="c-00">{{ array[arrayIndex].name || '成交状态'}}</view>
                    </picker>
                    <image :src="imgHost+'dxSelect/fourthEdition/xia.png'" class="xiaimg"></image>
                </view>
            </view>
            <view class="flex_s f-30">
                <text class="c-66">级别：</text>
                <view class="screenitem flex">
                    <picker class="screenPicker" @change="bindPickerChangeLevel" range-key="name" :value="levelIndex"
                        :range="level">
                        <view class="c-00">{{ level[levelIndex].name || '等级'}}</view>
                    </picker>
                    <image :src="imgHost+'dxSelect/fourthEdition/xia.png'" class="xiaimg"></image>
                </view>
            </view>
        </view>
        <view>
            <block v-for="(item,index) in listS.list" :key="index">
                <view class="flex bg-ff mt-30 pl-30 pr-20 radius-15">
                    <view class="box-100 radius-50">
                        <image :src="item.headPortrait == ''? avaUrl : item.headPortrait" class="wh100"></image>
                    </view>
                    <view class="flex-box ml-20 ptb-30">
                        <view class="flex-dir-row">
                            <text class="f-30 c-00" :class="item.nickName? 'mr-20': ''">{{ item.nickName }}</text>
                            <view class="label">
                                <text
                                    v-if="item.identityType>0 && item.gradeLevel==0">{{ item.identityTypeName }}</text>
                                <text v-if="item.gradeLevel>0">{{ item.gradeLevel}}星家长</text>
                                <text v-if="item.identityType==0">{{ item.identityTypeName}}</text>
                            </view>
                        </view>
                        <view class="f-26 c-99 flex mt-20">
                            <text class="c-00 f-28">{{ item.createdTime }}</text>
                            <text>{{ item.mobile}}</text>
                        </view>
                    </view>
                </view>
            </block>
        </view>
        <view v-if="listS.list != undefined && listS.list.length==0" class="t-c flex-col"
            :style="{height: useHeight+'rpx'}">
            <image :src="imgHost+'alading/correcting/no_data.png'" mode="widthFix" class="mb-20 img_s"></image>
            <view style="color: #BDBDBD;">暂无数据</view>
        </view>
        <view v-if="no_more && listS.list != undefined && listS.list.length>0">
            <u-divider text="到底了"></u-divider>
        </view>
    </view>
</template>

<script>
    import Util from '@/util/util.js'
    const {
        $navigationTo,
        $showError,
        $http,
    } = require("@/util/methods.js")
    export default {
        data() {
            return {
                arrayIndex: 0,
                hasDeal: -1,
                imgHost: getApp().globalData.imgsomeHost,
                array: [{
                    name: '全部',
                    value: '-1'
                }, {
                    name: '未成交',
                    value: '0'
                }, {
                    name: '已成交',
                    value: '1'
                }],
                levelIndex: 0,
                gradeLevel: -1,
                level: [{
                    name: '全部',
                    value: -1
                }, {
                    name: '家长',
                    value: 0
                }],
                listS: {},
                page: 1,
                no_more: false,
                userinfo: {},
                // 默认头像
                avaUrl: Util.getCachedPic("https://document.dxznjy.com/dxSelect/home_avaUrl.png","home_avaUrl_path"),
                useHeight: 0, //除头部之外高度
            }
        },
        onLoad() {

        },
        onShow() {
            this.list()
            this.grade()
        },
        onReady() {
            uni.getSystemInfo({
                success: (res) => {
                    // 可使用窗口高度，将px转换rpx
                    let h = (res.windowHeight * (750 / res.windowWidth));
                    this.useHeight = h - 146;
                }
            })
        },
        onReachBottom() {
            if (this.page >= this.listS.totalPage) {
                this.no_more = true
                return false;
            }
            this.list(true, ++this.page);
        },
        methods: {
            // 星级列表
            async grade() {
                let _this = this
                const res = await $http({
                    url: 'zx/setting/gradeLevelList',
                    data: {}
                })
                if (res) {
                    let arr = []
                    res.data.forEach((item, index) => {
                        let obj = {}
                        obj.name = item + '星'
                        obj.value = item
                        arr.push(obj)
                    })
                    _this.level = [..._this.level, ...arr]
                    console.log(res)
                }
            },
            // 成交状态筛选
            bindPickerChange(e) {
                this.arrayIndex = Number(e.detail.value)
                console.log(this.arrayIndex)
                this.page = 1
                this.no_more = false
                this.list()
            },
            // 筛选等级
            bindPickerChangeLevel(e) {
                let index = e.detail.value
                this.levelIndex = index
                this.gradeLevel = this.level[index].value
                this.page = 1
                this.no_more = false
                this.list()
            },
            async list(isPage, page) {
                let _this = this
                const res = await $http({
                    url: 'zx/user/clientList',
                    data: {
                        hasDeal: _this.array[_this.arrayIndex].value,
                        gradeLevel: _this.gradeLevel,
                        page: page || 1,
                        listType: 1
                    }
                })
                if (res) {
                    if (isPage) {
                        let old = _this.listS.list
                        _this.listS.list = [...old, ...res.data.list]
                    } else {
                        _this.listS = res.data
                    }
                }
            },
        }
    }
</script>

<style lang="scss" scoped>
    .label {
        background-image: linear-gradient(to right, #FEEFD5, #E7BD7B);
        padding: 0 20rpx;
        height: 40rpx;
        line-height: 40rpx;
        border-radius: 20rpx 0;
        color: #90621A;
        font-size: 24rpx;
    }

    .screenitem {
        width: 150rpx;
        height: 60rpx;
        border: 1rpx solid #C8C8C8;
        border-radius: 35rpx;
        padding: 0 30rpx;

        .screenPicker {
            flex: 1;
        }

        .xiaimg {
            width: 20rpx;
            height: 20rpx;
        }
    }

    .flex_s {
        display: flex;
        align-items: center;
    }

    .img_s {
        width: 160rpx;
    }
</style>