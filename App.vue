<!-- 接口地址  https://ding.vtui365.com/course/swagger-ui.html -->
<script>
  import Config from 'util/config.js';
  import PublicVariable from 'util/publicVariable';
  import { $http } from '@/util/methods.js';
  import { httpUser } from '@/util/luch-request/indexUser.js';
  // #ifdef MP-WEIXIN
  //神策
  import sensors from 'sa-sdk-miniprogram';
  sensors.setPara({
    name: 'sensors',
    server_url: `${Config.DXHost}log/do/sensors`,
    // 全埋点控制开关
    autoTrack: {
      appLaunch: true,
      appShow: true,
      appHide: true,
      pageShow: true,
      pageShare: true,
      mpClick: true,
      pageLeave: true
    },
    show_log: false, // 是否允许控制台打印查看埋点数据(建议开启查看)
    // 是否允许修改 onShareAppMessage 里 return 的 path，用来增加(登录 ID，分享层级，当前的 path)，在 app onShow 中自动获取这些参数来查看具体分享来源、层级等
    allow_amend_share_path: true
  });
  sensors.registerApp({
    '$dx-source': 'ZHEN_XUAN##WX##MINIAPP',
    $userCode: function () {
      return uni.getStorageSync('log_userCode');
    }
  });
  sensors.init();
  // #endif

  // #ifdef APP-PLUS
  // const sensors = uni.requireNativePlugin('Sensorsdata-UniPlugin-App');
  // //同意隐私协议后调用进行 SDK 初始化
  // sensors.initSDK({
  //   server_url: `${Config.DXHost}log/do/sensors`,
  //   show_log: true, //是否开启日志
  //   name: 'sensors',
  //   global_properties: {
  //     // 配置全局属性，所有上报事件属性中均会携带
  //     // property1: 'value1'
  //   },
  //   autoTrack: {
  //     //小程序全埋点配置
  //     appLaunch: true, // 默认为 true，false 则关闭 $MPLaunch 事件采集
  //     appShow: true, // 默认为 true，false 则关闭 $MPShow 事件采集
  //     appHide: true, // 默认为 true，false 则关闭 $MPHide 事件采集
  //     pageShow: true, // 默认为 true，false 则关闭 $MPViewScreen 事件采集
  //     pageShare: true, // 默认为 true，false 则关闭 $MPShare 事件采集
  //     mpClick: false, // 默认为 false，true 则开启 $MPClick 事件采集
  //     mpFavorite: true, // 默认为 true，false 则关闭 $MPAddFavorites 事件采集
  //     pageLeave: false // 默认为 false， true 则开启 $MPPageLeave事件采集
  //   },
  //   app: {
  //     // Android & iOS 初始化配置
  //     remote_config_url: '',
  //     flush_interval: 15000, //两次数据发送的最小时间间隔，单位毫秒
  //     flush_bulkSize: 100, //设置本地缓存日志的最大条目数，最小 50 条， 默认 100 条
  //     flush_network_policy: 30, //设置 flush 时网络发送策略
  //     auto_track: 0, // 1 应用启动， 2 应用退出，3 应用启动和退出 默认 0
  //     encrypt: false, //是否开启加密
  //     add_channel_callback_event: false, //是否开启渠道事件
  //     javascript_bridge: false, // WebView 打通功能
  //     android: {
  //       //Android 特有配置
  //       session_interval_time: 30000,
  //       request_network: true,
  //       max_cache_size: 32, // 默认 32MB，最小 16MB
  //       mp_process_flush: false //使用小程序 SDK 时，小程序进程是否可发送数据
  //     },
  //     ios: {
  //       //iOS 特有配置
  //       max_cache_size: 10000 //最大缓存条数，默认 10000 条
  //     }
  //   }
  // });
  // #endif

  // 七鱼客服插件
  // #ifdef MP-WEIXIN
  requirePlugin('qiyukf', (myPluginInterface) => {
    let appId = 'wx9ffadc161c84c7c7';
    let appKey = 'bf4b50ff1b7f469c799230a0296dc25a';
    myPluginInterface.__configAppId(appId);
    myPluginInterface._$configAppKey(appKey); // 申请企业的appKey
  });
  // #endif

  export default {
    onLaunch: function (e) {
      // this.getDynamicGatewayFlag();
      console.log(e);
      // let token =
      //   '***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************';
      // uni.setStorageSync('token', token);

      if (e.query.baseUrl) {
        uni.setStorageSync('baseUrl', e.query.baseUrl + '/');
        Config.DXHost = e.query.baseUrl + '/';
      }
      if (e.query.token) {
        uni.setStorageSync('token', e.query.token);
      }
    },
    globalData: {
      imguseHost: Config.ImguseHost,
      imgsomeHost: Config.ImgsomeHost,
      postHost: Config.PostHost,
      interestDownTime: 15
    },
    onShow: function (e) {
      console.log(2222222222);

      // console.log(e);
      this.jumpFormParent(e.referrerInfo);
      this.onStartupScene(e.query);
      // console.log('App Show')
      // this.checkForUpdate();
    },
    methods: {
      async getDynamicGatewayFlag() {
        let res = await $http({ url: 'zx/common/getDynamicGatewayFlag', method: 'get' });
        if (res?.data) {
          let url = 'https://uat-gateway.dxznjy.com/';
          if (Config.DXHost != url) {
            Config.DXHost = url;
            PublicVariable.host = url;
            httpUser.setConfig((config) => {
              config.baseUrl = Config.DXHost;
              return config;
            });
          }
        }
      },
      // 检测是否更新
      checkForUpdate() {
        const _this = this;
        // 检查小程序是否有新版本发布
        const updateManager = uni.getUpdateManager();
        // 请求完新版本信息的回调
        updateManager.onCheckForUpdate((res) => {
          console.log('onCheckForUpdate-res', res);
          //检测到新版本，需要更新，给出提示
          if (res && res.hasUpdate) {
            uni.showModal({
              title: '更新提示',
              content: '检测到新版本，是否下载新版本并重启小程序？',
              success(res) {
                if (res.confirm) {
                  //用户确定下载更新小程序，小程序下载及更新静默进行
                  _this.downLoadAndUpdate(updateManager);
                } else {
                  // 若用户点击了取消按钮，二次弹窗，强制更新，如果用户选择取消后不需要进行任何操作，则以下内容可忽略
                  uni.showModal({
                    title: '温馨提示~',
                    content: '本次版本更新涉及到新的功能添加，旧版本无法正常访问的哦~',
                    confirmText: '确定更新',
                    cancelText: '取消更新',
                    success(res) {
                      if (res.confirm) {
                        //下载新版本，并重新应用
                        _this.downLoadAndUpdate(updateManager);
                      }
                    }
                  });
                }
              }
            });
          }
        });
      },
      // 下载小程序新版本并重启应用
      downLoadAndUpdate(updateManager) {
        const _this = this;
        uni.showLoading({
          title: '小程序更新中'
        });

        // //静默下载更新小程序新版本
        updateManager.onUpdateReady((res) => {
          console.log('onUpdateReady-res', res);
          uni.hideLoading();
          //新的版本已经下载好，调用 applyUpdate 应用新版本并重启
          updateManager.applyUpdate();
        });

        // 更新失败
        updateManager.onUpdateFailed((res) => {
          console.log('onUpdateFailed-res', res);
          // 新的版本下载失败
          uni.hideLoading();
          uni.showModal({
            title: '已经有新版本了哟~',
            content: '新版本已经上线啦~，请您删除当前小程序，重新搜索打开哟~',
            showCancel: false
          });
        });
      },
      //接收家长端小程序带来的值
      jumpFormParent(referrerInfo) {
        if (uni.getStorageSync('token')) {
          return;
        }
        if (referrerInfo.extraData) {
          if (referrerInfo.extraData.parentToken && referrerInfo.extraData.parentToken != '') {
            uni.setStorageSync('token', referrerInfo.extraData.parentToken);
          }
        }
      },
      onStartupScene(query) {
        let refereeId = query.scene ? query.scene : '';
        refereeId && this.saveRefereeId(refereeId);
      },
      saveRefereeId(refereeId) {
        wx.setStorageSync('referee_id', refereeId);
      }
    },
    onHide: function () {
      // console.log('App Hide')
    }
  };
</script>

<style lang="scss">
  @import 'katex/dist/katex.min.css';
  @import 'uview-ui/index.scss';
  /*每个页面公共css */
  @import '@/static/iconfont.css';
  // @import url("/wxcomponents/vant/dist/common/index.wxss");
  @import url('/util/common.css');
  @import url('/common/font.css');
  @import './common/common.css';
  @import './common/zwyCss.css';

  // @import url("/util/common.css");

  page {
    background-color: #f3f8fc;
  }

  .pb-200 {
    padding-bottom: 200rpx;
  }

  .box-34 {
    width: 34rpx;
    height: 34rpx;
  }

  .box-36 {
    width: 36rpx;
    height: 36rpx;
  }

  .box-40 {
    width: 40rpx;
    height: 40rpx;
  }

  .pb-112 {
    padding-bottom: 112rpx;
  }

  .moreicon {
    width: 80rpx;
    height: 28rpx;
  }

  .box-30 {
    width: 30rpx;
    height: 30rpx;
  }

  .arrow {
    width: 16rpx;
    height: 14rpx;
  }

  .tips {
    width: 32upx;
    height: 20upx;
  }

  /* radio 选中后的样式 */
  uni-radio .uni-radio-input.uni-radio-input-checked {
    background-color: #248067 !important;
    border-color: #248067 !important;
    background-clip: content-box !important;
    padding: 6rpx !important;
    box-sizing: border-box;
  }

  .indextop uni-swiper,
  .indextop .swiper-box {
    min-height: 10vh;
  }

  .course-detail uni-swiper,
  .course-detail .swiper-box {
    min-height: 10vh;
  }

  .h-flex-x {
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
    justify-content: flex-start;
    align-items: flex-start;
    align-content: flex-start;

    &.h-flex-2 {
      > view {
        width: 50%;
      }
    }
  }
</style>
