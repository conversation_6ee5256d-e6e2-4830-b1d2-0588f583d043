<template>
  <view class="p-30">
    <view v-if="goods_list.length == 0">
      <!-- <u-empty mode="history" icon="https://cdn.uviewui.com/uview/empty/list.png" text="暂无数据"></u-empty> -->
      <!-- <u-empty mode="history" icon="/static/cart/no_data.png" text="暂无数据"></u-empty> -->
      <view class="no_data radius-15 f-30 flex_s">
        <image :src="imgHost + 'alading/correcting/no_data.png'" mode="widthFix" class="img_s mb-10"></image>
        <view style="color: #bdbdbd">
          暂无数据，去
          <text class="product_s" @tap="skintap('Personalcenter/my/course')">产品中心</text>
          看看
        </view>
      </view>
      <view class="f-32 mt-40 t-c">
        <uni-icons custom-prefix="iconfont" type="icon-love" size="23" color="#EA6031"></uni-icons>
        <text class="ml-20 colour">你可能喜欢</text>
      </view>
      <view class="courseList pt-30">
        <block v-for="(item, index) in infoLists.list" :key="index">
          <view class="courseItem radius-20 pb-10 positionRelative" @tap.stop="skintap('Coursedetails/productDetils?id=' + item.courseId)">
            <view class="courseimg relative">
              <image :src="item.courseImage" class="wh100" mode="widthFix"></image>
            </view>
            <view class="positionAbsolute courseItem_tig">
              <image v-if="item.courseId == 'f2cf0e76538473a7179c993674e09bdf'" :src="imgHost + 'alading/correcting/mustBuy_icon.png'" class="wh100" mode="widthFix"></image>
              <image v-if="item.courseId == 'c499556f9da64dce19b723296c884bc4'" :src="imgHost + 'alading/correcting/recommend_icon.png'" class="wh100" mode="widthFix"></image>
            </view>
            <view class="mtb-20 pl-20 pr-20">
              <view class="bold f-28">{{ item.courseName }}</view>
              <view class="color_red font12 mtb-16">
                会员价
                <span class="bold f-34">￥{{ item.memberPrice }}</span>
              </view>
              <view class="displayflex color_grey f-24" style="justify-content: space-between">
                <view class="">
                  原价
                  <text style="text-decoration: line-through">￥{{ item.originalPrice }}</text>
                </view>
                <view class="">{{ item.studyNumber }}+人付款</view>
              </view>
            </view>
          </view>
        </block>
        <view v-if="no_more && infoLists.list.length > 0" style="width: 100%; text-align: center">
          <u-divider text="到底了"></u-divider>
        </view>
      </view>
    </view>
    <view>
      <u-swipe-action>
        <u-swipe-action-item class="mb-20" :options="options1" v-for="(item, index) in goods_list" :key="index" @click="delete1(item)">
          <view class="bg-ff p-30 flex-dir-row">
            <view class="flex-dir-row flex-y-c">
              <view class="dish-item-radio mr-25" @click="onChecked(index)">
                <label class="radio">
                  <radio :checked="item.checked" color="#EA6031"></radio>
                </label>
              </view>
              <view class="box-180 radius-20 positionRelative">
                <image :src="item.courseImage" class="wh100"></image>
                <view v-if="item.courseId == 'c499556f9da64dce19b723296c884bc4'" class="positionAbsolute courseItem_tig">
                  <image :src="imgHost + 'alading/correcting/recommend_icon.png'" class="mustWidth" mode="widthFix"></image>
                </view>
              </view>
            </view>
            <view class="flex-box ml-20 flex-dir-col flex-x-b">
              <view class="f-32 bold mb-10 overstepSingle">{{ item.courseName }}</view>
              <view class="mb-25">
                <text class="f-26 color_red">
                  会员价
                  <text class="f-32 bold">￥{{ item.coursePrice }}</text>
                </text>
                <text class="ml-30 f-26 color_grey" style="text-decoration: line-through">原价￥9999</text>
              </view>
              <view class="f-28 flex">
                <u-number-box v-model="item.buyNum" @change="valChange($event, index)"></u-number-box>
              </view>
            </view>
          </view>
        </u-swipe-action-item>
      </u-swipe-action>
    </view>
    <!-- 底部栏 -->
    <view class="fixed" v-if="goods_list.length != 0">
      <view class="plr-30 flex">
        <view class="cart-item-total flex-dir-row flex-y-c">
          <view class="checked-all mr-20">
            <label @click="onCheckedAll" class="radio dis-flex flex-y-center">
              <radio class="margin-right: 10rpx;" :checked="checkedAll" color="#EA6031"></radio>
              <text class="f-28 ml-10 color_grey66">全选</text>
            </label>
          </view>
          <view class="cart-item-total-price dis-flex"></view>
        </view>
        <view class="flexbox">
          <view class="f-28 col-m mr-20 color_grey66">
            合计：
            <text class="color_tangerine fontWeight">￥{{ cartTotalPrice }}</text>
          </view>
          <view class="paybtn f-32 c-ff" @click="submit">结算</view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
  const { $http, $showMsg, $navigationTo } = require('@/util/methods.js');
  export default {
    data() {
      return {
        options1: [
          {
            text: '删除',
            icon: 'https://document.dxznjy.com/dxSelect/delete.png',
            iconSize: '30',
            style: {
              backgroundColor: '#EA6031'
            }
          }
        ],
        cartTotalPrice: 0, //总价
        checkedAll: false,
        goods_list: [],
        selectArr: [],
        page: 1,
        no_more: false,
        infoLists: {},
        imgHost: getApp().globalData.imgsomeHost
      };
    },
    onLoad() {
      this.course();
    },
    onShow() {
      this.getCartList();
    },
    onReachBottom() {
      if (this.page >= this.infoLists.totalPage) {
        this.no_more = true;
        return false;
      }
      this.course(true, ++this.page);
    },
    methods: {
      async delete1(e) {
        let _this = this;
        const res = await $http({
          url: 'zx/course/delShoppingCar',
          data: {
            carId: e.carId
          }
        });
        if (res) {
          $showMsg(res.message);
          setTimeout(function () {
            _this.getCartList();
          }, 1500);
        }
      },
      // 购物车数量加减
      async valChange(e, index) {
        let _this = this;
        let num = e.value;
        const res = await $http({
          url: 'zx/course/changeShoppingCarNum',
          method: 'post',
          data: {
            buyNum: num,
            courseId: _this.goods_list[index].courseId
          }
        });
        if (res) {
        }
      },
      // 确认下单
      submit() {
        let _this = this;

        let newArr = _this.goods_list.filter((v, j) => v.checked == true);
        let result = newArr.map((obj) => {
          return Object.assign(
            {},
            {
              buyNum: obj.buyNum,
              courseId: obj.courseId
            }
          );
        });
        if (!result.length) {
          $showMsg('请先选择课程');
          return false;
        }
        uni.navigateTo({
          url: './confirm?cart_ids=' + JSON.stringify(result)
        });
      },
      // 全选
      onCheckedAll() {
        let _this = this,
          goodsList = _this.goods_list;
        // 更新商品选中记录
        goodsList.forEach((item) => {
          item.checked = !_this.checkedAll;
        });
        _this.goods_list = goodsList;
        _this.checkedAll = !_this.checkedAll;
        _this.onUpdateChecked();
        // 更新购物车已选商品总价格
        _this.updateTotalPrice();
      },
      // 单选
      onChecked(index) {
        let _this = this;
        let obj = _this.goods_list[index],
          checked = !obj.checked;
        _this.goods_list[index].checked = checked;
        // 更新选择状态
        _this.onUpdateChecked();
        // 更新购物车已选商品总价格
        _this.updateTotalPrice();
      },
      async updateTotalPrice() {
        let _this = this;
        let newArr = _this.goods_list.filter((v, j) => v.checked == true);
        let result = newArr.map((obj) => {
          return Object.assign(
            {},
            {
              buyNum: obj.buyNum,
              courseId: obj.courseId
            }
          );
        });
        const res = await $http({
          url: 'zx/course/calculatePrice',
          method: 'post',
          data: {
            courseAndNumDto: result,
            currencyNumber: 0
          }
        });
        if (res) {
          _this.cartTotalPrice = res.data.totalPrice;
        }
        console.log(result);
      },
      onUpdateChecked() {
        let _this = this;
        let newArr = _this.goods_list.filter((v, j) => v.checked == true);
        if (newArr.length == _this.goods_list.length) {
          _this.checkedAll = true;
        } else {
          _this.checkedAll = false;
        }
        // let newArr = _this.goods_list.filter((v, j) => arr.some((val) => j == val))
        // _this.selectArr = newArr
      },

      // 加入购物车
      async getCartList() {
        let _this = this;
        const res = await $http({
          url: 'zx/course/courseInCarList',
          data: {}
        });
        if (res) {
          res.data.forEach((item, index) => {
            item.checked = false;
          });
          _this.goods_list = res.data;
        }
      },

      async course(isPage, page) {
        let _this = this;
        const res = await $http({
          url: 'zx/course/courseList',
          data: {
            indexShow: 1,
            cityCode: '',
            cateId: '',
            page: page || 1,
            cateType: 1
          }
        });
        if (res) {
          if (isPage) {
            let old = _this.infoLists.list;
            _this.infoLists.list = [...old, ...res.data.list];
          } else {
            _this.infoLists = res.data;
          }
        }
      },

      skintap(url) {
        $navigationTo(url);
        // if (!uni.getStorageSync('token')) {
        // 	uni.navigateTo({
        // 		url: '/Personalcenter/login/login'
        // 	})
        // } else {
        // 	$navigationTo(url)
        // }
      }
    }
  };
</script>

<style lang="scss">
  .no_data {
    width: 100%;
    height: 500rpx;
    background-color: #fff;
  }
  .flex_s {
    text-align: center;
    vertical-align: middle;
    // margin-top: 200rpx;
  }

  .img_s {
    width: 136rpx;
    margin-top: 154rpx;
  }
  .product_s {
    color: #ea6031;
  }

  .colour {
    color: #ea6031;
    letter-spacing: 4rpx;
  }
  .u-swipe-action-item {
    margin-bottom: 30rpx;
  }

  .paybtn {
    width: 170rpx;
    height: 70rpx;
    background-image: linear-gradient(to bottom, #f09234, #ea6031);
    border-radius: 35rpx;
    line-height: 70rpx;
    text-align: center;
  }

  .icon_dw {
    width: 26rpx;
    height: 30rpx;
  }

  .remark {
    height: 150rpx;
    width: 100%;
    overflow: scroll;
  }

  .fixed {
    width: 100%;
    padding: 20rpx 0;
    bottom: 0;
    position: fixed;
    left: 0;
    background-color: #fff;
    border-bottom: 1px solid #ebebeb;
  }

  .u-swipe-action-item {
    border-radius: 14rpx;
  }

  .u-number-box {
    background-color: #f8f8f8 !important;
    border-radius: 50rpx;
    width: 180rpx !important;
  }
  .u-number-box__input {
    background-color: #f8f8f8 !important;
  }

  /deep/.u-number-box__plus {
    height: 56rpx !important;
    width: 56rpx !important;
    background-color: #eee !important;
    border-radius: 50% !important;
    .u-icon__icon {
      color: #333 !important;
      font-size: 20rpx !important;
      font-weight: bold;
    }
  }

  /deep/.data-v-18418972 {
    height: 56rpx !important;
    font-weight: bold;
  }

  /deep/.u-number-box__minus {
    height: 56rpx !important;
    width: 56rpx !important;
    background-color: #eee !important;
    border-radius: 50% !important;

    .u-icon__icon {
      color: #999 !important;
      font-size: 20rpx !important;
    }
  }

  .courseItem_tig {
    width: 115rpx;
    top: 0;
    left: 0;
  }

  .mustWidth {
    width: 100upx;
  }

  .courseList {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
  }

  .courseItem {
    width: 330upx;
    border-radius: 20upx;
    background-color: #fff;
    margin-bottom: 30rpx;
  }

  /deep/.u-popup__content {
    position: relative;
    background-color: transparent !important;
  }

  .close_icon {
    width: 44rpx;
    position: absolute;
    bottom: 96rpx;
    left: 47%;
  }

  /deep/.u-icon__img {
    width: 40rpx !important;
    height: 40rpx !important;
    margin-bottom: 20rpx !important;
  }

  /deep/.u-swipe-action-item__right__button__wrapper {
    flex-direction: column !important;
    padding: 0 50rpx !important;
  }

  /deep/.u-line-1 {
    font-size: 28rpx !important;
  }

  /deep/.u-number-box__input {
    padding: 0 !important;
    width: 100% !important;
  }
</style>
