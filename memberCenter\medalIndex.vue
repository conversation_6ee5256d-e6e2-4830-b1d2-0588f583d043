<template>
	<view>
		<view class="medal_content_top">
			<view class="flex-self-s flex-x-s">
				<view class="pl-45 c-ff mt-80 left_header_css">
					<image class="header_avaUrl radius-all" :src="personBadgeInfo.headPhoto?personBadgeInfo.headPhoto:headerAvaUrl"></image>
					<view class="mt-16"><span class="f-36 bold inline">{{ personBadgeInfo.userName }}</span><span class="f-28 inline ml-10">的徽章墙</span></view>
					<view class="f-28"> 
						<view><span class="inline">累计获得 </span> <span class="f-40 inline mlr-5">{{personBadgeInfo.num}}</span> <span class="inline">枚勋章</span></view>
						<view class="medal_line"></view>
					</view>
				</view>
				<view v-if="showBadgeInfo.photo">
					<image class="medal_header_avaUrl mt-35" :src="showBadgeInfo.photo"></image>
					<view class="f-24 c-ff mt-16 pl-15">{{showBadgeInfo.createTime}}获得</view>
				</view>
			</view>
		</view>
		<view>
			<image class="icon_center_css" src="https://document.dxznjy.com/course/8dfcadb8deb1442d99a0f16a123d3746.png"></image>
		</view>
		<view class="mt-45">
			<view class="flex-self-s flex-wrap flex-x-b medal_List_css">
				<view v-for="(item,index) in meadalList" :key="index" @click="wearBadeg(item)" class="medal_item_css">
					<image v-if="item.badegShow"  class="medal_image_css" :src="item.photo"></image>
					<image v-else class="medal_image_css" :src="item.grayPhoto"></image>
					<view class="f-28 c-55 image_name_css lh-40">{{item.badgeName}}</view>
				</view>
			</view>
		</view>
		<uni-popup ref="badge_popup" type="bottom" style="padding: 0;" >
			<view class="badge_popup_css pb-45 pt-24 bg-ff">
				<view  v-if="!showBadgeItem.badegShow">您还未获取该勋章</view>
				<image class="badge_image_css" v-if="showBadgeItem.badegShow" :src="showBadgeItem.photo"></image>
				<image class="badge_image_css" v-else :src="showBadgeItem.grayPhoto"></image>
				<view class="f-28 c-33 mb-15 bold">{{showBadgeItem.badgeName}}</view>
				<view class="f-24 c-55">{{showBadgeItem.badgeRule}}</view>
				<button class="badge_button_css c-ff f-28" @click="addBadge" v-if="showBadgeItem.badegShow" >佩戴勋章</button>
			</view>
		</uni-popup>
	</view>

</template>

<script>
	const {
		$navigationTo,
		$getSceneData,
		$showError,
		$http
	} = require("@/util/methods.js")
	export default {
		data() {
			return {
				headerAvaUrl:'https://document.dxznjy.com/dxSelect/home_avaUrl.png',
				personBadgeInfo:{},
				showBadgeInfo:{},
				showBadgeItem:{},
				meadalList:[]
			}
		},
		onLoad() {
			this.getPersonBadge()
		},
		methods: {
			// /
			async getBadge(){
				const res = await $http({
					url: 'zx/wap/badge',
					data: {}
				})
				if(res){
					this.meadalList=res.data
					if(this.personBadgeInfo.obtains.length>0){
						this.personBadgeInfo.obtains.forEach(item=>{
							this.meadalList.forEach(info=>{
								if(item.badgeId==info.id){
									info.badegShow=true
								}
							})
						})
					}
				}
			},
			getTime(time){
				let list=time.split(' ')[0].split('-')
				if(list.length>0){
					return list[0]+'年'+list[1]+'月'+list[2]+'日'
				}
				
				
			},
			wearBadeg(item){
				this.showBadgeItem=item
				this.$refs.badge_popup.open()
			},
			async addBadge(){
				const res = await $http({
					url: 'zx/wap/badge/wear?badgeId='+this.showBadgeItem.id+'&userId='+uni.getStorageSync('user_id'),
					method: 'POST',
					data: {
						badgeId:this.showBadgeItem.id,
						userId:uni.getStorageSync('user_id')?uni.getStorageSync('user_id'):'',
					}
				})
				if(res){
					this.getPersonBadge(true)
					this.$refs.badge_popup.close()
				}
			},
			async getPersonBadge(show){
				const res = await $http({
					url: 'zx/wap/badge/getPersonBadge',
					data: {
						userId:uni.getStorageSync('user_id')?uni.getStorageSync('user_id'):'',
					}
				})
				if(res){
					this.personBadgeInfo=res.data
					this.personBadgeInfo.obtains.forEach(item=>{
						if(item.isWear==1){
							this.showBadgeInfo=item
							this.showBadgeInfo.createTime=this.getTime(item.createTime)
						}
					})
					if(!show){
						this.getBadge()
					}
				}
			},
		}
	}
</script>

<style lang="scss" scoped>
	.medal_item_css{
		padding:0 48rpx;
		.medal_image_css{
			width: 144rpx;
			height: 174rpx;
			display: block;
		}
		.image_name_css{
			width: 148rpx;
			text-align: center;
			margin-top: 18rpx;
			margin-bottom: 40rpx;
		}
	}
	.medal_content_top{
		background: url('https://document.dxznjy.com/course/7383578caf1b418e9694159bdb9bb188.png') no-repeat;
		background-size: 100%;
		width: 750rpx;
		height: 464rpx;
		.header_avaUrl{
			width: 82rpx;
			height: 82rpx;
			border:6rpx solid #E3FEDF;
		}
		.medal_line{
			width: 262rpx;
			height: 26rpx;
			background: #FB7A11;
			border-radius: 14rpx;
			margin-top: -22rpx;
			margin-left: -16rpx;
		}
		.left_header_css{
			width:370rpx;
		}
	}
	.medal_header_avaUrl{
		width: 252rpx;
		height: 308rpx;
	}
	.icon_center_css{
		width: 264rpx;
		height: 46rpx;
		margin:0 auto;
		display: block;
		margin-top: 28rpx;
	}
	.medal_item_css:nth-child(3n){
		
		// margin-left: 100rpx;
	}
	.badge_popup_css{
		text-align: center;
		.badge_image_css{
			width: 252rpx;
			height: 308rpx;
			margin: 20rpx auto;
		}
		.badge_button_css{
			width: 686rpx;
			height: 74rpx;
			line-height: 74rpx;
			border-radius: 38rpx;
			background-color: #339378;
			margin:20rpx auto;
		}
	}
</style>
