<template>
	<view>
		<view class="release_item_css">
			<view>
				<image class="radius-all" :style="'width:'+releaseStyle.leftImageWidth+';height:'+releaseStyle.leftImageWidth" :src="releaseInfo.headPhoto||avaUrl"></image>
			</view>
			<view class="ml-20">
				<view class="c-33 f-28 lh-40">{{releaseInfo.userName}}</view>
				<view class="f-28 c-55 lh-42 mt-15">{{releaseInfo.content}}</view>
				<view v-if="releaseType=='1'||releaseType=='3'"  class="release_list_css flex-wrap flex-x-s">
					<view v-for="(item,index) in releaseInfo.photoPositionDtos" class="item_css" :key="index">
						<view v-if="item.url.split('.')[item.url.split('.').length-1]=='mp4'" >
							<video object-fit="fill" play-btn-position="center" :show-fullscreen-btn="false" :show-center-play-btn="false"   class="file_image_css" :src="item.url"></video>
						</view>
						<view v-else >
							<image @click="showImage(releaseInfo.photoPositionDtos,index)" class="file_image_css" :src="item.url"></image>
						</view>
					</view>
				</view>
				<view v-if="releaseType=='3'"  class="f-24 release_time">
					{{releaseInfo.releaseTime}}
				</view>
				<view v-if="releaseType=='2'" >
					<view class="flex-a-c flex-x-s">
						<view class="mr-65">
							<span>{{releaseInfo.releaseTime}}</span>
							<span @click="getInputFocus">回复</span>
						</view>
						<u-icon name="heart" color="#666" size="38"></u-icon>
						<view>123</view>
					</view>
					<view>展开{{releaseInfo.comments}}调数据</view>
					<view class="input_key_css" :style="'bottom:'+keyUpHeight+'px;'" v-if="inputFocus">
						{{inputFocus}}
						<input class="p-45" type="text" :adjust-position="false" :auto-blur="true" :focus="inputFocus" placeholder="回复" ref="inputFocus">
						<view @click="replyInfo">发布</view>
					</view>
				</view>
				<view v-if="releaseType=='1'" class="flex-self-c flex-x-b mt-35 release_bottom_css">
					<view class="f-24 release_time">
						{{releaseInfo.releaseTime}}
					</view>
					<view class="right_css_style">
						<image @click="getRelease" class="release_image" src="https://document.dxznjy.com/course/f43e60249b8f42b6916693e6fe78d065.png"></image>
						<view class="ml-8 f-24 release_text">{{releaseInfo.comments }}</view>
							<!-- heart -->
						<!-- <u-icon name="heart" color="#555" size="34"></u-icon> -->
						<image v-if="releaseInfo.isThumb" @click="getThumb(1)" class="release_image ml-35" src="https://document.dxznjy.com/course/277db746077545ee980467f2ec7a50db.png"></image>
						<image v-else class="release_image ml-35" @click="getThumb(2)" src="https://document.dxznjy.com/course/4eaf055301bc472a949c5b4e15792cf4.png"></image>
						<view class="ml-8 f-24 release_text">{{releaseInfo.thumbsUp }}</view>
						<image  v-if="releaseInfo.isCollect&&!showLike"  @click="addCollect(1)"  class="release_image ml-35" src="https://document.dxznjy.com/course/84e60ae962eb4a91bceb13e092f0d740.png"></image>
						<image  v-else-if="!showLike"  @click="addCollect(2)" class="release_image ml-35" src="https://document.dxznjy.com/course/f67364b6ce9d4cf38041eb26bfe9460f.png"></image>
					</view>
				</view>
			</view>
		</view>
		<uni-popup ref="preview_image" type="center" style="padding: 0;" >
			<view class="preview_image_content">
				<image mode="widthFix" class="w100" :src="previewImage"></image>
				<view class="close_circle" @click="$refs.preview_image.close()">
					<u-icon name="close-circle" color="#b1b1b1" size="38"></u-icon>
				</view>
			</view>
		</uni-popup>
	</view>
</template>
<script>
	const {
			$navigationTo,
			$getSceneData,
			$showMsg,
			$showError,
			$http
		} = require("@/util/methods.js")
	export default {
		props:['releaseInfo','releaseStyle','releaseType','showLike','myAvaUrl'],
		data() {
			return {
				inputFocus:false,
				keyUpHeight:0,
				previewImage:'',
				avaUrl: this.myAvaUrl?this.myAvaUrl:'https://document.dxznjy.com/dxSelect/home_avaUrl.png',
			}
		},
		methods: {
			getRelease(){
				this.$emit('getRelease',this.releaseInfo)
			},
			// /zx/wap/CultureCircle/thumb
			getThumb(key){
				this.$emit('addThumb',this.releaseInfo,key)
			},
			addCollect(key){
				this.$emit('addCollect',this.releaseInfo,key)
			},
			getInputFocus(){
				this.inputFocus=true
				uni.onKeyboardHeightChange(res => {
					this.keyUpHeight=res.height
					console.log(res)
					if(res.duration>0&&res.height==0){
						this.inputFocus=false
					}
				})
			},
			showImage(list,index){
				this.$emit('showImage',list,index)
			},
			replyInfo(){
				this.inputFocus=false
			}
		}
	}
</script>

<style lang="scss" scoped>
	.release_item_css{
		display: flex;
		justify-content: flex-start;
		word-break: break-all;
	}
	.release_bottom_css{
		width: 575rpx;
	}
	.release_image{
		width: 32rpx;
		height: 32rpx;
	}
	.release_time{
		color:#BFBFBF;
	}
	.release_list_css{
		margin-top: 22rpx;
		.item_css{
			margin-right: 10rpx;
		}
		.file_image_css{
			width: 160rpx;
			height: 160rpx;
		}
	}
	.right_css_style{
		display: flex;
		justify-content: flex-start;
	}
	.input_key_css{
		position: absolute;
		left:0;
		bottom:500px;
		background-color: #fff;
		width: 750rpx;
		display: flex;
		justify-content: flex-start;
		z-index: 999;
	}
	.preview_image_content{
		position: relative;
		width: 686rpx;
		margin:auto;
		.close_circle{
			position: absolute;
			top:30rpx;
			right:30rpx;
		}
	}
</style>
