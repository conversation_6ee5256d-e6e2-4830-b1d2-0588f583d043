<template>
  <div class="suspension_btn" @click="release()">
    <image class="suspension_image" src="https://document.dxznjy.com/course/7e37ef354d5f421cb5dc00ce79bb477c.png"></image>
  </div>
</template>

<script>
  export default {
    props: ['fixedText', 'showIcon'],
    data() {
      return {};
    },
    methods: {
      release() {
        this.$emit('release');
      }
    }
  };
</script>

<style lang="scss" scoped>
  .suspension_btn {
    position: fixed;
    z-index: 1;
    right: 0rpx;
    bottom: 128rpx;
    // width: 360rpx;
    // height: 95rpx;
    border-radius: 48rpx;
    // background: #28A781;
    // box-shadow: 0rpx 4rpx 8rpx 0rpx rgba(62,207,164,0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    .suspension_image {
      width: 147rpx;
      height: 80rpx;
    }
  }
</style>
