<template>
	<page-meta :page-style="'overflow:'+(show?'hidden':'visible')"></page-meta>
	<view class="grow_up_record">
		<view class="c-ff plr-32 flex-a-c flex-x-b">
			<view>
				<view class="f-24 lh-36">当前成长值：</view>
				<view class="lh-72 f-56 bold mt-8">{{ growRecordList.growthValue }}</view>
			</view>
			<view @click="reward()" class="grow_btn f-24">成长值规则</view>
		</view>
		<view class="bg-ff grow_up_content plr-32">
			<view v-for="(item,index) in growRecordList.details" :key="index" class="flex-a-c flex-x-b grow_up_item">
				<view>
					<view class="f-28 c-55 bold lh-40">{{item.eventName}}</view>
					<viewe class="f-28 c-ab lh-40 mt-16">{{item.time}}</viewe>
				</view>
				<view class="f-28 grow_right_css bold">+{{item.integral}}</view>
			</view>
		</view>
		<uni-popup  ref="reward_popup" type="center" style="padding: 0;" @change="change">
			<view class="cost_content_css lh-32">
				<view class="f-22 plr-15 cost_content_style">
					<view class="f-24 cost_content_text">成长值活动规则</view>
					<view class="mt-15">欢迎各位家长会员参与我们的“文化中心”成长值排行榜挑战！本活动旨在激励大家积极分享学习成果、增进互动交流，共同营造一个活力满满的会员文化社区。以下是活动的具体规则：
					活动时间</view>
					•活动周期：每月1日至月末最后一天。
					<view>•排行榜更新：每日23时：59分：59秒更新当日成长值排行榜。
					活动对象</view>
					•所有鼎校甄选会员用户自动参与。
					<view>排行榜分类</view>
					1、日排行榜：每日根据用户当日获得的成长值进行排名。
					成长值获取方式<br/>
					1、会员用户每日进入文化中心板块+10点成长值<br/>
					2、会员用户每完成一节家庭教育录播课时学习，+30点成长值<br/>
					3、会员用户每上传一次文化记录，+10点成长值。上限40点/日<br/>
					4、会员用户每日点赞一篇动态，+5点成长值。上限10点/日<br/>
					5、会员用户每日评论一篇动态，+5点成长值。上限10点/日<br/>
					6、会员用户每日发布一个提问，+5点成长值。上限10点/日<br/>
					7、会员用户每日回答一个提问，+5点成长值。上限10点/日
					<view>其他注意事项</view>
					•禁止任何作弊行为，如刷赞、水评论等，一经发现取消排名<br/>
					•如果榜单成长值相同，排名按照最先达到该成长值时间进行排名。<br/>
					•活动最终解释权归鼎校甄选官方所有。<br/>
					•如有疑问，可联系在线客服咨询。<br/>
					让我们在文化的海洋里遨游，携手攀登巅峰，赢取属于你的荣耀吧！
				</view>
				<view class="close_circle" @click="$refs.reward_popup.close()">
					<u-icon name="close-circle" color="#b1b1b1" size="38"></u-icon>
				</view>
			</view>
		</uni-popup>
	</view>
</template>

<script>
	const {
			$navigationTo,
			$getSceneData,
			$showError,
			$http
		} = require("@/util/methods.js")
	export default {
		data() {
			return {
				growRecordList:[],
				show:false
			}
		},
		onShow() {
			this.getEventRecord()
		},
		methods: {
			// /
			async getEventRecord(){
				let _this = this
				const res = await $http({
					url: 'zx/wap/rank/getEventRecord',
					showLoading:true,
					data: {
						userId:uni.getStorageSync('user_id')?uni.getStorageSync('user_id'):'',
					}
				})
				if(res){
					this.growRecordList=res.data
				}
			},
			//将来说明
			reward(){
				this.$refs.reward_popup.open()
			},
			change(e) {
				this.show = e.show
			},
		}
	}
</script>

<style lang="scss" scoped>
.grow_up_record{
	background: url(https://document.dxznjy.com/course/bf8d6c5631e3447d9f1056f806865e2e.png) no-repeat;
	background-size: 100%;
	padding-top: 32rpx;
	.grow_up_content{
		width: 686rpx;
		padding-top: 7rpx;
		border-radius: 40rpx 40rpx 0rpx 0rpx;
		margin-top: 24rpx;
		min-height: calc(100vh - 190rpx);
		.grow_right_css{
			color:#EE9F00;
		}
	}
	.grow_btn{
		background:rgba(11, 165, 130, 0.74);
		width: 162rpx;
		height: 48rpx;
		border-radius: 24rpx;
		line-height: 48rpx;
		text-align: center;
	}
	.grow_up_item{
		padding:24rpx 0;
		border-bottom: 1rpx solid #ECF0F4;
		width: 686rpx;
	}
	.cost_content_css{
		width: 686rpx;
		height: 980rpx;
		background:url('https://document.dxznjy.com/course/f12fb50e68cc4cf5b9c433a9e968d4e7.png') no-repeat;
		background-size: 100%;
		padding-top:290rpx;
		position: relative;
		.close_circle{
			position: absolute;
			right:20rpx;
			top:0;
		}
		.cost_content_style{
			background: #fff;
			border-radius: 38rpx;
			padding-bottom: 20rpx;
			.cost_content_text{
				text-align: center;
			}
		}
	}
}
</style>
