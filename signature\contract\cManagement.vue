<template>
  <view class="c-55 f-28">
    <view>
      <u-notice-bar mode="horizontal" :text="noticeText" fontSize="24rpx" bgColor="#8fd9c3" color="#4e5969"></u-notice-bar>
    </view>
    <view class="content">
      <view class="list_view">
        <view class="list_head bold lh-42 c-33">已拥有合同数：{{ createAuth }}份</view>
        <view class="list_context flex-a-c flex-x-s">
          <view>实名认证：</view>
          <view class="lc_yellow" v-if="rzSimple === 0">未认证</view>
          <view class="lc_yellow lc_g" v-if="rzSimple === 1">已认证</view>
          <view class="lc_green mr-20" @click="goAuthentication('2')" v-if="rzSimple === 0">前往认证></view>
          <view class="lc_green mr-20" @click="goAuthentication('1')" v-if="rzSimple === 1">查看认证信息</view>

          <u-icon name="reload" color="#006F57" @click="fetchAuthInfoStatus"></u-icon>
        </view>
        <view class="list_bot">
          <view class="btn_b b_r" @click="createDetail('2')">待我签署</view>
          <view class="btn_b b_r" @click="createDetail('1')">合同明细</view>
          <!-- <view class="btn_b b_l" @click="createContract">创建合同</view> -->
        </view>
      </view>
      <view class="list_view">
        <view class="list_head bold lh-42 c-33">剩余合同数：{{ hasAuth }}份</view>
        <!-- <view class="list_context flex-a-c flex-x-s">
          <view>合同有效期：</view>
          <view class="radius-5">有效期至{{ authTime === null ? '-' : authTime }}</view>
        </view> -->
        <view class="list_bot">
          <view class="btn_b b_r" @click="createOrder">订购记录</view>
          <!-- <view class="btn_b b_l" @click="createlist">立即订购</view> -->
        </view>
      </view>
    </view>
  </view>
</template>

<script>
  const { $http } = require('@/util/methods.js');
  export default {
    data() {
      return {
        createAuth: '',
        hasAuth: '',
        authTime: '',
        rzSimple: -1,
        rzSimpleInfo: '',
        // wzfPay:true,
        wzfPay: false,
        authType: 0,
        userId: uni.getStorageSync('user_id') ? uni.getStorageSync('user_id') : '',
        noticeText:
          '根据电子签名法律法规相关要求，企业、个人在电子签名前均需要实名认证，通过信息核验、人脸比对、校验码回填、机构对公账户打款等方式确认身份的真实性，以此保障各方权益。',
        app: 0
      };
    },
    onLoad(e) {
      if (e.token) {
        this.app = e.app;
        this.$handleTokenFormNative(e);
        uni.setStorageSync('merchantCode', e.merchantCode);
        this.userId = uni.getStorageSync('user_id') ? uni.getStorageSync('user_id') : '';
      }
    },
    onUnload() {
      // #ifdef APP-PLUS
      if (this.app) {
        plus.runtime.quit();
      }
      // #endif
    },
    onShow() {
      this.fetchAuthInfo();
      // this.fetchAuthNum();
      this.getCountdownContracts(); //获取剩余合同份数
      this.getCountdownCount(); //获取已签合同数
    },
    methods: {
      //获取认证信息
      async fetchAuthInfo() {
        const res = await $http({
          url: 'zx/wap/esign/auth/authInfo'
        });
        if (res) {
          this.rzSimple = res.data.status;
          this.rzSimpleInfo = res.data.hasInfo;
          this.authType = res.data.authType;
        }
      },
      /**
       * 获取拥有合同数
       */
      async getCountdownCount() {
        // const res = await $http({
        //   url: 'znyy/sign/contract/getContractCount',
        //   method: 'get'
        // });
        let params = {
          mobile: uni.getStorageSync('phone') ? uni.getStorageSync('phone') : ''
        };
        this.$httpUser.get('znyy/sign/contract/getContractCount', params).then((res) => {
          if (res) {
            console.log('🚀 123123~ this.$httpUser.get ~ res:', res);
            this.createAuth = res.data.data || 0;
          }
        });
      },

      //刷新认证状态
      async fetchAuthInfoStatus() {
        const res = await $http({
          url: 'zx/wap/esign/auth/refresh?userId=' + this.userId,
          method: 'post'
        });
        if (res) {
          this.rzSimple = res.data.status;
          console.log('🚀 ~ fetchAuthInfoStatus ~ this.rzSimple:', this.rzSimple);
          this.rzSimpleInfo = res.data.hasInfo;
          this.authType = res.data.authType;
        }
      },
      //获取合同数量
      async fetchAuthNum() {
        const res = await $http({
          url: 'zx/wap/contract/totalInfo'
        });
        if (res) {
          this.createAuth = res.data.contractUseCount;
          // this.hasAuth = res.data.contractRemainCount;
          // this.authTime = res.data.expireTime;
        }
      },

      /**
       * 获取剩余合同份数
       */
      async getCountdownContracts() {
        const res = await $http({
          url: 'znyy/merchant/contract/query/contract/num?merchantCode=' + uni.getStorageSync('merchantCode')
        });
        if (res) {
          console.log('🚀 ~ getCountComponent ~ res:', res);
          // this.countComponent = res.data;
          this.hasAuth = res.data;
          console.log('🚀 ~ getCountdownContracts ~ this.hasAuth:', this.hasAuth);
        }
      },

      createContract() {
        if (this.rzSimple === 0) {
          uni.showToast({
            title: '请先实名认证!',
            duration: 2000,
            icon: 'error'
          });
        } else {
          uni.navigateTo({
            url: '/signature/contract/createContract?authType=' + this.authType
          });
        }
      },
      createDetail(status) {
        uni.navigateTo({
          url: '/signature/contract/contractDetail?status=' + status
        });
      },
      createlist() {
        if (this.wzfPay) {
          uni.showModal({
            title: '提示',
            content: '您当前有一笔待支付的订单，请在订购记录内查看并支付',
            success: function (res) {
              if (res.confirm) {
                console.log('用户点击确定');
              } else if (res.cancel) {
                console.log('用户点击取消');
              }
            }
          });
        } else {
          uni.navigateTo({
            url: '/signature/contract/confirmOrder'
          });
        }
      },
      createOrder() {
        uni.navigateTo({
          url: '/signature/contract/orderRecord'
        });
      },
      goAuthentication(status) {
        if (status == '2') {
          if (this.rzSimpleInfo) {
            uni.navigateTo({
              url: '/signature/contract/cManageCheck'
            });
          } else {
            uni.navigateTo({
              url: '/signature/contract/qyCertification?disabled=' + status
            });
          }
        } else {
          uni.navigateTo({
            url: '/signature/contract/qyCertification?disabled=' + status
          });
        }
      }
    }
  };
</script>

<style scoped>
  .content {
    padding: 32rpx;
  }

  .list_view {
    width: 686rpx;
    border-radius: 24rpx;
    padding: 32rpx 24rpx;
    background-color: #ffffff;
    box-sizing: border-box;
    margin-bottom: 32rpx;
    letter-spacing: 3rpx;
  }

  .list_head {
    color: #333333;
  }

  .list_context {
    color: #555555;
    margin-top: 36rpx;
  }

  .lc_yellow {
    color: #fd9b2a;
    text-align: center;
    width: 116rpx;
    border: 1px solid #ffe1be;
    background-color: #fdf6ed;
    border-radius: 8rpx;
    margin-right: 16rpx;
  }

  .lc_green {
    color: #006f57;
  }

  .lc_g {
    color: #81e2af;
    border: 1px solid #81e2af;
    background-color: #ecfbf3;
  }

  .list_bot {
    margin-top: 62rpx;
    display: flex;
    flex-direction: row-reverse;
  }

  .btn_b {
    width: 196rpx;
    height: 60rpx;
    border-radius: 60rpx;
    line-height: 60rpx;
    text-align: center;
    margin-left: 32rpx;
  }

  .b_l {
    background-color: #fff;
    color: #4e9f87;
    border: 1px solid #7baea0;
    margin-left: 32rpx;
  }

  .b_r {
    background-image: linear-gradient(to bottom, #88cfba, #1d755c);
    color: #ffffff;
  }

  .bg_green {
    width: 346rpx;
    height: 52rpx;
    line-height: 52rpx;
    color: #439582;
    background-color: #f8fff7;
    text-align: center;
  }
</style>
