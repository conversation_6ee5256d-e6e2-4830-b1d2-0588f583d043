<template>
	<page-meta :page-style="'overflow:'+(show?'hidden':'visible')"></page-meta>
	<view class="ranking_content_css" >
		<view class="ranking_record pt-20">
			<!-- <span class="f-24 c-ff" @click="receiveRecord">领取记录</span> -->
		</view>
		<view class="ranking_content_text mt-10 c-ff">
			<view class="title f-56 lh-72">日排行榜</view>
			<!-- <view class="f-28 lh-40 mt-8">领取今日奖励 20 积分</view> -->
			<view class="flex-a-c flex-x-s f-24 mt-16 lh-36 ranking_bottom" @click="reward()">
				<view class="mr-12 lh-30">奖励说明</view>
				<u-icon name="arrow-right" color="#fff" size="24"></u-icon>
			<!-- arrow-right -->
			</view>
		</view>
		<view class="ranking_main_css bg-ff mt-40">
			<view class="ranking_main_top">
				<view class="ranking_top_content flex-a-c flex-x-s">
					<view class="title f-28 lh-40">当前排名</view>
					<image class="header_image_css radius-all" :src="headers"></image>
					<view class="f-28 lh-40 user_name_css ml-25">{{ encryptionName(rankInfo.userName) }}</view>
					<view class="title_icon bold">当前排名 {{ rankInfo.ranking||'-' }}</view>
					<image v-if="rankInfo.upOrDown==1" class="ranking_top ml-15" src="https://document.dxznjy.com/course/689f9354bf9945e7876d48b4d94eee39.png"></image>
					<image v-else-if="rankInfo.upOrDown==-1" class="ranking_top ranKing_down ml-15" src="https://document.dxznjy.com/course/689f9354bf9945e7876d48b4d94eee39.png"></image>
				</view>
			</view>
			<view>
				<view class="flex-a-c flex-x-s lh-100 plr-32">
					<view class="table_one">排名</view>
					<view class="table_two">姓名</view>
					<view class="table_three">成长值</view>
				</view>
				<view class="table_item_css">
					<view v-if="memberList.length>0">
						<view  v-for="(item,index) in memberList" :key="index" class="plr-32 table_item">
							<view  class="flex-a-c flex-x-s">
								<view class="table_one table_tb">
									<image class="ranking_image" v-if="index==0" src="https://document.dxznjy.com/course/85dd5dec04c94ae2b73cb968993fd4b1.png"></image>
									<image class="ranking_image" v-else-if="index==1" src="https://document.dxznjy.com/course/ffc53e1c40324ebd9009bba29250dde7.png"></image>
									<image class="ranking_image" v-else-if="index==2" src="https://document.dxznjy.com/course/bcda04b0e58a4edbaa4abf17079bb8c2.png"></image>
									<span v-else class="pl-12">{{index+1}}</span>
								</view>
								<view class="table_two  table_tb">
									<image class="image_tab_style radius-all" :src="item.photo?item.photo:headerAvaUrl"></image>
									<span class="name_style c-55 f-28">{{item.userName}}</span>
								</view>
								<view class="table_three table_tb">{{item.growthValue}}</view>
							</view>
						</view>
					</view>
					<view class="pt-30" v-if="memberList.length==0">
						<emptyPage></emptyPage>
					</view>
				</view>
			</view>
		</view>
		<growthPopup ref="growthPopupRefs"></growthPopup>
		<uni-popup  ref="reward_popup" type="center" style="padding: 0;" @change="change">
			<view class="cost_content_css lh-32">
				<view class="f-22 plr-15 cost_content_style">
					<view class="f-24 cost_content_text">成长值活动规则</view>
					<view class="mt-15">欢迎各位家长会员参与我们的“文化中心”成长值排行榜挑战！本活动旨在激励大家积极分享学习成果、增进互动交流，共同营造一个活力满满的会员文化社区。以下是活动的具体规则：
					活动时间</view>
					•活动周期：每月1日至月末最后一天。
					<view>•排行榜更新：每日23时：59分：59秒更新当日成长值排行榜。
					活动对象</view>
					•所有鼎校甄选会员用户自动参与。
					<view>排行榜分类</view>
					1、日排行榜：每日根据用户当日获得的成长值进行排名。
					成长值获取方式<br/>
					1、会员用户每日进入文化中心板块+10点成长值<br/>
					2、会员用户每完成一节家庭教育录播课时学习，+30点成长值<br/>
					3、会员用户每上传一次文化记录，+10点成长值。上限40点/日<br/>
					4、会员用户每日点赞一篇动态，+5点成长值。上限10点/日<br/>
					5、会员用户每日评论一篇动态，+5点成长值。上限10点/日<br/>
					6、会员用户每日发布一个提问，+5点成长值。上限10点/日<br/>
					7、会员用户每日回答一个提问，+5点成长值。上限10点/日
					<view>其他注意事项</view>
					•禁止任何作弊行为，如刷赞、水评论等，一经发现取消排名<br/>
					•如果榜单成长值相同，排名按照最先达到该成长值时间进行排名。<br/>
					•活动最终解释权归鼎校甄选官方所有。<br/>
					•如有疑问，可联系在线客服咨询。<br/>
					让我们在文化的海洋里遨游，携手攀登巅峰，赢取属于你的荣耀吧！
				</view>
				<view class="close_circle" @click="$refs.reward_popup.close()">
					<u-icon name="close-circle" color="#b1b1b1" size="38"></u-icon>
				</view>
			</view>
		</uni-popup>
	</view>
</template>

<script>
const {
		$navigationTo,
		$getSceneData,
		$showError,
		$http
	} = require("@/util/methods.js")
	import growthPopup from './components/growthPopup.vue'
	import emptyPage from "./components/emptyPage.vue"
	export default {
		components:{growthPopup,emptyPage},
		data() {
			return {
				headers:uni.getStorageSync('headPortrait')?uni.getStorageSync('headPortrait'):'https://document.dxznjy.com/dxSelect/home_avaUrl.png',
				headerAvaUrl:'https://document.dxznjy.com/dxSelect/home_avaUrl.png',
				nickName:uni.getStorageSync('nickName'),
				// voicePath: 'wxfile://tmp_9c8899bca9aa6c19e3de6e1703a3d40c.m4a'
				memberList:[],
				identityType:uni.getStorageSync('identityType'),
				rankInfo:{},
				show:false,
			}
		},
		onLoad() {
			this.getWapRank()
		},
		methods: {
			// /
			async getRankList(){
				// page = page || 1;
			    let _this = this
			    const res = await $http({
			    	url: 'zx/wap/rank/list',
					showLoading:true,
			    	data: {}
			    })
				if(res){
					this.memberList=res.data||[]
					// encryptionName
					this.memberList.forEach(item=>{
						item.userName=this.encryptionName(item.userName)
					})
				}
			},
			encryptionName(name){
				console.log(name)
				console.log('.........................................')
				if(name){
					if(name.length==1){
						return name+'*'
					}else if(name.length==2){
						const ary = name.split('');		// 转化为数组
					   ary.splice(1, 0, '*');	// 使用数组方法插入字符串
					   return ary.join('')
					}else{
						return name.slice(0,1)+'*'+name.charAt(name.length - 1)
					}
				}
			},
			change(e) {
				this.show = e.show
			},
			async getWapRank(){
			    let _this = this
			    const res = await $http({
			    	url: 'zx/wap/rank',
			    	data: {
						userId:uni.getStorageSync('user_id')?uni.getStorageSync('user_id'):'',
					}
			    })
				if(res){
					this.rankInfo=res.data||{}
					if(!this.rankInfo.userName){
						this.rankInfo.userName=this.nickName
					}
					this.getRankList()
					// if(this.rankInfo.upOrDown){
					// 	this.getGrowthValue('RANKING_INCREASE')
					// }
				}
			},
			//将来说明
			reward(){
				this.$refs.reward_popup.open()
			},
			async getGrowthValue(text){
				if(this.identityType!=4){
					return
				}
				const res = await $http({
					url: 'zx/wap/CultureCircle/getGrowthValue?eventType='+text+'&userId='+uni.getStorageSync('user_id'),
					method: 'POST',
					data: {}
				})
				if(res){
					if(Number(res.data)){
						this.$refs.growthPopupRefs.open(res.data)
					}
				}
			},
			receiveRecord(){
				uni.navigateTo({
					url: '/memberCenter/detail/rankingRecord'
				})
			}
		}
	}

</script>

<style lang="scss" scoped>
	.ranking_content_css{
		background: url('https://document.dxznjy.com/course/a79760c439d34a66aa46b80aae2a38c5.png') no-repeat;
		background-size: 100%;
		height: 100vh;
		.ranking_record{
			text-align: right;
			padding-right: 32rpx;
			span{
				display: inline-block;
				width: 128rpx;
				height: 48rpx;
				background: rgba(11,165,130,0.74);
				border-radius: 24rpx;
				text-align: center;
				line-height: 48rpx;
			}
		}
		.ranking_content_text{
			padding-left: 68rpx;
			.title{
				font-weight: WenYiHei;
			}
			.ranking_bottom{
				opacity: 0.8;
			}
		}
		.table_item_css{
			min-height: calc(100vh - 430rpx);
		}
		.ranking_main_css{
			border-radius: 40rpx 40rpx 0rpx 0rpx;
			.ranking_main_top{
				padding:32rpx;
				.ranking_top_content{
					width: 686rpx;
					padding:22rpx 0;
					background: #FFFFFF;
					box-shadow: 0rpx 4rpx 8rpx 2rpx rgba(238,235,235,0.46);
					border-radius: 16rpx;
					.ranking_top{
						width: 30rpx;
						height: 44rpx;
					}
					.ranKing_down{
						transform: rotateX(180deg);
					}
					.title{
						width: 56rpx;
						color:#04614B;
						margin-left: 24rpx;
					}
					.title_icon{
						color:#04614B;
						margin-left: 24rpx;
					}
				} 
				.header_image_css{
					width: 96rpx;
					height: 96rpx;
					margin-left: 24rpx;
				}
				.user_name_css{
					width: 220rpx;
				}
			}
		}
		.image_tab_style{
			width: 56rpx;
			height: 56rpx;
			vertical-align: middle;
		}
		.name_style{
			vertical-align: middle;
			display: inline-block;
			margin-left: 16rpx;
		}
		.table_one{
			width: 136rpx;
			color:#979595;
			height: 112rpx;
		}
		.table_two{
			width: 435rpx;
			color:#979595;
			height: 112rpx;
		}
		.table_three{
			color:#979595;
			height: 112rpx;
		}
		.table_item{
			height: 112rpx;
			line-height: 112rpx;
			.ranking_image{
				width: 58rpx;
				height: 64rpx;
				margin-top: 24rpx;
			}
		}
		.table_item:first-child{
			background: linear-gradient( 93deg, rgba(252,222,105,0.1) 0%, rgba(255,255,255,0) 100%);
		}
		.table_item:nth-child(2){background: linear-gradient( 93deg, rgba(255,226,229,0.46) 0%, rgba(255,255,255,0) 100%);}
		.table_item:nth-child(3){background: linear-gradient( 93deg, rgba(225,239,250,0.43) 0%, rgba(255,255,255,0) 100%);}
	}
	.cost_content_css{
		width: 686rpx;
		height: 980rpx;
		background:url('https://document.dxznjy.com/course/f12fb50e68cc4cf5b9c433a9e968d4e7.png') no-repeat;
		background-size: 100%;
		padding-top:290rpx;
		position: relative;
		.close_circle{
			position: absolute;
			right:20rpx;
			top:0;
		}
		.cost_content_style{
			background: #fff;
			border-radius: 38rpx;
			padding-bottom: 20rpx;
			.cost_content_text{
				text-align: center;
			}
		}
	}
</style>
