<!-- 超人码进货处理中 -->
<template>
  <view class="plr-30">
    <view class="bg-ff plr-30 pb-40 radius-15 positionRelative" :style="{ height: useHeight + 'rpx' }">
      <view class="ptb-30 c-00 f-30">
        <view class="mb-12 flex-s">
          <view class="f-30 mb-10">进货{{ orderInfo.codeNum }}个超人码</view>
          <view class="flex-s f-28 c-33">{{ orderInfo.createdTime }}</view>
          <!-- <view class="classPayStatus_btn" style="background-color: #E57126;" >等待{{orderInfo.outMerchantName}}处理中</view> -->
        </view>
        <!-- <view class="flex-s f-28 c-33">{{orderInfo.createdTime}}</view> -->
        <view style="color: #e57126">等待{{ orderInfo.outMerchantName }}处理中...</view>
      </view>

      <view class="flex-s positionAbsolute" :style="{ height: svHeight + 'px' }">
        <view class="common_btn common_btn_orange" @click="openPopup">取消</view>
        <view class="common_btn common_btn_orange_active" @click="contact">
          <image :src="imgHost + 'dxSelect/phone_icon2.png'" class="img_s"></image>
          联系他
        </view>
      </view>
    </view>

    <!-- 取消进货 -->
    <uni-popup ref="contactPopup" type="center" @change="changePopup">
      <view class="dialogBG">
        <view class="reviewCard_box positionRelative">
          <view class="cartoom_image">
            <image :src="dialog_iconUrl" mode="widthFix"></image>
          </view>
          <view class="reviewCard">
            <view class="reviewTitles">是否确定取消进货？</view>
            <view class="flex-s mt-55">
              <view class="review_btn" @click="cancel">确定</view>
              <view class="close_btn" @click="closeDialog">取消</view>
            </view>
          </view>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script>
  import Util from '@/util/util.js';
  const Supermanjs = require('@/common/superman.js');

  export default {
    data() {
      return {
        useHeight: 0, //除头部之外高度

        svHeight: 50,
        imgHost: getApp().globalData.imgsomeHost,
        userInfo: {}, //用户信息
        linkPhone: '', //联系号码
        orderInfo: '',
        infoShow: false, // 判断进货、出货

        dialog_iconUrl: Util.getCachedPic('https://document.dxznjy.com/dxSelect/dialog_icon.png', 'dialog_icon_path')
      };
    },

    async onLoad(e) {
      this.orderInfo = JSON.parse(decodeURIComponent(e.orderInfo));
      this.init();
    },
    onReady() {
      let that = this;
      uni.getSystemInfo({
        //调用uni-app接口获取屏幕高度
        success(res) {
          // 可使用窗口高度，将px转换rpx
          let h = res.windowHeight * (750 / res.windowWidth);
          that.useHeight = h - 65;
        }
      });
    },

    onShow() {
      // this.$refs.contactPopup.open();
    },

    methods: {
      openPopup() {
        this.$refs.contactPopup.open();
      },
      //关闭弹窗
      closeDialog() {
        this.$refs.contactPopup.close();
      },

      // 获取上级联系方式
      async init() {
        // this.userInfo = await Supermanjs.getUserInfo();
        this.linkPhone = await Supermanjs.getMerchantPhone(this.orderInfo.outMerchantCode);
      },

      // 取消进货
      async cancel() {
        let isSuccess = await Supermanjs.cancelApplyOutCode(this.orderInfo.outCodeApplyId);
        if (isSuccess) {
          this.$refs.contactPopup.close();
          this.$util.alter('取消进货成功');
          // uni.redirectTo({
          // 	url:'/supermanClub/supermanSign/sign'
          // })
          setTimeout(() => {
            uni.navigateBack();
          }, 500);
        }
      },

      // 联系他
      contact() {
        if (this.linkPhone) {
          uni.makePhoneCall({
            phoneNumber: this.linkPhone //仅为示例
          });
        } else {
          this.$util.alter('暂未获取到上级联系方式');
        }
      }
    }
  };
</script>

<style lang="scss" scoped>
  page {
    background-color: #fff;
  }

  .common_btn {
    padding: 0;
    height: 90upx;
    display: flex;
    justify-content: center;
    align-items: center;
    box-sizing: border-box;
    border-radius: 45upx;
    text-align: center;
  }
  .common_btn_orange {
    width: 190upx;
  }
  .common_btn_orange_active {
    width: 410upx;
  }

  .img_s {
    width: 40upx !important;
    height: 38upx !important;
    margin-right: 6upx;
  }

  .positionAbsolute {
    bottom: 40upx;
    width: 630upx;
  }

  // 分享选项弹出层
  .dialogBG {
    width: 100%;
    /* height: 100%; */
  }

  /* 21天结束复习弹窗样式 */
  .reviewCard_box {
    width: 670rpx;
    position: relative;
  }

  .reviewCard_box image {
    width: 100%;
    height: 100%;
  }

  .reviewCard {
    position: relative;
    width: 100%;
    height: 100%;
    background: #ffffff;
    color: #000;
    border-radius: 24upx;
    padding: 50upx 60upx;
    box-sizing: border-box;
  }

  .cartoom_image {
    width: 420rpx;
    position: absolute;
    top: -250rpx;
    left: 145rpx;
    z-index: -1;
  }

  .review_close {
    position: absolute;
    top: 20rpx;
    right: 20rpx;
    z-index: 1;
  }

  .reviewTitles {
    padding: 0 100rpx;
    font-size: 32rpx;
    margin-top: 40rpx;
    margin-bottom: 80rpx;
    text-align: center;
  }

  .review_btn {
    width: 250upx;
    height: 80upx;
    background: linear-gradient(180deg, #88cfba 0%, #1d755c 100%);
    border-radius: 45upx;
    font-size: 30upx;
    color: #ffffff;
    line-height: 80upx;
    justify-content: center;
    text-align: center;
  }

  .close_btn {
    width: 250upx;
    height: 80upx;
    color: #2e896f;
    font-size: 30upx;
    line-height: 80upx;
    text-align: center;
    border-radius: 45upx;
    box-sizing: border-box;
    border: 1px solid #2e896f;
  }

  // 短信
  .content {
    color: #333;
    border-radius: 15rpx;
    padding: 90rpx 20rpx;
    width: 600rpx;
  }

  /deep/.u-icon--right {
    justify-content: center;
  }
</style>
