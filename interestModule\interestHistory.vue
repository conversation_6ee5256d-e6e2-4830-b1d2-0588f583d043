<template>
  <view class="wordContent">
    <view class="userHead" style="background: #18b48e">
      <image class="iconBack" :src="imgHost + 'title_back.png'" @click="gotoBack()" mode=""></image>
      历史记录
    </view>
    <!-- 单词列表 -->
    <view class="wordShowList">
      <view class="showhistory">
        <view class="showhistoryInner">
          <text>时间：202103-19 12:00:00</text>
          <text>时间：202103-19 12:00:00</text>
        </view>
        <view class="historyListBottom">
          <text @click="lookHistory()">查看报告</text>
        </view>
      </view>
      <view class="showhistory">
        <view class="showhistoryInner">
          <text>时间：202103-19 12:00:00</text>
          <text>时间：202103-19 12:00:00</text>
        </view>
        <view class="historyListBottom">
          <text @click="lookHistory()">查看报告</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
  export default {
    data() {
      return {
        imgHost: getApp().globalData.imguseHost
      };
    },
    onLoad() {},
    methods: {
      // 查看报告
      lookHistory() {
        console.log('查看报告');
      },

      //返回按钮
      gotoBack() {
        uni.navigateBack({
          delta: 1
        });
      }
    }
  };
</script>

<style>
  page {
    background: #f9f9f9;
    height: 100vh;
    padding: 0;
  }
  .wordContent {
    height: 100%;
    position: relative;
  }

  .iconBack {
    width: 80rpx;
    height: 68rpx;
  }

  .wordShowList {
    padding: 50rpx;
  }
  .showhistory {
    background: #ffffff;
    border-radius: 18rpx;
    margin-bottom: 58rpx;
    box-shadow: 0rpx 0rpx 20rpx #dddddd;
  }
  .showhistoryInner {
    padding: 50rpx 70rpx 0 70rpx;
  }
  .showhistoryInner text {
    line-height: 80rpx;
    display: block;
    width: 100%;
  }
  .historyListBottom {
    height: 120rpx;
    border-top: 2rpx solid #e4e4e4;
    text-align: center;
  }
  .historyListBottom text {
    display: inline-block;
    height: 56rpx;
    padding: 0 40rpx;
    border-radius: 30rpx;
    line-height: 56rpx;
    margin-top: 32rpx;
    background: #18b48e;
    color: #ffffff;
    font-size: 34rpx;
  }
</style>
