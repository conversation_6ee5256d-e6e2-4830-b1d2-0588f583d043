<template>
  <view class="plr-30 pb-30">
    <view class="list_box mt-30">
      <view v-if="phaseColorListKnow.length > 0">
        <uv-list>
          <uv-list-item>
            <view class="listBorder" v-for="(item, index) in phaseColorListKnow" :key="index" @tap="gotoKnowledge(item)">
              <view class="flex-a-c">
                <text class="p-30">{{ item.knowledgeName }}</text>
                <span class="boxl-50 lh-30 b-g f-18 t-c radius-6" :style="item.phaseColor">{{ item.phase }}</span>
              </view>
              <view class="flex-a-c">
                <u-icon name="arrow-right"></u-icon>
              </view>
            </view>
          </uv-list-item>
        </uv-list>
      </view>
      <view class="bg-ff radius-15 plr-20 mt-30 t-c flex-col" v-else :style="{ height: useHeight + 'rpx' }" style="position: relative">
        <image src="/static/index/<EMAIL>" mode="widthFix" class="mb-20 img_s"></image>
        <view style="color: #bdbdbd">暂无数据</view>
        <image src="/static/index/<EMAIL>" mode="widthFix" class="mb-20 img_s"></image>
      </view>
    </view>
  </view>
</template>
<script>
  export default {
    data() {
      return {
        studentCode: '',
        // 列表数据
        listKnow: [],
        page: {
          total: 1,
          pageSize: 15,
          pageNum: 1
        }
      };
    },

    onLoad(options) {
      // todo studentCode 待传
      this.studentCode = options.studentCode;
      // this.studentCode = '6231217888'
      this.getGraList();
    },
    onShow() {},
    onReachBottom() {
      let allTotal = this.page.pageNum * this.page.pageSize;
      console.log('已加载全部数据');
      if (allTotal < this.page.total) {
        // 当前条数小于总条数，则增加请求页数
        this.page.pageNum++;
        this.getGraList(); // 调用加载数据方法
      } else {
        uni.showToast({
          title: '已加载全部数据',
          icon: 'none'
        });
      }
    },
    computed: {
      // ... 其他计算属性
      phaseColorListKnow() {
        return this.listKnow.map((item) => ({
          ...item,
          phaseColor: this.getPhaseColorClass(item.phase)
        }));
      }
    },
    methods: {
      // 获取语法列表数据
      async getGraList() {
        try {
          let res = await this.$httpUser.get('dyf/wap/applet/todayReview', {
            studentCode: this.studentCode,
            pageNum: this.page.pageNum,
            pageSize: this.page.pageSize
          });
          this.page.total = res.data.data.totalItems;
          if (this.page.pageNum === 1) {
            // 如果是第一页，清空列表并添加新数据
            this.listKnow = res.data.data.data;
          } else {
            // 否则追加新数据
            this.listKnow = [...this.listKnow, ...res.data.data.data];
          }
        } catch (error) {
          console.error('获取数据失败:', error);
        }
      },
      // 定义一个方法来根据阶段值返回颜色类名
      getPhaseColorClass(phase) {
        console.log('phase:', phase);
        if (phase.includes('小学')) {
          return 'color: #81e2af;'; // 绿色
        } else if (phase.includes('初中')) {
          return 'color: #ffd593;'; // 橘色
        } else if (phase.includes('高中')) {
          return 'color: #6de2ff;'; // 蓝色
        }
        return ''; // 默认颜色
      },
      // 复习语法跳转到讲义
      gotoKnowledge(item) {
        // console.log(item, '复习语法跳转到讲义')
        if (!uni.getStorageSync('token')) {
          this.goToLogin();
          return;
        }
        // console.log(item, 'redirectTo复习语法跳转到讲义')
        uni.redirectTo({
          url: `/antiAmnesia/review/handout?title=${item.knowledgeName}&studentCode=${this.studentCode}&id=${item.id}`
        });
      }
    }
  };
</script>

<style>
  .list_box {
    border-radius: 14upx;
    background: #ffffff;
    box-shadow: 0rpx 4rpx 16rpx 0rpx #f5f7f9;
    padding: 30upx;
    font-size: 26upx;
    color: rgba(102, 102, 102, 1);
    position: relative;
  }

  .pick {
    flex: 1;
    height: 60upx;
    line-height: 60upx;
    border: 1px solid #c8c8c8;
    border-radius: 20upx;
    text-align: center;
    padding: 0 30upx;
    position: relative;
  }

  /* 弹层宽度 */
  .uv-dp__container {
    height: 150rpx;
    width: 690rpx;
    margin-left: 30rpx;
  }

  button {
    width: 40% !important;
  }

  .listBorder {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-flow: row wrap;
    border-bottom: 2rpx dashed #efefef;
  }

  .badge {
    width: 50rpx;
    height: 50rpx;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 50%;
    background-color: #edeced;
    font-size: 18rpx;
  }
</style>
