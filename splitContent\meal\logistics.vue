<template>
	<view>
		<view class="ptb-30 mlr-30 bg-ff radius-20" :style="{height:useHeight +'rpx'}">
			<view class="plr-30 b-b f-32 pb-30">
				<view>收货地址</view>
				<view class="color_grey66 mt-20" style="line-height: 45upx;">
					{{logisticsList.buyerName}} {{logisticsList.buyerPhone}} {{logisticsList.address}}
				</view>
			</view>
			<view class="plr-30 mt-30 f-32">
				<view class="">物流公司：{{logisticsList.expressCompany}}</view>
				<view class="mt-20">物流单号：{{logisticsList.expressOrderNo}}</view>
			</view>
		</view>
	</view>
</template>

<script>
	const {
	    $navigationTo,
	    $http,
	} = require("@/util/methods.js")
	export default {
		data() {
			return {
				orderId: '',
				logisticsList:{}, // 物流信息
				useHeight:0
			};
		},
		mounted() {

		},
		onReady() {
			uni.getSystemInfo({
				success: (res) => {
					// 可使用窗口高度，将px转换rpx
					let h = (res.windowHeight * (750 / res.windowWidth));
					this.useHeight = h - 80;
				}
			})
		},
		onLoad(e){
			this.orderId = e.orderId;
		},
		onShow(){
			this.getLogistics()
		},
		methods: {
			// 物流信息
			async getLogistics() {
				let _this = this
				const res = await $http({
					url: 'zx/order/mealOrderExpressInfo',
					data: {
						orderMealId:_this.orderId
					}
				})
				if (res) {
					_this.logisticsList=res.data
				}
			}
		},
	};
</script>


<style>
</style>
