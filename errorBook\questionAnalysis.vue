<template>
  <view class="expandContent pl-25 pr-25 pt-20">
    <view class="bg-h">
      <view class="positioning" @click="goback">
        <uni-icons type="left" size="24" color="#000"></uni-icons>
      </view>
      <view class="word-position t-c col-12">
        <view class="f-34">举一反三</view>
      </view>
    </view>
    <viewQuestion v-if="analysisData" :item="analysisData" :index="0" :status="2"></viewQuestion>
    <view class="question-btn" @click="open">
      <view class="bg-ff radius-35 t-c btn-setting flex-c" style="width: 196rpx; height: 69rpx" @click="goStudyBut">
        <image src="https://document.dxznjy.com/dxSelect/4ad0599e-9340-4287-81f0-339d8f6f6f91.png" mode="" style="width: 132rpx; height: 40rpx"></image>
      </view>
    </view>
    <!-- 视频弹框区域 -->
    <u-popup :closeOnClickOverlay="false" :show="show" :round="10" mode="bottom" class="knowledgePointVideo" @close="close" @change="changePopup">
      <view class="flex-c" style="padding: 32rpx 30rpx 36rpx; position: relative">
        <text class="f-34 c-33 knowledgePointVideoText">知识点视频</text>
        <view style="position: absolute; right: 38rpx; top: 38rpx" @click="close">
          <u-icon name="close" size="20" color="#333"></u-icon>
        </view>
      </view>
      <view class="ml-30 mr-30" style="display: inline-block; height: 1rpx; background: rgba(153, 153, 153, 0.3)"></view>
      <scroll-view
        :scroll-top="0"
        scroll-y="true"
        style="height: 600rpx"
        @touchmove.stop
        @scrolltolower="onVideoScrollToLower"
        refresher-enabled
        :refresher-triggered="isRefreshing"
        @refresherrefresh="onVideoRefresh"
      >
        <view class="video-row flex-c f-32 c-33 bg-ff" v-for="(item, index) in videoList" :key="index" @click="goVideoPlay(index)">
          <text class="video-title" :style="{ color: item.isUnlock ? '#000' : '#666' }">{{ item.videoName }}</text>
          <view class="video-play-btn flex-c">
            <image src="https://document.dxznjy.com/dxSelect/df8d8077-0516-494c-82a1-76e282564cb0.png" mode="" style="width: 48rpx; height: 48rpx"></image>
          </view>
        </view>
        <view v-if="!hasMoreVideo" class="no-more">没有更多数据了</view>
      </scroll-view>
    </u-popup>
  </view>
</template>

<script>
  import viewQuestion from '@/errorBook/components/viewQuestion.vue';
  export default {
    data() {
      return {
        show: false,
        rollShow: false,
        id: '',
        analysisData: {},
        page: {
          pageSize: 10,
          pageNum: 1
        },
        videoList: [],
        // 视频列表相关
        hasMoreVideo: true,
        isRefreshing: false,
        videoPageNum: 1,
        videoPageSize: 10,
        studentCode: ''
      };
    },
    onLoad(options) {
      this.id = options.id;
      this.studentCode = options.studentCode;
      this.getAnalysisData(this.id);
    },
    components: {
      viewQuestion
    },
    methods: {
      //   去学习
      goStudyBut() {
        this.show = true;
        this.videoPageNum = 1;
        this.hasMoreVideo = true;
        this.videoList = []; // 清空之前的视频列表
        // uni.showLoading({ title: '加载中' });
        this.$httpUser
          .get('dyf/math/wap/correctionNoteBook/videoPage', {
            answerQuestionId: this.id,
            studentCode: this.studentCode,
            pageNum: this.videoPageNum,
            pageSize: this.videoPageSize
          })
          .then((res) => {
            uni.hideLoading();
            const newVideos = res.data.data.data || [];
            if (newVideos.length > 0) {
              this.videoList = newVideos;
              this.hasMoreVideo = newVideos.length >= this.videoPageSize;
            } else {
              this.hasMoreVideo = false;
            }
          })
          .catch((err) => {
            uni.showToast({ title: '加载失败', icon: 'none' });
            uni.hideLoading();
          });
      },
      goVideoPlay(index) {
        if (this.videoList[index].isUnlock == 0) {
          uni.showToast({
            title: '视频暂未解锁',
            icon: 'none'
          });
        } else {
          uni.navigateTo({
            url: `/knowledgeGraph/knowVideoPlay?type=1&index=${index}&videoInfo=` + JSON.stringify(this.videoList)
          });
        }
      },
      getAnalysisData(id) {
        this.$httpUser.get('dyf/math/wap/correctionNoteBook/getInfo?id=' + id).then((res) => {
          if (res.data.success) {
            this.analysisData = res.data.data;
          } else {
            this.analysisData = [];
          }
        });
      },
      // 禁止滚动穿透
      changePopup(e) {
        this.rollShow = e.show;
      },
      open() {
        this.show = true;
      },
      close() {
        this.show = false;
      },
      // 视频列表下拉刷新
      onVideoRefresh() {
        this.isRefreshing = true;
        this.videoPageNum = 1;
        this.hasMoreVideo = true;
        this.getVideoList();
      },
      // 视频列表滚动到底部
      onVideoScrollToLower() {
        if (this.hasMoreVideo) {
          this.videoPageNum++;
          this.getVideoList(true);
        }
      },
      // 获取视频列表
      getVideoList(loadMore = false) {
        if (!this.hasMoreVideo) return;

        this.$httpUser
          .get('dyf/math/wap/correctionNoteBook/videoPage', {
            answerQuestionId: this.id,
            studentCode: this.studentCode,
            pageNum: this.videoPageNum,
            pageSize: this.videoPageSize
          })
          .then((res) => {
            const newVideos = res.data.data.data || [];
            if (newVideos.length > 0) {
              this.videoList = loadMore ? [...this.videoList, ...newVideos] : newVideos;
            } else {
              this.hasMoreVideo = false;
            }
            this.isRefreshing = false;
          })
          .catch((err) => {
            uni.showToast({ title: '加载失败', icon: 'none' });
            this.isRefreshing = false;
          });
      },
      goback() {
        uni.navigateBack();
      }
    }
  };
</script>

<style>
  .positioning {
    position: fixed;
    top: 120rpx;
    left: 30rpx;
    z-index: 101;
  }
  .word-position {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    background-color: #f3f8fc; /* 或你想要的纯色 */
    z-index: 100;
    height: 190rpx; /* 头部高度 */
    line-height: 70rpx;
    padding-top: 110rpx;
    box-sizing: border-box;
    overflow: hidden;
  }
  .expandContent {
    padding-top: 190rpx;
    padding-bottom: 100rpx; /* 按钮高度+间距 */
    /* height: 100vh; */
    box-sizing: border-box;
  }
  .questionArea {
    padding: 0 35rpx 35rpx;
    box-sizing: border-box;
  }
  .question {
    width: 650rpx;
    font-weight: 400;
    font-size: 30rpx;
    color: #333333;
    line-height: 42rpx;
  }
  .questionType {
    display: inline-block;
    width: 78rpx;
    height: 40rpx;
    line-height: 40rpx;
    border-radius: 8rpx;
  }
  .questionType1 {
    color: #dc0000;
    background: rgba(220, 0, 0, 0.15);
    border: 1rpx solid #dc0000;
  }
  .questionType2 {
    color: #ffa416;
    background: rgba(255, 164, 22, 0.15);
    border: 1rpx solid #ffa416;
  }
  .questionType3 {
    color: #00d1ff;
    background: rgba(0, 186, 255, 0.15);
    border: 1rpx solid #00d1ff;
  }
  /deep/ .u-cell__body {
    padding: 22rpx 56rpx !important;
    background: #effbf7;
  }
  /deep/ .u-cell {
    background: #fbfbfb;
  }
  /deep/ .u-cell__title-text {
    font-size: 28rpx !important;
    color: #428a6f !important;
  }
  /deep/.u-collapse-item__content__text {
    background: #effbf6;
    padding: 5rpx 12rpx 29rpx 47rpx !important;
  }
  .option {
    width: 626rpx;
    height: 90rpx;
    line-height: 90rpx;
    border-radius: 20rpx;
    padding-left: 33rpx;
    box-sizing: border-box;
    border: 1rpx solid #efeff0;
  }
  .componentMargin {
    margin-left: -35rpx;
    margin-right: -35rpx;
  }
  .knowledgePointVideo {
    width: 100%;
  }
  .knowledgePointVideo :deep(.u-popup__content__close--top-right) {
    top: 50rpx;
    right: 40rpx;
  }
  .knowledgePointVideoText {
    font-weight: 400;
    line-height: 48rpx;
  }
  .video-row {
    font-weight: 400;
    padding: 24rpx 80rpx 24rpx 53rpx;
    line-height: 45rpx;
    box-sizing: border-box;
  }
  .disabled {
    color: #666;
  }
  .video-title {
    flex: 1;
    word-wrap: break-word;
    word-break: break-all;
    white-space: normal;
  }
  .video-play-btn {
    width: 56rpx;
    height: 56rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #effbf6;
    border-radius: 50%;
    margin-left: 20rpx;
  }
  .question-btn {
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    box-sizing: border-box;
    position: fixed;
    bottom: 10rpx;
    padding-bottom: var(--safe-area-inset-bottom);
    margin-left: -20rpx;
  }
  .btn-setting {
    border: 2rpx solid #428a6f;
    box-sizing: border-box;
  }
  .no-more {
    text-align: center;
    padding: 15px;
    color: #999;
    font-size: 14px;
  }
</style>
