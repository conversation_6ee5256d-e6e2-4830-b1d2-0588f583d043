<template>
  <view class="bg-ff problem_item_css">
    <view class="release_item_css">
      <view class="positionRelative" :style="'width:' + problemStyle.leftImageWidth + ';height:' + problemStyle.leftImageWidth">
        <image
          class="radius-all"
          :style="'width:' + problemStyle.leftImageWidth + ';height:' + problemStyle.leftImageWidth"
          :src="problemInfo.headPhoto ? problemInfo.headPhoto : avaUrl"
        ></image>
        <image class="positionAbsolute wen_icon_css" src="https://document.dxznjy.com/course/b5cf5b0b3f2b493cb1998fe2eca8f823.png"></image>
      </view>
      <view class="ml-15 width_right">
        <view class="f-28 c-33 lh-40">{{ problemInfo.userName }}</view>
        <view class="f-28 c-55 lh-42">{{ problemInfo.content }}</view>
      </view>
    </view>
    <view class="pl-10">
      <view class="answer_height f-28 c-55 lh-42" :class="showFlse && showAnswer ? 'answerAuto' : 'answerHeight'">
        <view v-if="problemInfo.comment" class="item_css pb-30">
          <view class="release_list_css">
            <view class="file_image_css positionRelative">
              <image class="file_image_css radius-all" :src="problemInfo.comment.headPhoto ? problemInfo.comment.headPhoto : avaUrl"></image>
              <image class="positionAbsolute da_icon_css" src="https://document.dxznjy.com/course/3fbe7257487d4fdc9be292d520e0e0c3.png"></image>
            </view>
            <view class="ml-20 da_content_css">{{ problemInfo.comment.content }}</view>
          </view>
        </view>
        <view v-if="problemInfo.commentVos">
          <view v-for="item in problemInfo.commentVos" :key="item.id" class="item_css pb-30">
            <view class="release_list_css">
              <view class="file_image_css positionRelative">
                <image class="file_image_css radius-all" :src="item.headPhoto ? item.headPhoto : avaUrl"></image>
                <image class="positionAbsolute da_icon_css" src="https://document.dxznjy.com/course/3fbe7257487d4fdc9be292d520e0e0c3.png"></image>
              </view>
              <view v-if="showAnswer" class="ml-20" :class="showFlse ? 'da_content_style' : 'da_content_css'">{{ item.content }}</view>
              <view v-else class="ml-20 da_content_css">{{ item.content }}</view>
            </view>
          </view>
        </view>
        <view v-if="problemType == '1'" class="more_css f-28">
          <view @click="goAnswer">
            <span v-if="showFlse">收起</span>
            <span v-else>
              全部
              <span>{{ problemInfo.comments ? problemInfo.comments : problemInfo.commentVos ? problemInfo.commentVos.length : 0 }}</span>
              个回答
            </span>

            <image class="more_icon_css ml-12" src="https://document.dxznjy.com/course/6bacd7ff3f3547df8870e1e67405cb08.png"></image>
          </view>
          <view class="right_css_style" v-if="problemInfo.comments >= 0">
            <image @click="goAnswer" class="release_image" src="https://document.dxznjy.com/course/f43e60249b8f42b6916693e6fe78d065.png"></image>
            <view class="ml-8 f-24 release_text">{{ problemInfo.comments }}</view>
            <!-- <image class="release_image ml-35" src="https://document.dxznjy.com/course/4eaf055301bc472a949c5b4e15792cf4.png"></image> -->
            <image
              v-if="problemInfo.isThumb"
              @click="getThumb(1)"
              class="release_image ml-35"
              src="https://document.dxznjy.com/course/277db746077545ee980467f2ec7a50db.png"
            ></image>
            <image v-else class="release_image ml-35" @click="getThumb(2)" src="https://document.dxznjy.com/course/4eaf055301bc472a949c5b4e15792cf4.png"></image>
            <view class="ml-8 f-24 release_text">{{ problemInfo.thumbsUp }}</view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>
<script>
  export default {
    props: ['problemInfo', 'problemStyle', 'problemType', 'codeType', 'showAnswer'],
    data() {
      return {
        inputFocus: false,
        keyUpHeight: 0,
        showFlse: false,
        avaUrl: 'https://document.dxznjy.com/dxSelect/home_avaUrl.png'
      };
    },
    onLoad() {},
    onShow() {},
    methods: {
      goAnswer() {
        if (this.showAnswer) {
          this.showFlse = !this.showFlse;
        } else {
          this.$emit('goAnswer', this.problemInfo);
        }
      },
      getThumb(key) {
        this.$emit('addThumb', this.problemInfo, key);
      },
      getInputFocus() {
        this.inputFocus = true;
        uni.onKeyboardHeightChange((res) => {
          this.keyUpHeight = res.height;
          if (res.duration > 0 && res.height == 0) {
            this.inputFocus = false;
          }
        });
      },
      replyInfo() {
        this.inputFocus = false;
      }
    }
  };
</script>

<style lang="scss" scoped>
  .problem_item_css {
    padding-left: 24rpx;
    padding-top: 32rpx;
    border-radius: 16rpx;
    padding-bottom: 20rpx;
  }
  .width_right {
    width: 540rpx;
  }
  .release_item_css {
    display: flex;
    justify-content: flex-start;
    word-break: break-all;
    .wen_icon_css {
      width: 40rpx;
      height: 40rpx;
      bottom: 0;
      right: 0;
    }
  }
  .release_image {
    width: 32rpx;
    height: 32rpx;
  }
  .answerHeight {
    height: 100rpx;
  }
  .answerAuto {
    height: auto;
  }
  .answer_height {
    padding-bottom: 30rpx;
    padding-left: 10rpx;
    margin-top: 24rpx;
    position: relative;
    overflow: hidden;
    .more_css {
      display: flex;
      justify-content: space-between;
      position: absolute;
      bottom: 0;
      left: 0;
      background-color: #fff;
      width: 100%;
      color: #28a781;
      padding-left: 80rpx;
      padding-right: 2rpx;
      width: 545rpx;
      .more_icon_css {
        width: 15rpx;
        height: 20rpx;
      }
    }
  }
  .release_list_css {
    display: flex;
    justify-content: flex-start;
    align-items: flex-start;
    .item_css {
      margin-right: 10rpx;
    }
    .file_image_css {
      width: 62rpx;
      height: 62rpx;
      .da_icon_css {
        width: 40rpx;
        height: 40rpx;
        bottom: 0;
        right: 0;
      }
    }
  }
  .right_css_style {
    display: flex;
    justify-content: flex-start;
  }
  .input_key_css {
    position: absolute;
    left: 0;
    bottom: 500px;
    background-color: #fff;
    width: 750rpx;
    display: flex;
    justify-content: flex-start;
    z-index: 999;
  }
  .da_content_style {
    width: 540rpx;
    word-break: break-all;
  }
  .da_content_css {
    width: 550rpx;
    overflow: hidden;
    text-overflow: ellipsis; /* 超出部分省略号 */
    white-space: nowrap;
  }
</style>
