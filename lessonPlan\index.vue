<template>
  <page-meta :page-style="'overflow:' + (showTime ? 'hidden' : 'visible')"></page-meta>
  <view class="">
    <image
      src="https://document.dxznjy.com/dxSelect/68e3ea2b-9e69-4b12-9b48-4cea40776e53.png"
      mode="widthFix"
      v-if="isInfoFirst == 'true' && isShowInfoFirst"
      @click="updateFirst"
      style="width: 100vw; height: 100vh"
    ></image>
    <view class="plr-32 bg-ff" v-else>
      <view class="watchInfoBox" @click="watchInfo">查看学员试课信息</view>
      <view class="plr-20 pb-40 pt-24 radius-15 positionRelative">
        <form id="#nform">
          <view class="information ptb-35">
            <view style="width: 32%" class="f-30 bold">手机号：</view>
            <view class="phone-input">
              <input type="text" v-model="infolist.mobile" placeholder-style="font-size:24rpx;" name="number" class="input c-00 form-input" disabled="true" />
            </view>
          </view>
          <view class="information ptb-35">
            <view style="width: 32%" class="f-30 bold">姓名学号：</view>
            <view class="phone-input">
              <input
                type="text"
                placeholder-style="font-size:24rpx;"
                name="trialname"
                class="input c-00 form-input"
                v-model="studentNameAndCodeInput"
                @input="handleInput"
                disabled="true"
              />
            </view>
          </view>
          <view class="information ptb-35">
            <view style="width: 32%" class="f-30 bold">性别：</view>
            <view class="phone-input positionRelative">
              <view class="icon_x">
                <uni-icons type="down" size="20" color="#7A7A7A" style="color: #7a7a7a !important"></uni-icons>
              </view>
              <picker mode="selector" class="f-30 lh-40 mt-5 w100 pt-10" @change="chooseGender" :range="range" range-key="text" :value="genderIndex">
                <text :class="{ placehoder: infolist.gender == null, 'c-00': infolist.gender != null }" class="form-input">
                  {{ infolist.gender != null ? range[genderIndex].text : '请选择性别' }}
                </text>
              </picker>
            </view>
          </view>
          <view class="information ptb-35">
            <view style="width: 32%" class="f-30 bold">所属城市：</view>
            <view class="phone-input flex-s pr-20 positionRelative">
              <view class="icon_x">
                <uni-icons type="down" size="20" color="#7A7A7A" style="color: #7a7a7a !important"></uni-icons>
              </view>
              <!-- #ifdef MP-WEIXIN -->
              <picker mode="region" class="f-30 lh-40 mt-5 w100 pt-10" @change="bindRegionChange" style="height: 100%" :value="regionValue">
                <text :class="{ placehoder: !infolist.province, 'c-00': infolist.province }" class="form-input">
                  {{ infolist.province ? infolist.province + infolist.city + infolist.area : '请选择所属城市' }}
                </text>
              </picker>
              <!-- #endif -->
              <!-- #ifdef APP-PLUS -->
              <gb-picker @change="bindRegionChange" @tap="openPicker">
                <text :class="{ placehoder: !infolist.province, 'c-00': infolist.province }" class="form-input">
                  {{ infolist.province ? infolist.province + infolist.city + infolist.area : '请选择所属城市' }}
                </text>
              </gb-picker>
              <!-- #endif -->
            </view>
          </view>
          <view class="information ptb-35">
            <view style="width: 32%" class="f-30 bold">学生年级：</view>
            <view class="phone-input positionRelative">
              <view class="icon_x">
                <uni-icons type="down" size="20" color="#7A7A7A" style="color: #7a7a7a !important"></uni-icons>
              </view>
              <picker mode="selector" class="f-30 lh-40 mt-5 w100 pt-10" @change="choiceGrade" :range="gradelist" range-key="label" :value="gradeIndex">
                <text :class="{ placehoder: !infolist.grade, 'c-00': infolist.grade }" class="form-input">
                  {{ infolist.grade ? gradelist[gradeIndex].label : '请选择学生年级' }}
                </text>
              </picker>
            </view>
          </view>
          <view class="information ptb-35">
            <view style="width: 32%" class="f-30 bold">学员类型：</view>
            <view class="phone-input positionRelative">
              <view class="icon_x">
                <uni-icons type="down" size="20" color="#7A7A7A" style="color: #7a7a7a !important"></uni-icons>
              </view>
              <picker mode="selector" class="f-30 lh-40 mt-5 w100 pt-10" @change="choiceType" :range="studengtTypelist" range-key="text" :value="typeIndex">
                <text :class="{ placehoder: !infolist.studentType, 'c-00': infolist.studentType }" class="form-input">
                  {{ infolist.studentType ? studengtTypelist[typeIndex].text : '请选择学员类型' }}
                </text>
              </picker>
            </view>
          </view>
          <view class="information ptb-35 borderB">
            <view style="width: 32%" class="f-30 bold">英语教材版本：</view>
            <view class="phone-input positionRelative">
              <view class="icon_x">
                <uni-icons type="down" size="20" color="#7A7A7A" style="color: #7a7a7a !important"></uni-icons>
              </view>
              <picker mode="selector" class="f-30 lh-40 mt-5 w100 pt-10" @change="choiceCourseEdition" :range="textBookList" range-key="label" :value="textBookIndex">
                <text :class="{ placehoder: !infolist.courseEdition, 'c-00': infolist.courseEdition }" class="form-input">
                  {{ infolist.courseEdition ? infolist.courseEdition : '请选择英语教材版本' }}
                </text>
              </picker>
            </view>
          </view>
          <view class="information ptb-35">
            <view style="width: 32%" class="f-30 bold">语文在校成绩：</view>
            <view class="phone-input">
              <input
                type="number"
                v-model="infolist.chineseScore"
                placeholder-style="font-size:24rpx;"
                name="trialname"
                placeholder="请输入该学员最近3次的语文平均成绩"
                class="input c-00 form-input"
              />
            </view>
          </view>
          <view class="information ptb-35">
            <view style="width: 32%" class="f-30 bold">数学在校成绩：</view>
            <view class="phone-input">
              <input
                type="number"
                v-model="infolist.mathScore"
                placeholder-style="font-size:24rpx;"
                name="trialname"
                placeholder="请输入该学员最近3次数学文平均成绩"
                class="input c-00 form-input"
              />
            </view>
          </view>
          <view class="information ptb-35">
            <view style="width: 32%" class="f-30 bold">英语在校成绩：</view>
            <view class="phone-input">
              <input
                type="number"
                v-model="infolist.englishScore"
                placeholder-style="font-size:24rpx;"
                name="trialname"
                placeholder="请输入该学员最近3次的英语平均成绩"
                class="input c-00 form-input"
              />
            </view>
          </view>
          <view class="ptb-35">
            <view class="f-30 bold label">总目标：</view>
            <view class="f-28 lh-40 pt-15 pb-45 textarea_style_css content">
              <textarea
                v-model="infolist.goalDesc"
                maxlength="200"
                placeholder="请输入总目标规划"
                height="140rpx"
                border="none"
                class="textarea-real"
                @focus="onTextareaFocus"
              ></textarea>
              <view class="textarem_number f-24 c-55">{{ infolist.goalDesc.length ? infolist.goalDesc.length : 0 }}/200</view>
            </view>
          </view>
          <view class="watchInfoBox mb-100" @click="watchSuggest">总目标规划说明</view>
          <view class="tips" :style="{ height: svHeight + 'px' }">
            <button class="phone-btn mb-20" @click="goToCreate">规划阶段课程</button>
          </view>
        </form>
      </view>
      <!-- 试课信息查看弹窗-->
      <uni-popup ref="infoPopup" type="bottom" :is-mask-click="false">
        <view class="ptb-20 infoPopup" style="border-radius: 16rpx 16rpx 0 0">
          <view class="infoTitle mtb-20">学员试课信息</view>
          <view class="popupIcon" @click="closeInfo">
            <uni-icons type="clear" size="28" color="#B1B1B1"></uni-icons>
          </view>
          <view class="pl-30">
            <view class="mb-25">姓名：{{ trialCourselist.studentName }}</view>
            <view class="mb-25">年级：{{ trialCourselist.gradeName }}</view>
            <view class="mb-25">词汇测试水平：{{ trialCourselist.vocabularyLevel ? trialCourselist.vocabularyLevel : '未测试' }}</view>
            <view class="mb-25">首测词汇量：{{ trialCourselist.expWords ? trialCourselist.expWords : '未测试' }}</view>
            <view class="mb-25">体验词库：{{ trialCourselist.studyBooks }}</view>
            <view class="mb-25">记忆情况：{{ trialCourselist.memoryTime }}分钟{{ trialCourselist.memoryNum }}个单词</view>
            <view class="mb-65">抗遗忘正确率：{{ trialCourselist.resistForgetAcc ? trialCourselist.resistForgetAcc : '未复习' }}</view>
          </view>
        </view>
      </uni-popup>
      <!-- 规划说明建议弹窗-->
      <uni-popup ref="suggestPopup" type="bottom" :is-mask-click="false">
        <view class="ptb-20 infoPopup syggestBack">
          <view class="infoTitle mtb-30">总目标规划说明建议</view>
          <view class="popupIcon" @click="closeSuggest">
            <uni-icons type="clear" size="28" color="#B1B1B1"></uni-icons>
          </view>
          <view class="plr-50 suggestText">
            <view class="mb-25">
              <text style="color: #339378">1.学员无明确目标：</text>
              请先了解学员能力，依据检测的词汇量水平、体验课词汇学习效率、结合在校英语成绩和当下规划师对学员意向考段的剩余学习时间等维度，为学员设定总目标。
            </view>
            <view class="mb-25">
              <text style="color: #339378">2.学员有明确目标：</text>
              直接填写即可。
            </view>
            <view class="mb-25" style="color: #ea6031">3.为防止后续问题，请避免使用“*天/*课时提升*分/通关/必须通过”等绝对用语。</view>
          </view>
        </view>
      </uni-popup>
    </view>
    <!-- 省市区选择 province city area初始省市区设置 show:是否显示  @sureSelectArea：确认事件 @hideShow：隐藏事件-->
    <cc-selectDity
      :province="infolist.province"
      :city="infolist.city"
      :area="infolist.area"
      :show="showLocal"
      @changeClick="changeClick"
      @sureSelectArea="onsetCity"
      @hideShow="onhideShow"
    ></cc-selectDity>
  </view>
</template>

<script>
  const { $showError, $http } = require('@/util/methods.js');

  const SENSITIVE_WORDS = [
    '保证',
    '提分',
    '通关',
    '必过',
    '第一',
    '满分',
    '万能',
    '独家',
    '奇迹',
    '顶尖',
    '铁定',
    '肯定',
    '速成',
    '国家级',
    '专家',
    '名师',
    '学霸',
    '领先',
    '绝无仅有',
    '空前绝后',
    '提高成绩',
    '提升分数',
    '高分',
    '抢分',
    '应试',
    '押题',
    '幼升小',
    '小升初',
    '中考',
    '高考',
    '升学率',
    '录取率',
    '真题',
    '状元',
    '超前学习',
    '领跑新学期',
    '超越学校课程',
    '保证提分',
    '一次性通过',
    '短期突破高分',
    '包过',
    '保过',
    '班级排名',
    '年级排名',
    '学校排名',
    '尖子生',
    '名校毕业教师',
    '命题人',
    '阅卷人',
    '原价',
    '现价',
    '限时优惠',
    '分期付款',
    '贷款',
    '独家教材',
    '国家级奖项',
    '100%通过率',
    '再不努力就晚了',
    '落后于同龄人',
    '新政影响',
    '学员成功案例',
    '状元学员分享',
    '最佳',
    '顶级',
    '唯一'
  ];
  export default {
    data() {
      return {
        range: [
          {
            value: 0,
            text: '男'
          },
          {
            value: 1,
            text: '女'
          }
        ],
        genderIndex: -1,
        gradelist: [],
        gradeIndex: -1,
        studengtTypelist: [
          { value: 1, text: '普通学员' },
          { value: 2, text: '两三学员' },
          { value: 3, text: 'PET学员' },
          { value: 4, text: 'KET学员' },
          { value: 5, text: 'FCE学员' },
          { value: 6, text: '雅思学员' },
          { value: 7, text: '托福学员' }
        ],
        typeIndex: -1,
        infolist: { goalDesc: '' }, //基本信息
        trialCourselist: {},
        textBookList: [],
        textBookIndex: -1,
        list: '',
        useHeight: 0, //除头部之外高度
        svHeight: 37,
        showTime: false,
        flag: false, // 防止重复点击
        showSuggest: false,
        region: [],
        numberReg: /^[1-9]\d*$/, // 大于0的正整数
        sensitiveReg: new RegExp(`(${SENSITIVE_WORDS.join('|')})`, 'gi'), // 敏感词正则
        textareaStyle: {
          background: ' #f3f8fc',
          height: '140rpx',
          border: '2rpx solid #e5e5e5',
          'margin-top': '27rpx',
          'border-radius': '16rpx'
        },
        studentCode: '',
        curriculumId: '',
        navigateList: {},
        studentNameAndCodeInput: '',
        isInfoFirst: uni.getStorageSync('isInfoFirst') || 'true',
        isShowInfoFirst: false,
        vocabulary: 0,
        merchantCode: '',
        showLocal: false,
        province: '',
        city: '',
        area: ''
      };
    },

    async onLoad(e) {
      this.studentCode = e.studentCode;
      this.curriculumId = e.curriculumId;
      this.navigateList = JSON.parse(e.infolist);
      this.infolist.studentCode = e.studentCode;
      this.merchantCode = e.merchantCode;
      await this.fetchTrialCourse();
      this.fetchGrade();
      await this.fetchInfo();
      this.fetchReport();
      this.fetchTextbook();
      this.fetchVocabulary();
      this.$refs.infoPopup.open();
    },
    onReady() {
      let that = this;
      uni.getSystemInfo({
        //调用uni-app接口获取屏幕高度
        success(res) {
          // 可使用窗口高度，将px转换rpx
          let h = res.windowHeight * (750 / res.windowWidth);
          that.useHeight = h - 65;
        }
      });
    },

    onShow() {},
    computed: {
      regionValue() {
        if (this.infolist.province && this.infolist.city && this.infolist.area) {
          return [this.infolist.province, this.infolist.city, this.infolist.area];
        }
        return [];
      }
    },
    watch: {
      studentNameAndCodeInput(val) {
        const match = val.match(/^(.+?)（(.+?)）$/);
        if (match) {
          this.infolist.studentName = match[1].trim();
          this.infolist.studentCode = match[2].trim();
        } else {
          this.infolist.studentName = '';
          this.infolist.studentCode = '';
        }
      }
    },
    methods: {
      // 获取词汇量检测
      async fetchVocabulary() {
        let result = await this.$httpUser.get('znyy/course/sel/last/test', {
          studentCode: this.studentCode
        });
        if (result.data.code == 20000) {
          this.vocabulary = result.data.data.wordUpperLimit;
        }
      },
      updateFirst() {
        this.isInfoFirst = 'false';
        uni.setStorageSync('isInfoFirst', 'false');
      },
      // 获取报告词数
      async fetchReport() {
        let result = await this.$httpUser.get('znyy/wap/student-lesson-plan/ai-report-list', {
          studentCode: '',
          merchantCode: this.merchantCode
        });
        if (result.data.code == 20000) {
          uni.setStorageSync('coursePlanLength', result.data.data != null ? result.data.data.length : 0);
        }
      },
      // 获取试课信息
      async fetchTrialCourse() {
        let result = await this.$httpUser.get('znyy/wap/student-lesson-plan/experience-course-info', {
          studentCode: this.studentCode,
          curriculumId: this.curriculumId
        });
        if (result.data.code == 20000) {
          this.trialCourselist = result.data.data;
        }
      },
      // 获取年级
      async fetchGrade() {
        let result = await this.$httpUser.get('znyy/bvstatus/GradeType');
        if (result) {
          this.gradelist = result.data.data;
        }
      },
      // 获取版本信息
      async fetchTextbook() {
        let result = await this.$httpUser.get('znyy/bvstatus/TextbookVersion');
        if (result) {
          this.textBookList = result.data.data;
        }
      },
      // 查询基础信息
      async fetchInfo() {
        let result = await this.$httpUser.get('znyy/wap/student-lesson-plan/student-basic-info/find', {
          studentCode: this.studentCode
        });
        if (result.data.data) {
          this.infolist = result.data.data;
          this.studentNameAndCodeInput = `${this.infolist.studentName}（${this.infolist.studentCode}）`;
          this.gradeIndex = this.navigateList.grade - 1;
          this.typeIndex = this.infolist.studentType - 1;
          this.genderIndex = this.infolist.gender;
        } else {
          this.infolist.mobile = this.navigateList.expPhone;
          this.infolist.studentName = this.navigateList.expName;
          this.studentNameAndCodeInput = `${this.infolist.studentName}（${this.infolist.studentCode}）`;
          this.infolist.province = this.navigateList.province;
          this.infolist.city = this.navigateList.city;
          this.infolist.area = this.navigateList.area;
          this.infolist.grade = this.navigateList.grade;
          this.gradeIndex = this.navigateList.grade - 1;
        }
      },
      handleInput(e) {
        const value = e.detail.value;
        const match = value.match(/^(.+?)\s*\((.+?)\)$/); // 匹配：任意字符(括号内数字)
        if (match) {
          this.infolist.studentName = match[1];
          this.infolist.studentCode = match[2];
        } else {
          this.infolist.studentName = '';
          this.infolist.studentCode = '';
        }
      },
      watchInfo() {
        this.$refs.infoPopup.open();
      },
      closeInfo() {
        this.$refs.infoPopup.close();

        if (this.isInfoFirst == 'true' && !this.isShowInfoFirst) {
          this.isShowInfoFirst = true;
        }
      },
      watchSuggest() {
        this.$refs.suggestPopup.open();
      },
      closeSuggest() {
        this.$refs.suggestPopup.close();
      },
      validateForm() {
        const { mobile, studentName, studentCode, gender, province, city, area, grade, studentType, courseEdition, chineseScore, mathScore, englishScore, goalDesc } =
          this.infolist;
        if (
          !mobile ||
          !(studentName || studentCode) ||
          gender === '' ||
          gender === undefined ||
          gender === null ||
          !(province && city && area) ||
          !grade ||
          studentType === '' ||
          studentType === undefined ||
          studentType === null ||
          !courseEdition ||
          !goalDesc
        ) {
          $showError('请完整填写所有信息');
          return false;
        }
        if (chineseScore != '' && chineseScore != undefined) {
          if (!this.numberReg.test(chineseScore)) {
            $showError('语文成绩需为大于0的正整数');
            return false;
          }
        }
        if (mathScore != '' && mathScore != undefined) {
          if (!this.numberReg.test(mathScore)) {
            $showError('数学成绩需为大于0的正整数');
            return false;
          }
        }
        if (englishScore != '' && englishScore != undefined) {
          if (!this.numberReg.test(englishScore)) {
            $showError('英语成绩需为大于0的正整数');
            return false;
          }
        }
        if (this.filterSensitiveWords()) {
          $showError('总目标包含敏感词，请修改后再提交');
          return false;
        }
        return true;
      },
      filterSensitiveWords() {
        const match = this.infolist.goalDesc.match(this.sensitiveReg);
        if (match) {
          return true; // 包含敏感词
        }
        return false; // 未包含
      },
      async goToCreate() {
        if (!this.validateForm()) return;
        const saveData = { ...this.infolist, ...this.trialCourselist };
        saveData.merchantCode = this.merchantCode;
        saveData.tryLearnTime = this.trialCourselist.dateTime;
        saveData.wordLevel = this.vocabulary;
        saveData.address = saveData.province + saveData.city + saveData.area;
        const res = await $http({
          url: 'znyy/wap/student-lesson-plan/student-basic-info/save',
          method: 'POST',
          data: saveData
        });
        if (res.code == 20000) {
          const planCode = res.data.planCode;
          const memoryTime = this.trialCourselist.memoryTime;
          const memoryNum = this.trialCourselist.memoryNum;
          uni.navigateTo({
            url: `/lessonPlan/createPlan?studentCode=${this.studentCode}&memoryTime=${memoryTime}&memoryNum=${memoryNum}&courseEdition=${this.infolist.courseEdition}&studentName=${this.infolist.studentName}&planCode=${planCode}`
          });
        } else {
          uni.showToast({
            title: res.message,
            icon: 'none'
          });
        }
      },
      openPicker() {
        this.showLocal = true;
      },
      onhideShow() {
        this.showLocal = false;
      },
      // 城市
      bindRegionChange: function (e) {
        this.infolist.province = e.detail.value[0];
        this.infolist.city = e.detail.value[1];
        this.infolist.area = e.detail.value[2];
        this.region = e.detail.value;
      },
      changeClick(value, value2, value3) {
        this.province = value;
        this.city = value2;
        this.area = value3;
        // // 更新 region 的值
        // this.region = [value, value2, value3];
      },
      // 选中省市区
      onsetCity(e) {
        this.infolist.province = this.province;
        this.infolist.city = this.city;
        this.infolist.area = this.area;
        this.showLocal = false;
        // 更新 region 的值
        this.region = [this.province, this.city, this.area];
      },
      //性别下拉框
      chooseGender(e) {
        this.genderIndex = e.detail.value;
        this.infolist.gender = e.detail.value;
      },
      //年级
      choiceGrade(e) {
        this.gradeIndex = e.detail.value;
        this.infolist.grade = this.gradelist[e.detail.value].value;
      },
      choiceType(e) {
        this.typeIndex = e.detail.value;
        this.infolist.studentType = this.studengtTypelist[e.detail.value].value;
      },
      choiceCourseEdition(e) {
        this.textBookIndex = e.detail.value;
        this.infolist.courseEdition = this.textBookList[e.detail.value].label;
      },
      onTextareaFocus() {
        const coursePlanLength = uni.getStorageSync('coursePlanLength');
        if (coursePlanLength < 3 && !this.showSuggest) {
          this.$refs.suggestPopup.open();
          this.showSuggest = true;
        }
      }
    }
  };
</script>

<style lang="scss" scoped>
  page {
    background-color: #fff;
  }
  .watchInfoBox {
    color: #339378;
    text-align: right;
    font-size: 30rpx;
  }

  .information {
    display: flex;
    justify-content: space-between;
    align-items: center;

    /deep/.uni-icons {
      color: #fff !important;
    }
  }
  .information .label {
    // width: 29%;

    flex-shrink: 0;
  }

  .information .content {
    flex: 1;
    margin-left: 20rpx; /* 可根据需要调整 */
    position: relative;
  }
  .infoPopup {
    background-color: #fff;
    position: relative;
    .popupIcon {
      position: absolute;
      top: 35rpx;
      right: 35rpx;
    }
    .suggestText {
      font-size: 28rpx;
      line-height: 46rpx;
    }
  }
  .syggestBack {
    background-image: url('https://document.dxznjy.com/dxSelect/8e1c33bd-0d65-4466-85f0-9c7268d2fc42.png');
    background-size: 100% 100%;
  }

  .infoTitle {
    text-align: center;
    font-weight: bold;
    font-size: 32rpx;
  }
  .phone-input {
    background: #f3f8fc;
    border-radius: 8rpx;
    flex: 1;
    height: 64rpx;
    font-size: 28rpx;
    color: #999;
    padding-left: 30rpx;
  }

  .name-input {
    background: #fff;
    border-radius: 8rpx;
    font-size: 28rpx;
    color: #999;
    height: 70rpx;
    display: flex;
    justify-content: space-between;
    padding: 0 30rpx;
    margin-top: 30rpx;
    align-items: center;
  }

  .uni-list-cell-db {
    background: #fff;
    border-radius: 8rpx;
    width: 100%;
    height: 70rpx;
    font-size: 28rpx;
    color: #999;
    display: flex;
    padding-left: 20rpx;
    align-items: center;
  }

  /deep/.date_color {
    color: #000 !important;
  }

  /deep/.regions {
    color: #999 !important;
    font-size: 30upx;
  }

  .regions-input {
    width: 100%;
    font-size: 30rpx;
    color: #999;
    display: flex;
    align-items: center;
  }

  /deep/.phone-btn {
    width: 586rpx;
    height: 74rpx;
    position: absolute;
    bottom: 40rpx;
    left: 54rpx;
    line-height: 80rpx;
    border-radius: 38rpx;
    font-size: 28rpx;
    color: #fff !important;
    background-color: #339378;
  }

  /deep/.uni-select {
    padding: 0 10rpx 0 0;
    border: 0;
  }

  /deep/.uni-select__input-placeholder {
    font-size: 28rpx;
  }

  /deep/.uni-select--disabled {
    background-color: #fff;
  }

  /deep/.uni-stat__select {
    height: 60rpx !important;
  }

  .borderB {
    border-bottom: 1px solid #efefef;
  }

  /deep/.uni-select__input-placeholder {
    color: #999 !important;
    font-size: 30rpx !important;
  }

  .icon_x {
    position: absolute;
    top: 14rpx;
    right: 18rpx;
    z-index: 1;
  }

  .choose-icon2 {
    width: 35rpx;
    height: 35rpx;
  }

  .time-icon {
    /deep/.u-icon--right {
      position: absolute;
      right: 0;
      top: 20rpx;
    }
  }

  /deep/.u-picker__view {
    height: 600rpx !important;
  }

  .dialogBG {
    margin: 0 20rpx 20rpx 20rpx;
    height: 590rpx;
    background-color: #fff;
    border-radius: 12rpx;
  }

  .top-button {
    margin-top: 20rpx;
    text-align: center;
    height: 80rpx;
    display: flex;
    justify-content: space-evenly;
  }

  .confirm-button {
    width: 210rpx;
    height: 80rpx;
    background-color: #2e896f;
    color: #fff;
    font-size: 32rpx;
    display: flex;
    justify-content: center; /* 文本水平居中对齐 */
    align-items: center; /* 文本垂直居中对齐 */
  }

  .cancel-button {
    width: 210rpx;
    height: 80rpx;
    border: 1px solid #2e896f;
    color: #2e896f;
    font-size: 32rpx;
    display: flex;
    justify-content: center; /* 文本水平居中对齐 */
    align-items: center; /* 文本垂直居中对齐 */
  }

  .borderT {
    border-top: 1px solid #efefef;
  }
  .form-input {
    flex: 1;
    height: 100%;
    font-size: 28rpx;
    border: none;
    background: transparent;
  }
  .placehoder {
    font-size: 24rpx;
    color: #717171;
  }
  .textarea_style_css {
    position: relative;
    flex: 1;
    background-color: #f3f8fc;
    .textarem_number {
      position: absolute;
      bottom: 0;
      right: 20rpx;
    }
  }
  /* 深度穿透组件，让内部元素继承背景色 */
  /deep/ .u-textarea {
    background-color: inherit !important; /* 继承外层背景色 */
    border: none !important;
  }
  /deep/ .u-textarea::placeholder {
    font-size: 24rpx;
  }
  /deep/ .input-placeholder {
    font-size: 24rpx !important;
    color: #717171 !important;
  }

  /* 输入框文字样式请在 textarea-real 里单独设置 */
  .textarea-real {
    font-size: 28rpx;
    color: #000 !important;
    padding-left: 30rpx;
  }
  .orderRemark {
    display: flex;
    justify-content: flex-end;
  }
  /deep/ .u-textarea__field {
    font-size: 28rpx !important;
    color: #000 !important;
    padding-left: 12rpx;
  }
</style>
