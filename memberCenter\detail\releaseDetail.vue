<template>
	<view class="release_detail_main">
		<scroll-view  class="da_content_css plr-32"  v-if="answerInfo.length > 0" @scrolltolower="scrolltolower" :show-scrollbar="false" 
		   bounces  @scroll="scroll"  :scroll-top="scrollTop" :throttle="false" scroll-with-animation scroll-anchoring  scroll-y  enhanced>
			<view>
				<view v-for="(info,index) in answerInfo"  :key="index">
					<view class="flex-x-s flex-self-s da_item_css bg-ff p-24">
						<view class="positionRelative" style="width: 82rpx;height: 82rpx;" >
							<image class="radius-all"  style="width: 82rpx;height: 82rpx;" :src="info.headPhoto?info.headPhoto:avaUrl"></image>
							<!-- <image class="positionAbsolute da_icon_css" src="https://document.dxznjy.com/course/3fbe7257487d4fdc9be292d520e0e0c3.png"></image> -->
						</view>
						<view class="ml-15 release_right_css">
							<view>
								<view class="c-33 f-28 lh-40">
									<view>
										{{info.userName}}
									</view>
									<!-- <view class="right_css_style">
										<image class="release_image ml-35" src="https://document.dxznjy.com/course/4eaf055301bc472a949c5b4e15792cf4.png"></image>
										<view class="ml-8 f-24 release_text">66</view>
									</view> -->
								</view>
								<view class="c-55 f-28 lh-42">
									<view>{{info.content}}</view>
									<view class="flex-a-c flex-x-s">
										<view  style="color:#28A781;"  @click.stop="getFouseItem(info)" >回复</view>
										<view  class="f-24 c-BF lh-30 ml-25">{{info.time}}</view>
									</view>
								</view>
							</view>
							<view  v-if="info.commentVos&&info.commentVos.length>0">
								<view v-if="info.showFalse">
									<view v-for="(item,index) in info.commentVos" :key="index">
										<view class="flex-x-s flex-self-s da_item_css bg-ff p-24">
											<view class="positionRelative" style="width: 82rpx;height: 82rpx;" >
												<image class="radius-all"  style="width: 82rpx;height: 82rpx;" :src="item.headPhoto?item.headPhoto:avaUrl"></image>
												<!-- <image class="positionAbsolute da_icon_css" src="https://document.dxznjy.com/course/3fbe7257487d4fdc9be292d520e0e0c3.png"></image> -->
											</view>
											<view class="ml-15">
												<view class="c-33 f-28 lh-40">{{item.userName}}</view>
												<view class="c-55 f-28 lh-42">{{item.content}}</view>
											</view>
										</view>
										<view class="f-24 c-BF mt-16 text-right">{{item.time}}</view>
									</view>
								</view>
								<view v-else class="more_css f-28">
									<view  @click="goAnswer(info)">
										<span>全部 <span>{{ info.commentVos.length }}</span> 个回复</span>
										<image class="more_icon_css ml-12" src="https://document.dxznjy.com/course/6bacd7ff3f3547df8870e1e67405cb08.png"></image>
									</view>
								</view>
								<view  v-if="info.showFalse"  class="more_css f-28">
									<view  @click="goAnswer(info)">
										<span>收起</span>
										<image class="more_icon_css ml-12" src="https://document.dxznjy.com/course/6bacd7ff3f3547df8870e1e67405cb08.png"></image>
									</view>
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>
		</scroll-view>
		<view class="pt-140" v-if="answerInfo.length==0">
			<emptyPage emptyText="还没有人评论哦~快来评论吧"></emptyPage>
		</view>
		<view class="input_center_css bg-ff">
			<input type="text" v-model="content"  placeholder="请输入您的评论......">
			<view @click="pushRelease(releaseInfo,1)"  class="fabu_btn f-28 c-ff">发布</view>
		</view>
		<view v-if="shoFoucs" class="bg-ff input_data_css"  :style="'bottom:'+keyUpHeight+'px;'">
			<input  v-model="contentFlex" @blur="getBotton" type="text" :adjust-position="false" :auto-blur="true" :focus="shoFoucs" placeholder="请输入您的评论......">
			<view  @click="pushRelease(selectInfo)"  class="fabu_btn f-28 c-ff">发布</view>
		</view>
		<growthPopup ref="growthPopupRefs"></growthPopup>
	</view>
</template>

<script>
const {
		$navigationTo,
		$showMsg,
		$getSceneData,
		$showError,
		$http
	} = require("@/util/methods.js")
	import growthPopup from "../components/growthPopup.vue"
	import emptyPage from "../components/emptyPage.vue"
	export default {
		components:{growthPopup,emptyPage},
		data() {
			return {
				shoFoucs:false,
				keyUpHeight:-200,
				contentFlex:'',
				avaUrl: 'https://document.dxznjy.com/dxSelect/home_avaUrl.png',
				answerInfo:[],
				content:'',
				identityType:uni.getStorageSync('identityType'),
				selectInfo:{},
				screenWindow:0,
				releaseInfo:{},
				infoLists:{},
				scrollTopNum:0,
				scrollTop:0,
				page:1,
			}
		},
		onReachBottom() {
			console.log('-------------==============================------------')
			if (this.page * 10 >= this.infoLists.totalItems) {
				return false;
			}
			this.getComment(true, ++this.page);
		},
		onLoad(e) {
			let that =this
			this.releaseInfo=JSON.parse(uni.getStorageSync('releaseInfo'))
			this.getComment()
			uni.getSystemInfo({
				success: function (res) {
					that.screenWindow=res.screenHeight-res.windowHeight
				}
			});
		},
		methods: {
			goAnswer(info){
				// info.showFalse=!info.showFalse
				this.$set(info,'showFalse',!info.showFalse)
			},
			scrolltolower(){
				console.log('-------------==============================------------')
				if (this.page * 10 >= this.infoLists.totalItems) {
					return false;
				}
				this.getComment(true, ++this.page);
			},
			scroll(e){
				this.scrollTopNum=e.detail.scrollTop
			},
			getFouseItem(info){
				this.selectInfo=info
				let _this=this
				this.$nextTick(() => {
					_this.shoFoucs=true
					uni.onKeyboardHeightChange(res => {
						_this.keyUpHeight=res.height-_this.screenWindow/2
					})
				})
				
			},
			getBotton(){
				this.shoFoucs=false
				this.keyUpHeight=-200
			},
			async getComment(isPage,page){
				let _this = this
				const res = await $http({
					url: 'zx/wap/CultureCircle/getComment',
					showLoading:true,
					data: {
						topicId:this.releaseInfo.id,
						pageNum:page||1,
						pageSize:10,
					}
				})
				if(res){
					this.infoLists = res.data;
					if(isPage==-1){
						_this.scrollTop=this.scrollTopNum
						this.$nextTick(() =>{
							 _this.scrollTop = 0
						})
					}
					if (isPage&&isPage!=-1) {
						this.answerInfo = [...this.answerInfo, ...res.data.data];
					} else {
						this.answerInfo = res.data.data || [];
					}
				}
			},
			// /zx/wap/CultureCircle/getComment
			async pushRelease(info,key){
				let pageInfo={}
				pageInfo={
					content:this.content,
					topicId:this.releaseInfo.id,
					userId:uni.getStorageSync('user_id')?uni.getStorageSync('user_id'):''
				}
				if(key==1){
					pageInfo.replyType=1
				}else{
					pageInfo.replyType=2
					pageInfo.content= this.contentFlex
					pageInfo.targetId=info.userId
					pageInfo.parentId=info.id
				}
				if(!pageInfo.content){
					$showMsg('请输入你的评论内容');
					return
				}
				this.auditContent(pageInfo)
			},
			async auditContent(pageInfo){
				let _this = this
				const res = await $http({
					url: 'zx/common/auditContent',
					method: 'POST',
					showLoading:true,
					data: {
						content:pageInfo.content,
						scene :2,
					}
				})
				if(res){
					if(res.data=='pass'){
						let _this = this
						const res = await $http({
							url: 'zx/wap/CultureCircle/comment',
							showLoading:true,
							method: 'POST',
							data: pageInfo
						})
						if(res){
							_this.page=1
							_this.getGrowthValue('COMMENT')
							_this.content=''
							_this.contentFlex=''
							_this.getBotton()
							_this.getComment(-1)
							
						}
					}else if(res.data=='risk'){
						$showMsg('您评论的内容有风险');
					}
				}
			},
			async getGrowthValue(text){
				if(this.identityType!=4){
					return
				}
				const res = await $http({
					url: 'zx/wap/CultureCircle/getGrowthValue?eventType='+text+'&userId='+uni.getStorageSync('user_id'),
					method: 'POST',
					data: {}
				})
				if(res){
					if(Number(res.data)){
						this.$refs.growthPopupRefs.open(res.data)
					}
				}
			},
		}
	}
</script>

<style lang="scss" scoped>
	// .problem_top_content{
	// 	padding:0 56rpx;
	// 	padding-top: 40rpx;
	// }
	.c-BF{
		color:#bfbfbf;
	}
	.input_data_css{
		position: fixed;
		left:0;
		bottom:-200px;
		background-color: #fff;
		width: 750rpx;
		display: flex;
		justify-content: flex-start;
		z-index: 999;
		padding-bottom: 120rpx;
		input{
			width: 600rpx;
			height: 130rpx;
			line-height: 130rpx;
			color:#BEBEBE;
			padding-left: 32rpx;
			font-size: 28rpx;
		}
		.fabu_btn{
			width: 160rpx;
			height: 72rpx;
			background-color: #339378;
			border-radius: 36rpx;
			text-align: center;
			line-height: 72rpx;
			margin-top: 32rpx;
			margin-right: 32rpx;
		}
	}
	.release_detail_main{
		height: 100vh;
		overflow: hidden;
	}
	.da_content_css{
		padding-top: 32rpx;
		height: calc(100vh - 220rpx);
		width: 686rpx;
		.text-right{
			text-align: right;
		}
		.da_item_css{
			border-radius: 16rpx;
			margin-bottom: 24rpx;
			padding:24rpx;
		}
		.more_css{
			display: flex;
			justify-content: space-between;
			width: 100%;
			color:#28A781;
			margin-top: 32rpx;
			.more_icon_css{
				width: 15rpx;
				height: 20rpx;
			}
			
		}
		
	}
	.input_center_css{
		position: absolute;
		left:0;
		bottom:0px;
		background-color: #fff;
		width: 750rpx;
		display: flex;
		justify-content: flex-start;
		z-index: 999;
		padding-bottom: 40rpx;
		input{
			width: 600rpx;
			height: 130rpx;
			line-height: 130rpx;
			color:#BEBEBE;
			padding-left: 32rpx;
			font-size: 28rpx;
		}
		.fabu_btn{
			width: 160rpx;
			height: 72rpx;
			background-color: #339378;
			border-radius: 36rpx;
			text-align: center;
			line-height: 72rpx;
			margin-top: 32rpx;
			margin-right: 32rpx;
		}
	}
	.release_image{
		width: 32rpx;
		height: 32rpx;
	}
	.right_css_style{
		display: flex;
		justify-content: flex-start;
	}
	.release_right_css{
		width:565rpx;
	}
.da_icon_css,.wen_icon_css{
	width: 40rpx;
	height: 40rpx;
	bottom:0;
	right:0;
}
</style>
