<template>
  <view class="wordContent">
    <view class="userHead">
      <view class="title_top">
        <image class="iconBack" :src="imgHost + 'title_back.png'" @click="gotoBack()" mode=""></image>
        单词标识
      </view>
    </view>

    <!-- 单词列表 -->
    <view class="wordShowList">
      <view class="showWord" v-for="(item, index) in showDataList" :class="activeIndex == index ? 'active' : ''" @click="playWordList(item, index)" :key="index">
        <text style="width: 50%" :style="item.translation.length > 8 ? 'font-size:28rpx' : ''" class="showWordEnglish">{{ item.word }}</text>
        <text class="showWordChinese" style="width: 50%" :style="item.translation.length > 8 ? 'font-size:28rpx' : ''" v-if="item.isTap">{{ item.translation }}</text>
      </view>
    </view>

    <!-- 底部翻页 -->
    <view class="bottomPage">
      <image class="pageFlip" :style="pageIndex != 1 ? '' : 'opacity: 0'" :src="imgHost + 'interesting/page_up.png'" @click="toPage('up')" mode=""></image>
      <view class="pageNum">
        <text style="color: #fa6414">{{ pageIndex }}</text>
        /
        <text style="color: #000000">{{ allDataList.length }}</text>
      </view>
      <image class="pageFlip" :style="pageIndex != allDataList.length ? '' : 'opacity: 0'" :src="imgHost + 'interesting/page_down.png'" @click="toPage('down')" mode=""></image>
    </view>
  </view>
</template>

<script>
  export default {
    data() {
      return {
        scheduleCode: null,
        imgHost: getApp().globalData.imguseHost,
        wrongData: {}, //获取错词需要的数据
        activeIndex: -1,
        pageIndex: 1, //当前页
        showDataList: [], //当前页展示数据单词列表
        allDataList: [], //所有错误数据
        pageNum: 5
      };
    },
    onLoad(options) {
      this.wrongData = JSON.parse(decodeURIComponent(options.wrongData));
      //  console.log(this.wrongData)
      this.getShowData();
    },
    methods: {
      getShowData() {
        let that = this;
        that.$httpUser
          .get(
            `znyy/course/getWrongWords?play=${that.wrongData.play}&status=${that.wrongData.status}&nowGroup=${that.wrongData.nowGroup}&nowLevel=${that.wrongData.nowLevel}&roundId=${that.wrongData.roundId}`
          )
          .then((res) => {
            //  console.log(res)
            if (res.data.success) {
              that.allDataList = that.arrSplitFun(res.data.data.data, that.pageNum);
              that.showDataList = that.allDataList[that.pageIndex - 1];
              that.showDataList.forEach((ele) => {
                that.$set(ele, 'isTap', null);
              });
            } else {
              that.$util.alter(res.data.message);
            }
          });
      },

      // 数组分割成多少个一组
      arrSplitFun(arr, N) {
        var result = [];
        for (var i = 0; i < arr.length; i += N) {
          result.push(arr.slice(i, i + N));
        }
        return result;
      },

      // 点击列表
      playWordList(item, index) {
        this.activeIndex = index;
        this.$playVoice(item.word, false);
        this.showDataList.forEach((ele, idx) => {
          idx != index ? (ele.isTap = null) : '';
        });
        if (item.isTap == null) {
          item.isTap = false;
        } else if (item.isTap == false) {
          item.isTap = true;
        } else {
          item.isTap = null;
        }
      },

      // 翻页
      toPage(ele) {
        if (ele == 'up') {
          if (this.pageIndex != 1) {
            this.pageIndex--;
          }
        }
        if (ele == 'down') {
          if (this.pageIndex != this.allDataList.length) {
            this.pageIndex++;
          }
        }
        this.activeIndex = -1;
        this.showDataList = this.allDataList[this.pageIndex - 1];
        this.showDataList.forEach((ele, idx) => {
          ele.isTap = null;
        });
      },

      //返回按钮
      gotoBack() {
        uni.navigateTo({
          url: '/antiAmnesia/review/funReview?scheduleCode=' + this.wrongData.scheduleCode + '&merchantCode=' + this.wrongData.merchantCode
        });
      }
    }
  };
</script>

<style>
  page {
    background: #f0f8f0;
    height: 100vh;
    padding: 0;
  }
  .wordContent {
    height: 100%;
    position: relative;
  }

  .userHead {
    background: #18b48e;
    /* #ifdef APP-PLUS */
    height: 120rpx;
    /* #endif */
  }
  .title_top {
    /* #ifdef APP-PLUS */
    padding-top: 50rpx;
    /* #endif */
  }
  .iconBack {
    /* #ifdef APP-PLUS */
    padding-top: 50rpx;
    /* #endif */
    width: 80rpx;
    height: 68rpx;
  }

  .wordShowList {
    padding: 50rpx;
  }
  .showWord {
    margin-top: 40rpx;
    height: 150rpx;
    color: #14806c;
    padding: 0 28rpx;
    box-sizing: border-box;
    background-color: #ffffff;
    border: 2rpx solid #f0f8f0;
    width: 100%;
    border-radius: 20rpx;
    line-height: 150rpx;
  }
  .showWord.active {
    border-color: #48a993;
  }
  .showWord text {
    font-size: 45rpx;
    display: inline-block;
    width: 49%;
    text-align: center;
  }
  .showWord .showWordEnglish {
    font-style: italic;
    text-align: left !important;
  }

  .showWord.active .showWordEnglish {
    font-weight: bold;
  }

  .bottomPage {
    width: 100%;
    height: 120rpx;
    position: absolute;
    display: flex;
    justify-content: center;
    align-items: center;
    bottom: 0;
    background: #ffffff;
    text-align: center;
    box-shadow: 0rpx -6rpx 20rpx #d6e8d7;
  }

  .pageFlip {
    width: 98rpx;
    height: 98rpx;
  }
  .pageNum {
    display: inline-block;
    margin: 0 20rpx;
    color: #a1ccc4;
  }
</style>
