<template>
  <view>
    <!-- 		<view class="col-12 relative" style="position: fixed;top: 0;">
			<view>
				<image :src="imgHost+'dxSelect/fourthEdition/parent-bgc.png'" class="header-img"></image>
			</view>
			<view class="icon_img"  @click="goback">
				<uni-icons type="left" size="20" color="#000"></uni-icons>
			</view>
			<view class="page_title bold flex-col c-00">
				<text class="">提现</text>
			</view>
			
			<view class="plr-30" style="position: fixed;top: 200rpx;">
				<view class="mb-30 c-66">可提现金额（元）</view>
				<view>￥<text class="f-36 bold">{{userAccount != null?userAccount.availableCashAmount:0}}</text></view>
			</view>
			<view style="position: fixed;top: 360rpx;" class="w100">
				<image :src="imgHost+'dxSelect/fourthEdition/parent-bgc2.png'" mode="widthFix" class="w100"></image>
			</view>
			
			<view class="plr-60" style="position: fixed;top: 410rpx;">
				<view class="mb-30 c-ff">提现金额</view>
				<view class="flex c-ff">
					<text class="f-58 bold money_height">￥</text>
					<input v-model="inputValue" type="digit" name="amount" class="f-50 bold flex-box money_height mt-10"/>
				</view>
			</view>
		
		</view> -->
    <!-- <view class="home_con">
			<form @submit="submit_s">
				<view class="personal_withdraw bg-ff radius-15 flex_s c-00">
					<view class="radius-15 f-28">
						<view class="mb-30 c-66">提现金额</view>
						<view class="flex">
							<text class="f-58 bold money_height">￥</text>
							<input v-model="inputValue" type="digit" name="amount" class="f-50 bold flex-box money_height"/>
						</view>
					</view>
				</view>
				
			</form>
		</view> -->
    <view class="partner p-30 f-28">
      <form id="#nform">
        <view class="partnerFlex" style="margin-top: 44rpx; margin-bottom: 20rpx">
          <view class="bold lineHiehgt">
            <text style="color: red">*</text>
            提现金额:
          </view>
          <view style="width: 60%">
            <input class="uni-input" name="input" placeholder="请输入提现金额" maxlength="10" v-model="inputValue"
              type="text" />
          </view>
        </view>
        <view class="tips">温馨提示：最低提现金额为1元，单笔提现手续费为1元。</view>
        <view class="" v-if="signContract">
          <view class="" v-if="!disabledAuthen">
            <view class="t-c c-c2">
              当前登录账号暂未实名认证
              <span style="color: red; margin-left: 20rpx" @tap="goAuthen">前往认证</span>
            </view>
          </view>
          <view class="c-c2" v-if="disabledAuthen">
            <p>认证信息</p>
            <p>收款人姓名:{{ bankInfo.realName }}</p>
            <p>银行卡号:{{ bankInfo.bankCard }}</p>
            <p>手机号:{{ bankInfo.mobile }}</p>
            <p>实名认证状态:已认证</p>
          </view>
        </view>
        <!-- 		<view class="partnerFlex" style="margin-top: 44rpx;margin-bottom: 50rpx;">
						<view class=" bold lineHiehgt"><text style="color: red;">*</text>收款人姓名:</view>
						<view style="width: 60%;">
							<input class="uni-input" name="input" placeholder="请输入收款人姓名" maxlength="10" v-model="name" type="text"/>
						</view>
					</view>
					<view class="partnerFlex" style="margin-top: 44rpx;margin-bottom: 50rpx;">
						<view class=" bold lineHiehgt"><text style="color: red;">*</text>开户行名称:</view>
						<view style="width: 60%;">
							<input class="uni-input" name="input" placeholder="请输入开户行名称" v-model="clubCode"/>
							<text style="font-size: 24rpx;color: #EA6031;">请输入银行账号对应的开户行全称</text>
						</view>
					</view>
					<view class="partnerFlex" style="margin-top: 44rpx;margin-bottom: 50rpx;">
						<view class=" bold lineHiehgt"><text style="color: red;">*</text>收款银行账号:</view>
						<view style="width: 60%;">
							<input class="uni-input" name="input" placeholder="请输入收款银行账号" v-model="clubCode"/>
						</view>
					</view> -->
      </form>
      <view class="submitButton">
        <button @tap.stop="submit_s" class="f-28 c-ff" :disabled="!disabledAuthen">提交信息</button>
      </view>
    </view>

    <!-- 	<view class="f-28 personal_content plr-30">
			<view class="bold f-30">提现须知</view>
			<view class="mt-5 c-66 lh-50">1、为保证提现成功，请先完成实名认证绑卡操作 ，银行卡信息审核通过后方可进行提现操作。</view>
			<view class="mt-5 c-66 lh-50">2、提现到账时间 ：提现成功后一般将在1-2个工作日左右到账。</view>
			
			<view class="flex-c" style="width: 100%;">
				<button class="Withdrawal f-30 c-ff t-c" :disabled="disabled" @click="submit_s">确定提现</button>
			</view>
		</view> -->

    <!-- 提现金额不能小于1元提示 -->
    <uni-popup ref="notifyPopup" type="top" mask-background-color="rgba(0,0,0,0)">
      <view class="t-c bg-ff flex-c ptb-25 radius-50 mt-80 notify">
        <u-icon name="error-circle-fill" color="#FA370E" size="42"></u-icon>
        <view class="f-34 ml-15">低于1元不可提现</view>
      </view>
    </uni-popup>
  </view>
</template>

<script>
  const {
    $showError,
    $http
  } = require('@/util/methods.js');
  const {
    httpUser
  } = require('@/util/luch-request/indexUser.js');
  import Util from '@/util/util.js';
  import Superman from '@/common/superman.js';
  let count;
  export default {
    data() {
      return {
        show: false,
        price: 0,
        inputValue: '', // 输入框金额
        disabled: false,
        isBindPayPhone: 1,
        yanStatus: false,
        minute: 60,
        tranceNum: null, //流水号
        mobile: null, //手机号
        signContractStatus: 1, //签约状态
        signContractUrl: null, //签约地址
        type: 1, // 1学习超人 2俱乐部
        userInfo: {}, //用户信息
        userAccount: {}, //俱乐部账号金额信息
        sourceOrderId: '', // 提现详情id
        imgHost: getApp().globalData.imgsomeHost,
        disabledAuthen: true,
        signContract: false,
        userCode: '',
        bankInfo: {},
        payAmount: '',
        tableKey: 2
      };
    },
    onLoad(e) {
      this.type = Number(e.type);
      this.userinfo = JSON.parse(e.userinfo);
      this.userCode = e.userCode;
      this.payAmount = e.payAmount;
      this.tableKey = e.tableKey;
      console.log(this.userCode, ' this.userCode');
    },
    onShow() {
      let _this = this;
      _this.signContract = false;
      _this.indexData();
    },
    methods: {
      async getRealName() {
        uni.showLoading({
          title: '加载中...'
        });
        // 判断用户是否实名认证
        let res = await httpUser.get('mps/user/info/user/code', {
          userCode: this.userCode
        });
        if (res.data.success) {
          this.signContract = true;
          if (res.data.data.signContractStatus != 1) {
            this.disabledAuthen = false;
            /* 	uni.showModal({
  					title: '提示',
  					content: '实名认证未完成，前往认证',
  					showCancel: false,
  					success: function(res) {
  						if (res.confirm) {
  							uni.redirectTo({
  								url: '/splitContent/authen/authen'
  							})
  						} else if (res.cancel) {
  							uni.navigateBack()
  							console.log('用户点击取消');
  						}
  					}
  				}); */
          } else {
            this.bankInfo = res.data.data;
          }
          uni.hideLoading();
        }
        this.userAccount = await Superman.getAccount(this.type - 1, this.userinfo);
        if (this.userAccount == '') {
          this.userAccount = {};
        }
        this.userAccount.availableCashAmount = Util.Fen2Yuan(this.userAccount.availableCashAmount || 0); // 可提现
        // await this.getMoney();
      },
      goAuthen() {
        uni.navigateTo({
          url: '/splitContent/authen/authen?userCode=' + this.userCode
        });
      },
      // 获取超人、俱乐部可提现金额
      // async getMoney(){
      // 	const resdata = await $http({
      // 		url: 'zx/user/userWithdrawMoney',
      // 		data: {
      // 			type: this.type,
      // 		}
      // 	})
      // 	if (resdata) {
      // 		this.userAccount.availableCashAmount= (resdata.data || 0).toFixed(2); // 可提现
      // 	}
      // },
      async indexData() {
        let _this = this;
        const res = await $http({
          url: 'zx/user/userInfoNew'
        });
        if (res) {
          _this.userInfo = res.data;
          if (this.tableKey == 2) {
            _this.getRealName();
          }
        }
      },
      onClickLeft() {
        uni.navigateBack();
      },
      // 提交提现申请
      async submit_s() {
        let _this = this;
        if (!_this.inputValue) {
          $showError('提现金额不能为空');
          return false;
        }

        if (_this.inputValue > Number(_this.payAmount)) {
          $showError('可提现余额不足');
          return false;
        }
        if (_this.inputValue < 0.01) {
          $showError('提现金额不得低于1元');
          return false;
        }

        /* 	if (_this.inputValue < 1) {
  			this.$refs.notifyPopup.open();
  			setTimeout(()=>{
  				this.$refs.notifyPopup.close();
  			},1500)
  			return false
  		} */

        if (_this.disabled) {
          return false;
        }
        await $showError(`是否确认提现金额${_this.inputValue}元`);
        _this.disabled = true;
        uni.showLoading({
          title: '提交中'
        });
        let isFlexWithdraw = this.tableKey == 2 ? false : true;
        const res = await $http({
          url: 'zx/user/userWithdrawApply',
          method: 'post',
          data: {
            isFlexWithdraw: isFlexWithdraw,
            type: this.type,
            userCode: this.userCode,
            withdrawAmount: Number(_this.inputValue)
          }
        });
        _this.disabled = false;
        console.log(res.data);
        _this.sourceOrderId = res.data.sourceOrderId;
        if (res) {
          if (res.status == 1) {
            _this.$util.alter('提现成功');
            _this.inputValue = '';
            uni.navigateBack();
            // _this.withdrawCallback(res.data);
          } else {
            uni.hideLoading();
          }
        }
      },

      // 申请回调
      async withdrawCallback(data) {
        let _this = this;
        console.log(data);
        let res = await httpUser.put('mps/order/withdraw/unified', data);
        console.log(res);
        if (res.data.success) {
          console.log(res);
          _this.$util.alter('提现成功');
          _this.inputValue = '';
          uni.navigateTo({
            url: '/Personalcenter/vip/cashAdvance?withdrawId=' + _this.sourceOrderId
          });
        } else {
          this.userWithdrawFail(res.data);
        }
      },

      async userWithdrawFail(data) {
        console.log(data);
        let _this = this;
        const resdata = await $http({
          url: 'zx/user/userWithdrawFail',
          method: 'post',
          data: {
            msg: data.message,
            withdrawId: _this.sourceOrderId
          }
        });
        _this.inputValue = '';
        uni.navigateTo({
          url: '/Personalcenter/vip/cashAdvance?withdrawId=' + _this.sourceOrderId
        });
      },

      goback() {
        uni.navigateBack();
      }
    }
  };
</script>
<style>
  page {
    background-color: #f6f9fb;
  }
</style>
<style lang="scss" scoped>
  .personl_header_bg {
    height: 462upx;
    background: linear-gradient(to bottom, #2f8c70, #c7e0d8);
  }

  .personal_withdraw {
    width: 690upx;
    padding: 40rpx 30rpx 50rpx;
    position: absolute;
    top: -96upx;
    left: 30upx;
    box-sizing: border-box;
    backdrop-filter: blur(10upx);
    z-index: 999;
  }

  .page_title {
    position: absolute;
    top: 80upx;
    width: 100%;
    text-align: center;
  }

  .flex_s {
    display: flex;
    align-items: inherit;
    flex-direction: column;
    justify-content: center;
  }

  .home_bg {
    width: 100%;
    height: 160rpx;
    position: absolute;
    top: 165rpx;
    left: 0;
  }

  .icon_img {
    position: absolute;
    top: 80rpx;
    left: 30rpx;
    z-index: 999 !important;
  }

  .home_con {
    position: relative;
    margin-top: 442upx;
    border-radius: 20upx 20upx 0 0;
    padding: 30upx;
    background-color: #f3f8fc;
  }

  .Withdrawal {
    position: absolute;
    bottom: 60rpx;
    width: 586rpx;
    height: 90rpx;
    background-image: linear-gradient(to bottom, #88cfba, #1d755c);
    border-radius: 45rpx;
    line-height: 90rpx;
    margin: 30rpx auto 0 auto;
  }

  .sub_btn {
    margin-top: 80upx;
  }

  .buy_s {
    background-image: linear-gradient(to right, #159380, #0e6457);
    height: 98upx;
    line-height: 98upx;
    border-radius: 49upx;
  }

  .submit {
    background-image: linear-gradient(to right, #329e8b, #2c6e62);
    height: 88upx;
    border-radius: 44upx;
    line-height: 88upx;
  }

  .section {
    background-color: #f8f8f8;
    padding: 20upx;
    margin-bottom: 20upx;
    font-size: 28upx;
  }

  .phonecon {
    width: 600upx;
  }

  .with_bg {
    background-image: linear-gradient(to right, #0e6457, #159380);
    height: 484upx;
  }

  .con_tixian {
    position: relative;
    margin-top: -60rpx;
  }

  .fixed_b {
    position: fixed;
    bottom: 30upx;
    left: 5%;
    width: 90%;
  }

  .money_height {
    height: 70rpx;
  }

  .personal_content {
    margin-top: 700rpx;
  }

  .notify {
    box-shadow: 0rpx 0rpx 20rpx #e0e0e0;
  }

  /deep/.uni-popup__wrapper {
    padding: 0 60rpx !important;
  }

  .header-img {
    width: 100%;
    height: 466rpx;
  }

  .hint-img {
    width: 24rpx;
    height: 24rpx;
    position: absolute;
    top: -10rpx;
    right: -10rpx;
  }

  .course-img {
    width: 100%;
    height: 320rpx;
  }

  .partner {
    width: 100vw;
    height: 100vh;
    background-color: #fff;
    box-sizing: border-box;
  }

  .uni-input {
    height: 64rpx;
    padding-left: 32rpx;
    background-color: #f3f8fc;
  }

  .partnerFlex {
    display: flex;
    justify-content: space-between;
  }

  .lineHiehgt {
    line-height: 64rpx;
  }

  .submitButton {
    width: 686rpx;
    line-height: 74rpx;
    margin: 0 auto;
    background: #339378;
    border-radius: 38rpx;
    position: fixed;
    bottom: 32rpx;
  }

  .tips {
    width: 100%;
    height: auto;
    font-size: 24rpx;
    color: #999;
    margin-bottom: 20rpx;
  }
</style>