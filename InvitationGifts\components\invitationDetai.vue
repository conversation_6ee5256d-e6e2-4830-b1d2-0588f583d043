<template>
	<view class="" v-if="activityList&&activityList.length>0">
	<view class="shareCardRecord">
		<scroll-view scroll-y="true" @scrolltolower='scrolltable' class="scrollClass">
			<uniTable ref="table" :loading="loading" border stripe 
				@selection-change="selectionChange" style="overflow-y: auto;">
				<uniTr>
					<uniTh width='70'>被邀请人信息</uniTh>
					<uniTh width="80">活动类型</uniTh>
					<uniTh width="80">获得奖励</uniTh>
					<uniTh width="50">时间</uniTh>
				</uniTr>

				<uniTr v-for="(value,index) in activityList" :key="index">
					<uniTd>{{value.userInfo.split(',')[0]}}<br>
						{{value.userInfo.split(',')[1]}}
					</uniTd>
					<uniTd>
						{{value.type}}
					</uniTd>
					<uniTd>{{value.reward}}</uniTd>
					<uniTd>{{value.acquisitionTime}}</uniTd>
				</uniTr>

			</uniTable>
		</scroll-view>
	</view>
	</view>
	<view v-else class="curriculum_css_no pt-30 pb-55 f-28">
		<image class="curriculum_image" src="https://document.dxznjy.com/course/ac587707bf314badadb28a158852c77d.png"></image>
		<view class="c-66 f-24 ">暂无邀请记录</view>
	</view>
</template>

<script>
	const {
		httpUser
	} = require('@/util/luch-request/indexUser.js')
	import uniTable from "./uni-table/components/uni-table/uni-table.vue"
	import uniTd from "./uni-table/components/uni-td/uni-td.vue"
	import uniTr from "./uni-table/components/uni-tr/uni-tr.vue"
	import uniTh from "./uni-table/components/uni-th/uni-th.vue"
	export default {
		components: {
			uniTable,
			uniTr,
			uniTd,
			uniTh
		},
		data() {
			return {
				data: [],
				activityList: [], //规则文本
				activityId: '', //活动id
				finished: false,
				activitypageValue: 1,
				userId: uni.getStorageSync('user_id') ? uni.getStorageSync('user_id') : '',
				phoneHeight: ''
			};
		},
		onLoad(e) {
			this.activityId = e.activityId
		},
		onShow() {
			this.fetchActivityList();
			const info = uni.getSystemInfoSync();
			this.phoneHeight = info.windowHeight
		},
		onHide() {
			this.activityList = []
			this.activitypageValue = 1
		},
		onReady() {},
		methods: {
			//获取邀请记录
			fetchActivityList() {
				this.$httpUser.get('zx/wap/invite/getInvitationFlowList?pageSize=' + this.activitypageValue +
					'&pageNum=20&activityId=' + this.activityId).then(res => {
					if (res.data.data.data.length === 0) {
						this.finished = true
					} else {
						this.activityList = [...this.activityList,
							...res.data.data.data
						]
					}
				})
			},
			scrolltable() {
				if (this.finished) {
					return;
				}
				this.activitypageValue++;
				this.fetchActivityList();
			},
			
		}
	};
</script>
</script>

<style lang="scss" scoped>

	.goods {
		width: 48%;
		height: 700rpx;
	}



	.scrollClass {
		height: calc(100vh - 100rpx);
	}

	/*分享弹窗样式*/
	.shareCard {
		position: relative;
		height: 600rpx;
		background: #FFFFFF;
		color: #000;
		padding-top: 50upx;
		box-sizing: border-box;
		overflow: hidden;
		width: 90vw;
	}

	.shareCardRecord {
		position: relative;
		height: 100%;
		width: 100vw;
		background: #FFFFFF;
		color: #000;
		box-sizing: border-box;
		overflow: hidden;
		overflow-y: auto;
	}

	.curriculum_css_no{
		position: relative;
		width: 710rpx;
		margin:auto;
		text-align: center;
		.curriculum_image{
			width: 74rpx;
			height: 76rpx;
			display: block;
			margin:16rpx auto;
		}
		.curriculum_title{
			text-align: left;
		}
	}
</style>