<template>
  <view class="container">
    <view v-if="recordList.length > 0">
      <view class="record-item" v-for="item in recordList" :key="item.id">
        <!-- <input readonly v-model="item.customerMobile" /> -->
        <!-- <view>{{ item.customerMobile }}</view> -->
        <view>{{ item.merchantCode }}</view>
        <view class="record-time">{{ item.createdTime }}</view>
      </view>
    </view>
    <view v-else>
      <view style="height: 280rpx"></view>
      <u-empty text="暂无通话记录" iconSize="180" textSize="24"></u-empty>
    </view>
    <!-- 加载更多 -->
    <view v-if="no_more && recordList != undefined && recordList.length > 0">
      <u-divider text="到底了"></u-divider>
    </view>
  </view>
</template>

<script>
  export default {
    data() {
      return {
        recordList: [],
        data: {
          pageNum: 1,
          pageSize: 20
        },
        totalItems: 0, // 记录总数量
        no_more: false,
        userId: uni.getStorageSync('user_id') ? uni.getStorageSync('user_id') : ''
      };
    },
    onShow() {
      console.log('页面显示');
      //   this.recordList = [];
      this.data.pageNum = 1;
      this.no_more = false;
      this.getRecordslist(false, 1);
    },
    methods: {
      async getRecordslist(isPage, page) {
        uni.showLoading({
          title: ''
        });
        this.$httpUser.get(`zx/privacy/log/find?pageNum=${this.data.pageNum}&pageSize=${this.data.pageSize}&userId=${this.userId}`).then((res) => {
          console.log(res, 99999);
          if (res.data.status === 1) {
            if (isPage) {
              let old = this.recordList;
              this.recordList = [...old, ...res.data.data.data];
            } else {
              console.log(res.data.data.data, 88888);
              this.recordList = res.data.data.data;
            }
            this.totalItems = res.data.data.totalCount;
          }
        });

        uni.hideLoading();
      }
    },
    // 触底的事件
    onReachBottom() {
      console.log('触底');
      if (this.data.pageNum * 20 >= Number(this.totalItems)) {
        this.no_more = true;
        return false;
      }
      this.getRecordslist(true, ++this.data.pageNum);
    }
  };
</script>

<style lang="scss" scoped>
  /* 布局容器 */
  .container {
    padding: 0rpx 32rpx 32rpx 32rpx;
    background: #fff;
    min-height: 100vh;
    box-sizing: border-box;
  }
  .record-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 110rpx;
    color: #555555;
    font-size: 28rpx;
    font-weight: 600;
    border-bottom: 2rpx solid #faf9f9;
  }
  /* .record-item:nth-last-child(1) {
    border-bottom: none;
  } */
  .record-time {
    font-size: 28rpx;
    color: #a3a7b2;
    font-weight: 400;
  }
</style>
