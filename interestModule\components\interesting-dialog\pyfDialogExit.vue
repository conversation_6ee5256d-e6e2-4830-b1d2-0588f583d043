<template>
  <view class="popopPower_exit">
    <view class="popopPower_exit_content">
      <image src="https://document.dxznjy.com/course/7261ca803dec4005a203bad3be6baeae.png" />
      <text>{{ title }}</text>
      <view class="popopPower_exit_comfirm" @click="close()">继续努力</view>
      <view class="popopPower_exit_back" @click="confirm()">退出</view>
    </view>
  </view>
</template>

<script>
  export default {
    props: {
      /**
       * 标题
       */
      title: {
        type: String,
        default: '要是现在离开，小虫子就跑走了 T T'
      }
    },
    methods: {
      confirm() {
        this.$emit('confirm');
      },

      //关闭弹窗
      close() {
        this.$emit('close');
      }
    }
  };
</script>
<style lang="scss" scoped>
  page {
    height: 100vh;
    padding: 0;
  }
  .popopPower_exit {
    position: relative;
    background: rgba(0, 0, 0, 0.3);
    width: 100vw;
    height: 100vh;
  }
  .popopPower_exit_content {
    width: 750rpx;
    height: 580rpx;
    position: absolute;
    bottom: 0;
    background: #fff;
    border-radius: 25rpx 25rpx 0 0;
    display: flex;
    flex-wrap: wrap;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    image {
      width: 268rpx;
      height: 164rpx;
      margin-bottom: 32rpx;
    }
    text {
      font-family: AlibabaPuHuiTi_3_55_Regular;
      font-size: 32rpx;
      color: #555555;
      line-height: 44rpx;
    }
    .popopPower_exit_comfirm {
      width: 632rpx;
      height: 84rpx;
      margin-top: 32rpx;
      position: initial;
      font-size: 28rpx;
      color: #fff;

      display: grid;
      place-items: center;
      background: url('https://document.dxznjy.com/course/876615d6850949d88ead31f147887b7b.png') 100% 100% no-repeat;
      background-size: 100% 100%;
    }
    .popopPower_exit_back {
      width: 300rpx;
      height: 80rpx;
      font-family: AlibabaPuHuiTi_3_85_Bold;
      font-size: 28rpx;
      color: #ffa332;
      line-height: 80rpx;
      font-style: normal;
      text-align: center;
    }
  }
</style>
