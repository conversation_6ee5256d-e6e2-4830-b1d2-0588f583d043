<template>
  <view>
    <view class="bg-h">
      <view class="word-position t-c col-12" style="">
        <view @click="goback">
          <uni-icons type="left" size="24" color="#000"></uni-icons>
        </view>
        <view class="f-26">复习报告{{ title }}</view>
        <view></view>
      </view>
    </view>
    <view class="ReportTop" v-if="obj">
      <view style="display: flex; justify-content: space-between">
        <view>
          <view class="name">学生姓名：{{ obj.studentName }}</view>
          <view class="name">学生编号：{{ obj.studentCode }}</view>
          <view class="name">课程名称：{{ obj.courseName }}</view>
          <view class="pos" style="margin-top: 24rpx" v-if="type == 1">
            <view class="posText">复习进度</view>
            <view class="posBg"></view>
          </view>
        </view>
        <view class="right">
          <image src="https://document.dxznjy.com/course/588afa1826e145c5ad86aebcffb88c53.png" style="width: 100%; height: 100%"></image>
        </view>
      </view>
      <view class="review" v-if="type == 1">
        <view class="reviewItem">
          <view class="reviewItemText">需复习</view>
          <view class="reviewItemNum" v-if="obj">
            {{ obj.totalReviewBarriersToday }}
            <span style="margin-left: 16rpx; font-size: 28rpx">个</span>
          </view>
        </view>
        <view class="reviewItem" style="background-image: url('https://document.dxznjy.com/course/052580c050a0464e8cbc6e5370376ddb.png')">
          <view class="reviewItemText">已复习</view>
          <view class="reviewItemNum" v-if="obj">
            {{ obj.reviewedBarriersToday }}
            <span style="margin-left: 16rpx; font-size: 28rpx">个</span>
          </view>
        </view>
        <!--    <view class="reviewItem" style="background-image: url('https://document.dxznjy.com/course/c89566d699a04521b76b134488857efb.png')">
          <view class="reviewItemText">未复习</view>
          <view class="reviewItemNum">
            {{ obj.unreviewedBarriersToday }}
            <span style="margin-left: 16rpx; font-size: 28rpx">个</span>
          </view>
        </view> -->
      </view>
      <view class="pos" style="margin-top: 24rpx">
        <view class="posText">正确率</view>
        <view class="posBg"></view>
      </view>
      <view class="accuracy">
        <view class="accuracyText">
          生词
          <span style="font-size: 32rpx; font-weight: bold; margin-left: 10rpx">{{ obj.wordAccuracy }}%</span>
        </view>
        <progress :percent="obj.wordAccuracy" activeColor="#36957a" stroke-width="16" border-radius="20" />
      </view>
    </view>
    <view class="errorWork" v-if="obj && obj.wordsData && obj.wordsData.length">
      <view class="pos" style="margin-top: 24rpx">
        <view class="posText">错题回顾</view>
        <view class="posBg"></view>
      </view>
      <view class="newWork">生词</view>
      <view class="workItems">
        <view class="">
          <view class="workItem" v-for="(item, index) in obj.wordsData" :key="index">
            <view style="flex: 1">{{ item.word }}</view>
            <view style="flex: 1; font-weight: normal">{{ item.chinese }}</view>
          </view>
        </view>
      </view>
    </view>
    <view class="emtp" v-else>
      <image src="https://document.dxznjy.com/course/445806b8053f4246b0897dca73f40629.png" style="width: 122rpx; height: 122rpx" mode=""></image>
      <view class="emptyText">无复习的生词，无数据显示</view>
    </view>
    <view class="lone" v-if="type == 1">
      <view class="btn1" @click="goOld">往期复习</view>
    </view>
  </view>
</template>

<script>
  export default {
    data() {
      return {
        title: '',
        forgettingId: '', //抗遗忘id
        studentCode: '',
        merchantCode: '',
        type: 0,
        obj: null
      };
    },
    onLoad(options) {
      this.studentCode = options.studentCode;
      this.merchantCode = options.merchantCode;
      this.forgettingId = options.forgettingId;
      this.type = options.type;
    },
    onShow() {
      this.init();
    },
    methods: {
      async init() {
        let { data } = await this.$httpUser.get(
          `znyy/superReadReview/getReviewReport?studentCode=${this.studentCode}&merchantCode=${this.merchantCode}&forgettingId=${this.forgettingId}`
        );
        if (data.success) {
          this.obj = data.data;
          if (this.obj.wordsData) {
            this.obj.wordsData = JSON.parse(this.obj.wordsData);
          } else {
            this.obj.wordsData = [];
          }
          this.title = this.obj.createTime.slice(0, 11);
        }
      },
      goback() {
        uni.navigateBack();
      },
      goOld() {
        uni.redirectTo({
          url: `/ReadForget/pastForget?studentCode=${this.studentCode}&merchantCode=${this.merchantCode}`
        });
      }
    }
  };
</script>

<style lang="scss" scoped>
  .emtp {
    margin-top: 22rpx;
    border-radius: 20rpx 20rpx 0rpx 0rpx;
    background-color: #fff;
    flex-direction: column;
    display: flex;
    height: 512rpx;
    justify-content: center;
    align-items: center;
    .emptyText {
      margin-top: 40rpx;
      font-size: 28rpx;
      color: #bababa;
      text-align: center;
    }
  }
  .word-position {
    position: fixed;
    top: 0;
    left: 0;
    background-color: #f3f8fc;
    // height: 190rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-top: 110rpx;
    box-sizing: border-box;
  }
  .lone {
    position: fixed;
    height: 120rpx;
    background-color: #fff;
    width: 100vw;
    bottom: 20rpx;
    left: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    .btn1 {
      width: 324rpx;
      height: 92rpx;
      border: 2rpx solid #428a6f;
      border-radius: 46rpx;
      text-align: center;
      line-height: 92rpx;
      box-sizing: border-box;
      font-size: 28rpx;
      color: #428a6f;
    }
  }
  .ReportTop {
    margin-top: 180rpx;
    padding: 24rpx 32rpx;
    background: linear-gradient(to bottom, #339378, #fff);
    .name {
      height: 50rpx;
      line-height: 50rpx;
      font-size: 28rpx;
      font-weight: bold;
      color: #fff;
      margin-bottom: 14rpx;
    }
    .right {
      width: 296rpx;
      height: 278rpx;
    }
  }
  .errorWork {
    height: 512rpx;
    padding: 32rpx;
    box-sizing: border-box;
    background-color: #fff;
    border-radius: 20rpx 20rpx 0rpx 0rpx;
    margin-top: 24rpx;
    .newWork {
      height: 40rpx;
      font-size: 28rpx;
      color: #3b977d;
      line-height: 40rpx;
      font-weight: bold;
      margin-top: 32rpx;
    }
    .workItems {
      height: 334rpx;
      overflow-y: auto;
    }
    .workItem {
      display: flex;
      height: 44rpx;
      margin-top: 24rpx;
      font-size: 32rpx;
      color: #555555;
      align-items: center;
      font-weight: bold;
    }
  }
  .review {
    display: flex;
    justify-content: space-around;
    height: 206rpx;
    margin-top: 10rpx;
    margin-bottom: 32rpx;
    .reviewItem {
      padding: 30rpx 24rpx 0;
      box-sizing: border-box;
      width: 218rpx;
      background: url('https://document.dxznjy.com/course/0970c3f238aa4a3986a15786deb12cb8.png') no-repeat;
      background-size: contain;
      .reviewItemText {
        height: 40rpx;
        font-weight: bold;
        font-size: 28rpx;
        color: #349479;
        line-height: 40rpx;
      }
      .reviewItemNum {
        color: #333333;
        font-size: 56rpx;
        font-weight: bold;
        margin-top: 28rpx;
      }
    }
  }
  .accuracy {
    height: 136rpx;
    padding: 16rpx 24rpx;
    box-sizing: border-box;
    .accuracyText {
      font-size: 28rpx;
      color: #555555;
      line-height: 44rpx;
      height: 44rpx;
      margin-bottom: 16rpx;
    }
  }
  .pos {
    // width: 152rpx;
    display: inline-block;
    height: 44rpx;
    position: relative;
    font-size: 28rpx;
    font-weight: bold;
    color: #333333;
    .posText {
      position: relative;
      z-index: 2;
      left: 8rpx;
    }
    .posBg {
      position: absolute;
      bottom: 0;
      left: 0;
      width: 120%;
      height: 20rpx;
      background-color: #66bfa6;
      border-radius: 14rpx;
      z-index: 1;
    }
  }
</style>
