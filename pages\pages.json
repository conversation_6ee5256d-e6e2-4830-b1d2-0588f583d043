{
	"pages": [ //pages数组中第一项表示应用启动页，参考：https://uniapp.dcloud.io/collocation/pages
		{
			"path": "pages/index/index",
			"style": {
				"navigationStyle": "custom",
				// "navigationBarTitleText": "vant-weapp",
				// 使用小程序组件
				// "usingComponents": {
				// 	"van-button": "/wxcomponents/vant/dist/button/index"
				// }
			}
		},
		{
			"path": "pages/home/<USER>/page",
			"style": {
				"navigationBarTitleText": ""
			}
		},
		{
			"path": "pages/index/course",
			"style": {
				"navigationBarTitleText": "课程中心"
			}
		}, {
			"path": "pages/index/mealDetail",
			"style": {
				"navigationBarTitleText": "套餐详情"
			}
		}, {
			"path": "Coursedetails/productDetils",
			"style": {
				"navigationBarTitleText": "商品详情"
			}
		}, {
			"path": "pages/index/confirm",
			"style": {
				"navigationBarTitleText": "下单页"
			}
		}, {
			"path": "pages/index/cart",
			"style": {
				"navigationBarTitleText": "购物车"
			}
		}, 
		{
			"path": "pages/interest/orderDetail",
			"style": {
				"navigationBarTitleText": "订单",
				"navigationStyle": "default",
				"navigationBarBackgroundColor": "#FFFFFF",
				"navigationBarTextStyle": "black"
			}
		},
		{
			"path": "pages/review/list",
			"style": {
				"onReachBottomDistance": 100,
				"navigationBarTitleText": "复习单词",
				"navigationStyle": "default",
				"navigationBarBackgroundColor": "#FFFFFF",
				"navigationBarTextStyle": "black"
			}
		
		}, {
			"path": "pages/review/report",
			"style": {
				"navigationBarTitleText": "复习报告",
				"navigationBarBackgroundColor": "#FFFFFF",
				"navigationBarTextStyle": "black"
			}
		}, {
			"path": "pages/review/history",
			"style": {
				"onReachBottomDistance": 100,
				"navigationBarTitleText": "往期复习",
				"navigationStyle": "default",
				"navigationBarBackgroundColor": "#FFFFFF",
				"navigationBarTextStyle": "black"
			}
		}, {
			"path": "pages/review/index",
			"style": {
				"navigationBarTitleText": "复习",
				"navigationStyle": "default",
				"navigationBarBackgroundColor": "#FFFFFF",
				"navigationBarTextStyle": "black"
			}
		},
		{
			"path": "pages/review/funReview",
			"style": {
				"navigationStyle": "custom",
				"enablePullDownRefresh": true,
				"app-plus": {
					"titleNView": false
				}
			}
		},
		{
			"path": "pages/review/studyFunReview",
			"style": {
				"navigationBarTitleText": "学习",
				"navigationBarBackgroundColor": "#FFFFFF",
				"navigationBarTextStyle": "black"
			}
		},{
		 "path": "pages/antiForgetting/reviewReport",
			"style": {
				// "usingComponents": {
				// 	"van-popup": "/wxcomponents/vant/dist/popup/index"
				// },
				"navigationStyle": "custom"
			}
		}, {
			"path": "pages/antiForgetting/historyReviewReport",
			"style": {
				// "usingComponents": {
				// 	"van-popup": "/wxcomponents/vant/dist/popup/index"
				// },
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/wordCheck/wordCheck",
			"style": {
				// "usingComponents": {
				// 	"van-empty": "/wxcomponents/vant/dist/empty/index",
				// 	"van-divider": "/wxcomponents/vant/dist/divider/index",
				// 	"van-sticky": "/wxcomponents/vant/dist/sticky/index"
				// },
				"navigationBarTitleText": "词汇量检测"
			}
		}, {
			"path": "pages/wordCheck/wordCheckReport",
			"style": {
				// "usingComponents": {
				// 	"van-empty": "/wxcomponents/vant/dist/empty/index",
				// 	"van-divider": "/wxcomponents/vant/dist/divider/index",
				// 	"van-sticky": "/wxcomponents/vant/dist/sticky/index"
				// },
				"navigationBarTitleText": "词汇量测试报告"
			}
		},
		{
			"path": "/splitContent/authen/authen",
			"style": {
				"navigationBarTitleText": "实名认证"
			}
		}, {
			"path": "pages/login/login",
			"style": {
				"navigationBarTitleText": "授权登录"
			}
		},
		{
			"path": "pages/poster/index",
			"style": {
				"navigationBarTitleText": "推荐海报"
			}
		},
		{
			"path": "pages/home/<USER>/index",
			"style": {
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/home/<USER>/order",
			"style": {
				"navigationBarTitleText": "订单列表"
			}
		},
		{
			"path": "pages/home/<USER>/orderdetail",
			"style": {
				"navigationBarTitleText": "订单详情"
			}
		},{
			"path": "pages/home/<USER>/logistics",
			"style": {
				"navigationBarTitleText": "物流详情"
			}
		},
		{
			"path": "pages/home/<USER>/index",
			"style": {
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/home/<USER>/customer",
			"style": {
				"navigationBarTitleText": "客户列表"
			}
		},
		{
			"path": "pages/home/<USER>/commission",
			"style": {
				"navigationBarTitleText": "佣金记录"
			}
		},
		{
			"path": "pages/home/<USER>/Withdrawal",
			"style": {
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/home/<USER>/With_List",
			"style": {
				"navigationBarTitleText": "提现明细"
			}
		},
		{
			"path": "pages/home/<USER>/apply",
			"style": {
				"navigationBarTitleText": "成为渠道商"
			}
		},
		{
			"path": "pages/home/<USER>/index",
			"style": {
				"navigationBarTitleText": "渠道商服务"
			}
		},
		{
			"path": "pages/home/<USER>/customer",
			"style": {
				"navigationBarTitleText": "客户列表"
			}
		},
		{
			"path": "pages/home/<USER>/qdsList",
			"style": {
				"navigationBarTitleText": "渠道商列表"
			}
		},
		{
			"path": "pages/home/<USER>/Finance",
			"style": {
				"navigationBarTitleText": "财务管理"
			}
		},
		{
			"path": "pages/home/<USER>/qd_apply",
			"style": {
				"navigationBarTitleText": "渠道商申请"
			}
		},
		{
			"path": "pages/home/<USER>/qd_apply",
			"style": {
				"navigationBarTitleText": "渠道商申请"
			}
		},
		{
			"path": "pages/home/<USER>/list/list",
			"style": {
				"navigationBarTitleText": "地址列表"
			}
		},
		{
			"path": "pages/home/<USER>/add/add",
			"style": {
				"navigationBarTitleText": "新增地址"
			}
		},
		{
			"path": "pages/home/<USER>/edit/edit",
			"style": {
				"navigationBarTitleText": "编辑地址"
			}
		},
		{
			"path": "pages/home/<USER>/order",
			"style": {
				"navigationBarTitleText": "套餐订单中心"
			}
		},
		{
			"path": "pages/home/<USER>/orderdetail",
			"style": {
				"navigationBarTitleText": "订单详情"
			}
		},
		{
			"path": "pages/writeoff/writeoff",
			"style": {
				"navigationBarTitleText": "订单核销"
			}
		}
	],
	"globalStyle": {
		"navigationBarTextStyle": "black",
		"navigationBarTitleText": "uni-app",
		"navigationBarBackgroundColor": "#ffffff",
		"backgroundColor": "#F7F7F7"
	},
	"tabBar": {
		"color": "#757575",
		"selectedColor": "#292929",
		"backgroundColor": "#ffffff",
		"borderStyle": "white",
		"list": [{
				"pagePath": "pages/index/index",
				"text": "首页",
				"iconPath": "/static/tab/index.png",
				"selectedIconPath": "/static/tab/index_active.png"
			},
			{
				"pagePath": "pages/home/<USER>/index",
				"text": "我的",
				"iconPath": "/static/tab/home.png",
				"selectedIconPath": "/static/tab/home_active.png"
			}
		]
	},
	
	// 配置分包
	"subPackages": [{
			"root": "Coursedetails",
			"name": "couser",
			"pages": [{
					"path": "feedback/index",
					"style": {
						"navigationBarTitleText": "反馈",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "leave/leave",
					"style": {
						"navigationBarTitleText": "请假",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "share/share",
					"style": {
						"navigationBarTitleText": "学习分享",
						"navigationStyle": "default",
						"navigationBarBackgroundColor": "#FFFFFF",
						"navigationBarTextStyle": "black",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "share/reviewshare",
					"style": {
						"navigationBarTitleText": "复习分享",
						"navigationStyle": "default",
						"navigationBarBackgroundColor": "#FFFFFF",
						"navigationBarTextStyle": "black",
						"enablePullDownRefresh": false
					}
	
				}
			]
		},
		{
			"root": "Personalcenter",
			"name": "personal",
			"pages": [{
					"path": "my/mystudent",
					"style": {
						"navigationBarTitleText": "我的学员",
						"navigationStyle": "default",
						"navigationBarBackgroundColor": "#FFFFFF",
						"navigationBarTextStyle": "black",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "my/mystudentAdd",
					"style": {
						"navigationBarTitleText": "新增学员",
						"navigationStyle": "default",
						"navigationBarBackgroundColor": "#FFFFFF",
						"navigationBarTextStyle": "black",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "news/news",
					"style": {
						"navigationBarTitleText": "消息",
						"navigationStyle": "default",
						"navigationBarBackgroundColor": "#FFFFFF",
						"navigationBarTextStyle": "black",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "studyPrint/studyPrint",
					"style": {
						"navigationBarTitleText": "学习内容打印",
						"navigationStyle": "default",
						"navigationBarBackgroundColor": "#FFFFFF",
						"navigationBarTextStyle": "black",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "studyPrint/studyContentPrint",
					"style": {
						"navigationBarTitleText": "学习内容打印",
						"navigationStyle": "default",
						"navigationBarBackgroundColor": "#FFFFFF",
						"navigationBarTextStyle": "black",
						"enablePullDownRefresh": false
						// "backgroundColor": "#ECECEC"
					}
				}
			]
		}
	],
	
	"easycom": {
		"^u-(.*)": "uview-ui/components/u-$1/u-$1.vue"
	},
	"condition" : { //模式配置，仅开发期间生效
		"current": 0, //当前激活的模式(list 的索引项)
		"list": [
			{
				"name": "", //模式名称
				"path": "", //启动页面，必选
				"query": "" //启动参数，在页面的onLoad函数里面得到
			}
		]
	}
}
