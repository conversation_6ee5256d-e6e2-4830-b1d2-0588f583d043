<template>
  <view class="audit-card" :style="{ backgroundImage: backgroundUrl }">
    <!-- 学员信息 -->
    <view class="flex items-center" style="height: 80rpx">
      <view class="label-text flex">
        <view class="label-text">学员姓名</view>
        <view class="value-text">{{ cardItem.studentName }}</view>
        <!-- 认领成功 显示已失效的条件 认领/claimType=1  认领/申诉 状态claimStatus=0  审核状态appealStatus = '1'  此时显示 -->
        <!-- 是认领的状态claimType == 1且申诉状态appealStat为空 且 认领无效cardItem.claimStatus == 0 -->
        <image
          style="width: 66rpx; height: 66rpx; margin-left: -12rpx"
          appealStatus
          v-if="cardItem.claimType == 1 && cardItem.claimStatus == 0 && cardItem.appealStatus == ''"
          src="https://document.dxznjy.com/dxSelect/f2e240ac-413e-4256-bc97-4037c99c13e6.png"
        />
        <!-- 申诉状态  claimType=2  appealStatus=1 claimStatus=0无效 此时显示 -->
        <!-- 是申诉状态claimType == 2且申诉状态appealStatus为空 且 认领无效cardItem.claimStatus == 0 -->
        <image
          style="width: 66rpx; height: 66rpx; margin-left: -12rpx"
          v-if="cardItem.claimType == 2 && cardItem.appealStatus == 1 && cardItem.claimStatus == 0"
          src="https://document.dxznjy.com/dxSelect/f2e240ac-413e-4256-bc97-4037c99c13e6.png"
        />
      </view>
      <view class="info" @click="submitButton">{{ showStatusText }}</view>
    </view>
    <!-- 操作区域 -->
    <view class="flex flex-x-a items-center mt-40">
      <view class="label-text" style="width: 140rpx; text-align: right">学员电话</view>
      <view class="dashed-line"></view>
      <view class="label-text time-text" style="width: 140rpx">{{ cardItem.claimType == 1 ? '认领时间' : '申诉时间' }}</view>
      <view class="dashed-line"></view>
      <view class="f-28 lh-40 h-40" :class="['submit-btn-' + cardItem.showStatus]" style="width: 140rpx">
        {{ buttonText }}
      </view>
    </view>

    <!-- 数据展示 -->
    <view class="flex items-center flex-x-b mt-20">
      <view class="value-text" v-if="cardItem.mobile">{{ cardItem.mobile }}</view>
      <view v-else>-</view>
      <view class="value-text" style="margin-left: -2%" v-if="cardItem.applyTime">{{ cardItem.applyTime }}</view>
      <view v-else>-</view>
      <view v-if="cardItem.auditTime" :class="['value-text', textTimeColorComputed]">
        {{ cardItem.auditTime }}
      </view>
      <view v-else class="empty-card">-</view>
    </view>
  </view>
</template>

<script>
  export default {
    name: 'AuditCard',
    props: {
      cardItem: {
        type: Object,
        default: {}
      }
    },
    data: {
      statusMap: {
        0: '申诉中',
        1: '申诉成功',
        2: '申诉失败',
        claimType1: '认领成功',
        claimType2: '申诉中'
      },
      textTimeColorStatus: ''
    },
    computed: {
      // 根据 appealStatus 返回对应的背景图 URL
      backgroundUrl() {
        const appealStatus = this.cardItem?.showStatus;
        let imageUrl = '';
        const statusMap = {
          1: 'https://document.dxznjy.com/dxSelect/12118760-683d-4e73-9fc7-2112c2fdcf5d.png',
          2: 'https://document.dxznjy.com/dxSelect/a598824d-cd74-4a5a-86c2-a1e0565baabf.png',
          3: 'https://document.dxznjy.com/course/54955dfab9024f738d3886a5238f7081.png',
          4: 'https://document.dxznjy.com/course/a5951d4f6346462fbe3c1b0a33212f43.png'
        };
        if (appealStatus in statusMap) {
          imageUrl = statusMap[appealStatus];
        }
        return `url(${imageUrl})`;
      },

      // 根据 appealStatus 返回按钮文案
      buttonText() {
        const statusTextMap = {
          1: '认领成功',
          2: '申诉中',
          3: '申诉成功',
          4: '申诉失败'
        };
        return statusTextMap[this.cardItem.showStatus] || '';
      },

      textTimeColorComputed() {
        const textNumber = 'text-time-' + this.cardItem?.showStatus;
        return textNumber;
      },

      showStatusText() {
        const statusTextMap = {
          2: '查看申诉信息',
          3: '查看申诉信息',
          4: '查看驳回信息'
        };
        return statusTextMap[this.cardItem.showStatus] || '';
      }
    },
    methods: {
      submitButton() {
        if (this.cardItem.claimType === '2') {
          this.$emit('submitButton', this.cardItem);
        }
      }
    }
  };
</script>
<style lang="scss" scoped>
  .audit-card {
    width: 686rpx;
    height: 242rpx;
    background-color: #f3f3f3;
    border-radius: 20rpx;
    padding: 0 24rpx;
    box-sizing: border-box;
    background-repeat: no-repeat;
    background-position: center;
    background-size: cover;

    .submit-btn-1 {
      font-size: 24rpx;
      color: #4ab280;
      background-color: #eefff7;
      width: 130rpx;
      height: 50rpx;
      line-height: 50rpx;
      text-align: center;
      background: #eefff7;
      border-radius: 8rpx;
      border: 1rpx solid #52bd8b;
    }

    .submit-btn-2 {
      font-size: 24rpx;
      color: #4580e6;
      background-color: #f3f7ff;
      width: 130rpx;
      height: 50rpx;
      line-height: 50rpx;
      text-align: center;
      background: #f3f7ff;
      border-radius: 8rpx;
      border: 1rpx solid #7ba5ed;
    }

    .submit-btn-3 {
      font-size: 24rpx;
      color: #fd954e;
      background-color: #fff9f1;
      width: 130rpx;
      height: 50rpx;
      line-height: 50rpx;
      text-align: center;
      background: #fff9f1;
      border-radius: 8rpx;
      border: 1rpx solid #fdba5e;
    }

    .submit-btn-4 {
      width: 130rpx;
      height: 50rpx;
      line-height: 50rpx;
      text-align: center;
      background: #fff4f1;
      border-radius: 8rpx;
      border: 1rpx solid #f36354;
      font-size: 24rpx;
      color: #f36354;
      background-color: #fff4f1;
    }

    .submit-btn {
      font-size: 24rpx;
      color: #f36354;
      background-color: #fff4f1;
    }

    .text-time-1 {
      color: #4ab280;
    }

    .text-time-2 {
      color: #4580e6;
    }

    .text-time-3 {
      color: #fd954e;
    }

    .text-time-4 {
      color: #f36354;
    }
  }

  .label-text {
    font-size: 28rpx;
    line-height: 40rpx;
    color: #999a9d;
    margin-right: 10rpx;
  }

  .time-text {
    font-size: 28rpx;
    line-height: 40rpx;
    color: #999a9d;
    text-align: center;
  }

  .info {
    font-size: 28rpx;
    line-height: 40rpx;
    color: #999a9d;
  }

  .value-text {
    font-size: 28rpx;
    line-height: 40rpx;
    color: #555555;
  }

  .empty-card {
    width: 120rpx;
    text-align: center;
  }

  .dashed-line {
    width: 60rpx;
    border: 1rpx dotted #d5d5d5;
    /* 宽度、样式、颜色 */
    margin-right: 10rpx;
  }
</style>
