<template>
  <view class="content f-28">
    <view class="head_tab">
      <view :class="{ active: selectIndex === 0 }" @click="selectedTab(0)" v-if="isContractCreator">全部</view>
      <view :class="{ active: selectIndex === 1 }" @click="selectedTab(1)">待签署</view>
      <view :class="{ active: selectIndex === 2 }" @click="selectedTab(2)">已签署</view>
      <view :class="{ active: selectIndex === 3 }" @click="selectedTab(3)" v-if="isContractCreator">已撤销</view>
      <view :class="{ active: selectIndex === 5 }" @click="selectedTab(5)" v-if="isContractCreator">已过期</view>
      <view :class="{ active: selectIndex === 7 }" @click="selectedTab(7)" v-if="isContractCreator">已拒签</view>
    </view>
    <view class="" v-if="listS.data && listS.data.length > 0">
      <view class="list_view" v-for="(item, index) in listS.data" :key="index">
        <view class="list_head">
          {{ getName(item.templateType) }}
          <view class="lc_yellow" v-if="item.status === 1">
            {{ item.statusText }}
          </view>
          <view class="lc_yellow lc_g" v-if="item.status === 2">
            {{ item.statusText }}
          </view>
          <view class="lc_yellow lc_gray" v-if="item.status === 3">
            {{ item.statusText }}
          </view>
          <view class="lc_yellow lc_gray" v-if="item.status === 5">
            {{ item.statusText }}
          </view>
          <view class="lc_yellow lc_gray" v-if="item.status === 7">
            {{ item.statusText }}
          </view>
        </view>
        <view class="list_context">
          <text class="list_name">生成时间：</text>
          <view class="bg_green">
            {{ item.createTime }}
          </view>
        </view>
        <view class="list_context">
          <text class="list_name">甲方签署人姓名：</text>
          <view class="bg_green">
            {{ item.firstPartySignName }}
          </view>
        </view>
        <view class="list_context">
          <text class="list_name">甲方签署人手机号：</text>
          <view class="bg_green">
            {{ item.firstPartySignPhone }}
          </view>
        </view>
        <view class="list_context">
          <text class="list_name">乙方企业名称：</text>
          <view class="bg_green">
            {{ item.secondPartyEnterpriseName }}
          </view>
        </view>
        <view class="list_context">
          <text class="list_name">签署来源：</text>
          <view class="bg_green">
            {{ item.signSource == 1 ? '腾讯电子签' : 'e签宝' }}
          </view>
        </view>
        <view class="list_context" v-if="item.signStatus === 2">
          <text class="list_name">签署时间：</text>
          <view class="bg_green">
            {{ item.signTime }}
          </view>
        </view>
        <view class="list_bot">
          <view class="btn_b b_r" v-if="item.signStatus === 1 && item.signStatus === 1" @click="sign(item)">签署</view>
          <view class="btn_b b_l" @click="checkContract(item)" v-if="item.signStatus === 2">查看合同</view>
          <view class="btn_b b_l" v-if="item.signStatus === 1 && isContractCreator" @click="cancel(item)">撤销</view>
        </view>
      </view>
    </view>
    <view v-else class="curriculum_css_no pt-30 pb-55 f-28">
      <image class="curriculum_image" src="https://document.dxznjy.com/course/ac587707bf314badadb28a158852c77d.png"></image>
      <view class="c-66 f-24 mtb-25">暂无明细</view>
    </view>
    <view v-if="no_more && listS.data.length > 0">
      <u-divider text="到底了"></u-divider>
    </view>
  </view>
</template>

<script>
  export default {
    data() {
      return {
        numC: 120,
        selectIndex: 0,
        isContractCreator: '', //是否合同发起人
        page: 1,
        no_more: false,
        listS: {},
        signerSignUrl: ''
      };
    },
    onLoad(option) {
      if (option != null && option.status != undefined) {
        if (option.status == '1') {
          this.isContractCreator = true;
        } else {
          this.isContractCreator = false;
        }
      }
    },
    onShow() {
      if (this.isContractCreator) {
        this.fetchContractList();
      } else {
        this.selectedTab(1);
      }
    },

    computed: {
      getName() {
        const list = {
          EELMSContract: '学习管理系统合同',
          EPLMSContract: '学习管理系统合同',
          EEChannelPartnerAgreement: '渠道合作伙伴协议合同',
          PEEChannelPartnerAgreement: '渠道合作伙伴协议合同',
          EPPAAgreement: '推广大使协议合同',
          PAAgreement: '推广大使协议合同'
        };
        return (val) => list[val];
      }
    },
    onReachBottom() {
      if (this.selectIndex == 0) {
        if (this.page >= this.listS.totalPage) {
          this.no_more = true;
          return false;
        }
        this.fetchContractList(true, ++this.page);
      }
      if (this.selectIndex == 1) {
        if (this.page >= this.listS.totalPage) {
          this.no_more = true;
          return false;
        }
        this.fetchContractList(true, ++this.page, 1);
      }
      if (this.selectIndex == 2) {
        if (this.page >= this.listS.totalPage) {
          this.no_more = true;
          return false;
        }
        this.fetchContractList(true, ++this.page, 2);
      }
      if (this.selectIndex == 2) {
        if (this.page >= this.listS.totalPage) {
          this.no_more = true;
          return false;
        }
        this.fetchContractList(true, ++this.page, 3);
      }
      if (this.selectIndex == 2) {
        if (this.page >= this.listS.totalPage) {
          this.no_more = true;
          return false;
        }
        this.fetchContractList(true, ++this.page, 4);
      }
      if (this.selectIndex == 2) {
        if (this.page >= this.listS.totalPage) {
          this.no_more = true;
          return false;
        }
        this.fetchContractList(true, ++this.page, 5);
      }
    },
    methods: {
      //获取合同信息
      fetchContractList(isPage, page, status) {
        uni.showLoading({
          title: '加载中...'
        });
        let params = {
          pageNum: page || 1,
          pageSize: 10,
          // isContractCreator: this.isContractCreator,
          // userId: uni.getStorageSync('user_id') ? uni.getStorageSync('user_id') : '',
          mobile: uni.getStorageSync('phone') ? uni.getStorageSync('phone') : '',
          status: status
        };
        if (status) {
          params.status = status;
        } else {
          // params.status = 0;
          delete params.status;
        }
        console.log('🚀 ~ fetchContractList ~ params:', params);

        this.$httpUser.get('znyy/sign/contract/getContractDetail', params).then((res) => {
          if (res) {
            if (isPage) {
              let old = this.listS.data;
              // this.listS.totalPage = Math.ceil(res.data.data.total / 10);
              this.listS.data = [...old, ...res.data.data.data];
            } else {
              this.listS = res.data.data;
            }
          }
          uni.hideLoading();
        });
      },
      selectedTab(index) {
        this.selectIndex = index;
        this.page = 1;
        if (index == 0) {
          this.fetchContractList();
        } else {
          this.fetchContractList(false, 1, index);
        }
      },
      createContract() {
        uni.navigateTo({
          url: '/signature/contract/createContract'
        });
      },
      async checkContract(item) {
        if (item.contractSignUrl == '') {
          uni.showToast({
            title: '暂无合同链接'
          });
          return;
        }

        const url = item.contractSignUrl;
        // #ifdef APP-PLUS
        // App 环境
        plus.runtime.openURL(url);
        // #endif

        // #ifdef MP-WEIXIN
        // 微信小程序环境
        uni.setClipboardData({
          data: url,
          success: () => {
            uni.showModal({
              title: '提示',
              content: '链接已复制，请在浏览器中打开',
              showCancel: false
            });
          },
          fail: (fail) => {
            console.error('复制链接失败:', fail);
          }
        });
        // #endif
      },
      sign(item) {
        if (item.contractSignUrl == '') {
          uni.showToast({
            title: '暂无签署链接'
          });
        } else {
          uni.navigateTo({
            url: '/signature/contract/signingPage?url=' + encodeURIComponent(JSON.stringify(item.contractSignUrl))
          });
        }
      },
      async cancel(item) {
        this.$httpUser
          .post('zx/wap/contract/revoke', {
            userId: uni.getStorageSync('user_id') ? uni.getStorageSync('user_id') : '',
            contractId: item.id
          })
          .then((res) => {
            console.log(res);
            if (res.data.code === 20000) {
              uni.showToast({
                title: '撤销成功',
                icon: 'success'
              });
              setTimeout(() => {
                this.fetchContractList();
              }, 500);
            } else {
              uni.showToast({
                title: res.data.message,
                icon: 'none'
              });
            }
          });
      }
    }
  };
</script>

<style scoped>
  .content {
    padding: 32rpx;
  }

  .head_tab {
    height: 46rpx;
    padding: 0 10rpx;
    color: #5a5a5a;
    font-size: 28rpx;
    box-sizing: border-box;
    margin: 32rpx 0;
    display: flex;
    justify-content: space-evenly;
  }

  .active {
    border-bottom: 8rpx solid #339378;
    color: #333333;
    font-weight: 900;
    border-radius: 2rpx;
  }

  .list_view {
    width: 686rpx;
    border-radius: 24rpx;
    padding: 32rpx 24rpx;
    background-color: #ffffff;
    box-sizing: border-box;
    margin-bottom: 32rpx;
    letter-spacing: 3rpx;
  }

  .list_head {
    color: #333333;
    font-size: 32rpx;
    font-weight: 800;
    display: flex;
    justify-content: space-between;
    padding: 0 0 24rpx 0;
    border-bottom: 0.5px solid #f6f7f9;
  }

  .list_context {
    color: #555555;
    margin-top: 36rpx;
    display: flex;
  }

  .list_name {
    width: 44%;
  }

  .list_context text {
    /* width:172rpx ;
	background-color: pink; */
  }

  .lc_yellow {
    color: #fd9b2a;
    text-align: center;
    font-weight: 300;
    font-size: 26rpx;
    width: 116rpx;
    height: 44rpx;
    line-height: 44rpx;
    border: 1px solid #ffe1be;
    background-color: #fdf6ed;
    border-radius: 8rpx;
    margin-right: 16rpx;
  }

  .lc_green {
    color: #006f57;
  }

  .lc_g {
    color: #81e2af;
    border: 1px solid #81e2af;
    background-color: #ecfbf3;
  }

  .lc_gray {
    color: #cccccc;
    border: 1px solid #cccccc;
    background-color: #f7f7f7;
  }

  .list_bot {
    margin-top: 62rpx;
    display: flex;
    flex-direction: row-reverse;
  }

  .btn_b {
    width: 196rpx;
    height: 60rpx;
    border-radius: 60rpx;
    line-height: 60rpx;
    text-align: center;
    margin-left: 32rpx;
  }

  .bg_green {
    flex: 1;
  }

  .b_l {
    background-color: #fff;
    color: #4e9f87;
    border: 1px solid #7baea0;
    margin-left: 32rpx;
  }

  .b_r {
    background-image: linear-gradient(to bottom, #88cfba, #1d755c);
    color: #ffffff;
  }

  .curriculum_css_no {
    position: relative;
    width: 710rpx;
    margin: auto;
    margin-top: 400rpx;
    text-align: center;
  }

  .curriculum_image {
    width: 122rpx;
    height: 114rpx;
    display: block;
    margin: 16rpx auto;
  }
</style>
