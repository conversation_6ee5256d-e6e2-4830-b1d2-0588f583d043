<!-- 渐变色柱状图组件  2020-08-22  jp -->
<template>
	<view>

            <view class="charts" :style="'width:'+widths+'rpx;height:'+height+'rpx'">
            	<view class="charts_h" v-for="(item,index) in list" :key="index" :style="'width:'+x_width+'rpx'">
            		<view class="charts_hx">
            			<text style="font-size:22rpx;color:#666;">{{item[Y.value]+Y.units}}</text>
            			<view :style="'width:'+proportion+'%;margin-left:'+((100-proportion)/2)+'%;'+'height:'+item.jpHeightCalculation +'%'"
            			 style="height: 100%;display: flex; justify-content: flex-end; flex-direction: column;">
            				<view class="charts_hxs" style="height: 100%;" :style="[arrowStyle(item)]"></view>
            			</view>
            		</view>
            	</view>
            </view>
            <!-- 线 -->
             <view style="height: 1px;width: 100%;background-color:rgba(227, 227, 227, 0.5);"></view>

		
        <!-- 文本 -->
        <view  style="display: flex;justify-content: center;margin-top: 10rpx;">
            <view v-for="(item,index) in list" :key="index">
                <view :class="index == 0?'m-l30':'m-r68'">
                    <view class="textX" style="font-size:24rpx;color:#666;">{{item[X.value]+X.units}}</view>
                </view>
            </view>
        </view>
	</view>
</template>

<script>
	export default {
		props: {
			Y: { 
				type: Object,
				default () {
					return {
						value: 'Y',
						size: 22,
						units: '',
						color: '#333'
					}
				}
			},
			X: { //多选初始值
				type: Object,
				default () {
					return {
						value: 'X',
						size: 22,
						units: '',
						color: '#333'
					}
				}
			},
			list: { //数据
				type: Array,
				default () {
					return []
				}
			},
			width: { //宽
				type: Number,
				default () {
					return 750
				}
			},
			height: { //高
				type: Number,
				default () {
					return 500
				}
			},
			proportion: { //宽度占比
				type: Number,
				default () {
					return 20
				}
			},
			x_width: { //宽度
				type: Number,
				default () {
					return 100
				}
			},
			bgColor: {
				type: String,
				default () {
					return '#fff'
				}
			},
			btColor: {
				type: String,
				default () {
					return '#fff'
				}
			},
            line:{
                type: Array,
                default () {
                	return []
                }
            }
		},
		data() {
			return {
				widths: 750
			}
		},
		mounted() {
			this.widths = (this.width > this.x_width * this.list.length) ? this.width : (this.x_width * this.list.length)
			this.setHeight()
		},
		watch: {
			list() {
				this.widths = (this.width > this.x_width * this.list.length) ? this.width : (this.x_width * this.list.length)
				this.setHeight()
			}
		},
		methods: {
			setHeight() {
				let h = 0
				this.list.forEach((el, index) => {
					if (el[this.Y.value] > h) {
						h = el[this.Y.value]
					}
				})
				this.list.forEach((el, index) => {
					if (el[this.Y.value] > 0) {
						el.jpHeightCalculation = ((el[this.Y.value] / h) * 100) + 1
					} else {
						el.jpHeightCalculation = 1
					}
				})
			},
			arrowStyle(item) {
				let style = {}
				if (this.bgColor) {
					style = {
						'background': `-webkit-linear-gradient(top, ${this.btColor}, ${this.bgColor})`,
						/* Safari 5.1 - 6.0 */
						'background': `-o-linear-gradient(top, ${this.btColor}, ${this.bgColor})`,
						/* Safari 5.1 - 6.0 */
						'background': `-moz-linear-gradient(top,${this.btColor}, ${this.bgColor})`,
						/* Safari 5.1 - 6.0 */
						'background': `linear-gradient(to top, ${this.btColor}, ${this.bgColor})`
					}
				}
				return style;
			}
		}
	}
</script>

<style lang="scss" scoped>
    .textX{
        text-align: center;
        width: 250rpx;
    }
	.charts {
		width: 100%;
		height: 100%;
		display: flex;
		justify-content: space-evenly;
	
		.charts_h {
			text-align: center;
			height: 100%;
	
			.charts_hx {
				height: 100%;
				display: flex;
				justify-content: flex-end;
				flex-direction: column;
	
				.charts_hxs {
					width: 100%;
					display: inline-block;
				}
			}
		}
	}
</style>
