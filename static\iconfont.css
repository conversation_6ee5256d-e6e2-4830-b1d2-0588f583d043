@font-face {
  font-family: "iconfont"; /* Project id 3642671 */
  src: url('/static/iconfont.ttf') format('truetype');
}

.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-clock1:before {
  content: "\e61b";
}

.icon-clock2:before {
  content: "\e600";
}

.icon-link:before {
  content: "\e746";
}

.icon-photo:before {
  content: "\e61c";
}

.icon-share:before {
  content: "\e60b";
}

.icon-headphones:before {
  content: "\e634";
}

.icon-love:before {
  content: "\e610";
}

.icon-icontychatbubble2:before {
  content: "\e637";
}

.icon-headset:before {
  content: "\e8e6";
}

.icon-usercheck:before {
  content: "\e6b8";
}

.icon-close-circle:before {
  content: "\e6be";
}

.icon-calendar-copy:before {
  content: "\e78c";
}

.icon-clock:before {
  content: "\e631";
}

