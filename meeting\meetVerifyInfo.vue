<template>
  <view class="bg" :style="{ height: useHeight + 'rpx' }">
    <view class="col-12">
      <view class="text-title">核销码</view>
      <view class="mt-30 flex-c">
        <uqrcode v-if="imgCode" ref="uqrcode" canvas-id="qrcode" :value="imgCode"></uqrcode>
        <view v-if="!isEnable">
          <image style="width: 320rpx; height: 320rpx" :src="imgHost + 'dxSelect/fourthEdition/meeting_expired_code.png'"></image>
        </view>
      </view>

      <view v-if="isType == 1" class="flex-space-between" style="margin-top: 100rpx">
        <text>名称</text>
        <text>{{ meetingItem ? meetingItem.meetingQrName : '-' }}</text>
      </view>
      <view class="flex-space-between" :style="{ marginTop: (isType == 1 ? 40 : 100) + 'rpx' }">
        <text>订单编号</text>
        <text>{{ details ? details.orderNo : '-' }}</text>
      </view>
      <view class="flex-space-between" style="margin-top: 40rpx">
        <text>核销码编号</text>
        <text>{{ details ? details.meetingQrId : '-' }}</text>
      </view>
      <view v-if="isType == 1" class="flex-space-between" style="margin-top: 40rpx">
        <text>购买人信息</text>
        <text>{{ details ? `${details.buyerName}(${details.buyerPhone})` : '-' }}</text>
      </view>
      <view class="flex-space-between" style="margin-top: 40rpx">
        <text>推荐人信息</text>
        <text>{{ details ? `${details.referrerName}(${details.referrerPhone})` : '无' }}</text>
      </view>
      <view class="flex-space-between" style="margin-top: 40rpx">
        <text>创建时间</text>
        <text>{{ details ? details.createdTime : '-' }}</text>
      </view>
      <view class="flex-space-between" style="margin-top: 40rpx">
        <text>订单备注</text>
        <text class="remake">{{ details ? details.remark : '无' }}</text>
      </view>
    </view>
    <view v-if="isType == 1" class="gradient_btn mb-30" @click="sureOption">确定核销</view>
  </view>
</template>

<script>
  const { $http } = require('@/util/methods.js');
  import uqrcode from './components/Sansnn-uQRCode/components/uqrcode/uqrcode.vue';
  export default {
    components: { uqrcode },
    data() {
      return {
        imgHost: getApp().globalData.imgsomeHost,
        screenHeight: '', // 屏幕高度
        useHeight: '', // 屏幕高度

        isType: 0, //0等待核销   1立即核销
        isEnable: true, //暂无失效状态

        meetingQrId: '', // 核销码id
        details: null,

        imgCode: '',
        meetingItem: null,

        courseId: ''
      };
    },
    onLoad(option) {
      this.isType = option.isType;
      this.meetingQrId = option.meetingQrId;
      this.courseId = option.courseId ? option.courseId : '';
      this.getOrderDetails();
    },

    onReady() {
      this.getHeight();
      uni.getSystemInfo({
        success: (res) => {
          // 可使用窗口高度，将px转换rpx
          let h = res.windowHeight * (750 / res.windowWidth);
          this.useHeight = h - 90;
        }
      });
    },

    methods: {
      getHeight() {
        // 获取系统信息
        const systemInfo = uni.getSystemInfoSync();
        // 获取屏幕高度
        this.screenHeight = systemInfo.windowHeight;
        this.screenWidth = systemInfo.windowWidth - 60;
      },

      // 订单编号orderNo
      // 核销码orderId
      async getOrderDetails() {
        uni.showLoading({
          title: '加载中...',
          mask: true
        });
        let _this = this;
        let res = await $http({
          url: 'zx/order/userMeetingOrderDetail',
          data: {
            meetingQrId: _this.meetingQrId
          }
        });
        console.log('🚀 ~ getOrderDetails ~ res:', res);
        if (res && res.status == 1) {
          _this.details = res.data;
          _this.imgCode = 'zx#' + _this.details.meetingQrId;
          _this.meetingItem = res.data;
        }
      },

      cancel() {
        this.$refs.suggestSucc.close();
        uni.navigateBack();
      },

      async sureOption() {
        let _this = this;
        uni.showLoading({
          title: '核销中...',
          mask: true
        });
        const res = await $http({
          url: 'zx/order/verificationMeetingByMeetingQr',
          data: {
            meetingQrId: _this.imgCode,
            courseId: _this.courseId
          }
        });
        if (res && res.status == 1) {
          uni.showToast({
            icon: 'success',
            title: '核销成功'
          });
          setTimeout(() => {
            uni.navigateBack();
          }, 1000);
        } else {
          uni.showToast({
            icon: 'none',
            title: res.data.message
          });
        }
      }
    }
  };
</script>

<style>
  .bg {
    margin: 20rpx 30rpx 0 30rpx;
    border-radius: 14rpx;
    background-color: #fff;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: space-between;
  }

  .gradient_btn {
    text-align: center;
    background: linear-gradient(180deg, #88cfba 0%, #1d755c 100%);
    border-radius: 45rpx;
    width: 630rpx;
    height: 90rpx;
    color: #ffffff;
    line-height: 90rpx;
    font-size: 30rpx;
  }

  .text-title {
    text-align: center;
    font-weight: 600;
    margin-top: 95rpx;
    font-size: 34rpx;
    color: #000000;
    line-height: 47rpx;
  }

  .flex-space-between {
    margin: 0 30rpx 0 30rpx;
    display: flex;
    justify-content: space-between;
  }
  .flex-space-between text {
    font-size: 30rpx;
    color: #333333;
    line-height: 42rpx;
  }

  .suggest {
    width: 670rpx;
    height: 380rpx;
    background-color: #fff;
    border-radius: 24rpx;
    display: flex;
    justify-content: center; /* 文本垂直居中对齐 */
    align-items: center; /* 文本水平居中对齐 */
  }

  .suggestTxt {
    margin-top: 30rpx;
    color: #999999;
    font-size: 28rpx;
  }

  .remake {
    text-align: end;
    word-break: break-all;
    width: 450rpx;
    white-space: pre-line;
  }
</style>
