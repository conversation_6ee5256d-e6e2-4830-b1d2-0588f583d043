<template>
  <view @longpress="longPress" style="background-color: #5b5cff; height: 1800rpx">
    <view v-if="switchstate">
      <image :src="path" mode="widthFix" style="width: 100%"></image>
      <l-painter
        isCanvasToTempFilePath
        ref="painter"
        @success="path = $event"
        custom-style="position: fixed; left: 200%"
        css="background-color: #5b5cff;width: 750rpx; padding-bottom: 40rpx;"
      >
        <l-painter-view css="position: relative;object-fit:fill;height:100%;background-image:url(https://document.dxznjy.com/applet/jiazhang/bgc.png)">
          <l-painter-view css="position: fixed;">
            <l-painter-image src="https://document.dxznjy.com/alading/pic/logo.png" css="margin-left: 50rpx; margin-top: 50rpx; width: 115rpx;" />

            <l-painter-view css="margin-top: 50rpx; padding-left: 20rpx;">
              <l-painter-text
                :text="backdetails.studentName + '的学习报告' || ' ' + '的学习报告'"
                css="opacity: 0.7; padding-bottom: 10rpx; color: #fff; font-size: 48rpx; display: block; margin-left: 30rpx;"
              />
              <l-painter-text text="Study Report" css="opacity: 0.7;color: #fff; font-size: 30rpx; margin-left: 30rpx;" />
            </l-painter-view>
          </l-painter-view>

          <l-painter-view
            css="position: relative;margin-left: 25rpx; margin-top: 363rpx; padding: 32rpx; box-sizing: border-box; background: #fff; border-radius: 16rpx; width: 700rpx;"
          >
            <l-painter-image
              src="https://document.dxznjy.com/alading/pic/cattle.png"
              css="position: absolute;right:0rpx;top:-260rpx;object-fit: contain;width: 158rpx;"
            ></l-painter-image>
            <l-painter-view
              css="position: absolute;border-radius: 10rpx;top:-90rpx;left:-40rpx;width: 200rpx;height:70rpx;background: linear-gradient(,#F3C717 100%, #FEE506 100%)"
            >
              <l-painter-text text="学习详情" css="box-shadow: #999;margin-left: 35rpx;line-height: 70rpx;font-size: 32rpx;color:#fff;text-align: center;"></l-painter-text>
            </l-painter-view>
            <l-painter-view css="margin-top: 32rpx; color: #000;line-height: 1em;">
              <l-painter-text :text="'姓名：' + backdetails.studentName || ''" css="font-size: 30rpx;display: block; margin-bottom: 30rpx; " />
              <l-painter-text :text="'年级：' + backdetails.gradeName || ''" css="font-size: 30rpx;display: block; margin-bottom: 30rpx; " />
              <l-painter-text :text="'学习时间：' + backdetails.studyTime || ''" css="font-size: 30rpx;display: block; margin-bottom: 30rpx; " />
              <l-painter-text :text="'学习学时：' + backdetails.studyHour + '小时' || '' + '小时'" css="font-size: 30rpx;display: block; margin-bottom: 30rpx; " />
              <l-painter-text :text="'总报学时：' + backdetails.totalCourse + '小时' || '' + '小时'" css="font-size: 30rpx;display: block; margin-bottom: 30rpx; " />
              <l-painter-text :text="'所学内容：' + backdetails.studyContent || ''" css="font-size: 30rpx;display: block; margin-bottom: 30rpx; " />
              <l-painter-text
                v-if="backdetails.isWord"
                :text="'所学词库：' + backdetails.studyBooks + '个' || '' + '个'"
                css="font-size: 30rpx;display: block; margin-bottom: 30rpx; "
              />
              <l-painter-text
                v-if="backdetails.isWord"
                :text="'复习词汇：' + backdetails.reviewWords + '个' || '' + '个'"
                css="font-size: 30rpx;display: block; margin-bottom: 30rpx; "
              />
              <l-painter-text
                v-if="backdetails.isWord"
                :text="'复习遗忘词汇：' + backdetails.forgetWords + '个' || '' + '个'"
                css="font-size: 30rpx;display: block; margin-bottom: 30rpx; "
              />
              <l-painter-text
                v-if="backdetails.isWord"
                :text="'复习遗忘率：' + backdetails.forgetRate + '%' || '' + '%'"
                css="font-size: 30rpx;display: block; margin-bottom: 30rpx; "
              />
              <l-painter-text
                v-if="backdetails.isWord"
                :text="'学新词汇：' + backdetails.newWords + '个' || '' + '个'"
                css="font-size: 30rpx;display: block; margin-bottom: 30rpx; "
              />
              <l-painter-text
                v-if="backdetails.isWord"
                :text="'学新遗忘词汇：' + backdetails.newForget + '个' || '' + '个'"
                css="font-size: 30rpx;display: block; margin-bottom: 30rpx; "
              />
              <l-painter-text
                v-if="backdetails.isWord"
                :text="'学新遗忘率：' + backdetails.newForgetRate + '%' || '' + '%'"
                css="font-size: 30rpx;display: block; margin-bottom: 20rpx; "
              />
              <l-painter-text
                v-if="backdetails.isWord"
                :text="'今日共识记词汇（复习遗忘词汇+学新词汇）：' + backdetails.todayWords + '个' || '' + '个'"
                css="font-size: 30rpx;display: block; margin-bottom: 10rpx; line-height: 50rpx;"
              />
              <l-painter-text
                v-if="backdetails.isWord"
                :text="'学习进度：' + backdetails.learnSchedule + '%' || '' + '%'"
                css="font-size: 30rpx;display: block; margin-bottom: 20rpx; line-height: 50rpx;"
              />
            </l-painter-view>
            <l-painter-view>
              <l-painter-text
                :text="'教练评语：' + backdetails.feedback || ' '"
                css="font-size: 30rpx;display: block; line-height: 50rpx;padding-top: 20rpx;border-top: 1px dashed #c2c2c2;"
              />
            </l-painter-view>
          </l-painter-view>

          <!-- <l-painter-view
            css="position: relative;margin-left: 20rpx; margin-top: 60rpx;  margin-bottom: 40rpx; padding: 80rpx 38rpx 40rpx 44rpx; box-sizing: border-box; background: #fff; border-radius: 16rpx; width: 710rpx;"
          >
            <l-painter-view
              css="position: absolute;border-radius: 10rpx;top:-140rpx;left:-55rpx;width: 200rpx;height:70rpx;background: linear-gradient(,#F3C717 100%, #FEE506 100%)"
            >
              <l-painter-text text="学习详情" css="box-shadow: #999;margin-left: 35rpx;line-height: 70rpx;font-size: 32rpx;color:#fff;text-align: center;"></l-painter-text>
            </l-painter-view>
            <l-painter-text css="color: #000; line-height: 1.8em; font-size: 32rpx; display: block; box-sizing: border-box" text="阿拉鼎星球会员小程序"></l-painter-text>
            <l-painter-text css="color: #999; line-height: 1.8em; font-size: 26rpx; width: 488rpx;" text="还在等什么？试一下就知道了，成为学霸没有想象中的那么难"></l-painter-text>
            <l-painter-image src="https://document.dxznjy.com/alading/correcting/qr_code.png" css="position: absolute;width: 140rpx;bottom: 42rpx;right: 30rpx;" />
          </l-painter-view> -->
        </l-painter-view>
      </l-painter>
    </view>

    <view v-else>
      <image :src="path" mode="widthFix" style="width: 100%"></image>
      <l-painter
        isCanvasToTempFilePath
        ref="painter"
        @success="path = $event"
        custom-style="position: fixed; left: 200%"
        css="background-color: #5b5cff;width: 750rpx; padding-bottom: 40rpx;"
      >
        <l-painter-view css="position: relative;object-fit:fill;height:100%;background-image:url(https://document.dxznjy.com/applet/jiazhang/bgc.png)">
          <l-painter-view css="position: fixed;">
            <l-painter-image src="https://document.dxznjy.com/alading/pic/logo.png" css="margin-left: 50rpx; margin-top: 50rpx; width: 115rpx;" />

            <l-painter-view css="margin-top: 50rpx; padding-left: 20rpx;">
              <l-painter-text
                :text="totallist.studentName + '的学习报告' || ' ' + '的学习报告'"
                css="opacity: 0.7; padding-bottom: 10rpx; color: #fff; font-size: 48rpx; display: block; margin-left: 30rpx;"
              />
              <l-painter-text text="Study Report" css="opacity: 0.7;color: #fff; font-size: 30rpx; margin-left: 30rpx;" />
            </l-painter-view>
          </l-painter-view>

          <l-painter-view
            css="position: relative;margin-left: 25rpx; margin-top: 363rpx; padding: 32rpx; box-sizing: border-box; background: #fff; border-radius: 16rpx; width: 700rpx;"
          >
            <l-painter-image
              src="https://document.dxznjy.com/alading/pic/cattle.png"
              css="position: absolute;right:0rpx;top:-260rpx;object-fit: contain;width: 158rpx;"
            ></l-painter-image>
            <l-painter-view
              css="position: absolute;border-radius: 10rpx;top:-90rpx;left:-40rpx;width: 200rpx;height:70rpx;background: linear-gradient(,#F3C717 100%, #FEE506 100%)"
            >
              <l-painter-text text="学习详情" css="box-shadow: #999;margin-left: 35rpx;line-height: 70rpx;font-size: 32rpx;color:#fff;text-align: center;"></l-painter-text>
            </l-painter-view>
            <l-painter-view css="margin-top: 32rpx; color: #000;line-height: 1em;">
              <l-painter-text :text="'姓名：' + totallist.studentName || ''" css="font-size: 30rpx;display: block; margin-bottom: 30rpx; " />
              <l-painter-text :text="'年级：' + totallist.gradeName || ''" css="font-size: 30rpx;display: block; margin-bottom: 30rpx; " />
              <l-painter-text :text="'所学内容：' + totallist.studyBooks || ''" css="font-size: 30rpx;display: block; margin-bottom: 30rpx; " />
              <l-painter-text :text="'学习进度：' + totallist.learnSchedule + '%' || '' + '%'" css="font-size: 30rpx;display: block; margin-bottom: 30rpx; " />
              <l-painter-text :text="'复习词汇：' + totallist.reviewWords + '个' || '' + '个'" css="font-size: 30rpx;display: block; margin-bottom: 30rpx; " />
              <l-painter-text :text="'复习遗忘词汇：' + totallist.forgetWords + '个' || '' + '个'" css="font-size: 30rpx;display: block; margin-bottom: 30rpx; " />
              <l-painter-text :text="'复习遗忘率：' + totallist.forgetRate + '%' || '' + '%'" css="font-size: 30rpx;display: block; margin-bottom: 30rpx; " />
              <l-painter-text :text="'学新词汇：' + totallist.newWords + '个' || '' + '个'" css="font-size: 30rpx;display: block; margin-bottom: 30rpx; " />
              <l-painter-text :text="'学新遗忘词汇：' + totallist.newForget + '个' || '' + '个'" css="font-size: 30rpx;display: block; margin-bottom: 30rpx; " />
              <l-painter-text :text="'学新遗忘率：' + totallist.newForgetRate + '%' || '' + '%'" css="font-size: 30rpx;display: block;  " />
            </l-painter-view>
          </l-painter-view>

          <!-- <l-painter-view
            css="position: relative;margin-left: 20rpx; margin-top: 60rpx;  margin-bottom: 40rpx; padding: 80rpx 38rpx 40rpx 44rpx; box-sizing: border-box; background: #fff; border-radius: 16rpx; width: 710rpx;"
          >
            <l-painter-view
              css="position: absolute;border-radius: 10rpx;top:-140rpx;left:-55rpx;width: 200rpx;height:70rpx;background: linear-gradient(,#F3C717 100%, #FEE506 100%)"
            >
              <l-painter-text text="学习详情" css="box-shadow: #999;margin-left: 35rpx;line-height: 70rpx;font-size: 32rpx;color:#fff;text-align: center;"></l-painter-text>
            </l-painter-view>
            <l-painter-text css="color: #000; line-height: 1.8em; font-size: 32rpx; display: block; box-sizing: border-box" text="鼎校甄选小程序"></l-painter-text>
            <l-painter-text css="color: #999; line-height: 1.8em; font-size: 26rpx; width: 488rpx;" text="还在等什么？试一下就知道了，成为学霸没有想象中的那么难"></l-painter-text>
            <l-painter-image src="https://document.dxznjy.com/dxSelect/three/icon/code.png" css="position: absolute;width: 140rpx;bottom: 42rpx;right: 30rpx;" />
          </l-painter-view> -->
        </l-painter-view>
      </l-painter>
    </view>

    <u-popup :show="show" mode="bottom" :round="10" :closeable="true" :safeAreaInsetBottom="false" @close="close">
      <view class="plr-30 flex pt-60 pb-40">
        <view class="flex-col flex-box" @tap="appShareOption(1)">
          <image src="https://document.dxznjy.com/dxSelect/image/icon_weixin1.png" class="box-100"></image>
          <text class="f-22 c-44 mt-10">微信好友</text>
        </view>
        <view class="flex-col flex-box" @tap="appShareOption(2)">
          <image src="https://document.dxznjy.com/dxSelect/image/icon_weixin2.png" class="box-100"></image>
          <text class="f-22 c-44 mt-10">朋友圈</text>
        </view>
        <view class="flex-col flex-box" @click="appSaveOption()">
          <image src="https://document.dxznjy.com/dxSelect/image/icon_save.png" class="box-100"></image>
          <text class="f-22 c-44 mt-10">保存本地</text>
        </view>
      </view>
    </u-popup>
  </view>
</template>

<script>
  export default {
    data() {
      return {
        switchstate: true, // 日总结
        data: {}, // 日总结数据
        backdetails: {}, // 反馈详情数据
        totallist: {}, // 课程反馈总
        type: '', // 反馈类型
        revirelist: {}, // 复习反馈详情数据
        path: '', // 生成海报
        show: false,
        imgHost: getApp().globalData.imgsomeHost
      };
    },
    onLoad(option) {
      console.log(option);
      if ('true' === option.state) {
        this.getFeedback(option); // 日总结
      } else {
        this.getTotalback(option); // 总总结
      }
    },
    methods: {
      // 课程反馈详情日
      async getFeedback(option) {
        let res = await this.$httpUser.get('deliver/app/parent/getFeedbackInfo', {
          id: option.id,
          type: 1
        });
        if (res.data.success) {
          this.backdetails = res.data.data;
        }
        this.switchstate = true;
      },

      // 课程反馈详情总
      async getTotalback(option) {
        let res = await this.$httpUser.get('deliver/app/parent/getTotalStatistics', {
          id: option.id,
          planId: option.planId,
          type: 1
        });
        if (res.data.success) {
          this.totallist = res.data.data;
        }
        this.switchstate = false;
      },

      // 生成海报
      longPress() {
        //长按保存
        uni.showLoading();
        if (this.path !== '') {
          uni.hideLoading();
          // #ifdef MP-WEIXIN
          uni.authorize({
            scope: 'scope.writePhotosAlbum',
            success: () => {
              uni.saveImageToPhotosAlbum({
                filePath: this.path,
                success: () => {
                  uni.showModal({
                    title: '保存成功',
                    content: '图片已成功保存到相册，快去分享到您的圈子吧',
                    showCancel: false
                  });
                }
              });
            },
            fail() {
              uni.showModal({
                title: '保存失败',
                content: '您没有授权，无法保存到相册',
                showCancel: false
              });
            }
          });
          // #endif

          // #ifdef APP-PLUS
          this.openAppOption();
          // #endif
          uni.hideLoading();
        } else {
          uni.showModal({
            title: '提示',
            content: '生成海报失败,请重试',
            showCancel: false
          });
        }
      },
      openAppOption() {
        this.show = true;
      },
      close() {
        this.show = false;
      },
      //type 1好友  2朋友圈
      appShareOption(type) {
        uni.share({
          provider: 'weixin',
          scene: type === 1 ? 'WXSceneSession' : 'WXSceneTimeline',
          type: 2,
          imageUrl: this.path,
          success: function (res) {
            console.log('success:' + JSON.stringify(res));
            uni.showToast({
              icon: 'none',
              title: '分享成功'
            });
          },
          fail: function (err) {
            console.log('fail:' + JSON.stringify(err));
            uni.showToast({
              icon: 'none',
              title: '分享失败'
            });
          }
        });
      },
      appSaveOption() {
        this.close();
        uni.saveImageToPhotosAlbum({
          filePath: this.path,
          success: function () {
            uni.showModal({
              title: '保存成功',
              content: '图片已成功保存到相册，快去分享到您的圈子吧',
              showCancel: false
            });
          }
        });
      }
    }
  };
</script>

<style lang="scss" scoped>
  .bgc_pic {
    position: fixed;
    width: 100%;
    // background-image: url(../../static/pic/bgc.png);
  }

  .logo {
    position: fixed;
    top: 30rpx;
    left: 50rpx;
    height: 50rpx;
    width: 100rpx;
    z-index: 1;
  }

  .split {
    padding-top: 320rpx;
  }

  .card {
    position: relative;
    width: 650rpx;
    background-color: #fff;
    border-radius: 40rpx;
    padding: 50rpx 30rpx 30rpx 20rpx;
    margin: auto;
    z-index: 99;
  }

  .cattle {
    position: absolute;
    left: 490rpx;
    top: -195rpx;
    height: 200rpx;
    width: 160rpx;
  }

  .text-content {
    font-size: 30rpx;
    margin-top: 20rpx;
    margin-left: 20rpx;
  }

  .teacher {
    border-top: 1px dashed #999;
    padding-top: 20rpx;
  }

  .title {
    position: absolute;
    top: -30rpx;
    width: 200rpx;
    height: 70rpx;
    color: #fff;
    line-height: 70rpx;
    text-align: center;
    font-size: 34rpx;
    background-color: #f9d60e;
    border-radius: 10rpx;
    box-shadow: 0 0 10rpx 6rpx rgba(0, 0, 0, 0.2);
    z-index: 1;
  }

  .details {
    position: relative;
    width: 650rpx;
    background-color: #fff;
    border-radius: 40rpx;
    padding: 50rpx 30rpx 30rpx 20rpx;
    margin: auto;
    margin-top: 60rpx;
  }

  .head {
    position: absolute;
    top: -30rpx;
    width: 200rpx;
    height: 70rpx;
    color: #fff;
    line-height: 70rpx;
    text-align: center;
    font-size: 34rpx;
    background-color: #f9d60e;
    border-radius: 10rpx;
    box-shadow: 0 0 10rpx 6rpx rgba(0, 0, 0, 0.2);
    z-index: 1;
  }

  .content {
    display: flex;

    .left {
      margin-top: 20rpx;
    }

    .rubric {
      font-weight: 700;
    }

    .substance {
      margin-top: 10rpx;
      font-size: 26rpx;
      color: #999;
    }

    .right {
      width: 140rpx;
      height: 140rpx;
      min-width: 140rpx;
      min-height: 140rpx;
      background-color: #d8d8d8;
    }

    /deep/.code {
      width: 140rpx;
      height: 140rpx;
      min-width: 140rpx;
      min-height: 140rpx;
    }
  }

  .data-v-bc55c48e {
    text-align: center;
  }
</style>
