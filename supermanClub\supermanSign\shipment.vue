<template>
  <page-meta :page-style="'overflow:' + (rollShow ? 'hidden' : 'visible')"></page-meta>
  <view class="plr-30 positionRelative">
    <view class="positionAbsolute back" @click="goBack">
      <uni-icons type="back" size="25"></uni-icons>
    </view>
    <view class="t-c f-32" style="margin-top: 110rpx">出货</view>
    <!-- <view class="p-30 bg-ff radius-15 t-c mt-30">
			当前剩余超人码：{{codeNumber.haveCodeNum || 0}}个
		</view> -->

    <view class="plr-30 ptb-40 bg-ff radius-15 c-66 mt-30 positionRelative" :style="{ height: useHeight + 'rpx' }">
      <view class="t-c margin90">
        <image :src="imgHost + 'dxSelect/three/icon/ch-icon.png'" class="img_icon"></image>
        <view class="c-33 f-30">当前剩余超人码：{{ codeNumber.haveCodeNum || 0 }}个</view>
      </view>

      <view>
        选择下级：
        <view class="flex-s choose mt-20" @click="chooseSubordinate">
          <view :class="merchantName ? 'c-00' : 'c-99'">{{ merchantName || '请选择' }}</view>
          <uni-icons type="right" size="18" color="#999"></uni-icons>
        </view>
        <u-picker
          ref="uPicker"
          :show="show"
          :columns="supermanList"
          @confirm="confirm"
          @cancel="cancel"
          confirmColor="#1D755C"
          itemHeight="68"
          :immediateChange="true"
          keyName="merchantName"
        ></u-picker>
      </view>

      <view class="mt-40">
        出货数量：
        <view class="mt-20 c-00">
          <input class="choose" type="number" placeholder="请输入" @blur="getNumber" placeholder-style="color:#999" />
        </view>
      </view>
      <view class="btn" @click="getSell">确定</view>
    </view>

    <uni-popup ref="popup" type="center" @change="changePopup">
      <view class="t-c bg-ff content">
        <u-icon name="checkmark-circle-fill" color="#2DC032" size="136"></u-icon>
        <view class="mt-30">出货成功</view>
        <view class="mt-10">当前剩余超人码：{{ codeNumber.haveCodeNum }}个</view>
      </view>
    </uni-popup>

    <uni-popup ref="failPopup" type="center" @change="changePopup">
      <view class="t-c bg-ff content-fail">
        <u-icon name="close-circle-fill" color="#FA370E" size="136"></u-icon>
        <view class="mt-30">出货失败</view>
        <view class="mt-10">可能由于网络原因，请检查您的网络</view>
      </view>
    </uni-popup>

    <!-- 出货数量不够提示 -->
    <uni-popup ref="notifyPopup" type="top" @change="changePopup" mask-background-color="rgba(0,0,0,0)">
      <view class="t-c bg-ff flex-c ptb-25 radius-50 mt-80 notify">
        <u-icon name="error-circle-fill" color="#FA370E" size="50"></u-icon>
        <view class="f-34 ml-15">出货数量不能大于剩余数量</view>
      </view>
    </uni-popup>
  </view>
</template>

<script>
  const { $http } = require('@/util/methods.js');
  export default {
    data() {
      return {
        merchantCode: '', // 下级code
        merchantName: '', // 下级名称
        show: false,
        supermanList: [],
        sellNum: '', // 出货数量
        useHeight: 0, //除头部之外高度
        rollShow: false, // 禁止穿透
        codeNumber: {}, // 超人码数量
        imgHost: getApp().globalData.imgsomeHost
      };
    },
    onReady() {
      uni.getSystemInfo({
        success: (res) => {
          // 可使用窗口高度，将px转换rpx
          let h = res.windowHeight * (750 / res.windowWidth);
          this.useHeight = h - 290;
        }
      });
    },
    onLoad() {
      this.getSupermanNum();
      this.getSupermanclubList();
    },
    onShow() {},
    methods: {
      goBack() {
        uni.navigateBack();
      },
      getNumber(e) {
        console.log(e);
        this.sellNum = e.detail.value;
      },
      changePopup(e) {
        this.rollShow = e.show;
      },
      // 选择下级弹窗
      chooseSubordinate() {
        if (this.supermanList[0].length == 0) {
          this.$util.alter('您还没有下级哦');
          return;
        }
        this.show = true;
      },
      confirm(e) {
        console.log(e);
        this.merchantCode = e.value[0].merchantCode;
        this.merchantName = e.value[0].merchantName;
        this.show = false;
      },
      cancel() {
        this.show = false;
      },

      // 获取超人码数量
      async getSupermanNum() {
        const res = await $http({
          url: 'zx/invitation/code/getUserCodeNum'
        });
        console.log(res);
        if (res) {
          this.codeNumber = res.data;
        }
      },

      // 获取下级俱乐部列表
      async getSupermanclubList() {
        const res = await $http({
          url: 'zx/merchant/getSubordinateMerchant'
        });
        console.log(res.data);
        if (res) {
          this.supermanList.push(res.data);
          console.log(this.supermanList);
        }
      },

      // 出货
      async getSell() {
        if (this.codeNumber.haveCodeNum < this.sellNum) {
          this.$refs.notifyPopup.open();
          return;
        }
        if (this.merchantCode == '' || this.merchantName == '') {
          this.$util.alter('请选择俱乐部下级');
          return;
        }
        if (this.sellNum == '') {
          this.$util.alter('请填写出货数量');
          return;
        }
        if (this.sellNum <= 0) {
          this.$util.alter('出货数量需要大于0哦');
          return;
        }
        uni.showLoading();
        const res = await $http({
          url: 'zx/invitation/code/outInvitationCodes',
          data: {
            merchantCode: this.merchantCode,
            num: this.sellNum
          }
        });
        uni.hideLoading();
        console.log(res);
        if (res) {
          this.getSupermanNum();
          this.$refs.popup.open();
        } else {
          this.$refs.failPopup.open();
        }
      }
    }
  };
</script>

<style lang="scss" scoped>
  .back {
    left: 10rpx;
    top: -6rpx;
  }
  .choose {
    height: 90rpx;
    padding: 0 30rpx;
    border-radius: 45rpx;
    border: 1px solid #c8c8c8;
  }

  .notify {
    box-shadow: 0rpx 0rpx 20rpx #e0e0e0;
  }

  /deep/.u-picker__view {
    height: 400rpx !important;
  }

  /deep/.u-popup__content {
    margin: 20rpx !important;
    border-radius: 15rpx !important;
  }

  /deep/.u-toolbar {
    border-bottom: 1px solid #eeeef0;
  }

  /deep/.u-picker__view__column__item {
    background-color: #f4f4f4;
  }

  .btn {
    position: absolute;
    bottom: 30rpx;
    height: 90rpx;
    width: 91.5%;
    line-height: 90rpx;
    border-radius: 45rpx;
    text-align: center;
    background: linear-gradient(to bottom, #88cfba, #1d755c);
  }

  .content {
    color: #333;
    border-radius: 15rpx;
    padding: 90rpx 0rpx;
    width: 600rpx;
  }

  /deep/.u-icon--right {
    justify-content: center;
  }

  .content-fail {
    color: #333;
    border-radius: 15rpx;
    padding: 90rpx 90rpx;
  }

  /deep/ .uni-popup__wrapper {
    padding: 0 60rpx !important;
  }

  .line-height90 {
    line-height: 90rpx;
  }

  .margin90 {
    margin: 90rpx auto;
  }

  .img_icon {
    width: 328rpx;
    height: 310rpx;
  }
</style>
