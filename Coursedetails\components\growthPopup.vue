<template>
	<uni-popup ref="problem_popup" type="center" style="padding: 0;" >
		<view class="problem_content_css bg-ff">
			<view class="popup_text">
				<span class="f-32 lh-44 c-33">恭喜！您的成长值</span>
				<span class="f-42 get_number bold ml-15">+{{ growthNum }}</span>
			</view>
			<view class="tips_content_close">
				<u-icon name="close-circle-fill" color="#B1B1B1" size="38"></u-icon>
			</view>
			<view @click="close()" class="button_input_css f-32 c-ff">
				我知道了
			</view>
		</view>
	</uni-popup>
</template>

<script>
	export default {
		data() {
			return {
				growthNum:0,
				showKey:0,
			}
		},
		methods: {
			open(num,key){
				this.showKey=key
				this.growthNum=num
				this.$refs.problem_popup.open()
			},
			close(){
				this.$refs.problem_popup.close()
				if(this.showKey==1){
					uni.navigateBack({
						delta: 1
					});
				}
			}
		}
	}
</script>

<style lang="scss" scoped>
.problem_content_css{
	width: 560rpx;
	height: 342rpx;
	border-radius: 15rpx;
	text-align: center;
	position: relative;
	.tips_content_close{
		position: absolute;
		top:34rpx;
		right:40rpx;
	}
	.popup_text{
		padding-top: 118rpx;
		span{
			display: inline-block;
		}
	}
	.button_input_css{
		width:280rpx;
		height: 92rpx;
		line-height: 92rpx;
		text-align: center;
		background-color: #428A6F;
		margin:0 auto;
		margin-top: 46rpx;
		border-radius: 46rpx;
	}
	.get_number{
		color:#EE9F00;
	}
}
</style>
