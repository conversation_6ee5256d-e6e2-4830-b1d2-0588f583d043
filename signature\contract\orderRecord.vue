<template>
  <view class="content">
    <view class="flex-a-c flex-x-b ptb-35 plr-30">
      <view class="f-32 c-55 tabs_item_css" v-for="(item, index) in orderTabList" @click="tabsClick({ index: index, key: item.key })" :key="item.key">
        <view :class="selectIndex == index ? 'active_tabs_css' : ''">
          <view>
            <view class="tabs_name">{{ item.name }}</view>
            <view class="line_tabs_css"></view>
          </view>
        </view>
      </view>
    </view>
    <view class="list_view" v-for="(item, index) in boxList" :key="index" @click="handleDetail(item)">
      <view class="list_head">
        <!-- {{ item.packageName }} -->
        套餐一
        <view class="lc_yellow" v-if="item.status === 1">待支付</view>
        <view class="lc_yellow lc_g" v-if="item.status === 3">支付成功</view>
        <view class="lc_yellow lc_eorr" v-if="item.status === 4">支付失败</view>
        <view class="lc_yellow lc_gray" v-if="item.status === 8">已取消</view>
        <view class="lc_yellow lc_gray" v-if="item.status === 9">订单已过期</view>
      </view>
      <view class="list_context">
        <text>套餐名称：</text>
        <view class="bg_green word-10">
          <!-- {{ item.packageName }} -->
          套餐一
        </view>
      </view>
      <view class="list_context">
        <text>套餐内合同数（份）：</text>
        <view class="bg_green">
          {{ item.num }}
        </view>
      </view>
      <view class="list_context">
        <text>有效期：</text>
        <view class="bg_green">
          {{ item.expireTime }}
        </view>
      </view>
      <view class="list_context">
        <text>套餐总金额（元）：</text>
        <view class="bg_green">
          {{ item.originalPrice }}
        </view>
      </view>
      <view class="list_context">
        <text>应付金额（元）：</text>
        <view class="bg_green">
          {{ item.price }}
        </view>
      </view>
      <!-- <view class="list_bot">
        <view class="btn_b b_r" @click.stop="continuePay(item)" v-if="item.status === 1">支付</view>
        <view class="btn_b b_l" v-if="item.status === 1" @click.stop="cancelPay(item)">取消订单</view>
      </view> -->
    </view>
    <view v-if="boxList.length == 0">
      <emptyPage></emptyPage>
    </view>
  </view>
</template>

<script>
  const { $http } = require('@/util/methods.js');
  const { httpUser } = require('@/util/luch-request/indexUser.js');
  import emptyPage from '../components/emptyPage.vue';
  export default {
    components: { emptyPage },
    data() {
      return {
        selectIndex: 0,
        tabsCurrent: 0,
        orderTabList: [
          { name: '全部', key: 0 },
          { name: '待支付', key: 1 },
          { name: '支付成功', key: 3 }
          // { name: '待支付', key: 2, status: 1 },
          // { name: '支付成功', key: 3, status: 6 }
          // { name: '已取消', key: 4, status: 8 }
        ],
        boxList: [],
        disabled: false,
        page: 1,
        infoLists: {},
        userinfo: {}
      };
    },
    onReachBottom() {
      console.log('...........................................');
      if (this.page * 20 >= this.infoLists.total) {
        return false;
      }
      this.getRecordList(true, ++this.page);
    },
    onLoad() {
      this.getRecordList();
      this.getUserInfoData();
    },
    methods: {
      handleDetail(item) {
        uni.setStorageSync('signatureOrder', item);
        uni.navigateTo({
          url: '/signature/contract/orderDetails'
        });
      },

      async getRecordList(isPage, page, status) {
        page = page || 1;
        let _this = this;
        let parm = {
          pageNum: page,
          pageSize: 20,
          merchantCode: uni.getStorageSync('merchantCode')
        };
        if (status) {
          parm.status = status;
        } else {
          parm.status = 0;
        }

        const res = await $http({
          // url: 'zx/wap/pay/package/record/list',
          url: 'znyy/merchant/contract/detail',
          data: parm
        });
        if (res) {
          console.log(res);
          _this.infoLists = res;
          console.log('🚀 ~ getRecordList ~ isPage:', isPage);
          console.log('🚀 ~ getRecordList ~ res.data.data:', res.data);

          if (isPage) {
            _this.boxList = [..._this.boxList, ...res.data];
          } else {
            _this.boxList = res.data || [];
          }
          console.log('---------------------------------------------------');
        }
      },
      tabsClick(e) {
        console.log('🚀 ~ tabsClick ~ e:', e);
        this.selectIndex = e.index;
        this.getRecordList(false, 1, e.key);
      },
      createContract() {
        uni.navigateTo({
          url: '/signature/contract/createContract'
        });
      },
      async continuePay(item) {
        let _this = this;
        if (_this.disabled) {
          return false;
        }
        uni.showLoading();
        _this.disabled = true;
        let resdata;
        resdata = await $http({
          url: 'zx/wap/pay/package/record/continue/pay',
          method: 'POST',
          data: {
            orderId: item.id
          }
        });
        _this.disabled = false;
        if (resdata.data.needPay == 1) {
          _this.payBtn(resdata.data.applyPayDto);
        }
      },
      async payBtn(data) {
        // #ifdef APP-PLUS
        if (this.userinfo.openId == '') {
          let payCodeData = await httpUser.get('zx/user/getPaymentUserCode?mobile=');
          let payCode = payCodeData.data.data;
          data.userCode = payCode;
        }
        // #endif
        let _this = this;
        let resdata = await httpUser.post('mps/line/collect/order/unified/collect', data);
        let res = resdata.data.data;
        _this.disabled = false;
        // uni.hideLoading();
        if (res) {
          if (res.openAllinPayMini) {
            this.flag1 = true;
            this.payInfo = res;
            // #ifdef APP-PLUS
            uni.$appPayTlian(res, 'wxpay');
            // #endif
            // #ifdef MP-WEIXIN
            uni.$payTlian(res);
            // #endif
          } else {
            uni.requestPayment({
              provider: 'wxpay',
              timeStamp: res.payInfo.timeStamp,
              nonceStr: res.payInfo.nonceStr,
              package: res.payInfo.packageX,
              signType: res.payInfo.signType,
              paySign: res.payInfo.paySign,
              success: function (ress) {
                _this.getRecordList();
              },
              fail: function (err) {
                uni.showToast({
                  title: '支付失败'
                });
              }
            });
          }
        }
      },
      async cancelPay(item) {
        let _this = this;
        uni.showModal({
          title: '提示',
          content: '确认取消该订单吗？',
          success: async function (res) {
            if (res.confirm) {
              const resdata = await $http({
                url: 'zx/wap/pay/package/record/cancel',
                // method: 'POST',
                data: {
                  orderId: item.id
                }
              });
              if (resdata) {
                _this.getRecordList();
              }
            } else if (res.cancel) {
              console.log('用户点击取消');
            }
          }
        });
      },
      async getUserInfoData() {
        let _this = this;
        const res = await $http({
          url: 'zx/user/userInfoNew'
        });
        if (res) {
          _this.userinfo = res.data;
        }
      }
    }
  };
</script>

<style scoped lang="scss">
  .content {
    padding: 32rpx;
  }
  .head_tab {
    height: 50rpx;
    padding: 0 10rpx;
    color: #5a5a5a;
    font-size: 28rpx;
    box-sizing: border-box;
    margin: 32rpx 0;
    display: flex;
    justify-content: space-between;
  }
  .tabs_item_css {
    .active_tabs_css {
      font-weight: bold;
      color: #333;
      .tabs_name {
        z-index: 1;
      }
      .line_tabs_css {
        width: 100%;
        height: 8rpx;
        border-radius: 8rpx;
        background-color: #28a781;
        margin-top: -10rpx;
        // position: absolute;
        // bottom:3rpx;
        z-index: 0;
      }
    }
  }
  .active {
    border-bottom: 8rpx solid #339378;
    color: #333333;
    font-weight: 900;
    border-radius: 2rpx;
  }
  .list_view {
    width: 686rpx;
    border-radius: 24rpx;
    padding: 32rpx 24rpx;
    background-color: #ffffff;
    box-sizing: border-box;
    margin-bottom: 32rpx;
    letter-spacing: 3rpx;
  }

  .list_head {
    color: #333333;
    font-size: 32rpx;
    font-weight: 800;
    display: flex;
    justify-content: space-between;
    padding: 0 0 24rpx 0;
    border-bottom: 0.5px solid #f6f7f9;
  }
  .list_context {
    color: #555555;
    margin-top: 36rpx;
    display: flex;
    /* justify-content: space-between; */
  }
  .list_context text {
    /* width:172rpx ;
	background-color: pink; */
  }

  .lc_yellow {
    color: #fd9b2a;
    text-align: center;
    font-weight: 300;
    font-size: 26rpx;
    width: 116rpx;
    border: 1px solid #ffe1be;
    background-color: #fdf6ed;
    border-radius: 8rpx;
    margin-right: 16rpx;
    line-height: 40rpx;
  }
  .lc_green {
    color: #006f57;
  }
  .lc_g {
    color: #81e2af;
    border: 1px solid #81e2af;
    background-color: #ecfbf3;
    padding: 0 10rpx;
  }
  .lc_eorr {
    color: #ff8653;
    border: 1px solid #ff8653;
    background-color: rgba(255, 195, 113, 0.4);
    padding: 0 10rpx;
  }
  .lc_gray {
    padding: 0 10rpx;
    color: #cccccc;
    border: 1px solid #cccccc;
    background-color: #f7f7f7;
  }
  .list_bot {
    margin-top: 62rpx;
    display: flex;
    flex-direction: row-reverse;
  }
  .btn_b {
    width: 196rpx;
    height: 60rpx;
    border-radius: 60rpx;
    line-height: 60rpx;
    text-align: center;
    margin-left: 32rpx;
  }
  .b_l {
    background-color: #fff;
    color: #4e9f87;
    border: 1px solid #7baea0;
    margin-left: 32rpx;
  }
  .b_r {
    background-image: linear-gradient(to bottom, #88cfba, #1d755c);
    color: #ffffff;
  }
  .bg_green {
    padding: 0 24rpx;
    /* color: #439582; */
    background-color: #f8fff7;
    text-align: center;
  }
  .word-10 {
    display: inline-block; /* 确保元素是行内块级元素 */
    width: 12em; /* 设置宽度为10em（可以调整为合适的单位） */
    white-space: nowrap; /* 不换行 */
    overflow: hidden; /* 超出部分隐藏 */
    text-align: left;
    text-overflow: ellipsis; /* 超出部分用省略号表示 */
  }
</style>
