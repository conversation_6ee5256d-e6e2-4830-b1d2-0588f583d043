<template>
  <view class="contain">
    <view class="imgSet" v-if="answeringStatus == '1'">
      <image src="https://document.dxznjy.com/dxSelect/6dfe2a0b-3b73-4824-9a05-f863b89edc28.png" mode="" style="width: 293rpx; height: 308rpx"></image>
      <text class="mt-12 f-36 c-33">太棒啦</text>
      <view class="answer">
        <image src="https://document.dxznjy.com/dxSelect/181e3ec1-46e6-4fd5-b57b-a7be48ded81a.png" mode="" style="width: 52rpx; height: 52rpx"></image>
        <text class="ml-20">回答正确！</text>
      </view>
    </view>
    <view class="imgSet" v-if="answeringStatus == '2'">
      <image src="https://document.dxznjy.com/dxSelect/eb33a190-1f0e-44ab-843b-9d7ec32e527e.png" mode="" style="width: 293rpx; height: 308rpx"></image>
      <text class="mt-12 f-36 c-33">再接再励！</text>
      <view class="answer">
        <image src="https://document.dxznjy.com/dxSelect/eb45ed3e-c2b1-4535-b1a3-d6b3d5031b93.png" mode="" style="width: 52rpx; height: 52rpx"></image>
        <text class="ml-20">回答错误！</text>
      </view>
      <view class="mt-45">
        <text>正确答案：</text>
        <text class="ml-20" style="color: #428a6f">{{ correctAnswer }}</text>
      </view>
    </view>
    <view class="bottomButton">
      <u-button shape="circle" :plain="true" color="#428A6F" @click="viewsHandle">查看解析</u-button>
      <u-button shape="circle" class="leftButton" color="#428A6F" @click="confirmHandle">再做一题</u-button>
    </view>
  </view>
</template>

<script>
  export default {
    data() {
      return {
        show: true,
        answerQuestionId: '',
        answeringStatus: '',
        correctAnswer: '',
        data: {},
        studentCode: ''
      };
    },
    onLoad(options) {
      this.data = JSON.parse(decodeURIComponent(options.data));
      this.studentCode = options.studentCode;
      this.answerQuestionId = this.data.answerQuestionId;
      this.answeringStatus = this.data.answeringStatus;
      this.correctAnswer = this.data.correctAnswer;
    },
    methods: {
      viewsHandle() {
        uni.redirectTo({
          url: '/errorBook/questionAnalysis?id=' + this.answerQuestionId + '&studentCode=' + this.studentCode
        });
      },
      confirmHandle() {
        let selectInfo = uni.getStorageSync('selectInfo');
        uni.showLoading({
          title: '加载中...',
          mask: true
        });
        // 调用接口
        this.$httpUser
          .get('dyf/math/wap/correctionNoteBook/getQuestionByUnCorrect', {
            answerQuestionId: this.answerQuestionId,
            gradeId: selectInfo.PhaseStudentId,
            studentCode: this.studentCode
          })
          .then((res) => {
            // 处理接口返回
            if (res.data.success) {
              const encodedData = encodeURIComponent(JSON.stringify(res.data.data));
              uni.redirectTo({
                url: `/errorBook/expandImprove?expandImproveData=${encodedData}&studentCode=${this.studentCode}`
              });
            } else {
              uni.showToast({
                title: res.message,
                icon: 'none'
              });
            }
          })
          .catch((err) => {
            uni.showToast({
              title: err.message,
              icon: 'none'
            });
          })
          .finally(() => {
            uni.hideLoading();
          });
      }
    }
  };
</script>

<style>
  .contain {
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: center;
    width: 100%;
    height: 100vh;
    background: url('https://document.dxznjy.com/dxSelect/2f3a9ce1-cfed-44d4-adcb-e0ca8f36f6f8.png') center/cover no-repeat;
    position: relative;
  }
  .imgSet {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    margin-top: 0;
  }
  .imgSet text {
    font-weight: 600;
  }
  .answer {
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    margin-top: 207rpx;
  }
  .bottomButton {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 24rpx;
    box-sizing: border-box;
    position: fixed;
    bottom: 127rpx;
    padding-bottom: var(--safe-area-inset-bottom);
  }

  ::v-deep .u-button {
    width: 325rpx !important;
    height: 80rpx;
    font-size: 32rpx;
  }
</style>
