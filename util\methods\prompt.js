/**
 * 成功的提示
 * @param {String} msg 提示的文字
 */
const $showSuccess = (msg) => {
  return new Promise((resolve,reject) => {
    uni.showToast({
      title: msg,
      icon: 'success',
      mask: true,
      duration: 1500,
      success() {
        setTimeout(function() {
          resolve()
        }, 1500);
      }
    });
  })
}
/**
 * 失败的弹窗提示
 * @param {String} msg 提示的文字
 */
const $showError = (msg) => {
  return new Promise((resolve,reject) => {
    uni.showModal({
      title: '友情提示',
      content: msg,
      showCancel: false,
      success(res) {
        resolve(res)
      }
    });
  })
}
/**
 * 信息提示
 * @param {String} msg 提示的文字
 */
const $showMsg = (msg) => {
  return new Promise((resolve,reject) => {
    uni.showToast({
      title: msg,
      icon: 'none',
      mask: true,
      duration: 1500,
      success() {
        setTimeout(function() {
          resolve()
        }, 1500);
      }
    });
  })
}

module.exports = {
  $showSuccess,
  $showError,
  $showMsg
}