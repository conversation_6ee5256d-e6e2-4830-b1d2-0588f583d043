<template>
  <page-meta :page-style="'overflow:' + (rollShow ? 'hidden' : 'visible')"></page-meta>
  <view>
    <view v-if="shopdetail != null" class="content positionRelative" :style="'height:' + height">
      <view class="course-detail pb-200">
        <view class="relative" v-if="shopdetail != null && shopdetail.goodsCarouselList != null">
          <swiper
            :indicator-dots="false"
            :autoplay="shopdetail.goodsVideoUrl ? false : true"
            circular="true"
            duration="500"
            indicator-active-color="#ffffff"
            class="swiper"
            :style="{ height: swiperheight + 'rpx' }"
            @change="setCurrent"
          >
            <!-- goodsVideoUrl -->
            <!-- #ifdef MP-WEIXIN -->
            <block v-if="shopdetail.goodsVideoUrl">
              <swiper-item class="section_item" :style="{ height: swiperheight + 'rpx' }">
                <polyv-player
                  id="polyv-player-id"
                  @loadedmetadata="bindloadedmetadata"
                  :autoplay="false"
                  :playerId="playerIdcont"
                  :vid="shopdetail.goodsVideoUrl"
                  :width="width"
                  height="100%"
                  :ts="ts"
                  :sign="sign"
                ></polyv-player>
                <!-- <video
                id="myVideo"
                class="video_css_content"
                :src="shopdetail.goodsVideoUrl"
                :poster="shopdetail.goodsVideoUrl + '?x-oss-process=video/snapshot,t_0,f_jpg'"
                controls></video> -->
              </swiper-item>
            </block>
            <!-- #endif -->
            <block v-for="(item, index) in shopdetail.goodsCarouselList" :key="index">
              <swiper-item class="section_item">
                <image v-if="shopdetail.goodsType == 1" :src="item.picUrl" class="w100" mode="widthFix"></image>
                <image v-else :src="item.picUrl" class="w100" mode="widthFix"></image>
              </swiper-item>
            </block>
          </swiper>
        </view>
        <div :style="'top:' + productTop + 'rpx'" class="product_content_main plr-32">
          <view class="pt-28 mb-30">
            <view class="flex-s">
              <view class="flex-dir-row flex-y-c">
                <view class="c-red">
                  <text class="f-48 lh-64 color_red_css ml-5 bold">
                    <text class="f-24">￥</text>
                    <text>{{ shopdetail.goodsOriginalPrice }}</text>
                  </text>
                </view>
              </view>
            </view>

            <view class="flex-s f-28 c-33 mt-8">
              <view class="goods_name_css twolist bold w686 lh-42">{{ shopdetail == null ? '' : shopdetail.goodsName }}</view>
            </view>
          </view>

          <!-- 标签 -->
          <view class="flexbox w686 mt-20 f-24" v-if="shopdetail.goodsTagOne">
            <view class="fontWeight lh-36">
              <text v-if="shopdetail.goodsTagOne" class="label_css radius-8 mr-8 display_inline">{{ shopdetail.goodsTagOne }}</text>
              <text v-if="shopdetail.goodsTagTwo" class="label_css radius-8 mr-8 display_inline">{{ shopdetail.goodsTagTwo }}</text>
              <text v-if="shopdetail.goodsTagThree" class="label_css radius-8 mr-8 display_inline">{{ shopdetail.goodsTagThree }}</text>
            </view>
            <!-- w+ -->
            <view class="flexbox">
              <view class="c-99 lh-36">已售 {{ shopdetail.goodsSales }}份</view>
            </view>
          </view>
          <!-- 购买记录 -->
          <view class="ptb-30 w686 purchase_records bg-ff mt-30">
            <view class="flex-s plr-25">
              <view class="f-28">
                <image class="wh24 display_block mr-8" src="https://document.dxznjy.com/course/0c9fa08112a44df49fa48023fb5b0aad.png"></image>
                <text class="fontWeight">{{ shopdetail.goodsSales }}人已购买</text>
              </view>
              <view class="flex-a-c" @click="seeMore">
                <text class="mr-10 f-26 c-66">查看更多</text>
                <uni-icons type="right" size="16" color="#999"></uni-icons>
              </view>
            </view>
            <view class="mt-40 plr-25" v-if="buyDataList.length">
              <view v-if="buyDataList.length <= 2" v-for="(item, index) in buyDataList" :key="index">
                <view class="flex-s">
                  <view class="flex-a-c">
                    <image :src="item.headPortrait || avaUrl" class="head-img"></image>
                    <view class="mr-15 c-66 f-26 user_name">{{ item.nickName }}</view>
                  </view>
                  <view class="flex-a-c">
                    <text class="c-99 f-24 time_height_css">{{ formatItem(item.createdTime, true) + '下了一单' }}</text>
                    <view class="ml-20 c-66 shopping" @click.stop="buySubject()">去下单</view>
                  </view>
                </view>
              </view>
              <swiper
                v-if="buyDataList.length > 2"
                display-multiple-items="2"
                circular
                vertical
                autoplay
                interval="3000"
                duration="1000"
                class="swiperBox"
                style="height: 240rpx"
              >
                <swiper-item v-for="(item, index) in buyDataList" :key="index" class="swiper-item">
                  <view class="flex-s">
                    <view class="flex-a-c">
                      <image :src="item.headPortrait || avaUrl" class="head-img"></image>
                      <view class="mr-15 c-66 f-26 user_name">{{ item.nickName }}</view>
                    </view>
                    <view class="flex-a-c">
                      <text class="c-99 f-24 time_height_css">{{ formatItem(item.createdTime, true) + '下了一单' }}</text>
                      <view class="ml-20 c-66 shopping" @click.stop="buySubject()">去下单</view>
                    </view>
                  </view>
                </swiper-item>
              </swiper>
            </view>
          </view>
          <!-- 商品评价 -->
          <view class="ptb-30 bg-ff mb-30 purchase_records w686 mt-24">
            <view class="flex-s plr-25">
              <view class="f-28">
                <image class="wh24 display_block mr-8" src="https://document.dxznjy.com/course/8a4b7c1e25564b72a42f1b1ccc07ba42.png"></image>
                <text class="fontWeight">商品评价（{{ evaluatelist.totalCount || 0 }}）</text>
              </view>
              <view class="flex-a-c" @click="goUrl">
                <text class="mr-10 f-26 c-66">{{ evaluatelist.totalCount > 0 ? '查看更多' : '暂无评价' }}</text>
                <uni-icons type="right" size="16" color="#999"></uni-icons>
              </view>
            </view>
            <view class="mt-40 plr-25" v-if="evaluatelist.list.length > 0">
              <view class="flex-s">
                <view class="flex-a-c">
                  <image :src="evaluatelist.list[0].headPortrait || avaUrl" class="head-img"></image>
                  <view class="mr-20 c-55 f-28">{{ evaluatelist.list[0].userName }}</view>
                  <view class="golden">
                    {{ evaTypeList.find((i) => i.key == evaluatelist.list[0].identityType).value }}
                  </view>
                </view>
                <uni-rate disabled :value="evaluatelist.list[0].evaluateGrade" active-color="#FD9B2A" class="mt-20" size="20" />
              </view>
              <view class="mt-15 f-24 c-55 pl-8 evalue-content">{{ evaluatelist.list[0].evaluateContent }}</view>
            </view>
            <view class="mt-40 plr-25" v-if="evaluatelist.list.length > 1">
              <view class="flex-s">
                <view class="flex-a-c">
                  <image :src="evaluatelist.list[1].headPortrait || avaUrl" class="head-img"></image>
                  <view class="mr-20 c-55 f-28">{{ evaluatelist.list[1].userName }}</view>
                  <view class="golden">
                    {{ evaTypeList.find((i) => i.key == evaluatelist.list[1].identityType).value }}
                  </view>
                </view>
                <uni-rate disabled :value="evaluatelist.list[1].evaluateGrade" active-color="#FD9B2A" class="mt-20" size="20" />
              </view>
              <view class="mt-15 f-24 c-55 pl-8 evalue-content twolist">{{ evaluatelist.list[1].evaluateContent }}</view>
            </view>
          </view>
          <!-- 交付课 录播课 学习资料 -->
          <view v-if="shopdetail && shopdetail.goodsType && [2, 3, 4].includes(shopdetail.goodsType)" class="ptb-30 bg-ff mb-30 purchase_records w686 mt-24">
            <view class="flex-s plr-25">
              <view class="f-28">
                <image class="wh24 display_block mr-8" src="https://document.dxznjy.com/course/20fbc71836df4b4aa5701450e0021fe6.png"></image>
                <text class="fontWeight">学习资料</text>
              </view>
              <view class="flex-a-c" @click="goProfile(shopdetail)">
                <text class="mr-10 f-26 c-66">查看</text>
                <uni-icons type="right" size="16" color="#999"></uni-icons>
              </view>
            </view>
          </view>
          <view v-if="shopdetail.goodsType !== 4">
            <view class="bg-ff w686 pt-25 pb-15 flex-dir-row product_details flex-y-c">
              <image class="wh24 ml-25 mr-8" src="https://document.dxznjy.com/course/ad3dff63abb6421a9eda1e7b411ea2ff.png"></image>
              <text class="f-28 c-22 bold">商品详情</text>
            </view>
            <view class="bg-ff plr-25 pb-30">
              <view style="width: 638rpx">
                <rich-text v-if="shopdetail && shopdetail.meetingCate == 0" :nodes="shopdetail != null ? getRichText(shopdetail.courseInfo) : ''"></rich-text>
                <rich-text v-else :nodes="shopdetail != null ? getRichText(shopdetail.goodsDesc) : ''"></rich-text>
              </view>
            </view>
          </view>
          <view v-else class="pb-150">
            <view class="bg-ff w686 product_details">
              <u-tabs
                :list="userList"
                lineWidth="0"
                lineHeight="0"
                :activeStyle="{ color: '#333333', fontWeight: 'bold', fontSize: '28rpx' }"
                :inactiveStyle="{
                  color: '#5A5A5A ',
                  transform: 'scale(1)',
                  fontSize: '28rpx'
                }"
                itemStyle="padding-bottom:20rpx;width:300rpx;"
                :lineColor="`url(${lineBg}) 100% 110%`"
                @click="tabsClick"
              ></u-tabs>
            </view>
            <view class="bg-ff plr-25 pb-30" v-if="tabName == '课程简介'">
              <view style="width: 638rpx">
                <rich-text v-if="shopdetail && shopdetail.meetingCate == 0" :nodes="shopdetail != null ? getRichText(shopdetail.courseInfo) : ''"></rich-text>
                <rich-text v-else :nodes="shopdetail != null ? getRichText(shopdetail.goodsDesc) : ''"></rich-text>
              </view>
            </view>
            <view v-else class="bg-ff plr-25 pb-20">
              <courseCatalog
                v-for="(item, index) in shopdetail.goodsCatalogueList"
                :key="item.id"
                :type="crouseType"
                :index="index"
                :item="item"
                :courseId="goodsId"
                :userCode="userCode"
                @changeDown="changeDown"
              ></courseCatalog>
            </view>
          </view>
        </div>
      </view>
      <!-- 底部栏 -->
      <view class="fixed_b flex">
        <button class="plr-20 padding_left" hover-class="none" @click="kfAppHandle">
          <image class="wh40" src="https://document.dxznjy.com/course/c40968f952914189ab801e1abd52a6d8.png" mode=""></image>
          <view class="f-24 c-55 ml-10">客服</view>
        </button>
        <button class="pr-20" @tap="$noMultipleClicks(collectChange, shopdetail && shopdetail.whetherCollect ? 2 : 1)" hover-class="none">
          <image v-if="shopdetail && shopdetail.whetherCollect" class="wh40" src="https://document.dxznjy.com/course/b154d6ebc67d458b872ed6148a52774a.png" mode=""></image>
          <image v-else class="wh40" src="https://document.dxznjy.com/course/7fc5232bb51b406f97e91814e3bb3b45.png" mode=""></image>
          <view class="f-24 c-55 ml-10">收藏</view>
        </button>
        <view class="fixed_button f-28">
          <view class="button_left f-28 c-ff" @click="shareFriend">分享好友</view>
          <view class="button_right" @tap="buySubject()">立即购买</view>
        </view>
      </view>
      <!-- 查看收益（该功能暂未使用） -->
      <uni-popup ref="popup" type="center" @change="changePopup">
        <view class="dialogBG">
          <view class="reviewCard_box positionRelative">
            <view class="cartoom_image">
              <image src="https://document.dxznjy.com/dxSelect/dialog_icon.png" mode="widthFix"></image>
            </view>
            <view class="review_close" @click="closeDialog">
              <uni-icons type="clear" size="28" color="#B1B1B1"></uni-icons>
            </view>
            <view class="reviewCard">
              <view class="bold t-c f-34">本单预估收益</view>
              <view class="c-fea f-30 t-c mt-12" v-if="shopdetail.courseLabel == 2">注：以产品会员价计算收益</view>
              <view class="c-fea f-30 t-c mt-12" v-if="shopdetail.courseLabel == 1">将体验课分享给好友并成功下单可获得该收益</view>

              <view class="f-30 mt-40 t-c" v-if="shopdetail.courseLabel == 1">
                <view>推荐人分润：100%</view>
                <view class="mt-20">推荐人预估收益：{{ shopdetail.memberPrice }}元</view>
              </view>
              <view class="flex-s f-30" v-if="shopdetail.courseLabel == 2">
                <view class="content mt-40">
                  <view v-if="userinfo.identityType == 1 || userinfo.identityType == 3">学习超人分润：{{ amount.memberRite }}%</view>
                  <view class="mt-20" v-if="userinfo.identityType == 2 || userinfo.identityType == 3">
                    {{ amount.level != 4 ? (amount.level == 5 ? 'B2' : 'B3') : 'B1' }}俱乐部分润：{{ amount.merchantRite }}%
                  </view>
                </view>
                <view class="content mt-40">
                  <view v-if="userinfo.identityType == 1 || userinfo.identityType == 3">预估收益：{{ amount.memberPrice }}元</view>
                  <view class="mt-20" v-if="userinfo.identityType == 2 || userinfo.identityType == 3">预估收益：{{ amount.merchantPrice }}元</view>
                </view>
              </view>
              <view class="t-c mt-40 flex-c">
                <view class="review_btn" @click="closeDialog">确定</view>
              </view>
              <view class="flex-a-c flex-x-e mt-20">
                <view class="f-24 c-99">最终解释权归鼎校甄选所有</view>
              </view>
            </view>
          </view>
        </view>
      </uni-popup>
      <u-toast ref="uToast"></u-toast>
      <!-- 交付课（体验课）和录播课购买 -->
      <uni-popup ref="experiencePopup" type="bottom" @change="changeExperienceDialog">
        <view class="content-popup">
          <view class="content_top_popup">
            <view class="icon-clear" @click="closeExperienceDialog">
              <view class="order_title">订单支付</view>
            </view>
            <view class="product_content_css pt-35">
              <view class="product_info">
                <view class="radius-15">
                  <image :src="shopdetail.goodsPicUrl" class="couser_image"></image>
                </view>
                <view class="product_right">
                  <view>
                    <view class="f-24 price_tangerine">
                      <text class="price_title">实付价</text>
                      <text class="price_icon">￥</text>
                      <text class="price_css" v-if="materialInfo.actualPayment || materialInfo.actualPayment === 0">{{ materialInfo.actualPayment }}</text>
                    </view>
                    <view class="number_plus_css">
                      <uni-number-box :min="1" :max="1" v-model="value" @change="changeValue" />
                    </view>
                  </view>
                </view>
              </view>
            </view>
          </view>
          <view class="bg-ff">
            <view class="f-28 radius-15 plr-30">
              <view class="c-55 mt-35 fontWeight f-28">填写信息</view>
              <form id="#nform">
                <view class="information">
                  <view style="width: 260rpx" class="c-00">
                    <span class="redtext">*</span>
                    {{ shopdetail && shopdetail.meetingCate == 0 ? '家长联系方式：' : '家长联系方式：' }}
                  </view>
                  <view class="phone-input">
                    <input
                      type="number"
                      placeholder-class="placeholder-style"
                      v-model="parentMobile"
                      @blur="changeParent()"
                      @input="changeMobile()"
                      name="number"
                      :focus="parentFocuse"
                      :placeholder="shopdetail && shopdetail.meetingCate == 0 ? '请输入家长联系方式' : '请输入家长联系方式'"
                      class="input c-55"
                      maxlength="11"
                    />
                  </view>
                  <view v-if="parentMobile" @click="changeFouce('parentFocuse')">
                    <image src="https://document.dxznjy.com/dxSelect/image/edit_icon.png" style="width: 30upx; height: 30upx"></image>
                  </view>
                </view>
                <view class="information">
                  <view :style="shopdetail.goodsType != 2 ? 'width:210rpx' : 'width:260rpx'" class="c-00">
                    <span class="redtext">*</span>
                    {{ shopdetail && shopdetail.meetingCate == 0 ? '学员姓名：' : '学员姓名：' }}
                  </view>
                  <view class="phone-input">
                    <picker
                      v-if="shopdetail.goodsType != 2"
                      class="btnchange"
                      :disabled="showFalse"
                      @change="bindPickerChange"
                      @click="getStudentPopu"
                      :value="studentIndex"
                      :range="studentList"
                      range-key="realname"
                      name="grade"
                    >
                      <view class="c-55">{{ studentIndex >= 0 ? studentList[studentIndex].realname : '请输入学员姓名' }}</view>
                    </picker>
                    <input
                      v-else
                      type="text"
                      :maxlength="12"
                      placeholder-class="placeholder-style"
                      v-model="trialclassStudent"
                      name="trialname"
                      placeholder="请输入学员姓名"
                      class="input c-55"
                    />
                  </view>
                </view>
                <view class="information">
                  <view style="width: 260rpx" class="c-00">
                    <span class="redtext">*</span>
                    推荐人姓名：
                  </view>
                  <view class="phone-input">
                    <input
                      type="text"
                      placeholder-class="placeholder-style"
                      v-model="recommendInfo.name"
                      name="trialname"
                      :disabled="showRecommend"
                      :focus="referrerFocuse"
                      placeholder="请输入推荐人姓名"
                      class="input c-55"
                    />
                  </view>
                  <!-- <view v-if="!showRecommend" @click="changeFouce('referrerFocuse')">
                    <image src="https://document.dxznjy.com/dxSelect/image/edit_icon.png" style="width: 30upx; height: 30upx"></image>
                  </view> -->
                </view>
                <view class="information">
                  <view style="width: 260rpx" class="c-00">
                    <span class="redtext">*</span>
                    推荐人手机号：
                  </view>
                  <view class="phone-input">
                    <input
                      type="text"
                      placeholder-class="placeholder-style"
                      v-model="recommendInfo.mobile"
                      :disabled="showRecommend"
                      name="trialname"
                      :focus="referrerFocuse"
                      placeholder="请输入推荐人手机号"
                      class="input c-55"
                    />
                  </view>
                  <!-- <view v-if="!showRecommend" @click="changeFouce('referrerFocuse')">
                    <image src="https://document.dxznjy.com/dxSelect/image/edit_icon.png" style="width: 30upx; height: 30upx"></image>
                  </view> -->
                </view>
                <view class="font14 tipText pb-30">
                  <image src="https://document.dxznjy.com/dxSelect/three/icon/prompt-icon.png" style="width: 30upx; height: 30upx"></image>
                  <text class="ml-20 c-66">推荐人没有填无</text>
                </view>
                <view class="information">
                  <view style="width: 260rpx" class="c-00">
                    <span class="redtext"></span>
                    兑换码：
                  </view>
                  <view class="phone-input">
                    <input
                      placeholder-class="placeholder-style"
                      v-model="redeemCode"
                      name="redeemCode"
                      placeholder="请输入兑换码"
                      class="c-55 redeemCode-input"
                      auto-blur
                      @blur="handleCodeBlur()"
                      style="width: 380rpx"
                    />
                  </view>
                  <view class="codelose" v-if="redeemCode && redeemCode.length > 0" @click="handleClearCode()">
                    <uni-icons type="clear" size="24" color="#B1B1B1"></uni-icons>
                  </view>
                </view>
                <view class="ptb-15 mt-30 f-28 bold">订单备注</view>
                <view class="p-30 bg-f7">
                  <textarea placeholder="请输入" :maxlength="200" placeholder-class="placeholder-style" @input="inputtext" class="remark f-30"></textarea>
                  <text class="orderRemark">{{ this.remark.length }}/200</text>
                </view>
              </form>
            </view>
            <!-- #ifdef APP-PLUS -->
            <view class="payment_css f-24">
              <text class="c-55">使用</text>
              <text class="ml-8 display_inline">微信支付</text>
            </view>
            <!-- #endif -->
            <view class="flex mt-50 pb-30">
              <view class="paybtn f-32 c-ff" @click="coursePay(2)">
                立即支付
                <text class="ml-10">￥{{ materialInfo.actualPayment }}</text>
              </view>
            </view>
          </view>
        </view>
      </uni-popup>
      <!-- 实物 交付课正式课 通用商品 时间类型商品 -->
      <uni-popup ref="materialObjectPopup" type="bottom" @change="changeMaterialDialog">
        <view class="content-popup">
          <view class="content_top_popup" :style="shopdetail.goodsType == 1 ? 'margin-bottom:8rpx' : ''">
            <view class="icon-clear" @click="closeMaterialDialog">
              <view class="order_title">订单支付</view>
            </view>
            <view v-if="shopdetail.goodsType == 1">
              <view v-if="addressInfo && addressInfo.addressId" @click="getAddress">
                <view class="f-28 c-55 mtb-15">配送至：</view>
                <view class="f-28 c-55 lh-44">{{ addressInfo.cityName }}{{ addressInfo.provinceName }}{{ addressInfo.districtName }}{{ addressInfo.address }}</view>
                <view class="f-28 c-55 lh-42 mt-15">
                  <text>{{ addressInfo.consigneeName }}</text>
                  <text class="tet_css">{{ addressInfo.consigneePhone }}</text>
                </view>
              </view>
              <view v-else class="ptb-55" @click="getAddress">请选择收货地址</view>
            </view>
          </view>
          <view class="bg-ff material_content">
            <view class="product_content_css">
              <view class="product_info">
                <view class="radius-15">
                  <image :src="materialInfo.goodsPicUrl" class="couser_image"></image>
                </view>
                <view class="product_right">
                  <view>
                    <view class="f-24 price_tangerine">
                      <text class="price_title">实付价</text>
                      <text class="price_icon">￥</text>
                      <text class="price_css" v-if="materialInfo.actualPayment || materialInfo.actualPayment === 0">{{ materialInfo.actualPayment }}</text>
                    </view>
                    <view class="number_plus_css">
                      <uni-number-box v-if="shopdetail.goodsType == 3" :min="1" :max="1" v-model="value" @change="changeValue" />
                      <uni-number-box v-else :min="1" :max="500" v-model="value" @change="changeValue" />
                    </view>
                  </view>
                </view>
              </view>
              <!-- 通用商品可用优惠券 时间类型商品 -->
            </view>
            <view v-if="shopdetail.goodsType == 3">
              <view v-for="spec in shopdetail.goodsSpecPriceList" :key="spec.id">
                <view v-if="spec.specLevelOne == '自有'" class="mt-24">
                  <view class="f-24 c-33">课程（节）</view>
                  <view v-if="SecondBuy" class="flex-a-c c-55 f-28 mt-16">
                    <view v-if="specLevelTwoFalse1" class="specLevelTwo_css f-22 c-ff radius-5">
                      {{ changeSpecLevelOneValue }}
                    </view>
                    <uni-number-box
                      v-else
                      :min="minSpecLevelOneValue"
                      :max="maxSpecLevelOneValue"
                      :step="maxSpecLevelOneValue"
                      @change="(val) => changeSelfCourseValue(val, spec)"
                      v-model="changeSpecLevelOneValue"
                    />
                    <view :disabled="minSpecLevelOneValue == -1" v-show="minSpecLevelOneValue != -1" @click="saveAndModify()" class="ml-20">
                      {{ specLevelTwoFalse1 ? '修改' : '保存' }}
                    </view>
                    <view class="ml-20" v-if="(couponInfo && couponInfo.couponId) || realRedeemCode">
                      价格：{{ calculateInfo.finalPrice >= 0 ? calculateInfo.finalPrice : '' }}
                    </view>
                    <view class="ml-20" v-else>
                      价格：{{
                        identityType == 4 || parentMemberType == 5
                          ? ((spec.goodsVipPrice / spec.specLevelTwo) * changeSpecLevelOneValue).toFixed(2)
                          : ((spec.goodsOriginalPrice / spec.specLevelTwo) * changeSpecLevelOneValue).toFixed(2)
                      }}
                    </view>
                  </view>
                  <view v-else class="flex-a-c c-55 f-28 mt-16">
                    <view class="specLevelTwo_css f-22 c-ff radius-5">{{ spec.specLevelTwo }}</view>

                    <view class="ml-20" v-if="(couponInfo && couponInfo.couponId) || realRedeemCode">
                      价格：{{ calculateInfo.finalPrice >= 0 ? calculateInfo.finalPrice : '' }}
                    </view>

                    <view class="ml-20" v-else>价格：{{ identityType == 4 || parentMemberType == 5 ? spec.goodsVipPrice.toFixed(2) : spec.goodsOriginalPrice.toFixed(2) }}</view>
                  </view>
                </view>
                <view v-if="spec.specLevelOne == '交付'" class="mt-24">
                  <view class="f-24 c-33">交付课程（节）</view>
                  <view class="flex-a-c c-55 f-28 mt-16">
                    <view v-if="specLevelTwoFalse" class="specLevelTwo_css f-22 c-ff radius-5">{{ specLevelTwoValue }}</view>
                    <uni-number-box
                      v-else
                      :min="minSpecLevelTwoValue"
                      :max="showNum ? maxSecondSpecLevelTwoValue : maxSpecLevelTwoValue"
                      @change="(val) => changeCourseValue(val, spec)"
                      v-model="specLevelTwoValue"
                    />
                    <view :disabled="maxSpecLevelTwoValue == 0" @click="specLevelTwoFalse = !specLevelTwoFalse" class="ml-20">
                      {{ specLevelTwoFalse ? '修改' : '保存' }}
                    </view>

                    <view class="ml-20">
                      价格：{{
                        identityType == 4 || parentMemberType == 5
                          ? ((spec.goodsVipPrice / spec.specLevelTwo) * specLevelTwoValue).toFixed(2)
                          : ((spec.goodsOriginalPrice / spec.specLevelTwo) * specLevelTwoValue).toFixed(2)
                      }}
                    </view>
                  </view>
                </view>
              </view>
            </view>
            <view v-else class="mt-12 c-55 f-28">
              <view class="mt-35">
                <view>{{ materialInfo.specTypeOne }}</view>
                <view>
                  <text
                    v-for="(info, index) in materialInfo.specNameOne"
                    :key="index"
                    @click="getSelectName('One', info)"
                    :class="['mt-25 mr-25 item_css', { active_css: info == selectSpecNameOne }]"
                  >
                    {{ info }}
                  </text>
                </view>
              </view>
              <view class="mt-35">
                <view>{{ materialInfo.specTypeTwo }}</view>
                <view>
                  <text
                    v-for="(info, index) in materialInfo.specNameTwo"
                    :key="index"
                    @click="getSelectName('Two', info)"
                    :class="['mt-25 mr-25 item_css', { active_css: info == selectSpecNameTwo }]"
                  >
                    {{ info }}
                  </text>
                </view>
              </view>
            </view>
            <view v-if="shopdetail.goodsType != 1" class="f-28 radius-15">
              <view class="c-55 mt-35 fontWeight f-28">填写信息</view>
              <form id="#nform">
                <view class="information">
                  <view style="width: 260rpx" class="c-00">
                    <span class="redtext">*</span>
                    {{ shopdetail && shopdetail.meetingCate == 0 ? '家长联系方式：' : '家长联系方式：' }}
                  </view>
                  <view class="phone-input">
                    <input
                      type="number"
                      placeholder-class="placeholder-style"
                      :disabled="!isShowChangePhoneNumber"
                      v-model="parentMobile"
                      @input="changeMobile()"
                      name="number"
                      :focus="parentFocuse"
                      :placeholder="shopdetail && shopdetail.meetingCate == 0 ? '请输入家长联系方式' : '请输入家长联系方式'"
                      class="input c-55"
                      maxlength="11"
                      @blur="changeParent()"
                    />
                  </view>
                  <view v-if="parentMobile && isShowChangePhoneNumber" @click="changeFouce('parentFocuse')">
                    <image src="https://document.dxznjy.com/dxSelect/image/edit_icon.png" style="width: 30upx; height: 30upx"></image>
                  </view>
                </view>
                <view class="information">
                  <view style="width: 210rpx" class="c-00">
                    <span class="redtext">*</span>
                    {{ shopdetail && shopdetail.meetingCate == 0 ? '学员姓名：' : '学员姓名：' }}
                  </view>
                  <view class="phone-input">
                    <picker
                      class="btnchange"
                      :disabled="showFalse"
                      @click="getStudentPopu"
                      @change="bindPickerChange"
                      :value="studentIndex"
                      :range="studentList"
                      range-key="realname"
                      name="grade"
                    >
                      <view class="c-55">{{ studentIndex >= 0 ? studentList[studentIndex].realname : '请输入学员姓名' }}</view>
                    </picker>
                  </view>
                </view>
                <!-- 实物商品 通用商品 -->
                <template v-if="(shopdetail.goodsType != 1 && shopdetail.goodsType != 8) || !isHideRecommend">
                  <view class="information">
                    <view style="width: 260rpx" class="c-00">
                      <span class="redtext">*</span>
                      推荐人姓名：
                    </view>
                    <view class="phone-input">
                      <input
                        type="text"
                        placeholder-class="placeholder-style"
                        v-model="recommendInfo.name"
                        name="trialname"
                        :disabled="showRecommend"
                        :focus="referrerFocuse"
                        placeholder="请输入推荐人姓名"
                        class="input c-55"
                      />
                    </view>
                    <!--  <view v-if="recommendInfo.name && identityType != 4 && parentMemberType != 5" @click="changeFouce('referrerFocuse')">
                      <image src="https://document.dxznjy.com/dxSelect/image/edit_icon.png" style="width: 30upx; height: 30upx"></image>
                    </view> -->
                  </view>
                  <view class="information">
                    <view style="width: 260rpx" class="c-00">
                      <span class="redtext">*</span>
                      推荐人手机号：
                    </view>
                    <view class="phone-input">
                      <input
                        type="text"
                        placeholder-class="placeholder-style"
                        v-model="recommendInfo.mobile"
                        name="trialname"
                        :disabled="showRecommend"
                        :focus="referrerFocuse"
                        placeholder="请输入推荐人手机号"
                        class="input c-55"
                      />
                    </view>
                    <!-- <view v-if="recommendInfo.mobile && identityType != 4 && parentMemberType != 5" @click="changeFouce('referrerFocuse')">
                      <image src="https://document.dxznjy.com/dxSelect/image/edit_icon.png" style="width: 30upx; height: 30upx"></image>
                    </view> -->
                  </view>
                  <view class="font14 tipText pb-30">
                    <image src="https://document.dxznjy.com/dxSelect/three/icon/prompt-icon.png" style="width: 30upx; height: 30upx"></image>
                    <text class="ml-20 c-66">推荐人没有填无</text>
                  </view>
                </template>
                <view class="information">
                  <view style="width: 260rpx" class="c-00">
                    <span class="redtext"></span>
                    兑换码：
                  </view>
                  <view class="phone-input">
                    <input
                      placeholder-class="placeholder-style"
                      v-model="redeemCode"
                      name="redeemCode"
                      placeholder="请输入兑换码"
                      class="c-55 redeemCode-input"
                      auto-blur
                      @blur="handleCodeBlur()"
                      style="width: 380rpx"
                    />
                  </view>
                  <view class="codelose" v-if="redeemCode && redeemCode.length > 0" @click="handleClearCode()">
                    <uni-icons type="clear" size="24" color="#B1B1B1"></uni-icons>
                  </view>
                </view>
                <view class="ptb-15 mt-30 f-28 bold">订单备注</view>
                <view class="p-30 bg-f7">
                  <textarea placeholder="请输入" :maxlength="200" placeholder-class="placeholder-style" @input="inputtext" class="remark f-30"></textarea>
                  <text class="orderRemark">{{ this.remark.length }}/200</text>
                </view>
              </form>
            </view>
            <!-- #ifdef APP-PLUS -->
            <view class="payment_css f-24">
              <text class="c-55">使用</text>
              <text class="ml-8 display_inline">微信支付</text>
            </view>
            <!-- #endif -->
            <view class="flex mt-50">
              <view class="paybtn f-32 c-ff" @click="coursePay(2)">
                立即支付
                <text class="ml-10">￥{{ materialInfo.actualPayment }}</text>
              </view>
            </view>
          </view>
        </view>
      </uni-popup>
      <!-- 正式课 -->
      <uni-popup ref="formalCourse" type="bottom" @change="changePopup">
        <view class="content-popup">
          <view class="icon-clear" @click="closeDialog">
            <view class="order_title">订单支付</view>
            {{ couponList.length }}
          </view>
          <view class="product_content_css">
            <view class="product_info">
              <view class="radius-15" v-if="shopdetail.bannerImages && shopdetail.bannerImages.length">
                <image :src="shopdetail.bannerImages[0]" class="couser_image"></image>
              </view>
              <view class="product_right">
                <view>
                  <view class="f-24 price_tangerine">
                    <text class="price_title">实付价</text>
                    <text class="price_icon">￥</text>
                    <text class="price_css">
                      {{ userinfo.identityType == 0 ? (cartTotalPrice ? cartTotalPrice : shopdetail.originalPrice) : cartTotalPrice ? cartTotalPrice : shopdetail.memberPrice }}
                    </text>
                  </view>
                  <view class="number_plus_css">
                    <uni-number-box :min="1" :max="500" v-model="value" @change="changeValue" />
                  </view>
                </view>
                <view></view>
              </view>
            </view>
          </view>
          <view class="">
            <view class="plr-30 pb-30 bg-ff radius-15 mtb-30">
              <view class="ptb-30 f-32 bold">订单备注</view>
              <view class="p-30 bg-f7">
                <textarea placeholder="请输入" :maxlength="200" placeholder-class="placeholder-style" @input="inputtext" class="remark f-30"></textarea>
                <text class="orderRemark">{{ this.remark.length }}/200</text>
              </view>
            </view>
            <view class="flex">
              <view v-if="userinfo.identityType == 0" class="paybtn f-32 c-ff" @click="coursePay">
                立即支付
                <text class="ml-10">￥{{ cartTotalPrice ? cartTotalPrice : shopdetail.originalPrice }}</text>
              </view>
              <view v-if="userinfo.identityType != 0" class="paybtn f-32 c-ff" @click="coursePay">
                立即支付
                <text class="ml-10">￥{{ cartTotalPrice ? cartTotalPrice : shopdetail.memberPrice }}</text>
              </view>
            </view>
          </view>
        </view>
      </uni-popup>
      <!-- 购买记录 -->
      <uni-popup ref="morePopup" type="bottom" @change="changePopup">
        <view class="browse-popup positionRelative">
          <view class="dialog-icon" @click="closeBrowse">
            <uni-icons type="clear" size="30" color="#B1B1B1"></uni-icons>
          </view>
          <view class="t-c f-32 c-00 p-30">购买记录</view>
          <view class="flex-s browse-title">
            <view>买家</view>
            <view class="flex-s" style="width: 380rpx">
              <view>份数</view>
              <view>购买时间</view>
            </view>
          </view>
          <scroll-view :scroll-top="0" scroll-y="true" style="height: 79%">
            <view class="pb-30" v-for="(item, index) in buyDataList" :key="index">
              <view class="flex-s plr-30 mt-30">
                <view class="flex-a-c">
                  <image :src="item.headPortrait || avaUrl" class="head-img"></image>
                  <text class="f-28">{{ item.nickName }}</text>
                </view>
                <view class="flex-s" style="width: 380rpx">
                  <view class="c-66 pl-5 f-28">+{{ item.purchaseQuantity }}</view>
                  <view class="c-66 f-28">{{ formatItem(item.createdTime) }}</view>
                </view>
              </view>
            </view>
            <view v-if="buyDataList.length > 0" class="ptb-30">
              <u-divider text="到底了"></u-divider>
            </view>
          </scroll-view>
        </view>
      </uni-popup>
      <!-- 分享弹窗 -->
      <sharePopup ref="sharePopups"></sharePopup>
      <uni-popup ref="paymentPopup" type="center" style="padding: 0" @change="changePopup">
        <view class="content_css">
          <view class="content_title_css">
            <view class="f-30 bold lh-36">购买成功</view>
            <view class="f-26 c-77 lh-44 mt-55">
              <view>您购买的课程已自动发放至--我的课程</view>
              <view>点击查看课程后，无法再发起退款申请</view>
            </view>
            <view @tap="paymentPopupClose" class="active_css button_css f-26 mt-70">我已知晓</view>
            <view class="close_css" @tap="$refs.paymentPopup.close()"></view>
          </view>
          <image class="curriculum_image" mode="widthFix" src="https://document.dxznjy.com/course/68d3c5370be54a81808e0b1efe64c1cf.png"></image>
        </view>
      </uni-popup>
      <uni-popup ref="studentPopup" type="center" style="padding: 0" @change="changePopup">
        <view class="content_css">
          <view class="content_title_css">
            <view class="f-30 c-33 lh-44 content_center_css">
              <span v-if="showStudentType == 1">暂未绑定学员，点击{{ showStudentType == 1 ? '' : '此学生鼎学能课时已经全部购买' }}</span>
              <view v-if="showStudentType == 2">此学生鼎学能课时已经全部购买，点击</view>
              <span @tap="studentPopupClose" class="color_student f-26">添加学员</span>
            </view>
            <view class="close_css" @tap="$refs.studentPopup.close()"></view>
          </view>
          <image class="curriculum_image" mode="widthFix" src="https://document.dxznjy.com/course/68d3c5370be54a81808e0b1efe64c1cf.png"></image>
        </view>
      </uni-popup>
      <!-- 弹幕 -->
      <barrage v-if="shopdetail && shopdetail.meetingCate == 0" ref="Barrage" :barrageData="barrageList" style="z-index: 1"></barrage>
    </view>
    <view v-else class="loading-container">
      <view class="loading-spinner"></view>
    </view>
  </view>
</template>

<script>
  const { $http, $showSuccess, $showMsg, $navigationTo } = require('@/util/methods.js');
  import Config from '@/util/config.js';
  import Util from '@/util/util.js';
  import Sumperman from '@/common/superman.js';
  import dayjs from 'dayjs';
  const MD5 = require('../util/md5.js');
  let secretkey = 'Jkk4ml1Of8';
  let vid = '';
  let ts = new Date().getTime(); //获取时间
  let sign = '';
  const { httpUser } = require('@/util/luch-request/indexUser.js');
  import courseCatalog from './components/courseCatalog.vue'; //视频列表组件
  import Barrage from './components/barrage/barrage.vue'; // 弹幕插件
  // #ifdef MP-WEIXIN
  import sensors from 'sa-sdk-miniprogram';
  // #endif
  // #ifdef APP-PLUS
  // const sensors = uni.requireNativePlugin('Sensorsdata-UniPlugin-App');
  // #endif
  import sharePopup from '@/components/sharePopup.vue';
  export default {
    components: {
      Barrage,
      courseCatalog,
      sharePopup
    },
    data() {
      return {
        CurriculumCodeArr: Config.CurriculumCodeArr,
        noClick: true, //防抖
        //保利威视频
        playerIdcont: 'polyvPlayercont',
        startTime: 0,
        ts: ts,
        sign: sign,
        width: '100%',
        payInfo: {},
        orderNo: '',
        flag1: false,
        value: 1,
        show: false,
        id: '',
        currentIndex: 1,
        identityType: uni.getStorageSync('identityType'), //  0 普通用户 1 合伙人 2 俱乐部 3 合伙人&俱乐部 4-会员
        parentMemberType: 0, // 0：非会员，5：会员
        swiperheight: 750,
        productTop: 358,
        tabindex: 1,
        shopdetail: null,
        showSpecLevelOne: false,
        sceneId: '',
        notifyShow: false, // 消息提示
        closeable: true,
        imgHost: getApp().globalData.imgsomeHost,
        invitationInfo: {}, //邀请信息
        calculateInfo: {}, //优惠券信息
        crouseType: 0,
        userList: [
          {
            name: '课程简介'
          },
          {
            name: '课程目录'
          }
        ],
        courseInfo: {},
        tabName: '课程简介',
        lineBg: 'https://document.dxznjy.com/course/01c96465d9ea46f69aa97465b1201aef.png',
        // 课程
        goods_list: [],
        order_total_price: 0,
        disabled: false,
        addressInfo: {
          address: '',
          buyerName: '',
          buyerPhone: '',
          addressId: ''
        },
        params: {
          type: 'default',
          message: '支付成功',
          duration: 3000,
          url: '/splitContent/order/helpOrder?app=2'
        },
        materialInfo: {},
        selectSpecNameTwo: '',
        selectSpecNameOne: '',
        cartTotalPrice: 0,
        useCoin: false,
        userinfo: {},
        remark: '',
        code1: '',
        height: 0,
        shareId: '', //分享id
        invitationInfo: {}, // 邀请信息
        shardInfo: {}, //邀请人信息
        recommendInfo: {
          //推荐人信息
          mobile: '',
          name: ''
        },
        /** 评价type */
        evaTypeList: [
          {
            key: '0',
            value: '家长'
          },
          {
            key: '1',
            value: '超人'
          },
          {
            key: '4',
            value: '超级会员'
          },
          {
            key: '5',
            value: '家长会员'
          },
          {
            key: '6',
            value: '超级合伙人'
          },
          {
            key: '7',
            value: '超级俱乐部'
          },
          {
            key: '8',
            value: '超级品牌'
          }
        ],
        trialclassStudent: '', //试课学员姓名
        orderId: '',
        parentMobile: '', //家长联系方式
        specPriceId: undefined, //实物价格id
        specPriceTwoId: '',
        parentFocuse: false,
        referrerFocuse: false,
        showTrialClass: false, //是否展示试课单
        buyNum: 1, // 课程购买数量
        amount: {}, // 分润收益
        rollShow: false,
        token: '', // 判断用户是否登录
        avaUrl: 'https://document.dxznjy.com/dxSelect/home_avaUrl.png',
        rateValue: 1, // 评价评分
        couponList: [],
        couponInfo: {},
        studentInfo: {},
        studentList: [], //学员信息
        studentIndex: undefined,
        noticeList: [
          //公告栏播报
          {
            avaUrl: 'https://document.dxznjy.com/dxSelect/home_avaUrl.png',
            name: '王**开',
            time: '刚刚购买了一件'
          },
          {
            avaUrl: 'https://document.dxznjy.com/dxSelect/home_avaUrl.png',
            name: '王**开',
            time: '9分钟前购买了一件'
          },
          {
            avaUrl: 'https://document.dxznjy.com/dxSelect/home_avaUrl.png',
            name: '王**开',
            time: '20分钟前购买了一件'
          },
          {
            avaUrl: 'https://document.dxznjy.com/dxSelect/home_avaUrl.png',
            name: '王**开',
            time: '30分钟前购买了一件'
          }
        ],
        page: 1,
        showStudentType: 1,
        showFalse: true,
        showRecommend: true,
        no_more: false,
        specLevelTwoValue: 0, //交付课时数量
        specLevelTwoInfo: {},
        maxSpecLevelTwoValue: 0, //交付课最大课时
        minSpecLevelTwoValue: 1, //交付课最小课时
        maxSpecLevelOneValue: 0, //交付课自有课时
        minSpecLevelOneValue: 0, //交付课自有课时
        maxSpecLevelTwoInfoCopy: {},
        specLevelOneValue: 0, //自由课时数量
        specLevelTwoFalse: true,
        specLevelTwoFalse1: true,
        changeSpecLevelOneValue: 0, //选择自有课时数
        secondBuyHaveCouresgoodsVipPrice: 0, //第二次购买自由课时vip价格
        secondBuyHaveCouresgoodsOriginalPrice: 0, //第二次购买自由课时真实价格
        oldSpecLevelOneValue: 0,
        evaluatelist: {
          list: []
        }, // 评论列表
        buyDataList: [], // 购买记录
        barrageList: {}, // 弹幕
        flag: false,
        availableList: [], //可领取列表
        newPrice: 0, //自有交付课
        userCode: uni.getStorageSync('user_id') ? uni.getStorageSync('user_id') : '',
        goodsId: '',
        SecondBuy: false,
        maxSecondSpecLevelTwoValue: 0, //最大二次购买交付课时
        courseOnePrice: 0,
        courseTowPrice: 0,
        showNum: false,
        getStudentInfo: true,
        redeemCode: '', //兑换码
        realRedeemCode: '',
        activityInfo: {},
        bannerId: '',
        //甄选app
        app: 0,
        payType: 'wxpay',
        removeListener: null,
        isJumpNext: false
      };
    },
    computed: {
      // 正式课 可以修改家长信息（合伙人以上身份支持修改，会员身份及普通用户账号不支持修改，仅可帮自己购买）
      isShowChangePhoneNumber() {
        return true;
        // return this.shopdetail && this.shopdetail.goodsType == 3 && this.userinfo && (this.userinfo.merchantCode || this.userinfo.julebuCode || this.userinfo.brandCode);
      },
      // 是否显示推荐人输入
      isHideRecommend() {
        return this.shopdetail && this.shopdetail.goodsType == 8 && this.shopdetail.enableCommander == 0;
      }
    },
    watch: {
      realRedeemCode(newVal, oldVal) {
        uni.showLoading({
          title: '计算中...',
          mask: true
        });
        if (newVal && newVal != oldVal) {
          this.getCalculate('', newVal);
          this.couponInfo = {};
        } else {
          this.handleCodeBlur();
          this.getCalculate();
        }
      }
    },
    onUnload() {
      // #ifdef APP-PLUS
      if (this.app && !this.isJumpNext) {
        plus.runtime.quit();
      }
      // #endif
    },
    onLoad(e) {
      // #ifdef APP-PLUS
      this.app = e.app;
      this.$handleTokenFormNative(e);
      this.removeListener = uni.$addAppPayGlobalEvtListener(this.sucees, this.fail, this.payInfo.orderId, this.flag1);
      // #endif
      console.log(e, 222);
      console.log('2222222222222222222222222');
      // 判断推荐人信息
      if (e.id == undefined) {
        this.id = 'c499556f9da64dce19b723296c884bc4';
        this.sceneId = e.scene;
      } else {
        if (e.id == '1116761476733865984') {
          e.id = '1283596029749366784';
        }
        this.id = e.id;
        this.identityType = uni.getStorageSync('identityType');
        this.sceneId = uni.getStorageSync('user_id');
        this.userCode = this.sceneId ?? '';
        let data = {
          userId: e.scene,
          invitationCode: '',
          secCode: ''
        };
        uni.setStorageSync('invitationInfo', data);
      }

      if (e.bannerId) {
        this.bannerId = e.bannerId;
      }
      //埋点-商品详情页
      // #ifdef MP-WEIXIN
      getApp().sensors.track('$MPViewScreen', {
        pageName: '商品详情页',
        goodsId: this.id,
        bannerId: this.bannerId
      });
      // #endif

      // 活动邀请
      if (e.activityId && e.shareUserCode) {
        this.activityInfo = {
          activityId: e.activityId,
          shareUserCode: e.shareUserCode
        };
      }

      this.getInvitation(e);
      if (this.shareId != undefined && this.shareId != '') {
        this.getMobile();
      }
      this.params.url = '/splitContent/order/paymentSuccess';
      this.getBarrageList();
      //埋点
      let enterType = 'jump';
      if (e.sampshare && e.scene) {
        enterType = 'link';
      } else if (e.scene && e.type == 1) {
        enterType = 'qrcode';
      }
      // #ifdef MP-WEIXIN
      console.log(enterType);
      sensors.track('courseDetailEvt', {
        $enterType: enterType,
        $refereeId: this.shareId
      });
      // #endif
      this.detail();

      // 立即购买唤起弹窗
      uni.$on('purchasePop-up', () => {
        // 在这里执行页面刷新的操作
        this.buySubject(); // 假设 getData 是一个获取数据的方法
      });
    },
    beforeDestroy() {
      // #ifdef APP-PLUS
      if (this.removeListener) {
        this.removeListener();
      }
      // #endif
    },
    onShow() {
      if (this.flag1) {
        // #ifdef MP-WEIXIN
        uni.$tlpayResult(this.sucees, this.fail, this.payInfo.orderId);
        // #endif
      }
      let token = uni.getStorageSync('token');
      this.getEvaluateList();
      this.browseList();
      if (token) {
        this.getAvailableCoupons();
        this.homeData();
        this.getIncome();
        this.getStudent();
      }
      if (uni.getStorageSync('address')) {
        this.addressInfo = uni.getStorageSync('address');
      } else {
        if (token) this.getAddressList();
      }
    },
    onShareAppMessage() {
      return {
        title: this.shopdetail.goodsShareTextList[0].shareText,
        imageUrl: this.shopdetail.goodsSharePoster, //分享封面
        //如果有参数的情况可以写path
        path: '/pages/beingShared/index?id=' + this.id + '&type=6&scene=' + this.sceneId + '&bannerId=' + this.bannerId
      };
    },
    methods: {
      getRichText(text) {
        // #ifdef APP-PLUS
        return text.replace(/<img/g, '<img class="vertical-img"');
        // #endif
        // #ifdef MP-WEIXIN
        // return text.replaceAll('style="max-width:100%;"', 'style="vertical-align:bottom;max-width:100%;"');
        return text.replaceAll('<img ', '<img class="vertical-img"');
        // #endif
      },

      //判断自有课时是否能修改
      saveAndModify() {
        let that = this;
        if (this.minSpecLevelOneValue == -1) return;
        that.specLevelTwoFalse1 = !that.specLevelTwoFalse1;
      },
      // 修改二次购买自由课程数量
      async changeSelfCourseValue(e, item) {
        let that = this;
        let num = e;
        let list = this.shopdetail.goodsSpecPriceList.filter((spec) => spec.specLevelOne == '自有');
        if (e == 0) {
          that.specLevelTwoValue = 1;
          that.showNum = false;
        } else {
          that.showNum = true;
          that.changeSpecLevelOneValue = e;
          that.specLevelTwoValue = e + that.maxSpecLevelTwoValue;
          that.maxSecondSpecLevelTwoValue = that.specLevelTwoValue;
        }

        // 获取自由课时,总价格
        that.secondBuyHaveCouresgoodsVipPrice = ((item.goodsVipPrice / item.specLevelTwo) * num).toFixed(2);
        // 获取自由课时,原始总价格
        that.secondBuyHaveCouresgoodsOriginalPrice = ((item.goodsOriginalPrice / item.specLevelTwo) * num).toFixed(2);
        //自有课时价格
        that.courseOnePrice = Number(this.identityType == 4 || this.parentMemberType == 5 ? that.secondBuyHaveCouresgoodsVipPrice : that.secondBuyHaveCouresgoodsOriginalPrice);
        this.courseTowPrice = Number(
          this.identityType == 4 || this.parentMemberType == 5
            ? ((that.specLevelTwoInfo.goodsVipPrice / that.specLevelTwoInfo.specLevelTwo) * that.specLevelTwoValue).toFixed(2)
            : ((that.specLevelTwoInfo.goodsOriginalPrice / that.specLevelTwoInfo.specLevelTwo) * that.specLevelTwoValue).toFixed(2)
        );
        // 支付价格
        this.materialInfo.actualPayment = (that.courseTowPrice + that.courseOnePrice).toFixed(2);
      },

      sucees() {
        this.flag1 = false;
        this.flag = false;
        this.handleSavePayRecord();
        if (this.shopdetail.goodsType == 4) {
          this.$refs.paymentPopup.open();
        } else {
          this.redirectToOrderIndex();
        }
      },
      fail(type) {
        this.flag1 = false;
        this.flag = false;
        // 取消支付跳到支付取消页面
        if (type === 'cancel') {
          uni.navigateTo({
            url: `/splitContent/order/payCancel?orderId=${this.payInfo.sourceOrderId}&cancelType=class&type=create`
          });
        }
      },
      fails() {
        uni.showToast({
          title: '支付失败',
          icon: 'none',
          duration: 2000
        });
        setTimeout(function () {
          uni.redirectTo({
            url: '/splitContent/order/helpOrder?app=2'
          });
        }, 1500);
        this.flag1 = false;
        this.flag = false;
      },
      // 弹幕
      colrdo(data) {
        //插入一条弹幕
        this.$refs.lffBarrage.add({
          item: data
        });
      },
      // 禁止滚动穿透
      changePopup(e) {
        this.rollShow = e.show;
      },
      getStudentPopu() {
        if (this.showFalse) {
          this.showStudentType = 1;
          this.$refs.studentPopup.open();
        }
      },
      // 弹幕
      async getBarrageList() {
        let _this = this;
        const res = await $http({
          url: 'zx/order/evaluate/bulletCommentList',
          data: {
            courseId: _this.id
          }
        });
        if (res) {
          // 给个数组即可
          this.barrageList = res.data;
          // this.$refs.Barrage.startBarrage(res.data);
        }
      },
      //收货地址
      async getAddressList() {
        let _this = this;
        const resdata = await $http({
          url: 'zx/order/userAddressList'
        });
        if (resdata) {
          _this.addressInfo = resdata.data[0] || {};
        }
      },
      //获取收货地址
      getAddress() {
        uni.navigateTo({
          url: '/splitContent/address/list/list?from=goods'
        });
      },
      async getEvaluateList() {
        let _this = this;
        const res = await $http({
          url: 'zx/order/evaluate/orderEvaluatePage',
          data: {
            courseId: _this.id,
            page: _this.page
          }
        });
        if (res) {
          _this.evaluatelist = res.data;
        }
      },
      async browseList() {
        const res = await $http({
          url: 'zx/order/top-hundred-orders',
          data: {
            goodsId: this.id,
            pageNum: 1,
            pageSize: 100
          }
        });
        if (res) {
          this.buyDataList = res.data;
        }
      },
      // 获取首页信息
      async homeData() {
        let _this = this;
        const res = await $http({
          url: 'zx/user/userInfoNew'
        });
        if (res) {
          _this.parentMemberType = res.data.parentMemberType;
          _this.userinfo = res.data;
          _this.recommendInfo.name = res.data.nickName;
          _this.recommendInfo.mobile = res.data.mobile;
        }
      },
      //改版家长手机号
      changeParent() {
        if (this.shopdetail.goodsType == 2 && (this.identityType == 4 || this.parentMemberType == 5)) {
          if (this.userinfo.mobile != this.parentMobile) {
            this.showRecommend = false;
          } else {
            if (this.recommendInfo.name && this.recommendInfo.mobile) {
              this.showRecommend = true;
            }
          }
        }
      },
      // 获取商品详情
      async detail() {
        let _this = this;
        _this.goodsId = _this.id;
        const res = await $http({
          url: 'zx/wap/goods/single/detail',
          data: {
            goodsId: _this.id,
            userId: uni.getStorageSync('user_id') ? uni.getStorageSync('user_id') : ''
          }
        });
        if (res) {
          _this.shopdetail = res.data;
          if (_this.shopdetail.goodsType === 3) {
            // const newPrice =
            _this.shopdetail.goodsSpecPriceList.forEach((item) => {
              console.log('123', item.goodsVipPrice, _this.materialInfo.actualPayment);
              // if (_this.identityType == 4) {
              // 	_this.materialInfo.actualPayment = item.goodsVipPrice = item.goodsVipPrice + _this.materialInfo.actualPayment
              // } else {
              // 	item.goodsVipPrice = item.goodsVipPrice
              // }
            });
          }
          _this.shopdetail.goodsSpecPriceList.forEach((item) => {
            if (item.specLevelOne == '交付') {
              this.specLevelTwoValue = item.specLevelTwo;
              this.maxSpecLevelTwoValue = item.specLevelTwo;
              this.maxSpecLevelTwoInfoCopy = {
                ...item
              };
              this.specLevelTwoInfo = item;
            } else if (item.specLevelOne == '自有') {
              this.specLevelOneValue = item.specLevelTwo;
              this.oldSpecLevelOneValue = item.specLevelTwo;
            }
          });
          _this.shopdetail.goodsCatalogueList.forEach((item) => {
            item.down = true;
          });
          if (_this.shopdetail.goodsType == 1) {
            this.swiperheight = 750;
            this.productTop = 730;
          } else {
            this.swiperheight = 375;
            this.productTop = 358;
          }
        }
        this.getHeight();
      },
      bindloadedmetadata() {
        let polyvPlayerContext = this.selectComponent('#polyv-player-id');
        polyvPlayerContext.switchQuality(1);
      },
      getHeight() {
        let _this = this;
        _this.height = '100vh';
        setTimeout(() => {
          const query = uni.createSelectorQuery().in(_this);
          query
            .select('.product_content_main')
            .boundingClientRect((data) => {
              _this.height = Number(data.bottom) + 'px';
            })
            .exec();
        }, 1000);
      },
      setCurrent(e) {
        this.currentIndex = e.detail.current + 1;
      },
      // 打开弹窗
      openCart() {
        if (this.id == 'c499556f9da64dce19b723296c884bc4' || this.id == 'f2cf0e76538473a7179c993674e09bdf') {
          uni.showToast({
            icon: 'none',
            title: '不能加入购物车哦'
          });
        } else {
          this.show = true;
        }
      },
      // 查看更多评价
      goUrl() {
        if (this.evaluatelist.list.length > 0) {
          uni.navigateTo({
            url: '/splitContent/order/evaluateList?id=' + this.id
          });
        } else {
          this.$util.alter('暂无更多评论');
        }
      },
      // 查看学习资料
      // 查看学习资料
      goProfile(shopdetail) {
        console.log('mmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmm');
        //  根据不同的课程大类id跳转不同的学习资料
        console.log('跳转学习资料', shopdetail);
        if (this.CurriculumCodeArr.includes(shopdetail.curriculumCode)) {
          uni.navigateTo({
            url: `/Coursedetails/study/learningMaterials?goodsId=${shopdetail.goodsId}&curriculumId=${shopdetail.curriculumId}&curriculumCode=${shopdetail.curriculumCode}&goodsType=${shopdetail.goodsType}&id=${this.id}`
          });
        } else {
          //
          uni.navigateTo({
            url: `/Coursedetails/study/courseMaterials?id=${this.id}&goodsId=${shopdetail.goodsId}&curriculumId=${shopdetail.curriculumId}&curriculumCode=${shopdetail.curriculumCode}&goodsType=${shopdetail.goodsType}`
          });
        }
      },
      // 时间处理
      formatItem(createtime, show) {
        return Util.timeago(createtime, show); // 总收益
      },
      closeBrowse() {
        this.$refs.morePopup.close();
      },
      // 浏览记录查看更多
      seeMore() {
        this.$refs.morePopup.open();
      },
      getTimeText(specName) {
        let text = '';
        if (!isNaN(specName)) {
          // 时间类型商品
          switch (Number(specName)) {
            case 7:
              text = '周卡7天';
              break;
            case 30:
              text = '月卡30天';
              break;
            case 90:
              text = '季卡90天';
              break;
            case 180:
              text = '半年卡180天';
              break;
            default:
              text = `天卡${specName}天`;
              break;
          }
        } else {
          text = specName;
        }

        console.log('text', text);
        return text;
      },
      // 购买    goodsType  1:实物商品，2:交付课-体验课，3:交付课-正式课，4:录播课，5:会议，6:鼎币商城 9时间类型商品
      async buySubject() {
        this.flag = false;
        this.maxSecondSpecLevelTwoValue = this.maxSpecLevelTwoValue;
        console.log(this.maxSecondSpecLevelTwoValue);

        this.couponInfo = {};
        if (!uni.getStorageSync('token')) {
          uni.navigateTo({
            url: '/Personalcenter/login/login'
          });
        } else {
          let _this = this;
          // _this.$refs.materialObjectPopup.open()
          //3:交付课-正式课
          if (_this.shopdetail.goodsType == 3 && this.studentInfo.studentCode) {
            if (
              this.shopdetail.curriculumName == '鼎学能' ||
              this.shopdetail.curriculumName == '鼎数学' ||
              this.CurriculumCodeArr.includes(this.shopdetail.curriculumCode) ||
              this.shopdetail.curriculumName == '珠心算' ||
              this.shopdetail.curriculumName == '拼音法' ||
              this.shopdetail.curriculumName == '拼音法（高年级）' ||
              this.shopdetail.curriculumName.includes('1对')
            ) {
              //获取课程信息
              // this.getBuyInfo();
              //获取课程
              this.getSummary(this.studentInfo);
            }
          }
          //4:录播课  2:交付课-体验课
          if (_this.shopdetail.goodsType == 4 || _this.shopdetail.goodsType == 2) {
            _this.$refs.experiencePopup.open();
            // #ifdef MP-WEIXIN
            //埋点-立即购买-打开订单支付弹框
            getApp().sensors.track('openOrderPayPopup', {
              name: '打开订单支付弹框',
              type: '课程',
              goodsId: _this.id
            });
            // #endif

            this.materialInfo.goodsOriginalPrice = this.shopdetail.goodsOriginalPrice;
            this.materialInfo.goodsPicUrl = this.shopdetail.goodsPicUrl;
            if (this.specLevelTwoValue && this.specLevelTwoInfo) {
              this.changeCourseValue(this.specLevelTwoValue, this.specLevelTwoInfo);
            } else {
              if ((this.identityType && this.identityType == 4) || this.parentMemberType == 5) {
                this.materialInfo.actualPayment = this.shopdetail.goodsVipPrice;
              } else {
                this.materialInfo.actualPayment = this.shopdetail.goodsOriginalPrice;
              }
            }
          } else {
            _this.$refs.materialObjectPopup.open();
            // #ifdef MP-WEIXIN
            //埋点-立即购买-打开订单支付弹框
            getApp().sensors.track('openOrderPayPopup', {
              name: '打开订单支付弹框',
              type: '商品',
              goodsId: _this.id
            });
            // #endif
            _this.materialInfo = {
              specNameOne: [],
              specNameTwo: [],
              specTypeOne: '',
              specTypeTwo: ''
            };
            // whetherSelected
            _this.shopdetail.goodsSpecList.forEach((item) => {
              if (item.specLevel == 1) {
                if (this.shopdetail.goodsType != 9) {
                  _this.materialInfo.specNameOne.push(item.specName);
                } else {
                  let text = this.getTimeText(item.specName);
                  _this.materialInfo.specNameOne.push(text);
                }
                _this.materialInfo.specTypeOne = item.specType;
              } else {
                _this.materialInfo.specTypeTwo = item.specType;
                _this.materialInfo.specNameTwo.push(item.specName);
              }
            });

            this.shopdetail.goodsSpecPriceList.forEach((item) => {
              if (item.whetherSelected) {
                this.selectSpecNameOne = item.specLevelOne;
                this.selectSpecNameTwo = item.specLevelTwo;
                this.materialInfo = {
                  ...this.materialInfo,
                  ...item
                };
                this.specPriceId = item.id;
                if (_this.shopdetail.goodsType == 3) {
                  this.specLevelTwoValue = item.specLevelTwo;
                }
                if ((this.identityType && this.identityType == 4) || this.parentMemberType == 5) {
                  this.materialInfo.actualPayment =
                    this.couponInfo.couponId && (this.calculateInfo.finalPrice || this.calculateInfo.finalPrice == 0)
                      ? this.calculateInfo.finalPrice
                      : Number(this.buyNum * item.goodsVipPrice).toFixed(2);
                } else {
                  this.materialInfo.actualPayment =
                    this.couponInfo.couponId && (this.calculateInfo.finalPrice || this.calculateInfo.finalPrice == 0)
                      ? this.calculateInfo.finalPrice
                      : Number(this.buyNum * item.goodsOriginalPrice).toFixed(2);
                }
              }
              if (item.specLevelOne == '自有') {
                this.specPriceId = item.id;
              } else {
                this.specPriceTwoId = item.id;
              }
            });
            if (_this.shopdetail.goodsType == 3) {
              if (this.specLevelTwoValue && this.specLevelTwoInfo) {
                this.changeCourseValue(this.specLevelTwoValue, this.specLevelTwoInfo);
              } else {
                if ((this.identityType && this.identityType == 4) || this.parentMemberType == 5) {
                  this.materialInfo.actualPayment = this.shopdetail.goodsVipPrice;
                } else {
                  this.materialInfo.actualPayment = this.shopdetail.goodsOriginalPrice;
                }
              }
              this.materialInfo.goodsPicUrl = this.shopdetail.goodsPicUrl;
              this.materialInfo.goodsVipPrice = this.shopdetail.goodsVipPrice;
              this.materialInfo.goodsOriginalPrice = this.shopdetail.goodsOriginalPrice;
            }
            if (_this.materialInfo.specNameOne.findIndex((item) => item == this.selectSpecNameOne) == -1) {
              this.selectSpecNameOne = _this.materialInfo.specNameOne[0];
              this.getSelectName('One', this.selectSpecNameOne);
            }
          }
          console.log(this.calculateInfo, 101010);
          // if (this.calculateInfo.finalPrice) {
          // 	this.materialInfo.actualPayment = this.calculateInfo.finalPrice;
          // }
          // 自有课程数为0
          if (this.showSpecLevelOne) {
            this.couponList = [];
            this.couponInfo = {};
          } else {
            _this.getCouponList();
          }
          console.log(this.materialInfo);
          console.log('----------------------------------------------------');
        }
      },
      //获取优惠券 /
      async getCouponList(key) {
        let _this = this,
          arr = [];
        // const res = await $http({
        // 	url: "zx/operation/goods/coupon/list",
        // 	data: {
        // 		goodsId: this.shopdetail.goodsId,
        // 		payPrice: this.materialInfo.goodsOriginalPrice * this.value,
        // 	},
        // });
        const res = await $http({
          url: 'zx/wap/coupon/user/usable/list',
          data: {
            goodsId: _this.shopdetail.goodsId,
            userId: uni.getStorageSync('user_id') || '',
            shareable: 1
          }
        });
        if (res) {
          if (res.data) {
            _this.couponList = res.data;
            // 改变商品数量且有选择优惠券
            if (key) {
              console.log(_this.couponInfo.couponUserReceiveId);
              let index = res.data.findIndex((item) => item.couponUserReceiveId == _this.couponInfo.couponUserReceiveId);
              if (index >= 0) {
                _this.getCalculate(_this.couponInfo.couponId);
              } else {
                _this.couponInfo = {};
                _this.materialInfo.actualPayment = (Number(_this.value) * Number(_this.materialInfo.goodsOriginalPrice)).toFixed(2);
              }
            }
            _this.couponList.forEach((item) => {
              if (item.couponUserReceiveId == _this.couponInfo.couponUserReceiveId) {
                item.check = true;
              } else {
                item.check = false;
              }
            });
          }
        }
      },
      selectCoupon() {
        this.$refs.couponPopup.open();
        this.redeemCode = '';
        if (!this.couponInfo.couponId) {
          this.couponInfo = this.couponList[0];
          this.couponList[0].check = true;
          this.getCalculate(this.couponInfo.couponId);
        }
      },
      //切换优惠券item
      changeCoupon(item) {
        console.log('item1', item, this.couponInfo);
        if (item.check === true) {
          this.couponList.forEach((info) => {
            if (item.couponUserReceiveId == info.couponUserReceiveId) {
              info.check = false;
            }
          });
          this.couponInfo = {};
          this.getCalculate('');
        } else {
          this.couponInfo = item;
          this.getCalculate(item.couponId);
          this.couponList.forEach((info) => {
            if (item.couponUserReceiveId == info.couponUserReceiveId) {
              info.check = true;
            } else {
              info.check = false;
            }
          });
        }

        console.log('item2', item, this.couponInfo);
      },
      // /zx/wab/order/calculate/price
      //获取优惠券
      async getCalculate(id, code) {
        let _this = this;
        const res = await $http({
          url: 'zx/wap/order/calculate/price',
          method: 'post',
          data: {
            couponId: id,
            goodsId: this.shopdetail.goodsId,
            quantity: this.value,
            specPriceId: this.specPriceId,
            redemptionCode: code // 兑换码
          }
        });
        if (res) {
          this.calculateInfo = res.data;
          this.calculateInfo.finalPrice = this.calculateInfo.finalPrice < 0 ? 0 : this.calculateInfo.finalPrice;
          console.log('999999912', this.shopdetail, this.calculateInfo.finalPrice);
          // 判断是否是正式课
          if (this.shopdetail.goodsType == 3) {
            this.changeCourseValue(this.specLevelTwoValue, this.specLevelTwoInfo);
          } else {
            this.materialInfo.actualPayment = this.calculateInfo.finalPrice;
          }
        }
      },
      getSelectName(key, info) {
        this['selectSpecName' + key] = info;
        if (this.selectSpecNameOne) {
          this.shopdetail.goodsSpecPriceList.forEach((item) => {
            console.log('🚀 ~ this.shopdetail.goodsSpecPriceList.forEach ~ item:', item);
            // 时间类型商品显示需要拼接天季年
            if ((this.shopdetail.goodsType == 9 ? this.getTimeText(item.specLevelOne) : item.specLevelOne) + item.specLevelTwo == this.selectSpecNameOne + this.selectSpecNameTwo) {
              this.materialInfo = {
                ...this.materialInfo,
                ...item
              };
              this.specPriceId = item.id;
              this.getCouponList();
            }
          });
          console.log('🚀 ~ getSelectName ~ this.materialInfo:', this.materialInfo);
          if ((this.identityType && this.identityType == 4) || this.parentMemberType == 5) {
            this.materialInfo.actualPayment = (Number(this.value) * Number(this.materialInfo.goodsVipPrice)).toFixed(2);
          } else {
            this.materialInfo.actualPayment = (Number(this.value) * Number(this.materialInfo.goodsOriginalPrice)).toFixed(2);
          }
          this.couponInfo = {};
        }
      },
      //点击好友分享
      shareFriend() {
        console.log('this.shopdetail.goodsSharePoster', this.shopdetail.goodsSharePoster);
        let shareContent = {
          type: '6',
          id: this.shopdetail.goodsId,
          bannerId: this.bannerId
        };
        shareContent.imgurl = this.shopdetail.goodsSharePoster;
        shareContent.title = this.shopdetail.goodsShareTextList[0] ? this.shopdetail.goodsShareTextList[0].shareText : null;
        let shareInfo = {
          title: this.shopdetail.goodsShareTextList[0].shareText,
          imageUrl: this.shopdetail.goodsSharePoster, //分享封面
          path: '/pages/beingShared/index?id=' + this.id + '&type=6&scene=' + this.sceneId
        };
        this.$refs.sharePopups.open(shareContent, shareInfo, 'mini');
      },
      sharePoster() {
        this.$refs.sharePopup.close();
        uni.navigateTo({
          url: `/splitContent/poster/index?type=6&id=${this.id}`
        });
      },
      //立即开通
      getBecomMember() {
        //2024-11-6 紧急修改 购买超级会员修改成购买家长会员
        $navigationTo('Personalcenter/my/parentVipEquity?type=2');
        // $navigationTo('Personalcenter/my/nomyEquity?type=2');
      },
      async collectChange(key) {
        if (key == 1) {
          let _this = this;
          // #ifdef MP-WEIXIN
          //埋点-收藏
          getApp().sensors.track('collectClick', {
            name: '收藏'
          });
          // #endif

          const res = await $http({
            url: 'zx/wap/goods/collect/save',
            method: 'post',
            data: {
              goodsId: this.shopdetail.goodsId,
              userId: uni.getStorageSync('user_id') ? uni.getStorageSync('user_id') : ''
            }
          });
          if (res) {
            this.shopdetail.whetherCollect = true;
          }
        } else {
          let _this = this;
          const res = await $http({
            url: 'zx/wap/goods/collect/cancel',
            method: 'post',
            data: {
              goodsIdList: [this.shopdetail.goodsId],
              userId: uni.getStorageSync('user_id') ? uni.getStorageSync('user_id') : ''
            }
          });
          if (res) {
            this.shopdetail.whetherCollect = false;
          }
        }
      },
      //app客服
      contactApp() {
        uni.share({
          provider: 'weixin',
          scene: 'WXSceneSession',
          openCustomerServiceChat: true,
          corpid: Config.contactId,
          customerUrl: Config.contactUrl,
          success: function (res) {
            console.log(res);
          },
          fail: function (res) {
            console.log(res);
          }
        });
      },
      kfAppHandle() {
        uni.$customerService();
      },
      tabsClick(item) {
        this.tabName = item.name;
        this.getHeight();
      },
      changeDown(index) {
        this.$set(this.videoList[Number(index)], 'down', !this.videoList[Number(index)].down);
      },
      openBenefits() {
        this.$refs.popup.open();
      },
      closeDialog() {
        this.$refs.popup.close();
        this.$refs.experiencePopup.close();
        this.$refs.formalCourse.close();
        this.value = 1;
        let result = [
          {
            buyNum: Number(this.value),
            courseId: this.id
          }
        ];
        this.updateTotalPrice(result);
      },

      changeExperienceDialog(e) {
        this.closeMDEventFunc('课程');
        this.rollShow = e.show;
      },
      changeMaterialDialog(e) {
        this.studentIndex = undefined;

        this.closeMDEventFunc('商品');
        this.rollShow = e.show;
      },
      closeExperienceDialog() {
        this.closeMDEventFunc('课程');
        this.$refs.experiencePopup.close();
      },
      closeMaterialDialog() {
        this.closeMDEventFunc('商品');
        this.closeMaterialObjectPopup();
      },

      //埋点-立即购买-关闭订单支付弹框
      closeMDEventFunc(type) {
        // #ifdef MP-WEIXIN
        getApp().sensors.track('closeOrderPayPopup', {
          name: '关闭订单支付弹框',
          type: type,
          goodsId: this.id
        });
        // #endif
      },

      // 初始获取分享人信息
      async getInvitation(e) {
        let _this = this;
        _this.invitationInfo = uni.getStorageSync('invitationInfo');
        _this.shardInfo = uni.getStorageSync('shardInfo');
        if (this.identityType != 4 && this.parentMemberType != 5) {
          _this.recommendInfo.name = _this.shardInfo.nickName;
          _this.recommendInfo.mobile = _this.shardInfo.mobile;
        }
        if (_this.invitationInfo) {
          _this.shareId = _this.invitationInfo.userId;
          // 取过值之后立马清除本地缓存
          uni.removeStorageSync('invitationInfo');
          uni.removeStorageSync('shardInfo');
        }
      },
      //交付课时修改
      changeCourseValue(e, item) {
        this.buyNum = e;
        let list = this.shopdetail.goodsSpecPriceList.filter((spec) => spec.specLevelOne == '自有');
        // one 自有 tow 交付
        this.courseTowPrice = Number(
          this.identityType == 4 || this.parentMemberType == 5
            ? ((item.goodsVipPrice / item.specLevelTwo) * this.buyNum).toFixed(2)
            : ((item.goodsOriginalPrice / item.specLevelTwo) * this.buyNum).toFixed(2)
        );
        console.log(this.courseTowPrice);
        //minSpecLevelOneValue =-1 无剩余课时
        console.log(this.couponInfo.couponId);
        // 正式课 且有优惠券
        if (this.shopdetail.goodsType == 3 && this.couponInfo.couponId) {
          console.log(this.minSpecLevelOneValue);
          if (this.minSpecLevelOneValue == -1 || this.minSpecLevelOneValue == -2) {
            this.courseOnePrice =
              this.couponInfo.couponId && (this.calculateInfo.finalPrice || this.calculateInfo.finalPrice == 0)
                ? this.calculateInfo.finalPrice
                : Number(this.identityType == 4 ? list[0].goodsVipPrice : list[0].goodsOriginalPrice);
          } else {
            this.courseOnePrice = this.calculateInfo.finalPrice;
          }
        }
        //  正式课 有效的兑换码
        else if (this.shopdetail.goodsType == 3 && this.realRedeemCode) {
          console.log(this.realRedeemCode);
          if (this.minSpecLevelOneValue == -1 || this.minSpecLevelOneValue == -2) {
            this.courseOnePrice =
              this.realRedeemCode && (this.calculateInfo.finalPrice || this.calculateInfo.finalPrice == 0)
                ? this.calculateInfo.finalPrice
                : Number(this.identityType == 4 ? list[0].goodsVipPrice : list[0].goodsOriginalPrice);
          } else {
            this.courseOnePrice = this.calculateInfo.finalPrice;
          }
        } else {
          // 判断是否有选择学员
          if (this.getStudentInfo) {
            this.courseOnePrice =
              (this.couponInfo.couponId || this.realRedeemCode) && (this.calculateInfo.finalPrice || this.calculateInfo.finalPrice == 0)
                ? this.calculateInfo.finalPrice
                : Number(this.identityType == 4 ? list[0].goodsVipPrice : list[0].goodsOriginalPrice);
          } else {
            this.courseOnePrice = Number(
              this.identityType == 4 || this.parentMemberType == 5
                ? (list[0].goodsVipPrice / this.oldSpecLevelOneValue) * this.changeSpecLevelOneValue
                : (list[0].goodsOriginalPrice / this.oldSpecLevelOneValue) * this.changeSpecLevelOneValue
            );
          }
        }
        this.materialInfo.actualPayment = (this.courseTowPrice + this.courseOnePrice).toFixed(2);
        console.log(this.materialInfo.actualPayment);
      },

      async changeValue(e) {
        let _this = this;
        _this.buyNum = e;
        let result = [
          {
            buyNum: Number(_this.buyNum),
            courseId: this.id
          }
        ];
        if (this.couponInfo.couponId) {
          this.getCouponList(1);
        } else {
          this.getCouponList();
          if ((this.identityType && this.identityType == 4) || this.parentMemberType == 5) {
            this.materialInfo.actualPayment = (Number(this.value) * Number(this.materialInfo.goodsVipPrice)).toFixed(2);
          } else {
            this.materialInfo.actualPayment = (Number(this.value) * Number(this.materialInfo.goodsOriginalPrice)).toFixed(2);
          }
        }
        // materialInfo.actualPayment
        this.updateTotalPrice(result);
      },

      // 更新价格
      async updateTotalPrice(result) {
        let _this = this,
          arr = [];
        const res = await $http({
          url: 'zx/course/calculatePrice',
          method: 'post',
          data: {
            courseAndNumDto: result,
            currencyNumber: 0
          }
        });
        if (res) {
          _this.cartTotalPrice = res.data.payPrice;
        }
      },

      handleSavePayRecord() {
        if (this.activityInfo.activityId && this.activityInfo.shareUserCode) {
          $http({
            url: 'zx/wap/activity/pay/record/save',
            method: 'post',
            data: {
              activityId: this.activityInfo.activityId,
              shareUserCode: this.activityInfo.shareUserCode,
              type: 1,
              orderId: this.orderId
            }
          });
        }
      },

      inputtext(e) {
        this.remark = e.detail.value;
        console.log(this.remark, '订单备注');
        console.log(this.remark.length, '订单备注长度');
        if (this.remark.length >= 200) {
          $showMsg('订单备注最多200个字');
          this.$nextTick(() => {
            this.remark = e.detail.value.substring(0, 200);
          });
          return false;
        }
      },
      // 课程支付
      async coursePay(key) {
        if (this.shopdetail.goodsType == 3) {
          if (!this.specLevelTwoFalse) {
            $showMsg('请保存购买交付课时数量');
            return;
          }
          if (!this.specLevelTwoFalse1) {
            $showMsg('请保存购买自有课时数量');
            return;
          }
        }
        let _this = this;
        if (_this.flag) {
          return;
        }

        _this.flag = true;
        uni.showLoading();
        let result = {
          buyNum: Number(_this.buyNum),
          courseId: this.id
        };
        // 只有39.9试课券才会走这里
        // specLevelTwoFalse

        if (this.shopdetail.goodsType != 1) {
          if (!_this.trialclassStudent) {
            $showMsg('请输入姓名');
            _this.flag = false;
            return false;
          }
          // 不显示推荐人
          if (this.isHideRecommend) {
            _this.recommendInfo.name = '无';
            _this.recommendInfo.mobile = '无';
          }

          // 推荐人信息
          if (!_this.recommendInfo.name) {
            $showMsg('请输入正确的推荐人姓名,如无推荐人则填写‘无’');
            _this.flag = false;
            return false;
          }
          // 推荐人信息
          if (!_this.recommendInfo.mobile) {
            $showMsg('请输入正确的推荐人手机号,如无推荐人则填写‘无’');
            _this.flag = false;
            return false;
          }
          if (_this.recommendInfo.mobile != '无') {
            if (!Util.isMobile(_this.recommendInfo.mobile)) {
              $showMsg('请输入正确的推荐人手机号');
              _this.flag = false;
              return false;
            }
          }
        }
        if (this.shopdetail.goodsType == 1) {
          if (!_this.addressInfo.addressId) {
            $showMsg('请填写收货地址');
            _this.flag = false;
            return false;
          }
        }
        // #ifdef MP-WEIXIN
        //埋点-立即购买-去支付
        getApp().sensors.track('productDetilsBuyClick', {
          name: '立即购买',
          goodsId: this.id
        });
        // #endif

        let url = '',
          params = {};
        url = 'zx/wap/order/generate';

        params.addressId = this.shopdetail.goodsType == 1 ? _this.addressInfo.addressId : undefined;
        // params.currencyNumber = _this.userinfo.currencyNumber;
        // params.discountsAmount = 0;
        params.remark = _this.remark;
        // params.courseAndNumDto = result;
        params.goods = {
          // 兑换码
          redemptionCode: this.realRedeemCode,
          couponId: this.couponInfo.couponId,
          goodsId: this.shopdetail.goodsId,
          payAmount: this.materialInfo.actualPayment,
          purchaseQuantity: this.value,
          specPriceId: this.shopdetail.goodsType != 4 ? this.specPriceId : undefined,
          couponUserReceiveId: this.couponInfo.couponUserReceiveId
        };
        if (this.shopdetail.goodsSpecPriceList && this.shopdetail.goodsSpecPriceList.length > 0) {
          this.shopdetail.goodsSpecPriceList.forEach((item) => {
            if (item.specLevelOne == '自有') {
              params.goods.couponSpecPriceId = item.id;
            }
          });
        }
        console.log('goodsSpecPriceList', this.shopdetail.goodsSpecPriceList);
        if (this.shopdetail.goodsType == 3) {
          params.goods.specPriceQuantity = this.changeSpecLevelOneValue;
          params.goods.specPriceTwoId = this.specPriceTwoId;
          params.goods.specPriceTwoQuantity = this.specLevelTwoValue;
        }
        params.shareId = _this.shareId;
        if (_this.recommendInfo != null && this.shopdetail.goodsType != 1) {
          params.referrerName = _this.recommendInfo.name;
          params.referrerPhone = _this.recommendInfo.mobile;
        }
        if (this.shopdetail.goodsType == 2) {
          params.studentName = _this.trialclassStudent;
        }
        params.parentsMobile = _this.parentMobile;
        if (this.isShowChangePhoneNumber) {
          // 可代购 实际购买人
          params.actualBuyMobile = _this.parentMobile;
        }
        params = {
          ...params,
          ...this.studentInfo
        };
        const res = await $http({
          url: url,
          method: 'post',
          data: params
        });
        if (res) {
          console.log('--res---', JSON.stringify(res));
          if (res.data) {
            _this.orderId = res.data.orderId;
            _this.orderNo = res.data.orderNo;
            // needPay 1需要支付  0不需要支付
            if (res.data.needPay == 1) {
              //todo 增加微信支付宝选择
              _this.payBtn(res.data);
            } else {
              this.handleSavePayRecord();
              _this.successorder(res.data.orderId);
            }
          } else {
            uni.hideLoading();
            uni.showModal({
              title: '温馨提示',
              content: res.message,
              showCancel: false
            });
          }
        } else {
          _this.flag = false;
        }
      },

      async payBtn(data) {
        // #ifdef APP-PLUS
        if (this.userinfo.openId == '') {
          let payCodeData = await httpUser.get('zx/user/getPaymentUserCode?mobile=' + this.recommendInfo.mobile);
          let payCode = payCodeData.data.data;
          try {
            data.userCode = payCode;
          } catch (error) {
            console.log(error);
          }
        }
        if (this.payType == 'alipay') {
          //支付宝
          data.customerParams.allInPayPayType = 'ALIPAY_MINIPROGRAM_CASHIER_VSP_ORG';
        }
        // #endif
        let _this = this;
        let resdata = await httpUser.post('mps/line/collect/order/unified/multi/collect/check', data);
        let res = resdata.data.data;
        _this.disabled = false;
        uni.hideLoading();
        if (res) {
          if (res.openAllinPayMini) {
            this.flag1 = true;
            this.payInfo = res;
            // #ifdef APP-PLUS
            uni.$appPayTlian(res, this.payType);
            // #endif
            // #ifdef MP-WEIXIN
            uni.$payTlian(res);
            // #endif
          } else {
            uni.requestPayment({
              provider: 'wxpay',
              timeStamp: res.payInfo.timeStamp,
              nonceStr: res.payInfo.nonceStr,
              package: res.payInfo.packageX,
              signType: res.payInfo.signType,
              paySign: res.payInfo.paySign,
              success: function (ress) {
                this.flag = false;
                // _this.successpay(id);
                if (_this.shopdetail.goodsType == 4) {
                  _this.$refs.paymentPopup.open();
                } else {
                  _this.redirectToOrderIndex();
                }
              },
              fail: function (err) {
                uni.showToast({
                  title: '支付失败'
                });
                this.flag = false;
                setTimeout(function () {
                  uni.redirectTo({
                    url: '/splitContent/order/helpOrder?app=2'
                  });
                }, 1500);
              }
            });
          }
        }
        this.couponInfo = {};
        this.getCouponList();
        this.$refs.experiencePopup.close();
        this.closeMaterialObjectPopup();
        this.$refs.formalCourse.close();
      },
      //关闭弹窗
      closeMaterialObjectPopup() {
        this.$refs.materialObjectPopup.close();
      },
      paymentPopupClose() {
        this.$refs.paymentPopup.close();
        this.redirectToOrderIndex();
      },
      studentPopupClose() {
        this.$refs.studentPopup.close();
        uni.navigateTo({
          url: `/Personalcenter/my/mystudentAdd?type=1&memberId=${uni.getStorageSync('user_code')}&buyPhone=${this.parentMobile}`
        });
      },
      async successorder(orderId) {
        let _this = this;
        const resdata = await $http({
          url: 'zx/course/orderPayNoMoney/' + orderId,
          data: {}
        });
        if (resdata) {
          _this.redirectToOrderIndex();
        }
      },
      getNewTime() {
        var taday = new Date();
        var year = taday.getFullYear();
        var month = taday.getMonth() + 1;
        var day = taday.getDate();
        var hours = taday.getHours();
        var min = taday.getMinutes();
        var seconds = taday.getSeconds();
        return (
          year +
          '-' +
          (month < 10 ? '0' + month : month) +
          '-' +
          (day < 10 ? '0' + day : day) +
          ' ' +
          (hours < 10 ? '0' + hours : hours) +
          ':' +
          (min < 10 ? '0' + min : min) +
          ':' +
          (seconds < 10 ? '0' + seconds : seconds)
        );
      },
      redirectToOrderIndex() {
        let _this = this;
        _this.flag = false;
        let info = {
          payAmount: this.materialInfo.actualPayment,
          payAmountMode: '微信支付',
          orderId: this.orderId,
          orderNo: this.orderNo,
          payAmountTime: this.getNewTime(),
          goodsType: this.shopdetail.goodsType
        };
        uni.setStorageSync('orderInfoPayAmount', JSON.stringify(info));
        this.isJumpNext = true;
        // #ifdef APP-PLUS
        uni.navigateTo({
          url: _this.params.url
        });
        // #endif
        // #ifdef MP-WEIXIN
        uni.redirectTo({
          url: _this.params.url
        });
        // #endif
      },

      // 根据分享id获取推荐人信息
      async getMobile() {
        let that = this;
        const res = await $http({
          url: 'zx/exp/getReferrerMobile',
          data: {
            shareId: that.shareId
          }
        });
        if (res) {
          that.recommendInfo = res.data;
          if (that.recommendInfo == null) {
            that.recommendInfo = {
              mobile: '无',
              name: ''
            };
          } else {
            if (!that.recommendInfo.name) {
              that.recommendInfo.name = '无';
            }
            if (!that.recommendInfo.mobile) {
              that.recommendInfo.mobile = '无';
            }
          }
        }
      },
      async bindPickerChange(e) {
        this.studentIndex = Number(e.target.value);
        if (e.target.value >= 0) {
          if (
            (this.shopdetail.curriculumName == '鼎学能' ||
              this.shopdetail.curriculumName == '鼎数学' ||
              this.CurriculumCodeArr.includes(this.shopdetail.curriculumCode) ||
              this.shopdetail.curriculumName == '珠心算' ||
              this.shopdetail.curriculumName == '拼音法' ||
              this.shopdetail.curriculumName == '拼音法（高年级）' ||
              this.shopdetail.curriculumName.includes('1对')) &&
            this.shopdetail.goodsType == 3
          ) {
            this.getSummary(this.studentList[this.studentIndex], !this.studentInfo.studentCode);
          }
          this.trialclassStudent = this.studentList[this.studentIndex].realname;
          // 接口返回的studentcode是小写!!!
          this.studentInfo.studentCode = this.studentList[this.studentIndex].studentcode;
          this.studentInfo.studentName = this.studentList[this.studentIndex].realname;
        }
      },
      // 我的课程列表
      async getSummary(info, show) {
        console.log(info);
        const res = await $http({
          url: 'zx/wap/course/student/course/buyInfo',
          data: {
            studentCode: info.studentcode || info.studentCode,
            curriculumId: this.shopdetail.curriculumId,
            goodsId: this.shopdetail.goodsId
          }
        });
        if (res) {
          console.log(res.data);
          this.showSpecLevelOne = false;
          this.getStudentInfo = false;
          if (res.data) {
            //haveDeliverHours:交付专用课时
            //销课交付课时 :relievedDeliverHours
            // maxSpecLevelTwoValue: 0, //交付课最大课时
            //minSpecLevelTwoValue: 0, //交付课最小课时
            //maxSpecLevelOneValue: 0, //交付课自有课时
            //         minSpecLevelOneValue: 0, //交付课自有课时
            // Number(res.data.maxBuyHaveDeliverHours) +
            this.maxSpecLevelTwoValue = Number(res.data.haveCourseHours) > 0 ? Number(res.data.haveCourseHours) : Number(res.data.maxBuyHaveDeliverHours); //可购买最大交付课时
            this.maxSpecLevelOneValue = Number(res.data.maxBuyHaveCourseHours); //可购买最大自有课时
            console.log(this.maxSpecLevelTwoValue);
            let num = Number(res.data.totalCourseHours);

            if (num > 0) {
              this.minSpecLevelTwoValue = Number(res.data.minBuyHaveDeliverHours) == -1 ? 1 : Number(res.data.minBuyHaveDeliverHours); //可购买最小交付课时
              this.minSpecLevelOneValue = Number(res.data.minBuyHaveCourseHours) == 0 ? Number(res.data.minBuyHaveCourseHours) : -1; //可购买最小自有课时
              this.SecondBuy = true;
              this.specLevelTwoValue = Number(res.data.minBuyHaveDeliverHours);
              this.changeSpecLevelOneValue = Number(res.data.minBuyHaveCourseHours) == -1 ? Number(res.data.maxBuyHaveCourseHours) : Number(res.data.minBuyHaveCourseHours);
              this.courseOnePrice = 0;
              this.courseTowPrice = 0;
            } else {
              this.SecondBuy = false;
              this.specLevelTwoValue = Number(res.data.maxBuyHaveDeliverHours);
              this.changeSpecLevelOneValue = Number(res.data.maxBuyHaveCourseHours);
            }
            if ((this.specLevelTwoValue >= 0 && res.data[0]) || (this.specLevelTwoValue < 0 && res.data[0])) {
              this.showSpecLevelOne = true;
              this.specLevelOneValue = 0;
              if (this.specLevelTwoValue <= 0) {
                this.specLevelTwoValue = 0;
                if (this.studentList.length < 3) {
                  this.showStudentType = 2;
                  this.$refs.studentPopup.open();
                } else {
                  this.showSpecLevelOne = false;
                  this.maxSpecLevelTwoValue = this.maxSpecLevelTwoInfoCopy.specLevelTwo;
                }
              }
            }
            this.changeCourseValue(this.specLevelTwoValue, this.specLevelTwoInfo);
          }
        }
      },
      // 点击修改按钮
      changeFouce(ele) {
        this[ele] = true;
      },
      // 兑换码校验格式
      validateCouponCode(code) {
        const couponCodeRegex = /^[A-Z0-9]{4}-[A-Z0-9]{4}-[A-Z0-9]{4}-[A-Z0-9]{4}$/;
        return couponCodeRegex.test(code);
      },

      // 兑换码输入框失焦
      async handleCodeBlur() {
        this.realRedeemCode = '';
        if (!this.redeemCode) {
          return;
        }
        if (!this.validateCouponCode(this.redeemCode)) {
          $showMsg('请输入正确的兑换码');
          return;
        }

        uni.showLoading({
          title: '校验中...',
          mask: true
        });
        const res = await $http({
          url: `zx/wap/coupon/redemption/code/info?redemptionCode=${this.redeemCode}`
        });

        if (res.data) {
          this.realRedeemCode = this.redeemCode;
        }
      },
      handleClearCode() {
        this.redeemCode = '';
        this.realRedeemCode = '';
      },

      async getIncome() {
        let _this = this;
        const resdata = await $http({
          url: 'zx/course/getCourseShareProfitVo',
          data: {
            courseId: _this.id
          }
        });
        if (resdata) {
          _this.amount = resdata.data;
        }
      },
      // 获取可领取优惠券
      async getAvailableCoupons() {
        const res = await $http({
          url: 'zx/wap/coupon/user/available/list',
          data: {
            goodsId: this.id,
            userId: uni.getStorageSync('user_id') ? uni.getStorageSync('user_id') : '',
            shareable: 1,
            // 可显示在小程序端
            showApp: 1
          }
        });
        if (res.code === 20000) {
          this.availableList = res.data;
        }
        console.log('user', res, this.availableList);
      },
      // 领券列表
      getCoupons() {
        this.$refs.getCoupon.open();
        this.availableList = [];
        this.getAvailableCoupons();
      },
      async receivedCoupon(item) {
        uni.showLoading({
          title: '领取中...',
          mask: true
        });
        const res = await $http({
          url: 'zx/wap/coupon/user/received',
          method: 'POST',
          data: {
            couponId: item.couponId,
            userId: uni.getStorageSync('user_id') ? uni.getStorageSync('user_id') : ''
          }
        });

        if (res.code == 20000) {
          uni.showToast({
            title: '领取成功',
            icon: 'success'
          });
          this.getAvailableCoupons();
        }
      },

      changeMobile() {
        if (this.parentMobile.length == 11) {
          this.checkRegisterAndGetStudent();
        }
      },

      async checkRegisterAndGetStudent() {
        const res = await $http({
          url: 'zx/common/zyCheckRegister?phone=' + this.parentMobile,
          method: 'get'
        });
        if (res.status == 1) {
          const byPhone = await $http({
            url: 'zx/student/studentList/byPhone?phone=' + this.parentMobile,
            method: 'get'
          });
          if (byPhone.code == 20000) {
            console.log(byPhone);
            this.studentList = byPhone.data;
            if (this.studentIndex >= 0 && this.shopdetail.goodsType == 3) {
              this.bindPickerChange(
                {
                  target: {
                    value: this.studentIndex
                  }
                },
                true
              );
            }
            if (this.studentList.length <= 0) {
              this.showFalse = true;
            } else {
              this.showFalse = false;
            }
          } else {
            uni.showToast({
              title: byPhone.message || '获取学员错误',
              icon: 'error'
            });
          }
        } else {
          uni.showToast({
            title: res.message || '手机号错误',
            icon: 'error'
          });
        }
      }
    }
  };
</script>
<style>
  .placeholder-style {
    color: #555555 !important;
    font-size: 28rpx;
  }

  .vertical-img {
    vertical-align: bottom;
  }
</style>
<style lang="scss" scoped>
  .cartIcon {
    width: 120rpx;
    height: 120rpx;
    position: fixed;
    bottom: 20vh;
    right: 30rpx;
    border-radius: 70rpx;
    box-shadow: 0 0 15rpx rgba(0, 0, 0, 0.1);
  }

  .sheets {
    position: absolute;
    bottom: 30rpx;
    right: 30rpx;
    padding: 10rpx 30rpx;
    border-radius: 40rpx;
    background-color: rgba(0, 0, 0, 0.3);
    z-index: 1;
  }

  .fixed_b {
    position: fixed;
    left: 0;
    bottom: 0;
    width: 100%;
    padding: 14upx 0 40rpx 0;
    background-color: #fff;
    box-shadow: 0 0 15upx 0 rgba(0, 0, 0, 0.2);

    .padding_left {
      padding-left: 35rpx;
    }

    .fixed_button {
      border-radius: 16rpx;
      overflow: hidden;
      display: flex;
      heigth: 74rpx;
      line-height: 74rpx;
      text-align: center;
      width: 426rpx;
      color: #fff;
      margin-right: 32rpx;

      .button_left {
        width: 194rpx;
        background-color: #56c7a5;
      }

      .button_right {
        width: 232rpx;
        background-color: #fd9b2a;
      }
    }
  }

  .add_cart {
    background-image: linear-gradient(to bottom, #88cfba, #1d755c);
    height: 90upx;
    border-radius: 50upx;
    line-height: 90upx;
    text-align: center;
    color: #fff;
    // padding: 0 20rpx;
  }

  .buy_s {
    background-image: linear-gradient(to bottom, #88cfba, #1d755c);
    height: 70upx;
    width: 200rpx;
    border-radius: 40upx;
    line-height: 70upx;
    text-align: center;
    color: #fff;
    // padding: 0 20rpx;
  }

  /deep/.u-safe-area-inset-bottom {
    padding-bottom: 20rpx !important;
  }

  .benefits {
    width: 150rpx;
    height: 60rpx;
    color: #0e5c4e;
    font-size: 28rpx;
    line-height: 60rpx;
    text-align: center;
    border-radius: 45rpx;
    border: 1px solid #0e5c4e;
  }

  /* 21天结束复习弹窗样式 */
  .reviewCard_box {
    width: 670rpx;
    /* height: 560rpx; */
    position: relative;
  }

  .reviewCard_box image {
    width: 100%;
    height: 100%;
  }

  .reviewCard {
    position: relative;
    width: 100%;
    height: 100%;
    background: #ffffff;
    color: #000;
    border-radius: 24upx;
    padding: 45upx 30upx;
    box-sizing: border-box;
  }

  .cartoom_image {
    width: 420rpx;
    position: absolute;
    top: -250rpx;
    left: 145rpx;
    z-index: -1;
  }

  .review_close {
    position: absolute;
    top: 20rpx;
    right: 20rpx;
    z-index: 1;
  }

  .content {
    display: flex;
    flex-direction: column;
    height: 100vh;
    overflow-y: scroll;

    .section_item {
      overflow: hidden;
    }

    .video_css_content {
      height: 375rpx;
      width: 750rpx;
    }

    .product_content_main {
      position: absolute;
      width: 686rpx;
      border-top-left-radius: 24rpx;
      border-top-right-radius: 24rpx;
      padding-bottom: 200rpx;
      // overflow-y: scroll;
      // height: calc(100vh - 472rpx);
      background: linear-gradient(180deg, #ffffff 0%, #f0f5fa 15%, #f9fcff 50%, #f0f5fa 100%);

      .w686 {
        width: 686rpx;
      }

      .color_red_css {
        color: #fa4f2b;
        display: inline-block;
      }

      .member_back {
        width: 210rpx;
        height: 44rpx;
        line-height: 44rpx;
        text-align: center;
        background: url('https://document.dxznjy.com/course/8442f0de8da146798babc4aa04065bc9.png') no-repeat;
        background-size: 100%;
        margin-left: 5rpx;
      }

      .goods_name_css {
        // overflow: hidden;
        // text-overflow: ellipsis;  /* 超出部分省略号 */
        // word-break: break-all;  /* break-all(允许在单词内换行。) */
        // display: -webkit-box; /* 对象作为伸缩盒子模型显示 */
        // -webkit-box-orient: vertical; /* 设置或检索伸缩盒对象的子元素的排列方式 */
        // -webkit-line-clamp: 2; /* 显示的行数 */
      }
    }
  }

  .review_btn {
    width: 250upx;
    height: 80upx;
    background: linear-gradient(180deg, #88cfba 0%, #1d755c 100%);
    border-radius: 45upx;
    font-size: 30upx;
    color: #ffffff;
    line-height: 80upx;
    justify-content: center;
    text-align: center;
  }

  /deep/.uni-popup__wrapper {
    justify-content: center !important;
  }

  // 步进器
  /deep/.uni-numbox__minus,
  /deep/.uni-numbox__plus {
    width: 48rpx;
    height: 48rpx;
    padding: 0 !important;
    // border-radius: 50% !important;
    background-color: #f7f7f7 !important;
  }

  /deep/.uni-numbox__value {
    margin: 0 !important;
    height: 60rpx !important;
    background-color: #fff !important;
    font-size: 40rpx;
    font-weight: bold;
    color: #333;
  }

  // /deep/.uni-numbox__plus{
  // 	width: 60rpx;
  // 	height: 60rpx;
  // 	padding: 0 !important;
  // 	border-radius: 50% !important;
  // 	background-color: #E0E0E0 !important;
  // }

  /deep/ .uni-numbox {
    height: 48rpx;
    // background-color: #F7F7F7 !important;
    border-radius: 48rpx !important;
  }

  /deep/ .uni-numbox__value {
    width: 69rpx !important;
    height: 48rpx !important;
    line-height: 40rpx;
  }

  // 正式课
  .content-popup {
    background-color: #f1f1f1;
    border-top-left-radius: 45rpx;
    border-top-right-radius: 45rpx;
    max-height: 90vh;
    overflow-y: scroll;

    .content_top_popup {
      background-color: #fff;
      padding: 24rpx 32rpx;

      .tet_css {
        display: inline-block;
        margin-left: 16rpx;
      }
    }

    .material_content {
      padding: 46rpx 32rpx;
    }
  }

  .remark {
    height: 150rpx;
    width: 100%;
    overflow: scroll;
  }

  .icon-clear {
    display: flex;
    justify-content: flex-end;
  }

  .flex_s {
    display: flex;
    align-items: center;
  }

  .paybtn {
    width: 686rpx;
    height: 74rpx;
    line-height: 74rpx;
    text-align: center;
    border-radius: 38rpx;
    background-color: #339378;
    margin: 0 auto;
  }

  .amount {
    background-color: #ea6031;
    border-radius: 40rpx;
    padding: 8rpx 15rpx;
    margin-left: 20rpx;
    font-size: 28rpx;
    color: #fff;
  }

  // 体验课
  .information {
    height: 108upx;
    display: flex;
    align-items: center;
    border-bottom: 1px solid #efefef;
  }

  .phone-input {
    color: #999;
    border: none;
    display: flex;
    height: 70rpx;
    font-size: 28rpx;
    align-items: center;
  }

  .redeemCode-input {
    font-family: AlibabaPuHuiTiBold;
    font-weight: normal;
    font-size: 28rpx;
    color: #555555;
    line-height: 40rpx;
  }

  .redtext {
    color: red;
    margin-right: 4rpx;
  }

  .tipText {
    padding-top: 30upx;
    display: flex;
    align-items: center;
  }

  /deep/ .input {
    width: 87% !important;
  }

  // 商品评价
  .head-img {
    width: 64rpx;
    height: 64rpx;
    border-radius: 50%;
    margin-right: 20rpx;
  }

  .golden {
    color: #886a34;
    height: 38rpx;
    font-size: 26rpx;
    padding: 0 8rpx;
    line-height: 38rpx;
    text-align: center;
    border-radius: 6rpx;
    background: linear-gradient(to right, #f5ebd6, #dec288);
  }

  .evalue-content {
    // display: -webkit-box;
    // -webkit-box-orient: vertical;
    // overflow: hidden;
    // word-break: break-all;
    // text-overflow: ellipsis;
    // -webkit-line-clamp: 2; /* 控制显示的行数 */
  }

  .content_css {
    width: 686rpx;
    /* #ifdef APP-PLUS */
    height: 100%;
    /* #endif */
    margin: 0 auto;
    position: relative;

    .curriculum_image {
      width: 686rpx;
      height: 718rpx;
    }

    .content_title_css {
      position: absolute;
      z-index: 999;
      bottom: 0;
      left: 0;
      height: 410rpx;
      width: 100%;
      text-align: center;

      .content_center_css {
        width: 100%;
        text-align: center;
        margin-top: 120rpx;
      }

      .color_student {
        color: #56c7a5;
      }
    }
  }

  // 购买记录
  .shopping {
    color: #fff;
    width: 120rpx;
    height: 50rpx;
    font-size: 28rpx;
    text-align: center;
    line-height: 50rpx;
    border-radius: 30rpx;
    background-color: #2f8c70;
  }

  .swiper-item {
    display: block;
    height: 120rpx !important;
    line-height: 120rpx;
    text-align: center;
  }

  .user_name {
    max-width: 130rpx;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }

  .browse-popup {
    background-color: #fff;
    border-top-left-radius: 45rpx;
    border-top-right-radius: 45rpx;
  }

  .dialog-icon {
    position: absolute;
    top: 25rpx;
    right: 25rpx;
  }

  .browse-title {
    padding: 30rpx;
    font-size: 30rpx;
    background-color: #f4f4f4;
  }

  /deep/ .browse-popup {
    height: 78vh !important;
  }

  /deep/ .u-divider {
    margin: 0 auto !important;
  }

  .order_title {
    width: 100%;
    text-align: center;
    font-size: 32rpx;
    color: #333;
    font-weight: bold;
  }

  .specLevelTwo_css {
    height: 50rpx;
    width: 80rpx;
    line-height: 50rpx;
    text-align: center;
    background-color: #56c7a5;
  }

  .gray_background {
    background-color: darkgray;
  }

  .product_content_css {
    .product_info {
      display: flex;
      justify-content: flex-start;

      .couser_image {
        width: 152rpx;
        height: 152rpx;
        display: block;
      }

      .product_right {
        margin-left: 24rpx;

        .price_tangerine {
          color: #fa4f2b;
          font-weight: bold;

          .price_icon {
            display: inline-block;
            margin-left: 7rpx;
            margin-right: 5rpx;
          }

          .price_title {
            font-size: 32rpx;
          }

          .price_css {
            font-size: 48rpx;
          }

          .original_price_css {
            color: #959697;

            .original_title {
              display: inline-block;
              margin-left: 16rpx;
              margin-right: 10rpx;
            }
          }
        }

        .number_plus_css {
          margin-top: 44rpx;
        }
      }
    }

    .coupon_content {
      margin-top: 48rpx;

      .coupon_left_css {
        .coupon_botton {
          display: inline-block;
          width: 154rpx;
          height: 42rpx;
          line-height: 41rpx;
          border: 1rpx solid #ff9100;
          color: #ff9100;
          text-align: center;
          border-radius: 8rpx;
          margin-left: 16rpx;
        }
      }

      .coupon_right_css {
        font-size: 28rpx;
        color: #ff9100;

        .coupon_title {
          display: inline-block;
          width: 220rpx;
          text-overflow: ellipsis;
          overflow: hidden;
          word-break: break-all;
          white-space: nowrap;
          vertical-align: middle;
        }

        /deep/.u-icon {
          display: inline-block !important;
          vertical-align: middle;
        }
      }
    }
  }

  .item_css {
    background-color: #f2f2f2;
    display: inline-block;
    border-radius: 8rpx;
    padding: 10rpx 24rpx;
  }

  .close_css {
    position: absolute;
    right: 29rpx;
    top: -11rpx;
    width: 40rpx;
    height: 40rpx;
  }

  .button_css {
    width: 230rpx;
    padding: 10rpx 0;
    margin: 0 auto;
    border-radius: 30rpx;
    margin-top: 90rpx;
  }

  .active_css {
    background-color: #56c7a5;
    color: #fff;
  }

  .payment_css {
    text-align: center;
    margin-top: 28rpx;
  }

  .display_inline {
    display: inline-block;
    color: #339378;
  }

  .couponPopup_content {
    height: 85vh;
    background-color: #fff;
    border-radius: 24rpx 24rpx 0rpx 0rpx;

    .coupon_price {
      width: 686rpx;
      height: 146rpx;
      margin: 0 auto;
      margin-top: 34rpx;
      background: url('https://document.dxznjy.com/course/5eb5e0ab050646bb80e1f69b1a2ccad9.png') no-repeat;
      background-size: 100%;
      color: #fff;

      .flex_state {
        display: flex;
        justify-content: flex-start;
        align-items: center;
        padding-left: 32rpx;
        text-align: center;
        line-height: 42rpx;

        .w96 {
          width: 96rpx;
        }

        .w65 {
          width: 56rpx;
        }

        .w124 {
          width: 124rpx;
        }

        .w96 {
          width: 96rpx;
        }

        .w75 {
          width: 75rpx;
        }

        .w60 {
          width: 60rpx;
        }

        .w55 {
          width: 55rpx;
        }
      }
    }

    .coupon_item {
      width: 686rpx;
      height: 146rpx;
      background: url('https://document.dxznjy.com/course/2613084130c84f9e80a6c91da112cdc5.png') no-repeat;
      background-size: 100%;
      display: flex;
      justify-content: flex-start;
      align-items: center;

      .coupon_left {
        width: 204rpx;
        text-align: center;
      }

      .coupon_center {
        width: 400rpx;
        margin-left: 32rpx;

        .coupon_name_css {
          color: #a25700;
        }

        .coupon_time_css {
          color: #fd9b2a;
          margin-top: 20rpx;
        }
      }

      .wh26 {
        width: 26rpx;
        height: 26rpx;
      }
    }
  }

  .display_block {
    display: inline-block;
  }

  .coupon_label_css {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    color: #fd9b2a;

    .coupon_header_css {
      padding-left: 16rpx;
      padding-right: 14rpx;
      background-color: #fff2e3;
      position: relative;
    }

    .plr1416 {
      padding-left: 14rpx;
      padding-right: 16rpx;
    }

    .coupon_header_css::after {
      content: ' ';
      border-right: 2rpx solid #fd9b2a;
      position: absolute;
      height: 26rpx;
      right: 0;
      top: 7rpx;
    }

    .coupon_item {
      border: 1rpx solid #fd9b2a;
      line-height: 34rpx;
      padding: 4rpx 0;
    }
  }

  .label_css {
    padding: 2rpx 16rpx;
    background-color: #dfffe4;
  }

  .member_content_css {
    width: 686rpx;
    height: 64rpx;
    background: url('https://document.dxznjy.com/course/7eefc948b350461c8c1e56f894ff54d6.png') no-repeat;
    background-size: 100%;

    .member_botton {
      background-color: #339378;
      margin-top: 4rpx;
    }

    .member_botton,
    .member_text {
      vertical-align: middle;
    }
  }

  .purchase_records {
    border-radius: 24rpx;

    .time_height_css {
      height: 90rpx;
      line-height: 75rpx;
      padding-top: 25rpx;
      overflow: hidden;
    }
  }

  .wh24 {
    width: 24rpx;
    height: 24rpx;
  }

  .wh40 {
    width: 40rpx;
    height: 40rpx;
  }

  .product_details {
    border-radius: 24rpx 24rpx 0 0;

    /deep/.u-tabs__wrapper__scroll-view {
      margin-top: 24rpx;
      padding-bottom: 24rpx;
    }

    /deep/.u-tabs__wrapper__nav__line {
      margin-left: 26rpx;
    }
  }

  .get-coupons {
    width: 120rpx;
    height: 48rpx;
    background: #339378;
    border-radius: 8rpx;
    margin-left: 26rpx;
    font-family: PingFang-SC, PingFang-SC;
    font-weight: bold;
    font-size: 24rpx;
    color: #ffffff;
    text-align: center;
    line-height: 48rpx;
  }

  .coupon-popup {
    background-color: #fff;
    padding: 32rpx 14rpx 34rpx 18rpx;
    border-radius: 24rpx 24rpx 0 0;

    // overflow-y: scroll;
    .coupon-title {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 42rpx;
      font-family: AlibabaPuHuiTi_2_85_Bold;
      font-size: 32rpx;
      color: #333333;
      line-height: 42rpx;
      text-align: left;
      font-style: normal;
      font-weight: bold;
      margin-bottom: 24rpx;
    }

    .content {
      max-height: 760rpx;
      background: #fbfbfb;
      border-radius: 8rpx;
      padding: 20rpx 16rpx 0 16rpx;

      .require {
        height: 42rpx;
        font-family: AlibabaPuHuiTi_2_85_Bold;
        font-size: 28rpx;
        color: #339378;
        line-height: 42rpx;
        text-align: left;
        font-style: normal;
        font-weight: bold;
        margin-left: 2rpx;
        margin-bottom: 24rpx;
      }

      .item {
        width: 686rpx;
        background: #f0fef9;
        border-radius: 16rpx;
        margin-bottom: 24rpx;
        padding: 50rpx 0 56rpx 0;

        .activity-type {
          width: 158rpx;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;

          .activity-name {
            font-family: AlibabaPuHuiTi_2_85_Bold;
            font-size: 40rpx;
            color: #489981;
            line-height: 56rpx;
            text-align: left;
            font-style: normal;
            font-weight: bold;
            margin-bottom: 4rpx;
          }

          .activity-limit {
            font-family: AlibabaPuHuiTi_2_55_Regular;
            font-size: 24rpx;
            color: #489981;
            line-height: 34rpx;
            text-align: right;
            font-style: normal;
          }
        }

        .activity-content {
          width: 358rpx;

          .title {
            margin-top: 6rpx;
            font-family: AlibabaPuHuiTi_2_85_Bold;
            font-size: 28rpx;
            color: #555555;
            line-height: 40rpx;
            text-align: left;
            font-weight: bold;
            margin-bottom: 8rpx;
          }

          .time {
            font-family: AlibabaPuHuiTi_2_55_Regular;
            font-size: 28rpx;
            color: #c5c4c4;
            line-height: 40rpx;
            text-align: left;
          }
        }

        .activity-use {
          width: 120rpx;
          height: 48rpx;
          background: #339378;
          border-radius: 24rpx;
          margin-left: 20rpx;
          font-weight: bold;
          font-size: 24rpx;
          color: #ffffff;
          line-height: 48rpx;
          text-align: center;
        }
      }
    }
  }

  .height_scroll {
    height: calc(85vh - 380rpx);
    overflow-y: scroll;
  }

  .orderRemark {
    display: flex;
    justify-content: flex-end;
  }

  ////loading
  .loading-container {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100vh;
  }

  .loading-spinner {
    width: 50px;
    height: 50px;
    border: 5px solid #f3f3f3;
    border-top: 5px solid #2e896f;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }

  @keyframes spin {
    0% {
      transform: rotate(0deg);
    }

    100% {
      transform: rotate(360deg);
    }
  }
</style>
