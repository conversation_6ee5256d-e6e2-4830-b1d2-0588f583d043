<template>
  <page-meta :page-style="'overflow:' + (showModal ? 'hidden' : 'visible')"></page-meta>
  <view>
    <!--    <view v-if="isShow">
      <page-container :show="isShow" :overlay="false" @beforeleave="beforeleave"></page-container>
    </view> -->
    <u-navbar title="AI智能报告" placeholder :bgColor="'transparent'" :fixed="false">
      <view class="u-nav-slot1" slot="left" @click="goBack">
        <u-icon name="arrow-left" color="#000" bold size="24"></u-icon>
      </view>
      <view class="u-nav-slot" slot="center">
        <view class="u-nav-slot-center" style="color: #333; font-weight: 600">AI智能报告</view>
      </view>
    </u-navbar>
    <view class="report-container">
      <view class="" style="height: 470rpx">
        <view class="toastTop">下拉刷新数据</view>
      </view>
      <!-- 基本信息 -->
      <view class="base-info">
        <view class="flex-a-c" style="height: 100%; padding: 0 20rpx; justify-content: space-around">
          <view class="left">
            <view class="f-28">
              <text style="color: #939393">学员姓名：</text>
              <text style="color: #21504b; font-weight: 600">{{ showData.studentName || '' }}</text>
            </view>
            <view class="f-28 mt-15">
              <text style="color: #939393">学员编号：</text>
              <text style="color: #21504b; font-weight: 600">{{ showData.studentCode || '' }}</text>
            </view>
            <view class="f-28 mt-15">
              <text style="color: #939393">复习时间：</text>
              <text style="color: #21504b; font-weight: 600">{{ showData.studyData || '' }}</text>
            </view>
          </view>
          <view class="right mt-25" v-if="!showModal">
            <qiun-data-charts type="arcbar" :opts="arcbarOptions" :chartData="arcbarChartData" />
          </view>
        </view>
      </view>
      <!-- 本次学习 -->
      <view class="now-study">
        <image src="https://document.dxznjy.com/dxSelect/1742809954000" style="width: 360rpx; margin-top: -20rpx" mode="widthFix"></image>
        <view class="charts-box" v-if="!showModal">
          <qiun-data-charts type="ring" :opts="ringOptions" :chartData="ringChartData" />
        </view>
      </view>
      <!-- 遗忘单词 -->
      <view class="forget-word">
        <image src="https://document.dxznjy.com/dxSelect/1742809995000" style="width: 360rpx; margin-top: -20rpx" mode="widthFix"></image>
        <view class="title color000 plr-50 ptb-10">
          <text class="colorred">红色</text>
          代表遗忘单词，
          <text class="colorgreen">绿色</text>
          代表已会单词。
        </view>
        <view class="wordList">
          <view class="mt-20" style="height: 100%">
            <view class="wordItem plr-30" v-for="(item, index) in wordData" :key="index">
              <view :class="['wordItemtext overstepSingle', item.correct ? 'colorgreen' : 'colorred']">{{ item.word }}</view>
              <view :class="['wordItemtext overstepSingle', item.correct ? 'colorgreen' : 'colorred']">{{ item.translation }}</view>
              <view class="" @click="sayWord(item)">
                <image src="https://document.dxznjy.com/course/fc1e6407cf7c44c99a42ede501501856.png" style="width: 40rpx; height: 36rpx" mode=""></image>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 综合能力测评 -->
      <!--    <view class="allRound-ablity">
        <view class="charts-box">
          <qiunDataCharts type="radar" :chartData="radarChartData" :opts="radarOptions" />
        </view>
      </view> -->

      <!-- 单词发音详情 -->
      <view class="table-data">
        <image src="https://document.dxznjy.com/dxSelect/1742809974000" style="width: 360rpx; margin-top: -20rpx" mode="widthFix"></image>
        <!-- 表头 -->
        <view class="plr-20">
          <view class="table-row header" style="background-color: #fff">
            <view class="col-word">单词</view>
            <view class="col-number">中文发音等级</view>
            <view class="col-number">英文发音等级</view>
          </view>
        </view>
        <!-- 数据行 -->
        <view class="table-container" v-if="tableData.length > 0">
          <view class="table-row" v-for="(item, index) in tableData" :key="index">
            <view class="col-word">{{ item.word }}</view>
            <view class="col-number">{{ getLevel(item.translationAccuracy) }}</view>
            <view class="col-number">{{ getLevel(item.pronunciationAccuracy) }}</view>
          </view>
        </view>
        <view class="table-container nodata" v-else>
          <image src="http://document.dxznjy.com/automation/1743152006000" style="width: 100rpx; height: 100rpx; margin-bottom: 10rpx" mode=""></image>
          <text>本次复习无单词发音~</text>
        </view>
      </view>
      <!-- 学习建议 -->
      <view class="suggest" v-if="!!richText && richTexts.length > 0">
        <image src="https://document.dxznjy.com/course/eead6163c9be4067a85c0c22975d3f92.png" style="width: 260rpx; margin-top: -20rpx" mode="widthFix"></image>
        <view class="suggest-box p-20">
          <view class="suggest-item" v-for="(item, index) in richTexts" :key="index">
            <view class="point"></view>
            <view class="" style="flex: 1">{{ item }}</view>
          </view>
        </view>
      </view>

      <!-- 遗忘单词详情 -->
      <view class="forget-detail">
        <image src="https://document.dxznjy.com/course/6732026e54e041398d9233143a045cb9.png" style="width: 260rpx; margin-top: -20rpx" mode="widthFix"></image>
        <view class="top">
          <view class="right-btn" @click="downloadWord" v-if="!noDownload">
            <image src="https://document.dxznjy.com/course/e21985a8cabc4ffe812cc17034c4d8c1.png" style="width: 32rpx; height: 32rpx" mode=""></image>
            <text>下载</text>
          </view>
          <view class="btn confirm" @click="goForget">复习遗忘单词</view>
        </view>
        <view class="plr-20">
          <view class="table-row-header">
            <view class="col-word1">单词</view>
            <view class="col-number1">遗忘次数</view>
            <view class="col-number1">轮次</view>
            <view class="col-number1">音标</view>
            <view class="col-number1">中文</view>
          </view>
        </view>
        <!-- 数据行 -->
        <view class="table-container" v-if="forgetData.length > 0">
          <view class="table-row" v-for="(item, index) in forgetData" :key="index">
            <view class="col-word">{{ item.word || '' }}</view>
            <view class="col-number">{{ item.forgetCount }}</view>
            <view class="col-number">{{ item.nowRound }}</view>
            <view class="col-number">{{ item.phoneticSymbol }}</view>
            <view class="col-number">{{ item.chinese }}</view>
          </view>
        </view>
        <view class="table-container nodata" v-else>
          <image src="http://document.dxznjy.com/automation/1743152006000" style="width: 100rpx; height: 100rpx; margin-bottom: 10rpx" mode=""></image>
          <view>本次复习单词发音优秀,无遗忘或不会的单词,继续保持!</view>
        </view>
      </view>
      <view class="footer">
        <view class="footer-btn">
          <!-- <image src="https://document.dxznjy.com/course/4a2f584112494687bcdeb2b949ffc702.png" mode=""></image> -->
          <!-- <view class="btn cancel" @click="goForget">复习遗忘单词</view> -->
          <!-- <view class="btn confirm" @click="goUrl(`/antiAmnesia/review/history?studentCode=${showData.studentCode}&reviewType=1`)">往期复习</view> -->
          <image
            src="https://document.dxznjy.com/course/49dadfd3a2df467696796656416cb28a.png"
            style="width: 327rpx"
            mode="widthFix"
            @click="goUrl(`/antiAmnesia/review/history?studentCode=${showData.studentCode}&reviewType=1`)"
          ></image>
          <button class="" hover-class="none" open-type="share" @click="handleShare">
            <image src="https://document.dxznjy.com/course/afdd6e2891734502b000525b39eb4d5f.png" style="width: 327rpx" mode="widthFix"></image>
          </button>
        </view>
      </view>
    </view>
    <u-modal :show="toastShow" title=" " showCancelButton style="z-index: 999">
      <view class="slot-content">
        <view class="modal-title">AI数据生成中，请稍候下拉刷新加载</view>
      </view>
      <view class="confirmButton1" slot="confirmButton">
        <view class="btn confim" @click="confirmFn">好的</view>
      </view>
    </u-modal>
    <survey-modal ref="surveyModal" :showModal="showModal" @confirm="confirmStar" @closeDialog="closeDialog"></survey-modal>
  </view>
</template>

<script>
  var innerAudioContext;
  import qiunDataCharts from '../components/qiun-data-charts/components/qiun-data-charts/qiun-data-charts.vue';
  import surveyModal from '../components/surveyModal.vue';
  export default {
    components: {
      qiunDataCharts,
      surveyModal
    },
    data() {
      return {
        radarChartData: {},
        radarOptions: {},
        ringChartData: {},
        ringOptions: {},
        arcbarChartData: {},
        arcbarOptions: {},
        tableData: [],
        reviewId: '',
        showData: {},
        oldShowData: {},
        studyCountData: [],
        wordData: [],
        isHistory: false,
        studentCode: '',
        isShow: true,
        richText: '',
        richTexts: [],
        forgetData: [],
        noDownload: false,
        isLoading: false,
        toastShow: false,
        showModal: false
      };
    },
    onLoad(options) {
      innerAudioContext = uni.createInnerAudioContext();
      innerAudioContext.onPlay(() => {
        console.log('开始播放');
      });
      innerAudioContext.onStop(function () {
        console.log('播放结束');
      });
      innerAudioContext.onPause(function () {
        console.log('播放暂停');
      });
      innerAudioContext.onError((err) => {
        console.log(err);
        innerAudioContext.stop();
      });
      console.log(options);
      if (options) {
        // this.studentCode = '625057770';
        // this.reviewId = '1379773335300231168';
        // options.history == 1;
        this.studentCode = options.studentCode;
        this.reviewId = options.reviewId;
        this.isHistory = options.history == 1 ? true : false;
        this.noDownload = options.noDownload && options.noDownload == 1 ? true : false;
        console.log(this.richTexts);
        this.toastShow = true;
        setTimeout(() => {
          if (options.history == 1) {
            // this.getReviewReport(options.reviewId);
            this.$nextTick(() => {
              let newPromise = new Promise((resolve) => {
                resolve();
              });
              //然后异步执行echarts的初始化函数
              newPromise.then(() => {
                //  此dom为echarts图标展示dom
                this.getReviewReport(options.reviewId);
              });
            });
          } else {
            this.oldShowData = JSON.parse(decodeURIComponent(options.showData));
            this.$nextTick(() => {
              let newPromise = new Promise((resolve) => {
                resolve();
              });
              //然后异步执行echarts的初始化函数
              newPromise.then(() => {
                this.getReviewReport(options.reviewId);
              });
            });
          }
          this.toastShow = false;
          this.getReviewReport(options.reviewId);
          this.getQuestionStatus();
        }, 3000);
      }
    },
    onPullDownRefresh() {
      if (this.isLoading) return;
      this.isLoading = true;
      this.$nextTick(() => {
        let newPromise = new Promise((resolve) => {
          resolve();
        });
        //然后异步执行echarts的初始化函数
        newPromise.then(() => {
          this.getReviewReport(this.reviewId);
        });
      });
      setTimeout(() => {
        this.isLoading = false;
        uni.stopPullDownRefresh();
      }, 3000);
    },
    mounted() {
      // this.$nextTick(() => {
      //   let newPromise = new Promise((resolve) => {
      //     resolve();
      //   });
      //   //然后异步执行echarts的初始化函数
      //   newPromise.then(() => {
      //     //  此dom为echarts图标展示dom
      //     this.getReviewReport();
      //   });
      // });
    },
    watch: {
      showModal: {
        deep: true,
        immediate: true,
        handler(newValue, oldValue) {
          if (newValue) {
            this.isLoading = true;
            uni.stopPullDownRefresh();
          } else {
            this.isLoading = false;
          }
        }
      }
    },

    methods: {
      async getQuestionStatus() {
        let res = await this.$httpUser.get('znyy/word/review/queryQuestionnaire', {
          studentCode: this.studentCode,
          type: 1
        });
        if (res && res.data.success) {
          console.log(res, '===============');
          if (res.data.data) {
            return;
          } else {
            setTimeout(() => {
              this.showModal = true;
            }, 5000);
          }
        }
      },
      confirmStar(e) {
        let that = this;
        console.log(e);
        let obj = {};
        // obj = { ...e };
        obj.studentCode = that.studentCode;
        obj.questionnaireContent = JSON.stringify(e);
        obj.type = 1;
        // console.log(obj, '111111111111');
        // return;
        that.$httpUser.post('znyy/word/review/saveQuestionnaire', obj).then((res) => {
          console.log(res);
          if (res && res.data.success) {
            uni.showToast({
              title: '感谢您的参与',
              icon: 'none'
            });
            setTimeout(() => {
              that.showModal = false;
            }, 1000);
          }
        });
        // this.showModal = false;
      },
      closeDialog() {
        this.showModal = false;
      },
      confirmFn() {
        this.toastShow = false;
      },
      goForget() {
        // getApp().sensors.track('aiReviewReportClick', {
        //   name: '复习遗忘单词'
        // });
        let that = this;
        // goUrl(`/antiAmnesia/review/aiForgetReview?studentCode=${showData.studentCode}&isGoBack=${true}
        uni.setStorage({
          key: 'aiForgetWordData',
          data: that.forgetData,
          success() {
            uni.navigateTo({
              url: `/antiAmnesia/review/aiForgetReview?reviewId=${this.reviewId}&isGoBack=${true}&studentCode=${that.studentCode}`
            });
          }
        });
        // uni.navigateTo({
        //   url:`/antiAmnesia/review/aiForgetReview?studentCode=${showData.studentCode}&isGoBack=${true}`
        // })
      },
      async downloadWord() {
        // getApp().sensors.track('aiReviewReportClick', {
        //   name: '下载'
        // });
        let that = this;
        if (that.forgetData.length < 1)
          return uni.showToast({
            title: '暂无遗忘单词可打印',
            icon: 'none'
          });
        let data = JSON.stringify(that.forgetData);
        let res = await this.$httpUser.post('znyy/word/review/download/file', data);
        console.log(res.data.data.files);
        let arr = [];
        // res.data.data.files.forEach((item) => {
        //   arr.push(this.removeBase64Prefix(item));
        // });
        arr = res.data.data.files;
        console.log(arr);
        this.downLoadImg(arr);
      },
      // 下载地址(图片) - iOS和Android兼容
      downLoadImg(downImgSrc) {
        let that = this;

        // #ifdef APP-PLUS
        // 获取系统信息判断平台
        const systemInfo = uni.getSystemInfoSync();
        console.log('当前平台:', systemInfo.platform);

        if (systemInfo.platform === 'android') {
          // Android平台权限处理
          that.handleAndroidPermissions(downImgSrc);
        } else if (systemInfo.platform === 'ios') {
          // iOS平台直接下载（iOS会自动处理相册权限）
          that.handleiOSDownload(downImgSrc);
        } else {
          // 其他平台直接下载
          that.startDownload(downImgSrc);
        }
        // #endif

        // #ifdef MP-WEIXIN || H5
        // 小程序和H5端不支持下载到相册
        uni.showToast({
          title: '请在App中使用此功能',
          icon: 'none'
        });
        // #endif
      },

      // Android平台权限处理
      handleAndroidPermissions(downImgSrc) {
        let that = this;

        // Android 13 (API 33) 及以上版本不需要 WRITE_EXTERNAL_STORAGE 权限
        const androidVersion = plus.os.version;
        const isAndroid13Plus = parseInt(androidVersion) >= 13;

        if (isAndroid13Plus) {
          // Android 13+ 直接下载
          console.log('Android 13+，直接下载');
          that.startDownload(downImgSrc);
        } else {
          // Android 12 及以下版本需要存储权限
          console.log('Android 12及以下，申请存储权限');
          plus.android.requestPermissions(
            ['android.permission.WRITE_EXTERNAL_STORAGE'],
            (resultObj) => {
              let granted = true;
              for (let result of resultObj.granted) {
                if (result !== 'android.permission.WRITE_EXTERNAL_STORAGE') {
                  granted = false;
                  break;
                }
              }
              if (!granted) {
                uni.showToast({
                  title: '请授权存储权限以保存图片',
                  icon: 'none',
                  duration: 3000
                });
                return;
              }
              that.startDownload(downImgSrc);
            },
            (error) => {
              console.log('Android权限申请失败:', error);
              uni.showToast({
                title: '存储权限被拒绝，无法保存图片',
                icon: 'none',
                duration: 3000
              });
            }
          );
        }
      },

      // iOS平台下载处理
      handleiOSDownload(downImgSrc) {
        let that = this;
        console.log('iOS平台，开始下载');

        // iOS平台会在保存时自动申请相册权限
        that.startDownload(downImgSrc);
      },
      startDownload(imgList) {
        let that = this;
        let totalImages = imgList.length;
        let successCount = 0;
        let failCount = 0;

        console.log(`开始下载 ${totalImages} 张图片`);

        // 显示下载进度
        uni.showLoading({
          title: `下载中 0/${totalImages}`,
          mask: true
        });

        imgList.forEach((item, index) => {
          console.log(`下载第 ${index + 1} 张图片:`, item);

          uni.downloadFile({
            url: item,
            success: (res) => {
              console.log(`图片 ${index + 1} 下载响应:`, res);

              if (res.statusCode === 200) {
                // 保存到相册
                uni.saveImageToPhotosAlbum({
                  filePath: res.tempFilePath,
                  success: () => {
                    successCount++;
                    console.log(`图片 ${index + 1} 保存成功，当前成功: ${successCount}`);

                    // 更新进度
                    uni.showLoading({
                      title: `下载中 ${successCount + failCount}/${totalImages}`,
                      mask: true
                    });

                    // 检查是否全部完成
                    that.checkDownloadComplete(successCount, failCount, totalImages);
                  },
                  fail: (err) => {
                    console.error(`图片 ${index + 1} 保存失败:`, err);

                    // 检查是否用户取消
                    if (err.errMsg && err.errMsg.includes('cancel')) {
                      console.log('用户取消保存');
                      uni.hideLoading();
                      return;
                    }

                    failCount++;

                    // 更新进度
                    uni.showLoading({
                      title: `下载中 ${successCount + failCount}/${totalImages}`,
                      mask: true
                    });

                    // 检查是否全部完成
                    that.checkDownloadComplete(successCount, failCount, totalImages);
                  }
                });
              } else {
                failCount++;
                console.error(`图片 ${index + 1} 下载失败，状态码:`, res.statusCode);

                // 更新进度
                uni.showLoading({
                  title: `下载中 ${successCount + failCount}/${totalImages}`,
                  mask: true
                });

                // 检查是否全部完成
                that.checkDownloadComplete(successCount, failCount, totalImages);
              }
            },
            fail: (err) => {
              failCount++;
              console.error(`图片 ${index + 1} 下载出错:`, err);

              // 更新进度
              uni.showLoading({
                title: `下载中 ${successCount + failCount}/${totalImages}`,
                mask: true
              });

              // 检查是否全部完成
              that.checkDownloadComplete(successCount, failCount, totalImages);
            }
          });
        });
      },

      // 检查下载完成状态
      checkDownloadComplete(successCount, failCount, totalImages) {
        if (successCount + failCount >= totalImages) {
          // 所有图片处理完成
          uni.hideLoading();

          if (successCount === totalImages) {
            // 全部成功
            uni.showToast({
              title: `${totalImages}张图片保存成功`,
              icon: 'success',
              duration: 2000
            });
          } else if (successCount > 0) {
            // 部分成功
            uni.showToast({
              title: `${successCount}张成功，${failCount}张失败`,
              icon: 'none',
              duration: 3000
            });
          } else {
            // 全部失败
            uni.showToast({
              title: '图片保存失败，请重试',
              icon: 'none',
              duration: 3000
            });
          }

          console.log(`下载完成: 成功${successCount}张，失败${failCount}张`);
        }
      },
      getLevel(score) {
        let level = '';
        let val = score * 1;
        if (val === 0) {
          level = '差';
        } else {
          if (val >= 90) {
            level = '优秀';
          } else if (val >= 75 && val < 90) {
            level = '良好';
          } else if (val >= 60 && val < 75) {
            level = '一般';
          } else {
            level = '差';
          }
        }
        return level;
      },
      // beforeleave() {
      //   let that = this;
      //   that.isShow = false; //这个很重要，一定要先把弹框删除掉
      //   that.goBack();
      // },
      goBack() {
        // uni.navigateBack();
        if (this.isHistory) {
          console.log(123);
          uni.redirectTo({
            url: `/antiAmnesia/review/history?studentCode=${this.studentCode}`
          });
          console.log(345);
        } else {
          // uni.reLaunch({
          //   url: `/antiAmnesia/review/allWords?studentCode=${this.studentCode}`
          // });
          uni.switchTab({
            url: '/pages/home/<USER>/index'
          });
        }
      },
      sayWord(item) {
        let that = this;
        uni.showLoading({ title: '加载中' });
        if (item.linkUrl) {
          innerAudioContext.obeyMuteSwitch = false;
          innerAudioContext.src = item.linkUrl;
          innerAudioContext.play();
          uni.hideLoading();
        } else {
          that.$httpUser
            .get('znyy/app/query/word/voice', {
              word: item.word
            })
            .then((result) => {
              if (result.data.success) {
                var w = encodeURIComponent(result.data.data);
                var linkUrl;
                if (w.endsWith('.mp3')) {
                  linkUrl = 'https://document.dxznjy.com/' + w;
                } else {
                  linkUrl = 'https://document.dxznjy.com/' + w + '.mp3';
                }
                that.$set(item, 'linkUrl', linkUrl);
                uni.hideLoading();
                innerAudioContext.obeyMuteSwitch = false;
                innerAudioContext.src = linkUrl;
                innerAudioContext.play();
              } else {
                uni.hideLoading();
                that.$util.alter(result.data.message);
              }
            })
            .catch((err) => {
              uni.hideLoading();
              console.log(err);
            });
        }
        innerAudioContext.onEnded(() => {
          that.$forceUpdate();
        });
      },
      goUrl(url) {
        uni.navigateTo({
          url: url
        });
      },
      handleShare() {
        // getApp().sensors.track('aiReviewReportClick', {
        //   name: '分享报告'
        // });
        let shareInfo = {
          title: 'AI抗遗忘复习报告',
          imageUrl: '', //分享封面
          path: `/parentEnd/report/aiReviewReport?reviewId=${this.reviewId}&history=1&noDownload=1&studentCode=${this.studentCode}`
        };
        uni.$appShare(shareInfo, 2);
        plus.runtime.quit();
      },

      async getReviewReport(id) {
        let that = this;
        uni.showLoading({
          title: '加载中',
          duration: 3000
        });
        let result = await this.$httpUser.get('znyy/review/query/student/word/review/detail?reviewId=' + id);
        if (result && result.data.success) {
          that.showData = result.data.data;
          that.tableData = that.showData.aiWordPronunciationDetailDtoList;
          that.wordData = that.showData.words ? JSON.parse(that.showData.words) : [];
          that.richText = that.showData.aiWordReviewReportDto.studySuggest;
          that.richText = that.richText.replace(/学习建议/, '').replace(' ', '');
          that.forgetData = that.showData.aiForgetWordDetailDtoList;
          console.log(that.richText, '11111111111');
          that.richTexts = that.richText.split(/\d+\./).filter(Boolean);
          console.log(that.richTexts, '222222222');
          // that.richTexts = that.richText.split('",');
          await that.initChart();
        }
      },
      formatSecondsFriendly(seconds) {
        const minutes = Math.floor(seconds / 60);
        const remainingSeconds = seconds % 60;
        let result = '';
        if (minutes > 0) result += `${minutes}分`;
        if (remainingSeconds > 0) result += `${remainingSeconds}秒`;
        return result || '0秒'; // 处理 0 秒的情况
      },
      initChart() {
        let that = this;
        uni.showLoading({
          title: '加载中',
          duration: 3000
        });
        console.log(that.showData, '111111111111111');
        let reviewDuration = that.formatSecondsFriendly(that.showData.aiWordReviewReportDto.reviewDuration);
        let radarData = [
          {
            name: '我的',
            data: [that.showData.aiWordReviewReportDto.userFluency, that.showData.aiWordReviewReportDto.userAccuracy, that.showData.aiWordReviewReportDto.userCompleteness] // 每个维度得分
          },
          {
            name: '平均水平',
            data: [that.showData.aiWordReviewReportDto.avgFluency, that.showData.aiWordReviewReportDto.avgAccuracy, that.showData.aiWordReviewReportDto.avgCompleteness]
          },
          {
            name: '优秀水平',
            data: [
              that.showData.aiWordReviewReportDto.excellentFluency,
              that.showData.aiWordReviewReportDto.excellentAccuracy,
              that.showData.aiWordReviewReportDto.excellentCompleteness
            ]
          }
        ];
        let arr = that.wordData.filter((i) => i.correct);
        let ringData = that.isHistory
          ? [
              { name: `已复习${that.wordData.length}个`, value: +that.wordData.length },
              { name: `遗忘数${that.wordData.length - arr.length}个`, value: that.wordData.length - arr.length },
              { name: `正确数${arr.length}个`, value: arr.length * 1 }
            ]
          : [
              { name: `需复习${that.oldShowData.wordTotalCount}个`, value: +that.oldShowData.wordTotalCount },
              { name: `已复习${that.oldShowData.reviewTotal}个`, value: +that.oldShowData.reviewTotal },
              { name: `遗忘数${that.oldShowData.forgetNum}个`, value: +that.oldShowData.forgetNum },
              { name: `未复习${that.oldShowData.noReviewNum * 1}个`, value: that.oldShowData.noReviewNum * 1 }
            ];

        console.log(ringData);
        setTimeout(() => {
          // 图表配置
          that.radarOptions = {
            color: ['#6385f7', '#2ece98', '#efc374'],
            padding: [10, 10, 0, 10],
            fontSize: 12,
            dataPointShape: false,
            legend: {
              show: true,
              direction: 'horizontal',
              position: 'left',
              float: 'top',
              lineHeight: 25
            },
            extra: {
              radar: {
                gridType: 'radar', // 网格类型
                gridColor: '#fff', // 网格线颜色
                gridCount: 3, // 网格层数
                labelOffset: 10, // 标签偏移量
                labelColor: '#666', // 标签颜色
                radius: 80,
                max: 100, // 最大值
                min: 0, // 最小值
                opacity: 0.1,
                border: true
              }
            }
          };
          that.ringOptions = {
            rotate: false,
            color: ['#efc374', '#2ece98', '#6385f7', '#de8470'],
            rotateLock: false,
            padding: [5, 5, 5, 5],
            dataLabel: false,
            enableScroll: false,
            legend: {
              show: true,
              position: 'right',
              float: 'top',
              lineHeight: 25
            },
            title: {
              // name: that.showData.rate ? that.showData.rate + '%' : '',
              name: that.showData.rate && that.showData.rate != 0 ? that.showData.rate + '%' : that.showData.rate == 0 ? '0%' : '',
              fontSize: 20,
              color: '#21504B'
            },
            subtitle: {
              name: '正确率',
              fontSize: 16,
              color: '#78AD9E'
            },

            extra: {
              ring: {
                ringWidth: 20,
                activeOpacity: 0.5,
                activeRadius: 10,
                offsetAngle: 0,
                labelWidth: 15,
                border: false,
                borderWidth: 3,
                borderColor: '#FFFFFF'
              }
            }
          };
          that.arcbarOptions = {
            color: ['#97e3c9', '#8aceba'],
            padding: [0, 0, 0, 0],
            // title: {
            //   // name: that.showData.rate ? that.showData.rate + '%' : '',
            //   name: that.showData.rate && that.showData.rate != 0 ? that.showData.rate + '%' : that.showData.rate == 0 ? '0%' : '',
            //   fontSize: 20,
            //   color: '#21504B'
            // },
            // subtitle: {
            //   name: '正确率',
            //   fontSize: 16,
            //   color: '#78AD9E'
            // },

            title: {
              name: reviewDuration,
              fontSize: 15,
              color: '#666666'
            },
            subtitle: {
              name: '学习时长',
              fontSize: 8,
              color: '#7cb5ec'
            },
            extra: {
              arcbar: {
                type: 'circle',
                width: 10,
                lineCap: 'butt',
                backgroundColor: '#E9E9E9',
                startAngle: 0.5,
                endAngle: 0.25,
                gap: 2,
                linearType: 'none'
              }
            }
          };

          // 模拟数据（需替换为实际数据）
          that.radarChartData = {
            categories: ['发音准确度', '发音流利度', '发音完整度'],
            series: radarData
          };
          that.ringChartData = {
            series: [
              {
                data: ringData,
                label: {
                  show: true, // 显示标签
                  // 环形图样式设置
                  radius: ['50%', '90%'] // 内半径50%，外半径70%（环形图效果）
                }
              }
            ]
          };
          that.arcbarChartData = {
            series: [
              {
                name: '正确率',
                color: '#2fc25b',
                data: 1
                // data: that.showData.rate == 0 ? 0 : that.showData.rate / 100
              }
            ]
          };
          uni.hideLoading();
        }, 500);
      }
    }
  };
</script>

<style lang="less">
  page {
    height: 100%;
    background: linear-gradient(180deg, #edfdf4 0%, #96e8ca 100%);
  }
  .report-container {
    padding: 20rpx;
    background: url('https://document.dxznjy.com/course/68b9cf8e34284eae8e0ed741b2774e8c.png') no-repeat;
    background-size: contain;
    // background: linear-gradient(180deg, #edfdf4 0%, #96e8ca 100%);
    height: auto;
    min-height: 1000rpx;
    .toastTop {
      font-size: 16rpx;
      color: #c0c0c0;
      text-align: center;
    }
  }
  .base-info {
    background: url('https://document.dxznjy.com/dxSelect/1742809882000') no-repeat;
    background-size: cover;
    height: 290rpx;
    // width: 686rpx;
    .left {
      margin-top: 20rpx;
    }
    .right {
      width: 200rpx;
      height: 200rpx;
    }
    .label {
      font-size: 28rpx;
      color: #4c9b8d;
    }
  }
  .now-study {
    // width: 686rpx;
    min-height: 350rpx;
    background: #fafffd;
    border-radius: 0rpx 40rpx 0rpx 40rpx;
    border: 2rpx solid #daf6e6;
    margin-top: 40rpx;
    .charts-box {
      width: 100%;
      height: 400rpx;
    }
  }
  .table-data {
    // width: 686rpx;
    min-height: 452rpx;
    background: #fafffd;
    border-radius: 0rpx 40rpx 0rpx 40rpx;
    border: 2rpx solid #daf6e6;
    margin-top: 40rpx;
    .table-row.header {
      // font-weight: bold;
      font-size: 24rpx;
      color: #9fa2ac;
      // width: 686rpx;
      // margin-top: -20rpx
      // background-color: #f8f8f8;
    }
  }
  .forget-detail {
    // padding: 20rpx;
    margin-top: 40rpx;
    background: #fafffd;
    border-radius: 0rpx 40rpx 0rpx 40rpx;
    .top {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 20rpx;
    }
    .left-btn {
      width: 236rpx;
      height: 62rpx;
      background: #04be87;
      border-radius: 34rpx;
      line-height: 62rpx;
      text-align: center;
      font-size: 26rpx;
      font-weight: bold;
      color: #fff;
    }
    .right-btn {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 150rpx;
      height: 56rpx;
      border-radius: 28rpx;
      background: #f8f8f8;
      line-height: 56rpx;
      text-align: center;
      color: #04be87;
      font-size: 28rpx;
      font-weight: 600;
      image {
        margin-right: 10rpx;
      }
    }
    .btn {
      width: 236rpx;
      height: 62rpx;
      border-radius: 50rpx;
      line-height: 62rpx;
      text-align: center;
      box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.1), 0px 1px 3px rgba(0, 0, 0, 0.08);
      transition: all 0.2s ease;
      &:active {
        box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.2), 0px 1px 3px rgba(0, 0, 0, 0.15);
        transform: translateY(2px);
      }
    }
    .confirm {
      background: #04be87;
      color: #fff;
      font-size: 28rpx;
    }
    .col-number,
    .col-word {
      font-size: 24rpx;
      color: #21504b;
    }
    .table-row-header {
      font-size: 24rpx;
      color: #9fa2ac;
      display: flex;
      height: 60rpx;
      background-color: #fff;
      line-height: 60rpx;
    }
  }
  .forget-word {
    // width: 686rpx;
    min-height: 452rpx;
    background: #fafffd;
    border-radius: 0rpx 40rpx 0rpx 40rpx;
    border: 2rpx solid #daf6e6;
    margin-top: 40rpx;
    font-size: 28rpx;
    .wordList {
      overflow-y: auto;
      // height: 625rpx;
      // min-height: 200rpx;
      max-height: 625rpx;
      padding: 0 20rpx 20rpx 20rpx;
      .title {
        font-size: 28rpx;
        color: #21504b;
        line-height: 44rpx;
        text-align: left;
      }
      .wordItem {
        // width: 630rpx;
        font-size: 28rpx;
        height: 80rpx;
        background: #f9fbfe;
        border-radius: 8rpx;
        display: flex;
        justify-content: space-between;
        align-items: center;
        &:nth-child(odd) {
          background-color: #f9fbfe;
        }
        &:nth-child(even) {
          background-color: #fff;
        }
        .wordItemtext {
          flex: 1;
        }
      }
    }
  }
  .colorgreen {
    color: #009e70;
  }
  .color000 {
    color: #000;
  }
  .colorred {
    color: #de8470;
  }
  .allRound-ablity {
    background: url('https://document.dxznjy.com/dxSelect/1742809917000') no-repeat;
    background-size: cover;
    height: 504rpx;
    width: 686rpx;
    margin-top: 40rpx;
    .charts-box {
      width: 100%;
      height: 100%;
      padding-top: 40rpx;
    }
  }
  .radarBox {
    width: 100%;
    background: #fff;
  }

  .table-container {
    margin-top: 4rpx;
    // padding: 20rpx;
    padding: 20rpx;
    background: #fff;
    border-radius: 12rpx;
    overflow-y: auto;
    // height: 450rpx;
    max-height: 450rpx;
    min-height: 150rpx;
  }

  .table-title {
    font-size: 32rpx;
    font-weight: bold;
    margin-bottom: 20rpx;
    text-align: left;
  }

  .table-row {
    display: flex;
    // padding: 20rpx 0;
    color: #21504b;
    height: 60rpx;
    line-height: 60rpx;
    &:nth-child(even) {
      background-color: #fff;
    }
    &:nth-child(odd) {
      background-color: #f9fbfe;
    }
    // border: 1rpx solid #eee;
    // border-bottom: none;
  }

  .col-word,
  .col-number,
  .col-word1,
  .col-number1 {
    flex: 1; /* 单词列宽度占比更大 */
    text-align: center;
    // padding-left: 20rpx;
    // border-right: 1rpx solid #eee;
    font-size: 24rpx;
    word-wrap: inherit;
    white-space: nowrap; /* 不换行 */
    overflow: hidden; /* 隐藏超出部分 */
    text-overflow: ellipsis; /* 超出部分显示省略号 */
  }
  .col-number:last-child {
    border-right: none;
  }
  /* 最后一行去除底部边框 */
  // .table-row:last-child {
  //   border-bottom: 1rpx solid #eee;
  // }
  .footer {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 40rpx;
    image {
      width: 686rpx;
      height: 96rpx;
    }
    .footer-btn {
      width: 100%;
      display: flex;
      justify-content: space-around;
      align-items: center;
    }
    .btn {
      width: 264rpx;
      height: 82rpx;
      border-radius: 50rpx;
      line-height: 82rpx;
      text-align: center;
      box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.1), 0px 1px 3px rgba(0, 0, 0, 0.08);
      transition: all 0.2s ease;
      &:active {
        box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.2), 0px 1px 3px rgba(0, 0, 0, 0.15);
        transform: translateY(2px);
      }
    }
    .cancel {
      background: #04be87;
      color: #fff;
    }
    .confirm {
      background: #428a6f;
      color: #fff;
    }
  }
  .suggest {
    // width: 662rpx;
    // height: 316rpx;
    // max-height: 300rpx;
    min-height: 150rpx;
    background: #fafffd;
    border-radius: 0rpx 40rpx 0rpx 40rpx;
    font-size: 28rpx;
    color: #21504b;
    line-height: 44rpx;
    margin-top: 40rpx;
    border-radius: 20rpx;
    .suggest-box {
      height: 260rpx;
      overflow-y: auto;
      .suggest-item {
        // text-indent: 2rem;
        display: flex;
        align-items: flex-start;
        justify-content: space-between;
        margin-bottom: 10rpx;
      }
      .point {
        width: 16rpx;
        height: 16rpx;
        background: #04be87;
        margin: 10rpx 10rpx 0 0;
      }
    }
    .left-btn {
      width: 236rpx;
      height: 62rpx;
      background: #04be87;
      border-radius: 34rpx;
      line-height: 62rpx;
      text-align: center;
      font-size: 32rpx;
      font-weight: bold;
      color: #fff;
    }
  }
  .nodata {
    display: flex;
    flex-direction: column;
    justify-content: space-around;
    align-items: center;
    color: #009e70;
    font-size: 28rpx;
    font-weight: 700;
    padding: 20rpx 0;
  }
  .modal-title {
    font-size: 28rpx;
    color: #555555;
    margin-bottom: 10rpx;
  }
  .confirmButton1 {
    display: flex;
    justify-content: space-around;
    align-items: center;
    .btn {
      width: 510rpx;
      height: 82rpx;
      background: #428a6f;
      border-radius: 12rpx;
      line-height: 82rpx;
      text-align: center;
    }
    .confirm {
      background: #428a6f;
      color: #fff;
    }
  }
</style>
