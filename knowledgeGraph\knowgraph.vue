<template>
  <view>
    <web-view ref="webView" :src="webViewSrc"></web-view>
  </view>
</template>

<script>
  const { isTest179, isTest189 } = require('@/util/config');
  export default {
    data() {
      return {
        // url: 'http://***************:9000/',
        studentCode: '',
        nodeId: '',
        nodeLevel: '',
        nodeLevel: '',
        token: ''
      };
    },
    computed: {
      webViewSrc() {
        let baseDomain = 'classh5'; // 默认使用 classh5

        if (isTest179) {
          baseDomain = 'dl';
        } else if (isTest189) {
          baseDomain = 'onetomany';
        }
        return `https://${baseDomain}.ngrok.dxznjy.com/unibest/#/pages/knowGraph/about?studentCode=${this.studentCode}&nodeId=${this.nodeId}&nodeLevel=${this.nodeLevel}&token=${this.token}&nodeName=${this.nodeName}`;
      }
    },
    onLoad(option) {
      // #ifdef APP-PLUS
      if (option.token) {
        this.token = option.token;
        this.$handleTokenFormNative(option); // 从app获取token
      }
      // #endif
      this.studentCode = option.studentCode;
      this.nodeId = option.nodeId;
      this.nodeLevel = option.nodeLevel;
      this.nodeName = option.nodeName;
    },

    methods: {}
  };
</script>
