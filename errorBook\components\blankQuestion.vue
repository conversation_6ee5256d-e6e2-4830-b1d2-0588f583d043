<template>
  <view class="questionBox pr-40 pl-40">
    <!-- 题目 -->
    <view class="mt-10 ptb-60">
      <view class="mb-65 t-w">{{ currentIndex }}.{{ questionText }}</view>
      <!-- 题目答案 -->
      <u--input
        v-if="type == 0"
        custom-style="border-bottom-color: black !important"
        :color="isCorrect"
        :disabled="currentAnswerData.id"
        border="bottom"
        clearable
        v-model="inputContent"
      ></u--input>
      <u--input
        v-if="type == 1"
        custom-style="border-bottom-color: black !important"
        :color="isCorrect"
        :disabled="inputContent == '' ? false : true"
        border="bottom"
        clearable
        v-model="inputContent"
      ></u--input>
    </view>
    <!-- 解析与正确答案 -->
    <view class="mt-80 f-28" v-if="type == 0">
      <view class="flex-e mb-20">
        <text class="iconBox" v-if="!currentAnswerData.id" @click="viewAnswer">查看答案</text>
      </view>
      <view v-if="currentAnswerData.id">
        <view class="mb-10">
          正确答案：
          <text class="t-w">{{ correctAnswer }}</text>
        </view>
        <text>解析： {{ analysis }}</text>
      </view>
    </view>

    <!-- 已做 -->
    <view class="mt-80 f-28" v-if="type == 1">
      <view class="flex-e mb-20">
        <!-- <text class="iconBox" v-if="!this.inputContent" @click="viewAnswer">查看答案11111</text> -->
      </view>
      <view>
        <view class="mb-10">
          正确答案：
          <text class="t-w">{{ correctAnswer }}</text>
        </view>
        <text>解析： {{ analysis }}</text>
      </view>
    </view>
  </view>
</template>

<script>
  export default {
    props: {
      questionList: Array,
      currentQuestionIndex: Number,
      type: String
    },
    data() {
      return {
        inputContent: '',
        optionList: [],
        currentIndex: 1,
        questionText: '', // 题目
        // superReadOptionDtoList: [], // 填空答案数组
        analysis: '', // 解析
        correctAnswer: '', // 正确答案
        isCorrect: '', // 输入答案颜色区别
        currentAnswerData: { id: null }
      };
    },
    mounted() {
      this.initQuestionData();
    },
    watch: {
      currentQuestionIndex(newVal) {
        this.initQuestionData(newVal);
      },
      type(newVal) {
        if (newVal == 0) {
          this.inputContent = '';
          this.isCorrect = '';
          this.currentAnswerData = { id: null };
        }
      }
    },
    methods: {
      initQuestionData(index = this.currentQuestionIndex, type = this.type) {
        // 获取存储的答案数据列表
        const storedAnswerDataList = uni.getStorageSync('answerDataList');

        // 检查是否存在存储数据且不为空数组
        if (storedAnswerDataList && storedAnswerDataList.length > 0) {
          // 遍历列表查找当前题目索引对应的答案数据
          this.currentAnswerData = storedAnswerDataList.find((item) => item.index === index);
          console.log('currentAnswerData', this.currentAnswerData);
          if (this.currentAnswerData?.studentAnswer) {
            this.inputContent = this.currentAnswerData.studentAnswer;
          } else {
            // 如果没有找到对应索引的数据，则创建一个空的 currentAnswerData
            this.currentAnswerData = { id: null };
          }
        }
        console.log('111111111111111', this.currentAnswerData);
        const questionInfo = this.questionList[index]?.questionInfo || {};
        this.optionList = questionInfo;
        this.correctAnswer = this.optionList.correctAnswer;
        // 根据题目类型进行初始化
        if (type == 1) {
          this.inputContent = this.questionList[index].studentAnswer;
          this.isCorrect = this.questionList[index].studentAnswer == this.correctAnswer ? '#31CF93' : '#FFAF85';
        } else if (type == 0 && !this.currentAnswerData?.studentAnswer) {
          this.inputContent = '';
          this.isCorrect = '';
        } else if (type == 0 && this.currentAnswerData?.studentAnswer) {
          this.inputContent = this.currentAnswerData.studentAnswer;
          this.isCorrect = this.inputContent == this.correctAnswer ? '#31CF93' : '#FFAF85';
        } else {
          this.inputContent = ''; // 未作答
          this.isCorrect = '';
        }
        this.selectedOption = null;
        this.currentIndex = index + 1;
        this.questionText = this.optionList.questionText.replace(/##/g, '________');
        this.analysis = this.optionList.analysis;
      },

      viewAnswer() {
        if (this.inputContent != '' && !this.currentAnswerData.id) {
          const newAnswerData = { id: this.optionList.id, studentAnswer: this.inputContent, index: this.currentQuestionIndex };
          this.currentAnswerData = newAnswerData;
          const storedAnswerDataList = uni.getStorageSync('answerDataList');
          storedAnswerDataList.push(newAnswerData);
          uni.setStorageSync('answerDataList', storedAnswerDataList);

          this.isCorrect = this.inputContent === this.correctAnswer ? '#31CF93' : '#FFAF85';
          this.$emit('answerSubmitted', { id: this.optionList.id, studentAnswer: this.inputContent, index: this.currentQuestionIndex });
          // 获取当前存储的 answerViewMap 对象
          let answerViewMap = uni.getStorageSync('answerViewMap') || {};
          // 更新当前索引对应的 answerViewMap 值
          answerViewMap[this.currentQuestionIndex] = true;
          answerViewMap[this.currentQuestionIndex + 1] = true;
          // 更新本地存储的 answerViewMap 对象
          uni.setStorageSync('answerViewMap', answerViewMap);
        } else {
          uni.showToast({
            title: '请先填写答案~',
            icon: 'none'
          });
        }
      }
    }
  };
</script>

<style lang="scss" scoped>
  .iconBox {
    border: 2rpx solid #339378;
    border-radius: 8rpx;
    font-size: 24rpx;
    color: #339378;
    padding: 6rpx;
  }

  .questionBox {
    background-color: rgba(255, 255, 255, 1);
    position: relative;
    display: flex;
    flex-direction: column;
  }
</style>
