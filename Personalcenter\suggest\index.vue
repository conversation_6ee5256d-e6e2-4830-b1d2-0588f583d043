<template>
  <view>
    <form @submit="submitS">
      <view class="p-30 bg-ff mlr-30 radius-15" :style="{ height: useHeight + 'rpx' }">
        <view class="f-32 ptb-30" style="font-weight: bold">问题和意见</view>
        <view class="radius-15 plr-30 pt-30 opinion">
          <textarea
            style="height: 600rpx"
            maxlength="200"
            placeholder="请填写您的问题描述，以便我们提供更好的帮助（不低于10个字，不超过200字）"
            name="suggestionContent"
            class="remark f-28"
          ></textarea>
        </view>
        <view class="f-32 ptb-30" style="font-weight: bold">您的姓名（选填）</view>
        <view class="radius-15 plr-30 pt-30 opinion-name">
          <textarea placeholder="请输入您的姓名" name="suggestionName" class="remark-name f-28"></textarea>
        </view>
        <button class="submit f-30 t-c c-ff mt-40" form-type="submit">提交反馈</button>
      </view>
    </form>

    <uni-popup ref="suggestSucc" type="center" @maskClick="cancel()">
      <view class="suggest">
        <view>
          <image style="width: 100rpx; height: 100rpx" src="https://document.dxznjy.com/dxSelect/icon_green_yes.png" mode="widthFix"></image>
          <view class="suggestTxt">反馈成功</view>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script>
  const { $http, $showMsg } = require('@/util/methods.js');
  export default {
    data() {
      return {
        useHeight: 0, //除头部之外高度
        userCode: '',
        phone: '',
        app: 0
      };
    },
    onLoad(e) {
      if (e.token) {
        this.app = e.app;
        this.$handleTokenFormNative(e);
      }
    },
    onReady() {
      uni.getSystemInfo({
        success: (res) => {
          // 可使用窗口高度，将px转换rpx
          console.log(res);
          let h = res.windowHeight * (750 / res.windowWidth);
          this.useHeight = h - 90;
        }
      });
    },
    onShow() {
      this.userCode = uni.getStorageSync('user_code');
      this.phone = uni.getStorageSync('phone');
    },
    onUnload() {
      // #ifdef APP-PLUS
      if (this.app) {
        plus.runtime.quit();
      }
      // #endif
    },
    methods: {
      submitS(e) {
        let _this = this;
        let values = e.detail.value;
        if (values.suggestionContent.length < 10) {
          $showMsg('请填写不低于10个字的问题描述');
          return false;
        }
        uni.showModal({
          title: '提示',
          content: '确认提交吗？',
          success: async function (res) {
            if (res.confirm) {
              const resdata = await $http({
                url: 'znyy/suggest/create',
                method: 'post',
                data: {
                  suggest: values.suggestionContent,
                  phone: _this.phone,
                  name: values.suggestionName,
                  userCode: _this.userCode
                }
              });
              if (resdata.success) {
                _this.$refs.suggestSucc.open();
              } else {
                uni.showModal({
                  title: '提示',
                  content: resdata.message,
                  showCancel: false,
                  success: function (res) {
                    if (res.confirm) {
                      uni.navigateBack();
                    } else if (res.cancel) {
                      console.log('用户点击取消');
                    }
                  }
                });
              }
            } else if (res.cancel) {
              console.log('用户点击取消');
            }
          }
        });
      },

      cancel() {
        this.$refs.suggestSucc.close();
        uni.navigateBack();
      }
    }
  };
</script>

<style lang="scss" scoped>
  .submit {
    background-image: linear-gradient(to bottom, #88cfba, #1d755c);
    height: 88rpx;
    border-radius: 44upx;
    line-height: 88upx;
  }

  .opinion {
    background-color: rgba(153, 153, 153, 0.08);
    height: 800rpx;
  }

  .remark {
    width: 100%;
    color: #999999;
  }

  .opinion-name {
    background-color: rgba(153, 153, 153, 0.08);
    height: 64rpx;
  }

  .remark-name {
    width: 100%;
    color: #999999;
  }

  .suggest {
    width: 670rpx;
    height: 380rpx;
    background-color: #fff;
    border-radius: 24rpx;
    display: flex;
    justify-content: center; /* 文本垂直居中对齐 */
    align-items: center; /* 文本水平居中对齐 */
  }

  .suggestTxt {
    margin-top: 30rpx;
    color: #999999;
    font-size: 28rpx;
  }
</style>
