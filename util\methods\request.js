import {
	$confirmLogin
} from './common.js';
import {
	$showMsg
} from './prompt.js';
import Config from '../config.js';
// #ifdef MP-WEIXIN
import sensors from 'sa-sdk-miniprogram';
// #endif
// #ifdef APP-PLUS
// const sensors = uni.requireNativePlugin('Sensorsdata-UniPlugin-App');
// #endif

/**
 * 请求封装
 * @param {Object} opt  request对象,url,data,method
 * @param {Object} arg  是否显示loading以及校验登录
 * @returns {Object}
 */
const $http = (opt, arg) => {
	let that = this;
	let {
		showLoading = false, checkLogin = false
	} = arg || {};
	// 判断是否登录
	if (checkLogin) {
		let confirmLogin = $confirmLogin();
		if (!confirmLogin) {
			return false;
		}
	}
	var jump = uni.getStorageSync('jump');
	if (jump) return;
	// 判断是否显示loading
	if (opt.showLoading) {
		uni.showLoading({
			title: '加载中...',
			mask: true
		});
	}
	// 获取个人token
	let token = uni.getStorageSync('token');
	// 门店充值需要的token
	let payToken = uni.getStorageSync('payToken');
	// if(payToken){
	// 	token = payToken
	// }
	// 从opt中结构参数
	let {
		url,
		method = 'GET',
		data = {},
		header
	} = opt;
	// 配置请求地址
	// let host1 = uni.getStorageSync('url') ? uni.getStorageSync('url') : host;
	// console.log(host1);
	let o = uni.getStorageSync('baseUrl')||Config.DXHost

	opt.url = o + url;
	console.log(opt.url);
	opt.data = Object.assign(data, {
		//invitor_id:  uni.getStorageSync('invitor_id') || 0,
	});
	if (url.indexOf('znyy/school/recharge/getRechargeLineOrderCreateDto') != -1) {
		token = uni.getStorageSync('token');
	}

	header = {
		Token: token,
		'Content-Type': 'application/json'
	};
	// #ifdef MP-WEIXIN
	header['anonymous_id'] = sensors.getAnonymousID();
	header['dx-source'] = 'ZHEN_XUAN##WX##MINIAPP';
	// #endif
	// #ifdef APP-PLUS
	header['dx-source'] = 'ZHEN_XUAN##PHONE##APP';
	// #endif
	if (payToken || token) {
		if (url.indexOf('zx/common/getDynamicGatewayFlag') != -1) {

		} else {
			header['x-www-iap-assertion'] = payToken ? payToken : token
		}

	}
	// opt.data.store_id = store_id
	// 根据token判断请求头
	// if (token !== '') {
	// 	opt.header.Token = token
	// } else {
	// 	opt.header.Token = ''
	// }
	// opt.header["Token"]=uni.getStorageInfoSync("token")||''
	return new Promise((resolve, reject) => {
		uni.request({
			url: opt.url,
			method: opt.method,
			header: header,
			data: opt.data,
			sslVerify: false,
			success(res) {
				console.log(opt.url, res);
				if (res.data.status == 1 || res.data.success || res.data.code == 80002) {
					// 成功且code=1时抛出
					resolve(res.data);
				} else if (res.data.code == 50004 || res.data.code == 40018) {
					// 50004 请重新登录   40018 登陆已过期，请重新登陆
					var jump = uni.getStorageSync('jump'); //以下解决多次跳转登录页的重点
					if (!jump) {
						setTimeout(() => {
							uni.navigateTo({
								url: '/Personalcenter/login/login'
							});
						}, 100);
						uni.removeStorage({
							key: 'token'
						});
						uni.removeStorage({
							key: 'club'
						});
						uni.removeStorage({
							key: 'brand'
						});
						uni.removeStorage({
							key: 'Partner'
						});
						uni.setStorageSync('jump', 'true');
					}
				} else if (res.data.status == 10000) {
					if (opt.url.indexOf('/zx/user/userInfoNew') == -1) {
						uni.navigateTo({
							url: '/Personalcenter/login/login'
						});
						uni.removeStorage({
							key: 'token'
						});
						uni.removeStorage({
							key: 'club'
						});
						uni.removeStorage({
							key: 'brand'
						});
						uni.removeStorage({
							key: 'Partner'
						});
					}
					resolve(false);
					console.log('获取用户信息userInfoNew', res.data);
				} else {
					console.log(res.data.message, '111111111111');
					// $showMsg(res.data.message)
					let message = res.data.message;
					if (message.includes('@eid')) {
						message = message.split('@eid')[0];
					}
					if (opt.showError !== 1) {
						uni.showModal({
							title: '温馨提示',
							content: message || '服务器错误',
							showCancel: false
						});
					}
					if (url.indexOf('zx/course/orderPayAnew') != -1) {
						resolve(res.data);
					} else {
						resolve(false);
					}
					// resolve(res.data);
				}
				uni.hideLoading();
			},
			fail(err) {
				console.log('--err--', err);
				$showMsg('网络连接失败');
				uni.hideLoading();
			},
			complete() {
				uni.hideLoading();
			}
		});
	});
};

module.exports = {
	$http
};