<template>
	<view class="wordcheck_container">
		<view class="countdown">
			<smh-countDown ref="countDown" :second="15" :radius="150" :fontSize="40" color="#fcb575" bgcolor="#fef3e9"
				:lineWidth="5" @end="end"></smh-countDown>
			<view v-if="!isStartCheck" class="tip">测试时间约5~8分钟</view>

		</view>
		<view class="word_check">
			<view class="word_header">{{questionData.word}}</view>
			<view class="word_choose_list">
				<view :style="isClickIndex == index?'background-color:#3ac9b0':'background-color:#f0f8f0'"
					class="choose_item" v-for="(item,index) in questionData.answerList" :key="index">
					<text>{{item}}</text>
					<view class="choose_btn" @click="excuteQuestion(item,index)"></view>
				</view>
			</view>
		</view>

		<view class="word_footer">
			<view v-if="!isStartCheck" class="left" @click="goUrl('/Personalcenter/my/wordCheck/wordCheckReport')">上次成绩</view>
			<view v-if="!isStartCheck" class="right" @click="startCheck">开始检测</view>
			<view v-if="isStartCheck" class="right" @click="excuteQuestion('',-1)">不认识</view>
		</view>

		<!-- 弹窗 -->
			<uni-popup ref="endDialogRef" type="center" :maskClick="false" :classBG="'white'">
				<end-dialog @confirm="submitCheck" :tipContent="tipContent" :showCancel="false">
				</end-dialog>
			</uni-popup>
			
			
	</view>
</template>

<script>
	import endDialog from "../../components/endDialog/end-dialog.vue"
	export default {
		components: {
			endDialog
		},
		data() {
			return {
				isStartCheck: false,
				questionList: [],
				questionData: {
					word: "schoolbag",
					chinese: "学校,同学,抓伤,行李,书包",
					rightAnswer: "书包",
					answerList: ['学校', '同学', '抓伤', '行李', '书包'],
				},
				submitTest: {},
				nowIndex: 0,
				rightCount: 0,
				wrongCount: 0,
				time: 0,
				isClickIndex: -1,
				tipContent: '',
				
			};

		},
		onLoad() {
			let that = this;
			that.questionList.push(that.questionData);
		},
		methods: {
			// 开始检测
			startCheck() {
				this.isStartCheck = true;
				this.getQuestion();
			},

			// 获取检测题目
			async getQuestion() {
				let result = await this.$httpUser.get('znyy/course/query/word/level/test/db');
				if (result && result.data.success) {
					if (result.data.data.length > 0) {
						this.questionList = result.data.data;
						this.nextQuestion();
					} else {
						this.tipContent = "暂无单词可以检测，请复习后再来测试";
						this.openTipDialog();
					}
				}
			},


			// 答题处理 false不认识
			excuteQuestion(word, index) {
				let that = this;
				if (this.isStartCheck) {
					this.isClickIndex = index;
					console.log(word, this.questionData.rightAnswer);
					if (word == this.questionData.rightAnswer) {
						this.rightCount++;
						this.wrongCount = 0;
					} else {
						this.wrongCount++;
					}
					setTimeout(function() {
						that.nextQuestion();
					}, 500);
				}
			},


			// 下一题
			nextQuestion() {
				console.log(this.$refs.countDown.second1);
				this.time += 15 - this.$refs.countDown.second1;
				this.$refs.countDown.refresh();
				// 做完了 | 连续错三个
				if (this.nowIndex > this.questionList.length || this.wrongCount >= 3) {
					this.$refs.countDown.pause();
					this.tipContent = "本轮测试已结束，请复习后再来测试";
					this.openTipDialog();
					return;
				}

				this.questionData = this.questionList[this.nowIndex];
				this.questionData.answerList = this.questionData.chinese.split(",");
				this.isClickIndex = -1;
				this.nowIndex++;
			},

			// 提交检测
			async submitCheck() {
				let that = this;
				that.submitTest.rightCount = that.rightCount; // 正确的个数
				that.submitTest.totalCount = that.nowIndex; // 测试总个数
				that.submitTest.timeSpan = that.time; // 做题时间
				that.submitTest.merchantCode = ""; // 校区编号
				that.$httpUser.post(`znyy/course/submit/test/word/level`, that.submitTest).then((res) => {
					if (!res.data.success) {
						that.$util.alter(res.data.message);
					} else {
						that.$util.alter("提交成功!");
						that.goUrl("/Personalcenter/my/wordCheck/wordCheckReport");
					}
					this.$refs.endDialogRef.close();
				})
			},

			// 打开弹窗
			openTipDialog() {
				this.$refs.endDialogRef.open();
			},

			goUrl(url) {
				uni.redirectTo({
					url: url
				})
			},

			end() {
				console.log('结束事件');
				//重新执行
				this.excuteQuestion("", -1);
			},
		},
	}
</script>

<style lang="less">
	.countdown {
		width: 100%;
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		margin-top: 60rpx;

		.tip {
			margin: 20rpx;
			color: #000;
			font-size: 24rpx;
		}
	}

	.word_check {
		width: 640rpx;
		margin: 20rpx auto 0;
		background-color: #fff;
		height: 600rpx;
		border-radius: 12rpx;
		box-shadow: 0upx 30upx 60upx #e3e3e3;
		display: flex;
		flex-direction: column;
		align-items: center;

		.word_header {
			margin-top: 20rpx;
			color: #3ac9b0;
			font-size: 44rpx;
		}
	}


	.choose_item {
		width: 560rpx;
		background-color: #f0f8f0;
		text-align: left;
		height: 80rpx;
		line-height: 80rpx;
		margin-top: 20rpx;
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding-left: 20rpx;

		.choose_btn {
			width: 40rpx;
			height: 40rpx;
			border-radius: 20rpx;
			background-color: #fff;
			margin-right: 20rpx;
		}
	}

	.word_footer {
		display: flex;
		justify-content: center;
		align-items: center;
		margin-top: 60rpx;

		>view {
			width: 200rpx;
			height: 90rpx;
			font-size: 32rpx;
			text-align: center;
			line-height: 90rpx;
			border-radius: 45rpx;
			font-weight: bold;
			color: #fff;
		}

		.left {
			background-color: #3ac9b0;
			margin: 0 40rpx;
		}

		.right {
			background-color: #fcb575;
			margin: 0 40rpx;
		}

	}
</style>
