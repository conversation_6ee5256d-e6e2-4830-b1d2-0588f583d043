<template>
	<page-meta :page-style="'overflow:'+(show?'hidden':'visible')"></page-meta>
	<view class="bgColor" :style="{height:screenHeight + 'rpx'}">
		<view class="box-bg flex-x">
			<uni-icons type="left" size="22" color="#fff" @click="back"></uni-icons>
			<view style="margin-left: 35%;" class="c-ff f-34">
			    学时详情
			</view>
		</view>
		<view style="margin-top: 170rpx;" class="plr-30">
			<view class="flex-s bg-ff radius-15 p-25">
				<view class="flex-a-c flex-box">
					<image :src="imgHost+'dxSelect/recharge/search-icon.png'" class="search-icon mr-15"></image>
					<input v-model="searchVlaue" class="uni-input" placeholder="请输入家长联系方式或学员编号" />
				</view>
				<text class="search-text" @click="openSelect">搜索</text>
			</view>
		</view>
		
		

		<view class="back-color plr-30" style="width: 92%;">
			<view class="recharge_detail f-30" style="width: 92%;">
				<view class="bg-ff radius-15 plr-30 ptb-40">
					<view class="f-32 bold">{{studentName}}学时详情</view>
					<view class="flex-s flex-x-b mt-40">
						<view class="purchased">
							<view class="pl-30 pt-20">
								<view class="c-2e8 f-32">{{lessonDetails.totalCourseHours || 0}}节</view>
								<view class="c-f9c f-26 mt-10">已购学时</view>
							</view>
						</view>
						<view class="residue">
							<view class="pl-30 pt-20">
								<view class="c-f1a f-32">{{lessonDetails.haveCourseHours || 0}}节</view>
								<view class="c-f1a f-26 mt-10">剩余学时</view>
							</view>
						</view>
					</view>
					
					<view class="flex-s flex-x-b mt-30">
						<view class="pay-deliver">
							<view class="pl-30 pt-20">
								<view class="c-f76 f-32">{{lessonDetails.haveDeliverHours || 0}}节</view>
								<view class="c-fba f-26 mt-10">剩余交付学时</view>
							</view>
						</view>
						<view class="residue-deliver">
							<view class="pl-30 pt-20">
								<view class="c-fe9 f-32">{{packagelength || 0}}节</view>
								<view class="c-ff4 f-26 mt-10">课程包交付学时</view>
							</view>
						</view>
					</view>
				</view>
				
				<view class="bg-ff radius-15 plr-30 ptb-40 mt-20" v-if="coursePackage!=null&& coursePackage!=''">
					<view class="flex-s">
						<view class="f-32 bold">课程包详情</view>
						<view class="f-28 c-66">不限制学时</view>
					</view>
					
				    <view v-for="(item,index) in coursePackage" :key="index">
						<view class="c-f1d f-30 mt-20 bold" v-if="item.courseNames.length>0">{{item.skuName}}</view>
						<view class="c-99 course" v-for="(val,index) in item.courseNames">{{val}}</view>
					</view>
				</view>
				
				<view class="bg-ff radius-15 plr-30 ptb-40 mt-20" v-if="orderList.data != undefined && orderList.data.length>0">
					<view class="f-32 bold ">充值明细</view>
					<view v-for="(item,index) in orderList.data" :key="index">
						<view class="c-33 f-30 mt-40 border-t pt-30" v-if="item.type==1">
							<view class="flex-s">
								<view>学时：{{item.courseLength}}节</view>
								<view v-if="item.deliverLength>0 &&item.deliverLength!=''">交付学时：{{item.deliverLength}}节</view>
								<view class="c-fe5">￥{{item.price}}</view>
							</view>
							<view class="c-99 mt-20">{{item.createTime}}</view>
						</view>
						
						
						<view class="c-33 f-30 mt-40 border-t pt-30" v-if="item.type==2">
							<view class="flex-s">
								<view>课程包：{{item.packageName}}</view>
								<view class="c-fe5">￥{{item.price}}</view>
							</view>
							<view class="c-99 mt-20">{{item.createTime}}</view>
						</view>
					</view>
				</view>
				
				<view v-if="orderList.data != undefined && orderList.data.length==0 ||!rechargeShow" class="bg-ff radius-15 mt-30" :style="{height: useHeight+'rpx'}">
					<view class="f-32 bold ml-30 mt-30">充值明细</view>
					<view class="t-c flex-col" :style="{height: useHeight -60 +'rpx'}">
						<image :src="imgHost+'alading/correcting/no_data.png'" class="mb-20 img_s"></image>
						<view style="color: #BDBDBD;">暂无充值明细</view>
					</view>
				</view>
				
				<!-- <view v-if="" class="bg-ff radius-15 mt-30" :style="{height: useHeight+'rpx'}">
					<view class="f-32 bold ml-30 mt-30">充值明细</view>
					<view class="t-c flex-col" :style="{height: useHeight -60 +'rpx'}">
						<image :src="imgHost+'alading/correcting/no_data.png'" class="mb-20 img_s"></image>
						<view style="color: #BDBDBD;">暂无充值明细</view>
					</view>
				</view> -->
			</view>
		</view>
		
		<!-- 没有学员提示 -->
		<uni-popup ref="notifyPopup" type="top" mask-background-color="rgba(0,0,0,0)">
			<view class="plr-60">
				<view class="t-c bg-ff flex-c ptb-25 radius-50 mt-80 notify">
					<image :src="imgHost+'dxSelect/recharge/hint.png'" class="hint-icon"></image>
					<view class="f-34 ml-15">暂无学员，请重新搜索</view>
				</view>
			</view>
		</uni-popup>
		
		<!-- 选择学员弹窗 -->
		<uni-popup ref="popup" type="center" @change="change">
			<view class="dialogBG">
				<view class="reviewCard_box positionRelative">
					<view class="cartoom_image">
						<image :src="dialog_iconUrl" mode="widthFix"></image>
					</view>
					<view class="review_close" @click="closeDialog">
						<uni-icons type="clear" size="28" color="#B1B1B1"></uni-icons>
					</view>
					<view class="reviewCard">
						<view class="reviewTitle bold">选择学员</view>
						<view class="dialogContent" v-for="(item,index) in studentList"
							@click="chooseStudent(item,index)" :class="isactive == index ? 'addclass' : 'not-selected'">
							{{item.realName}}
							<text style="margin-left: 20rpx;">({{item.studentCode}})</text>
						</view>
						<!-- 底部确定和取消按钮 -->
						<view class="mask-footer">
							<button class="confirm-button mask-left" @click="confirm()">确定</button>
							<button class="cancel-button mask-right" @click="closeDialog()">取消</button>
						</view>
					</view>
				</view>
			</view>
		</uni-popup>
	</view>
</template>

<script>
    import Util from '@/util/util.js'
	const { $navigationTo, $http} = require("@/util/methods.js")
	const {httpUser } = require('@/util/luch-request/indexUser.js')
	export default {
		data() {
			return {
				show: false, // 禁止穿透
				isactive: -1, // 选中索引
				array: [],
				index: 0,
				screenHeight: 0,
				imgHost: getApp().globalData.imgsomeHost,
				studentValue:0, // 学员
				selectShow:true, // 是否展示请选择字符
				useHeight: 0, //除头部之外高度
				studentCode: '', // 充值账户
				searchVlaue:'',
				studentList: [], // 学员列表
				lessonDetails: {}, // 学时详情
				coursePackage: '',
				orderList:[],
				merchantCode:'',
				packagelength:0, // 学时包交付学时
				page: 1,
				no_more: false,
				rechargeShow:false,// 
				studentName:'',
                
                dialog_iconUrl:Util.getCachedPic("https://document.dxznjy.com/dxSelect/dialog_icon.png","dialog_icon_path"),
			};
		},
		onLoad() {
			this.getHeight()
		},
		onShow() {
			this.getPayToken();
			// this.$refs.notifyPopup.open()
			// this.$refs.popup.open()
		},
		onReady() {
		    uni.getSystemInfo({
		        success: (res) => {
		            // 可使用窗口高度，将px转换rpx
		            let h = (res.windowHeight * (750 / res.windowWidth));
		            this.useHeight = h - 830;
		        }
		    })
		},
		onHide() {
			uni.removeStorage({
				key: 'payToken',
				success: (res) => {
					console.log('删除payToken');
				}
			})
		}, 
		onUnload() {
			uni.removeStorage({
				key: 'payToken',
				success: (res) => {
					console.log('删除payToken');
				}
			})
		}, //监听页面销毁
		onReachBottom() {
			if (this.page >= this.orderList.totalPage) {
				this.no_more = true
				return false;
			}
			this.getOrderPage(true, ++this.page);
		},
		methods: {
			back() {
				uni.navigateBack()
			},

			getHeight() {
				// 获取系统信息
				const systemInfo = uni.getSystemInfoSync();
				// 获取屏幕高度
				console.log(systemInfo)
				this.screenHeight = systemInfo.screenHeight - 200;
			},
			
			clearChange(){
				this.index = 0;
				this.selectShow = true;
			},
			
			// 禁止穿透滚动
			change(e) {
				this.show = e.show
			},
			// 选择学员关闭图标
			closeDialog() {
				this.$refs.popup.close();
				this.isactive = -1;
				this.studentCode = '';
			},
			
			chooseStudent(item, index) {
				this.isactive = index;
			},
			
			async getPayToken(){
				let token = uni.getStorageSync('token');
				let data = { memberToken:token}
				let res = await this.$httpUser.get("new/security/login/school/member/token",data);
				uni.setStorageSync('payToken', res.data.data.token);
			},
			// 选择学员弹窗确定按钮
			confirm() {
				if (this.isactive == -1) {
					this.$util.alter('请先选择学员');
					return
				}
				this.studentCode = this.studentList[this.isactive].studentCode;
				this.studentName = this.studentList[this.isactive].realName;
				this.$refs.popup.close();
				this.isactive = -1;
				this.getCourse();
				this.getCoursePackage();
				this.getOrderPage();
			},
			
			
			
			// 查询学员
			async openSelect() {
				let that = this;
				that.studentCode = '';
				if (that.searchVlaue == '') {
					that.$util.alter('搜索信息不能为空');
					return;
				}
				uni.showLoading();
				let res = await $http({
					url: 'znyy/areas/student/getStudentInfo',
					method: 'get',
					data: {
						value: that.searchVlaue
					}
				})
				uni.hideLoading();
				if (res.data.length > 0) {
					that.studentList = res.data;
					if (that.studentList.length > 1) {
						this.$refs.popup.open();
					} else {
						this.studentCode = this.studentList[0].studentCode;
						this.studentName = this.studentList[0].realName;
						this.getCourse();
						this.getCoursePackage();
						this.getOrderPage();
						
					}
				} else {
					this.$refs.notifyPopup.open();
					setTimeout(() => {
						this.$refs.notifyPopup.close();
					}, 1500)
				}
			},
			
			async getCourse() {
				let res = await $http({
					url: 'znyy/school/recharge/student/course',
					data:{
						studentCode:this.studentCode
					}
				})
				console.log(res)
				if (res) {
					this.lessonDetails = res.data;
					this.packagelength = (this.lessonDetails.haveDeliverNum - this.lessonDetails.haveDeliverHours).toFixed(2);
				}
			},
			
			// 课程包详细课程
			async getCoursePackage() {
				this.coursePackage = '';
				let res = await $http({
					url: 'znyy/course/student/getStudentCoursePackage',
					data:{
						studentCode:this.studentCode
					}
				})
				if (res) {
					this.coursePackage = res.data;
				}
			},
			
			// 充值明细
			async getOrderPage(isPage, page) {
				let res = await $http({
					url: 'znyy/school/recharge/getStudentRechargeOrderPage',
					data:{
						pageNum:this.page,
						pageSize:10,
						studentCode:this.studentCode
					}
				})
				this.rechargeShow = true;
				if (res) {
					if (isPage) {
						let old = this.orderList.data;
						this.orderList.data = [...old, ...res.data.data]
					} else {
						this.orderList = res.data;
					}
				}
			},
			
		}
	}
</script>
<style>
	page {
		background-color: #f3f8fc;
	}
</style>
<style lang="scss" scoped>
	.bgColor {
		padding-top: 1rpx;
		background: linear-gradient(to bottom,#2F8C70, #fff );
	}

	.box-bg {
		width: 100%;
		z-index: 9;
		position: fixed;
		padding-top: 80rpx;
		background: linear-gradient(to bottom,#55a18a,#59a38d);
	}

	/deep/ .uni-nav-bar-text {
		font-size: 34rpx !important;
	}

	/deep/ .uni-navbar--border {
		border-bottom-color: #2F8C70 !important;
	}

	.class_details {
		height: 60rpx;
		width: 160rpx;
		color: #F2F2F2;
		font-size: 30rpx;
		margin-top: 100rpx;
		line-height: 60rpx;
		text-align: center;
		border-radius: 30rpx;
		background-color: rgba(255, 255, 255, 0.2);
	}

	.back-color {
		height: 80%;
		margin-top: 165rpx;
		background-color: #f2f8fd;
		border-top-left-radius: 15rpx;
		border-top-right-radius: 15rpx;
	}

	.recharge_detail {
		position: absolute;
		top: 300rpx;
		padding-bottom: 30rpx;
	}

	.add {
		width: 100%;
		height: 80rpx;
		color: #2E896F;
		font-size: 30rpx;
		line-height: 80rpx;
		text-align: center;
		border-radius: 45rpx;
		border: 1px solid #2E896F;
	}

	.search-icon {
		width: 38rpx;
		height: 38rpx;
	}

	.flex-x {
		display: flex;
		align-items: center;
	}

	.border-b {
		border-bottom: 1px solid #EFEFEF;
	}

	/deep/.uni-select {
		border: none !important;
	}

	/deep/ .uni-select__input-text {
		font-size: 30rpx;
		margin-right: 40rpx !important;
	}

	.amount {
		position: fixed;
		bottom: 0;
		left: 0;
		z-index: 9;
		width: 92%;
		height: 110rpx;
		padding: 0 30rpx;
		font-size: 28rpx;
		box-shadow: 0rpx 2rpx 30rpx #e3e3e3;
	}
	
	.pay{
		color: #fff;
		width: 200rpx;
		height: 70rpx;
		font-size: 32rpx;
		text-align: center;
		line-height: 70rpx;
		border-radius: 35rpx;
		background-image: linear-gradient(to bottom, #88CFBA, #1D755C);
	}
	
	.course{
		color: #666;
		height: 60rpx;
		width: 500rpx;
		font-size: 26rpx;
		margin-top: 35rpx;
		line-height: 60rpx;
		padding-left: 24rpx;
		border-radius: 6rpx;
		background-color: #f5f5f5;
	}
	
	.students-style{
		padding-top: 170rpx;
		height: 100rpx;
		line-height: 100rpx;
	}
	
	.purchased{
		width:300rpx;
		height: 135rpx;
		background-image: url('https://document.dxznjy.com/applet/4.1.5/class-ygks.png');
		background-repeat: no-repeat;
		background-size: 300rpx 135rpx;
	}
	
	.residue{
		width:300rpx;
		height: 135rpx;
		background-image: url('https://document.dxznjy.com/applet/4.1.5/class-syks.png');
		background-repeat: no-repeat;
		background-size: 300rpx 135rpx;
	}
	
	
	
	.pay-deliver{
		width:300rpx;
		height: 140rpx;
		background-image: url('https://document.dxznjy.com/applet/4.1.5/class-ygjfks.png');
		background-repeat: no-repeat;
		background-size: 300rpx 135rpx;
	}
	
	.residue-deliver{
		width:300rpx;
		height: 135rpx;
		background-image: url('https://document.dxznjy.com/applet/4.1.5/class-syjfks.png');
		background-repeat: no-repeat;
		background-size: 300rpx 135rpx;
	}
	
	
	.border-t{
		border-top: 1px solid #EFEFEF;
	}
	
	// 搜索框
	/deep/.uni-searchbar {
		padding: 0 !important;
	}
	
	/deep/.uni-searchbar__box {
		height: 90rpx;
		padding: 20rpx 30rpx !important;
		justify-content: start;
		border-radius: 14rpx !important;
	}
	
	.uni-search-bar .uni-input {
		text-align: left;
		/* 设置文字左对齐 */
	}
	
	.search {
		position: relative;
	
		.search-text {
			position: absolute;
			top: -5rpx;
			right: 30rpx;
			color: #1D755C;
			font-size: 30rpx;
			padding-left: 30rpx;
		}
	}
	
	/deep/.uni-searchbar__box-icon-search {
		padding: 0 !important;
	}
	
	/deep/.uni-icons {
		font-size: 42rpx !important;
	}
	
	.img_s {
	    width: 160rpx;
		height: 160rpx;
	}
	
	.notify {
		box-shadow: 0rpx 0rpx 20rpx #e0e0e0;
	}
	
	.hint-icon {
		width: 38rpx;
		height: 38rpx;
	}
	
	
	/* 弹窗样式 */
	.dialogBG {
		width: 100%;
		/* height: 100%; */
	}
	
	/* 21天结束复习弹窗样式 */
	.reviewCard_box {
		width: 670rpx;
		/* height: 560rpx; */
		position: relative;
	}
	
	.reviewCard_box image {
		width: 100%;
		height: 100%;
	}
	
	.reviewCard {
		position: relative;
		width: 100%;
		height: 100%;
		background: #FFFFFF;
		color: #000;
		border-radius: 24upx;
		padding: 50upx 55upx;
		box-sizing: border-box;
	}
	
	.successFn-img {
		position: absolute;
		left: 160rpx;
		top: -298rpx;
		width: 300rpx;
		height: 300rpx;
		z-index: 9;
	
	}
	
	.successFnContent {
		font-size: 30rpx;
		font-family: AlibabaPuHuiTiR;
		color: #333333;
		line-height: 50rpx;
		margin-top: 40rpx;
		margin-bottom: 40rpx;
	}
	
	.cartoom_image {
		width: 420rpx;
		position: absolute;
		top: -250rpx;
		left: 145rpx;
		z-index: -1;
	}
	
	.review_close {
		position: absolute;
		top: 20rpx;
		right: 20rpx;
		z-index: 1;
	}
	
	.reviewTitle {
		width: 100%;
		text-align: center;
		font-size: 34upx;
		display: flex;
		justify-content: center;
	}
	
	.dialogContent {
		box-sizing: border-box;
		font-size: 32upx;
		line-height: 45upx;
		text-align: center;
		margin-top: 40rpx;
	}
	
	/* 底部确定和取消按钮 */
	.mask-footer {
		margin: 0 auto;
		display: flex;
		width: 530rpx;
		justify-content: space-between;
		margin-top: 20px;
	}
	
	.mask-footer button {
		width: 250rpx;
		height: 80rpx;
		line-height: 80rpx;
		border-radius: 45rpx;
		border: 1rpx solid #2E896F;
		font-size: 30rpx;
	}
	
	.mask-left {
		background: linear-gradient(180deg, #88CFBA 0%, #1D755C 100%);
		color: #FFFFFF;
	}
	
	.mask-right {
		color: #2E896F;
	}
	
	/* 底部确定和取消按钮 */
	
	.addclass {
		width: 100%;
		height: 70rpx;
		line-height: 70rpx;
		font-size: 30rpx;
		color: #2E896F;
		border: 1px solid #2E896F;
		border-radius: 35rpx;
	}
	
	.not-selected {
		width: 100%;
		height: 70rpx;
		line-height: 70rpx;
		font-size: 30rpx;
		color: #000;
		border: 1px solid #C8C8C8;
		border-radius: 35rpx;
	}
	
	.search-icon {
		width: 32rpx;
		height: 32rpx;
	}
	
	.uni-input {
		width: 100%;
		font-size: 30rpx !important;
	}
	
	.search-text {
		font-size: 30rpx;
		font-family: AlibabaPuHuiTiR;
		color: #1D755C;
		border-left: 1rpx solid #EFEFEF;
		padding-left: 30rpx;
	}
	
	
	.addclass {
		width: 100%;
		height: 70rpx;
		line-height: 70rpx;
		font-size: 30rpx;
		color: #2E896F;
		border: 1px solid #2E896F;
		border-radius: 35rpx;
	}
	
	.not-selected {
		width: 100%;
		height: 70rpx;
		line-height: 70rpx;
		font-size: 30rpx;
		color: #000;
		border: 1px solid #C8C8C8;
		border-radius: 35rpx;
	}
</style>
