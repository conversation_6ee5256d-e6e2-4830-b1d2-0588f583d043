<template>
  <page-meta page-style="overflow:visible"></page-meta>
  <view>
    <view class="h5-content">
      <rich-text :nodes="contentH5"></rich-text>
    </view>
  </view>
</template>

<script>
  const { $http } = require('@/util/methods.js');
  export default {
    data() {
      return {
        imgHost: getApp().globalData.imgsomeHost,
        screenHeight: '', // 屏幕高度
        useHeight: '', // 屏幕高度

        title: '',
        shopdetail: null,
        contentH5: ''
      };
    },
    onLoad(option) {
      this.title = option.title;
      uni.setNavigationBarTitle({
        title: this.title
      });
      if (option.id) {
        this.getMeetingDetails(option.id);
      }
    },

    onReady() {
      this.getHeight();
      uni.getSystemInfo({
        success: (res) => {
          // 可使用窗口高度，将px转换rpx
          let h = res.windowHeight * (750 / res.windowWidth);
          this.useHeight = h;
        }
      });
    },

    methods: {
      getHeight() {
        // 获取系统信息
        const systemInfo = uni.getSystemInfoSync();
        // 获取屏幕高度
        this.screenHeight = systemInfo.windowHeight;
        this.screenWidth = systemInfo.windowWidth - 60;
      },

      async getMeetingDetails(id) {
        let _this = this;
        const res = await $http({
          url: 'zx/course/courseDetail',
          data: {
            courseId: id
          }
        });
        if (res) {
          _this.shopdetail = res.data;
          this.getH5Content();
        }
      },

      getH5Content() {
        if (this.title == '大会日程') {
          this.contentH5 = this.shopdetail.meetingSchedule;
        } else if (this.title == '专题报告') {
          this.contentH5 = this.shopdetail.specialReport;
        } else if (this.title == '酒店详情') {
          this.contentH5 = this.shopdetail.hotelDetails;
        } else if (this.title == '合作伙伴') {
          this.contentH5 = this.shopdetail.businessPartner;
        }
      },

      goback() {
        uni.navigateBack();
      }
    }
  };
</script>

<style lang="scss" scoped>
  .page_title {
    position: absolute;
    top: 100upx;
    width: 100%;
    text-align: center;
  }

  .page_icon {
    position: absolute;
    top: 100upx;
    left: 20rpx;
  }

  .header-img {
    width: 100%;
    height: 400rpx;
  }

  .h5-content {
    text-align: center;
    margin: 30rpx;
    width: 690rpx;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 14rpx;
  }
</style>
