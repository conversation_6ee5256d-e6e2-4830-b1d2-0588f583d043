<!-- 进货/出货 处理完成 -->
<template>
  <view class="plr-30 pb-30">
    <view class="ptb-35 bg-ff plr-30 radius-15 flex-s">
      <view class="f-30">{{ merchantCode == orderInfo.enterMerchantCode ? '进货' : '出货' }}{{ orderInfo.codeNum || 0 }}个超人码</view>
      <view class="classPayStatus_btn" style="background-color: #2dc032">已完成</view>
    </view>

    <view class="flex-a-c mtb-20" v-if="orderInfo.isTwoOut == 0" style="align-items: baseline">
      <image :src="imgHost + 'dxSelect/secondPhase/prompt2.png'" class="prompt-img"></image>
      <view class="ml-10 c-33 f-28">由于您的上级超人码不足，本次超人码由{{ orderInfo.outMerchantName }}提供</view>
    </view>

    <!-- 待处理 -->
    <view class="pt-40 bg-ff plr-30 radius-15 mb-30" :class="orderInfo.isTwoOut == 0 ? '' : 'mt-30'">
      <view class="flex-s pb-30">
        <view class="f-30 c-00 fontWeight">
          {{ dateTime }}
          <!-- {{orderInfo.createdTime.slice(0,4)}}年{{orderInfo.createdTime.slice(5,7)}}月 -->
          <uni-icons class="ml-10" type="right" color="#c7c7c7" size="16"></uni-icons>
        </view>
        <!-- 后续需要添加判断，待处理没有更多数据的时候不显示更多 -->
        <view class="flex-c c-99">
          <text class="mr-14 f-28">{{ orderInfo.passStatus != 1 ? (orderInfo.passStatus == 2 ? '自动完成' : '总部确认完成') : '自行完成' }}</text>
        </view>
      </view>

      <scroll-view :scroll-top="scrollTop" scroll-y="true" class="listbox" v-for="(item, index) in applycodeList" :key="index">
        <view class="list border-t">
          <view class="ptb-30 c-00 f-30">
            <text>超人码：{{ item }}</text>
          </view>
        </view>
      </scroll-view>
    </view>
  </view>
</template>

<script>
  const { $http } = require('@/util/methods.js');
  export default {
    data() {
      return {
        type: 2, //1进货    2出货
        imgHost: getApp().globalData.imgsomeHost,
        orderInfo: '',
        applycodeList: {}, // 码列表
        scrollTop: '',
        merchantCode: '',
        dateTime: '' // 日期时间
      };
    },
    onLoad(e) {
      this.orderInfo = JSON.parse(decodeURIComponent(e.orderInfo));
      console.log(this.orderInfo);
      this.type = this.orderInfo.type;
      this.dateTime = this.orderInfo.createdTime.slice(0, 4) + '年' + this.orderInfo.createdTime.slice(5, 7) + '月';
    },
    onReady() {
      uni.setNavigationBarTitle({
        title: this.merchantCode == this.orderInfo.enterMerchantCode ? '进货管理' : '出货管理'
      });
    },

    onShow() {
      this.merchantCode = uni.getStorageSync('merchantCode');
      this.list();
    },

    onReachBottom() {
      // if (this.page >= this.listS.totalPage) {
      // 	this.no_more = true
      // 	return false;
      // }
      // this.list(true, ++this.page);
    },
    methods: {
      async list() {
        let _this = this;
        const res = await $http({
          url: 'zx/invitation/code/applyCodeDetail',
          data: {
            outCodeApplyId: _this.orderInfo.outCodeApplyId
          }
        });
        console.log(res);
        if (res) {
          _this.applycodeList = res.data;
        }
      },

      // 复制文案
      copy(item) {
        uni.setClipboardData({
          data: item,
          success: function (res) {
            uni.getClipboardData({
              success: function (res) {
                uni.showToast({
                  title: '已复制到剪贴板'
                });
              }
            });
          }
        });
      }
    }
  };
</script>

<style lang="scss" scoped>
  .flex_s {
    display: flex;
    align-items: center;
  }

  .border-t {
    border-top: 1px dashed #eee;
  }

  .prompt-img {
    width: 25rpx;
    height: 25rpx;
  }
</style>
