<template>
  <view class="plr-30">
    <view class="positioning" @click="goback">
      <uni-icons type="left" size="24" color="#000"></uni-icons>
    </view>
    <view class="word-position t-c col-12">
      <view v-if="type == 1" class="f-34">当日语法预览</view>
      <view v-else class="f-34">往期语法预览</view>
    </view>
    <view class="f-32 mt-180 bg-ff p-30 radius-15">
      <view class="flex_s flex-y-c">
        <view>阶段：</view>
        <view class="time-style flex f-30 flex_1">
          <view @click="getCourse" class="wh-90 flex_x">
            <image class="one mr-15" src="/static/index/time-icon.png"></image>
            <text class="overstepSingle" :class="lesson ? '' : 'date-style'">{{ lesson || '请选择' }}</text>
            <u-picker
              :show="courseShow"
              :immediateChange="true"
              :columns="couserlist"
              @cancel="cancelCourse"
              @confirm="confirmCourse"
              keyName="courseName"
              confirmColor="#357B71"
              :defaultIndex="[0]"
              ref="coursePicker"
            ></u-picker>
          </view>

          <uni-icons v-if="lesson" type="closeempty" size="16" color="#C8C8C8" class="right-icon" @click.stop="emptyCouese"></uni-icons>
          <uni-icons v-else type="right" size="16" color="#C8C8C8" class="right-icon"></uni-icons>
        </view>
      </view>
      <view class="flex_s flex-y-c mt-30" v-if="type == 2">
        <view>日期：</view>
        <view class="time-style flex f-30 flex_1">
          <view @click="getTime" class="wh-90 flex_x">
            <image class="one mr-15" src="/static/index/time-icon.png"></image>
            <text :class="date ? '' : 'date-style'">{{ date || '请选择' }}</text>
            <u-picker
              :show="show"
              :immediateChange="true"
              :columns="beforeDate"
              keyName="time"
              @cancel="cancel"
              @confirm="confirm"
              confirmColor="#357B71"
              :defaultIndex="[0]"
              ref="timePicker"
            ></u-picker>
          </view>

          <uni-icons v-if="date" type="closeempty" size="16" color="#C8C8C8" class="right-icon" @click.stop="emptyDate"></uni-icons>
          <uni-icons v-else type="right" size="16" color="#C8C8C8" class="right-icon"></uni-icons>
        </view>
      </view>
    </view>

    <view class="bg-ff radius-15 plr-20 mt-30" v-if="allWord.totalItems != 0" :style="{ height: useHeight + 'rpx' }" style="position: relative">
      <view class="flex_s mt-30 c-66 f-32">
        <view class="col-4">语法知识点</view>
        <view class="col-2 t-c">轮次</view>
        <view v-if="type == 2" class="col-6 t-r">应复习日期</view>
        <view v-else class="col-6 t-r">学习时间</view>
      </view>

      <scroll-view
        :scroll-top="scrollTop"
        scroll-y="true"
        class="scroll-Y ptb-20"
        @scrolltoupper="upper"
        @scrolltolower="lower"
        @scroll="scroll"
        :style="{ height: wordHeight + 'rpx' }"
      >
        <view class="flex_s mt-60 c-66 f-30" v-for="(item, index) in allWord.data" :key="index">
          <view class="col-4">
            <uni-tooltip :content="item.knowledgeName" placement="right">
              <text>{{ truncatedKnowledgeName(item.knowledgeName) }}</text>
            </uni-tooltip>
            <text class="ml-20 h-30 boxl-50 lh-30 f-18 t-c radius-6" :style="item.phaseColor">{{ truncatedPhase(item.phase) }}</text>
          </view>
          <view class="col-2 t-c">{{ item.round }}</view>
          <view v-if="type == 2" class="col-6 t-r">{{ item.learningTime.slice(0, 10) || '' }}</view>
          <view v-else class="col-6 t-r">{{ item.learningTime }}</view>
        </view>
      </scroll-view>

      <view class="title paging">
        <page-pagination
          ref="pageChange"
          :total="page.total"
          :pageSize="page.pageSize"
          :currentPage="page.currentPage"
          :numAround="true"
          size="large"
          @change="change"
        ></page-pagination>
      </view>
    </view>

    <view class="bg-ff radius-15 plr-20 mt-30 t-c flex-col" v-else :style="{ height: useHeight + 'rpx' }" style="position: relative">
      <image src="/static/index/<EMAIL>" mode="widthFix" class="mb-20 img_s"></image>
      <view style="color: #bdbdbd">暂无数据</view>
    </view>
  </view>
</template>

<script>
  import pagePagination from '../components/page-pagination/components/page-pagination/page-pagination.vue';
  import UniTooltip from './component/uni-tooltip/components/uni-tooltip/uni-tooltip.vue';

  export default {
    components: {
      pagePagination,
      UniTooltip
    },
    data() {
      return {
        page: {
          total: 1,
          pageSize: 9,
          currentPage: 1
        },
        useHeight: 0, // 除头部之外高度
        type: '', // 1当日单词 2往期单词
        studentCode: '', //学员Code
        lesson: '', // 课程
        courseCode: '', //  课程code
        couserlist: [], // 课程列表
        courseShow: false, // 课程选择
        datetime: '', // 日期时间
        date: '', //显示日期
        show: false,

        allWord: {}, // 所有单词（包括当日单词 、往期单词）
        beforeDate: [], // 往期单词日期集合

        scrollTop: 0,
        old: {
          scrollTop: 0
        },
        wordHeight: 0
        // defaultIndex:[0], // 默认索引
      };
    },
    onLoad(options) {
      // console.log(options, '当日语法预览')
      this.studentCode = options.studentCode;
      // todo 学员 code 固定值 用于测试
      // this.studentCode = '6231217888'
      this.type = options.type;
    },
    onReady() {
      uni.getSystemInfo({
        success: (res) => {
          // 可使用窗口高度，将px转换rpx
          let h = res.windowHeight * (750 / res.windowWidth);
          if (this.type == 1) {
            this.useHeight = h - 370;
            this.wordHeight = this.useHeight - 220;
          } else {
            this.useHeight = h - 470;
            this.wordHeight = this.useHeight - 220;
          }
        }
      });
    },
    onShow() {
      if (this.type == 1) {
        this.getTodayGra();
      } else {
        this.getBeforeGra();
      }
    },
    methods: {
      // 课程选择(弹出层)
      getCourse() {
        this.$refs.coursePicker.setIndexs([0]); // 注意这里是数组[索引值]
        if (this.type == 2) {
          if (this.lesson == '') {
            this.courseShow = true;
            this.getCourseList();
          } else {
            this.getCourseList();
            this.courseShow = true;
            if (this.datetime != '') {
              this.getBeforeGra();
            }
          }
          if (this.lesson == '' && this.datetime == '') {
            this.courseCode = '';
            this.getBeforeGra();
            this.getCourseList();
            this.getBeforeDate();
          }
        } else {
          if (this.lesson == '') {
            this.courseShow = true;
            this.getCourseList();
            this.getTodayGra();
          } else {
            this.courseCode = '';
            this.courseShow = true;
          }
        }
      },
      getTime() {
        this.$refs.timePicker.setIndexs([0]); // 注意这里是数组[索引值]
        this.show = true;
        this.getBeforeDate();
        if (this.lesson != '') {
          this.getBeforeGra();
        }
        if (this.lesson == '' && this.date == '') {
          this.courseCode = '';
          this.getBeforeGra();
          this.getCourseList();
          this.getBeforeDate();
        }
      },
      confirmCourse(e) {
        this.lesson = e.value[0].courseName;
        this.courseCode = e.value[0].courseCode;
        this.courseShow = false;
        if (this.type == 1) {
          this.page.currentPage = 1;
          this.$refs.pageChange.nowPage = 1;
          this.getTodayGra();
        } else {
          this.page.currentPage = 1;
          this.$refs.pageChange.nowPage = 1;
          this.getBeforeGra();
        }
      },
      cancelCourse() {
        this.courseShow = false;
        // if (this.type == 1) {
        //   this.getTodayGra()
        // } else {
        //   this.getBeforeGra()
        // }
      },
      // 日期选择
      confirm(e) {
        // console.log(e)
        this.datetime = e.value[0].time;
        this.date = e.value[0].time;
        this.show = false;
        this.page.currentPage = 1;
        this.$refs.pageChange.nowPage = 1;
        this.getBeforeGra();
      },
      cancel() {
        this.show = false;
        this.getBeforeGra();
      },

      // 清空课程
      emptyCouese() {
        this.lesson = '';
        this.courseCode = '';
        this.$refs.pageChange.nowPage = 1;
        if (this.type == 2) {
          this.getBeforeGra();
        } else {
          this.getTodayGra();
        }
      },
      // 清空日期
      emptyDate() {
        this.date = '';
        this.datetime = '';
        this.$refs.pageChange.nowPage = 1;
        this.getBeforeGra();
      },
      // 分页器
      change(currentPage, type) {
        this.page.currentPage = currentPage;
        if (this.type == 1) {
          this.getTodayGra();
        } else {
          this.getBeforeGra();
        }
      },
      // 获得当日语法
      async getTodayGra() {
        uni.showLoading();
        let res = await this.$httpUser.get('dyf/wap/applet/sameDayPage', {
          studentCode: this.studentCode,
          phase: this.courseCode,
          pageNum: this.page.currentPage,
          pageSize: this.page.pageSize
        });
        uni.hideLoading();
        if (res.data.success) {
          this.allWord = res.data.data;
          this.allWord.data.forEach((item) => {
            item.phaseColor = this.getPhaseColor(item.phase);
          });
          this.page.total = this.allWord.totalItems;
        }
      },

      // 获得往期语法
      async getBeforeGra() {
        uni.showLoading();
        let res = await this.$httpUser.get('dyf/wap/applet/formerDayPage', {
          studentCode: this.studentCode,
          phase: this.courseCode,
          pageNum: this.page.currentPage,
          pageSize: this.page.pageSize,
          reviewDate: this.datetime
          // reviewDate: '2024-08-07'
        });
        uni.hideLoading();
        if (res.data.success) {
          this.allWord = res.data.data;
          this.allWord.data.forEach((item) => {
            item.phaseColor = this.getPhaseColor(item.phase);
          });
          this.page.total = this.allWord.totalItems;
        }
      },

      truncatedKnowledgeName(name) {
        if (name.length >= 3) {
          return name.slice(0, 3) + '...';
        }
        return name;
      },

      truncatedPhase(phase) {
        console.log('truncatedPhase:', phase);
        if (phase.length >= 3) {
          return phase.slice(0, 3);
        }
        return phase;
      },

      // 获取往期单词日期列表
      async getBeforeDate() {
        uni.showLoading();
        let res = await this.$httpUser.get('dyf/wap/applet/reviewDate', {
          studentCode: this.studentCode
        });
        uni.hideLoading();
        if (res.data.success) {
          let timelist = [];
          timelist.push(res.data.data);
          let data = timelist[0].map((item) => ({ time: item }));
          data.sort(function (a, b) {
            return a.time < b.time ? 1 : -1;
          });
          this.beforeDate = [];
          this.beforeDate.push(data);
        }
      },

      getPhaseColor(phase) {
        if (phase.includes('小学')) {
          return 'color: #81e2af;'; // 绿色
        } else if (phase.includes('初中')) {
          return 'color: #ffd593;'; // 橘色
        } else if (phase.includes('高中')) {
          return 'color: #6de2ff;'; // 蓝色
        }
        return ''; // 或者您可以返回其他默认颜色
      },
      // 获取阶段列表
      async getCourseList() {
        uni.showLoading();
        let res = await this.$httpUser.get('dyf/wap/applet/queryAll', {});
        const dictData = res.data.data;
        const phaseData = dictData.grammar_phase.map((item) => {
          return {
            courseName: item.dictLabel,
            courseCode: item.dictValue
          };
        });
        uni.hideLoading();
        if (res.data.success) {
          this.couserlist = [];
          this.couserlist.push(phaseData);
        }
      },

      goback() {
        uni.navigateBack();
      },

      upper: function (e) {
        // console.log(e)
      },
      lower: function (e) {
        // console.log(e)
      },
      scroll: function (e) {
        // console.log(e)
        this.old.scrollTop = e.detail.scrollTop;
      }
    }
  };
</script>

<style>
  page {
    background-color: #f3f8fc;
  }
</style>

<style lang="scss" scoped>
  .positioning {
    position: fixed;
    top: 100rpx;
    left: 30rpx;
    z-index: 9;
  }

  .word-position {
    position: fixed;
    top: 100rpx;
    left: 0;
  }

  .mt-180 {
    margin-top: 180rpx;
  }

  .flex_s {
    display: flex;
  }

  .flex_1 {
    flex: 1;
  }

  .flex_x {
    display: flex;
    align-items: center;
  }

  .title {
    width: 100%;
    text-align: center;
  }

  .paging {
    position: absolute;
    bottom: 40rpx;
    left: 0;
  }

  /deep/.page-pagination .page-con .page-scroll-child .page-num .ellipsis-btn {
    padding: 0 !important;
    border: 0 !important;
  }

  .time-style {
    position: relative;
    border: 1px solid #c8c8c8;
    width: 240rpx;
    padding: 12rpx 25rpx;
    border-radius: 35rpx;
  }

  .right-icon {
    position: absolute;
    top: 12rpx;
    right: 15rpx;
  }

  .one {
    width: 30rpx;
    height: 30rpx;
    min-width: 30rpx;
  }

  .date-style {
    color: #666;
  }

  .img_s {
    width: 160rpx;
  }

  /deep/.u-toolbar.data-v-55c89db1 {
    border-bottom: 1px solid #e0e0e0;
  }

  /deep/.u-line-1 {
    line-height: 68rpx !important;
    background-color: #f4f4f4 !important;
  }

  /deep/.u-picker__view {
    height: 440rpx !important;
  }

  /deep/.u-picker__view__column.data-v-f45a262e {
    border-radius: 12rpx;
  }

  /deep/.u-popup__content.data-v-3a231fda {
    border-radius: 12rpx;
    margin: 0 20rpx 20rpx 20rpx;
  }

  /deep/.u-picker__view__column__item {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    font-size: 30rpx !important;
    margin-left: 10rpx;
  }

  scroll-view ::-webkit-scrollbar {
    width: 0;
    height: 0;
    background-color: transparent;
  }

  .wh-90 {
    width: 94%;
  }

  /deep/.u-popup__content {
    margin: 20rpx;
    border-radius: 15rpx;
  }
</style>
