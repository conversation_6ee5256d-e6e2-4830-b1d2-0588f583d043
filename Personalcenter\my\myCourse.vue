<template>
  <view class="plr-30">
    <view class="radius-20">
      <u-tabs
        :list="userList"
        lineWidth="40"
        lineHeight="11"
        :activeStyle="{ color: '#333333', fontWeight: 'bold' }"
        :inactiveStyle="{
          color: '#5A5A5A ',
          transform: 'scale(1)',
          fontSize: '28rpx'
        }"
        itemStyle="padding-left:5px; padding-right: 25px; height: 34px;"
        :lineColor="`url(${lineBg}) 100% 110%`"
        @click="tabsClick"
      ></u-tabs>
      <view class="uni-margin-wrap">
        <view v-for="item in classList" :key="item.id" class="swiper-item uni-bg-red mb-30 bg-ff">
          <view class="title_name_css twolist fontWeight">{{ item.courseName }}</view>
          <view class="title_css">
            <text>
              <text>课程类型：</text>
              <text>{{ item.courseType == 2 ? '交付课-体验课' : item.courseType == 3 ? '交付课-正式课' : '录播课' }}</text>
            </text>
            <!-- <text class="right_css"><text>教练：</text><text>王某某</text></text> -->
          </view>
          <view class="title_css">上课时间：{{ item.createdTime }}</view>
          <!--     <view class="title_css">
            <text>
              <text>教练：</text>
              <text>{{ myClassList.nextStudyCourse.teacher || '' }}</text>
            </text>
          </view> -->
          <view class="bottom_css pt-30 pb-15 mt-30">
            <!--     <button
              v-if="checkNextTime(item.dateTime)"
              @click="gotoNextleave(item)"
              class="btn common_btn_orange_active btn_orange"
              style="margin-right: 10rpx"
              type="default"
              plain="true"
            >
              请假
            </button> -->
            <button @click="goLastback()" v-if="item.courseType == 3" class="btn btn_plan btn_left" type="default" plain="true">查看反馈</button>
            <button v-if="item.courseType == 4 && item.lastStudyTime && item.refundStatus != 2" @tap="gostudy(item)" class="btn btn_plan btn_left" type="default" plain="true">
              继续学习
            </button>
            <!-- <button  v-if="item.courseType==4&&item.lastStudyTime"  @click="skintap('Personalcenter/my/growthReport?courseId='+item.courseId+'&studentCode='+item.studentCode)" class="btn btn_orange" type="default"
							plain="true">学习成长报告</button> -->
            <!--  -->
            <button
              v-if="item.courseType == 4 && !item.lastStudyTime && item.refundStatus != 2"
              @tap="gostudy(item)"
              style="width: 222rpx"
              class="btn btn_orange"
              type="default"
              plain="true"
            >
              进入学习
            </button>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
  const { $navigationTo, $http } = require('@/util/methods.js');
  import dayjs from 'dayjs';
  export default {
    data() {
      return {
        page: 1,
        pageSize: 10,
        lineBg: 'https://document.dxznjy.com/course/01c96465d9ea46f69aa97465b1201aef.png',
        userList: [
          { name: '全部', courseType: '' },
          { name: '录播课', courseType: 4 },
          { name: '交付课', courseType: 3 }
        ],
        // 课程列表
        classList: [],
        no_more: false,
        infoLists: {},
        myClassList: {
          lastStudyCourse: {},
          nextStudyCourse: {}
        },
        imgHost: getApp().globalData.imgsomeHost,
        courseType: ''
      };
    },
    onShow() {
      this.getSubject();
    },

    onReachBottom() {
      if (this.page * 20 >= this.infoLists.totalItems) {
        this.no_more = true;
        return false;
      }
      this.getSubject(true, ++this.page);
    },

    methods: {
      // async getStudyList(isPage) {
      // 	let that = this;
      // 	// that.$refs.loadingPopup.open();
      // 		let res  = await that.$httpUser.get(
      // 				`deliver/app/teacher/getTeacherPlanStudyAlreadyStudyList?date=''&pageNum=${that.page}&pageSize=${that.pageSize}&searchType=0&studentName=''`)

      // },
      // async getSubject(){
      // 	 let res = await this.$httpUser.get("deliver/app/parent/getCourse");
      //   // houseList({type:this.cur},this.size,this.page).then(res=>{
      //   //   this.dataList.push(...res.data.rows)
      //   //   this.total = res.data.total
      //   // })
      // },
      tabsClick(item) {
        this.courseType = item.courseType;
        this.getSubject();
      },
      // 时间判断
      checkNextTime(time) {
        if (time != undefined) {
          let datetime = time;
          let nowTime = Date.now();
          let setTime = dayjs(datetime).unix() * 1000;
          return nowTime < setTime;
        }
      },
      // 我的课程列表
      async getSubject(isPage, page) {
        let _this = this;
        const res = await $http({
          url: 'zx/wap/course/user/list',
          data: {
            pageSize: 20,
            pageNum: page || 1,
            userId: uni.getStorageSync('user_id') ? uni.getStorageSync('user_id') : '',
            courseType: this.courseType
          }
        });
        if (res) {
          _this.infoLists = res.data;
          if (isPage) {
            let old = _this.classList;
            _this.classList = [...old, ...res.data.data];
          } else {
            _this.classList = res.data.data;
          }
        }
      },
      // 查看上次反馈
      goLastback() {
        uni.navigateTo({
          url: '/Coursedetails/feedback/index?data=' + JSON.stringify(this.myClassList.lastStudyCourse)
        });
      },
      skintap(url) {
        $navigationTo(url);
      },
      // 查看下次反馈
      goNextback() {
        uni.navigateTo({
          url: '/Coursedetails/feedback/index?data=' + JSON.stringify(this.myClassList.nextStudyCourse)
        });
      },

      // 下次课程请假跳转
      gotoNextleave() {
        uni.navigateTo({
          url: '/Coursedetails/leave/leave?data=' + JSON.stringify(this.myClassList.nextStudyCourse)
        });
      },
      // 去学习
      gostudy(item) {
        uni.setStorageSync('studentName', item.studentName);
        uni.navigateTo({
          url: '/Coursedetails/study/courseDetail?courseId=' + item.courseId + '&studentCode=' + item.studentCode
        });
      }
    }
  };
</script>

<style lang="scss" scoped>
  /deep/.u-tabs__wrapper__nav__item__text {
    display: inline-block;
    height: 50rpx;
  }
  .myClassList {
    height: 100upx;
    // border-bottom:1upx dashed #EEEEEE;
    box-sizing: border-box;
    line-height: 100upx;
  }
  .swiper-item {
    padding: 32rpx 24rpx;
    margin-top: 32rpx;
    .title_name_css {
      font-size: 30rpx;
      color: #333333;
      // overflow: hidden;
      // text-overflow: ellipsis;  /* 超出部分省略号 */
      // word-break: break-all;  /* break-all(允许在单词内换行。) */
      // display: -webkit-box; /* 对象作为伸缩盒子模型显示 */
      // -webkit-box-orient: vertical; /* 设置或检索伸缩盒对象的子元素的排列方式 */
      // -webkit-line-clamp: 2; /* 显示的行数 */
    }
    .title_css {
      color: #555555;
      margin-top: 22rpx;
      font-size: 30rpx;
      .right_css {
        display: inline-block;
        margin-left: 54rpx;
      }
    }
    .bottom_css {
      border-top: 2rpx solid #f6f7f9;
      text-align: right;
      .btn_left {
        margin-right: 20rpx;
      }
    }
  }
  .flex_s {
    display: flex;
  }

  .border-t {
    border-top: 1px dashed #eee;
  }

  .btn {
    width: 200rpx;
    height: 60rpx;
    line-height: 60rpx;
    box-sizing: border-box;
    border-radius: 30upx;
    font-size: 24upx;
    display: inline-block;
  }
  .btn_plan {
    width: 200rpx;
    color: #4e9f87 !important;
    border-color: #7baea0 !important;
    line-height: 59rpx;
  }
  .btn_orange {
    color: #fff !important;
    border: none !important;
    background: linear-gradient(to bottom, #88cfba, #1d755c);
  }

  .uni-bg-red {
    position: relative;
  }

  .shike-img {
    position: absolute;
    width: 45rpx;
    top: 30rpx;
    right: 140rpx;
  }
</style>
