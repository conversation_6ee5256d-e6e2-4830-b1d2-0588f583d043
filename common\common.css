/* 背景颜色 */
body.pages-purchasedetails-purchasedetails uni-page-body {
  background-color: #e6e6e6 !important;
}
/* 支付成功页面设置高度 */
body.pages-purchasesucc-purchasesucc uni-page-body > uni-view {
  height: 100%;
}

.income .uni-scroll-view,
.income .uni-scroll-view-content {
  display: flex;
  justify-content: space-between;
}
/* css画倒三角 */
.nabla {
  width: 0;
  height: 0;
  border-right: 12rpx solid transparent;
  border-left: 12rpx solid transparent;
  border-top: 16rpx solid #d8d8d8;
}

/* 单行多余溢出 */
.overstepSingle {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* flex布局 */
.flexbox {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* 单选框颜色 */
uni-radio .uni-radio-input.uni-radio-input-checked {
  background: #0fcb7f !important;
  border-color: #0fcb7f !important;
}
uni-radio .uni-radio-input {
  background: #cdcdcd;
}
uni-radio .uni-radio-input:before {
  font: normal normal normal 14px/1 uni;
  content: '\EA08';
  color: #fff;
  font-size: 18px;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -48%) scale(0.73);
}

/* 定位 */
.positionRelative {
  position: relative;
}
.positionAbsolute {
  position: absolute;
}

/* 自定义卡片样式 */
.userCard {
  width: 690upx;
  padding: 30rpx 30rpx 10rpx 30rpx;
  box-shadow: 0upx 10upx 60upx #e3e3e3;
  margin-top: 30rpx;
  box-sizing: border-box;
  border-radius: 20rpx;
  background-color: #ffffff;
}

/* marging 设置水平居中 */
.marginRCenter {
  margin: 0 auto;
}

/* 单选框大小 */
uni-radio .uni-radio-input,
radio .uni-radio-input {
  width: 32upx;
  height: 32upx;
}
uni-radio .uni-radio-input.uni-radio-input-checked:before,
radio .uni-radio-input.uni-radio-input-checked:before {
  font-size: 12px;
}

/* 字体大小 */
.font12 {
  font-size: 24upx !important;
}
.font14 {
  font-size: 28upx;
}
.font16 {
  font-size: 32upx;
}
.font18 {
  font-size: 36upx;
}
.font20 {
  font-size: 40upx;
}
.font24 {
  font-size: 48upx;
}
.font26 {
  font-size: 52upx;
}

.fontWeight {
  font-weight: bold;
}

/* 字体颜色 */
.color_black {
  color: #000000;
}
.color_red {
  color: #fa411a;
}
.color_tangerine {
  color: #fa370e;
}
.color_grey66 {
  color: #666666;
}
.color_grey {
  color: #999999;
}
.color_grey97 {
  color: #979797;
}
.color_grey98 {
  color: #98a9b2;
}
.color_white {
  color: white;
}
.color_black30 {
  color: #303133;
}
.color_blue {
  color: #0398ef;
}
.color_green {
  color: #2dc032;
}
.color_orange {
  color: #eb7e2d;
}
.color_yellow {
  color: #f8b342;
}

/* 列表点击的时候不变色 */
/deep/ .uni-list-item--hover {
  background: none;
}

/* 标题样式 */
.usercontent {
  width: 660upx;
  padding: 60upx 45upx 45upx 45upx;
  box-shadow: 0upx 30upx 60upx #e3e3e3;
}
.infomation_title {
  display: flex;
  margin-bottom: 36upx;
}
/* .infomation_title>view { display: inline-flex; } */
.columnbg_green {
  width: 8upx;
  height: 70upx;
  background: #01c176;
}
.columnbg_green1 {
  width: 8upx;
  height: 36upx;
  background: #01c176;
}
.titletext,
.titletext1 {
  font-size: 34upx;
  font-weight: bold;
  width: 100%;
  line-height: 36upx;
  margin: -6upx 0 0 10upx;
  line-height: 48upx;
}
.titletext1 {
  font-size: 36upx;
}
.titletext > view {
  display: block;
  line-height: 36upx;
  font-size: 24upx;
  font-weight: normal;
  color: #666666;
}

.rowbg_green {
  width: 33upx;
  height: 8upx;
  background: #01c176;
  justify-content: right;
  margin-top: 31upx;
}
.rowbg_green1 {
  width: 33upx;
  height: 8upx;
  background: #01c176;
  justify-content: right;
  margin-top: 14upx;
}

/* uniswiper */
.tabs {
  /* flex: 1;
		flex-direction: column;
		overflow-x: hidden;
		background-color: #ffffff; */
  /* #ifdef MP-ALIPAY || MP-BAIDU */
  /* height: 100vh; */
  /* #endif */
}
uni-swiper,
.swiper-box {
  display: block;
  height: 150px;
  min-height: 100vh;
}
.swiper-item {
  overflow-y: auto;
}
.scroll-h {
  width: 750upx;
  height: 80upx;
  flex-direction: row;
  /* #ifndef APP-PLUS */
  white-space: nowrap;
  /* #endif */
  display: flex;
  justify-content: space-between;
}
.line-h {
  height: 1upx;
  background-color: #cccccc;
}
.income {
  width: 100%;
  display: flex;
  justify-content: space-between;
}
.uni-tab-item {
  /* #ifndef APP-PLUS */
  display: inline-block;
  /* #endif */
  flex-wrap: nowrap;
  padding-left: 34upx;
  padding-right: 34upx;
}
.uni-tab-item-title {
  color: #555;
  font-size: 30upx;
  height: 80upx;
  line-height: 80upx;
  flex-wrap: nowrap;
  /* #ifndef APP-PLUS */
  white-space: nowrap;
  /* #endif */
}
.uni-tab-item-title-active {
  color: #01bf75;
}

/* 栅格布局 */
.gridlist {
  width: 100%;
  padding-bottom: 20upx;
}
.gridlist-item {
  width: 50%;
  display: inline-block;
  font-size: 36upx;
  margin: 15upx 0;
}
.gridlist-img {
  width: 90upx;
  height: 90upx;
  display: block;
  border-radius: 50%;
  margin-bottom: 15upx;
}
.gridlist-img image {
  width: 60upx;
  height: 60upx;
  margin: 15upx;
}
.gridlist-text {
  color: #666666;
  display: block;
  width: 80%;
  line-height: 36upx;
}

.userlist {
  width: 100%;
  display: inline-flex;
  border-top: 2upx solid #01c176;
  padding: 22upx 14upx;
  box-sizing: border-box;
}
.userlist > image {
  width: 60upx;
  height: 60upx;
  margin-right: 8upx;
}
.userlist view {
  line-height: 60upx !important;
}
.userlist text {
  color: #000000;
}
.lineTitle {
  margin-top: 20upx;
}
.lineTitle > view {
  font-size: 44upx;
  margin-bottom: 30upx;
  line-height: 50upx;
  background-size: 100% auto !important;
  text-align: center;
  color: #01c176;
}
.lineTitle > image {
  height: 236upx;
  width: 100%;
}

/* 固定底部——立即试课 */
.fixed_bottom {
  width: 100%;
  position: fixed;
  bottom: 0upx;
  background: #f5fbfe;
  display: flex;
  z-index: 3;
  padding: 40upx 20upx;
  box-sizing: border-box;
  box-shadow: 0px -15upx 30upx -7px #e3e3e3;
}
.fixed_bottom > text {
  display: block;
  width: 4upx;
  height: 80upx;
  background: #b0b4b6;
  margin: 5upx 30upx;
}
.cusService {
  text-align: center;
  width: 130upx;
}
.cusService > image {
  width: 40upx;
  height: 40upx;
}
.cusService > text {
  display: block;
  color: #666666;
}
.trialclassBtn,
.btngroup {
  display: inline-block;
  width: 520upx;
  text-align: center;
  height: 90upx;
  line-height: 90upx;
  overflow: hidden;
  background: #21d488;
  border-radius: 40upx;
  color: #ffffff;
  margin-top: 0upx;
}
.trialclassBtn.disabled {
  background: #acacac;
}

/* 拼团参团开团 */
.btngroup .trialclassBtn {
  width: 50%;
  border-radius: 0;
}
.btngroup .trialclassBtn1 {
  width: 100% !important;
}

/* 卡片没有margin */
/* .uni-card { margin: 0 0 60upx 0!important;position: relative; } */
.cardmaintitle {
  line-height: 70upx;
  color: #000000;
}
.cardsubtitle {
  color: #616161;
  line-height: 66upx;
}
.cardgive {
  color: #34cd91;
  padding: 16upx 16upx;
  border-radius: 10upx;
  background: #e3f8f1;
  margin: 15upx 0 25upx 0;
  line-height: 34upx;
}
.cardgive > text {
  display: block;
  line-height: 36upx;
}
.cardbtn {
  display: inline-block;
  background: #01c175;
  border-radius: 10upx;
  color: #ffffff;
  padding: 0 10upx;
  height: 36rpx;
  line-height: 36upx;
}
.card-countdown {
  color: #34cd91;
  margin-left: 20upx;
}
.cardprice {
  font-weight: bold;
  display: inline-block;
  color: #34cd91;
}
.flexcontent {
  display: flex;
  justify-content: space-between;
  line-height: 88upx;
  align-items: center;
}
.crossedprice {
  text-decoration: line-through;
  color: #b3b3b3;
  font-weight: normal;
  margin-left: 16rpx;
}
.cardsell,
.sellandremainder {
  color: #b3b3b3;
  line-height: 88upx;
  text-align: right;
}
.sellandremainder span {
  display: inline-block;
  width: 310upx;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 26upx;
}

.cardunivalent {
  margin: 0 30upx;
}
.assemble {
  color: #616161;
  line-height: 70upx;
}
.card-classhours {
  margin-right: 50upx;
}

.cardflag {
  width: 35upx;
  height: 70upx;
  padding-top: 8upx;
  position: relative;
  background: #f05050;
  color: #ffffff;
  position: absolute;
  right: 28upx;
  top: 0;
  line-height: 26upx;
}

.cardflag:after {
  content: '';
  position: absolute;
  left: 0;
  bottom: 0;
  border-bottom: 14upx solid #fff;
  border-left: 18upx solid transparent;
  border-right: 16upx solid transparent;
}

/* 倒计时 */
.uni-countdown {
  display: inline-flex !important;
}
.uni-countdown__splitor {
  line-height: 20rpx !important;
}
.uni-countdown__number {
  width: auto !important;
  margin: 4upx 0 !important;
  height: auto !important;
  line-height: 20rpx !important;
}

/* 按钮 */
.fillButton {
  width: 100%;
  height: 100%;
  z-index: 1;
}
.activebtnbox {
  width: 100%;
  text-align: center;
  position: fixed;
  bottom: 70upx;
}
.activebtn {
  display: inline-block;
  padding: 0 185upx;
  border: none;
  color: #ffffff;
  background: #14cc82;
  border-radius: 40upx;
  font-size: 36upx;
  height: 86upx;
  line-height: 86upx;
}

/* 底部弹窗 */
.popupBox {
  background: #ffffff;
  position: relative;
  width: 100%;
  padding: 30rpx;
  box-sizing: border-box;
  border-top-right-radius: 20rpx;
  border-top-left-radius: 20rpx;
  max-height: 80vh;
  overflow-y: auto;
}
.popupBox .close {
  display: block;
  width: 30rpx;
  height: 30rpx;
  position: fixed;
  right: 40rpx;
  z-index: 2222;
  color: #ffffff;
  top: 30rpx;
}
.popupBoxSchool {
  height: 80vh;
}

.shopblock {
  display: flex;
  width: 100%;
  flex-wrap: wrap;
  border-bottom: 2rpx solid #e8e8e8;
  padding-bottom: 22rpx;
}
.shopRight {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  padding: 20rpx 0;
}
.shopRight > view {
  width: 100%;
  line-height: 52rpx;
}
.shopPrice {
  font-weight: bold;
  color: #fe4f10;
  letter-spacing: 2rpx;
}
.colorgrey {
  color: #9a9a9a;
}
.shopShowimg image {
  width: 200rpx;
  height: 200rpx;
  border-radius: 10rpx;
  margin-right: 30rpx;
}

.shopShowTitle {
  width: 100%;
  font-size: 28rpx;
  letter-spacing: 2rpx;
  margin-bottom: 20rpx;
  padding-top: 24rpx;
}
.displayflex {
  display: flex;
  align-items: center;
  justify-content: center;
}
.dis_reverse {
  display: flex;
  align-items: center;
  flex-direction: row-reverse;
}
.mr30 {
  margin-right: 30rpx;
}
.displayflexbetween {
  display: flex;
  justify-content: space-between;
}
.bgebebeb {
  background: #ebebeb;
  border: 2rpx solid #ebebeb;
  display: flex;
  line-height: 60rpx;
  margin-bottom: 10rpx;
  font-size: 24rpx;
  border-radius: 10rpx;
  margin-right: 24rpx;
  height: 60rpx;
}
.bgebebeb.active {
  background: #ffffff;
  border: 2rpx solid #ff8f47;
}
.classNum .bgebebeb image {
  width: 50rpx;
  height: 50rpx;
  border-radius: 5rpx;
  margin: 5rpx 14rpx 5rpx 5rpx;
}
.classNum .bgebebeb {
  width: 126rpx;
}
.classStyle .bgebebeb {
  padding: 0 24rpx;
}
.classAddress .shopShowTitle {
  display: flex;
  justify-content: space-between;
  padding-bottom: 22rpx;
  border-bottom: 2rpx solid #e8e8e8;
}
.classAddress .font14 {
  line-height: 46rpx;
  color: #333333;
  letter-spacing: 2rpx;
}

.popupBox .trialclassBtn {
  width: 86%;
  margin-left: 7%;
  margin-top: 80rpx;
  letter-spacing: 4rpx;
}
.popupBoxSchool .trialclassBtn {
  position: fixed;
  bottom: 60rpx;
  z-index: 1000;
  margin-left: 0;
  left: 7%;
}
.popupboxtitle {
  width: 100%;
  padding-bottom: 30rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 80rpx;
  border-bottom: 2rpx solid #e8e8e8;
  padding-right: 60rpx;
  box-sizing: border-box;
}
.popupboxtitle text {
  display: block;
}

/* 拼团中 */
.popupCenter {
  width: 650rpx;
  margin: 0 auto;
  position: relative;
  overflow: hidden;
  padding: 0 30rpx 30rpx 30rpx;
  box-sizing: border-box;
  background: #ffffff;
}
.popupCenter .close {
  position: fixed;
  right: 30rpx;
  margin-top: -20rpx;
  background: rgba(0, 0, 0, 0.6);
  border-radius: 50%;
  width: 38rpx;
  height: 38rpx;
  line-height: 38rpx;
  text-align: center;
}

.fewpeople {
  width: 100%;
  margin: 0 auto;
  text-align: center;
  padding-top: 30rpx;
}

.distanceends {
  line-height: 70upx;
  color: #999999;
  letter-spacing: 2rpx;
}
.distanceends text {
  color: #00c073;
}
.assemblepeoplelistbox {
  width: 90%;
  margin: 20rpx auto;
  display: flex;
  flex-wrap: wrap;
}
.assemblepeoplelist {
  width: 90upx;
  height: 90upx;
  position: relative;
  border: 2upx #02c577 dashed;
  border-radius: 50%;
}
.assemblepeoplemargin {
  margin-left: 40rpx;
}
.assemblepeoplelist image {
  width: 90upx;
  height: 90upx;
  border-radius: 50%;
}
.assemblepeoplelist > text {
  color: #ffffff;
  position: absolute;
  width: 60rpx;
  padding: 2upx 8upx;
  background: #01c175;
  border-radius: 18upx;
  bottom: -10upx;
  left: 6upx;
}
.assemblequestion {
  width: 100%;
  text-align: center;
  line-height: 90upx;
  font-size: 32rpx;
  background: #f3f3f3;
  border-radius: 50%;
  color: #93797a;
}
.popupCenter .trialclassBtn {
  width: 60%;
  margin: 30rpx 20%;
  height: 80rpx;
  line-height: 80rpx;
}

/* 拼团成功或者开团成功弹窗 */
.popupSuccess {
  height: 670rpx;
  text-align: center;
  background: url(https://document.dxznjy.com/applet/popupWindowBg.png) 100% 100% no-repeat;
  background-size: 100% 100%;
}
.popTitle {
  width: 100%;
  margin-top: 62rpx;
  color: #ffffff;
  font-size: 40rpx;
  letter-spacing: 2rpx;
  height: 120rpx;
}
.popTitle text {
  display: block;
  width: 100%;
  font-size: 30rpx;
  line-height: 72rpx;
}
.successIcon image {
  width: 300rpx;
  height: 200rpx;
  margin-top: 110rpx;
}
.successBtn {
  width: 100%;
  margin-top: 60rpx;
  display: flex;
  justify-content: space-between;
}
.successBtn text,
.successBtn button {
  display: inline-block;
  padding: 0 60rpx;
  height: 70rpx;
  font-size: 28rpx;
  letter-spacing: 5rpx;
  border-radius: 30rpx;
  background: #01e6c2;
  color: #ffffff;
  line-height: 70rpx;
  margin: 0 auto;
}

/* 确认订单 */
.bgwhite {
  background: #ffffff;
}
.shopListTop {
  padding: 20rpx;
  display: flex;
  background: #ffffff;
  flex-wrap: wrap;
}
.shopListTop .shopImg {
  width: 140rpx;
  height: 140rpx;
  background: #d4d4d2;
  border-radius: 8rpx;
  margin-right: 32rpx;
}
.shopListTop .shopListRight {
  width: 538rpx;
  color: #878787;
  font-size: 24rpx;
  line-height: 50rpx;
}
.shopListTop .shopListRight > view {
  width: 100%;
}
.shopAllprice {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  border-top: 2rpx solid #f0f0f0;
  padding: 20rpx 20rpx 30rpx 20rpx;
  color: #878787;
}
.shopAllprice .redtext {
  color: #fe0000;
}
.listitemview {
  padding: 20rpx;
  line-height: 50rpx !important;
}

/* 拼团叠图 */
.groupomimgList {
  position: relative;
}
.groupomimgList .assemblepeoplelist {
  width: 80rpx;
  height: 80rpx;
  position: absolute;
}
.groupomimgList .assemblequestion {
  height: 80rpx;
  line-height: 80rpx;
}
.groupomimgList .assemblepeoplelist image {
  width: 100%;
  height: 100%;
}
.groupomimgList :nth-child(2) {
  z-index: 2;
  left: 70rpx;
}
.groupomimgList :nth-child(3) {
  z-index: 3;
  left: 140rpx;
}

/* 提现 */
/* 可提现 */
.incomeBanner_box {
  width: 100%;
  height: 490upx;
  background: url('https://document.dxznjy.com/applet/halo.png');
  background-size: 100% 100%;
  position: relative;
  text-align: center;
}
.incomeBanner {
  padding-top: 145upx;
  color: #ffffff;
  font-size: 28upx;
}
.incomeBanner text {
  display: block;
  font-size: 90upx;
  font-weight: bold;
  letter-spacing: 3upx;
  margin-top: 20upx;
}
.nowIncome {
  display: inline-block;
  position: absolute;
  width: 372rpx;
  top: 450rpx;
  left: 189rpx;
  color: #ffffff;
  font-size: 32upx;
  background: #25d78b;
  height: 80upx;
  line-height: 80upx;
  border: 2upx solid #00ffae;
  border-radius: 40upx;
}
.withdrawrecord {
  position: absolute;
  top: 30rpx;
  right: 30rpx;
  padding: 10rpx 16rpx;
  border: 2rpx solid #0bfaa7;
  border-radius: 30rpx;
  color: #ffffff;
  font-size: 24rpx;
}

/* 提现列表 */
.incomeListbox {
  margin-top: 60upx;
}
.incomecontent {
  padding-left: 10upx;
  width: 100%;
  box-sizing: border-box;
  min-height: 200rpx;
}
.incomeList {
  height: 82upx;
  line-height: 82upx;
  border-bottom: 2upx solid #e9e9e9;
}
.incomeList text {
  width: 33%;
  display: inline-block;
  font-size: 24upx;
  text-align: center;
}

/* 提示 */
.tipbox {
  font-size: 24upx;
  padding: 66upx 56upx 30upx 56upx;
  color: #fd0000;
}
.tipbox text {
  display: block;
  color: #999999;
  line-height: 30upx;
  margin-top: 16upx;
}

/* 可分润 */
.waitShareList {
  padding: 30upx 50upx;
  margin-top: 30upx;
  border-bottom: 2upx solid #e9e9e9;
  display: flex;
  justify-content: space-between;
}
.waitShareList image {
  width: 34upx;
  height: 34upx;
  margin-right: 24upx;
  margin-top: 0upx;
}
.waitshareinfo {
  font-size: 32upx;
}
.waitshareinfo text {
  display: block;
  font-size: 24upx;
  color: #999999;
  line-height: 30upx;
  margin-top: 10upx;
  margin-left: 58upx;
}
.waitprice {
  color: #fe0000 !important;
}

/* 总收益 */

.incomeall_box {
  width: 100%;
  height: 235upx;
  padding-top: 10upx;
  background: #f8f8f8;
  position: relative;
  text-align: center;
}
.incomeall {
  height: 100%;
  font-size: 24upx;
  color: #ffffff;
  background: url('https://document.dxznjy.com/applet/allincomebg.png');
  background-size: 100% 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;
}
.incomeall text {
  display: block;
  width: 100%;
  font-size: 42upx;
  letter-spacing: 3upx;
  margin-top: 10upx;
}

.gogroupBtn {
  font-size: 28rpx;
  width: 120rpx;
  color: #ffffff;
  background: #02c577;
  text-align: center;
  box-sizing: border-box;
  line-height: 45rpx;
  border-radius: 24rpx;
  padding: 0;
  margin-left: 16rpx;
}

.checkeduser {
  display: inline-block;
  border: 2rpx solid #0ac679;
  border-radius: 6rpx;
  width: 30rpx;
  height: 30rpx;
  line-height: 32rpx;
  float: left;
  margin-right: 10rpx;
}

/* 鼎加盟之后的弹窗 */

.djm_popupCenter {
  width: 580rpx;
  padding: 20rpx;
  background: #27d78c;
  border-radius: 30rpx;
}
.djm_inner {
  width: 520rpx;
  background: #ffffff;
  border-radius: 20rpx;
  text-align: center;
  padding: 30rpx;
}
.popup_djmTitle {
  font-size: 40rpx;
  font-weight: bold;
  padding-top: 50rpx;
  margin-bottom: 50rpx;
}
.djm_inner .input_boxList {
  width: 100%;
  line-height: 100rpx;
  position: relative;
  border-bottom: 1rpx solid #dddddd;
}
.djm_inner .input_boxList > text,
.djm_inner .input_boxList > input {
  display: inline-block;
}
.djm_inner .input_boxList > text {
  width: 120rpx;
  height: 100rpx;
  font-size: 28rpx;
  text-align: justify;
  vertical-align: top;
}
.djm_inner .input_boxList > text::after {
  display: inline-block;
  width: 100%;
  content: '';
  height: 0;
}
.djm_inner .input_boxList input {
  text-align: left;
  overflow: initial;
  font-size: 28rpx;
  height: 60rpx;
  line-height: 60rpx;
}
.djm_inner .get-yzm {
  position: absolute;
  right: 0;
  top: 30rpx;
  width: 130rpx;
  height: 50rpx;
  line-height: 50rpx;
  background: #01c176;
  color: #ffffff;
  font-size: 24rpx !important;
  border: none;
  border-radius: 30rpx;
  z-index: 99;
  padding: 0rpx 16rpx;
}

.djm_inner .btn-djm {
  width: 200upx;
  margin: 60rpx auto 50rpx auto;
  /* background: #1dd187; */
  background-image: linear-gradient(to right, #27d78c, #01c176);
  color: #fff;
  font-size: 30rpx;
  border-radius: 50rpx;
  text-align: center;
  height: 72rpx;
  line-height: 72rpx;
}
.err_message {
  font-size: 24rpx;
  color: #f00d07;
  text-align: left;
  padding: 0 60rpx;
}

.djm_popBtnSing {
  justify-content: center !important;
}
.djm_popBtnGrop {
  width: 60%;
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  margin-bottom: 100rpx;
}
.djm_popBtnGrop > text {
  background-image: linear-gradient(to right, #27d78c, #01c176);
  text-align: center;
  padding: 8rpx 30rpx;
  color: #ffffff;
  border-radius: 20rpx;
  font-size: 24rpx;
}
.input_boxList picker {
  letter-spacing: 2rpx;
  padding-left: 30rpx;
  font-size: 24rpx;
}
.pickerFont,
.djm_inputIcon {
  color: #e6e6e6 !important;
}
.djm_inputIcon {
  position: absolute;
  right: 20rpx;
  top: 10rpx;
  width: 30rpx !important;
  height: 30rpx !important;
}

.popup_input {
  font-size: 24rpx;
  border-bottom: 1px solid #dddddd;
  height: 50rpx;
  margin: 20rpx auto 80rpx auto;
}
.popup_input input {
  font-size: 24rpx;
  letter-spacing: 3rpx;
}

/* 绿色带返回剪头自定义头部 */
.djm_userHead {
  position: relative;
  line-height: 120rpx;
  color: #ffffff;
  background-image: linear-gradient(to right, #27d78c, #01c176);
  text-align: center;
  height: 120rpx;
}
.djm_userHead image {
  position: absolute;
  width: 36rpx;
  left: 30rpx;
  top: 50rpx;
}

.djmList1 {
  padding: 30rpx 50rpx;
}
.djmList1_title {
  font-size: 32rpx;
  line-height: 50rpx;
  letter-spacing: 2rpx;
  margin-bottom: 16rpx;
  border-bottom: 1rpx solid #dcdddd;
}
.djm_line_grey {
  width: 100%;
  height: 6rpx;
  background-color: #e9e8ee;
}
.djmList1 .uni-list-item {
  padding: 0 !important;
}
.djmList1 .uni-list-item__container {
  padding: 15rpx 0 !important;
}
.djmList1 .uni-list-item__container::after {
  height: 0 !important;
}
.djmList1 .uni-list-item__content .uni-list-item__content-note {
  display: none;
}

.djm_userbtn {
  background-image: linear-gradient(to right, #27d78c, #01c176);
  color: #ffffff !important;
  font-size: 30rpx;
  border-radius: 40rpx;
  text-align: center;
  height: 72rpx;
  line-height: 72rpx;
}

/* 自定义头部 */
.userHead {
  height: 80rpx;
  position: relative;
  line-height: 80rpx;
  text-align: center;
  color: #ffffff;
  padding-top: 40rpx;
  background: #01c176;
  font-size: 30rpx;
  font-weight: bold;
}
.userHead .iconBack {
  position: absolute;
  left: 20rpx;
  top: 40rpx;
}

/* 趣味复习弹窗公共样式 */
.interesting_popupCenter {
  width: 560rpx;
  height: 680rpx;
  position: relative;
  text-align: center;
  background: url('https://document.dxznjy.com/applet/interesting/dialog_bg.png') top no-repeat;
  background-size: 100% 560rpx;
}
.interesting_popupCenter1 {
  position: relative;
  background: url('https://document.dxznjy.com/applet/interesting/dialog_interesting_review.png') top no-repeat;
  background-size: 100% 560rpx;
}
.interesting_popupCenter.chooseWordList_pop {
  height: 980rpx;
  background-size: 100% 860rpx;
}

.interet_head {
  margin: 0 auto;
  position: relative;
}
.interet_popupTitle {
  width: 225rpx;
  height: 200rpx;
  margin-top: -18rpx;
  margin-bottom: 30rpx;
}
.interet_popupTitle.interet_popupTitle1 {
  width: 100%;
}
.interet_head > text {
  display: block;
  position: absolute;
  bottom: 58rpx;
  left: 225rpx;
  color: #ffffff;
  font-size: 30rpx;
}
.popup_content {
  height: 190rpx;
  padding: 0 30rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.popup_listContent {
  height: 520rpx;
  display: block;
}
.popup_listContent .chooseWordList {
  width: 100%;
  background: #ffffff;
  border: 1rpx solid #d4e6d4;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-left: 10rpx;
  box-sizing: border-box;
  margin-bottom: 20rpx;
  height: 80rpx;
  border-radius: 16rpx;
}
.popup_listContent .chooseWordList.active {
  border-color: #ed9177;
  background: #ffcc80;
}
.popup_listContent .chooseWordList.active .chooseWord_round {
  background: #ef5450;
}
.popup_listContent .chooseWordList text {
  display: inline-flex;
  font-size: 28rpx;
}
.chooseWord_round {
  background: #bdbdbd;
  font-size: 24rpx !important;
  padding: 4rpx 16rpx;
  color: #ffffff;
  border-top-left-radius: 14rpx;
  border-bottom-left-radius: 14rpx;
}

.popBtnGroup {
  display: flex;
  font-size: 30rpx;
  justify-content: center;
  padding: 0 50rpx;
  height: 90rpx;
  line-height: 90rpx;
  margin-top: 10rpx;
}
.popBtnGroupNowBuy {
  width: 292rpx;
  overflow-y: auto;
  color: #ffffff;
  background: url('https://document.dxznjy.com/applet/interesting/interestNow_buy.png') center center no-repeat;
  background-size: 100% 100%;
}
.popBtnGroup_list {
  width: 48%;
  margin: 0 1%;
  color: #ffffff;
}

.popupBottom {
  background: #ffffff;
  position: absolute;
  bottom: 120rpx;
  left: 0;
  width: 100%;
  padding-bottom: 30rpx;
  border-radius: 50rpx;
}

.popBtnGroup .popBtnGroup_list:first-child {
  background: url('https://document.dxznjy.com/applet/interesting/dialog_btn_left.png') center center no-repeat;
  background-size: 100% 100%;
}
.popBtnGroup .popBtnGroup_list:last-child {
  background: url('https://document.dxznjy.com/applet/interesting/dialog_btn_right.png') center center no-repeat;
  background-size: 100% 100%;
}
.popBtnGroup.popBtnGroup1 .popBtnGroup_list:first-child {
  background: url('https://document.dxznjy.com/applet/interesting/dialog_i_btn_left.png') center center no-repeat;
  background-size: 100% 100%;
}
.popBtnGroup.popBtnGroup1 .popBtnGroup_list:last-child {
  background: url('https://document.dxznjy.com/applet/interesting/dialog_i_btn_right.png') center center no-repeat;
  background-size: 100% 100%;
}

.reviewPopup_list {
  font-size: 26rpx;
  padding-top: 42rpx;
  box-sizing: border-box;
  border-radius: 16rpx;
  width: 152rpx;
  height: 175rpx;
  justify-content: center;
  align-items: center;
  border: 1rpx solid #411c0c;
  margin: 0 10rpx;
  color: #411c0c;
}
.reviewPopup_list .DesignFont {
  display: block;
  width: 100%;
  margin-top: 15rpx;
  font-size: 45rpx;
}
.reviewPopup_list .DesignFont.DesignFontBg {
  font-size: 50rpx;
  font-weight: bold;
}

.interesting_close {
  width: 80rpx;
  height: 80rpx;
  position: absolute;
  bottom: 0;
  left: 240rpx;
}

/* 学情报告 */
.turnListBox {
  width: 690rpx;
  margin: 30rpx;
  padding-bottom: 50rpx;
}
.turnList {
  width: 100%;
  padding: 30rpx;
  box-sizing: border-box;
  line-height: 40rpx;
  background: #ffffff;
  border-radius: 16rpx;
  box-shadow: 0rpx 0rpx 10rpx #dddddd;
  margin-bottom: 15rpx;
}
.turnlist_title {
  width: 100%;
}
.greenBlock {
  display: inline-block;
  width: 18rpx;
  height: 18rpx;
  background: #18b48e;
  border: 2rpx solid #9fe481;
}
.turnTitle_text {
  margin-left: 16rpx;
  font-size: 32rpx;
}
.three_border {
  display: inline-block;
  height: 40rpx;
  padding: 0 6rpx;
  background: #77d29b;
  color: #ffffff;
  text-align: center;
  line-height: 40rpx;
  border-radius: 8rpx;
  border-bottom-left-radius: 0;
  font-size: 32rpx;
  margin-left: 6rpx;
}
.turnlist {
  font-size: 24rpx;
  color: #bcbdbd;
  float: right;
}

.chart_round {
  position: relative;
  padding: 54rpx 34rpx 24rpx 34rpx;
  box-sizing: border-box;
}
.roundChart_list {
  display: inline-block;
  width: 256rpx;
  height: 256rpx;
  margin: 0 12rpx;
  background: url('https://document.dxznjy.com/applet/interesting/circular_line1.png') center no-repeat;
  background-size: 100% 100%;
}
.roundChart_list.roundChart_list1 {
  background: url('https://document.dxznjy.com/applet/interesting/circular_line2.png') center no-repeat;
  background-size: 100% 100%;
}
.roundProgress {
  width: 150rpx;
  height: 150rpx;
  position: relative;
  margin: 53rpx;
}

.roundProgress .circleTrans {
  transform: rotate(196deg);
  border-radius: 50%;
  transform-origin: center;
}

.circleBox_center {
  width: 118rpx;
  height: 118rpx;
  font-size: 28rpx;
  color: #ffffff;
  text-align: center;
  background: linear-gradient(-30deg, #c0edaa, #7bcea8);
  position: absolute;
  top: 16rpx;
  left: 16rpx;
  border-radius: 50%;
}
.roundChart_list.roundChart_list1 .circleBox_center {
  background: linear-gradient(-30deg, #9192f0, #ca7ee0);
  line-height: 118rpx;
}
.circleBox_center_progress {
  display: inline-block;
  font-size: 48rpx;
  padding-top: 28rpx;
  line-height: 10rpx;
}
.circleBox_center .circleBox_center_name {
  display: inline-block;
  margin-left: -32rpx;
  width: 170rpx;
  text-align: center;
  font-size: 24rpx;
  transform: scale(0.6, 0.6);
}

.turnChart_content1 {
  margin-top: 45rpx;
}

.progressLine_box {
  width: 234rpx;
  height: 170rpx;
  position: absolute;
  top: 40rpx;
  left: 256rpx;
}
.progressLine_t1 {
  width: 200rpx;
  height: 60rpx;
  position: relative;
  font-size: 24rpx;
}
.progressLine_t1 text {
  display: block;
  position: absolute;
  height: 30rpx;
  right: 0;
  top: 0;
}
.progressLine_img {
  width: 140rpx;
  height: 16rpx;
  position: absolute;
  bottom: 10rpx;
  left: 0;
}
.progressLine_t2 {
  margin: 60rpx 0 0 48rpx;
}

.turnGrad_list {
  margin-top: 35rpx;
  height: 90rpx;
}
.turnGradList_title {
  display: inline-block;
  float: left;
  margin: 36rpx 26rpx 36rpx 20rpx;
  width: 28rpx;
  height: 28rpx;
  border-radius: 50%;
  background-color: #959595;
  font-size: 24rpx;
  text-align: center;
  line-height: 28rpx;
  color: #ffffff;
}
.turnGrad_list .turnGradList_img {
  width: 74rpx;
  height: 74rpx;
  margin: 8rpx 20rpx 8rpx 0;
  float: left;
}
.turnGrad_list .turnGradList_img1 {
  height: 84rpx !important;
}

.turnGradList_text {
  width: 374rpx;
  display: inline-block;
  float: left;
  margin-top: 24rpx;
}
.turnGradList_text .turnGradList_textlis {
  display: inline-block;
  width: 118rpx;
  height: 32rpx;
  font-size: 18rpx;
  margin: 0 2rpx;
  border-radius: 18rpx;
}
.turnGradList_text .turnGradList_textlis.bg1 {
  background-color: #4bbf9f;
}
.turnGradList_text .turnGradList_textlis.bg2 {
  background-color: #ff5858;
}
.turnGradList_text .turnGradList_textlis.bg3 {
  background-color: #449179;
}

.turnGradList_text .turnGradList_textlis text {
  display: inline-block;
  width: 180rpx;
  height: 32rpx;
  line-height: 32rpx;
  float: left;
  color: #ffffff;
  font-size: 24rpx;
  transform: scale(0.6, 0.7);
  margin-left: -28rpx;
}

.lookDetail {
  width: 86rpx;
  padding-top: 10rpx;
  float: right;
  display: inline-block;
  box-sizing: border-box;
  height: 86rpx;
  background: #14a480;
  border-radius: 50%;
  color: #ffffff;
  font-size: 20rpx;
}
.lookDetail > text {
  display: block;
  width: 100rpx;
  margin-left: -8rpx;
  text-align: center;
  font-size: 20rpx;
  transform: scale(0.7, 0.7);
  line-height: 30rpx;
}

.lookDetail.default {
  align-items: center;
  display: flex;
  background: #959595;
}

.sunStyle {
  /*去掉下划线*/
  position: absolute;
  left: 146rpx;
  top: -60rpx;
  transform: translate(-50%, -50%);
  /* 设置字体大小 */
  font-size: 26rpx;
  background: #20b879;
  /* background: linear-gradient(90deg,#03a9f4,#f441a5,#ffeb3b,#03a9f4);
    background-size: 400%; */
  width: 248rpx;
  height: 52rpx;
  line-height: 52rpx;
  text-align: center;
  color: #fff;
  /* 字母变大写 */
  text-transform: uppercase;
  /* 设置成胶囊状 */
  border-radius: 50rpx;
  z-index: 1;
  letter-spacing: 2rpx;
}
/* 设置发光 */
/* .sunStyle:before{
    content: "";
    position: absolute;
    left: -6rpx;
    right: -6rpx;
    top: -6rpx;
    bottom: -6rpx;
    background: linear-gradient(90deg,#03a9f4,#f441a5,#ffeb3b,#03a9f4);
    background-size: 400%;
    border-radius: 50rpx;
    filter: blur(20px);
    z-index: -1;
}


.sunStyle:hover{
    animation: sun 8s infinite;
} */
/* 设置流光 */
@keyframes sun {
  100% {
    background-position: -400% 0;
  }
}

/* 书写单词弹窗 */
.writeWord_box {
  width: 100%;
  height: 100%;
  position: absolute;
  z-index: 99;
  top: 0;
  left: 0;
  z-index: 99;
  background: rgba(255, 255, 255, 0.3);
}
.writeWord_box::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
  background: rgba(255, 255, 255, 0.09);
  backdrop-filter: blur(20rpx);
  background-size: cover;
}

.writeWord {
  width: 100%;
  height: 100%;
}

.writeWord_dialog_box {
  width: 100%;
  height: 100%;
}
.word_write {
  width: 580rpx;
  height: auto;
  text-align: center;
  margin: 0 auto;
  padding-top: 236rpx;
  box-sizing: border-box;
}
.write_input {
  width: 100%;
  margin-bottom: 5rpx;
  text-align: center;
  height: 90rpx;
  line-height: 90rpx;
  color: #030303;
  font-family: 'syhtR';
  font-size: 60rpx;
}
.wronword {
  font-size: 60rpx;
  font-family: 'syheR';
  color: #141414;
}
.rightIcon {
  width: 122rpx;
  height: 122rpx;
}
.showTip {
  width: 580rpx;
  padding: 26rpx 30rpx;
  margin: 30rpx auto 0 auto;
  text-align: center;
  box-sizing: border-box;
  border-radius: 14rpx;
  background: #eaeaea;
  font-family: 'syhtR';
  color: #000000;
  font-size: 26rpx;
}
.wordTranbox text {
  width: 100%;
  letter-spacing: 2rpx;
  line-height: 38rpx;
  display: inline-block;
  white-space: pre-wrap;
  word-wrap: break-word;
  height: auto;
}

.write_word_btnGrop {
  width: 100%;
  height: 76rpx;
  display: flex;
  margin-top: 138rpx;
  justify-content: space-around;
}
.write_btn {
  width: 76rpx;
  height: 76rpx;
  display: inline-flex;
}
.write_btn image {
  width: 100%;
  height: 100%;
}

.reviewBox {
  width: 100%;
  height: 100%;
  background: linear-gradient(178deg, #072021 0%, #3a8c59 50%, #508846 100%);
  position: relative;
  overflow: hidden;
}
.reviewUp_box {
  width: 658rpx;
  left: 45rpx;
  padding-bottom: 20rpx;
  background: white;
  margin-top: 44rpx;
  margin-bottom: 44rpx;
  box-shadow: 2px 4px 20px 1px rgba(0, 0, 0, 0.12);
  border-radius: 14rpx;
  position: relative;
}

.review_share {
  position: absolute;
  width: 50rpx;
  height: 50rpx;
  right: 14rpx;
  top: 14rpx;
}
.review_share image {
  width: 100%;
  height: 100%;
}
.review_rate {
  width: 586rpx;
  height: 570rpx;
  margin: 9rpx auto 60rpx auto;
  position: relative;
}
.rate_bgImage {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}
.rate_bgImageG {
  position: absolute;
  top: -20rpx;
  left: 0;
  width: 100%;
  /* height: 68%; */
}
.rate_num {
  font-family: 'syhtM';
  font-size: 60rpx;
  color: #2ab3d8;
  position: absolute;
  z-index: 1;
  width: 100%;
  text-align: center;
  top: 210rpx;
}
.rate_numG {
  font-family: 'syhtM';
  font-size: 40rpx;
  color: #2ab3d8;
  position: absolute;
  z-index: 1;
  width: 100%;
  text-align: center;
  top: 55.9vw;
  left: -158rpx;
}
.rate_num text {
  font-size: 36rpx;
}
.review_rate_text {
  font-family: 'syhtR';
  font-size: 24rpx;
  color: #999999;
}
.review_task {
  width: 100%;
  text-align: center;
  position: absolute;
  bottom: 100rpx;
  font-weight: bold;
  font-family: 'syhtM';
  font-size: 36rpx;
}
.review_taskG {
  width: 100%;
  text-align: center;
  position: absolute;
  top: 140rpx;
  font-family: 'syhtM';
  font-size: 28rpx;
}

.review_census {
  width: 606rpx;
  height: 111rpx;
  border-radius: 14rpx;
  background: rgba(153, 153, 153, 0.1);
  margin: 0 auto;
}
.review_census > view {
  width: 146rpx;
  text-align: center;
  float: left;
  text-align: center;
  padding-top: 8rpx;
}
/* .over_review_census>view{
	width: 201rpx;
	text-align: center;
	float: left;
	text-align: center;
	padding-top: 8rpx;
} */
.rightBorder {
  width: 1rpx !important;
  height: 60rpx;
  background-color: #dddddd;
  margin-top: 26rpx;
}
.review_census,
.over_review_census > view text {
  color: #999999;
  font-family: 'syhtR';
  font-size: 24rpx;
}
.review_litleTitle {
  font-family: 'syhtM';
  font-size: 36rpx;
}
.review_litleTitle > view {
  margin-left: 30rpx;
}
.review_color1 {
  background: #feefe4;
  opacity: 0.8;
}

.review_color2 {
  background: #dafce2;
  opacity: 0.8;
}

.review_color3 {
  background: #dbf4fa;
  opacity: 0.8;
}

.review_color4 {
  background: #e2e2fe;
  opacity: 0.8;
}

.review_color11 {
  background: #feefe4;
  border: 2rpx solid #e3af73;
  background: #fcf2e9;
  opacity: 0.5;
}

.review_color22 {
  background: #dafce2;
  border: 2rpx solid #80f0b8;
  background: rgba(230, 251, 236, 0.5);
  opacity: 0.5;
}

.review_color33 {
  background: #dbf4fa;
  border: 2rpx solid #bfc4f8;
  background: rgba(230, 232, 253, 0.5);
  opacity: 0.5;
}

.record_body {
  width: 100%;
  height: 1080rpx;
  overflow-y: auto;
  padding-bottom: 100rpx;
  box-sizing: border-box;
}

.reviewBottom {
  width: 100%;
  height: 140rpx;
  position: absolute;
  display: flex;
  justify-content: space-between;
  bottom: 0;
  z-index: 2;
  background: #ffffff;
  border-top-left-radius: 35rpx;
  border-top-right-radius: 35rpx;
  padding: 30rpx 55rpx;
  box-sizing: border-box;
}
.reviewBtn {
  width: 300rpx;
  height: 80rpx;
  border-radius: 40rpx;
  display: inline-flex;
  justify-content: center;
  line-height: 80rpx;
  font-size: 32rpx;
  color: #ffffff;
  font-family: 'syhtM';
}
.reviewBtn1 {
  width: 300rpx;
  height: 80rpx;
  border-radius: 40rpx;
  display: inline-flex;
  justify-content: center;
  line-height: 80rpx;
  font-size: 32rpx;
  color: #1d755c;
  font-family: 'syhtM';
}
.reviewLeft_btn {
  background: #fda324;
}
.reviewLeft_btn_1 {
  background: #ffffff;
  border: 2rpx solid #1d755c;
}
.reviewRight_btn {
  background: #007c72;
}
.reviewRight_btn_1 {
  background: linear-gradient(180deg, #88cfba 0%, #1d755c 100%);
}

.study_record {
  width: 658rpx;
  margin-left: 45rpx;
  background: white;
  margin-top: 30rpx;
  box-shadow: 2px 4px 20px 1px rgba(0, 0, 0, 0.12);
  border-radius: 14rpx;
  padding-left: 40rpx;
  box-sizing: border-box;
}

.study_record_title {
  width: 100%;
  font-size: 24rpx;
  padding-top: 33rpx;
  font-size: 'syhtR';
  margin: 33rpx auto 4rpx auto;
}
.record_listBox {
  width: 100%;
  overflow-y: auto;
  padding-bottom: 20rpx;
}
.record_listBox .recordList:last-child {
  border: none !important;
}
.recordList {
  width: 100%;
  height: 122rpx;
  border-bottom: 1rpx dashed #dddddd;
}
.recordWord {
  width: 100%;
  height: 45rpx;
  line-height: 45rpx;
  padding-top: 18rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.recordIcon {
  width: 14rpx;
  height: 14rpx;
  margin-right: 18rpx;
  display: inline-block;
  border-radius: 7rpx;
  background: #5cbe63;
}
.recordIcon1 {
  background: #ffbe00 !important;
}
.record_title {
  font-size: 30rpx;
  font-family: 'syhtM';
}
.recordTranst {
  margin-left: 32rpx;
  margin-top: 10rpx;
  color: #999999;
  font-family: 'syhtR';
  font-size: 24rpx;
}

/* 复习单词单词或文字过长 */
.flHeight {
  display: inline-block;
  width: 300rpx;
  line-height: 45rpx;
}

/* 阿拉鼎1.4.0界面样式 */
.status_bar {
  height: var(--status-bar-height);
  width: 100%;
  position: relative;
  background-color: white;
}
.status_bar_image {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 2;
  width: 100%;
  height: 248upx;
}
.wechatapp {
  width: 690upx;
  margin: 240upx auto 220upx auto;
  text-align: center;
}
.wechatapp .header {
  width: 100%;
  margin: 0rpx auto 0;
}
.login_btn_group {
  width: 550upx;
  margin: 0 auto;
}
.login_btn_group1 {
  width: 590upx;
  margin: 0 auto;
}

/* 标题带左侧标志 */
.user_title {
  height: 50upx;
  font-size: 36upx;
  margin: 20upx 0;
  line-height: 50upx;
  display: flex;
  justify-content: space-between;
}
.userTitle_line {
  width: 6upx;
  height: 38upx;
  margin-right: 20upx;
  background: #ffbd00;
  border-radius: 30upx;
}

/* 公共按钮样式 */
.classPayStatus_btn {
  display: inline-block;
  padding: 3upx 8upx;
  text-align: center;
  border-radius: 4upx;
  color: #fff;
  font-size: 26upx;
  height: 35upx;
  line-height: 35upx;
}
.common_btn {
  padding: 8upx 43upx;
  box-sizing: border-box;
  border-radius: 30upx;
  font-size: 30upx;
  display: inline-block;
}
.common_btn_two {
  width: 250upx;
  height: 88upx;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  box-sizing: border-box;
  border-radius: 45upx;
  font-size: 30upx;
}
.common_btn_orange_active {
  color: #fff !important;
  background: linear-gradient(180deg, #88cfba 0%, #1d755c 100%);
  border: none !important;
}
.common_btn_orange {
  color: #2e896f !important;
  background: #fff !important;
  border: 1upx solid #2e896f !important;
}

/*分享弹窗样式*/
.shareCard {
  position: relative;
  width: 100%;
  height: 100%;
  background: #ffffff;
  color: #000;
  padding-top: 50upx;
  box-sizing: border-box;
}

.shareIcon {
  width: 100upx;
  height: 100upx;
}
.shareCancelBox {
  background-color: #f3f8fc;
  width: 100%;
  height: 120upx;
  padding-top: 20upx;
  box-sizing: border-box;
}
.share_cancel {
  width: 100%;
  height: 100upx;
  line-height: 100upx;
  text-align: center;
  font-size: 30upx;
  background-color: #fff;
  color: #666666;
}
.reviewTitle {
  width: 100%;
  text-align: center;
  font-size: 34upx;
  display: flex;
  justify-content: center;
}
.review_close {
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  z-index: 1;
}
