<template>
  <view style="position: relative">
    <view class="chose-item-bg"></view>

    <view class="long-data-picker">
      <picker-view :indicator-style="itemHeight" @change="bindDateChange" indicator-class="select-line" :value="valueArr">
        <picker-view-column style="width: 218rpx; margin-right: 60rpx">
          <view class="long-datetime-item" :class="valueArr[0] == index ? 'chose-text-style' : ''" v-for="(item, index) in dates" :key="index">{{ item }}</view>
        </picker-view-column>

        <picker-view-column style="width: 324rpx">
          <view class="long-datetime-item" style="display: flex; justify-content: space-between" v-for="(item, index) in trialTimeData" :key="index">
            <view :class="valueArr[1] == index ? 'chose-text-style' : ''" class="chose-hour-style">{{ item.startTime + '~' + item.endTime }}</view>
            <text :class="item.isNormal ? '' : item.canReserve ? 'text-color-green' : 'text-color-red'">
              {{ item.isNormal ? '' : item.canReserve ? '可预约' : '已满' }}
            </text>
          </view>
        </picker-view-column>
      </picker-view>
    </view>
  </view>
</template>

<script>
import moment from './moment.js'; //导入文件
moment.locale('zh-cn');
export default {
  name: 'long-date',
  props: {
    chooseNum: {
      type: Number,
      default: 30
    }
  },

  data() {
    return {
      itemHeight: `height: ${uni.upx2px(80)}px;`,
      dates: [], //日期
      trialTimeData: [], //试课时间

      formatdates: [],

      valueArr: [0, 0],
      timeIndex: 0
    };
  },

  mounted() {
    // this.initDate(1);
  },
  methods: {
    //初始化时间
    initDate(day) {
      let days = day ? day : 0;
      // console.log(day, 'longdatelongdatelongdatelongdatelongdatelongdatelongdate');
      //设置日期数组
      this.dates = [];
      //格式化日期数组
      this.formatdates = [];
      for (let i = 0; i <= this.chooseNum; i++) {
        this.formatdates.push(
          moment()
            .add(i + days, 'days')
            .format('YYYY-MM-DD')
        );
        var createDate = moment()
          .add(i + days, 'days')
          .format('MMMDo dddd');
        this.dates.push(createDate.replace('星期', '周'));
      }
    },
    // //初始化时间
    // initDate() {
    //   //设置日期数组
    //   this.dates = [];
    //   //格式化日期数组
    //   this.formatdates = [];
    //   for (let i = 0; i <= this.chooseNum; i++) {
    //     this.formatdates.push(moment().add(i, 'days').format('YYYY-MM-DD'));
    //     var createDate = moment().add(i, 'days').format('MMMDo dddd');
    //     this.dates.push(createDate.replace('星期', '周'));
    //     // console.log(this.dates, 'datesdatesdatesdatesdatesdatesdatesdatesdatesdatesdates');
    //   }
    // },
    getDataforChoseDate(dataArr) {
      console.log('parent data response');
      this.trialTimeData = dataArr;
    },

    getDataforChoseIndex(dataArr) {
      console.log('parent index response');
      this.valueArr = dataArr;
      this.timeIndex = this.valueArr[0];
    },

    //滚动切换时间
    bindDateChange(e) {
      //有效日期的滚动日期时间方法
      this.valueArr = e.detail.value;
      let isRequest = false;
      if (this.timeIndex != this.valueArr[0]) {
        isRequest = true;
        this.timeIndex = this.valueArr[0];
      }
      let dateWeekStr = this.dates[this.valueArr[0]];
      let dateStr = this.formatdates[this.valueArr[0]];
      // let hourStr = this.trialTimeData[this.valueArr[1]];
      this.$emit('select', {
        timeWeek: dateWeekStr,
        time: moment(dateStr).format('YYYY-MM-DD'),
        // hour:hourStr,
        isRequest: isRequest,
        valueArr: this.valueArr
      });
    }
  }
};
</script>

<style>
.long-data {
  margin-top: 30rpx;
  height: 80rpx;
  background: #ffffff;
  line-height: 80rpx;
  padding-left: 30rpx;
  padding-right: 30rpx;
  border-bottom: 1px solid #eee;
}

.long-data-picker {
  width: 710rpx;
  height: 467rpx;
  overflow: hidden;
  transition: height 0.3s;
  margin-left: 80rpx;
}

.chose-item-bg {
  position: absolute;
  top: 42%;
  border-radius: 12rpx;
  background-color: #f4f4f4;
  height: 80rpx;
  width: 710rpx;
}

.long-datetime-item {
  text-align: center;
  height: 80rpx;
  line-height: 80rpx;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-size: 30upx;
}

.long-data-picker picker-view {
  height: 100%;
}

.long-data-picker picker-view picker-view-column {
  flex: none;
}

.chose-hour-style {
  width: 208rpx;
  text-align: center;
}

.chose-text-style {
  font-weight: bold;
  font-size: 32rpx;
}

::v-deep.select-line::after {
  border-bottom: 0rpx solid #fff;
}
::v-deep.select-line::before {
  border-top: 0rpx solid #fff;
}

.text-color-green {
  color: rgba(46, 137, 111, 1);
}
.text-color-red {
  color: rgba(216, 52, 29, 1);
}
</style>
