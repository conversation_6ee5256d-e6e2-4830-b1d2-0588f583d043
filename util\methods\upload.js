import {
	$showMsg
} from "./prompt.js";
// import { upload_host } from "../publicVariable";

/**
 * 选择图片
 * @param {Number} sum_number 可上传的数量
 * @param {Array} add_img  已上传的数组
 */
const $chooseImage = (sum_number, add_img = []) => {
	return new Promise((resolve, reject) => {
		let that = this;
		// sumNumber 允许上传的总数量
		// add_img 已上传图片数组
		let photo_num = sum_number - add_img.length; //剩余可上传数
		if (photo_num == 0) {
			$showMsg(`最多上传${sum_number}张图片`);
			reject();
		}
		uni.chooseImage({
			count: photo_num,
			sizeType: ['original', 'compressed'],
			sourceType: ['album', 'camera'],
			success(res) {
				let tempFilePaths = res.tempFilePaths;
				add_img = add_img.concat(tempFilePaths)
				resolve(add_img)
			},
			fail(err) {
				$showMsg(err.errMsg)
			}
		})
	})
}

/**
 * 单图片上传
 * @param {String} img 上传的图片连接
 */
const $uploadImage = (img) => {
	return new Promise((resolve, reject) => {
		let that = this;
		uni.showLoading({
			title: '上传中',
		})
		uni.uploadFile({
			url: upload_host,
			filePath: img,
			name: 'file',
			header: {
				Token: uni.getStorageSync('token')
			},
			success: function(res) {
				let data = JSON.parse(res.data)
				if (data.status) {
					resolve(data)
				} else {
					$showMsg(data.msg)
				}
			},
			fail: function(err) {
				$showMsg(err.errMsg)
			},
			complete: function(res) {
				uni.hideLoading()
			},
		})
	})
}

/**
 * 多图片上传
 * @param {Array} img_arr 上传的图片数组
 */
const $uploadMoreImage = (img_arr) => {
	return new Promise((resolve, reject) => {
		uni.showLoading({
			title: '上传中',
			mask: true,
		})
		let img_arr_len = img_arr.length;
		let i = 0;
		let uploaded = [];
		img_arr.map(v => {
			uni.uploadFile({
				url: upload_host,
				filePath: img_arr[i],
				name: 'image',
				header: {
					"Content-type": "multipart/form-data",
				},
				formData: {
					site_token: 123456,
					method: "images.upload"
				},
				success(res) {
					let data = JSON.parse(res.data);
					uploaded.push(data.data.image_id);
				},
				fail(err) {
					$showMsg(err.errMsg)
				},
				complete(res) {
					i++;
					if (i == img_arr_len) {
						uni.hideLoading()
						resolve(uploaded)
					}
				},
			})
		})
	})
}

module.exports = {
	$chooseImage,
	$uploadImage,
	$uploadMoreImage
}
