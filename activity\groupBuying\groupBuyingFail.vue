<template>
  <!-- 拼团失败详情 -->
  <view>
    <view class="mlr-30">
      <view class="card">
        <view class="card-item">
          <view class="card-title">
            <view class="left fail">拼团失败</view>
            <view class="right">已退款</view>
          </view>
          <view class="card-divider"></view>
          <view class="goods-item">
            <view class="goods-img">
              <image class="wh100" :src="groupInstanceInfo.piGoodsVo.goodsPicUrl" mode="aspectFill" />
            </view>
            <view class="goods-content">
              <view class="goods-title">{{ groupInstanceInfo.piGoodsVo.goodsName || '' }}</view>
              <view class="goods-size">{{ groupInstanceInfo.piGoodsVo.goodsSpec || '' }}</view>
              <view class="goods-price">￥{{ groupInstanceInfo.groupPrice || '' }}</view>
            </view>
          </view>
          <view class="card-divider"></view>

          <view class="refund-text">退款金额: {{ groupInstanceInfo.groupPrice || '' }}</view>

          <view class="try-btn" v-if="groupInstanceInfo.id && !isUnShelve" @click="handleTry">再尝试一次吧</view>
        </view>
      </view>

      <view class="hot-text" v-if="!isUnShelve">
        <view class="line"></view>
        <view class="text">更多商品限时抢购中</view>
        <view class="line"></view>
      </view>

      <view class="goods-list">
        <template v-if="dataList.length > 0">
          <goods-card-item :itemStyle="{ background: '#fff' }" :dataSource="item" v-for="(item, index) in dataList" :key="index"></goods-card-item>
        </template>
      </view>
    </view>
  </view>
</template>

<script>
  const { $navigationTo, $http } = require('@/util/methods.js');
  import GoodsCardItem from './components/GoodsCardItem.vue';
  export default {
    data() {
      return {
        imgHost: getApp().globalData.imgsomeHost,
        groupStatus: ['拼团失败', '拼团进行中', '拼团成功', '拼团发起中'],
        dataList: [],
        groupInstanceInfo: {
          piGoodsVo: {}
        },
        isUnShelve: false
      };
    },
    components: {
      GoodsCardItem
    },
    onLoad(e) {
      this.groupInstanceId = e.groupInstanceId || '';
      this.groupActivityId = e.groupActivityId || '';
    },
    onShow() {
      this.getGroupInstanceInfo();
      this.getActivityGoods();
    },

    methods: {
      handleTry() {
        $navigationTo(
          `Coursedetails/productDetils?id=${this.groupInstanceInfo.goodsId}&groupActivityId=${this.groupInstanceInfo.groupActivityId}&isGroupBuyGood=1&isGroupLeader=1`
        );
      },
      async getGroupInstanceInfo() {
        uni.showLoading({
          title: '加载中...',
          mask: true
        });
        const res = await $http({
          url: 'zx/wap/group/instances/single',
          method: 'get',
          data: {
            instancesId: this.groupInstanceId
          }
        });

        if (res.data) {
          this.groupInstanceInfo = res.data;
          this.groupSize = res.data && res.data ? res.data.groupSize : 0;
          this.groupUserList = res.data.piGroupParticipantsVos || [];
        }
      },
      async getActivityGoods() {
        uni.showLoading({
          title: '加载中...',
          mask: true
        });
        const res = await $http({
          url: 'zx/wap/group/activity/good/page',
          method: 'get',
          data: {
            groupActivityId: this.groupActivityId,
            pageSize: 1000
          }
        });

        // 活动下架
        if (!res) {
          this.isUnShelve = true;
        }

        if (res.data) {
          this.dataList = res.data.data || [];
        }
      }
    }
  };
</script>

<style lang="scss">
  page {
    height: 100%;
    background: #f9fcff;
  }
</style>
<style lang="scss" scoped>
  .card {
    padding-top: 32rpx;
    .card-item {
      width: 686rpx;
      height: 568rpx;
      box-sizing: border-box;
      padding: 28rpx 24rpx 36rpx;
      background: #ffffff;
      border-radius: 16rpx;
      margin-bottom: 24rpx;

      .card-title {
        display: flex;
        align-items: center;
        justify-content: space-between;

        .left {
          font-family: AlibabaPuHuiTiBold;
          font-weight: bold;
          font-size: 32rpx;
          color: #339378;
          line-height: 44rpx;
        }

        .fail {
          color: #fd9b2a;
        }

        .right {
          font-size: 28rpx;
          color: #9fa0a1;
          line-height: 40rpx;
        }
      }

      .card-divider {
        width: 100%;
        height: 2rpx;
        margin: 24rpx 0;
        border-top: 2rpx solid #f6f7f9;
      }

      .refund-text {
        font-size: 28rpx;
        color: #9fa0a1;
        line-height: 44rpx;
      }
    }
  }

  .hot-text {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 32rpx;

    .line {
      width: 162rpx;
      height: 2rpx;
      border-top: 1rpx solid #9fa0a16e;
    }

    .text {
      padding: 0 16rpx;
      font-size: 28rpx;
      color: #9fa0a1;
      line-height: 44rpx;
    }
  }

  .goods-list {
    height: calc(100vh - 725rpx);
    margin-top: 32rpx;
    overflow-y: scroll;
    font-family: AlibabaPuHuiTiBold;
    font-weight: bold;
  }

  .try-btn {
    width: 632rpx;
    height: 92rpx;
    margin: 32rpx auto 0;
    background: #428a6f;
    border-radius: 46rpx;
    font-size: 32rpx;
    color: #ffffff;
    line-height: 92rpx;
    text-align: center;
    font-family: AlibabaPuHuiTiBold;
    font-weight: bold;
  }

  .goods-item {
    display: flex;
    align-items: center;
    width: 100%;
    height: 176rpx;
    margin-bottom: 34rpx;

    .goods-img {
      width: 176rpx;
      height: 176rpx;
      margin-right: 26rpx;
    }
  }

  .goods-content {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items: flex-start;
    height: 100%;
    .goods-title {
      width: 100%;
      max-width: 442rpx;
      font-size: 28rpx;
      color: #555555;
      font-weight: bold;
      line-height: 44rpx;
    }

    .goods-size {
      font-size: 28rpx;
      color: #9fa0a1;
      line-height: 44rpx;
    }

    .goods-price {
      color: #ed7d4d;
      font-size: 28rpx;
      color: #ed7d4d;
      line-height: 44rpx;
    }
  }

  .card-tips {
    padding-left: 32rpx;
    font-size: 28rpx;
    color: #555555;
    line-height: 44rpx;
    .orange {
      color: #fd9b2a;
    }
  }

  .noData {
    height: calc(100vh - 200rpx);
    image {
      width: 200rpx;
      height: 200rpx;
    }
  }
</style>
