<template>
  <view>
    <!-- #ifdef MP-WEIXIN -->
    <view class="pt-140">
      <u-empty mode="car" textSize="24" width="200" height="200" :icon="imgHost + 'alading/correcting/no_data.png'" :text="emptyText || '暂无记录'"></u-empty>
    </view>
    <!-- #endif -->
    <!-- #ifdef APP-PLUS -->
    <view class="pt-140">
      <u-empty mode="car" textSize="14" width="100" height="100" :icon="imgHost + 'alading/correcting/no_data.png'" :text="emptyText || '暂无记录'"></u-empty>
    </view>
    <!-- #endif -->
  </view>
</template>

<script>
  export default {
    props: ['emptyText'],
    data() {
      return {
        imgHost: getApp().globalData.imgsomeHost
      };
    },
    methods: {}
  };
</script>

<style></style>
