<template>
  <view style="height: 1600prx; background-color: #2e896f">
    <view>
      <uni-icons style="position: absolute; top: 100rpx; left: 20rpx" type="arrowleft" class="backIcon" size="20" color="#fff" @click="goback()"></uni-icons>
      <image :src="path" mode="widthFix" style="width: 100%" :show-menu-by-longpress="true"></image>
      <l-painter isCanvasToTempFilePath ref="painter" @success="path = $event" custom-style="position: fixed; left: 200%" css="width: 750rpx; padding-bottom: 40rpx">
        <l-painter-image
          src="https://document.dxznjy.com/app/images/zhujiaoduan/feedback_bg.png"
          css="position: absolute;top:30rpx;object-fit: contain;width: 100%;"
        ></l-painter-image>
        <l-painter-view css="position: absolute;top:190rpx;z-index:2;width:100%;text-align:center;color:#FFFFFF;font-size:34rpx;font-weight: bold;">
          <l-painter-text text="学习反馈" />
        </l-painter-view>
        <l-painter-view css="position: relative;margin-top: 320rpx; padding: 32rpx;box-sizing: border-box; background: #fff;border:30rpx solid #2e896f;">
          <!-- 学习反馈  -->
          <l-painter-view v-if="backlist && backlist.experience == false">
            <l-painter-text :text="'日期：' + (backlist.dateTime ? backlist.dateTime : '')" css="font-size: 30rpx;display: block; margin-bottom: 30rpx; " />
            <l-painter-text :text="'姓名：' + (backlist.studentName ? backlist.studentName : '')" css="font-size: 30rpx;display: block; margin-bottom: 30rpx; " />
            <l-painter-text :text="'年级：' + (backlist.gradeName ? backlist.gradeName : '')" css="font-size: 30rpx;display: block; margin-bottom: 30rpx; " />
            <l-painter-text :text="'学员编号：' + (backlist.studentCode ? backlist.studentCode : '')" css="font-size: 30rpx;display: block; margin-bottom: 30rpx; " />
            <!-- <l-painter-text :text="'实际时间：' + (backlist.actualStart?backlist.actualStart:'') + '~' 
                        +  (backlist.actualEnd?backlist.actualEnd:'')"
                            css="font-size: 30rpx;display: block; margin-bottom: 30rpx; " /> -->
            <l-painter-text :text="'实际时间：' + (backlist.studyTime ? backlist.studyTime : '')" css="font-size: 30rpx;display: block; margin-bottom: 30rpx; " />
            <l-painter-text :text="'学习学时：' + (backlist.studyHour ? backlist.studyHour : '0') + '小时'" css="font-size: 30rpx;display: block; margin-bottom: 30rpx; " />
            <l-painter-text
              :text="'已购鼎英语学时：' + (backlist.totalCourseHours ? backlist.totalCourseHours : '0') + '小时'"
              css="font-size: 30rpx;display: block; margin-bottom: 30rpx; "
            />
            <l-painter-text
              :text="'剩余鼎英语学时：' + (backlist.leaveCourseHours ? backlist.leaveCourseHours : '0') + '小时'"
              css="font-size: 30rpx;display: block; margin-bottom: 30rpx; "
            />

            <l-painter-view v-if="isWord">
              <l-painter-view css="width: 630rpx;">
                <l-painter-text text="所学词库：" css="font-size: 30rpx;margin-bottom: 30rpx;line-height: 1.8em" />

                <l-painter-text :text="backlist.studyBooks ? backlist.studyBooks.replaceAll('、', '\n') : ''" css="font-size: 30rpx;white-space: pre-line;margin-bottom: 30rpx;" />
              </l-painter-view>
              <l-painter-view css="width: 630rpx;">
                <l-painter-text
                  :text="'复习词汇：' + (backlist.reviewWords ? backlist.reviewWords : '0') + '个'"
                  css="font-size: 30rpx;display: inline-block;width:50%;margin-bottom: 30rpx;"
                />
                <l-painter-text
                  :text="'复习遗忘词汇：' + (backlist.forgetWords ? backlist.forgetWords : '0') + '个'"
                  css="font-size: 30rpx;display: inline-block;width:50%;margin-bottom: 30rpx; "
                />
              </l-painter-view>

              <l-painter-text :text="'复习遗忘率：' + (backlist.forgetRate ? backlist.forgetRate : '0') + '%'" css="font-size: 30rpx;display: block;margin-bottom: 30rpx; " />
              <l-painter-view css="width: 630rpx;">
                <l-painter-text
                  :text="'学新词汇：' + (backlist.newWords ? backlist.newWords : '0') + '个'"
                  css="font-size: 30rpx;display: inline-block;width:50%;margin-bottom: 30rpx; "
                />
                <l-painter-text
                  :text="'学新遗忘词汇：' + (backlist.newForget ? backlist.newForget : '0') + '个'"
                  css="font-size: 30rpx;display: inline-block;width:50%;margin-bottom: 30rpx; "
                />
              </l-painter-view>

              <l-painter-text :text="'学新遗忘率：' + (backlist.newForgetRate ? backlist.newForgetRate : '0') + '%'" css="font-size: 30rpx;display: block;margin-bottom: 30rpx; " />

              <l-painter-view css="width: 630rpx;">
                <l-painter-text text="学习进度：" css="font-size: 30rpx;margin-bottom: 30rpx;line-height: 1.8em" />
                <l-painter-text
                  :text="backlist.learnSchedule ? backlist.learnSchedule.replaceAll(',', '\n') : ''"
                  css="font-size: 30rpx;white-space: pre-line;margin-bottom: 30rpx;"
                />
              </l-painter-view>

              <l-painter-view css="width: 630rpx;">
                <l-painter-text :text="'今日共识记词汇'" css="font-size: 30rpx;margin-bottom: 30rpx; " />
                <l-painter-text :text="'(复习遗忘词汇+学新词汇)：'" css="font-size: 24rpx;margin-bottom: 30rpx;color:#999999" />
                <l-painter-text :text="(backlist.todayWords ? backlist.todayWords : '0') + '个'" css="font-size: 30rpx;margin-bottom: 30rpx; " />
              </l-painter-view>

              <l-painter-text :text="'学习效率：' + (backlist.studyRate ? backlist.studyRate : '0') + '%'" css="font-size: 30rpx;display: block;margin-bottom: 30rpx; " />
            </l-painter-view>
            <l-painter-text text="教练评语：" css="font-size: 30rpx;display: block;margin-bottom: 30rpx;line-height: 1.8em" />
            <l-painter-view css="padding: 30rpx;box-sizing: border-box;background: rgba(153, 153, 153, 0.08);border-radius: 8rpx;width: 630rpx;min-height:481rpx;">
              <l-painter-text :text="backlist.feedback ? backlist.feedback : ''" css="font-size: 30rpx;display: block;line-height: 1.8em" />
            </l-painter-view>
          </l-painter-view>

          <!-- 试课 -->
          <l-painter-view v-if="backlist && backlist.experience">
            <l-painter-text :text="'日期：' + (backlist.dateTime ? backlist.dateTime : '')" css="font-size: 30rpx;display: block; margin-bottom: 30rpx; " />
            <l-painter-text :text="'姓名：' + (backlist.studentName ? backlist.studentName : '')" css="font-size: 30rpx;display: block; margin-bottom: 30rpx; " />
            <l-painter-text :text="'年级：' + (backlist.gradeName ? backlist.gradeName : '')" css="font-size: 30rpx;display: block; margin-bottom: 30rpx; " />
            <l-painter-text :text="'学员编号：' + (backlist.studentCode ? backlist.studentCode : '')" css="font-size: 30rpx;display: block; margin-bottom: 30rpx; " />
            <!-- <l-painter-text :text="'实际时间：' + (backlist.actualStart?backlist.actualStart:'') + '~' 
                        +  (backlist.actualEnd?backlist.actualEnd:'')"
                            css="font-size: 30rpx;display: block; margin-bottom: 30rpx; " /> -->
            <l-painter-text :text="'实际时间：' + (backlist.studyTime ? backlist.studyTime : '')" css="font-size: 30rpx;display: block; margin-bottom: 30rpx; " />
            <l-painter-text :text="'试学学时：' + (backlist.studyHour ? backlist.studyHour : '0') + '小时'" css="font-size: 30rpx;display: block; margin-bottom: 30rpx; " />

            <l-painter-view v-if="isWord">
              <l-painter-text
                :text="'词汇量测试水平：' + (backlist.vocabularyLevel ? backlist.vocabularyLevel : '')"
                css="font-size: 30rpx;display: block;margin-bottom: 30rpx; "
              />

              <l-painter-text :text="'首测词汇量：' + (backlist.expWords ? backlist.expWords : '0') + '个'" css="font-size: 30rpx;display: block;margin-bottom: 30rpx; " />

              <l-painter-text :text="'识记词汇数量：' + (backlist.todayWords ? backlist.todayWords : '0') + '个'" css="font-size: 30rpx;display: block;margin-bottom: 30rpx; " />

              <l-painter-text :text="'遗忘数量：' + (backlist.newForget ? backlist.newForget : '0') + '个'" css="font-size: 30rpx;display: block;margin-bottom: 30rpx; " />

              <l-painter-text :text="'记忆率：' + (backlist.wordsRate ? backlist.wordsRate : '0') + '%'" css="font-size: 30rpx;display: block;margin-bottom: 30rpx; " />

              <l-painter-view css="width: 630rpx;">
                <l-painter-text text="体验词库：" css="font-size: 30rpx;margin-bottom: 30rpx;line-height: 1.8em" />
                <l-painter-text :text="backlist.studyBooks ? backlist.studyBooks.replaceAll('、', '\n') : ''" css="font-size: 30rpx;white-space: pre-line;margin-bottom: 30rpx;" />
              </l-painter-view>

              <l-painter-text
                :text="'记忆特点：' + (backlist.memoryTime ? backlist.memoryTime : '0') + '分钟记住' + (backlist.memoryNum ? backlist.memoryNum : '0') + '个单词'"
                css="font-size: 30rpx;display: block;margin-bottom: 30rpx; "
              />

              <l-painter-text :text="'体验后学习意愿：' + (backlist.studyIntention ? backlist.studyIntention : '')" css="font-size: 30rpx;display: block;margin-bottom: 30rpx; " />
            </l-painter-view>

            <l-painter-text text="教练评语：" css="font-size: 30rpx;display: block;margin-bottom: 30rpx;line-height: 1.8em" />
            <l-painter-view css="padding: 30rpx;box-sizing: border-box;background: rgba(153, 153, 153, 0.08);border-radius: 8rpx;width: 630rpx;min-height:481rpx;">
              <l-painter-text :text="backlist.feedback ? backlist.feedback : ''" css="font-size: 30rpx;display: block;line-height: 1.8em" />
            </l-painter-view>
          </l-painter-view>
        </l-painter-view>
      </l-painter>
    </view>
  </view>
</template>

<script>
  export default {
    data() {
      return {
        path: '', //生成海报
        backlist: '', //反馈详情
        isWord: true // 是否是单词
      };
    },
    onLoad(e) {
      let feedbackId = e.scene;
      if (feedbackId) {
        this.getFeedbackData(feedbackId);
      } else {
        this.$util.alter('获取参数错误');
      }
    },

    methods: {
      async getFeedbackData(feedbackId) {
        let sceneValue = await this.$httpUser.get('zx/user/getSceneValue?scene=' + feedbackId);
        if (sceneValue.data.status == 1) {
          let studentCourseFlowId = sceneValue.data.data.courseId;
          let res = await this.$httpUser.get('deliver/app/teacher/getSelfFeedbackInfo?studentCourseFlowId=' + studentCourseFlowId);
          if (res.data.success) {
            this.backlist = res.data.data;
            this.isWord = this.backlist.isWord;
          } else {
            this.$util.alter(res.data.message);
          }
        } else {
          this.$util.alter(sceneValue.data.message);
        }
      },

      //长按
      longPress() {
        this.$refs.painter.canvasToTempFilePathSync({
          fileType: 'jpg',
          pathType: 'url',
          quality: 1,
          success: (res) => {
            this.path = res.tempFilePath;
            this.saveLocal();
          },
          fail: (err) => {
            console.log(err);
            uni.showModal({
              title: '提示',
              content: '生成海报失败,请重试',
              showCancel: false
            });
          }
        });
      },

      //保存本地
      saveLocal() {
        uni.authorize({
          scope: 'scope.writePhotosAlbum',
          success: () => {
            uni.saveImageToPhotosAlbum({
              filePath: this.path,
              success: (res) => {
                uni.showToast({
                  icon: 'none',
                  title: '保存成功'
                });
                console.log(res);
              },
              fail(err) {
                uni.showToast({
                  icon: 'none',
                  title: '保存失败'
                });
              }
            });
          },
          fail() {
            uni.showModal({
              title: '保存失败',
              content: '您没有授权，无法保存到相册',
              showCancel: false
            });
          }
        });
      },
      goback() {
        uni.switchTab({
          url: '/pages/index/index'
        });
      }
    }
  };
</script>
