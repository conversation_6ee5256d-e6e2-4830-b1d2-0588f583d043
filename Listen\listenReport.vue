<template>
  <view style="background-color: #3bb680">
    <image src="https://document.dxznjy.com/course/5f62419933764e1e8283f624a5d0ba53.png" style="height: 632rpx; width: 100%"></image>
    <view class="contarin" :style="{ height: useHeight + 'rpx' }">
      <view class="conetent">
        <view class="title">
          <image src="https://document.dxznjy.com/course/e13596e50ac4440abbf4341fa111d9dc.png" style="width: 32rpx; height: 26rpx; margin-right: 10rpx"></image>
          报告总结
        </view>
        <view class="summarize">
          <view style="flex: 1">
            答题正确率
            <span class="green" v-if="data">{{ data.rightRate }}%</span>
          </view>
          <view style="flex: 1">
            答题用时
            <span class="green" v-if="data">{{ data.useTime }}</span>
          </view>
        </view>
        <view class="summarize">
          答题时间
          <span style="margin-left: 10rpx" v-if="data">{{ data.createTime }}</span>
        </view>
        <view class="title">
          <image src="https://document.dxznjy.com/course/e13596e50ac4440abbf4341fa111d9dc.png" style="width: 32rpx; height: 26rpx; margin-right: 10rpx"></image>
          答题卡
        </view>
        <view class="answerSheets" v-if="data">
          <view class="sheet" @click="goAnalysis(item.index)" v-for="item in data.answerList" :class="item.isRight ? 'right' : 'error'">{{ item.index }}</view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
  export default {
    data() {
      return {
        materialsId: null,
        studentCode: null,
        useHeight: 0,
        title: '',
        data: null
      };
    },
    onLoad(option) {
      this.studentCode = option.studentCode;
      this.materialsId = option.materialsId;
      this.title = option.title;
      this.init();
    },
    onReady() {
      uni.getSystemInfo({
        success: (res) => {
          // 可使用窗口高度，将px转换rpx
          this.useHeight = res.windowHeight * (750 / res.windowWidth) - 630;
        }
      });
    },
    methods: {
      async init() {
        let { data } = await this.$httpUser.get('dyf/zxListening/wap/getReportData?studentCode=' + this.studentCode + '&materialsId=' + this.materialsId);
        if (data.success) {
          this.data = data.data;
          this.data.useTime = this.formatSeconds(this.data.useTime);
        }
      },
      goAnalysis(i) {
        let num = i - 1;
        let obj = encodeURIComponent(JSON.stringify(this.data.questionDetailVoList[num]));
        let all = {
          title: this.title,
          allNum: this.data.questionDetailVoList.length,
          index: i
        };
        all = encodeURIComponent(JSON.stringify(all));
        if (this.data.reviewCount > 1) {
          uni.navigateTo({
            url: `/Listen/answerAnalysis?data=${obj}&all=${all}&reviewCount=${this.data.reviewCount}`
          });
        } else {
          uni.navigateTo({
            url: `/Listen/answerAnalysis?data=${obj}&all=${all}`
          });
        }
      },
      formatSeconds(seconds) {
        // 计算小时数
        const hours = Math.floor(seconds / 3600);
        // 计算剩余的分钟数
        const minutes = Math.floor((seconds % 3600) / 60);
        // 计算剩余的秒数
        const secs = seconds % 60;

        // 格式化为两位数
        const formattedHours = String(hours).padStart(2, '0');
        const formattedMinutes = String(minutes).padStart(2, '0');
        const formattedSeconds = String(secs).padStart(2, '0');

        return `${formattedHours}:${formattedMinutes}:${formattedSeconds}`;
      }
    }
  };
</script>

<style lang="scss" scoped>
  .contarin {
    .conetent {
      margin: -10rpx 32rpx 0;
      background: linear-gradient(180deg, #f5fdef 0%, #ffffff 100%);
      border-radius: 60rpx;
      height: 732rpx;
      box-sizing: border-box;
      padding: 74rpx 36rpx;
      .title {
        display: flex;
        height: 38rpx;
        font-size: 32rpx;
        color: #333333;
        font-weight: bold;
        align-items: center;
        font-family: AlimamaShuHeiTi, AlimamaShuHeiTi;
      }
      .summarize {
        display: flex;
        margin: 32rpx 0;
        font-size: 28rpx;
        color: #000000;

        height: 56rpx;
        align-items: flex-end;
        .green {
          font-weight: bold;
          margin-left: 10rpx;
          font-size: 40rpx;
          color: #3bb680;
        }
      }
      .answerSheets {
        max-height: 267rpx;
        overflow-y: auto;
        margin-top: 32rpx;
        display: flex;
        flex-wrap: wrap;
        .sheet {
          width: 76rpx;
          height: 76rpx;
          background: #fff6f2;
          margin-right: 40rpx;
          margin-bottom: 10rpx;
          border-radius: 76rpx;
          text-align: center;
          line-height: 76rpx;
          font-size: 32rpx;
          font-weight: bold;
        }
        .right {
          background: #e5f9ee;
          border: 2rpx solid #97e8b4;
          color: #14806c;
        }
        .error {
          background: #fff6f2;
          border: 2rpx solid #f96838;
          color: #f96838;
        }
      }
    }
  }
</style>
