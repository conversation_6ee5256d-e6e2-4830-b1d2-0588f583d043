<template>
  <view class="">
    <view class="orderList">
      <!-- <view class="Eyne" >
									<text>暂无更多数据</text>
								</view> -->
      <view class="listItem" v-for="(item, index) in listData" :key="index">
        <view>
          <view class="num" v-if="item.passFlag === 1">{{ '已通过' || '' }}</view>
          <view class="no_num" v-if="item.passFlag === 0">{{ '未通过' || '' }}</view>

          <h2 class="supplier_name">
            {{ item.examName || '' }}
          </h2>
          <u-line></u-line>
          <view class="supplier_name">
            试卷编号：{{ item.examId }}
            <!-- <text  class="non-payment">未付款</text> -->
            <!-- <text class="accountPaid">已付款</text> -->
          </view>
          <!-- <view class="number"> 采购订单编号：{{ item.number }} </view> -->
          <view class="purchase_name">对应课程：{{ item.courseName }}</view>
          <view class="create_account">试卷总分：{{ item.examFullMarks }}</view>
          <!-- <view class="remark"> 年度：{{ item.fiscal_name || '' }} </view>
									<view class="remark"> 月份：{{ item.month || '' }} </view> -->
          <view class="create_time_text">及格分数：{{ item.examPassScore }}</view>
          <view class="remark">考试成绩：{{ item.examScore }}</view>
          <view class="remark">考试时间：{{ item.examTime }}</view>
          <u-line></u-line>
          <view class="status list-button">
            <!-- <view v-if="item.can_reject" @click="reject(item)"> 驳回 </view>
                                    <view v-if="item.can_approve" @tap="passApprove(item)"> 通过 </view> -->
            <view @tap="testPaperDetail(item.id)">试卷详情</view>
          </view>
        </view>
      </view>
      <view v-if="!listData.length" class="List_empty">
        <u-empty textSize="25" iconSize="190" width="260" text="暂无记录" height="260" mode="list" icon="http://cdn.uviewui.com/uview/empty/list.png"></u-empty>
      </view>
    </view>
  </view>
</template>

<script>
  const { $http } = require('@/util/methods.js');
  export default {
    data() {
      return {
        courseId: '',
        listData: [],
        currentPage: 1,
        totalPage: 1
      };
    },
    onLoad(option) {
      this.courseId = option.courseId;
      console.log(option, 'option');

      this.getExaminationRecord();
    },
    onReachBottom() {
      console.log('滚动了');
      if (this.currentPage === this.totalPage) return uni.showToast({ title: '没有更多数据了', icon: 'none' });
      this.currentPage++;
      this.getExaminationRecord();
    },
    methods: {
      testPaperDetail(id) {
        uni.navigateTo({
          url: `/growth/centerList/viewTestPaper?examRecordId=${id}&isDetail=1`
        });
      },
      async getExaminationRecord() {
        const res = await $http({
          url: 'train/web/exam/training/course_exam_record_page',
          method: 'POST',
          data: {
            userId: uni.getStorageSync('bvAdminId') ? uni.getStorageSync('bvAdminId') : '',
            roleTag: uni.getStorageSync('roleTag') ? uni.getStorageSync('roleTag') : '',
            courseId: this.courseId,
            pageNum: this.currentPage,
            pageSize: 10
          }
        });
        this.listData = [...this.listData, ...res.data.data];
        this.currentPage = Number(res.data.currentPage);
        this.totalPage = Number(res.data.totalPage);
        console.log(res, '4444');
      }
    }
  };
</script>

<style scoped lang="scss">
  .orderList {
    padding: 40rpx 30rpx;

    .Eyne {
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .listItem {
      width: 100%;
      background-color: #ffffff;
      margin-bottom: 24rpx;
      padding: 22rpx 30rpx;
      box-sizing: border-box;
      border-radius: 24rpx;
      position: relative;
      color: #666666;
      font-size: 14px;

      .num {
        position: absolute;
        top: 0;
        right: 0;
        width: 126rpx;
        height: 64rpx;
        background: #ecfbf3;
        border-radius: 0rpx 24rpx 0rpx 24rpx;
        color: #81e2af;
        border: 2rpx solid #81e2af;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 14px;
      }

      .no_num {
        position: absolute;
        top: 0;
        right: 0;
        width: 126rpx;
        height: 64rpx;
        background: #fffaf2;
        border-radius: 0rpx 24rpx 0rpx 24rpx;
        color: #fd9b29;
        border: 2rpx solid #fd9b29;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 14px;
      }

      .create_account {
        color: #3284ff;
      }

      .create_account,
      .supplier_name,
      .number,
      .purchase_name,
      .create_time_text,
      .remark,
      .status {
        margin-bottom: 16rpx;
      }
    }
  }

  .non-payment {
    width: 83rpx;
    height: 34rpx;
    text-align: center;
    line-height: 34rpx;
    // background: rgba(255,66,66,0.05);
    border-radius: 5rpx;
    margin-left: 20rpx;
    font-size: 22rpx;
    font-family: PingFang SC, PingFang SC-400;
    font-weight: 400;
    // color: #FF4242;
    display: inline-block;
  }

  .accountPaid {
    width: 83rpx;
    height: 34rpx;
    text-align: center;
    line-height: 34rpx;
    background: rgba(62, 185, 104, 0.05);
    border-radius: 5rpx;
    margin-left: 20rpx;
    font-size: 22rpx;
    font-family: PingFang SC, PingFang SC-400;
    font-weight: 400;
    color: #3eb968;
    display: inline-block;
  }

  .list-button {
    display: flex;
    justify-content: flex-end;
    //   padding: 0 82rpx;
    margin-top: 36rpx;
    margin-bottom: 20rpx;

    & > view {
      // flex: 1;
      // height: 72rpx;
      // line-height: 70rpx;
      width: 176rpx;
      height: 60rpx;
      display: flex;
      justify-content: center;
      align-items: center;
      text-align: center;
      border-radius: 240rpx;
      font-size: 28rpx;
      font-weight: 500;
      background-color: #5aa891;
      color: #ffffff;
      font-size: 28rpx;
    }
  }

  .modifyContent {
    width: 100%;

    .title {
      width: 100%;
      text-align: center;
      margin-bottom: 20rpx;
    }
  }

  .buttons {
    display: flex;
    margin-top: 222rpx;
    margin-bottom: 20rpx;

    & > view {
      flex: 1;
      height: 72rpx;
      line-height: 70rpx;
      text-align: center;
      border-radius: 240rpx;
      font-size: 28rpx;
      font-weight: 500;
    }

    & > view:nth-of-type(1) {
      margin-right: 24rpx;
      border: 2rpx solid #999999;
      color: #999999;
    }

    & > view:nth-of-type(2) {
      margin-left: 24rpx;
      border: 2rpx solid #3284ff;
      color: #3284ff;
    }
  }

  .form-wrap {
    padding: 15rpx;
    margin: 15rpx 0;
  }

  .example {
    padding: 15px;
    background-color: #fff;
  }

  .active {
    color: #ff4242;
  }

  ::v-deep .u-line {
    margin: 35rpx auto !important;
  }

  .line {
    background-color: #f5f5f5;
    height: 2rpx;
    width: 100%;
  }

  .List_empty {
    margin: 100rpx auto;
  }
</style>
