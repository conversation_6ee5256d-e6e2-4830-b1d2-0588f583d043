<template>
  <page-meta :page-style="'overflow:' + (rollShow ? 'hidden' : 'visible')"></page-meta>
  <view>
    <view style="position: relative; height: 100vh">
      <image src="https://document.dxznjy.com/course/03051c1919a8436791c4d38117db6847.jpg" style="width: 100%; height: 100%"></image>
      <view class="btn" @click="checked">
        <image src="https://document.dxznjy.com/course/684fed50225f4a359f778d12c675cc96.png" style="width: 100%; height: 100%"></image>
      </view>
    </view>

    <uni-popup ref="popopPower" type="center" @change="changePower">
      <view class="dialogBG">
        <view class="box">
          <view class="problem">请选择孩子</view>
          <view class="items">
            <view class="item" @click="handleButtonClick(item, index)" v-for="(item, index) in arrayStudent">
              <image :src="item.avatar" class="avater" :class="index == activeIndex ? 'active' : ''"></image>
              <view style="text-align: center; font-size: 28rpx">
                {{ item.nick }}
              </view>
            </view>
            <view v-if="arrayStudent.length < 3" class="item">
              <view class="add" @click="add()">
                <uni-icons type="plusempty" size="24"></uni-icons>
              </view>
            </view>
          </view>
          <view class="btns">
            <view class="cancel" @click="cancel">取消</view>
            <view class="confirm" @click="$noMultipleClicks(confirm)">确定</view>
          </view>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script>
  const { $http } = require('@/util/methods.js');
  const { httpUser } = require('@/util/luch-request/indexUser.js');
  export default {
    data() {
      return {
        noClick: true, //防抖
        rollShow: false, //禁止滚动穿透,
        arrayStudent: [],
        buttonClick: '',
        buttonclickName: '',
        activeIndex: null,
        mobile: null
      };
    },
    onLoad(option) {
      this.init();
    },
    onShow() {
      this.getStundent();
    },
    methods: {
      async init() {
        this.mobile = uni.getStorageSync('phone');
      },
      async getStundent() {
        if (!this.mobile) {
          setTimeout(() => {
            this.getStundent();
          }, 200);
          return;
        }
        let result = await httpUser.get('zx/career/student/getListByParentMobile?parentMobile=' + this.mobile);
        if (result != undefined && result != '') {
          if (result.data.data != null) {
            this.arrayStudent = result.data.data.slice(0, 3);
          }
        }
      },
      async confirm() {
        if (!this.buttonClick)
          return uni.showToast({
            title: '请选择孩子',
            icon: 'none',
            duration: 2000
          });
        let { data } = await $http({
          url: 'zx/career/student/havePlan?studentCode=' + this.buttonClick
          // method:'post'
        });
        if (data) {
          uni.showToast({
            title: '该孩子已有学业生涯规划',
            icon: 'none',
            duration: 2000
          });
        } else {
          uni.navigateTo({
            url: '/Personalcenter/Career/index?id=' + this.buttonClick
          });
          this.cancel();
        }
      },
      checked() {
        this.$refs.popopPower.open();
      },
      cancel() {
        this.$refs.popopPower.close();
        this.activeIndex = null;
        this.buttonClick = '';
        this.buttonclickName = '';
      },
      add() {
        uni.navigateTo({
          url: '/Personalcenter/my/mystudentAdd?type=1&memberId=' + this.memberId
        });
        this.cancel();
      },
      changePower(e) {
        this.rollShow = e.show;
      },
      // 处理选择按钮点击事件
      handleButtonClick(item, index) {
        console.log(item, index);
        this.activeIndex = index;
        this.buttonClick = item.studentCode;
        this.buttonclickName = item.nick;
      }
    }
  };
</script>

<style lang="scss" scoped>
  .btn {
    position: absolute;
    bottom: 60rpx;
    width: 600rpx;
    left: 50%;
    transform: translateX(-50%);
    box-sizing: border-box;
    // line-height: 74rpx;
    height: 97rpx;
    font-size: 32rpx;
    font-weight: bold;
    text-align: center;
    border-radius: 97rpx;
    // background: linear-gradient(180deg, #88CFBA 0%, #1D755C 100%);
    // color: #fff;
  }

  .dialogBG {
    width: 100%;
    /* height: 100%; */
  }

  .cancel {
    width: 280rpx;
    height: 92rpx;
    border-radius: 46rpx;
    border: 2rpx solid #428a6f;
    box-sizing: border-box;
    color: #428a6f;
    font-size: 32rpx;
    line-height: 92rpx;
    text-align: center;
  }

  .confirm {
    width: 280rpx;
    height: 92rpx;
    border-radius: 46rpx;
    box-sizing: border-box;
    color: #fff;
    background-color: #428a6f;
    font-size: 32rpx;
    line-height: 92rpx;
    text-align: center;
  }

  .btns {
    margin-top: 60rpx;
    height: 92rpx;
    display: flex;
    justify-content: space-between;
  }

  .box {
    position: relative;
    width: 686rpx;
    height: 498rpx;
    background-color: #fff;
    border-radius: 16rpx;
    padding: 44rpx 46rpx;
    box-sizing: border-box;

    .problem {
      height: 44rpx;
      line-height: 44rpx;
      font-weight: bold;
      font-size: 32rpx;
      color: #333333;
      text-align: center;
      font-family: AlibabaPuHuiTiM;
      margin-bottom: 40rpx;
    }

    .items {
      display: flex;
      height: 180rpx;
      // align-items: center;
    }

    .item {
      margin: 0 40rpx;
    }

    .avater {
      height: 110rpx;
      width: 110rpx;
      border-radius: 110rpx;
      margin-bottom: 24rpx;
      box-sizing: border-box;
    }

    .add {
      height: 110rpx;
      width: 110rpx;
      border-radius: 110rpx;
      margin-bottom: 24rpx;
      background-color: #f3f8fc;
      text-align: center;
      line-height: 110rpx;
    }

    .active {
      border: 4rpx solid #318d6a;
    }
  }
</style>
