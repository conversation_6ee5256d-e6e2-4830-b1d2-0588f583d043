<template>
  <view class="userProgressbg" v-if="countdownStyle == 1">
    <view class="interesting_countdown_left"></view>
    <view v-show="percent > 50 && showCountdown" class="fun_top_tip">虫子快要逃走啦！</view>
    <!-- <view v-show="percent>50" class="fun_top_tip">优势在我！</view> -->
    <view class="userProgress">
      <view class="userProgress_center" :style="{ width: percent + '%' }">
        <view class="userProgress_center_inner"></view>
      </view>
    </view>
    <view v-if="showCountdown" class="interesting_countdown">
      <view class="interesting_countdown_icon"></view>
      <text style="">0:{{ (time + '').padStart(2, '0') }}</text>
    </view>
    <view class="interesting_countdown_right"></view>
  </view>
  <view class="userProgressbg" v-else>
    <!-- <view class="interesting_countdown_left_sun"></view> -->
    <!-- <view v-show="percent>50" class="fun_top_tip">优势在我！</view> -->
    <view class="userProgress_sun">
      <view class="userProgress_center" :style="{ width: percent + '%' }">
        <view class="userProgress_center_inner sun"></view>
      </view>
    </view>
    <view v-if="showCountdown" class="interesting_countdown">
      <view class="interesting_countdown_icon sun"></view>
      <text class="sun">0:{{ (time + '').padStart(2, '0') }}</text>
    </view>
    <view class="interesting_countdown_right sun"></view>
  </view>
</template>

<script>
  /**
   * 进度条组件
   * @property {Number} percent 进度百分比值 - 显示范围0-100 ，可能数比较大就需要自己转成百分比的值
   */
  export default {
    name: 'pyf-progress',
    props: {
      /**
       * 百分比
       */
      percent: {
        type: Number,
        default: 0
      },
      /**
       * 剩余时间
       */
      time: {
        type: Number,
        default: 0
      },
      /**
       * 样式
       * 1.虫子倒计时( 默认
       * 2.太阳倒计时
       */
      countdownStyle: {
        type: Number,
        default: 1
      },
      /**
       * 是否显示倒计时及倒计时文字
       */
      showCountdown: {
        type: Boolean,
        default: true
      }
    },
    data() {
      return {};
    },
    mounted() {
      // console.log(this.showCountdown, 'showCountdown');
    }
  };
</script>

<style lang="scss" scoped>
  .userProgressbg {
    position: absolute;
    top: 10%;
    z-index: 2;
    width: 686rpx;
    height: 74rpx;
    margin: 30rpx 4rpx 0;
  }
  .interesting_countdown_left {
    position: absolute;
    z-index: 1;
    bottom: 0;
    left: 0;
    width: 96rpx;
    height: 80rpx;
    background: url('https://document.dxznjy.com/course/ace0ff2ece2a400b911a3ff8f43021c1.png') 100% 100% no-repeat;
    background-size: 100% 100%;
  }
  .interesting_countdown_right {
    position: absolute;
    z-index: 1;
    bottom: 0;
    right: 0;
    width: 81rpx;
    height: 74rpx;
    background: url('https://document.dxznjy.com/course/035a77de471e4733b3b80c734e3b3d84.png') 100% 100% no-repeat;
    background-size: 100% 100%;

    &.sun {
      width: 88rpx;
      height: 70rpx;
      background: url('https://document.dxznjy.com/course/7a5968ba515c4221aa49c0d2a8a35a1b.png') 100% 100% no-repeat;
      background-size: 100% 100%;
      position: absolute;
      top: 25%;
      transform: translate(9%, 0);
    }
  }
  .userProgress {
    position: absolute;
    right: 14rpx;
    bottom: 6rpx;
    width: 596rpx;
    height: 40rpx;
    background: url('https://document.dxznjy.com/course/045e8105e115415f9af92945198829ff.png') 100% 100% no-repeat;
    background-size: 100% 100%;

    display: flex;
    align-items: end;
    box-sizing: border-box;
    padding-right: 35rpx;
  }
  .userProgress_center {
    position: relative;
    height: 34rpx;
    transition: all 1s;
  }
  .userProgress_center_inner {
    position: absolute;
    right: 0;
    transform: translateX(50%);
    width: 56rpx;
    height: 34rpx;

    background: url('https://document.dxznjy.com/course/1d860593cba34cd2beff61d6e2aefb69.png') 100% 100% no-repeat;
    background-size: 100% 100%;

    &.sun {
      position: absolute;
      top: 50%;
      transform: translate(50%, -50%);
      width: 60rpx;
      height: 60rpx;
      background: url('https://document.dxznjy.com/course/2ef738a7228c4765961784a378f9cd92.png') 100% 100% no-repeat;
      background-size: 100% 100%;
    }
  }
  .interesting_countdown {
    position: absolute;
    top: -20rpx;
    left: 50%;
    transform: translateX(-50%);
    font-size: 36rpx;
    display: flex;
    align-items: center;

    .interesting_countdown_icon {
      width: 43rpx;
      height: 43rpx;
      background: url('https://document.dxznjy.com/course/9138db4a70824c28b12cd4cbe74f7555.png') 100% 100% no-repeat;
      background-size: 100% 100%;
      &.sun {
        background: url('https://document.dxznjy.com/course/0a17affd23694a2cb242bbe5836b14c7.png') 100% 100% no-repeat;
        background-size: 100% 100%;
      }
    }
    text {
      color: #fff;
      font-size: 36rpx;
      margin-left: 10rpx;
      &.sun {
        color: #555;
      }
    }
  }
  .fun_top_tip {
    position: absolute;
    top: -36rpx;
    left: 59rpx;
    width: 182rpx;
    height: 50rpx;
    background: url('https://document.dxznjy.com/course/4784ab2964c245e1aae8e79b3bacde2c.png') 100% 100% no-repeat;
    background-size: 100% 100%;

    font-size: 22rpx;
    color: #555555;
    line-height: 44rpx;
    text-align: center;
  }
  // sun
  .interesting_countdown_left_sun {
    position: absolute;
    z-index: 1;
    bottom: 0;
    left: 0;
    width: 96rpx;
    height: 80rpx;
    background: url('https://document.dxznjy.com/course/2ef738a7228c4765961784a378f9cd92.png') 100% 100% no-repeat;
    background-size: 100% 100%;
  }
  .userProgress_sun {
    position: absolute;
    right: 14rpx;
    bottom: 6rpx;
    width: 647rpx;
    height: 40rpx;
    background: #91ca55;

    display: flex;
    align-items: end;
    box-sizing: border-box;
    padding-right: 40rpx;

    border-radius: 20rpx;
  }
</style>
