<template>
  <view class="interesting_popupCenter">
    <view class="interet_head">
      <image class="interet_popupTitle" :src="imgHost + 'interesting/intert_popupTitle.png'" mode=""></image>
      <text style="bottom: 48rpx">{{ title }}</text>
    </view>

    <image class="chooselist" :src="imgHost + imgUrl" mode=""></image>
    <view class="chooselistTitle">
      <view class="uni-list-cell-db">
        <picker @change="bindPickerChange" class="chooselistTitle" :value="index" :range="array" :range-key="keyName">
          {{ chooseTitle }}
          <uni-icons type="arrowright" size="20" color="#18b48e"></uni-icons>
          <!-- <view class="uni-input">{{array[index].name}}</view> -->
        </picker>
      </view>
    </view>

    <image @click="closeDialog()" class="interesting_close" :src="imgHost + 'interesting/dialog_closed.png'" mode=""></image>
  </view>
</template>

<script>
  export default {
    // isReview:true,//是否是复习弹窗  isSingle:false,//是否是单个按钮  isRed:true,//字体颜色是否是红色
    props: {
      title: '',
      chooseTitle: '',
      imgUrl: '',
      array: [],
      keyName: ''
    },
    data() {
      return {
        imgHost: getApp().globalData.imguseHost
      };
    },
    methods: {
      bindPickerChange: function (e) {
        this.$emit('chooselist', e);
      },

      //关闭弹窗
      closeDialog() {
        this.$emit('closeDialog');
      }
    }
  };
</script>

<style>
  .chooselist {
    width: 120rpx;
    height: 120rpx;
    margin-top: 50rpx;
  }

  .chooselistTitle {
    font-size: 40rpx;
    color: #18b48e;
    letter-spacing: 4rpx;
    text-align: center;
    margin-top: 20rpx;
  }
</style>
