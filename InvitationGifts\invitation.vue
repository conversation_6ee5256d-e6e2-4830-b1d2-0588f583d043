<template>
  <view class="invitationBg">
    <view class="invitationBox">
      <image src="https://document.dxznjy.com/course/b463281ad7c7426e8598bf61a80ddc62.png" class="invitation" mode="aspectFit" show-menu-by-longpress></image>
    </view>
  </view>
</template>

<script></script>

<style lang="scss" scoped>
  .invitationBg {
    width: 100vw;
    height: 100vh;
    background: url('https://document.dxznjy.com/course/a561109c87ac431c8b74d876707d9292.jpg') no-repeat;
    background-size: 100%;
    overflow: hidden;
    background-color: #edf1fc;
  }
  .invitationBox {
    margin-top: 620rpx;
  }
  .invitation {
    display: flex;
    margin: 0 auto;
    width: 80%;
  }
</style>
