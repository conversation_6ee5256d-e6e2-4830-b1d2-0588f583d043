<template>
  <view class="subjectTest" v-if="item">
    <view class="title">
      {{ index + 1 }}.【{{ tyepName }}】
      <!-- <span v-html="latexHTML"></span> -->
      <!-- <rich-text :nodes="item.questionText"></rich-text> -->
      <view style="">
        <view v-for="(segment, index) in parseContent(item.questionText)" :key="index" style="display: inline; vertical-align: top">
          <text v-if="segment.type === 'text'" style="display: inline; vertical-align: top">{{ segment.content }}</text>
          <text v-else style="font-size: 14px; max-width: 600rpx; overflow: auto; display: inline; vertical-align: top" v-html="option(segment.content)"></text>
        </view>
        ({{ item.questionScore }}分)
      </view>
      <!-- <rich-text style="font-size: 16px" :nodes="latexHTML"></rich-text> -->
    </view>
    <view class="imgs" v-if="item.questionImage && item.questionImage.length">
      <image v-for="(item, index) in item.questionImage" :src="item" :key="index" style="width: 200rpx; height: 200rpx" @click="previewImage(item)"></image>
    </view>
    <view class="questions" v-for="(obj, index) in item.mathSmallQuestionList" :key="index">
      <view class="questionsTitle" v-if="item.mathSmallQuestionList.length > 1">
        <!-- <span style="margin-left: 10rpx">{{ }}</span> -->
        <view style="">
          {{ item.questionType == 1 ? tyepName + (index + 1) : '问' + (index + 1) }}:
          <view v-for="(segment, index) in parseContent(obj.question)" :key="index" style="display: inline; vertical-align: top">
            <text v-if="segment.type === 'text'" style="display: inline; vertical-align: top">{{ segment.content }}</text>
            <text v-else style="font-size: 14px; max-width: 600rpx; overflow: auto; display: inline; vertical-align: top" v-html="option(segment.content)"></text>
          </view>
        </view>
      </view>
      <view
        class="questionsItem"
        :class="answerList[index].answer == e.choiceOption && status !== 0 ? answerList[index].class : ''"
        @click="check(e, index)"
        v-for="(e, i) in obj.optionList"
        :key="i"
      >
        {{ e.choiceOption }}.
        <view style="">
          <view v-for="(segment, index) in parseContent(e.content)" :key="index" style="display: inline; vertical-align: top">
            <text v-if="segment.type === 'text'" style="display: inline; vertical-align: top">{{ segment.content }}</text>
            <text v-else style="font-size: 14px; max-width: 600rpx; overflow: auto; display: inline; vertical-align: top" v-html="option(segment.content)"></text>
          </view>
        </view>
        <!-- <rich-text style="font-size: 16px" :nodes="option()"></rich-text> -->
      </view>
      <view class="" v-if="status == 1 || status == 3">
        <view class="" v-if="photoTemp">
          <u-upload
            :fileList="answerList[index].fileList"
            @afterRead="(e) => afterRead(e, index)"
            @delete="(e) => deletePic(e, index)"
            multiple
            width="90"
            height="90"
            :maxCount="1"
          >
            <view class="uplaodImg">
              <image src="https://document.dxznjy.com/dxSelect/a2e85787-99da-41f7-b107-aecd64f6c190.png" style="width: 40rpx; height: 36rpx"></image>
              <view style="font-size: 22rpx; color: #616161">拍照上传答案</view>
            </view>
          </u-upload>
        </view>

        <view
          class="imgs"
          v-else
          :style="{
            display: obj.myAnswerImage ? 'flex' : 'none'
          }"
        >
          <image :src="obj.myAnswerImage" style="width: 180rpx; height: 180rpx" @click="previewImage(obj.myAnswerImage)"></image>
        </view>
      </view>
    </view>
    <view class="questionsTitle" v-if="status == 2" style="margin-top: 10rpx">
      正确答案：
      <span class="questionsRight">{{ item.correctAnswer }}</span>
      得分:
      <span class="questionsRight" style="margin-right: 0">{{ item.myScore }}分</span>
    </view>
    <view class="tools" v-if="status == 2">
      <view class="tool study" @click="goShowVideo">
        <image src="https://document.dxznjy.com/dxSelect/e95e055f-0cfd-4744-9dc4-c4cf77be03fa.png" style="width: 40rpx; height: 36rpx"></image>
        去学习
      </view>
      <view class="tool lookAnalysis" @click="look">查看解析</view>
    </view>
    <view class="" v-if="status == 1 || status == 3">
      <view class="photo" v-if="photoTemp">
        <view style="height: 42rpx; line-height: 42rpx; font-weight: bold; color: #000">拍照上传答案请注意以下几点哦</view>
        <view style="height: 42rpx; line-height: 42rpx">1.请保存卷面整洁</view>
        <view style="height: 42rpx; line-height: 42rpx">2.作答过程完整</view>
        <view style="height: 42rpx; line-height: 42rpx">3.单个题目的答案要在一张图片上</view>
        <view style="height: 42rpx; line-height: 42rpx">4.小题号书写规范，如(1) (2) (3)</view>
      </view>
    </view>

    <u-popup :show="show" @close="close" mode="bottom">
      <view class="analysis" v-if="show">
        <!-- todo 解析图片 -->
        <view class="imgs" v-if="item.analysisImg && item.analysisImg.length">
          <image v-for="(item, index) in item.analysisImg" :src="item" :key="index" style="width: 200rpx; height: 200rpx" @click="previewImage(item)"></image>
        </view>
        <view style="">
          <view v-for="(segment, index) in parseContent(item.analysis)" :key="index" style="display: inline; vertical-align: top">
            <text v-if="segment.type === 'text'" style="display: inline; vertical-align: top">{{ segment.content }}</text>
            <text v-else style="font-size: 14px; max-width: 600rpx; overflow: auto; display: inline; vertical-align: top" v-html="option(segment.content)"></text>
          </view>
        </view>
      </view>
    </u-popup>
  </view>
</template>

<script>
  // import { KatexVue } from 'katex-vue';
  import Config from '../../util/config.js';
  export default {
    props: {
      //题目
      item: {
        type: Object,
        default: () => {}
      },
      //索引
      index: {
        type: Number
      },
      //状态
      status: {
        type: Number,
        default: 0
      }
    },
    watch: {
      index: {
        handler(val) {
          this.init();
        },
        immediate: true // 页面加载时立即执行一次
      }
    },
    // components: {
    //   KatexVue
    // },
    data() {
      return {
        // a: ,
        show: false,
        baseUrl: uni.getStorageSync('baseUrl'),
        fileList: [],
        latexHTML: '',
        typeArr: [
          { name: '单选', id: 0 },
          { name: '填空', id: 1 },
          { name: '计算', id: 2 },
          { name: '解方程', id: 3 },
          { name: '证明题', id: 4 },
          { name: '几何综合题', id: 5 }
        ],
        tyepName: '',
        photoTemp: false,
        answerList: [
          {
            fileList: []
          }
        ]
      };
    },
    methods: {
      option(e) {
        return this.renderFormula(e);
      },
      goShowVideo(e) {
        this.$emit('goShowVideo', this.item.id);
      },
      check(e, i) {
        if (this.status == 2 || this.status == 0) return;
        this.answerList[i].answer = e.choiceOption;
        this.$forceUpdate();
        let a = this.answerList.map((e) => e.answer).join(',');
        this.$emit('checked', a);
        // console.log(this.answerList);
      },
      close() {
        this.show = false;
        this.$emit('look', false);
      },
      look() {
        this.show = true;
        this.$emit('look', true);
      },
      init() {
        this.answerList = [];
        this.tyepName = this.typeArr.find((e) => e.id == this.item.questionType).name;
        this.photoTemp = this.item.questionType != 0 && this.item.questionType != 1;
        this.latexHTML = this.renderFormula(this.item.questionText);
        // console.log(this.item.mathSmallQuestionList);
        this.item.mathSmallQuestionList.forEach((e, i) => {
          let obj = {
            index: i,
            answer: '',
            class: 'right',
            fileList: []
          };
          if (e.myAnswerImage) {
            obj.fileList[0] = { url: e.myAnswerImage };
          }
          this.answerList.push(obj);
        });

        if (this.item.myAnswer) {
          let arr = this.item.myAnswer.split(',');
          arr.forEach((e, i) => {
            this.answerList[i].answer = e;
          });
        }
        if (this.status == 2) {
          let arr2 = this.item.correctAnswer.split(',');
          this.answerList.forEach((e, i) => {
            if (e.answer != arr2[i]) {
              e.class = 'error';
            }
          });
        }
        console.log(this.answerList);
      },
      parseContent(e) {
        console.log(e);
        // 正则表达式匹配 \\(...\\)
        const regex = /\\\((.*?)\\\)/g;
        let lastIndex = 0;
        let match;
        const segments = [];

        // 循环匹配所有公式
        while ((match = regex.exec(e)) !== null) {
          // 添加前面的文本
          if (match.index > lastIndex) {
            segments.push({
              type: 'text',
              content: e.substring(lastIndex, match.index)
            });
          }

          // 添加公式
          segments.push({
            type: 'formula',
            content: match[1] // 获取括号内的内容
          });

          lastIndex = match.index + match[0].length;
        }

        // 添加剩余的文本
        if (lastIndex < e.length) {
          segments.push({
            type: 'text',
            content: e.substring(lastIndex)
          });
        }
        console.log(segments);
        return segments;
      },
      renderFormula(text) {
        try {
          // 转义百分号
          if (!text) return;
          text = text.replace(/%/g, '\\%');
          // 处理不完整的 \frac 表达式
          text = text.replace(/\\frac\{([^}]*)\}(?!\{)/g, (match, p1) => {
            return `\\frac{${p1}}{1}`;
          });
          // 处理分数
          text = text.replace(/-(\d+)(\d+)(\d+)(\d+)​/g, (match, p1, p2, p3, p4) => {
            return `-\\frac{${p1}${p2}}{${p3}${p4}}`;
          });
          text = text.replace(/(\d+)(\d+)(\d+)(\d+)​/g, (match, p1, p2, p3, p4) => {
            return `\\frac{${p1}${p2}}{${p3}${p4}}`;
          });

          // 修复特定的分数格式
          text = text.replace(/\\frac\{\\mathrm\{\\pi\}\}\{(\d+)\}/g, '\\frac{\\pi}{$1}');
          text = text.replace(/\\frac\{\\pi\{1\}\}\{(\d+)\}/g, '\\frac{\\pi}{$1}');
          text = text.replace(/\\frac\{([^}]*)\}\{(\d+)\}/g, '\\frac{$1}{$2}');

          // 处理不完整的分数
          text = text.replace(/\\frac{(\d+)}\{(\d+)}/g, '\\frac{$1}{$2}');

          // 处理特殊字符
          text = text.replace(/\\cdot\\cdot/g, '\\cdots');
          text = text.replace(/\\ldots/g, '\\dots');
          text = text.replace(/\\dot{([^}]*)}/g, '\\dot{$1}');

          // 处理带指数的根号
          text = text.replace(/\\sqrt\[(\d+)\]\{(\d+)\}/g, (match, p1, p2) => {
            return `\\sqrt[${p1}]{${p2}}`;
          });

          // 处理循环小数
          text = text.replace(/0\.(\d+)˙(\d+)˙(\d+)˙(\d+)˙/g, (match, p1, p2, p3, p4) => {
            return `0.\\dot{${p1}${p2}${p3}${p4}}`;
          });

          // 处理希腊字母
          text = text.replace(/\\mathrm\{\\pi\}/g, '\\pi');
          text = text.replace(/\\mathrm\{\\Pi\}/g, '\\Pi');
          text = text.replace(/ππ/g, '\\pi');

          // 处理省略号
          text = text.replace(/……/g, '\\dots');
          text = text.replace(/⋅⋅⋅⋅⋅⋅/g, '\\dots');

          // return text;
          // 使用 KaTeX 渲染
          const rendered = this.$katex.renderToString(text, {
            throwOnError: false,
            displayMode: false,
            strict: false,
            trust: true
          });

          // 如果渲染失败，尝试直接返回原始表达式
          if (rendered.includes('ParseError')) {
            return text;
          }
          // console.log(rendered);
          return rendered;
        } catch (error) {
          console.error('Formula rendering error:', error);
          return text;
        }
      },
      previewImage(currentUrl) {
        // 如果要预览多张图片，可以传入 urls 数组
        uni.previewImage({
          current: currentUrl, // 当前显示图片的链接
          urls: [currentUrl] // 需要预览的图片链接列表
        });
      },
      deletePic(event, index) {
        this.answerList[index][`fileList${event.name}`].splice(event.index, 1);
        this.$emit('addImgae', index, '');
      },
      // 新增图片
      async afterRead(event, index) {
        // 当设置 multiple 为 true 时, file 为数组格式，否则为对象格式
        let lists = [].concat(event.file);
        let fileListLen = this.answerList[index][`fileList${event.name}`].length;
        lists.map((item) => {
          this.answerList[index][`fileList${event.name}`].push({
            ...item,
            status: 'uploading',
            message: '上传中'
          });
        });
        for (let i = 0; i < lists.length; i++) {
          const result = await this.uploadFilePromise(lists[i].url);

          let item = this.answerList[index][`fileList${event.name}`][fileListLen];
          console.log(item);
          this.answerList[index][`fileList${event.name}`].splice(
            fileListLen,
            1,
            Object.assign(item, {
              status: 'success',
              message: '',
              url: result.fileUrl
            })
          );
          fileListLen++;
        }
        this.$emit('addImgae', index, this.answerList[index].fileList[0].url);
      },
      uploadFilePromise(url) {
        let that = this;
        let arrimg = [];
        // that.image=[]
        return new Promise((resolve, reject) => {
          let a = uni.uploadFile({
            url: `${this.baseUrl}zxAdminCourse/common/uploadFile`,
            filePath: url,
            name: 'file',
            formData: {
              user: 'test'
            },
            header: {
              Token: uni.getStorageSync('token')
            },
            success: (res) => {
              setTimeout(() => {
                let data = JSON.parse(res.data);
                resolve(data.data);
              }, 1000);
            }
          });
        });
      }
    }
  };
</script>

<style scoped lang="scss">
  // @import 'katex/dist/katex.min.css';
  .katex {
    display: flex;
    flex-wrap: wrap;
  }
  .analysis {
    padding: 20rpx 30rpx;
    background-color: #effbf6;
  }
  .tools {
    margin-top: 20rpx;
    height: 69rpx;
    padding-right: 26rpx;
    display: flex;
    justify-content: flex-end;
  }
  .u-icon__icon {
    font-size: 20px !important;
  }
  .u-upload__deletable {
    height: 24px !important;
    width: 24rpx !important;
  }
  .uplaodImg {
    width: 167rpx;
    height: 166rpx;
    background: #f7f7f7;
    border-radius: 10rpx;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
  }
  .tool {
    width: 196rpx;
    height: 69rpx;
    display: flex;
    justify-content: center;
    align-items: center;
    box-sizing: border-box;
    font-size: 32rpx;
    border-radius: 69rpx;
    color: #428a6f;
  }
  .study {
    border: 2rpx solid #428a6f;
  }
  .lookAnalysis {
    margin-left: 27rpx;
    background-color: #f7f7f7;
  }
  .right {
    border: 1rpx solid #94e6c7 !important;
    background: rgba(148, 230, 199, 0.15) !important;
    color: #31cf93 !important;
  }
  .error {
    border: 1rpx solid #ffaf85 !important;
    background: rgba(255, 172, 129, 0.1) !important;
    color: #ffaf85 !important;
  }
  .subjectTest {
    border-radius: 10rpx;
    background-color: #fff;
    margin-top: 20rpx;
    padding: 30rpx 20rpx;
    // overflow: auto;
    .title {
      width: 650rpx;
      margin: 0 auto;
      display: flex;
      flex-wrap: wrap;
    }
  }
  .imgs {
    display: flex;
    padding: 24rpx;
    background-color: #f7f7f7;
    // height: 300rpx;
    justify-content: space-around;
    flex-wrap: wrap; /* 允许子元素换行 */
  }
  .photo {
    height: 271rpx;
    background-color: #f6f6f6;
    padding: 28rpx 0 0 56rpx;
    font-size: 26rpx;
    margin-top: 20rpx;
    color: #999;
  }
  .questions {
    .questionsItem {
      margin: 20rpx auto 18rpx;
      min-height: 90rpx;
      width: 650rpx;
      line-height: 90rpx;
      padding-left: 30rpx;
      box-sizing: border-box;
      border: 1rpx solid #dfdfdf;
      border-radius: 10rpx;
      font-size: 32rpx;
      display: flex;
      color: #333333;
    }
  }
  .questionsTitle {
    padding-left: 20rpx;
    min-height: 80rpx;
    line-height: 80rpx;
    background-color: #fff5ef;
    margin-top: 20rpx;
    // padding-left: 72rpx;
    color: #999;
  }
  .questionsRight {
    color: #009e74;
    font-weight: bold;
    margin-right: 120rpx;
  }
</style>
