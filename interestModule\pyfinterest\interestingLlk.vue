<template>
  <div class="funContent">
    <!-- 连线 -->
    <canvas class="line" canvas-id="lineCanvas"></canvas>

    <interesting-head :title="titleText" @backPage="beforeleave" :hasTitleBg="true" :closeWhite="false" :hasRight="false"></interesting-head>
    <!-- 倒计时 -->
    <pyf-progress :showCountdown="false" :percent="((pageNumber - 1) * 100) / maxPageNumber"></pyf-progress>
    <!-- 提示 -->
    <view class="ppl_tip">你听到了什么？</view>
    <!-- 选项列表 -->
    <view class="answerListBox">
      <!-- 左 -->
      <view class="answerList left">
        <view v-for="(item, index) in leftList" :key="index" :style="item.isShow ? 'opacity: 1' : 'opacity: 0'">
          <!-- 云朵 -->
          <view v-if="isLeft && leftIndex === index && isSubmit && isCorrect" class="answerListSuccess itemView"></view>
          <view v-else :class="['answerListLeft', 'itemView', { error: isLeft && leftIndex === index && isSubmit && !isCorrect }]" @click="chooseAnswer(item, index, 'left')">
            <!-- <u-icon name="volume" color="#2c2c2c" size="128"></u-icon> -->
            <view class="word_list_play"></view>

            <view :class="leftIndex === index && isPlaying ? 'voice-scale voice-scale--move' : 'voice-scale'">
              <view class="item" v-for="(item, index) in 13" :key="index"></view>
            </view>
            <image
              :id="`left${index}`"
              :style="item.isShow ? 'opacity: 1' : 'opacity: 0'"
              :class="isLeft && leftIndex === index ? (isSubmit ? (isCorrect ? 'answerIconRight' : 'answerIconWrong') : 'answerIconSelected') : 'answerIcon'"
              mode=""
            ></image>
          </view>
        </view>
      </view>
      <!-- 右 -->
      <view class="answerList right">
        <view v-for="(item, index) in rightList" :key="index" :style="item.isShow ? 'opacity: 1' : 'opacity: 0'">
          <!-- 云朵 -->
          <view v-if="isRight && rightIndex === index && isSubmit && isCorrect" class="answerListSuccess itemView"></view>
          <view v-else :class="['answerListRight', 'itemView', { error: isRight && rightIndex === index && isSubmit && !isCorrect }]" @click="chooseAnswer(item, index, 'right')">
            <text class="answerText" :style="{ opacity: item.isShow ? '1' : '0', 'font-size': !item.text ? '' : item.text.length < 10 ? '36rpx' : '30rpx' }">
              {{ item.text }}
            </text>
            <image
              :id="`right${index}`"
              :style="item.isShow ? 'opacity: 1' : 'opacity: 0'"
              :class="isRight && rightIndex === index ? (isSubmit ? (isCorrect ? 'answerIconRight' : 'answerIconWrong') : 'answerIconSelected') : 'answerIcon'"
              mode=""
            ></image>
          </view>
        </view>
      </view>
    </view>
    <!--结束弹窗 -->
    <uni-popup ref="popopPower" type="center" :maskClick="false" :classBG="''">
      <interesting-dialog
        :conclusionText="'真是太棒啦~ 答题完成！'"
        :finishData="[
          { name: '答题时长', data: reportData.studyDuration + 's', iconIndex: 2 },
          { name: '答题时间', data: reportData.studyStartTime, iconIndex: 2 }
        ]"
        @close="closeDialog"
        @confirm="onceMore"
      ></interesting-dialog>
    </uni-popup>
    <!-- 退出弹框 -->
    <uni-popup ref="popopPowerExit" type="bottom" :maskClick="false" :classBG="''">
      <pyf-dialog-exit @close="closePopopPowerExit" @confirm="submitBackPage()"></pyf-dialog-exit>
    </uni-popup>
    <!-- 引导 -->
    <uni-popup ref="guideOne" type="bottom" :maskClick="true" :classBG="''">
      <view style="position: relative">
        <image style="height: 100vh; width: 100vw" src="https://document.dxznjy.com/course/7ec5988f1b9a4b2c8408f3f74d4057ae.png"></image>
        <image class="guide_btn_close" @click="guideClose()" src="#"></image>
      </view>
    </uni-popup>
    <!-- 处理所有返回 -->
    <page-container v-if="showPage" :show="showPage" :duration="false" :overlay="false" @beforeleave="beforeleave"></page-container>
  </div>
</template>

<script>
  import interestingHead from '../components/interesting-head/pyfInterestingHead.vue';
  import interestingDialog from '../components/interesting-dialog/pyfReview.vue';
  import pyfProgress from '../components/cmd-progress/pyf-progress.vue';
  import pyfDialogExit from '../components/interesting-dialog/pyfDialogExit.vue';
  // 一些共同方法
  import { countStudyDuration, submitData, getWordversion, getHeight, playWord, resetAudioContext, beforeleave, handleReset } from './pyfUtils';

  export default {
    components: {
      interestingHead,
      interestingDialog,
      pyfProgress,
      pyfDialogExit
    },
    data() {
      return {
        titleText: '连连看',
        // studyDurationTimer: null, // 学习时长定时器

        leftList: [], //当前答题选项/左/语音 / { text: '音频地址', isShow: '是否显示/true-显示 false-隐藏' }
        rightList: [], //当前题目选项/右/文字/ { text: '单词', isShow: 同上 }
        showListData: [], // 当前题目所有数据
        remainListData: [], // 本轮剩余题目数据
        showData: {}, //当前课程所有数据
        reportData: {}, // 报告数据

        pageNumber: 1, // 当前题目页数
        maxPageNumber: 1, // 当前题目页数

        leftIndex: null, // 当前点击的左选项
        rightIndex: null, // 当前点击的右选项
        leftCenter: {}, // 当前点击的左选项中心坐标 / {x: 0, y: 0}
        rightCenter: {}, // 当前点击的右选项中心坐标

        isPlaying: false, // 是否正在播放
        isSubmit: false, // 本题是否提交
        isCorrect: false, // 当前连线两项是否正确
        isLoading: false, // 是否加载中/防止重复点击

        isEnd: false, //当前玩法是否结束

        successList: [], //正确列表数据存储
        errorList: [], //错误列表数据存储

        isGuide: 0, //0未显示过引导-下一步 1知道了-已完成引导 2已完成引导
        screenHeight: 0,
        screenWidth: 0,
        showPage: true, // 返回保护

        timbre: 'W', // 音色默认女声 M  W
        pronunciationType: 0, // 1英式  0美式  默认美式
        playType: 2 // 版本
      };
    },
    computed: {
      // // 判断当前页 是否全部完成 /是否可以下一题
      isComplete() {
        return this.rightList.every((item) => !item.isShow);
      },
      // 是否左右选项都被选中
      isLeftRight() {
        return (this.rightIndex || this.rightIndex === 0) && (this.leftIndex || this.leftIndex === 0);
      },
      // 是否左边选项没有被选中
      isLeft() {
        return this.leftIndex || this.leftIndex === 0;
      },
      // 是否右边选项没有被选中
      isRight() {
        return this.rightIndex || this.rightIndex === 0;
      },
      // 点击左侧对应 总数据的索引
      realIndex() {
        return (this.leftIndex || 0) + 5 * (this.pageNumber - 1);
      }
  },

    onLoad(options) {
      // console.log(options, 'options');
      // this.$refs.popopPower.open(); // 查看报告弹框

      // 获取系统信息
      getHeight(this);
      // 获取当前课程信息
      this.showData = options;
      // 获取当前学员设置的语音版本信息
      getWordversion(this);
      // 获取引导进度
      this.isGuide = uni.getStorageSync('PyfLlkGuide');
      // console.log(this.isGuide, 'this.isGuide');

      if (!this.isGuide) {
        this.isGuide = 0;
      }
      if (this.isGuide == 0) {
        setTimeout(() => {
          this.$refs.guideOne.open();
        }, 100);
      }
      if (this.isGuide == 1) {
        // 初始化数据
        this.getShowData();
      }

      // 获取连线Canvas的上下文
      this.lineCanvasContext = uni.createCanvasContext('lineCanvas', this);
    },
    methods: {
      getShowData() {
        let that = this;
        this.showData.studyStatus = this.showData.studyStatus * 1; // 学习状态： 0-开始学习；1-继续学习；2-再来一轮
        this.showData.passType = 3; // 关卡类型： 1-听音识词；2-拼拼乐；3-连连看；4-规则大闯关
        // this.showData.studyStartTime = uni.$u.timeFormat(new Date(), 'yyyy-mm-dd hh:MM:ss'); // 学习开始时间
        // this.showData.studyDuration = uni.getStorageSync('PyfLlkStudyDuration') || 0; // 学习时长
        this.showData.studyDuration = 0; // 学习时长
        // 获取题目数据
        this.getNoLevelData();
      },
      getNoLevelData() {
        let that = this;
        // 路由参数
        let queryParams = uni.$u.queryParams({
          studentCode: that.showData.studentCode,
          courseCode: that.showData.courseCode,
          merchantCode: that.showData.merchantCode,
          passType: that.showData.passType, // 关卡类型： 1-听音识词；2-拼拼乐；3-连连看；4-规则大闯关
          studyStatus: that.showData.studyStatus // 学习状态： 0-开始学习；1-继续学习；2-再来一轮
        });
        // 获取复习单词
        that.$httpUser.get(`znyy/pd/mobile/funReview/findCourseWord${queryParams}`).then((res) => {
          if (!res.data.success) {
            uni.navigateBack();
            return;
          }
          if (!res.data.data || res.data.data.length == 0) {
            that.$refs.popopPower.open();
            return;
          }
          that.showListData = res.data.data;
          // 开始计时
          countStudyDuration(that, 1);
          // 保存当前关卡信息 - 拿到未学单词
          that.showData.pdFunReviewStudyWordSaveDtoList = []; // 保存当前关卡信息
          that.remainListData = []; // 未学单词
          that.showListData.forEach((item, index) => {
            if (item.studyStatus == 0) that.remainListData.push({ ...item, Sindex: index });
            /**
             * 保存当前关卡信息
             * word: 单词
             * wordAudioUrl: 单词音频
             * studyStatus: 学习状态：0-未学习；1-已学习
             *  */
            that.showData.pdFunReviewStudyWordSaveDtoList.push({
              word: item.wordSyllable,
              wordAudioUrl: JSON.stringify({ word: item.wordSyllableAudioUrl, list: item.splitList }),
              // wordAudioUrl: [item.wordSyllableAudioUrl, ...item.splitList],
              studyStatus: item.studyStatus,
              answerStatus: item.answerStatus || 0
            });
          });
          if (that.remainListData.length == 0) return that.$refs.popopPower.open();
          // console.log(that.qIdIndex, 'that.qIdIndex6666666');
          // 对答案进行操作
          that.wordExecute();
          // 拿到总页数
          that.maxPageNumber = Math.ceil(that.remainListData.length / 5);

          // console.log(that.showListData, '趣味复习');
        });
      },
      // 题目执行 打乱顺序
      wordExecute() {
        let that = this;
        // 打乱顺序
        // 重置答题列表和选项列表
        (that.leftList = []), (that.rightList = []);
        // 根据页码截取五道题
        let index = (that.pageNumber - 1) * 5;
        let wordRemarkList = that.remainListData.slice(index, index + 5);
        // console.log(wordRemarkList, 'wordRemarkList');
        // 左侧选项列表
        wordRemarkList.forEach((item) => {
          that.leftList.push({ text: item.wordSyllableAudioUrl, list: item.splitList, isShow: true });
        });
        // 随机
        that.$util.shuffleArray(wordRemarkList);
        // 右侧选项列表
        wordRemarkList.forEach((item) => {
          that.rightList.push({ text: item.wordSyllable, isShow: true });
        });
        that.isLoading = false; // 防止重复点击
      },
      // 计算元素的中心点坐标
      getCenter(direction) {
        const that = this;
        return new Promise((resolve, reject) => {
          const query = uni.createSelectorQuery().in(this);
          query
            .select(`#${direction + that[`${direction}Index`]}`)
            .boundingClientRect((rect) => {
              if (rect) {
                let Center = direction == 'left' ? that.leftCenter : that.rightCenter;
                Center.x = rect.left + rect.width / 2;
                Center.y = rect.top + rect.height / 2;
                resolve(rect);
              } else {
                reject(new Error('未找到指定节点'));
              }
            })
            .exec();
        });
      },
      // 在 Canvas 上绘制直线
      drawLine() {
        let that = this;
        var color = '#FFC6C6';
        if (this.isCorrect) {
          color = '#97FF6C';
        }
        const ctx = this.lineCanvasContext;
        // 清空Canvas
        ctx.clearRect(0, 0, this.screenWidth, this.screenHeight); // 根据Canvas的大小调整清空区域
        ctx.beginPath();
        // console.log(this.leftCenter, this.rightCenter, 'this.leftCenter, this.rightCenter');

        ctx.moveTo(this.leftCenter.x, this.leftCenter.y);
        ctx.lineTo(this.rightCenter.x, this.rightCenter.y);
        ctx.strokeStyle = color;
        ctx.lineWidth = 3;
        ctx.stroke(); // 绘制路径
        ctx.closePath(); // 关闭路径
        ctx.draw(); // 绘制到canvas上

        setTimeout(() => {
          if (that.isCorrect) {
            that.leftList[that.leftIndex].isShow = false;
            that.rightList[that.rightIndex].isShow = false;
            // 判断本页是否完成 可以下一题
            if (that.isComplete) {
              that.nextQuestion();
            }
          }
          that.isSubmit = false; // 重置提交状态
          that.isCorrect = false; // 重置正确状态
          that.leftIndex = null; // 重置左侧索引
          that.rightIndex = null; // 重置右侧索引
          // 清空Canvas
          that.clernDrawLine(); // 清空Canvas
        }, 600);
      },
      // 清空Canvas
      clernDrawLine() {
        this.isConnecting = false;
        const ctx = this.lineCanvasContext;
        ctx.clearRect(0, 0, this.screenWidth, this.screenHeight); // 根据Canvas的大小调整清空区域
        ctx.beginPath();
        ctx.moveTo(0, 0);
        ctx.lineTo(0, 0);
        ctx.strokeStyle = '#ffffff'; // 连线的颜色
        ctx.lineWidth = 0; // 连线的宽度
        ctx.stroke();
        ctx.closePath();
        ctx.draw();
      },
      // 选择选项
      /**
       *
       * @param item 选项
       * @param index 选项索引
       * @param direction 点击列表方向
       */
      async chooseAnswer(item, index, direction) {
        // console.log(item, index, direction, 'item, index, direction');

        let that = this;
        // 判断是否已完成/隐藏
        if (!item.isShow) {
          return;
        }
        if (that.isSubmit) {
          return that.$util.alter('连线中，请稍等', 'none', 1000);
        }
        // 来自于左侧
        if (direction == 'left') {
          resetAudioContext(); // 重置音频上下文
          // 判断是否已选择
          if (that.isLeft) {
            if (that.leftIndex == index) {
              that.leftIndex = null;
            } else {
              that.$util.alter('不可以重复选择左侧', 'none', 1000);
            }
          } else {
            // 读题
            that.playWord(item.text, item.list);
            that.leftIndex = index;
            // 获取元素中心点坐标
            await that.getCenter('left');
          }
        }
        // 来自于右侧
        if (direction == 'right') {
          // 判断是否已选择
          if (that.isRight) {
            if (that.rightIndex == index) {
              that.rightIndex = null;
            } else {
              that.$util.alter('不可以重复选择右侧', 'none', 1000);
            }
          } else {
            that.rightIndex = index;
            // 获取元素中心点坐标
            await that.getCenter('right');
          }
        }
        // 如果左右都已选-查看答案是否正确
        if (that.isLeftRight) {
          resetAudioContext(); // 重置音频上下文
          // 查取真正的索引
          let ind = that.realIndex;
          let isTrue = that.rightList[that.rightIndex].text == that.remainListData[ind].wordSyllable;
          that.isSubmit = true;

          if (isTrue) {
            that.isCorrect = true;
            // console.log('回答正确');
            that.$playVoice('task_success.mp3', true, that.timbre, that.pronunciationType, that.playType, that.showData.studentCode);
            that.isCorrect = true;
            that.drawLine(); // 绘制连线
            // 标记正确单词 + 下一题
            that.markRightWord();
          } else {
            that.isCorrect = false;
            // console.log('回答错误', that.showListData[ind].wordSyllable);
            that.$playVoice('ino.mp3', true, that.timbre, that.pronunciationType, that.playType, that.showData.studentCode);
            that.drawLine(); // 绘制连线
          }
        }
      },
      // 下一页
      nextQuestion() {
        let that = this;
        // 判断是否结束
        if (this.isEnd) {
          this.$util.alter('当前玩法已结束');
          return;
        }
        // 节流
        if (that.isLoading) {
          return that.$util.alter('题目加载中，请勿频繁点击');
        } else {
          that.isLoading = true;
        }
        resetAudioContext(); // 重置音频上下文

        if (that.pageNumber < that.maxPageNumber) {
          setTimeout(function () {
            that.pageNumber++;
            that.wordExecute(); // 执行题目
          }, 0);
        } else {
          // 计算学习时长
          countStudyDuration(that, 2);

          // 结束
          that.isEnd = true; // 结束
          submitData(that); // 提交趣味复习单词
        }
      },
      // 标记错误单词
      markWrongWord() {},
      // 标记正确单词
      markRightWord() {
        let that = this;

        // 标记
        let successItem = that.remainListData[that.realIndex];
        that.successList.push(successItem);
        // console.log('标记正确单词', that.successList);
        // 保存答题数据
        that.showData.pdFunReviewStudyWordSaveDtoList[that.remainListData[that.realIndex].Sindex].studyStatus = 1; // 是否学习：0-未学 1-已学
        that.showData.pdFunReviewStudyWordSaveDtoList[that.remainListData[that.realIndex].Sindex].answerStatus = 1; // 做题对错状态： 0-错；1-正确
      },
      //播放音频
      playWord(word, wordList) {
        playWord(this, word, wordList);
      },
      // 查看报告弹框关闭
      closeDialog() {
        this.showPage = false;
        uni.navigateBack({ delta: 1 });

        this.$refs.popopPower.close();
      },
      // 查看报告弹框确定
      onceMore() {
        // 重置
        handleReset(this, this.showData.passType);
        this.showData.studyStatus = 2; // 学习状态： 0-开始学习；1-继续学习；2-再来一轮
        this.getNoLevelData(); // 获取题目数据
        this.$refs.popopPower.close();
      },
      // 关闭是否退出弹框
      closePopopPowerExit() {
        this.showPage = true;
        this.$refs.popopPowerExit.close();
      },
      // 返回上一页
      backPage() {
        uni.navigateBack({ delta: 1 });
      },
      /** 提交答题数据并返回上一页。
       * 提交答题数据并返回上一页。
       * 该方法调用 submitData 函数来处理数据提交。
       */
      submitBackPage(isSystemBack) {
        // 计算学习时长
        countStudyDuration(this, 2);

        submitData(this, false, isSystemBack); // 提交答题数据并返回上一页
      },
      // 返回
      beforeleave() {
        this.isPlaying = false; // 是否还在播放音频/false结束播放
        beforeleave(this);
      },
      // 引导
      async guideClose() {
        this.isGuide = 1;
        await uni.setStorageSync('PyfLlkGuide', this.isGuide);
        this.$refs.guideOne.close();
        this.getShowData();
      }
    },
    async onUnload() {
      this.submitBackPage(true);
      // console.log('--页面关闭后销毁实例--');
      // 页面关闭后销毁实例
      resetAudioContext(); // 重置音频上下文
    }
  };
</script>
<style>
  page {
    height: 100vh;
    padding: 0;
  }
</style>
<style lang="scss" scoped>
  $color: #7cb343; //主要绿色

  .funContent {
    width: 100%;
    padding: 0 30rpx;
    box-sizing: border-box;
    height: 100vh;

    background: url('https://document.dxznjy.com/course/d9332d0c5c954658a1f64675f79c336a.png') 100% 100% no-repeat;
    background-size: 100% 100%;
    position: relative;
    overflow: hidden;
  }
  .ppl_tip {
    height: 230rpx;
    padding: 43rpx 0 0 43rpx;
    box-sizing: border-box;
    font-weight: bold;
    color: #333;
    font-size: 32rpx;

    position: absolute;
    top: 16%;
  }
  .word_list_play {
    width: 44rpx;
    height: 44rpx;
    margin-right: 10rpx;
    background: url('https://document.dxznjy.com/course/48157a5e123b42d39f6f72bf8f1039e7.png') 100% 100% no-repeat;
    background-size: 100% 100%;
  }
  /deep/.cmd-progress-anim {
    background-image: none !important;
  }

  .answerListBox {
    position: absolute;
    top: 26%;
    z-index: 1;
    width: 690rpx;
    height: 50vh;
    min-height: 600rpx;
    margin: 10rpx auto 0 auto;
  }

  .answerList {
    position: absolute;
    z-index: 1;
    width: 292rpx;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-around;

    margin-top: 10rpx;

    font-weight: bold;
    color: #444;
    font-size: 40rpx;

    &.left {
      left: 0;
    }
    &.right {
      right: 0;
    }

    .answerListSuccess {
      background: url('https://document.dxznjy.com/course/fd88abcb44874a2cb8de566e149ac65a.png') 50% 50% no-repeat;
      background-size: 46% auto;
    }
  }

  .itemView {
    width: 292rpx;
    height: 104rpx;
    box-sizing: border-box;
    position: relative;
    display: grid;
    place-items: center; /* 居中 */
    // background: #fff7c7;
    border-radius: 30rpx;
  }
  .answerListLeft {
    background: url('https://document.dxznjy.com/course/2697f491ba494a4a932645e3d0a6a5f9.png') 100% 100% no-repeat;
    background-size: 100% 100%;
    display: flex;
    justify-content: center;

    &.error {
      background: url('https://document.dxznjy.com/course/3502610f583147b4b57d8cad8e8c952c.png') 100% 100% no-repeat;
      background-size: 100% 100%;
    }

    image {
      position: absolute;
      z-index: 1;
      right: -15rpx;
      width: 30rpx;
      height: 30rpx;
      border-radius: 50%;
    }
  }
  .answerListRight {
    background: url('https://document.dxznjy.com/course/8a33c40b702e419980555b7c55ff8f91.png') 100% 100% no-repeat;
    background-size: 100% 100%;

    word-break: break-word; /* 允许单词内换行 */
    overflow-wrap: break-word; /* 标准属性，效果类似 */

    &.error {
      background: url('https://document.dxznjy.com/course/6ec1ce7f2ade4de6ba806c8ccddf0513.png') 100% 100% no-repeat;
      background-size: 100% 100%;
    }

    image {
      position: absolute;
      z-index: 1;
      left: -15rpx;
      width: 30rpx;
      height: 30rpx;
      border-radius: 50%;
    }
  }

  .answerText {
    font-weight: bold;
    color: #555555;
    position: absolute;

    width: 100%;
    box-sizing: border-box;
    padding: 0 26rpx;

    display: grid;
    place-items: center; /* 居中 */
  }

  .answerIcon {
    background: url('https://document.dxznjy.com/applet/newInteresting/interesting_llk_normal.png') 100% 100% no-repeat;
    background-size: 100% 100%;
  }
  .answerIconSelected {
    background: $color;
    background-size: 100% 100%;
  }
  .answerIconWrong {
    background: url('https://document.dxznjy.com/applet/newInteresting/interesting_llk_wrong.png') 100% 100% no-repeat;
    background-size: 100% 100%;
  }
  .answerIconRight {
    background: url('https://document.dxznjy.com/applet/newInteresting/interesting_llk_tight.png') 100% 100% no-repeat;
    background-size: 100% 100%;
  }

  .guide_btn_next {
    position: absolute;
    bottom: 57rpx;
    right: 64rpx;
    width: 269rpx;
    height: 142rpx;
  }

  .guide_btn_close {
    position: absolute;
    bottom: 171rpx;
    right: 125rpx;
    width: 312rpx;
    height: 93rpx;
  }

  .line {
    position: absolute;
    z-index: 0;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
  }
  // 波浪效果
  $voice-scale-width: 150rpx;
  $voice-scale-height: 45rpx;
  $item-width: 7rpx;
  $item-radius: 3.5rp;
  // 定义不同柱子的高度和动画延迟
  $heights: (0.25, 0.25, 0.55, 0.35, 1, 0.35, 0.55, 1, 1, 0.55, 0.55, 0.35, 0.25);
  $delays: (0.8s, 0.6s, 0.4s, 0.2s, 0s, 0.2s, 0.4s, 0.6s, 0.8s, 1s, 0.8s, 0.6s, 0.4s);

  // 基础样式
  .voice-scale {
    width: $voice-scale-width;
    height: $voice-scale-height;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .item {
      display: block;
      width: $item-width;
      border-radius: $item-radius;
      background: $color;
      transition: all 0.5s;

      @for $i from 1 through 13 {
        &:nth-child(#{$i}) {
          height: nth($heights, $i) * 100%;
        }
      }
    }

    // 动态效果模式
    &.voice-scale--move {
      .item {
        animation: load 1s infinite linear;
        @for $i from 1 through 13 {
          // 动态模式下的动画延迟
          &:nth-child(#{$i}) {
            height: 10%;
            animation-delay: nth($delays, $i);
          }
        }
      }
    }
  }

  @keyframes load {
    0% {
      height: 10%;
    }

    50% {
      height: 100%;
    }

    100% {
      height: 10%;
    }
  }
  @media (min-width: 500px) {
    .ppl_tip {
      top: 19%;
    }
    .answerListBox {
      top: 31.5%;
    }
  }
</style>
