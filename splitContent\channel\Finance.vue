<template>
    <view>
        <view class="mlr-30">
            <!-- 时间筛选 -->
            <view class="bg-ff p-30 flex_s f-30 c-66 radius-15 choose_time">
                <text>时间：</text>
				<uniDatetimePicker v-model="range" type="daterange" @change="change"/>
				<view class="icon">
					<u-icon name="arrow-down" color="#C7C7C7"></u-icon>
				</view>
                <!-- <view class="flexbox time">
                    <view class="c-00" v-if="startTime!=''">{{startTime}} 至 {{endTime}}</view>
                    <view class="c-99" v-else>开始时间-结束时间</view>
                    <u-icon name="arrow-down" color="#C8C8C8" size="28"></u-icon>
                </view> -->
            </view>
            <view class="bg-ff radius-15 bg-ff mt-30 p-30" v-for="(item,index) in listS.list" :key="index">
                <text class="f-32 c-00 lh-60">
                    购买人：{{ item.buyerName }}
                </text>
                <view class="flexbox f-32 c-00 mt-20">
                    <view>订单价格：{{ item.stockPrice}}</view>
                    <view>分账金额：<text class="color_tangerine f-34 bold">￥{{ item.accountPrice}}</text></view>
                </view>
                <view class="mt-45 f-30 c-66">购买时间：{{ item.payTime }}</view>
            </view>
        </view>
        <view v-if="listS.list != undefined && listS.list.length==0" class="t-c flex-col"
            :style="{height: useHeight+'rpx'}">
            <!-- <image src="/static/cart/no_data.png" mode="widthFix" class="mb-20 img_s"></image> -->
            <image :src="imgHost+'alading/correcting/no_data.png'" class="mb-20 img_s" mode="widthFix"></image>
            <view style="color: #BDBDBD;">暂无数据</view>
        </view>
        <view v-if="no_more && listS.list != undefined && listS.list.length>0">
            <u-divider text="到底了"></u-divider>
        </view>
    </view>
</template>

<script>
    const {
        $navigationTo,
        $showError,
        $http,
    } = require("@/util/methods.js")
    import uniDatetimePicker from '../components/uni-datetime-picker/components/uni-datetime-picker/uni-datetime-picker.vue'
    export default {
        components:{
            uniDatetimePicker
        },
        data() {
            return {
                startTime: '',
                endTime: '',
                page: 1,
                no_more: false,
                listS: {},
                useHeight: 0, //除头部之外高度
                imgHost: getApp().globalData.imgsomeHost,
            }
        },

        onLoad() {

        },
        onReady() {
            uni.getSystemInfo({
                success: (res) => {
                    // 可使用窗口高度，将px转换rpx
                    let h = (res.windowHeight * (750 / res.windowWidth));
                    this.useHeight = h - 146;
                }
            })
        },
        onShow() {
            this.list()
        },
        onReachBottom() {
            if (this.page >= this.listS.totalPage) {
                this.no_more = true
                return false;
            }
            this.list(true, ++this.page);
        },
        methods: {
            // 日期选择
			change(e){
				console.log(e)
				this.startTime = e[0];
				this.endTime = e[1];
				this.screen();
			},
    
            screen() {
                if (this.endTime < this.startTime) {
                    uni.showToast({
                        title: '结束时间应大于开始时间',
                        icon: "none"
                    })
                    return false
                }
                this.list()
                this.page = 1
                this.no_more = false
            },
            bindDateChange(e) {
                this.startTime = e.detail.value
            },
            bindDateChange1(e) {
                this.endTime = e.detail.value
            },
            async list(isPage, page) {
                let _this = this
                const res = await $http({
                    url: 'zx/user/merchantFinanceList',
                    data: {
                        startTime: _this.startTime, //用户渠道商id
                        endTime: _this.endTime, //渠道商等级
                        page: page || 1
                    }
                })
                if (res) {
                    if (isPage) {
                        let old = _this.listS.list
                        _this.listS.list = [...old, ...res.data.list]
                    } else {
                        _this.listS = res.data
                    }
                }
            },
        }
    }
</script>

<style scoped lang="scss">
    .time {
        width: 477rpx;
        height: 60rpx;
        border: 1rpx solid #C8C8C8;
        display: inline-flex;
        flex-direction: row;
        align-items: center;
        border-radius: 35rpx;
        padding: 0 30rpx;
    }


    .flex_s {
        display: flex;
        align-items: center;
    }

    .img_s {
        width: 160rpx;
    }

    /deep/.u-calendar-month__days__day__select__buttom-info {
        bottom: 5rpx !important;
    }
	
	/deep/.uni-date-x--border{
		border-radius: 45rpx !important;
	}
	
	/deep/.uni-date-x{
		background-color: transparent !important;
	}
	
	/deep/.uni-icons{
		color: #fff !important;
	}
	
	/deep/.uni-date-x{
		flex: inherit !important;
	}
	
	/deep/.uni-date__x-input{
		margin-right: 30rpx !important;
	}
	/deep/.range-separator{
		margin-right: 30rpx !important;
	}
	
	.choose_time{
		position: relative;
		width: 92%;
	}
	
	.icon{
		position: absolute;
		right: 55rpx;
	}
</style>