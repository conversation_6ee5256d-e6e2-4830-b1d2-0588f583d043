<template>
    <view>
        <view class="relative flex-dir-row">
            <image :src="imgHost+'alading/correcting/channel_bgc.png'" class="wh100 channel_bgc" mode="widthFix">
            </image>
            <view class="t-c f-34 mt-80 highest w100 positionRelative">
                <view class="positionAbsolute ml-30" @click="back">
                    <uni-icons type="back" size="22" color="#fff"></uni-icons>
                </view>
                <view class="c-ff">渠道商中心</view>
            </view>
            <view class="time f-30" @click="show=true">
                <!-- <picker class="flex-box ml-20" mode="date" :value="date" @change="bindDateChange">
					<view class="timePicker">{{date || '日期筛选'}}</view>
				</picker> -->
                <view class="c-00 f-30">{{date || '日期筛选'}}</view>
                <u-datetime-picker ref="datetimePicker" :show="show" v-model="value" mode="date" confirmColor="#EA6031"
                    itemHeight="68" @confirm="confirm" @cancel="close"></u-datetime-picker>
            </view>
            <view class="channeltop">
                <view class="flex c-ff" :class="info?'mt-40':''">
                    <view class="flex-box flex-col">
                        <text class="f-30 bold mb-10">{{ info.parentsNum }}</text>
                        <text class="f-26">家长数</text>
                    </view>
                    <view class="flex-box flex-col">
                        <text class="f-30 bold mb-10">{{ info.totalStockPrice }}</text>
                        <text class="f-26">销售额</text>
                    </view>
                    <view class="flex-box flex-col">
                        <text class="f-30 bold mb-10">{{ info.memberNum }}</text>
                        <text class="f-26">会员数</text>
                    </view>
                </view>
            </view>
        </view>
        <view class="workbench">
            <!-- <view class="f-32 bold p-30">工作台</view> -->
            <view class="flex_c">
                <view class="flex-col ml-50" @tap="skintap('pages/home/<USER>/customer')">
                    <image :src="imgHost+'alading/correcting/channel_custom.png'" class="box-70"></image>
                    <text class="f-28 mt-10 c-ff">客户列表</text>
                </view>
                <view class="flex-col ml-50" @tap="skintap('pages/home/<USER>/qdsList')">
                    <image :src="imgHost+'alading/correcting/channel_list.png'" class="box-70"></image>
                    <text class="f-28 mt-10 c-ff">渠道商列表</text>
                </view>
                <!-- <view class="flex-col flex-box" @tap="skintap('pages/home/<USER>/qd_apply')">
					<image src="/static/user/sq1.png" class="box-60"></image>
					<text class="f-26 mt-20">渠道商申请</text>
				</view> -->
                <view class="flex-col ml-50" @tap="skintap('pages/home/<USER>/Finance')">
                    <image :src="imgHost+'alading/correcting/channel_money.png'" class="box-70"></image>
                    <text class="f-28 mt-10 c-ff">财务管理</text>
                </view>
            </view>
        </view>
    </view>
</template>

<script>
    const {
        $navigationTo,
        $showError,
        $http
    } = require("@/util/methods.js")
    import dayjs from "dayjs";
    export default {
        data() {
            return {
                date: '', // 选择的日期
                info: {},
                show: false, //控制日期打开关闭
                value: Number(new Date()),
                imgHost: getApp().globalData.imgsomeHost,
                useHeight: 0, //除头部之外高度
            }
        },
        onReady() {
            uni.getSystemInfo({
                success: (res) => {
                    // 可使用窗口高度，将px转换rpx
                    let h = (res.windowHeight * (750 / res.windowWidth));
                    this.useHeight = h - 95;
                }
            })
            // 微信小程序需要用此写法
            // this.$refs.datetimePicker.setFormatter(this.formatter)
        },
        onShow() {
            this.channelData()
        },
        methods: {
            // bindDateChange(e) {
            //     this.date = e.detail.value
            //     this.channelData()
            // },
            async channelData() {
                let _this = this
                const res = await $http({
                    url: 'zx/user/merchantDataStatistics',
                    data: {
                        searchTime: _this.date
                    }
                })
                if (res) {
                    _this.pageShow = false
                    _this.info = res.data
                }
            },
            back() {
                uni.navigateBack()
            },
            skintap(url) {
                $navigationTo(url)
            },

            confirm(e) {
                this.date = dayjs(e.value).format('YYYY-MM-DD');
                this.show = false;
				this.channelData()
            },

            close() {
                this.show = false;
            }

            // formatter(type, value) {
            // 	if (type === 'year') {
            // 		return `${value}年`
            // 	}
            // 	if (type === 'month') {
            // 		return `${value}月`
            // 	}
            // 	if (type === 'day') {
            // 		return `${value}日`
            // 	}
            // 	return value
            // },
        }
    }
</script>
<style>
    page {
        background-color: #202028;
    }
</style>
<style lang="scss" scoped>
    .channel_bgc {
        position: fixed;
    }

    .highest {
        z-index: 1;
    }

    .workbench {
        width: 100%;
        position: relative;
        margin-top: 380rpx;
    }

    .time {
        background: linear-gradient(to right, #fbf4eb, #E8B97A);
        position: absolute;
        top: 140rpx;
        left: 50rpx;
        width: 180rpx;
        // padding: 0 32rpx;
        height: 60rpx;
        text-align: center;
        line-height: 60rpx;
        border-radius: 45rpx;
    }

    .channeltop {
        position: absolute;
        width: 100%;
        height: 100rpx;
        top: 290rpx;
        left: 0;
    }

    /deep/.u-toolbar {
        border-bottom: 1px solid #e0e0e0;
    }

    /deep/.u-line-1 {
        line-height: 68rpx !important;
        background-color: #F4F4F4 !important;
    }

    /deep/.u-picker__view {
        height: 440rpx !important;
    }

    /deep/.u-popup__content {
        margin: 20rpx;
        border-radius: 15rpx;
    }

    .flex_c {
        display: flex;
        align-items: center;
        justify-content: flex-start;
    }
</style>