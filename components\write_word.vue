<template>
  <view class="writeWord_dialog_box">
    <view class="word_write" style="height: 480rpx">
      <input
        type="text"
        class="write_input"
        :class="animationStyle"
        :style="right == false ? 'color:#EA645B' : ''"
        :focus="autofocus"
        @blur="poinblur"
        value=""
        v-model="writeWord"
      />
      <view>
        <text v-if="right == false && showWord" class="wronword">{{ word }}</text>
        <image class="rightIcon" v-if="right == true" src="https://document.dxznjy.com/applet/interesting/write_right.png" mode=""></image>
      </view>
    </view>

    <view class="showTip" :style="showTip ? 'opacity:1' : 'opacity:0'">
      <text>{{ wordTranslation }}</text>
    </view>

    <view class="write_word_btnGrop">
      <view class="write_btn" @click="closeWrite">
        <image src="https://document.dxznjy.com/dxSelect/image/review_close_icon.png" mode=""></image>
      </view>
      <view class="write_btn" @click="showTipClick">
        <image v-if="!showTip" src="https://document.dxznjy.com/dxSelect/image/tip_default.png" mode=""></image>
        <image v-if="showTip" src="https://document.dxznjy.com/dxSelect/image/tip_active.png" mode=""></image>
      </view>
      <view class="write_btn" @click="confirm">
        <image src="https://document.dxznjy.com/dxSelect/image/review_right_icon.png" mode=""></image>
      </view>
    </view>
  </view>
</template>

<script>
  export default {
    name: 'write_word',
    props: { word: '', wordTranslation: '' },
    data() {
      return {
        right: null,
        showWord: false,
        writeWord: '',
        showTip: false,
        animationStyle: '',
        autofocus: true,
        isCanClick: true //是否可以点击
      };
    },
    methods: {
      poinblur: function () {
        this.autofocus = false;
        this.$nextTick(() => {
          this.autofocus = true;
        });
      },

      closeWrite() {
        this.$emit('closeWrite');
      },

      showTipClick() {
        let that = this;
        that.showTip = !that.showTip;
        if (!that.autofocus) {
          that.poinblur();
        }
        if (that.showTip) {
          setTimeout(function () {
            that.showTip = !that.showTip;
          }, 4000);
        }
      },

      confirm() {
        if (!this.isCanClick) return;
        let that = this;
        that.isCanClick = false;
        if (that.writeWord == '') {
          uni.showToast({
            title: '请输入单词',
            icon: 'none',
            position: 'bottom'
          });
        } else {
          if (that.writeWord != that.word) {
            that.right = false;
            that.showWord = true;
            that.animationStyle = 'animation1';
            setTimeout(function () {
              that.animationStyle = '';
            }, 1000);
            setTimeout(function () {
              that.showWord = false;
              that.isCanClick = true;
            }, 4000);
          } else {
            that.right = true;
            that.isCanClick = true;
          }
        }
      }
    }
  };
</script>

<style>
  .animation1 {
    animation: shake 0.3s !important;
  }

  @keyframes shake {
    0%,
    100% {
      transform: translateX(0);
    }

    10% {
      transform: translateX(-9px);
    }

    20% {
      transform: translateX(8px);
    }

    30% {
      transform: translateX(-7px);
    }

    40% {
      transform: translateX(6px);
    }

    50% {
      transform: translateX(-5px);
    }

    60% {
      transform: translateX(4px);
    }

    70% {
      transform: translateX(-3px);
    }

    80% {
      transform: translateX(2px);
    }

    90% {
      transform: translateX(-1px);
    }
  }
</style>
