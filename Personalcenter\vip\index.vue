<template>
  <view>
    <view class="col-12 relative" style="position: fixed; top: 0">
      <view class="col-12 personl_header_bg"></view>
      <view class="icon_img" @click="skintap('pages/home/<USER>/index')">
        <uni-icons type="left" size="20"></uni-icons>
      </view>
      <view class="page_title bold flex-col">
        <text>会员中心</text>
      </view>
      <view class="viptop home_bg">
        <view class="flex-dir-row flex-x-b pb-112">
          <view class="flex-box flex-dir-row pl-30">
            <view class="box-136 radius-50">
              <image :src="userinfo.headPortrait || avaUrl" class="wh100"></image>
            </view>
            <view class="flex-col-s ml-30 c-00">
              <view class="f-34 bold mb-25">{{ userinfo.nickName }}</view>
              <view class="f-28 c-66">{{ userinfo.mobile }}</view>
            </view>
          </view>
          <!-- v-if="userinfo.identityType==0||userinfo.identityType==1" -->
          <view class="apply1 f-30 c-ff pl-40 pr-50" @click="skintap('Personalcenter/vip/apply')">申请成为渠道商</view>
        </view>
      </view>
    </view>
    <view class="home_con" :style="{ height: useHeight + 'rpx' }">
      <view class="personal_withdraw bg-ff radius-15 flex_s">
        <view class="flex radius-15">
          <view class="flex-col flex-box">
            <text class="f-32 bold mb-15 color_tangerine">¥{{ userinfo.withdrawAmount }}</text>
            <text class="f-28 c-66">可提现金额</text>
          </view>
          <view class="flex-col flex-box">
            <text class="f-32 bold mb-15 color_tangerine">¥{{ userinfo.totalCommissionAmount }}</text>
            <text class="f-28 c-66">累计佣金金额</text>
          </view>
          <view class="flex-col flex-box">
            <text class="f-32 bold mb-15 color_tangerine">¥{{ userinfo.freezeAmount }}</text>
            <text class="f-28 c-66">待结算金额</text>
          </view>
        </view>
        <view class="Withdrawal f-30 bold c-ff t-c" @click="skintap('Personalcenter/vip/Withdrawal')">申请提现</view>
      </view>
      <view class="bg-ff radius-15 ptb-40 f-28 c-66" style="margin-top: 190rpx">
        <view class="flex-s flex-x-a">
          <view class="t-c" @click="skintap('Personalcenter/vip/customer')">
            <image :src="imgHost + 'alading/correcting/vip_tab_khlb.png'" class="wid68" mode="widthFix"></image>
            <view>客户列表</view>
          </view>
          <view class="t-c" @click="skintap('Personalcenter/vip/commission')">
            <image :src="imgHost + 'alading/correcting/vip_tab_yjjl.png'" class="wid68" mode="widthFix"></image>
            <view>佣金记录</view>
          </view>
          <view class="t-c" @click="skintap('Personalcenter/vip/With_List')">
            <image :src="imgHost + 'alading/correcting/vip_tab_txjl.png'" class="wid68" mode="widthFix"></image>
            <view>提现记录</view>
          </view>
          <view class="t-c" @click="skintap('pages/home/<USER>/order')">
            <image :src="imgHost + 'alading/correcting/vip_tab_tcdd.png'" class="wid68" mode="widthFix"></image>
            <view>套餐订单</view>
          </view>
        </view>
        <!-- <view class="flex" @click="skintap('Personalcenter/vip/customer')">
					<image src="/static/my/guazhangkehuliebiao.png" class="wid36 mr-20" mode="widthFix"></image>
					<view class="flex-box b-b flex lh-100">
						<text class="f-28 c-22">客户列表</text>
						<image src="/static/user/arrow.png" class="arrow" mode="widthFix"></image>
					</view>
				</view>
				<view class="flex" @click="skintap('Personalcenter/vip/commission')">
					<image src="/static/my/wodeyongjin1.png" class="wid36 mr-20" mode="widthFix"></image>
					<view class="flex-box b-b flex lh-100">
						<text class="f-28 c-22">佣金记录</text>
						<image src="/static/user/arrow.png" class="arrow" mode="widthFix"></image>
					</view>
				</view>
				<view class="flex" @click="skintap('Personalcenter/vip/With_List')">
					<image src="/static/my/tixianjilu.png" class="wid36 mr-20" mode="widthFix"></image>
					<view class="flex-box b-b flex lh-100">
						<text class="f-28 c-22">提现记录</text>
						<image src="/static/user/arrow.png" class="arrow" mode="widthFix"></image>
					</view>
				</view>
				<view class="flex" @click="skintap('pages/home/<USER>/order')">
					<image src="/static/my/cangpeitubiao_shangpin.png" class="wid36 mr-20" mode="widthFix"></image>
					<view class="flex-box b-b flex lh-100">
						<text class="f-28 c-22">套餐订单</text>
						<image src="/static/user/arrow.png" class="arrow" mode="widthFix"></image>
					</view>
				</view>
				<view class="flex" @click="skintap('Personalcenter/vip/qd_apply')">
					<image src="/static/my/shenqing.png" class="wid36 mr-20" mode="widthFix"></image>
					<view class="flex-box b-b flex lh-100">
						<text class="f-28 c-22">渠道商申请</text>
						<image src="/static/user/arrow.png" class="arrow" mode="widthFix"></image>
					</view>
				</view> -->
      </view>
    </view>
    <!-- <view class="mlr-30" style="margin-top: 20rpx;">
	    	<view class="bg-ff plr-20 radius-20">
	    		<view class="flex" @click="skintap('pages/home/<USER>/index')">
	    			<image src="/static/my/jishufuwushang.png" class="wid68 mr-20" mode="widthFix"></image>
	    			<view class="flex-box flex lh-100">
	    				<text class="f-28 c-22">渠道服务商</text>
	    				<image src="/static/user/arrow.png" class="arrow" mode="widthFix"></image>
	    			</view>
	    		</view>
	    	</view>
	    </view> -->
  </view>
</template>

<script>
  import Util from '@/util/util.js';
  const { $navigationTo, $http } = require('@/util/methods.js');
  let count;
  export default {
    data() {
      return {
        imgHost: getApp().globalData.imgsomeHost,
        userinfo: {},
        avaUrl: Util.getCachedPic('https://document.dxznjy.com/dxSelect/home_avaUrl.png', 'home_avaUrl_path'),
        useHeight: 0 //除头部之外高度
      };
    },
    onReady() {
      uni.getSystemInfo({
        success: (res) => {
          // 可使用窗口高度，将px转换rpx
          let h = res.windowHeight * (750 / res.windowWidth);
          this.useHeight = h - 490;
        }
      });
    },
    onShow() {
      this.homeData();
    },
    methods: {
      // 获取首页信息
      async homeData() {
        let _this = this;
        const res = await $http({
          url: 'zx/user/userInfoNew'
        });
        if (res) {
          _this.pageShow = false;
          _this.userinfo = res.data;
        }
      },
      skintap(url) {
        $navigationTo(url);
      }
    }
  };
</script>

<style lang="scss" scoped>
  .page_title {
    position: absolute;
    top: 80upx;
    width: 100%;
    text-align: center;
  }
  .Topbar {
    width: 100%;
    position: absolute;
    top: 0;
    left: 0;
  }
  .apply1 {
    background-image: linear-gradient(to right, #fcc5b1, #ea6531);
    height: 60rpx;
    border-radius: 45rpx 0 0 45rpx;
    line-height: 60rpx;
    text-align: center;
  }

  .viptop {
    width: 100%;
    position: absolute;
    left: 0;
    bottom: 100rpx;
  }

  .wid68 {
    width: 68upx;
  }

  .personl_header_bg {
    height: 462upx;
    background: linear-gradient(180deg, #ffb7ab 0%, #ffe2dd 100%);
  }

  .personal_withdraw {
    width: 690upx;
    height: 285upx;
    position: absolute;
    top: -96upx;
    left: 30upx;
    box-sizing: border-box;
    backdrop-filter: blur(10upx);
    z-index: 999;
  }
  .flex_s {
    display: flex;
    align-items: inherit;
    flex-direction: column;
    justify-content: center;
  }
  .home_bg {
    width: 100%;
    height: 160rpx;
    position: absolute;
    top: 165rpx;
    left: 0;
  }

  .icon_img {
    position: absolute;
    top: 80rpx;
    left: 30rpx;
    z-index: 999 !important;
  }

  .flex-col-s {
    display: flex;
    flex-direction: column;
    justify-content: center;
  }

  .home_con {
    position: relative;
    margin-top: 442upx;
    border-radius: 20upx 20upx 0 0;
    padding: 30upx;
    background-color: #f3f8fc;
  }

  .Withdrawal {
    width: 480rpx;
    height: 80rpx;
    background-image: linear-gradient(to bottom, #f08c34, #ea6531);
    border-radius: 40rpx;
    line-height: 80rpx;
    margin: 30rpx auto 0 auto;
  }
</style>
