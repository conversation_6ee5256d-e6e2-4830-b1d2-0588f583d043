<!-- 上级变更通知 -->
<template>
  <view class="plr-30">
    <scroll-view :scroll-top="scrollTop" scroll-y="true" class="content">
      <view class="plan" v-for="item in newslist">
        <view class="title">
          <view class="head">{{ item.slice(0, 4) }}-{{ item.slice(5, 7) }}-{{ item.slice(8, 10) }}</view>
        </view>
        <view class="hour">
          {{ item }}
        </view>
      </view>
    </scroll-view>
    <!-- 加载更多 -->
    <!-- <uni-load-more :status="status" :contentText="contentText" /> -->
  </view>
</template>

<script>
  const { $http } = require('@/util/methods.js');
  export default {
    data() {
      return {
        data: {
          pageNum: 1,
          pageSize: 10
        },
        newStatus: '', // 消息状态
        newslist: [], // 消息通知列表
        totalItems: '', // 消息总数量
        ifBottomRefresh: false,
        status: 'more', // 加载更多
        contentText: {
          contentdown: '加载更多数据',
          contentrefresh: '加载中...',
          contentnomore: '暂无更多数据'
        },
        scrollTop: ''
      };
    },
    onLoad() {
      let token = uni.getStorageSync('token');
      console.log(token);
      if (token) {
        this.getNewslist(); // 消息通知
      } else {
        console.log('没有token');
      }
    },
    // 触底的事件
    // onReachBottom() {
    // 	this.data.pageNum += 1
    // 	this.getNewslist()
    // 	if (this.status == 'no-more') return
    // },
    methods: {
      async getNewslist() {
        let res = await $http({
          url: 'zx/user/userParentChangeRecords'
        });
        if (res) {
          console.log(res);
          this.newslist = res.data;
        }
      }
    }
  };
</script>
<style lang="scss" scoped>
  .icon_img {
    position: absolute;
    left: 10rpx;
  }
  .content {
    // 学习进度
    .plan {
      margin-top: 30rpx;
      background-color: #fff;
      border-radius: 15rpx;
      // margin: 20rpx;
      .title {
        display: flex;
        font-size: 32rpx;
        font-weight: 700;
        padding: 20rpx 0 20rpx 0;
        margin-left: 30rpx;
        border-bottom: 1px dashed #eeeeee;
        justify-content: space-between;
        line-height: 50upx;
        .title_right {
          font-size: 30upx;
          color: #999999;
          padding-right: 30upx;
          font-weight: normal;
        }
      }
      .icon {
        width: 30rpx;
        line-height: 50rpx;
        text-align: center;
        margin-right: 20rpx;
      }
      .head {
        line-height: 60rpx;
      }
      .right {
        font-weight: 400;
        font-size: 30rpx;
        line-height: 60rpx;
        margin-left: 295rpx;
      }
      .hour {
        padding: 20rpx 30rpx;
        line-height: 50rpx;
        font-size: 30rpx;
        color: #666;
      }
    }
  }
</style>
