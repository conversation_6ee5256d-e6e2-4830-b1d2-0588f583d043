<template>
  <view class="release_content_css bg-ff">
    <view class="f-24 plr-32 release_textares_css">
      <textarea v-model="releaseInfo.content" class="c-33" placeholder="分享你的日常/学习心得/奇思妙想..."></textarea>
    </view>
    <view class="flex_css_pic plr-32 pt-24">
      <!-- <view v-for="(item,index) in fileList1" :key="index" class="file_item_css">
				<view v-if="item.type=='video'">
					<video object-fit="fill" class="file_image_css" play-btn-position="center" :show-fullscreen-btn="false" :show-center-play-btn="false" :src="item.url"></video>
				</view>
				<view v-else>
					<image class="file_image_css" :src="item.url"></image>
				</view>
			</view> -->
      <u-upload @afterRead="afterRead" :fileList="fileList1" @delete="deletePic" name="1" multiple width="160" height="160" :maxCount="9">
        <view class="upload_content_css flex-a-c flex-x-c">
          <u-icon name="plus" color="#979797" size="48"></u-icon>
        </view>
      </u-upload>
    </view>
    <view class="file_state mt-25 plr-32 pt-35 f-28 flex-a-c flex-x-s">
      <view>分享状态：</view>
      <!-- showType -->
      <view v-if="showType > 0" class="flex-a-c flex-x-s">
        <picker class="grade-picker" @change="pickerChange" :value="addReseIndex" :range="stateArray" name="studentGrade">
          <view class="f-28 c-55 mlr-5">{{ stateList[addReseIndex] }}</view>
        </picker>
        <u-icon name="arrow-right" color="#555" size="28"></u-icon>
      </view>
      <view v-else>公开</view>
    </view>
    <button @click="releaseClick" class="release_btn_css">发布</button>
    <growthPopup ref="growthPopupRefs"></growthPopup>
  </view>
</template>

<script>
  import Config from '@/util/config.js';
  import growthPopup from './components/growthPopup.vue';
  const { $navigationTo, $getSceneData, $showError, $showMsg, $http } = require('@/util/methods.js');
  export default {
    components: { growthPopup },
    data() {
      return {
        baseUrl: uni.getStorageSync('baseUrl'),
        fileList1: [],
        showType: 0,
        addReseIndex: 0,
        stateArray: ['公开-同步分享到文化圈', '私密-仅自己可见'],
        stateList: ['公开', '私密'],
        releaseClickFalse: false,
        releaseInfo: { content: '', photoPositionDtos: [] }
      };
    },
    onLoad(e) {
      let title = '发布瞬间';
      if (e.type == 1) {
        title = '上传温馨时刻';
        this.releaseInfo.topicType = 'FAMILY_CULTURE';
      } else if (e.type == 2) {
        title = '上传心灵旅程';
        this.releaseInfo.topicType = 'READ_CULTURE';
      } else if (e.type == 3) {
        title = '上传勤耕纪实';
        this.releaseInfo.topicType = 'WORK_CULTURE';
      } else if (e.type == 4) {
        title = '上传青春轨迹';
        this.releaseInfo.topicType = 'SPORT_CULTURE';
      } else {
        title = '发布瞬间';
        this.releaseInfo.topicType = e.codeType;
      }
      this.releaseInfo.isPublic = 1;
      this.releaseInfo.userId = uni.getStorageSync('user_id') ? uni.getStorageSync('user_id') : '';
      this.showType = e.type || 0;
      uni.setNavigationBarTitle({
        title: title
      });
    },
    onShow() {},
    methods: {
      pickerChange(e) {
        this.addReseIndex = e.detail.value;
        this.releaseInfo.isPublic = this.addReseIndex == 0 ? 1 : 0;
      },
      // 删除图片
      deletePic(event) {
        this[`fileList${event.name}`].splice(event.index, 1);
      },
      // 新增图片
      afterRead(event) {
        // 当设置 multiple 为 true 时, file 为数组格式，否则为对象格式
        let lists = [].concat(event.file);
        let _this = this;
        let fileListLen = this[`fileList${event.name}`].length;
        lists.map((item) => {
          this[`fileList${event.name}`].push({
            ...item,
            status: 'uploading',
            message: '上传中'
          });
        });
        for (let i = 0; i < lists.length; i++) {
          uni.uploadFile({
            url: `${this.baseUrl}zx/common/auditImage`,
            filePath: lists[i].url,
            name: 'file',
            header: {
              Token: uni.getStorageSync('token')
            },
            success: function (res) {
              let data = JSON.parse(res.data);
              if (data.data == 'pass') {
                _this.uploadFilePromise(lists[i].url, event.file[i]);
              } else if (data.data == 'risk') {
                $showMsg('您上传的图片有风险');
                _this.fileList1 = _this.fileList1.filter(function (item) {
                  return item.url != lists[i].url;
                });
              }
            }
          });
          //
          // const res = await $http({
          // 	url: 'zx/common/auditImage',
          // 	showLoading:true,
          // 	method: 'POST',
          // 	data:{
          // 		file:lists[i].url
          // 	}
          // })
          // if(res){
          // 	console.log(res)
          // }
        }
      },
      uploadFilePromise(url, info, index) {
        let _this = this;
        return new Promise((resolve, reject) => {
          uni.uploadFile({
            url: `${this.baseUrl}zx/common/uploadFile`,
            filePath: url,
            name: 'file',
            header: {
              Token: uni.getStorageSync('token')
            },
            success: function (res) {
              if (res.data) {
                let data = JSON.parse(res.data);
                if (data.status == 1) {
                  // _this.fileList1.splice(index, 0, {url:data.data.fileUrl,type:info.type});
                  _this.fileList1.push({ url: data.data.fileUrl, type: info.type });
                  _this.fileList1 = _this.fileList1.filter(function (item) {
                    return !item.message;
                  });
                } else {
                  uni.showToast({
                    title: data.message,
                    icon: 'none'
                  });
                }
                return data;
              }
            },
            fail: function (err) {
              reject(err);
              $showMsg(err.errMsg);
            },
            complete: function (res) {
              uni.hideLoading();
            }
          });
        });
      },
      async releaseClick() {
        if (this.releaseClickFalse) {
          return;
        }
        if (!this.releaseInfo.content) {
          $showMsg('请输入你的分享内容');
          return;
        }
        this.auditContent();
      },
      //
      async auditContent() {
        let _this = this;
        const res = await $http({
          url: 'zx/common/auditContent',
          method: 'POST',
          showLoading: true,
          data: {
            content: this.releaseInfo.content,
            scene: 4
          }
        });
        if (res) {
          if (res.data == 'pass') {
            this.releaseClickFalse = true;
            this.fileList1.forEach((item, index) => {
              this.releaseInfo.photoPositionDtos.push({ url: item.url, orderNum: index });
            });
            // /
            const res = await $http({
              url: 'zx/wap/CultureCircle/release',
              showLoading: true,
              method: 'POST',
              data: _this.releaseInfo
            });
            if (res) {
              this.getCultureCircle();
              this.releaseClickFalse = false;
              this.getGrowthValue('RELEASE');
            }
          } else if (res.data == 'risk') {
            $showMsg('您发布的内容有风险');
          }
        }
      },
      async getGrowthValue(text) {
        const res = await $http({
          url: 'zx/wap/CultureCircle/getGrowthValue?eventType=' + text + '&userId=' + uni.getStorageSync('user_id'),
          method: 'POST',
          data: {}
        });
        if (res) {
          if (Number(res.data)) {
            this.$refs.growthPopupRefs.open(res.data, 1);
          } else {
            uni.navigateBack({
              delta: 1
            });
          }
        }
      },
      //获取全部文化圈
      async getCultureCircle() {
        const res = await $http({
          url: 'zx/wap/badge/gain?userId=' + uni.getStorageSync('user_id') + '&eventType=' + this.releaseInfo.topicType,
          method: 'POST',
          data: {
            userId: uni.getStorageSync('user_id') ? uni.getStorageSync('user_id') : '',
            eventType: this.releaseInfo.topicType
          }
        });
        if (res) {
          console.log(res);
        }
      }
    }
  };
</script>
<style lang="scss" scoped>
  .release_content_css {
    height: 100vh;
    .release_textares_css {
      padding-top: 48rpx;
      textarea {
        color: #555 !important;
        height: 190rpx;
        font-size: 28rpx;
        width: 680rpx;
      }
    }
    .release_btn_css {
      position: absolute;
      left: 23rpx;
      bottom: 50rpx;
      width: 686rpx;
      height: 74rpx;
      line-height: 74rpx;
      border-radius: 38rpx;
      text-align: center;
      background-color: #339378;
      font-size: 28rpx;
      color: #fff;
    }
  }
  .flex_css_pic {
    display: flex;
    justify-content: flex-start;
    flex-wrap: wrap;
    width: 600rpx;
  }
  .file_item_css {
    margin-right: 10rpx;
    .file_image_css {
      width: 160rpx;
      height: 160rpx;
    }
  }
  .file_state {
    border-top: 1rpx solid #f8f8f8;
  }
  .upload_content_css {
    width: 160rpx;
    height: 160rpx;
    background-color: #f6f6f6;
  }
</style>
