<template>
  <page-meta :page-style="'overflow:' + (show ? 'hidden' : 'visible')"></page-meta>
  <view class="plr-30">
    <view class="bg-ff pt-30 radius-15 mb-20 navigation-bar">
      <view class="pl-20" style="width: 94%">
        <uni-search-bar v-model="searchValue" @blur="blur" @clear="clear" placeholder="请输入手机号/姓名搜索" :cancelButton="none" :radius="30"></uni-search-bar>
      </view>
      <view class="mt-20">
        <u-tabs
          :list="listCate"
          keyName="name"
          lineWidth="30"
          lineHeight="5"
          lineColor="#2E896F"
          :activeStyle="{
            color: '#000',
            fontWeight: 'bold',
            paddingBottom: '20rpx',
            transform: 'scale(1.04)'
          }"
          :inactiveStyle="{
            color: '#666',
            paddingBottom: '20rpx',
            transform: 'scale(1)'
          }"
          itemStyle="padding-left: 11px; padding-right: 11px; height: 34px;"
          @click="click"
        ></u-tabs>
      </view>
    </view>
    <view class="f-28 c-33 reminder">
      <view v-if="payStatus == 0 && totalList != 0">共{{ totalList }}位客户</view>
      <view v-if="payStatus == 1 && totalList != 0">已成交{{ totalList }}位客户</view>
      <view v-if="payStatus == 3 && totalList != 0">已升级{{ totalList }}位客户</view>
      <view v-if="payStatus == 2 && totalList != 0">未成交{{ totalList }}位客户</view>
      <view v-if="payStatus == 4 && totalList != 0" class="flex-x">
        <image :src="imgHost + 'dxSelect/secondPhase/prompt2.png'" class="prompt-img mr-10 mt-8"></image>
        <view>由于您变更了上级俱乐部,以下客户已被冻结且不分润</view>
      </view>
    </view>

    <view class="mt-20">
      <block v-for="(item, index) in listS.list" :key="index">
        <view class="bg-ff mb-30 radius-15 positionRelative">
          <view class="flex pl-30 pr-20 flex-y-s">
            <view class="box-100 radius-50 mt-30">
              <image :src="item.headPortrait == '' ? avaUrl : item.headPortrait" class="wh100" :class="item.isFreeze == 0 || item.status == 0 ? 'head-img' : ''"></image>
            </view>
            <view class="flex-box ml-30 ptb-30">
              <view class="flex-s">
                <view class="flex-a-c">
                  <text class="f-30 c-00" :class="item.isFreeze == 0 || item.status == 0 ? 'c-99' : ''">{{ item.remark ? item.remark : item.nickName }}</text>
                  <view class="ml-20" @click.stop="changeFoucus(item)">
                    <image src="https://document.dxznjy.com/dxSelect/image/edit_icon.png" style="width: 30upx; height: 30upx; display: inline-block"></image>
                  </view>
                </view>
                <!--identityType 0 普通用户 1 合伙人 2 俱乐部 3 合伙人&俱乐部 -->
                <view :class="item.isFreeze == 0 || item.status == 0 ? 'label-freeze' : 'label'">
                  <text>{{ item.identityName }}</text>
                </view>
              </view>
              <!-- <view v-if="item.identityType==0 && item.isDeal==1" class="classPayStatus_btn" style="background-color: rgb(229, 113, 38);">未成交</view>
							<view v-if="item.identityType==0 && item.isDeal==0" class="classPayStatus_btn" style="background-color: rgb(45, 192, 50);">已成交</view> -->
              <view class="flex-s mt-20" :class="item.isFreeze == 0 || item.status == 0 ? 'c-99' : ''">
                <text>
                  累计收益：
                  <text class="bold" :class="item.isFreeze == 0 || item.status == 0 ? 'c-99' : 'color_tangerine'">{{ item.totalAmount || 0 }}</text>
                </text>

                <view
                  v-if="item.identityType == 0 && item.isDeal == 1"
                  class="classPayStatus_btn"
                  :class="item.isFreeze == 0 || item.status == 0 ? 'notclosed-freeze' : 'not-closed'"
                >
                  未成交
                </view>
                <view v-if="item.identityType == 0 && item.isDeal == 0" class="classPayStatus_btn" :class="item.isFreeze == 0 || item.status == 0 ? 'closed-freeze' : 'closed'">
                  已成交
                </view>

                <view v-if="item.identityType > 0" class="classPayStatus_btn flex-s" style="display: flex">
                  <image v-if="item.isFreeze != 0 && item.status != 0" :src="imgHost + 'dxSelect/arrow-up.png'" class="img_s"></image>
                  <image v-if="item.isFreeze == 0 || item.status == 0" :src="imgHost + 'dxSelect/secondPhase/arrow-up2.png'" class="img_s"></image>
                  <text :class="item.isFreeze == 0 || item.status == 0 ? 'c-99' : 'c-fea'">{{ item.identityName }}</text>
                </view>
              </view>

              <view class="f-28 mt-20 flex-s">
                <view :class="item.isFreeze == 0 || item.status == 0 ? 'c-99' : 'c-33'">{{ item.mobile }}</view>
                <view class="flex-a-c" v-if="item.isFreeze == 0">
                  <image :src="imgHost + 'dxSelect/secondPhase/freeze-icon.png'" class="freeze-icon"></image>
                  <text class="freeze-time ml-5">{{ item.freezingTime }}</text>
                </view>
              </view>
              <view v-if="item.status == 0" class="mt-15 f-28" style="color: #e57126">该家长变更了学习超人，暂不分润</view>
            </view>
          </view>
        </view>
      </block>
    </view>
    <view v-if="listS.list.length == 0" class="t-c flex-col" :style="{ height: useHeight + 'rpx' }">
      <image :src="imgHost + 'alading/correcting/no_data.png'" class="mb-20 img_icon" mode="widthFix"></image>
      <view style="color: #bdbdbd">暂无数据</view>
    </view>
    <view v-if="no_more && listS.list.length > 0">
      <u-divider text="到底了"></u-divider>
    </view>

    <!-- 输入框弹窗 -->
    <uni-popup ref="inputPopup" type="center" :mask-click="false" @change="change">
      <view class="dialogBG">
        <view class="reviewCard_box positionRelative">
          <view class="cartoom_image">
            <image :src="dialog_iconUrl" mode="widthFix"></image>
          </view>
          <view class="reviewCard t-c">
            <view class="flex-a-c flex-x-e close-icon">
              <uni-icons type="clear" size="30" color="#B1B1B1" @click="closeDialog"></uni-icons>
            </view>
            <view class="f-38 bold">备注</view>
            <view class="mt-80 t-c input pl-30" :class="showClearIcon ? 'pr-20' : 'pr-30'">
              <input ref="inputRefs" :value="remark" @input="onInput" placeholder="请输入" :maxlength="importShow ? '6' : '11'" :class="showClearIcon ? 'mr-30' : ''" />
              <uni-icons v-if="showClearIcon" type="closeempty" size="24" color="#B1B1B1" @click="clearInput"></uni-icons>
            </view>

            <view class="flex-s mt-80">
              <view class="determine_btn" @click="determine">确定</view>
              <view class="close_btn" @click="closeInput">取消</view>
            </view>
          </view>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script>
  const { $http } = require('@/util/methods.js');
  import Util from '@/util/util.js';
  export default {
    data() {
      return {
        sortIndex: 0,
        showSort: false,
        listCate: [
          {
            name: '全部',
            value: 0
          },
          {
            name: '已成交',
            value: 1
          },
          {
            name: '已升级',
            value: 3
          },
          {
            name: '未成交',
            value: 2
          },
          {
            name: '已冻结',
            value: 4
          }
        ],
        searchValue: '', //搜索框
        payStatus: 0, // tab栏

        listS: {},
        page: 1,
        no_more: false,
        avaUrl: Util.getCachedPic('https://document.dxznjy.com/dxSelect/home_avaUrl.png', 'home_avaUrl_path'),
        useHeight: 0, //除头部之外高度
        imgHost: getApp().globalData.imgsomeHost,
        // 成交数据
        transactionData: {
          dealNum: '0',
          dealRate: 0,
          upgradeNum: '0'
        },

        totalList: '', // 数据总数

        show: false, // 禁止穿透滚动
        remark: '',
        showClearIcon: false,
        information: {}, // 当前点击的客户列表
        importShow: false,

        dialog_iconUrl: Util.getCachedPic('https://document.dxznjy.com/dxSelect/dialog_icon.png', 'dialog_icon_path')
      };
    },
    onLoad() {},
    onReady() {
      uni.getSystemInfo({
        success: (res) => {
          // 可使用窗口高度，将px转换rpx
          let h = res.windowHeight * (750 / res.windowWidth);
          this.useHeight = h - 285;
        }
      });
    },

    onShow() {
      this.getCustomerRelationsNum();
      this.list();
    },

    onReachBottom() {
      if (this.page >= this.listS.totalPage) {
        this.no_more = true;
        return false;
      }
      this.list(true, ++this.page);
    },
    methods: {
      // 获取客户成交人数成交率已升级人数
      async getCustomerRelationsNum() {
        const res = await $http({
          url: 'zx/user/selCustomerRelationsNum'
        });
        if (res) {
          this.transactionData = res.data;
        }
      },

      async list(isPage, page) {
        let _this = this;
        const res = await $http({
          url: 'zx/user/customerRelationsPage',
          data: {
            type: _this.payStatus == 0 ? '' : _this.payStatus,
            searchName: _this.searchValue,
            page: page || 1,
            pageSize: 10
          }
        });
        if (res) {
          _this.totalList = res.data.totalCount;
          if (isPage) {
            let old = _this.listS.list;
            _this.listS.list = [...old, ...res.data.list];
          } else {
            _this.listS = res.data;
          }
          _this.getIdentityName();
        }
      },

      // list 数据转换 用户身份
      getIdentityName() {
        let that = this;
        if (this.listS.list.length > 0) {
          this.listS.list.forEach((item) => {
            let identityName = '';
            if (item.identityType == 0) {
              identityName = '家长';
            } else if (item.identityType == 1) {
              identityName = '超人';
            } else if (item.identityType == 2 || item.identityType == 3) {
              identityName = '俱乐部' + that.numToABC();
            } else {
              identityName = '普通用户';
            }
            that.$set(item, 'identityName', identityName);
          });
        }
      },

      // 将数字转换成ABC
      numToABC(num) {
        let newString = '';
        if (num == 4) {
          newString = 'C级';
        } else if (num == 5) {
          newString = 'B级';
        } else if (num == 6) {
          newString = 'A级';
        }
        return newString;
      },

      click(e) {
        this.payStatus = e.value;
        console.log(this.payStatus);
        this.page = 1;
        this.no_more = false;
        this.list();
      },

      showSortDialog() {
        this.showSort = !this.showSort;
      },

      // 点击排序
      chooseSort(index) {
        this.sortIndex = index;
        this.list();
        this.showSortDialog();
      },

      blur(e) {
        this.searchValue = e.value;
        this.list();
      },

      clear() {
        this.searchValue = '';
        this.list();
      },

      closeDialog() {
        this.$refs.inputPopup.close();
        this.importShow = false;
        this.remark = '';
      },

      // 点击修改备注
      changeFoucus(item) {
        if (item.remark) {
          this.remark = item.remark;
        } else {
          this.remark = item.nickName;
        }
        this.showClearIcon = true;
        this.information = item;
        this.$refs.inputPopup.open();
      },

      closeInput() {
        this.$refs.inputPopup.close();
        this.importShow = false;
        this.remark = '';
      },

      determine() {
        this.modifyRemarks();
      },

      clearInput() {
        // uni.hideKeyboard();
        this.remark = '';
        this.importShow = true;
      },

      onInput(e) {
        this.remark = e.detail.value;
        if (e.detail.value.length > 0) {
          this.showClearIcon = true;
          if (e.detail.value.length <= 6) {
            this.importShow = true;
          }
        } else {
          this.showClearIcon = false;
        }
      },

      change(e) {
        this.show = e.show;
      },

      // 修改备注
      async modifyRemarks() {
        let _this = this;
        if (_this.remark.length > 6) {
          _this.$util.alter('备注不可超过六个字符');
          return;
        }
        uni.showLoading({ mask: true });
        const res = await $http({
          url: 'zx/user/setCustomerRelationsRemark',
          method: 'post',
          data: {
            remark: _this.remark,
            userId: _this.information.userId
          }
        });
        uni.hideLoading();
        if (res.status == 1) {
          this.$refs.inputPopup.close();
          this.remark = '';
          this.importShow = false;
          this.list();
        } else {
          _this.$util.alter('操作失败啦，请稍后再试');
        }
      }
    }
  };
</script>

<style lang="scss" scoped>
  .navigation-bar {
    position: fixed;
    top: 0;
    left: 30rpx;
    width: 92%;
    z-index: 9;
  }

  .reminder {
    margin-top: 140rpx;
  }
  .label {
    background-image: linear-gradient(to right, #feefd5, #e7bd7b);
    padding: 0 20rpx;
    height: 40rpx;
    line-height: 40rpx;
    border-radius: 20rpx 0;
    color: #886a34;
    font-size: 24rpx;
  }

  .label-freeze {
    background-color: #ebebeb;
    padding: 0 20rpx;
    height: 40rpx;
    line-height: 40rpx;
    border-radius: 20rpx 0;
    color: #999999;
    font-size: 24rpx;
  }

  .screenitem {
    width: 150rpx;
    height: 60rpx;
    border: 1rpx solid #c8c8c8;
    border-radius: 35rpx;
    padding: 0 30rpx;

    .screenPicker {
      flex: 1;
    }

    .xiaimg {
      width: 20rpx;
      height: 20rpx;
    }
  }

  .flex_s {
    display: flex;
    align-items: center;
  }

  .img_icon {
    width: 160rpx;
    height: 160rpx;
  }

  /deep/.uni-searchbar__box {
    height: 80rpx !important;
  }

  /deep/.uni-searchbar__box-icon-search {
    padding: 0 10rpx 0 30rpx !important;
  }

  /deep/.u-tabs__wrapper__nav__line {
    margin-left: 18rpx !important;
  }

  /deep/.uni-searchbar {
    padding-left: 20rpx !important;
  }

  .sortBox {
    width: 220upx;
    background: #ffffff;
    border: 1upx solid #cfc8c8;
    position: absolute;
    top: 100upx;
    left: 30upx;
  }

  .sortItem {
    width: 100%;
    height: 80upx;
    text-align: center;
    line-height: 80upx;
    font-size: 30upx;
    color: #666666;
    background-color: #fff;
  }

  .sortItem.active {
    background: #f4f4f4;
    color: #000000;
    font-weight: bold;
  }

  .img_s {
    width: 22upx;
    height: 28upx;
  }

  .prompt-img {
    width: 25rpx;
    height: 25rpx;
  }

  .flex-x {
    display: flex;
    align-items: flex-start;
  }

  .freeze-img {
    width: 690rpx;
    height: 220rpx;
  }

  /deep/.u-tabs {
    margin-left: 20rpx;
  }

  .freeze-time {
    color: #e57126;
  }

  .freeze-icon {
    width: 28rpx;
    height: 28rpx;
  }

  .head-img {
    opacity: 0.4;
  }

  .not-closed {
    color: #fff;
    background-color: #e57126;
  }

  .notclosed-freeze {
    color: #999;
    background-color: #ebebeb;
  }

  .closed {
    color: #fff;
    background-color: #2dc032;
  }

  .closed-freeze {
    color: #999;
    background-color: #ebebeb;
  }

  /* 21天结束复习弹窗样式 */
  .reviewCard_box {
    width: 670rpx;
    /* height: 560rpx; */
    position: relative;
  }

  .reviewCard_box image {
    width: 100%;
    height: 100%;
  }

  .reviewCard {
    position: relative;
    width: 100%;
    height: 100%;
    background: #ffffff;
    color: #000;
    border-radius: 24upx;
    padding: 50upx 55upx;
    box-sizing: border-box;
  }

  .close-icon {
    position: absolute;
    right: 20rpx;
    top: 20rpx;
  }

  .cartoom_image {
    width: 420rpx;
    position: absolute;
    top: -250rpx;
    left: 145rpx;
    z-index: -1;
  }

  .review_close {
    position: absolute;
    top: 20rpx;
    right: 20rpx;
    z-index: 1;
  }

  .reviewTitle {
    width: 100%;
    text-align: center;
    font-size: 34upx;
    display: flex;
    justify-content: center;
  }
  .dialogContent {
    box-sizing: border-box;
    font-size: 32upx;
    line-height: 45upx;
    text-align: center;
    margin-top: 40rpx;
  }

  .review_btn {
    width: 240upx;
    height: 80upx;
    background: linear-gradient(180deg, #88cfba 0%, #1d755c 100%);
    border-radius: 45upx;
    font-size: 30upx;
    color: #ffffff;
    line-height: 80upx;
    margin: 60rpx auto 0 auto;
    justify-content: center;
    text-align: center;
  }

  .nickname {
    width: 48%;
  }

  .total-income {
    color: #fa370e;
  }

  // 文本超出隐藏
  .container {
    width: 200rpx;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .determine_btn {
    width: 250upx;
    height: 80upx;
    color: #ffffff;
    font-size: 30upx;
    line-height: 80upx;
    text-align: center;
    border-radius: 45upx;
    background: linear-gradient(180deg, #88cfba 0%, #1d755c 100%);
  }

  .close_btn {
    width: 250upx;
    height: 80upx;
    color: #2e896f;
    font-size: 30upx;
    line-height: 80upx;
    text-align: center;
    border-radius: 45upx;
    box-sizing: border-box;
    border: 1px solid #2e896f;
  }

  .input {
    border: 1px solid #c8c8c8;
    border-radius: 50rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .reviewCard /deep/ .vue-ref {
    padding: 20rpx 0;
    width: 100%;
  }
</style>
