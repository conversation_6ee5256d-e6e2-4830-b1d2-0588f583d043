<template>
  <view class="ctxt plr-30 bg-ff">
    <view class="bg-ff ptb-30">
      <view class="binggo_c">
        <view class="video_css_content">
          <polyv-player
            id="polyv_player"
            @loadedmetadata="bindloadedmetadata"
            @ended="bindEnded"
            @pause="bindpause"
            :autoplay="true"
            :playerId="playerIdcont"
            :vid="videoSrc"
            :width="width"
            :height="height"
            :ts="ts"
            :sign="sign"
          ></polyv-player>
        </view>
      </view>
      <view class="botBtn">
        <view class="btn_b b_r" @click="goWord">下一环节</view>
      </view>
      <view class="zanwu" :status="loadingType"></view>
    </view>
  </view>
</template>

<script>
  let vid = '';
  let ts = new Date().getTime();
  let sign = '';
  let secretkey = 'Jkk4ml1Of8';
  const MD5 = require('../../util/md5.js');
  export default {
    data() {
      return {
        studentCode: '', //学员Code
        videoSrc: '',
        ts: ts, // 时间戳
        sign: sign,
        playerIdcont: 'polyvPlayercont',
        width: '100%',
        height: '100%'
      };
    },
    onLoad(options) {
      this.videoSrc = options.vid;
      // this.studentCode = options.studentCode;
      // this.studentCode = "6230313671";
      console.log('this.videoSrc', this.videoSrc);
      this.atu();
    },
    onUnload() {
      this.stopAudio();
      console.log('停止视频播放--------');
    },
    methods: {
      ceshi() {
        let _this = this;
        debugger;
        this.$refs.video_popup.open();
        this.$nextTick(() => {
          console.log('======================================');
          let polyvPlayerContext = this.selectComponent('#polyv_player');
          if (polyvPlayerContext) {
            const ts = new Date().getTime();
            const sign = MD5.md5(`${secretkey}${vid}${ts}`);
            polyvPlayerContext.changeVid({
              vid: this.videoSrc,
              ts,
              sign
            });
          } else {
            console.log('Polyv player context is null');
          }
        });
      },
      atu() {
        let polyvPlayerContext = this.selectComponent('#polyv_player');
        if (polyvPlayerContext) {
          const ts = new Date().getTime();
          const sign = MD5.md5(`${secretkey}${vid}${ts}`);
          polyvPlayerContext.changeVid({
            vid: this.videoSrc,
            ts,
            sign
          });
        } else {
          console.log('Polyv player context is null');
        }
      },
      stopAudio() {
        let polyvPlayerContext = this.selectComponent('#polyv_player');
        if (polyvPlayerContext) {
          polyvPlayerContext.pause(); // 停止视频播放
        }
      },
      bindloadedmetadata() {},
      bindEnded() {},
      // 视频暂停时的处理逻辑
      bindpause() {},
      bindloadedmetadata() {},
      goWord() {
        let planReviewId = uni.getStorageSync('pyfPlanReviewId');
        uni.redirectTo({
          url: '/PYFforget/yfyReviewCheck?planReviewId=' + planReviewId
        });
      }
    }
  };
</script>

<style scoped>
  .ctxt {
    height: 100vh;
  }

  .video_css_content {
    height: 96%;
  }

  .binggo_c {
    height: 92vh;
    margin-top: 10rpx;
  }

  .botBtn {
    position: fixed;
    bottom: 0rpx;
    right: 32rpx;
    padding-bottom: 64rpx;
    box-sizing: border-box;
    background-color: #ffffff;
  }

  .btn_b {
    width: 686rpx;
    height: 74rpx;
    border-radius: 60rpx;
    line-height: 74rpx;
    text-align: center;
  }

  .b_r {
    background-image: linear-gradient(to bottom, #88cfba, #1d755c);
    color: #ffffff;
    margin-left: 32rpx;
    letter-spacing: 3px;
  }
</style>
