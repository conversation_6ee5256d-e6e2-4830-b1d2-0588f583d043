<template>
  <page-meta :page-style="'overflow:' + (rollShow ? 'hidden' : 'visible')"></page-meta>
  <view>
    <view class="center-banner">
      <!-- 顶部文字 -->
      <view class="top">
        <view class="left-text">
          <text>21方格</text>
          <text>联动抗遗忘复习系统</text>
        </view>
        <view class="right-logo">
          <image src="https://document.dxznjy.com/applet/newimages/dingxiaologo.png" mode=""></image>
        </view>
      </view>
      <image src="https://document.dxznjy.com/applet/newimages/niuniu.png" class="images"></image>
      <!-- 底部今日复习和往期记录 -->
      <view class="footer">
        <view class="left-btn fot-btn" @click="gohistory()">往期记录</view>
        <view class="right-btn fot-btn" @click="golist()">今日复习</view>
      </view>
    </view>
    <!-- 推荐产品 -->
    <view class="recommended">
      <view class="like">你可能还会喜欢</view>
      <scroll-view
        v-if="leftList.length > 0"
        @scrolltolower="scrolltolower"
        :class="showScroll ? 'course_scroll_top' : ''"
        :show-scrollbar="false"
        bounces
        :throttle="false"
        scroll-with-animation
        scroll-anchoring
        scroll-y
        enhanced
      >
        <view class="courseList mt-12">
          <view class="waterfall-box h-flex-x h-flex-2">
            <view>
              <helang-waterfall
                v-for="(item, index) in leftList"
                :key="index"
                :item="item"
                tag="left"
                :index="index"
                :identityType="identityType"
                @shareVip="shareVip"
                @click="skintap('Coursedetails/productDetils?id=' + item.goodsId, item.goodsId)"
              ></helang-waterfall>
            </view>
            <view style="margin-left: 24rpx">
              <helang-waterfall
                v-for="(item, index) in rightList"
                :key="index"
                :item="item"
                :identityType="identityType"
                @shareVip="shareVip"
                @click="skintap('Coursedetails/productDetils?id=' + item.goodsId, item.goodsId)"
                tag="right"
                :index="index"
              ></helang-waterfall>
            </view>
          </view>
        </view>
      </scroll-view>
    </view>
    <!-- 今日复习 -->
    <uni-popup ref="reviewTypePopup" type="center">
      <view class="dialogBG">
        <view class="reviewCard">
          <view class="review_close" @click="cancelReviewType">
            <uni-icons type="clear" size="28" color="#B1B1B1"></uni-icons>
          </view>
          <view class="reviewTitle bold pb-10">选择复习类型</view>
          <view
            class="dialogContent"
            @click="handleButtonClick(item, index)"
            :class="activeIndex == index ? 'addclass' : 'not-selected'"
            v-for="(item, index) in reviewTypes"
            :key="index"
          >
            <view class="flex-a-c" style="justify-content: center" v-if="item.name == '单词'">
              <view class="" style="width: 70rpx"></view>
              <text class="mr-8">{{ item.name }}</text>
              <image src="https://document.dxznjy.com/course/b3084b45968a453a965c5e2af9ec099c.png" style="width: 70rpx" mode="widthFix"></image>
            </view>
            <view class="" v-else>
              <text>{{ item.name }}</text>
            </view>
          </view>
          <view class="mask-footerType">
            <button class="confirm-button" @click="confirmReviewType()">确定</button>
            <button class="cancel-button" @click="cancelReviewType">取消</button>
          </view>
        </view>
      </view>
    </uni-popup>

    <!-- 选择复习方式弹窗 -->
    <uni-popup ref="popopChooseType" :mask-click="false" type="center">
      <view class="dialogBG">
        <view class="reviewCard_box positionRelative">
          <view class="cartoom_image">
            <image src="https://document.dxznjy.com/dxSelect/dialog_icon.png" mode="widthFix"></image>
          </view>
          <view class="review_close" @click="closeDialog">
            <uni-icons type="clear" size="28" color="#B1B1B1"></uni-icons>
          </view>
          <view class="reviewCard">
            <view class="reviewTitle bold pb-10">选择复习方式</view>
            <view class="dialogContent1" @click="chooseType(item, index)" v-for="(item, index) in arrayType" :class="isactive == index ? 'addclass' : 'not-selected'">
              {{ item.title }}
            </view>
            <view class="mask-footer">
              <button class="confirm-button" @click="confirmType()">确定</button>
              <button class="cancel-button" @click="closeDialog">取消</button>
            </view>
          </view>
        </view>
      </view>
    </uni-popup>
    <sharePopup ref="sharePopups"></sharePopup>
  </view>
</template>

<script>
  import Config from '@/util/config.js';
  import sharePopup from '@/components/sharePopup.vue';
  const { $navigationTo, $http } = require('@/util/methods.js');
  export default {
    data() {
      return {
        imgHost: getApp().globalData.imgHost,
        array: [
          {
            studentCode: '',
            realName: '选择学员'
          }
        ],
        index: 0,
        studentCode: '',
        StudentCodeKey: '',
        flag: false, //防止多次点击
        // 语法 21 天抗遗忘
        reviewTypes: [
          { name: '单词', type: 'word' },
          { name: '语法', type: 'grammar' }
        ],
        // { name: '阅读理解', type: 'reading' }  暂时不做
        reviewType: '', // 复习类型
        showMask: false, // 控制遮罩层的显示与隐藏
        activeIndex: -1,
        Buttonclick: '',
        buttonclickName: '',
        selectedType: 'word',
        showScroll: false, //推荐产品
        identityType: '',
        leftHeight: 0,
        rightHeight: 0,
        leftList: [],
        rightList: [],
        app: 0,
        typeInfo: {}, //复习类型
        arrayType: [
          {
            title: 'AI模式',
            value: '1'
          },
          {
            title: '原始模式',
            value: '2'
          }
        ],
        isactive: -1,
        shareContent: {}
      };
    },
    components: {
      sharePopup
    },
    mounted() {
      this.$nextTick(() => {
        this.$refs.reviewTypePopup.open();
      });
    },
    onShareAppMessage() {
      setTimeout(() => {
        this.$refs.sharePopup.close();
      }, 2000);
      return {
        title: this.shareContent.title || '叮，你的好友敲了你一下，赶紧过来看看',
        imageUrl: this.shareContent.imgurl,
        // imageUrl: "https://document.dxznjy.com/dxSelect/superman_share.jpg",
        path: `/pages/beingShared/index?scene=${uni.getStorageSync('user_id')}&type=${this.shareContent.type}&id=${this.shareContent.id}`
      };
    },
    onLoad(option) {
      // this.studentCode = '6250222777';
      if (option.token) {
        this.app = option.app;
        this.$handleTokenFormNative(option);
        // this.studentCode = option.studentCode;
      }
      const buttonClick = decodeURIComponent(option.buttonClick || '');
      if (buttonClick && buttonClick != '') {
        this.studentCode = buttonClick;
        uni.setStorageSync('studentCode', buttonClick);
      } else {
        if (option.studentCode) {
          this.studentCode = option.studentCode;
          uni.setStorageSync('studentCode', buttonClick);
        } else {
          const storedStudentCode = uni.getStorageSync('studentCode');
          if (storedStudentCode) {
            this.studentCode = storedStudentCode;
          }
        }
      }

      const buttonclickName = decodeURIComponent(option.buttonclickName || '');
      if (buttonclickName) {
        uni.setStorageSync('21StudentName', option.buttonclickName);
      }

      this.loadData();

      var that = this;
      uni.$on('LoginSuccess', function (data) {
        that.loadData();
      });

      uni.$on('LoginOut', function (data) {
        that.loadData();
      });
    },
    onShow() {
      this.getlist();
    },
    onUnload() {
      // #ifdef APP-PLUS
      if (this.app) {
        plus.runtime.quit();
      }
      // #endif
    },
    // },
    methods: {
      //点击选择学员
      chooseType(item, index) {
        this.isactive = index;
        this.typeInfo = { ...item };
      },
      async confirmType() {
        let that = this;
        if (this.studentCode == '') {
          this.$util.alter('请先选择学员');
          return;
        }
        var token = uni.getStorageSync('token');
        if (that.typeInfo.value == 1) {
          if (token != '') {
            uni.redirectTo({
              url: `/antiAmnesia/review/history?studentCode=${this.studentCode}&selectedType=${this.selectedType}&antiForgetting=1&reviewType=1`
            });
          } else {
            uni.navigateTo({
              url: '/pages/login/login'
            });
          }
        } else {
          if (token != '') {
            uni.redirectTo({
              url: `/antiAmnesia/review/history?studentCode=${this.studentCode}&selectedType=${this.selectedType}&antiForgetting=1&reviewType=0`
            });
          } else {
            uni.navigateTo({
              url: '/pages/login/login'
            });
          }
        }
      },
      closeDialog() {
        this.isactive = -1;
        this.$refs.popopChooseType.close();
      },
      // 今日复习
      confirmReviewType() {
        if (this.reviewType) {
          this.selectedType = this.reviewType; // 记录选择的类型
          this.$refs.reviewTypePopup.close();
          this.handleClearData();
        } else {
          this.$message.error('请选择复习类型');
        }
      },
      cancelReviewType() {
        this.$refs.reviewTypePopup.close();
        this.showMask = false;
        this.handleClearData();
      },
      // 处理选择按钮点击事件
      handleButtonClick(item, index) {
        // #ifdef MP-WEIXIN
        getApp().sensors.track('popupReviewTypeClick', {
          name: item.name
        });
        // #endif
        this.activeIndex = index;
        this.Buttonclick = item.studentCode;
        this.buttonclickName = item.realName;
        this.reviewType = item.type;
      },
      // 选择学员弹窗清除之前选中样式
      handleClearData() {
        this.activeIndex = -1;
        this.Buttonclick = '';
        this.buttonclickName = '';
      },
      async loadData() {
        var loginMobile = uni.getStorageSync('LoginMobile');
        console.log(loginMobile);
        this.StudentCodeKey = 'review_' + loginMobile;
        let result = await this.$httpUser.get('znyy/review/query/my/student');
        if (result != undefined && result != '') {
          if (result.data.data != null) {
            this.array = [
              {
                studentCode: '',
                realName: '选择学员'
              }
            ];
            if (result.data.data.length > 0) {
              if (result.data.data.length == 1) {
                this.array = [];
                this.index = 0;
                this.array = this.array.concat(result.data.data);
                this.studentCode = this.array[this.index].studentCode;
                uni.setStorageSync(this.StudentCodeKey, this.studentCode);
              } else {
                var that = this;
                that.array = that.array.concat(result.data.data);
                var array = that.array;
                if (uni.getStorageSync(this.StudentCodeKey) != '') {
                  var studentCode = uni.getStorageSync(this.StudentCodeKey);
                  array.forEach(function (item, index) {
                    if (item.studentCode == studentCode) {
                      that.index = index;
                      that.studentCode = studentCode;
                    }
                  });
                }
              }
            }
          }
        }
      },
      golist() {
        // #ifdef MP-WEIXIN
        getApp().sensors.track('antiAmnesiaClick', {
          name: '今日复习'
        });
        // #endif
        if (this.studentCode == '') {
          this.$util.alter('请先选择学员');
          return;
        }
        if (this.flag) return;
        var token = uni.getStorageSync('token');
        if (token != '') {
          if (this.selectedType === 'word' || this.selectedType === undefined) {
            var wordCount = 0;
            this.flag = true;
            uni.showLoading();
            this.$httpUser
              .get('znyy/review/query/fun/word/count', {
                studentCode: this.studentCode
              })
              .then((wordCountResult) => {
                this.flag = false;
                uni.hideLoading();
                if (wordCountResult) {
                  if (wordCountResult.data.success) {
                    wordCount = wordCountResult.data.data.totalWordCount;
                  }
                  if (wordCount == 0) {
                    this.$util.alter('暂无可复习单词！');
                  } else {
                    if (!this.flag) {
                      uni.navigateTo({
                        url: '/antiAmnesia/review/allWords?studentCode=' + this.studentCode
                      });
                    }
                  }
                }
              });
          } else {
            if (this.selectedType === 'grammar') {
              if (!this.flag) {
                this.$httpUser
                  .get('dyf/wap/applet/todayReviewStatistics', {
                    // todo 学员 code 固定值 用于测试
                    studentCode: this.studentCode
                    // studentCode: '6231217888',
                  })
                  .then((res) => {
                    if (res.data.data.totalNum > 0) {
                      uni.navigateTo({
                        url: `/antiAmnesia/review/allWords?studentCode=${this.studentCode}&reviewType=${this.selectedType}`
                      });
                    } else {
                      this.$util.alter('暂无可复习语法！');
                    }
                  });
              }
            }
          }
        } else {
          uni.navigateTo({
            url: '/pages/login/login'
          });
        }
      },

      gohistory() {
        // #ifdef MP-WEIXIN
        getApp().sensors.track('antiAmnesiaClick', {
          name: '往期记录'
        });
        // #endif
        // this.$refs.popopChooseType.open();
        let that = this;
        if (this.studentCode == '') {
          this.$util.alter('请先选择学员');
          return;
        }
        var token = uni.getStorageSync('token');
        if (token != '') {
          uni.navigateTo({
            url: `/antiAmnesia/review/history?studentCode=${this.studentCode}&selectedType=${this.selectedType}&antiForgetting=1`
          });
        } else {
          uni.navigateTo({
            url: '/pages/login/login'
          });
        }
      },
      getRandomItems(arr, num) {
        // 1. 确保不超过数组的长度
        const limit = Math.min(num, arr.length);

        // 2. 随机选择不超过 num 的项
        const shuffledArray = arr.sort(() => 0.5 - Math.random());

        // 3. 返回前 limit 个元素
        return shuffledArray.slice(0, limit);
      },
      // 会员专享分享
      shareVip(type, item) {
        if (!uni.getStorageSync('token')) {
          uni.navigateTo({
            url: '/Personalcenter/login/login'
          });
          return;
        }
        this.shareContent.type = type;
        let id = '',
          imgurl = '';
        //type 1课程  2学习超人（会员） 3超人俱乐部
        if (type == '1') {
          id = item.courseId;
          imgurl = item.courseImage;
        } else {
          id = item.mealId;
          type == '2' ? (imgurl = Config.supermanShareImage) : (imgurl = Config.supermanClubShareImage);
        }
        this.shareContent.id = item.goodsId;
        if (type != 6) {
          this.shareContent.imgurl = imgurl;
        } else {
          this.shareContent.imgurl = item.goodsSharePoster;
          this.shareContent.title = item.goodsShareTextList[0] ? item.goodsShareTextList[0].shareText : null;
        }
        this.$refs.sharePopups.open(this.shareContent);
      },

      async getlist() {
        this.leftList = [];
        this.rightList = [];
        let idList = [];
        const idres = await $http({
          url: 'zx/operation/goods/recommendation/category/list'
        });

        if (idres) {
          console.log(idres);
          if (idres.code == 20000) {
            idList = idres.data.filter((it) => it.categoryName === '热门推荐');
          }
        }

        let list = [];

        // 创建一个数组来存储所有的 Promise
        const promises = idList.map(async (item) => {
          const res = await $http({
            url: 'zx/wap/goods/recommendation/enable/list',
            data: {
              pageSize: 10,
              pageNum: 1,
              recommendationCategoryId: item.id,
              userId: uni.getStorageSync('user_id') ? uni.getStorageSync('user_id') : ''
            }
          });
          return res.data.data; // 返回每个请求的数据
        });

        // 等待所有的 Promise 完成
        const results = await Promise.all(promises);

        // 将所有的结果合并到 list 中
        list = results.flat(); // 使用 flat() 方法将嵌套的数组合并为一个数组

        console.log(list, '最终的完整数据');

        // if (list.length > 10) {
        //   list = this.getRandomItems(list, 10);
        // }

        if (list) {
          // 初始化左右列表的数据
          let i = this.leftHeight > this.rightHeight ? 1 : 0;
          let [left, right] = [[], []];
          // 左右列表高度的差
          let differ = this.leftHeight - this.rightHeight;
          console.log(differ);
          console.log('--------------------------------------------');
          // 将数据源分为左右两个列表，容错差值请自行根据项目中的数据情况调节
          let reslist = list;
          list.forEach((item, index) => {
            /* 左侧高度大于右侧超过 600px 时，则前3条数据都插入到右边 */
            if (differ >= 600 && index < 3) {
              right.push(item);
              return;
            }

            /* 右侧高度大于左侧超过 600px 时，则前3条数据都插入到左边 */
            if (differ <= -600 && index < 3) {
              left.push(item);
              return;
            }

            /* 左侧高度大于右侧超过 350px 时，则前2条数据都插入到右边 */
            if (differ >= 350 && index < 2) {
              right.push(item);
              return;
            }
            /* 右侧高度大于左侧超过 350px 时，则前2条数据都插入到左边 */
            if (differ <= -350 && index < 2) {
              left.push(item);
              return;
            }

            /* 当前数据序号为偶数时，则插入到左边 */
            if (i % 2 == 0) {
              left.push(item);
            } else {
              /* 当前数据序号为奇数时，则插入到右边 */
              right.push(item);
            }
            i++;
          });
          if (true) {
            // let old = _this.infoLists.list
            // _this.infoLists.list = [...old, ...res.data.list]
            this.leftList = [...this.leftList, ...left];
            this.rightList = [...this.rightList, ...right];
          } else {
            _this.infoLists = res.data;
            this.leftList = left;
            this.rightList = right;
          }
          console.log(this.leftList, 'leftList');
          console.log(this.rightList, 'rightList');
        }
      },
      skintap(url, goodsId) {
        //埋点-21天抗遗忘你可能还会喜欢
        // #ifdef MP-WEIXIN
        getApp().sensors.track('antiAmnesiaReviewClick', {
          goodsId: goodsId
        });
        // #endif
        if (!uni.getStorageSync('token')) {
          uni.navigateTo({
            url: '/Personalcenter/login/login'
          });
        } else {
          $navigationTo(url);
        }
      }
    }
  };
</script>

<style>
  .top {
    display: flex;
    justify-content: space-between;
  }

  .left-text {
    margin-top: 44rpx;
    display: flex;
    flex-direction: column;
  }

  .left-text text {
    font-size: 40rpx;
    font-family: AlibabaPuHuiTiM;
    color: #000000;
    font-weight: bold;
    line-height: 60rpx;
  }

  .right-logo {
    margin-top: 35rpx;
  }

  .center-banner {
    margin: 20rpx auto;
    width: 630rpx;
    height: 1348rpx;
    background: #fff;
    border-radius: 20rpx;
    padding: 0 30rpx;
    margin-bottom: 50rpx;
  }

  .right-logo image {
    width: 173rpx;
    height: 80rpx;
  }

  .btnchange {
    width: 160rpx;
    height: 60rpx;
    background: #fd7918;
    line-height: 60rpx;
    text-align: center;
    border-radius: 60rpx;
    color: #fff;
    font-size: 30rpx;
    position: absolute;
    right: 20rpx;
    top: 144rpx;
  }

  .imgs {
    position: absolute;
    overflow: hidden;
    z-index: -10;
    background-size: cover;
    pointer-events: none;
    width: 100%;
    height: 1334rpx;
    /* right: 0px;
		  top: 0px;
		  bottom: 0px;
		  left: 0px; */
  }

  uni-page-body {
    padding-bottom: 0px !important;
  }

  .logo {
    display: block;
    width: 530rpx;
    height: 112rpx;
    position: absolute;
    left: 50%;
    margin-left: -265rpx;
    top: 60rpx;
  }

  .image {
    display: block;
    width: 630rpx;
    height: 60rpx;
    position: absolute;
    left: 50%;
    margin-left: -315rpx;
    top: 200rpx;
  }

  .images {
    width: 630rpx;
    height: 912rpx;
    margin-top: 74rpx;
  }

  .footer {
    display: flex;
    justify-content: space-between;
    margin-top: 60rpx;
    margin-bottom: 60rpx;
  }

  .fot-btn {
    text-align: center;
    width: 290rpx;
    height: 80rpx;
    line-height: 80rpx;
    border-radius: 40rpx;
    border: 2rpx solid #2e896f;
    font-size: 32rpx;
    font-family: AlibabaPuHuiTiR;
  }

  .left-btn {
    color: #2e896f;
  }

  .right-btn {
    background: #2e896f;
    color: #ffffff;
  }

  /* 弹窗样式 */
  .dialogBG {
    width: 100%;
    /* height: 100%; */
  }

  /* 底部按钮样式 */
  .mask-footerType {
    margin-top: 20px;
    display: flex;
    justify-content: space-around;
  }

  .mask-footerType button + button {
    margin-left: 20rpx;
  }

  .mask-footerType button {
    width: 250rpx;
    height: 80rpx;
    font-size: 30rpx;
    border-radius: 45rpx;
  }

  .confirm-button {
    display: flex;
    justify-content: space-around;
    align-items: center;
    /* 文本垂直居中对齐 */
    background: #2e896f;
    color: #ffffff !important;
  }

  .cancel-button {
    display: flex;
    justify-content: space-around;
    align-items: center;
    /* 文本垂直居中对齐 */
    color: #2e896f !important;
    border: 1rpx solid #2e896f !important;
  }

  .addclass {
    width: 100%;
    height: 70rpx;
    line-height: 70rpx;
    font-size: 30rpx;
    color: #2e896f;
    border: 1px solid #2e896f;
    border-radius: 35rpx;
  }

  .not-selected {
    width: 100%;
    height: 70rpx;
    line-height: 70rpx;
    font-size: 30rpx;
    color: #000;
    border: 1px solid #c8c8c8;
    border-radius: 35rpx;
  }

  .reviewCard {
    position: relative;
    width: 100%;
    height: 100%;
    background: #ffffff;
    color: #000;
    border-radius: 24upx;
    padding: 50upx 55upx;
    box-sizing: border-box;
  }

  .review_close {
    position: absolute;
    top: 20rpx;
    right: 20rpx;
    z-index: 1;
  }

  .reviewTitle {
    width: 100%;
    text-align: center;
    font-size: 34upx;
    display: flex;
    justify-content: center;
  }

  .dialogContent {
    box-sizing: border-box;
    font-size: 30upx;
    line-height: 70upx;
    text-align: center;
    margin-top: 40rpx;
  }
  .course_scroll_top {
    height: calc(100vh - 70rpx);
  }
  .recommended {
    /* margin-top: 30rpx; */
    padding: 0 30rpx;
  }
  .like {
    margin-bottom: 38rpx;
    font-weight: 700;
  }
  /* 弹窗样式 */
  .dialogBG {
    width: 100%;
    /* height: 100%; */
  }
  /* 21天结束复习弹窗样式 */
  .reviewCard_box {
    width: 670rpx;
    /* height: 560rpx; */
    position: relative;
  }

  .reviewCard_box image {
    width: 100%;
    height: 100%;
  }
  .reviewCard {
    position: relative;
    width: 100%;
    height: 100%;
    background: #ffffff;
    color: #000;
    border-radius: 24upx;
    padding: 50upx 55upx;
    box-sizing: border-box;
  }
  .cartoom_image {
    width: 420rpx;
    position: absolute;
    top: -250rpx;
    left: 145rpx;
    z-index: -1;
  }
  .review_close {
    position: absolute;
    top: 20rpx;
    right: 20rpx;
    z-index: 1;
  }

  .dialogContent1 {
    box-sizing: border-box;
    font-size: 32upx;
    text-align: center;
    margin-top: 40rpx;
  }
  /* 底部按钮样式 */
  .mask-footer {
    margin-top: 20px;
    display: flex;
    justify-content: space-around;
  }

  .mask-footer button {
    width: 250rpx;
    height: 80rpx;
    font-size: 30rpx;
    border-radius: 45rpx;
  }
</style>
