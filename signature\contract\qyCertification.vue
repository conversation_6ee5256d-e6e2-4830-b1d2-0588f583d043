<template>
  <view class="plr-35 f-28 pt-10" style="box-sizing: border-box; background-color: #fff; height: 100vh">
    <view style="display: flex; justify-content: space-between" class="mb-65 lh-42 mt-50">
      <view class="">
        <text style="color: red">*</text>
        认证主体角色:
      </view>
      <view class="">
        <radio-group @change="radioChange">
          <label class="radio">
            <radio :value="1" :checked="checkedValue" style="transform: scale(0.7)" :disabled="inputDisabled" />
            企业
          </label>
          <label class="radio">
            <radio :value="2" :checked="!checkedValue" style="transform: scale(0.7)" :disabled="inputDisabled" />
            个人
          </label>
        </radio-group>
      </view>
    </view>
    <view class="" v-if="form.authType == 1">
      <form id="#nform">
        <view style="display: flex; justify-content: space-between" class="mb-40 lh-42">
          <view class="">
            <text style="color: red">*</text>
            企业名称:
          </view>
          <view style="width: 70%; display: flex; align-items: center">
            <input class="uni-input" name="input" v-model="form.authName" type="text" style="flex: 1" :disabled="inputDisabled" placeholder="请输入企业名称" />
          </view>
        </view>
        <view style="display: flex; justify-content: space-between" class="mb-65 lh-42">
          <view class="">
            <text style="color: red">*</text>
            经办人姓名:
          </view>
          <view style="width: 70%; display: flex; align-items: center">
            <input class="uni-input" name="input" v-model="form.transactorName" type="text" style="flex: 1" :disabled="inputDisabled" placeholder="请输入经办人姓名" />
          </view>
        </view>
        <view style="display: flex; justify-content: space-between" class="mb-65 lh-42">
          <view class="">
            <text style="color: red">*</text>
            经办人手机号:
          </view>
          <view style="width: 70%">
            <input class="uni-input" name="input" v-model="form.phone" type="number" style="flex: 1" :disabled="inputDisabled" placeholder="请输入经办人手机号" maxlength="11" />
            <view style="color: red" class="f-22">*该手机号会做为后续签章过程中主要使用的手机号，请保证该手机号是实名办理的手机号。</view>
          </view>
        </view>
        <view style="display: flex; justify-content: space-between" class="mb-65 lh-42">
          <view class="">
            <text style="color: red">*</text>
            法人身份证号:
          </view>
          <view style="width: 70%">
            <input class="uni-input" name="input" v-model="form.idCard" type="text" style="flex: 1" :disabled="inputDisabled" placeholder="请输入身份证" maxlength="18" />
          </view>
        </view>
      </form>
    </view>
    <view class="" v-if="form.authType == 2">
      <form id="#nform">
        <view style="display: flex; justify-content: space-between" class="mb-65 lh-42">
          <view class="">
            <text style="color: red">*</text>
            姓名:
          </view>
          <view style="width: 70%; display: flex; align-items: center">
            <input class="uni-input" name="input" v-model="form.authName" type="text" style="flex: 1" :disabled="inputDisabled" placeholder="请输入姓名" />
          </view>
        </view>
        <view style="display: flex; justify-content: space-between" class="mb-65 lh-42">
          <view class="">
            <text style="color: red">*</text>
            个人手机号:
          </view>
          <view style="width: 70%">
            <input class="uni-input" name="input" v-model="form.phone" type="number" style="flex: 1" :disabled="inputDisabled" placeholder="请输入手机号" maxlength="11" />
            <view style="color: red" class="f-22">*该手机号会做为后续签章过程中主要使用的手机号，请保证该手机号是实名办理的手机号。</view>
          </view>
        </view>
        <view style="display: flex; justify-content: space-between" class="mb-65 lh-42">
          <view class="">
            <text style="color: red">*</text>
            个人身份证号:
          </view>
          <view style="width: 70%">
            <input class="uni-input" name="input" v-model="form.idCard" type="text" style="flex: 1" :disabled="inputDisabled" placeholder="请输入身份证号" maxlength="18" />
          </view>
        </view>
      </form>
    </view>
    <view class="submitButtonParent" v-if="!inputDisabled">
      <button @tap.stop="submitInfo" class="f-28 c-ff submitButton">前往认证</button>
    </view>
  </view>
</template>

<script>
  const { $http } = require('@/util/methods.js');
  export default {
    data() {
      return {
        form: {
          authName: '',
          authType: 1,
          phone: '',
          transactorName: '',
          idCard: '',
          userId: uni.getStorageSync('user_id') ? uni.getStorageSync('user_id') : ''
        },
        inputDisabled: false,
        checkedValue: true
      };
    },
    onLoad(option) {
      if (option != null && option.disabled != undefined) {
        if (option.disabled == '1') {
          this.inputDisabled = true;
          this.fetchAuthInfo();
        } else {
          this.inputDisabled = false;
        }
      }
    },
    onShow() {},
    methods: {
      submitInfo() {
        if (this.form.authType == 1) {
          if (this.form.authName == '') {
            this.$util.alter('请输入企业名称');
            return false;
          }
          if (this.form.transactorName == '') {
            this.$util.alter('请输入经办人姓名');
            return false;
          }
          if (this.form.phone == '') {
            this.$util.alter('请输入经办人手机号');
            return false;
          }
          if (this.form.idCard == '') {
            this.$util.alter('请输入法人身份证');
            return false;
          }
          if (this.form.idCard.length != '18') {
            this.$util.alter('身份证号码有误');
            return false;
          }
        }
        if (this.form.authType == 2) {
          if (this.form.authName == '') {
            this.$util.alter('请输入姓名');
            return false;
          }
          if (this.form.phone == '') {
            this.$util.alter('请输入手机号');
            return false;
          }
          if (this.form.idCard == '') {
            this.$util.alter('请输入个人身份证');
            return false;
          }
          if (this.form.idCard.length != '18') {
            this.$util.alter('身份证号码有误');
            return false;
          }
        }
        this.$httpUser.post('zx/wap/esign/auth/createAuth', this.form).then((res) => {
          if (res.data.code === 20000) {
            uni.showToast({
              title: '认证信息填写成功',
              icon: 'success'
            });
            console.log(res);
            if (res.data.data.authUrl != '') {
              uni.navigateTo({
                url: '/signature/contract/cManageCheck?url=' + encodeURIComponent(JSON.stringify(res.data.data.authUrl))
              });
            } else {
              uni.navigateBack();
            }
          } else if (res.data.status == 10003) {
            uni.showToast({
              title: res.data.message,
              icon: 'none'
            });
          }
        });
      },
      radioChange(val) {
        this.form.authType = val.detail.value;
        this.form.authName = '';
        this.form.phone = '';
        this.form.transactorName = '';
        this.form.idCard = '';
        if (this.form.authType == 1) {
          this.checkedValue = true;
        } else {
          this.checkedValue = false;
        }
      },
      //获取认证信息
      async fetchAuthInfo() {
        const res = await $http({
          url: 'zx/wap/esign/auth/authInfo'
        });
        if (res) {
          this.form.authName = res.data.authName;
          this.form.authType = res.data.authType;
          this.form.phone = res.data.phone;
          this.form.transactorName = res.data.transactorName;
          this.form.idCard = res.data.idCard;
          if (this.form.authType == 1) {
            this.checkedValue = true;
          } else {
            this.checkedValue = false;
          }
        }
      }
    }
  };
</script>

<style lang="scss" scoped>
  .buton {
    width: 100%;
    position: fixed;
    left: 0;
    bottom: 48rpx;
  }

  .buyButton {
    width: 40%;
    line-height: 74rpx;
    text-align: center;
    border-radius: 38rpx;
    border: 2rpx solid #339378;
  }

  .endbutton {
    color: #339378;
  }

  .fetchbutton {
    background-color: #339378;
    color: #fff;
  }

  .uni-input {
    height: 64rpx;
    padding-left: 32rpx;
    background-color: #f3f8fc;
    margin-left: 20rpx;
  }

  .partnerFlex {
    display: flex;
    justify-content: space-between;
  }

  .lineHiehgt {
    line-height: 64rpx;
  }

  .submitButtonParent {
    width: 100vw;
    height: 100rpx;
    background-color: #fff;
    position: fixed;
    left: 0;
    bottom: 0;
    z-index: 11;
  }

  .submitButton {
    width: 686rpx;
    line-height: 74rpx;
    margin: 0 auto;
    background: #339378;
    border-radius: 38rpx;
  }
</style>
