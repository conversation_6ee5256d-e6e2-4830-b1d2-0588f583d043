<!-- 待处理出货单 -->
<template>
  <view class="positionRelative pb-30">
    <view class="plr-30">
      <view class="pt-40 bg-ff plr-30 radius-15 mt-30" v-for="(item, index) in listS.list" :key="index">
        <view class="list c-66 f-30">
          <view class="pb-30 c-00 b-b">
            <view class="mb-12 flex-s">
              <view class="mb-10">
                {{ item.remark }}
              </view>
              <view class="common_btn common_btn_orange_active" @click="passThrough(item)">{{ item.dealStatus == 0 ? '进货' : '通过' }}</view>
            </view>
            <view class="flex-s f-28 c-66">{{ item.createdTime }}</view>
          </view>
          <view class="ptb-30 f-28 c-66">请在{{ item.expireTime }}内完成确认</view>
        </view>
      </view>
    </view>

    <view v-if="listS.list.length == 0" class="t-c flex-col" :style="{ height: useHeight + 'rpx' }">
      <image :src="imgHost + 'alading/correcting/no_data.png'" class="mb-20 img_s" mode="widthFix"></image>
      <view style="color: #bdbdbd">暂无数据</view>
    </view>
    <view v-if="no_more && listS.list.length > 0">
      <u-divider text="到底了"></u-divider>
    </view>
  </view>
</template>

<script>
  const { $http } = require('@/util/methods.js');
  const Util = require('@/util/util.js');
  export default {
    data() {
      const currentDate = Util.getDate({
        format: true
      });
      return {
        page: 1,
        no_more: false,
        useHeight: 0, //除头部之外高度
        imgHost: getApp().globalData.imgsomeHost,
        date: currentDate,
        listS: {},
        page: 1,
        no_more: false
      };
    },
    onLoad() {},
    onReady() {
      uni.getSystemInfo({
        success: (res) => {
          // 可使用窗口高度，将px转换rpx
          let h = res.windowHeight * (750 / res.windowWidth);
          this.useHeight = h - 35;
        }
      });
    },

    onShow() {
      this.getStocklist();
    },

    onReachBottom() {
      if (this.page >= this.listS.totalPage) {
        this.no_more = true;
        return false;
      }
      this.getStocklist(true, ++this.page);
    },
    methods: {
      // 获取客户成交人数成交率已升级人数
      async getCustomerRelationsNum() {
        const res = await $http({
          url: 'zx/user/selCustomerRelationsNum'
        });
        if (res) {
          this.transactionData = res.data;
        }
      },

      bindDateChange: function (e) {
        this.date = Util.getDateChinese(e.detail.value);
      },

      passThrough(item) {
        console.log('通过');
      },

      // 待处理出货列表
      async getStocklist(isPage, page) {
        let _this = this;
        const res = await $http({
          url: 'zx/invitation/code/outCodeApplyDealPage',
          data: {
            page: page || 1,
            pageSize: 10
          }
        });
        if (res) {
          if (isPage) {
            let old = _this.listS.list;
            _this.listS.list = [...old, ...res.data.list];
          } else {
            _this.listS = res.data;
          }
          console.log(_this.listS.length);
        }
      },

      // 进货申请审核
      async passThrough(item) {
        console.log(item);
        if (item.dealStatus == 1) {
          const res = await $http({
            url: 'zx/invitation/code/auditApplyOutCode',
            data: {
              id: item.id
            }
          });
          if (res) {
            // this.$util.alter('操作成功');
            this.getStocklist();
          }
        } else if (item.dealStatus == 0) {
          debugger;
          uni.navigateTo({
            url: '/supermanClub/supermanSign/stockGoods'
          });
        }
      }
    }
  };
</script>

<style lang="scss" scoped>
  .common_btn {
    background: linear-gradient(to bottom, #88cfba, #1d755c);
  }

  .common_btn1 {
    width: 100%;
    text-align: center;
    border-radius: 45upx;
    height: 98upx;
    line-height: 98upx;
    font-size: 30upx;
  }

  .img_s {
    width: 160rpx;
    height: 160rpx;
  }
</style>
