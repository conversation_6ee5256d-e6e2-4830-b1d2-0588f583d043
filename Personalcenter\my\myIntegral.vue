<template>
  <view class="integral_main_css">
    <view>
      <view class="integral_top_css">
        <text class="f-40 lh-50 inline_block">我的积分</text>
        <!--  -->
        <image class="button_css" @click="getDetailList" src="https://document.dxznjy.com/course/3f20db8d061647fcbd16def11650400a.png"></image>
        <text class="integral_right inline_block f-28">{{ generateSignIn.soonToExpireCredit || 0 }}积分即将过期</text>
      </view>
      <view class="integral_css mt-8">{{ generateSignIn.totalCredit }}</view>
    </view>
    <view class="sign_in_content">
      <!-- question-circle -->
      <view class="question_circle">
        <u-icon @tap="$refs.popopRuleStudent.open()" name="question-circle" color="#333333" size="14"></u-icon>
      </view>
      <view class="display_css_sign">
        <view
          v-for="i in arr"
          :key="i"
          :class="['sign_in_item', { active: i === generateSignIn.currentConsecutiveSignInDays, middle: i < generateSignIn.currentConsecutiveSignInDays }]"
        >
          <view class="content_css">
            <view>
              <text class="f-28 no_select inline_block">
                {{ generateSignIn.currentConsecutiveSignInDays >= i ? '已签到' : '未签到' }}
              </text>
            </view>
            <image class="money_css" src="https://document.dxznjy.com/course/ac555aff9177412395d6112dd079ca3c.png"></image>
            <view v-if="generateSignIn.currentConsecutiveSignInDays >= i" class="f-28 get_select">已获得{{ generateSignIn.currentSignInCreditDetailDtoList[i - 1].credit }}</view>
          </view>
          <view class="day_css">第{{ i }}天</view>
        </view>
        <view :class="['seven_css', { sevenActive: 7 === generateSignIn.currentConsecutiveSignInDays }]">
          <view>
            <text class="f-28 no_select inline_block lh-40">{{ generateSignIn.currentConsecutiveSignInDays == 7 ? '已签到' : '未签到' }}</text>
          </view>
          <image class="seven_image" src="https://document.dxznjy.com/course/ef514f94400543068a481459c19ebea2.png"></image>
          <view v-if="generateSignIn.currentConsecutiveSignInDays >= 7" class="f-28 get_select">已获得{{ generateSignIn.currentSignInCreditDetailDtoList[6].credit }}</view>
        </view>
        <view :class="['day_css w100', { dayActive: 7 === generateSignIn.currentConsecutiveSignInDays }]">第7天</view>
      </view>
    </view>
    <view class="f-20 mt-24 c-55 bottom_text_css">积分商城即将推出~🎉</view>
    <uni-popup ref="popopChooseStudent" type="center">
      <view>
        <image class="popup_image" src="https://document.dxznjy.com/course/a03822d95ec24798a022720003bd99b8.png"></image>
        <view class="bg-ff integral_bottom_css">+{{ integralInfo.signInfCredit }}积分</view>
        <view @tap="$refs.popopChooseStudent.close()" class="close_popup_css"></view>
      </view>
    </uni-popup>
    <!--  -->
    <uni-popup ref="popopRuleStudent" type="center">
      <view>
        <image class="popup_image" mode="widthFix" src="https://document.dxznjy.com/course/f5b7f4b26c2b423f970723f15c223ecd.png"></image>
        <view @tap="$refs.popopRuleStudent.close()" class="close_popup_css"></view>
      </view>
    </uni-popup>
  </view>
</template>

<script>
  const { $navigationTo, $getSceneData, $showError, $showMsg, $http } = require('@/util/methods.js');
  export default {
    data() {
      return {
        generateSignIn: {},
        integralInfo: {},
        app: 0,
        arr: [1, 2, 3, 4, 5, 6]
      };
    },
    onLoad(e) {
      // this.getDetailList()
      console.log(e, '2222222222222222222222222222222222222');
      if (e.token) {
        this.app = e.app;
        this.$handleTokenFormNative(e);
      }
      this.generateCredit();
      this.getInfo();
    },
    onUnload() {
      // #ifdef APP-PLUS
      if (this.app) {
        plus.runtime.quit();
      }
      // #endif
    },
    methods: {
      async generateCredit() {
        // this.$refs.popopChooseStudent.open()
        let _this = this;
        let userId = uni.getStorageSync('user_id') ? uni.getStorageSync('user_id') : '';
        const res = await $http({
          url: 'zx/wap/credit/generate/signIn/credit?userId=' + userId,
          method: 'post',
          showError: 1
        });
        if (res) {
          console.log(res);
          console.log('---------------------------------');
          if (res.data.isFirst) {
            this.$refs.popopChooseStudent.open();
            this.integralInfo = res.data;
          }
          this.getInfo();
        }
      },
      getDetailList() {
        $navigationTo('Personalcenter/my/integralRecord');
      },
      //
      async getInfo() {
        let userId = uni.getStorageSync('user_id') ? uni.getStorageSync('user_id') : '';
        const res = await $http({
          url: 'zx/wap/credit/user/info?userId=' + userId,
          showError: 1
        });
        if (res) {
          this.generateSignIn = res.data;
          console.log(this.generateSignIn, 22222);
          uni.setStorageSync('totalCredit', this.generateSignIn.totalCredit);
        }
      }
    }
  };
</script>

<style lang="scss" scoped>
  .integral_main_css {
    width: 750rpx;
    background: url('https://document.dxznjy.com/course/bfc6b6c2e2a146318f7280e528242ec3.png') no-repeat;
    background-size: 100%;
    .inline_block {
      display: inline-block;
      vertical-align: middle;
    }
    .integral_top_css {
      padding-left: 32rpx;
      padding-top: 52rpx;
      .button_css {
        width: 164rpx;
        height: 50rpx;
        margin-left: 16rpx;
        vertical-align: middle;
      }
      color: #11372e;
      .integral_right {
        margin-left: 104rpx;
      }
    }
    .integral_css {
      color: #13392f;
      font-size: 64rpx;
      line-height: 80rpx;
      font-weight: WenYiHei;
      padding-left: 32rpx;
    }
    .sign_in_content {
      width: 686rpx;
      margin: 0 auto;
      margin-top: 18rpx;
      padding-top: 128rpx;
      background: url('https://document.dxznjy.com/course/b5584255e6f048fb8d8f025b9756e0fc.png') no-repeat;
      background-size: 100%;
      padding-bottom: 32rpx;
      text-align: center;
      position: relative;
      .display_css_sign {
        display: flex;
        justify-content: flex-start;
        flex-wrap: wrap;
        padding-left: 32rpx;
        .sign_in_item {
          margin-right: 28rpx;
          margin-bottom: 24rpx;
          .content_css {
            width: 190rpx;
            height: 192rpx;
            background-color: #f5f6f7;
            border-radius: 16rpx;

            .money_css {
              width: 50rpx;
              height: 52rpx;
              margin: auto;
            }
          }
        }
        .middle {
          .content_css {
            background-color: #f2fffc;
          }
          .day_css {
            color: #173322;
          }
          .get_select {
            color: #5cc89e;
            padding: 5rpx 0;
          }
          .no_select {
            color: #5cc89e;
          }
          // #173322
        }
        .active {
          .content_css {
            background-color: #5dc99f;
          }
          .no_select {
            color: #fff;
          }
          .get_select {
            color: #fff;
            padding: 5rpx 0;
          }
          .day_css {
            color: #173322;
          }
        }
      }
      .day_css {
        color: #a2a4a8;
        margin-top: 16rpx;
      }
      .no_select {
        color: #a2a4a8;
        padding: 16rpx 0;
      }
      .dayActive {
        color: #173322;
      }
      .seven_css {
        background-color: #f5f6f7;
        width: 626rpx;
        height: 252rpx;
        .seven_image {
          width: 126rpx;
          height: 114rpx;
          margin-top: 16rpx;
        }
      }
      .sevenActive {
        background-color: #fff9e5;
        .no_select {
          color: #fa7717;
        }
        .get_select {
          color: #fa7717;
          margin-top: -10rpx;
        }
      }
      .question_circle {
        position: absolute;
        top: 27rpx;
        left: 170rpx;
      }
    }
    .bottom_text_css {
      text-align: center;
    }
    padding-bottom: 100rpx;
  }
  .popup_image {
    display: block;
    width: 686rpx;
    height: 718rpx;
    margin: 0 auto;
  }
  .integral_bottom_css {
    position: absolute;
    width: 400rpx;
    left: 130rpx;
    bottom: 52rpx;
    color: #fa7b1e;
    font-weight: 600;
    font-size: 74rpx;
    line-height: 100rpx;
    text-align: center;
  }
  .close_popup_css {
    position: absolute;
    width: 50rpx;
    height: 50rpx;
    top: 303rpx;
    right: 24rpx;
  }
</style>
