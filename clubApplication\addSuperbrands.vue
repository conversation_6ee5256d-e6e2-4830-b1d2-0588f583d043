<template>
  <view class="partner p-30 f-28">
    <view class="bold">申请人信息:</view>
    <form id="#nform">
      <view class="partnerFlex" style="margin-top: 44rpx; margin-bottom: 100rpx">
        <view class="bold lineHiehgt">
          <text style="color: red">*</text>
          姓名:
        </view>
        <view style="width: 60%">
          <input class="uni-input" name="input" placeholder="请输入姓名" maxlength="20" v-model="name" type="text" />
          <text style="font-size: 24rpx; color: #c6c6c6">请输入真实姓名，便于后续业务对接</text>
        </view>
      </view>
    </form>
    <view class="submitButton">
      <button @tap.stop="submitInfo" class="f-28 c-ff">提交</button>
    </view>
  </view>
</template>

<script>
  const { $http } = require('@/util/methods.js');
  export default {
    data() {
      return {
        name: '',
        clubCode: '',
        userId: ''
      };
    },
    onLoad(option) {
      let a = uni.getStorageSync('brand');
      if (a) {
        uni.switchTab({
          url: '/pages/home/<USER>/index'
        });
      }
      if (option != null) {
        this.userId = option.userId;
      }
    },
    methods: {
      async submitInfo() {
        if (this.name == '') {
          this.$util.alter('姓名不能为空');
          return false;
        }
        const res = await $http({
          url: 'zx/user/merchantApply',
          method: 'post',
          data: {
            realName: this.name,
            phone: uni.getStorageSync('phone') ? uni.getStorageSync('phone') : '',
            merchantType: 3,
            shareId: this.userId
          }
        });
        if (res) {
          uni.showToast({
            title: '已提交申请',
            icon: 'success'
          });
          uni.switchTab({
            url: '/pages/home/<USER>/index'
          });
        }
      }
    }
  };
</script>

<style lang="scss" scoped>
  .partner {
    width: 100vw;
    height: 100vh;
    background-color: #fff;
    box-sizing: border-box;
  }
  .uni-input {
    height: 64rpx;
    padding-left: 32rpx;
    background-color: #f3f8fc;
  }
  .partnerFlex {
    display: flex;
    justify-content: space-between;
  }
  .lineHiehgt {
    line-height: 64rpx;
  }
  .submitButton {
    width: 686rpx;
    line-height: 74rpx;
    margin: 0 auto;
    background: #339378;
    border-radius: 38rpx;
    position: fixed;
    bottom: 32rpx;
  }
</style>
