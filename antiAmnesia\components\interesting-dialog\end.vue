<template>
	<view class="interesting_popupCenter" :class="gradScreenWheel==3?'interesting_popupCenter1':''">

		<view class="interet_head" :style="gradScreenWheel==2?'width:180rpx':''">
				<image class="interet_popupTitle1" :src="imgHost+'interesting/dialog_interesting_review_title.png'">
				<!-- <image class="interet_popupTitle interet_popupTitle1" :src="imgHost+'interesting/'+titleIcon[showData.nowLevel-1]" mode="" /> -->
				<!-- <image v-if="gradScreenWheel==3" class="interet_popupTitle interet_popupTitle2" :src="imgHost+'interesting/class_icon9.png'" mode="" /> -->
		</view>

		<!-- <view class="popupText" v-if="gradScreenWheel==3">
			<text style="color: red;">已完成所有内容</text>
		</view>
		<view class="popup_content" v-if="gradScreenWheel==3">
			<view class="reviewPopup_list">
				<text>复习词数</text>
				<text class="DesignFont">{{reviewWordNum.length+errorWordNum.length}}</text>
			</view>
			<view class="reviewPopup_list">
				<text>错误词数</text>
				<text class="DesignFont" style="color: #ff3c3c;">{{errorWordNum.length}}</text>
			</view>
			<view class="reviewPopup_list">
				<text>正确率</text>
				<text class="DesignFont" style="color: #3c6c3c;">{{correctRate}}%</text>
			</view>
		</view> -->
		<!-- 组的文字 -->
		<!-- <view class="finishAnd" v-if="gradScreenWheel==1&&reviewWordNum.length+errorWordNum.length!=0">
			已完成第 <text>{{showData.nowGroup}}</text> 组复习,
			当前关卡待复习 <text>{{showData.levelTotalGroup - showData.levelGroup}}</text> 组
		</view> -->

		<!-- 关的文字 -->
		<!-- <view class="finishAnd" v-if="gradScreenWheel==2" :style="isIndex?'margin:60rpx':''">恭喜您通过第{{gradChinese[showData.nowLevel-1]}}关</view> -->

		<!-- 复习报告复习词数，错误词数，正确率 -->
		<!-- <view class="popup_content" v-if="gradScreenWheel<3&&reviewWordNum.length+errorWordNum.length!=0&&!isIndex">
			<view class="reviewPopup_list">
				<text>复习词数</text>
				<text class="DesignFont">{{reviewWordNum.length+errorWordNum.length}}</text>
			</view>
			<view class="reviewPopup_list">
				<text>错误词数</text>
				<text class="DesignFont" style="color: #ff3c3c;">{{errorWordNum.length}}</text>
			</view>
			<view class="reviewPopup_list">
				<text>正确率</text>
				<text class="DesignFont" style="color: #3c6c3c;">{{correctRate}}%</text>
			</view>
		</view> -->

		<!-- 轮的文字 -->
		<view class="finishAnd1" v-if="gradScreenWheel==3">恭喜您完成本轮学习</view>


		<!-- <view class="popBtnGroup popBtnGroup1" v-if="gradScreenWheel==1">
			<view class="popBtnGroup_list" @click="confirm()">查看错词</view>
			<view class="popBtnGroup_list" @click="rightClick()">继续复习</view>
		</view>
		<view class="popBtnGroup popBtnGroup1" v-if="gradScreenWheel==2" :style="isIndex?'margin-top: 90rpx;':''">
			<view class="popBtnGroup_list" @click="confirm()">查看错词</view>
			<view class="popBtnGroup_list" @click="rightClick()">下一关</view>
		</view> -->
		<view class="popBtnGroup" v-if="gradScreenWheel==3" >
			<view class="popBtnGroup_list" @click="rightClick()">返回检测</view>
			<view class="popBtnGroup_list" @click="rightClick()">其他玩法</view>
		</view>
		<!-- <view class="popBtnGroup" v-if="gradScreenWheel==4" style="margin-top: 90rpx;">
			<view class="popBtnGroup_list" @click="confirm()">查看错词</view>
			<view class="popBtnGroup_list" @click="rightClick()">其它玩法</view>
		</view> -->

		<!-- <image @click="closeDialog()" class="interesting_close" :src="imgHost+'interesting/dialog_closed.png'" mode=""></image> -->
	</view>



</template>

<script>
	import interestChart from "@/common/interestChart.js"
	export default {
		//是否是复习弹窗 //gradScreenWheel 1是组，2是关卡，3是轮    4是不可进入下一组
		//只有是组的时候展示需要nowGroup当前组 waitGroup 待复习的组
		//grad 一关结束，第几关    
		props: {
			reviewWordNum: Array,
			errorWordNum: Array,
			correctRate: Number,
			gradScreenWheel: Number,
			showData:{
				roundId:Number,	
				nowGroup: Number,
				waitGroup: Number,
			},
			isIndex:false,	
			play:String,
			scheduleCode:String,
			pageUrl:String,
			studentCode:String
		},
		data() {
			return {
				imgHost: getApp().globalData.imguseHost,
				titleIcon: [
					'class_icon1.png',
					'class_icon2.png',
					'class_icon3.png',
					'class_icon4.png',
					'class_icon5.png',
					'class_icon6.png',
					'class_icon7.png',
					'class_icon8.png'
				],
				gradChinese: ['一', '二', '三', '四', '五', '六', '七', '八'],
				showDataList:[],
			}
		},
		methods: {
			//确认
			confirm() {
				let that = this;
				if (that.gradScreenWheel != 3) {
					console.log("查看错词")
					let status;
					if(that.gradScreenWheel==1){status=3;}
					if(that.gradScreenWheel==2){status=2;}
					if(that.gradScreenWheel==3){status=1;}
					if(that.gradScreenWheel==4){status=4;}
					var wrongData={
						"roundId":that.showData.roundId,
						"nowLevel":that.showData.nowLevel,
						"nowGroup":that.showData.nowGroup,
						"play":that.play,
						"status":status,
						scheduleCode:that.scheduleCode,
						"merchantCode":that.showData.merchantCode
					}

					if(that.isIndex){
						that.$httpUser.get(`znyy/course/getWrongWords?play=${wrongData.play}&status=${wrongData.status}&nowGroup=${wrongData.nowGroup}&nowLevel=${wrongData.nowLevel}&roundId=${wrongData.roundId}`).then((res) => {
							if (res.data.success) {
								if(res.data.data.data.length==0){
									that.$util.alter("太棒了，答题全部正确");
								}else{
									uni.navigateTo({
										url: `/interestModule/wordIdentifying?wrongData=${encodeURIComponent(JSON.stringify(wrongData))}`
									})
								}
								
							} else {
								that.$util.alter(res.data.message)
							}
						})
						return;
					}
					
					if(that.errorWordNum.length==0){
						that.$util.alter("太棒了，答题全部正确");
						setTimeout(function(){
							uni.navigateTo({
								url: '/antiAmnesia/review/funReview?scheduleCode=' + that.scheduleCode + '&merchantCode='+that.showData.merchantCode
							})
						},1000);
					}else{
						uni.navigateTo({
							url: `/interestModule/wordIdentifying?wrongData=${encodeURIComponent(JSON.stringify(wrongData))}`
						})
					}
					return;
				}
				if (that.gradScreenWheel == 3) {	
					that.$httpUser.get(`znyy/stats/review/getAnalysisList?scheduleCode=${that.scheduleCode}`).then((res) => {
						if(res.data.success){						
							if(res.data.data.length!=0){
								that.showDataList = res.data.data.data;
								for(let i=0;i<that.showDataList.length;i++){
									let item = that.showDataList[i];
									if(item.roundId==that.showData.roundId){
										var data = {
											scheduleCode:that.scheduleCode,
											roundId:item.roundId,
											rightRate:item.rightRate,
											scoreGrade:item.scoreGrade
										};
										
										let turnGradeStudyData;
										// 轮次报告学情走势
										interestChart.turnGradeStudy(item.roundId).then((res)=>{
											turnGradeStudyData = res;
											// uni.navigateTo({
											// 	url: "/pages/interest/turnGrade?turnGradeData="+ encodeURIComponent(JSON.stringify(data))+'&turnGradeStudyData='+encodeURIComponent(JSON.stringify(turnGradeStudyData))
											// })
										});
										return;
									}
								}	
								
							}
						}else{
							uni.hideLoading()
							that.$util.alter(res.data.message)
						}										
					})
					
				}
				
				
			},
			//取消
			rightClick() {
				let that = this;
				// if (that.gradScreenWheel == 1) {
				// 	console.log("继续复习")
				// 	that.startNewGroup();
				// 	return;
				// }
				// if (that.gradScreenWheel == 2) {
				
				// 	console.log("开启下一关")
				// 	that.startNewLevel();
				// 	return;
				// }
				// if (that.gradScreenWheel == 3) {
				// 	console.log("开启下一轮")
				// 	that.levelPlayAgain();
				// 	return;
				// }
				// if (that.gradScreenWheel == 4) {
					uni.redirectTo({
						url: '/antiAmnesia/review/studyFunReview?scheduleCode=' + that.scheduleCode +'&studentCode=' + that.studentCode,
					})
					return;
				// }		
			},
			// 进入下一组
			startNewGroup() {
				let that = this;
				that.$httpUser.get(`znyy/course/start/new/group?scheduleCode=${that.scheduleCode}`).then(
					(res) => {
						if (res.data.success) {
							console.log("进入下一组");
							that.getShowDataToNextPage();
						} else {
							that.$util.alter(res.data.message)
						}
					})
			},
			// 进入下一关
			startNewLevel() {
				let that = this;
				
				that.$httpUser.get(`znyy/course/start/new/level?scheduleCode=${that.scheduleCode}`).then(
					(res) => {
						if (res.data.success) {
							console.log("进入下一关");
							that.getShowDataToNextPage();
						} else {
							that.$util.alter(res.data.message)
						}
					})
			},
			// 进入下一轮
			levelPlayAgain() {
				let that = this;
				that.$httpUser.get(`znyy/course/level/play/again?scheduleCode=${that.scheduleCode}`).then((res) => {
					if (res.data.success) {
						console.log("进入下一轮");
						this.getShowDataToNextPage()
					} else {
						that.$util.alter(res.data.message)
					}
				})
			},
			
			// 获取返回页展示数据
			getShowDataToNextPage() {
				let that = this;
				console.log(909090909090)
				console.log(that.scheduleCode)
				let merchantCode = that.showData.merchantCode;
				if(that.pageUrl=='funReview'){
					uni.navigateTo({
						url: '/antiAmnesia/review/funReview?scheduleCode=' + that.scheduleCode + '&merchantCode='+merchantCode
					})
					return;
				}
				that.$httpUser.get(`znyy/course/query/fun/words?scheduleCode=${that.scheduleCode}`).then((res) => {
					if (res.data.success) {
						let showData = res.data.data
						console.log(showData)
						showData.scheduleCode = that.scheduleCode;
						showData.merchantCode = merchantCode;
						var url = "/interestModule/" + that.pageUrl;
						uni.navigateTo({
							url: url + `?params=` + encodeURIComponent(JSON.stringify(showData))
						})		
					} else {
						that.$util.alter(res.data.message)
					}
				})
			},
			
			
			
			//关闭弹窗
			closeDialog() {
				this.$emit('closeDialog')
			}
		}
	}
</script>

<style>
	.interesting_popupCenter {
		height: 750rpx;
		position: relative;
		background: url('https://document.dxznjy.com/applet/interesting/dialog_interesting_review.png') top no-repeat;
		background-size: 100% 670rpx;
	}

	.interesting_popupCenter1 {
		height: 680rpx;
		background: url('https://document.dxznjy.com/applet/interesting/dialog_bg.png') top no-repeat;
		background-size: 100% 560rpx;
	}
	
	.interet_popupTitle1 {
		width: 225rpx;
		height: 200rpx !important;
		margin-top: -18rpx;
	}

	.popupText {
		height: 50rpx;
		line-height: 50rpx;
	}

	.finishAnd {
		display: inline-block;
		font-size: 24rpx;
		color: #FFFFFF;
		margin: 10rpx 0;
		text-align: center;
		padding: 0 30rpx;
		height: 60rpx;
		line-height: 60rpx;
		background-color: rgba(28, 16, 8, .8);
		border-radius: 50rpx;
	}

	.finishAnd text {
		color: #eb7c10;
	}

	.interet_popupTitle1 {
		width: 210rpx;
		height: 230rpx;
		margin-bottom: 0;
	}

	.interet_popupTitle2 {
		width: 470rpx;
		height: 394rpx;
		margin: -85rpx 45rpx 10rpx 60rpx;
	}

	.finishAnd1 {
		font-size: 45rpx;
		color: #e93c43;
		font-weight: bold;
		margin-bottom: 50rpx;
	}
</style>
