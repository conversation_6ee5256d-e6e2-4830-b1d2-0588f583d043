<template>
  <view v-if="newslist.length == 0" class="t-c flex-col bg-ff radius-15 mlr-30" :style="{ height: useHeight + 'rpx' }">
    <image :src="imgHost + 'dxSelect/fourthEdition/none-data.png'" class="mb-20 img"></image>
    <view style="color: #bdbdbd" class="f-30">暂无数据</view>
  </view>
  <view v-else class="container">
    <view class="content">
      <view class="all_read" v-if="newslist.length > 0" @click="reading()">全部已读</view>
      <view class="item" v-for="item in newslist" :key="item.id" @click="reading(item)">
        <view style="border-bottom: 2rpx solid #efefef">
          <view class="title">
            <view style="display: flex">
              <view class="icon">
                <view>
                  <image style="width: 48rpx; height: 48rpx" :src="iconList[item.iconType]"></image>
                </view>
                <view v-if="item.type !== 3 && item.type !== 4 && item.type !== 8 && item.status === 0" class="red_dot"></view>
              </view>
              <view>{{ item.title }}</view>
            </view>
            <view class="title_right">
              <u-tag v-if="(item.type === 3 || item.type === 4 || item.type === 8) && item.status === 0" text="上新" type="success" plain size="mini"></u-tag>
              <view style="margin-left: 8rpx">
                {{ formatTime(item.createTime) }}
              </view>
            </view>
          </view>
          <view class="item_content">{{ item.content }}</view>
        </view>
        <view class="detail">查看详情</view>
      </view>
    </view>
    <!-- 加载更多 -->
    <view v-if="no_more && newslist != undefined && newslist.length > 0">
      <u-divider text="到底了"></u-divider>
    </view>
  </view>
</template>

<script>
  import dayjs from 'dayjs';
  export default {
    data() {
      return {
        data: {
          pageNum: 1,
          pageSize: 10
        },
        array: [],
        newStatus: '', // 消息状态
        newslist: [], // 消息通知列表
        totalItems: '', // 消息总数量
        ifBottomRefresh: false,
        status: 'more', // 加载更多
        useHeight: 0,
        no_more: false,
        imgHost: getApp().globalData.imgsomeHost,
        iconList: [
          'https://document.dxznjy.com/course/1a3fca782f624ae1a82aea153083eb3d.png',
          'https://document.dxznjy.com/course/e82798aa2d044723b8d36d599cf39d60.png',
          'https://document.dxznjy.com/course/999a66f3a397420eaee06cac3fe98910.png',
          'https://document.dxznjy.com/course/a0a7b92497e44e2b82ef01ab67cabecd.png',
          'https://document.dxznjy.com/course/2bca18465b7b4df193d93d7c81fa8936.png',
          'https://document.dxznjy.com/course/52f4048a167f452f808f7fc786353684.png'
        ],
        app: 0
      };
    },
    onUnload() {
      // #ifdef APP-PLUS
      if (this.app && !this.isJumpNext) {
        plus.runtime.quit();
      }
      // #endif
    },
    onLoad(e) {
      // #ifdef APP-PLUS
      this.app = e.app;
      this.$handleTokenFormNative(e);
      // #endif
    },
    onShow() {
      this.newslist = [];
      this.data.pageNum = 1;
      let token = uni.getStorageSync('token');
      this.data.mobile = uni.getStorageSync('phone') || '';
      console.log(token);
      if (token) {
        this.getNewslist(); // 消息通知
      } else {
        console.log('没有token');
      }
    },
    onReady() {
      uni.getSystemInfo({
        success: (res) => {
          // 可使用窗口高度，将px转换rpx
          let h = res.windowHeight * (750 / res.windowWidth);
          this.useHeight = h - 30;
        }
      });
    },
    // 下拉刷新
    async onPullDownRefresh() {
      this.newslist = [];
      this.data.pageNum = 1;
      await this.getNewslist();
      uni.stopPullDownRefresh();
    },
    // 触底的事件
    onReachBottom() {
      console.log('触底');
      if (this.data.pageNum * 10 >= Number(this.totalItems)) {
        this.no_more = true;
        return false;
      }
      this.getNewslist(true, ++this.data.pageNum);
    },
    methods: {
      // 已读
      reading(item) {
        if (item && item.id) {
          console.log(item, JSON.stringify(item), 'iiiiii');
          uni.navigateTo({
            url: '/splitContent/message/MessageDetial?app=2&item=' + encodeURIComponent(JSON.stringify(item)),
            success: (res) => {
              console.log(res);
            }
          });
        }
        if (item && (item.type === 3 || item.type === 4 || item.type === 8)) return;
        this.getReadlist(item ? item.id : '');
      },

      // 通知列表
      async getNewslist(isPage, page) {
        uni.showLoading({
          title: ''
        });
        let res = await this.$httpUser.get('zx/message/notice/page', this.data);
        console.log(res, 99999);
        if (res.data.status === 1) {
          if (isPage) {
            let old = this.newslist;
            this.newslist = [...old, ...res.data.data.data];
          } else {
            console.log(res.data.data.data, 88888);
            this.newslist = res.data.data.data;
          }
          this.totalItems = res.data.data.totalItems;
        }
        uni.hideLoading();
      },
      // 已读通知
      async getReadlist(id) {
        console.log(id);
        let data = ``;
        if (id) {
          data = `&id=${id}`;
        } else {
          data = '';
        }
        let res = await this.$httpUser.post(`zx/message/notice/modification/status?status=1&mobile=${this.data.mobile}` + data);
        console.log(res, 7777);
        if (res.data.data === 'success') {
          console.log('已读');
          this.data.pageNum = 1;
          this.getNewslist();
        } else {
          this.$util.alter(res.data.message);
        }
      },
      // format
      formatTime(time) {
        return dayjs(time).format('MM/DD');
      }
    }
  };
</script>
<style lang="scss" scoped>
  .container {
    padding: 32rpx;
    .all_read {
      height: 40rpx;
      font-size: 28rpx;
      color: #5a5a5a;
      line-height: 40rpx;
      text-align: right;
      margin-bottom: 8rpx;
    }
  }
  .content {
    min-height: calc(100vh - 64rpx);
    .item {
      max-height: 310rpx;
      margin-top: 24rpx;
      padding: 28rpx 24rpx 32rpx 24rpx;
      background-color: #fff;
      border-radius: 16rpx;
      .title {
        display: flex;
        justify-content: space-between;
        align-items: center;
        height: 48rpx;
        margin-bottom: 30rpx;
        font-size: 32rpx;
        color: #333333;
        line-height: 48rpx;
        .icon {
          position: relative;
          display: flex;
          height: 48rpx;
          margin-right: 14rpx;
          .red_dot {
            position: absolute;
            top: 0;
            right: 0;
            width: 16rpx;
            height: 16rpx;
            border-radius: 50%;
            background-color: #fb4545;
          }
        }
        .title_right {
          display: flex;
          align-items: center;
          font-size: 24rpx;
          color: #aaaaaa;
        }
      }
      .item_content {
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2; /* 限制为两行 */
        overflow: hidden;
        text-overflow: ellipsis; /* 超出部分用省略号表示 */
        font-size: 28rpx;
        color: #777777;
        margin-bottom: 32rpx;
      }
    }
    .detail {
      margin-top: 30rpx;
      font-size: 28rpx;
      color: #333333;
    }
  }
  ::v-deep .u-tag--mini {
    height: 32rpx !important;
  }

  .img {
    width: 160rpx;
    height: 165rpx;
  }
</style>
