<template>
  <view>
    <view class="dialogBG">
      <view class="reviewCard_box positionRelative">
        <view class="cartoom_image">
          <image :src="dialog_iconUrl" mode="widthFix"></image>
        </view>
        <view class="review_close" @click="closeDialog">
          <uni-icons type="clear" size="28" color="#B1B1B1"></uni-icons>
        </view>
        <view class="reviewCard">
          <view class="reviewTitle bold">{{ title }}</view>

          <view class="dialogContent">
            {{ content }}
          </view>

          <view class="review_btn" @click="confirm()">{{ operate }}</view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
  import Util from '@/util/util.js';
  export default {
    props: {
      title: {
        type: String,
        default: '温馨提示'
      },
      content: {
        type: String,
        default: ''
      },
      showCancel: {
        type: Boolean,
        default: true
      },
      operate: {
        type: String,
        default: '确定'
      }
    },
    data() {
      return {
        dialog_iconUrl: Util.getCachedPic('https://document.dxznjy.com/dxSelect/dialog_icon.png', 'dialog_icon_path')
      };
    },
    methods: {
      //确认
      confirm() {
        this.$emit('confirm');
      },
      //取消
      cancel() {
        this.closeDialog();
      },
      //关闭弹窗
      closeDialog() {
        this.$emit('closeDialog');
      }
    }
  };
</script>

<style>
  .dialogBG {
    width: 100%;
    height: 100%;
  }

  /* 21天结束复习弹窗样式 */
  .reviewCard_box {
    width: 670rpx;
    /* height: 560rpx; */
    position: relative;
  }

  .reviewCard_box image {
    width: 100%;
    height: 100%;
  }

  .reviewCard {
    width: 100%;
    height: 100%;
    background: #ffffff;
    border-radius: 24upx;
    padding: 30upx;
    box-sizing: border-box;
    position: relative;
  }

  .cartoom_image {
    width: 420rpx;
    position: absolute;
    top: -250rpx;
    left: 145rpx;
    z-index: -1;
  }

  .review_close {
    position: absolute;
    top: 20rpx;
    right: 20rpx;
    z-index: 1;
  }

  .reviewTitle {
    margin-top: 20rpx;
    width: 100%;
    text-align: center;
    font-size: 34upx;
    display: flex;
    justify-content: center;
  }
  .dialogContent {
    padding: 0 30upx;
    box-sizing: border-box;
    margin: 80upx 0;
    font-size: 32upx;
    line-height: 45upx;
    text-align: center;
  }

  .review_btn {
    width: 240upx;
    height: 80upx;
    background: linear-gradient(180deg, #88cfba 0%, #1d755c 100%);
    border-radius: 45upx;
    font-size: 30upx;
    color: #ffffff;
    line-height: 80upx;
    margin: 41rpx auto 38rpx auto;
    justify-content: center;
    text-align: center;
  }

</style>
