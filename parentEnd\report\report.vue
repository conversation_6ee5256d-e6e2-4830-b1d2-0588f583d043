<template>
  <view class="container">
    <!-- 顶部箭头返回按钮 -->
    <view class="arrow">
      <uni-icons type="left" size="20" color="#FFFFFF" @click="back"></uni-icons>
    </view>
    <!-- 顶部文字 -->
    <view class="title">
      <view class="title-img">
        <image src="https://document.dxznjy.com/dxSelect/report/bgc-top.png" mode=""></image>
      </view>
      <!-- <view class="title-text">
				<view class="text-content">
					<view>鼎校</view>
					<view>高效学习倡导者</view>
					<view>—</view>
					<view>英语水平专业评测报告</view>
				</view>
			</view> -->
      <!-- 	<view class="title-img">
				<image src="https://document.dxznjy.com/applet/newimages/book.png" mode=""></image>
			</view> -->
    </view>
    <!-- 学员信息盒子 -->
    <view class="information boxbg">
      <text class="information-title studentTile">基本信息</text>
      <view class="information-top">
        <view class="text-one">{{ reportList.realName || '' }}同学 你的词汇量约为</view>
        <view class="text-two">
          <text class="big yellow">{{ reportList.wordLevel || '0' }}</text>
          <text class="yellow">词</text>
        </view>
        <view class="text-three">
          相当于
          <text class="">{{ reportList.title || '' }}</text>
        </view>
        <view class="text-four">评测日期：{{ reportList.addTime || '' }}</view>
      </view>
      <view class="information-bootom">
        <view class="studentTile">学员信息</view>
        <!-- 				<view class="bootom-text">
					<text>姓名：{{reportList.realName || ''}}</text>
					<text style="margin-right: 170rpx;">年级：{{reportList.grade+"年级" || ''}}</text>
				</view>
				<view class="bootom-text">
					<view>登录账号：{{reportList.loginName || ''}}</view>
					<text>号码：{{reportList.phone || ''}}</text>
				</view>
				<view class="bootom-text">
					<text>学校：{{reportList.school || ''}}</text>
				</view> -->
        <view class="">
          <view class="bootom-text">
            <view class="" style="width: 50%">姓名：{{ reportList.realName || '' }}</view>
            <view class="" style="width: 50%">年级：{{ reportList.gradeName || '' }}</view>
          </view>
          <view class="bootom-text">
            <view style="width: 50%">学校：{{ reportList.school || '' }}</view>
            <view class="" style="width: 50%">号码：{{ reportList.phone || '' }}</view>
          </view>
          <view class="bootom-text">
            <view class="">登录账号：{{ reportList.loginName || '' }}</view>
          </view>
        </view>
      </view>
    </view>
    <!-- 零起点总词汇量 -->
    <view class="starting-point boxbg">
      <view class="point-top studentTile">各年级英语水平划分表</view>
      <view class="point-title">{{ reportList.title || '' }}总词汇量：{{ reportList.wordUpperLimit || '' }}词</view>
      <!-- 中间金字塔 -->
      <view class="pyramid">
        <image src="https://document.dxznjy.com/dxSelect/report/xyjzt.png" mode="" style="height: 300rpx"></image>
        <!-- <image src="https://document.dxznjy.com/applet/newimages/pyramid.png" class="pyramid-img"></image> -->
        <!-- <view class="pyramid-text text-one">
					<view>Dlevel</view>
					<view>范围：雅思</view>
				</view>
				<view class="pyramid-text text-two">
					<view>Clevel</view>
					<view>范围：高中</view>
				</view>
				<view class="pyramid-text text-three">
					<view>Blevel</view>
					<view>范围：初中</view>
				</view>
				<view class="pyramid-text text-four">
					<view>Alevel</view>
					<view>范围：小学</view>
				</view> -->
      </view>
      <!-- <view class="point-footer">各年级英语水平划分表</view> -->
    </view>
    <!-- 现实掌握词汇 -->
    <view class="master boxbg" style="height: 560rpx; padding-bottom: 40rpx">
      <view class="ceping studentTile point-top">测评结果柱状对比图</view>
      <view class="master-title">现实掌握词汇：{{ reportList.wordLevel || '' }}词</view>
      <!-- 柱状图 -->
      <view style="margin-left: 20rpx; margin-right: 20rpx">
        <columnarEcharts :lineChartData="lineChartData"></columnarEcharts>
        <!-- 				<uni-ec-canvas class="uni-ec-canvas" id="line-chart" ref="canvas" canvas-id="lazy-load-chart"
					:ec="ec"></uni-ec-canvas> -->
        <!-- 	<view class="master-flex">
					<view class="" style="width: 50%;text-align:center;">
						现实掌握词汇量
					</view>
					<view class="" style="width: 50%;text-align:center;">
						{{wordtitle}}
					</view>

				</view> -->
      </view>
      <!-- <view class="ceping">测评结果柱状对比图</view> -->
    </view>
    <!-- 底部文字 -->
    <view class="footer boxbg" style="padding-bottom: 40rpx">
      <view class="footer-box">
        <view class="footer-title">
          <text class="border"></text>
          <text class="footer-t" style="margin-left: 20rpx">测评结果</text>
        </view>
        <view class="" style="display: flex; justify-content: center; align-items: center">
          <image src="https://document.dxznjy.com/dxSelect/report/cpjg.png" mode="" style="width: 400rpx; height: 400rpx"></image>
        </view>
        <view class="footer-text">{{ reportList.levelDescription || '' }}</view>
        <!-- 	<view v-if="reportList.evaluationAnalysis">
					<view class="footer-title">
						<text class="border"></text>
						<text style="margin-left: 20rpx;">测评分析</text>
					</view>
					<view class="footer-text">{{reportList.evaluationAnalysis || ''}}</view>
				</view>
				<view class="footer-title">
					<text class="border"></text>
					<text style="margin-left: 20rpx;">学习建议</text>
				</view>
				<view class="footer-text">{{reportList.learningAdvice || ''}}</view> -->
      </view>
    </view>
    <view class="footer boxbg" style="padding-bottom: 40rpx">
      <view class="footer-box">
        <view>
          <view class="footer-title">
            <text class="border"></text>
            <text class="footer-t" style="margin-left: 20rpx">测评分析</text>
          </view>
          <view class="" style="display: flex; justify-content: center; align-items: center">
            <image src="https://document.dxznjy.com/dxSelect/report/cpfx.png" mode="" style="width: 400rpx; height: 400rpx"></image>
          </view>
          <view class="footer-text">{{ reportList.evaluationAnalysis || '' }}</view>
        </view>
        <view class="">
          <view class="footer-title">
            <text class="border"></text>
            <text class="footer-t" style="margin-left: 20rpx">测评建议</text>
          </view>
          <view class="" style="display: flex; justify-content: center; align-items: center">
            <image src="https://document.dxznjy.com/dxSelect/report/cpjy.png" mode="" style="width: 400rpx; height: 400rpx"></image>
          </view>
          <view class="footer-text">{{ reportList.learningAdvice || '' }}</view>
        </view>
      </view>
    </view>
    <!-- 	<view class="footer boxbg" style="padding-bottom: 40rpx;">
			<view class="footer-box">
				<view class="footer-title">
					<text class="border"></text>
					<text class="footer-t" style="margin-left: 20rpx;">测评建议</text>
				</view>
				<view class="">
					<image src="../../static/index/cpjy.png" mode="" style="width: 400rpx;height: 400rpx;"></image>
				</view>
				<view class="footer-text">{{reportList.learningAdvice || ''}}</view>
			</view>
		</view> -->
  </view>
</template>

<script>
  import columnarEcharts from '../components/jp-charts/columnar-echarts.vue';
  // import uniEcCanvas from '@/echarts/uni-ec-canvas/uni-ec-canvas.vue'
  // import * as echarts from '@/echarts/uni-ec-canvas/echarts'
  export default {
    components: {
      // uniEcCanvas
      columnarEcharts
    },
    data() {
      return {
        wordtitle: '',
        ec: {
          lazyLoad: true,
          option: {
            grid: {
              left: '0%',
              right: '0%',
              bottom: '0%',
              containLabel: true
            },
            xAxis: [
              {
                // data: [1, 2, 3, 4, 5, 6, 7, 8, 9],
                axisLabel: {
                  interval: 0,
                  // formatter: function(value, index) {
                  // 	return value + "自定义X轴";
                  // },
                  textStyle: {
                    //X轴Y轴下面文字或数据颜色设置
                    color: '#999'
                  }
                  // rotate: 60// 倾斜角度
                },
                type: 'category', // 坐标轴类型
                show: false,
                axisTick: {
                  show: false
                }, // 是否显示坐标轴刻度
                axisLine: {
                  show: false
                } // 是否显示坐标轴轴线
              },
              {
                data: [],
                show: false,
                axisLabel: {
                  interval: 0
                },
                position: 'bottom', // 很重要，如果没有这个设置，默认第二个x轴就会在图表的顶部
                // offset: -200,// X 轴相对于默认位置的偏移，在相同的 position 上有多个 X 轴的时候有用
                type: 'category', // 坐标轴类型
                axisTick: {
                  show: false
                }, // 是否显示坐标轴刻度
                axisLine: {
                  show: false
                } // 是否显示坐标轴轴线
              }
            ],

            yAxis: [
              {
                type: 'value',
                // axisLabel: {
                //    //这种做法就是在y轴的数据的值旁边拼接单位，貌似也挺方便的
                //    formatter: "{value}°C",
                //  },
                minInterval: 1,
                nameTextStyle: {
                  color: '#666666',
                  padding: [30, 0, 0, 30],
                  nameLocation: 'start'
                },
                splitLine: {
                  show: true,
                  lineStyle: {
                    color: ' #E3E3E3',
                    width: 1,
                    type: 'solid'
                  }
                },
                axisLabel: {
                  fontSize: 12,
                  color: '#666666'
                },
                axisTick: {
                  show: false
                },
                axisLine: {
                  show: false,
                  lineStyle: {
                    color: '#EEEEEE',
                    width: 1
                  }
                }
              }
            ],

            series: [
              {
                name: '零起点',
                type: 'line',
                data: [],
                itemStyle: {
                  normal: {
                    lineStyle: {
                      width: 0.1 // 0.1的线条是非常细的了
                    }
                  }
                },
                lineStyle: {
                  // 设置线条的style等
                  normal: {
                    color: 'rgba(131,239,220,0.22)' // 折线线条颜色:红色
                  }
                },
                symbol: 'none', //去掉折线图节点
                smooth: true, //是否平滑曲线，true曲线，false直线
                // 区域填充样式
                areaStyle: {
                  normal: {
                    // 线性渐变，前四个参数分别是 x0, y0, x2, y2, 范围从 0 - 1，相当于在图形包围盒中的百分比，如果 globalCoord 为 `true`，则该四个值是绝对的像素位置
                    color: {
                      //分隔区域的颜色
                      x0: 0,
                      y0: 0,
                      x2: 0,
                      y2: 1,
                      colorStops: [
                        {
                          offset: 0,
                          color: '#50A0FC' // 0% 处的颜色
                        },
                        {
                          offset: 1,
                          color: '#E6F2FF' // 100% 处的颜色；中间还可以设置50%、20%、70%时的颜色
                        }
                      ],
                      globalCoord: false // 缺省为 false，以确保上面的x,y,x2,y2表示的是百分比
                    }
                  }
                }
                // "barGap": "0.2"
              },
              {
                name: '现实掌握词汇量',
                type: 'line',
                data: [],
                itemStyle: {
                  normal: {
                    lineStyle: {
                      width: 0.1 // 0.1的线条是非常细的了
                    }
                  }
                },
                lineStyle: {
                  // 设置线条的style等
                  normal: {
                    color: 'rgba(131,239,220,0.22)' // 折线线条颜色:红色
                  }
                },
                symbol: 'none', //去掉折线图节点
                smooth: true, //是否平滑曲线，true曲线，false直线
                // 区域填充样式
                areaStyle: {
                  normal: {
                    // 线性渐变，前四个参数分别是 x0, y0, x2, y2, 范围从 0 - 1，相当于在图形包围盒中的百分比，如果 globalCoord 为 `true`，则该四个值是绝对的像素位置
                    color: {
                      //分隔区域的颜色
                      x0: 0,
                      y0: 0,
                      x2: 0,
                      y2: 1,
                      colorStops: [
                        {
                          offset: 0,
                          color: '#00B72D' // 0% 处的颜色
                        },
                        {
                          offset: 1,
                          color: 'rgba(131,239,220,0.22) ' // 100% 处的颜色；中间还可以设置50%、20%、70%时的颜色
                        }
                      ],
                      globalCoord: false // 缺省为 false，以确保上面的x,y,x2,y2表示的是百分比
                    }
                  }
                }
              }
            ]
          }
        },
        itemId: '', //传述过来的id
        reportList: {}, //报告列表
        loadingType: 'nodata', //加载更多状态
        // 传递echarts数据
        // lineChartData: {
        // 	expectedData: [],
        // 	xAxisData: []
        // }
        lineChartData: []
      };
    },
    onLoad(option) {
      // 获取传递过来的id参数
      let itemId = option.id;
      this.itemId = itemId;
      this.getinitData();

      // this.$refs['canvas'].init()
    },
    methods: {
      // 顶部箭头点击返回上一页
      back() {
        uni.navigateBack();
      },
      // 获取报告数据
      async getinitData() {
        let itemId = this.itemId;
        let res = await this.$httpUser.get('znyy/areas/student/test/result/print/test/result/' + itemId);
        if (res) {
          this.reportList = res.data.data;
          // echarts柱状图数据名称
          this.lineChartData.xAxisData = [res.data.data.title, '现实掌握词汇量'];
          // echarts柱状图数据
          this.lineChartData.expectedData = [res.data.data.wordUpperLimit, res.data.data.wordLevel];
          this.lineChartData.push({
            name: res.data.data.title,
            value: res.data.data.wordUpperLimit
          });
          this.lineChartData.push({
            name: '现实掌握词汇量',
            value: res.data.data.wordLevel
          });
          this.wordtitle = res.data.data.title;
          // this.ec.option.series[0].data = [0, res.data.data.wordLevel, 0]
          // this.ec.option.series[1].data = [0, res.data.data.wordUpperLimit, 0]
          // // 柱状图偏移
          // this.ec.option.series[0].data = this.ec.option.series[0].data.map((x, i) => [30 + i * 100,
          // 	x
          // ]); //30为偏移量
          // this.ec.option.series[1].data = this.ec.option.series[1].data.map((x, i) => [200 + i * 100,
          // 	x
          // ]); //200为偏移量
          if (res.data.data.length == 0) {
            that.loadingType = 'nodata';
            uni.showToast({
              icon: 'none',
              title: '暂无更多内容了！',
              duration: 2000
            });
          }
        } else {
          uni.showToast({
            icon: 'none',
            title: '暂无数据！',
            duration: 2000
          });
        }
      }
    }
  };
</script>

<style lang="scss">
  // 统一盒子背景
  .boxbg {
    background: #d6ffe5;
    border-radius: 14rpx;
    font-size: 30rpx;
    font-family: AlibabaPuHuiTiR;
    color: #ffffff;
    margin-bottom: 30rpx;
    border: 4rpx solid #ffffff;
  }

  page {
    background-color: #bdeaca;

    .container {
      padding: 30rpx;

      // 顶部箭头返回按钮
      .arrow {
        margin-top: 70rpx;
      }

      // 顶部文字
      .title {
        margin-top: 40rpx;
        // margin-bottom: 60rpx;
        width: 100%;
        padding: 0 !important;

        .title-text {
          font-size: 46rpx;
          font-family: AlibabaPuHuiTiM;
          color: #ffffff;
        }

        .title-img image {
          width: 100%;
          height: 623rpx;
        }
      }

      // 学员信息盒子
      .information {
        width: 690rpx;
        // height: 716rpx;

        .information-title {
          display: flex;
          justify-content: center;
          align-items: center;
          font-family: AlibabaPuHuiTiM;
          font-size: 30rpx;
          color: #ffffff;
          line-height: 70rpx;
          height: 70rpx;
          letter-spacing: 2rpx;
          // text-align: left !important;
          text-align: center;
          /* 文本对齐方式 */
          font-weight: 550;
          vertical-align: top;
          background-image: url('https://document.dxznjy.com/dxSelect/report/tit.png');
          background-size: contain;
          background-repeat: no-repeat;
          /*图片不重复*/
          background-position: center;
          /*指定图片位置*/
          transform: translateY(-50%);
        }

        .yellow {
          color: #eb5e00;
        }

        .information-top {
          text-align: center;
          flex-direction: column;
          width: 690rpx;
          // height: 360rpx;
          border-bottom: 1rpx solid #ffffff;
          padding-top: 40rpx;

          .text-one {
            margin-bottom: 30rpx;
            font-family: AlibabaPuHuiTiM;
            font-size: 30rpx;
            color: #000000;
            line-height: 42rpx;
            font-weight: 550;
          }

          .text-two {
            .big {
              font-size: 80rpx;
              font-family: AlibabaPuHuiTiM;
            }
          }

          .text-three {
            margin-top: 30rpx;
            margin-bottom: 20rpx;
            font-family: AlibabaPuHuiTiR;
            font-size: 30rpx;
            color: #000000;
            line-height: 42rpx;
            font-weight: 500;
          }

          .text-four {
            font-family: AlibabaPuHuiTiR;
            font-size: 30rpx;
            color: #333333;
            line-height: 42rpx;
            padding-bottom: 30rpx;
          }
        }

        .information-bootom {
          line-height: 75rpx;
          padding-left: 30rpx;
          padding-right: 30rpx;

          .studentTile {
            font-weight: bold;
            font-family: AlibabaPuHuiTiM;
            font-size: 36rpx;
            color: #000000;
          }

          .bootom-text {
            display: flex;
            justify-content: space-between;
            font-family: AlibabaPuHuiTiR;
            font-size: 30rpx;
            color: #333333;
          }
        }
      }

      // 零起点总词汇量
      .starting-point {
        width: 690rpx;
        // height: 526rpx;
        text-align: center;
        margin-top: 75rpx;

        .point-top {
          display: flex;
          justify-content: center;
          align-items: center;
          font-family: AlibabaPuHuiTiM;
          font-size: 30rpx;
          color: #ffffff;
          line-height: 70rpx;
          height: 70rpx;
          // letter-spacing: 2rpx;
          // text-align: left !important;
          text-align: center;
          /* 文本对齐方式 */
          font-weight: 550;
          vertical-align: top;
          // background: linear-gradient( 142deg, #D6FFE5 0%, #EB5E00 100%);
          background-image: url('https://document.dxznjy.com/dxSelect/report/tit.png');
          background-size: contain;
          background-repeat: no-repeat;
          // /*图片不重复*/
          background-position: center;
          // /*指定图片位置*/
          transform: translateY(-50%);
        }

        .point-title {
          margin-bottom: 40rpx;
          font-family: AlibabaPuHuiTiM;
          font-size: 30rpx;
          color: #000000;
          line-height: 42rpx;
          font-family: 550;
        }

        .point-footer {
          margin-top: 20rpx;
        }

        // 中间金字塔
        .pyramid {
          position: relative;
          margin: 0 auto;
          padding-bottom: 40rpx;

          .pyramid-img {
            width: 400rpx;
            height: 350rpx;
          }

          .pyramid-text {
            position: absolute;
            font-size: 18rpx;
            font-family: SourceHanSansSC-Regular, SourceHanSansSC;
            font-weight: 400;
            color: #ffffff;
          }

          .text-one {
            left: 308rpx;
            top: 56rpx;
          }

          .text-two {
            left: 308rpx;
            top: 133rpx;
          }

          .text-three {
            left: 308rpx;
            top: 209rpx;
          }

          .text-four {
            left: 308rpx;
            top: 290rpx;
          }
        }
      }

      // 现实掌握词汇
      .master {
        width: 690rpx;
        height: 530rpx;
        // padding-top: 40rpx;
        text-align: center;
        margin-top: 50rpx;

        .master-title {
          margin-bottom: 20rpx;
          font-family: AlibabaPuHuiTiM;
          font-size: 30rpx;
          color: #000000;
          line-height: 42rpx;
          font-weight: 500;
        }

        .ceping {
          // margin-top: 20rpx;
        }

        .master-flex {
          display: flex;
          justify-content: center;
          align-items: center;
          margin-top: -70rpx;
          padding-left: 20rpx;
          font-family: AlibabaPuHuiTiR;
          font-size: 26rpx;
          color: #666666;
          line-height: 35rpx;
        }
      }

      // 底部文字
      .footer {
        width: 690rpx;
        padding-top: 15rpx;
        padding-bottom: 30rpx;

        .footer-box {
          margin: 40rpx 30rpx 0 30rpx;

          .footer-title {
            position: relative;
            margin-top: 40rpx;
            margin-bottom: 20rpx;

            .border {
              position: absolute;
              left: 0rpx;
              top: 6rpx;
              width: 6rpx;
              height: 30rpx;
              // background: linear-gradient(180deg, #C6FFE3 0%, #3AC67F 100%);
              background: #eb5e00;
            }

            .footer-t {
              font-family: AlibabaPuHuiTiM;
              font-size: 30rpx;
              color: #000000;
              line-height: 42rpx;
              font-weight: 550;
            }
          }

          .footer-text {
            // line-height: 50rpx;
            font-family: AlibabaPuHuiTiR;
            font-size: 30rpx;
            color: #333333;
            line-height: 50rpx;
          }
        }
      }
    }
  }

  // .uni-ec-canvas {
  // 	width: 600rpx;
  // 	height: 400rpx;
  // 	display: block;
  // 	// margin-top: 30rpx;
  // 	transform: translateY(-15%);
  // }

  .point-top {
    display: flex;
    justify-content: center;
    align-items: center;
    font-family: AlibabaPuHuiTiM;
    font-size: 30rpx;
    color: #ffffff;
    line-height: 70rpx;
    height: 70rpx;
    // letter-spacing: 2rpx;
    // text-align: left !important;
    text-align: center;
    /* 文本对齐方式 */
    font-weight: 550;
    vertical-align: top;
    // background: linear-gradient( 142deg, #D6FFE5 0%, #EB5E00 100%);
    background-image: url('https://document.dxznjy.com/dxSelect/report/tit.png');
    background-size: contain;
    background-repeat: no-repeat;
    // /*图片不重复*/
    background-position: center;
    // /*指定图片位置*/
    transform: translateY(-50%);
  }
</style>
