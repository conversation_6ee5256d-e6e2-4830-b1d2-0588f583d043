<template>
  <view class="pt">
    <view class="pt-verification-box">
      <view class="pt-verification-images">
        <view class="iconfont refresh" @click="refresh">&#xe64c;</view>
        <image mode="widthFix" :src="bgImg" class="bg-img"></image>
        <image :src="maskImg" class="drag-img" :style="{ left: dragWidth + 'px', top: left + '%', width: imgWidth, height: imgHeight }"></image>
      </view>
      <view class="pt-dragbar">
        <view :class="['pt-drag-area', { fail: fail, success: success }]" :style="{ width: dragWidth + 'px' }" v-if="dragWidth"></view>
        <movable-area class="pt-dragbar-area">
          <movable-view
            :class="['pt-dragbar-view', { active: dragWidth > 2, fail: fail, success: success }]"
            :direction="direction"
            @change="drag"
            @touchend="dragEnd"
            :damping="200"
            :x="x"
            :animation="false"
            :disabled="disabled"
            :out-of-bounds="false"
            :data-dragWidth="dragWidth"
          >
            <text class="iconfont">
              <block v-if="success">&#xe687;</block>
              <block v-else-if="fail">&#xe65c;</block>
              <block v-else>&#xe62a;</block>
            </text>
          </movable-view>
          <text v-if="dragWidth == 0" class="tips">{{ tips }}</text>
        </movable-area>
      </view>
    </view>
  </view>
</template>

<script>
  import phoneLoginVue from '../login/phoneLogin.vue';

  export default {
    props: {
      bgImg: {
        type: String,
        default: ''
      },
      maskImg: {
        type: String,
        default: ''
      },
      imgWidth: {
        type: String,
        default: ''
      },
      imgHeight: {
        type: String,
        default: ''
      },
      uuid: {
        type: String,
        default: ''
      },
      left: {
        type: Number,
        default: 140
      },
      // 滑块滑动的方向
      direction: {
        type: String,
        default: 'horizontal'
      },
      tips: {
        type: String,
        default: '向右拖动滑块填充拼图'
      }
    },
    data() {
      return {
        disabled: false,
        dragWidth: 0,
        success: false,
        fail: false,
        x: 0
      };
    },
    methods: {
      // 开始拖拽
      drag(e) {
        // if (e.detail.x < 276) {
        this.dragWidth = e.detail.x;
        // }
      },
      // 停止滑动后判断前后误差不超过5px，可自行修改
      async dragEnd(e) {
        let that = this;
        this.x = e.currentTarget.dataset.dragwidth;
        let a = Math.floor(e.currentTarget.dataset.dragwidth / 3.2);
        let obj = {
          maveX: a,
          uuid: this.uuid
        };

        let { data } = await this.$httpUser.post('new/security/captcha/check/slide', obj);
        // console.log(data);
        if (data.data.code == 20000) {
          this.success = true;
          setTimeout(() => {
            that.dragWidth = 0;
            that.x = 0;
            that.fail = false;
            that.$emit('success', data.data.data);
          }, 1000);
        } else {
          that.fail = true;
          setTimeout(() => {
            that.dragWidth = 0;
            that.x = 0;
            that.fail = false;
            // that.$emit('refresh');
          }, 1000);
        }

        //    if (e.currentTarget.dataset.dragwidth > 0 && !this.disabled) {
        //      let result = this.isRangeIn(e.currentTarget.dataset.dragwidth, max, mins);
        //      if (!result) {
        //        this.fail = true;
        //        uni.showToast({
        //          icon: 'none',
        //          title: '验证失败，再来一次吧',
        //          duration: 1500
        //        });
        //        // 验证失败延迟重置
        //        setTimeout(() => {
        //          this.$emit('refresh');
        //          this.dragWidth = 0;
        //          this.x = 0;
        //          this.fail = false;
        //        }, 1500);
        //      } else {
        //        this.success = true;
        //        this.disabled = true;
        //        this.$emit('success');
        //      }
        //    }
      },
      isRangeIn(num, maxnum, minnum) {
        var num = parseFloat(num);
        if (num <= maxnum && num >= minnum) {
          return true;
        }
        return false;
      },
      refresh() {
        this.dragWidth = 0;
        this.fail = false;
        this.success = false;
        this.x = 0;
        this.disabled = false;
        this.$emit('refresh');
      }
    }
  };
</script>

<style lang="scss" scoped>
  @font-face {
    font-family: 'iconfont'; /* project id 2047533 */
    src: url('https://at.alicdn.com/t/font_2047533_o8axbabfs3.ttf') format('truetype');
  }
  .pt-verification-box {
    background-color: #fff;
  }
  .iconfont {
    font-family: iconfont !important;
    font-size: 16px;
    font-style: normal;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }
  .pt {
    width: 320px;
    margin: 0 auto;
    &-verification-images {
      position: relative;
      .refresh {
        position: absolute;
        right: 10px;
        top: 10px;
        z-index: 10;
        color: #fff;
        font-weight: bold;
      }
      .bg-img {
        width: 100%;
        vertical-align: top;
      }
      .drag-img {
        position: absolute;
        // width: 108rpx;
        // top: -2rpx;
        // height: 128rpx;
        left: 0;
        z-index: 1;
      }
    }
    &-dragbar {
      position: relative;
      height: 40px;
      background-color: #f7f7f7;
      border: solid 1px #eee;
      margin-top: 10px;
      .pt-drag-area {
        position: absolute;
        box-sizing: border-box;
        height: 40px;
        border: solid 1px $uni-color-primary;
        background-color: #d1e9f1;
        top: 0px;
        &.fail {
          border-color: #ff8b16;
          background-color: #fff4e9;
        }
        &.success {
          border-color: #79c03c;
          background-color: #ecffdc;
        }
      }
      &-area {
        position: absolute;
        width: 100%;
        overflow: hidden;
        height: 100%;
        left: 0;
        top: 0;
        .tips {
          font-size: 14px;
          height: 40rpx;
          color: #999;
          position: absolute;
          left: 50%;
          top: 50%;
          width: 50%;
          transform: translate(-50%, -50%);
        }
      }
      &-view {
        width: 40px;
        height: 39px;
        display: flex;
        align-items: center;
        // box-sizing: border-box;
        justify-content: center;
        border: solid 1px #eee;
        background-color: #fff;
        top: -1px;
        left: 0;
        &.active {
          background-color: $uni-color-primary;
          border-color: $uni-color-primary;
          color: #fff;
        }
        &.fail {
          background-color: #ff8b16;
          border-color: #ff8b16;
        }
        &.success {
          border-color: #79c03c;
          background-color: #79c03c;
        }
      }
    }
  }
</style>
