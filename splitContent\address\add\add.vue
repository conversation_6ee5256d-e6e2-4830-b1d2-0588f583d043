<template>
  <view class="plr-32 address_content">
    <form @submit="saveData">
      <view class="bg-ff pt-35 pb-45 radius-15 plr-25">
        <view class="address-box flex ptb-30">
          <view class="left-name">
            <text class="f-28 c-00">收货人姓名:</text>
          </view>
          <view class="right-cont flex-box pl-45">
            <input name="consigneeName" class="f-28" maxlength="25" placeholder-style="color:#999999;font-size:28rpx;" placeholder="请填写收件人姓名" :value="consigneePhone" />
          </view>
        </view>
        <view class="address-box flex ptb-30">
          <view class="left-name">
            <text class="f-28 c-00">联系方式:</text>
          </view>
          <view class="right-cont flex-box pl-45">
            <input name="consigneePhone" class="f-28" placeholder-style="color:#999999;font-size:28rpx;" placeholder="请填写联系方式" type="number" :value="consigneePhone" />
          </view>
        </view>
        <view class="address-box flex ptb-30 adresH">
          <view class="left-name">
            <text class="f-28 c-00">所在地区:</text>
          </view>
          <view class="right-cont flex-box pl-45">
            <!-- #ifdef MP-WEIXIN -->
            <picker mode="region" @change="bindRegionChange">
              <text v-if="region" class="f-28">{{ region[0] }}{{ region[1] }}{{ region[2] }}</text>
              <text wx:else class="f-28 c-99">选择省、市、区</text>
            </picker>
            <!-- #endif -->

            <!-- #ifdef APP-PLUS -->
            <gb-picker @change="bindAppRegionChange" @tap="openPicker">
              <text v-if="region" class="f-28">{{ region[0] }}{{ region[1] }}{{ region[2] }}</text>
              <text v-else class="f-28 c-99">选择省、市、区</text>
            </gb-picker>
            <!-- #endif -->
          </view>
        </view>
        <view class="address-box ptb-30">
          <view class="left-name mb-20">
            <text class="f-28 c-00">详细地址:</text>
          </view>
          <view class="right-cont flex-box">
            <textarea
              name="address"
              class="col-12 f-30 height textarea_css"
              placeholder-style="color:#999999;font-size:28rpx;"
              placeholder="请填写详细地址"
              type="text"
              :value="address"
            ></textarea>
          </view>
        </view>
      </view>
      <!-- 选择省市区 -->
      <view class="profile-btn pt-40">
        <button class="confirm_btn m0 t-c c-ff" form-type="submit" :disabled="disabled">保存</button>
      </view>
      <!-- 省市区选择 province city area初始省市区设置 show:是否显示  @sureSelectArea：确认事件 @hideShow：隐藏事件-->
      <cc-selectDity :province="province" :city="city" :area="area" :show="show" @changeClick="changeClick" @sureSelectArea="onsetCity" @hideShow="onhideShow"></cc-selectDity>
    </form>
  </view>
</template>

<script>
  const { $http, $showError } = require('@/util/methods.js');
  export default {
    data() {
      return {
        disabled: false,
        error: null,
        region: null,
        useHeight: 0, //除头部之外高度
        address: '',
        consigneePhone: '',
        colors: '#f23a3a',
        show: false,
        province: '广东省',
        city: '广州市',
        area: '天河区',
        areaCode: '440106',
        addressData: {
          name: '',
          phone: '',
          address: '',
          moreAddres: '',
          isdefult: 0
        },
        isShow: true
      };
    },
    onLoad() {},
    onShow() {},
    onReady() {
      uni.getSystemInfo({
        success: (res) => {
          // 可使用窗口高度，将px转换rpx
          let h = res.windowHeight * (750 / res.windowWidth);
          this.useHeight = h - 854;
        }
      });
    },
    methods: {
      // 添加地址
      async saveData(e) {
        let _this = this,
          values = e.detail.value;
        values.region = _this.region;
        if (!_this.validation(values)) {
          $showError(_this.error);
          return false;
        }
        if (_this.disabled) {
          return false;
        }
        uni.showLoading({
          title: '提交中'
        });
        // 按钮禁用
        // _this.disabled = true
        const res = await $http({
          url: 'zx/order/saveOrderAddress',
          method: 'POST',
          data: {
            consigneeName: values.consigneeName,
            consigneePhone: values.consigneePhone,
            provinceName: _this.region[0],
            cityName: _this.region[1],
            districtName: _this.region[2],
            address: values.address
          }
        });
        uni.hideLoading();

        _this.disabled = false;
        uni.showToast({
          title: res.message,
          icon: 'none'
        });
        if (res) {
          setTimeout(function () {
            let pages = getCurrentPages();
            if (pages.length < 2) {
              return false;
            }
            let prevPage = pages[pages.length - 2];
            prevPage.needReGetOrder = true;
            uni.navigateBack();
          }, 2000);
        }
      },
      validation(values) {
        if (values.consigneeName === '') {
          this.error = '收件人不能为空';
          return false;
        }
        let regNmame = /[^\u4E00-\u9FA5|\d|\a-zA-Z|\r\n\s,.?!，。？！…—&$=()-+/*{}[\]]/g;
        if (regNmame.test(values.consigneeName)) {
          this.error = '收件人不能输入特殊字符和表情';
          return false;
        }
        if (values.consigneePhone.length < 1) {
          this.error = '手机号不能为空';
          return false;
        }
        let reg = /^((0\d{2,3}-\d{7,8})|(1[3456789]\d{9}))$/;
        if (!reg.test(values.consigneePhone)) {
          this.error = '手机号不符合要求';
          return false;
        }
        if (!this.region) {
          this.error = '省市区不能空';
          return false;
        }
        if (values.address === '') {
          this.error = '详细地址不能为空';
          return false;
        }
        return true;
      },
      bindRegionChange: function (e) {
        console.log(e);
        this.region = e.detail.value;
      },
      bindAppRegionChange: function (e) {
        console.log(e);
        this.region = e.name;
      },
      openPicker() {
        console.log('执行打开地址选择器');
        this.show = true;
      },
      changeClick(value, value2, value3, value4) {
        console.log('地址选择器 = ' + value + value2 + value3 + value4);
        this.province = value;
        this.city = value2;
        this.area = value3;
        this.areaCode = value4;
        // 更新 region 的值
        this.region = [value, value2, value3];
      },
      onhideShow() {
        this.show = false;
        console.log('执行了关闭地址选择器');
      },
      // 选中省市区
      onsetCity(e) {
        let data = e.detail.target.dataset;
        let address = data.province + data.city + data.area;
        this.show = false;
        // 更新 region 的值
        this.region = [data.province, data.city, data.area];
      }
    }
  };
</script>

<style>
  button[disabled]:not([type]) {
    background-color: #0e6457;
  }

  .adresH {
    height: 60upx;
  }

  .input {
    width: 70%;
    /* padding: 20upx 0; */
    align-items: center;
    font-size: 28upx;
    height: 60upx;
  }

  .selectcity input {
    width: 90%;
  }

  .address_content {
    height: 100vh;
  }

  .confirm_btn {
    position: absolute;
    bottom: 60upx;
    height: 74upx;
    left: 32rpx;
    border-radius: 38upx;
    line-height: 74upx;
    width: 686rpx;
    background: #339378;
  }

  .address-box {
    border-bottom: 2rpx solid #efefef;
    width: 638rpx;
  }

  .address-box:last-child {
    border: none;
  }

  .left-name {
    font-size: 30upx;
    width: 175rpx;
  }

  .textarea_css {
    width: 575rpx;
    height: 318rpx;
    border: 2rpx solid #efefef;
    padding: 32rpx 24rpx;
  }
</style>
