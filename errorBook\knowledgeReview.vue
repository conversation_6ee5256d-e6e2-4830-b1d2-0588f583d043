<template>
  <view class="plr-30 pb-30 list_box">
    <!-- 列表 -->
    <view v-for="(item, index) in itemList" :key="index" class="mt-20">
      <!-- 点击切换显示 topic -->
      <view @click="toggleFolder(item, index)">
        <view class="flex-s pb-30 b-db">
          <view class="f-32" :class="active ? 'headStyle' : ''">{{ item.knowledgeName }}</view>
          <uv-icon :name="item.topicShow ? 'arrow-up' : 'arrow-down'" size="15"></uv-icon>
        </view>
      </view>
      <!-- 展开内容 -->
      <view v-if="item.topicShow">
        <view v-for="(itemMsg, indexMsg) in noteListData[index] || []" :key="indexMsg">
          <view class="mt-20 bg-faf8 radius-8">
            <view class="flex-s m-10 p-10 ml-20" @click="toggleFile(item, indexMsg)">
              <text class="f-28">{{ itemMsg.topic }}</text>
              <uv-icon :name="itemMsg.open ? 'arrow-up' : 'arrow-down'" size="15"></uv-icon>
            </view>
            <view v-if="itemMsg.open" class="ml-30 content">
              <view class="flex">
                <text>{{ itemMsg.content }}</text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
    <view class="bottom-buttons">
      <u-button shape="circle" color="#428A6F" @click="viewTopicHandle">查看试题</u-button>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      active: 1,
      disabled: false,
      show: false,
      knowledgeId: '',
      itemList: [
        {
          knowledgeName: '',
          open: false,
          noteList: [],
        },
      ],
      noteListData: [],
    }
  },
  onLoad(options) {
    this.knowledgeId = options.id
    // this.knowledgeId = '1276119673065193474'

    this.getKnowledge()
  },
  methods: {
    getKnowledge() {
      this.$httpUser
        .get('dyf/wap/applet/wrongBookNote', {
          id: this.knowledgeId,
        })
        .then((res) => {
          this.itemList = res.data.data.map((item) => ({
            ...item,
            open: false,
            topicShow: false,
            // 初始化每个 noteList 项目的 open 状态
            noteList: item.noteList.map((note) => ({
              ...note,
              open: false, // 为每个 itemMsg 添加 open 属性
            })),
          }))
        })
    },
    viewTopicHandle() {
      uni.navigateBack({
        delta: 1,
      })
    },
    toggleFolder(item, index) {
      item.topicShow = !item.topicShow
      if (item.topicShow) {
        if (!this.noteListData[index]) {
          this.noteListData[index] = item.noteList
        }
      }
    },
    toggleFile(item, indexMsg) {
      // item.noteList[indexMsg].open = !item.noteList[indexMsg].open;
      this.$set(item.noteList, indexMsg, {
        ...item.noteList[indexMsg],
        open: !item.noteList[indexMsg].open,
      })
    },
  },
}
</script>
<style scoped>
.list_box {
  border-radius: 14upx;
  background: #ffffff;
  box-shadow: 0rpx 4rpx 16rpx 0rpx #f5f7f9;
  padding: 30upx;
  margin: 20upx;
  font-size: 26upx;
  color: rgba(102, 102, 102, 1);
  position: relative;
  min-height: calc(100vh - 150rpx);
  /* 调整内容区域最小高度 */
  box-sizing: border-box;
  padding-bottom: 150rpx;
  /* 确保内容不会与底部按钮重叠 */
}

.bottom-buttons {
  position: fixed;
  bottom: 30upx;
  left: 50%;
  transform: translateX(-50%);
  width: 85%;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 999;
  padding-bottom: 20rpx;
}

.icon {
  width: 48rpx;
  height: 34rpx;
  line-height: 34rpx;
  font-size: 24rpx;
  border: 2rpx solid var(--border-color);
  border-radius: 8rpx;
  color: var(--text-color);
  margin-left: 10rpx;
  font-family: AlibabaPuHuiTiR;
  text-align: left;
  font-style: normal;
}

.iconPri {
  --border-color: #81e2af;
  --text-color: #81e2af;
}

.iconMid {
  --border-color: #ffd593;
  --text-color: #ffd593;
}

.iconHigh {
  --border-color: #6de2ff;
  --text-color: #6de2ff;
}

.headStyle {
  color: #428a6f;
}

.content {
  height: auto;
  font-family: AlibabaPuHuiTiR;
  font-size: 28rpx;
  color: #ababab;
  line-height: auto;
  text-align: left;
  font-style: normal;
  margin: 52rpx;
}
</style>
