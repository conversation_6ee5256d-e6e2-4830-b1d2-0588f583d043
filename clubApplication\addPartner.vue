<template>
  <page-meta :page-style="'overflow:' + (show ? 'hidden' : 'visible')"></page-meta>
  <view class="partner p-30 f-28">
    <form id="#nform">
      <view class="partnerFlex" style="margin: 44rpx 0">
        <view class="bold lineHiehgt" style="width: 30%">
          <text style="color: red">*</text>
          登录账号:
        </view>
        <view style="flex: 1">
          <input class="uni-input" name="input" placeholder="请输入手机号" maxlength="11" v-model="form.phone" type="number" required :disabled="updateId != '' ? true : false" />
        </view>
      </view>
      <view class="partnerFlex" style="margin: 44rpx 0">
        <view class="bold lineHiehgt" style="width: 30%">
          <text style="color: red">*</text>
          {{ type === '2' ? '登录超级合伙人名称' : '登录超级俱乐部名称' }}:
        </view>
        <view style="flex: 1">
          <input class="uni-input" name="input" placeholder=" 请输入省市区+学校名称+超级品牌" maxlength="20" v-model="form.merchantName" type="text" placeholder-class="addName" />
          <text style="font-size: 24rpx; color: #c6c6c6">例：安徽省合肥市包河区平安国际金融超级{{ type === '2' ? '合伙人' : '俱乐部' }}）</text>
        </view>
      </view>
      <view class="partnerFlex" style="margin: 44rpx 0">
        <view class="bold lineHiehgt" style="width: 30%">
          <text style="color: red">*</text>
          总负责人:
        </view>
        <view style="flex: 1">
          <input class="uni-input" name="input" placeholder="请输入总负责人" maxlength="20" v-model="form.realName" type="text" />
        </view>
      </view>
      <view class="partnerFlex" style="margin: 44rpx 0">
        <view class="bold lineHiehgt" style="width: 30%">
          <text style="color: red">*</text>
          负责人身份证:
        </view>
        <view style="flex: 1">
          <input class="uni-input" name="input" placeholder="请输入总负责人身份证" maxlength="20" v-model="form.idCard" type="text" />
        </view>
      </view>
      <view class="partnerFlex" style="margin: 44rpx 0">
        <view class="bold lineHiehgt" style="width: 30%">
          <text style="color: red">*</text>
          签约时间:
        </view>
        <view style="flex: 1">
          <uni-Datetime-Picker style="flex: 1" type="datetime" :clear-icon="false" v-model="form.signupDate" @change="change" />
        </view>
      </view>
      <view class="partnerFlex" style="margin: 44rpx 0">
        <view class="bold lineHiehgt" style="width: 30%">推荐人编号:</view>
        <view style="flex: 1">
          <input class="uni-input" name="input" placeholder="请输入推荐人编号" maxlength="20" v-model="form.shareCode" type="text" />
        </view>
      </view>
      <view class="partnerFlex" style="margin: 44rpx 0" v-if="type === '2'">
        <view class="bold lineHiehgt" style="width: 30%">所属俱乐部编码:</view>
        <view style="flex: 1">
          <input class="uni-input" name="input" placeholder="请输入所属俱乐部编码" maxlength="20" v-model="form.affiliationClubCode" type="text" disabled="true" />
        </view>
      </view>
      <view class="partnerFlex" style="margin: 44rpx 0" v-if="type === '3'">
        <view class="bold lineHiehgt" style="width: 30%">所属品牌编码:</view>
        <view style="flex: 1">
          <input class="uni-input" name="input" placeholder="请输入所属品牌编码" maxlength="20" v-model="form.affiliationBrandCode" type="text" disabled="true" />
        </view>
      </view>
      <view class="partnerFlex" style="margin: 44rpx 0" v-if="type === '3'">
        <view class="bold lineHiehgt" style="width: 30%">首充开店名额:</view>
        <view style="flex: 1; display: flex; align-items: center">
          <input class="uni-input" name="input" v-model="form.firstStoreOpeningNum" type="text" style="flex: 1" :disabled="true" />
          <div>个</div>
        </view>
      </view>

      <view class="partnerFlex" style="margin: 44rpx 0">
        <view class="bold lineHiehgt" style="width: 30%">首充付款金额:</view>
        <view style="flex: 1; display: flex; align-items: center">
          <input class="uni-input" name="input" v-model="form.initialPaymentAmount" type="text" style="flex: 1" :disabled="true" />
          <div>元</div>
        </view>
      </view>
      <view class="partnerFlex" style="margin: 44rpx 0">
        <view class="bold lineHiehgt" style="width: 30%">
          <text style="color: red">*</text>
          合同照片:
        </view>
        <view style="flex: 1; display: flex; align-items: center">
          <u-upload
            @afterRead="contractAfterRead"
            @delete="contractDeletePic"
            name="1"
            multiple
            width="160"
            height="160"
            :file-list="contractFileList"
            :previewFullImage="true"
            :maxCount="10"
          >
            <view class="upload_content_css flex-a-c flex-x-c">
              <u-icon name="plus" color="#979797" size="48"></u-icon>
            </view>
          </u-upload>
        </view>
      </view>
      <view class="partnerFlex" style="margin: 44rpx 0">
        <view class="bold lineHiehgt" style="width: 30%">
          <text style="color: red">*</text>
          证件照片:
        </view>
        <view style="flex: 1; display: flex; align-items: center">
          <u-upload @afterRead="cardAfterRead" @delete="cardDeletePic" name="1" multiple width="160" height="160" :file-list="cardFileList" :previewFullImage="true" :maxCount="10">
            <view class="upload_content_css flex-a-c flex-x-c">
              <u-icon name="plus" color="#979797" size="48"></u-icon>
            </view>
          </u-upload>
        </view>
      </view>
      <view class="partnerFlex" style="margin: 44rpx 0">
        <view class="bold lineHiehgt" style="width: 30%">付款记录:</view>
        <view style="flex: 1; display: flex; align-items: center">
          <u-upload @afterRead="payAfterRead" @delete="payDeletePic" name="1" multiple width="160" height="160" :file-list="payFileList" :previewFullImage="true" :maxCount="10">
            <view class="upload_content_css flex-a-c flex-x-c">
              <u-icon name="plus" color="#979797" size="48"></u-icon>
            </view>
          </u-upload>
        </view>
      </view>
      <view class="partnerFlex" style="margin: 44rpx 0">
        <view class="bold lineHiehgt" style="width: 30%">门店照片:</view>
        <view style="flex: 1; display: flex; align-items: center">
          <u-upload
            @afterRead="storeAfterRead"
            @delete="storeDeletePic"
            name="1"
            multiple
            width="160"
            height="160"
            :file-list="storeFileList"
            :previewFullImage="true"
            :maxCount="10"
          >
            <view class="upload_content_css flex-a-c flex-x-c">
              <u-icon name="plus" color="#979797" size="48"></u-icon>
            </view>
          </u-upload>
        </view>
      </view>
      <view class="partnerFlex" style="margin: 44rpx 0">
        <view class="bold lineHiehgt" style="width: 30%">
          <text style="color: red">*</text>
          所在地区:
        </view>
        <view style="flex: 1; display: flex; align-items: center">
          <input class="uni-input" name="input" placeholder="请选择地址" maxlength="20" v-model="addresss" type="text" style="flex: 1" @tap="open" disabled="true" />
          <cityPicker :column="column" :default-value="defaultValue" :mask-close-able="maskCloseAble" @confirm="confirm" @cancel="cancel" :visible="visible" />
        </view>
      </view>
      <view class="partnerFlex" style="margin: 44rpx 0">
        <view class="bold lineHiehgt" style="width: 30%">
          <text style="color: red">*</text>
          详细地址:
        </view>
        <view style="flex: 1; display: flex; align-items: center">
          <input class="uni-input" name="input" placeholder="请输入详细地址" maxlength="20" v-model="form.address" type="text" style="flex: 1" />
        </view>
      </view>
      <view class="partnerFlex" style="margin: 44rpx 0">
        <view class="bold" style="width: 30%">
          <text style="color: red">*</text>
          门店简介:
        </view>
        <view style="width: 70%; border: none; color: black" class="textarea_style_css">
          <u--textarea placeholder="请输入" maxlength="500" autoHeight border="none" v-model="form.description"></u--textarea>
          <view class="textarem_number f-24 c-55">{{ form.description.length }}/500</view>
        </view>
      </view>
    </form>
    <view class="submitButtonParent">
      <button @tap.stop="submitInfo" class="f-28 c-ff submitButton" v-if="type == 2">添加合伙人</button>
      <button @tap.stop="submitInfo" class="f-28 c-ff submitButton" v-if="type == 3">添加俱乐部</button>
    </view>
  </view>
</template>

<script>
  import cityPicker from './components/piaoyi-cityPicker/components/piaoyi-cityPicker/piaoyi-cityPicker.vue';
  import uniDatetimePicker from './components/uni-datetime-picker/components/uni-datetime-picker/uni-datetime-picker.vue';
  import Config from '@/util/config.js';
  const { $http } = require('@/util/methods.js');
  export default {
    components: {
      cityPicker,
      uniDatetimePicker
    },
    data() {
      return {
        name: '',
        clubCode: '',
        visible: false,
        maskCloseAble: true,
        defaultValue: '',
        // defaultValue: ['河北省','唐山市','丰南区'],
        column: 3,
        type: '', // 2合伙人  3俱乐部
        form: {
          phone: '',
          merchantName: '',
          realName: '',
          idCard: '',
          signupDate: '',
          shareCode: '',
          affiliationClubCode: '',
          affiliationBrandCode: '',
          expireDate: '',
          firstStoreOpeningNum: '',
          initialPaymentAmount: '',
          address: '',
          description: '',
          contractPhotoPathList: [],
          idPhotoPathList: [],
          paymentPhotoPathList: [],
          storePhotoPathList: [],
          province: '',
          city: '',
          area: '',
          merchantType: '',
          merchantId: ''
        },
        contractFileList: [],
        cardFileList: [],
        payFileList: [],
        storeFileList: [],
        updateId: ''
      };
    },
    watch: {
      'form.description': {
        handler(val) {
          if (val.length >= 500) {
            this.form.description = val.substr(0, 500);
          }
        },
        immediate: true,
        deep: true
      }
    },
    computed: {
      addresss() {
        if (this.form.province) {
          return this.form.province + this.form.city + this.form.area;
        } else {
          return '';
        }
      }
    },
    onLoad(option) {
      this.type = option.type;
      if (this.type == 2) {
        uni.setNavigationBarTitle({
          title: '添加合伙人'
        });
      } else {
        uni.setNavigationBarTitle({
          title: '添加俱乐部'
        });
      }
      if (option != null && option.id != undefined) {
        this.updateId = option.id;
        this.form.merchantId = option.id;
        this.$httpUser
          .get('zxAdminCourse/web/piMerchant/merchantInfo', {
            merchantId: this.form.merchantId
          })
          .then((res) => {
            this.form = res.data.data.data;
            this.form.contractPhotoPathList.forEach((item) => {
              this.contractFileList.push({
                url: item
              });
            });
            this.form.idPhotoPathList.forEach((item) => {
              this.cardFileList.push({
                url: item
              });
            });
            this.form.paymentPhotoPathList.forEach((item) => {
              this.payFileList.push({
                url: item
              });
            });
            this.form.storePhotoPathList.forEach((item) => {
              this.storeFileList.push({
                url: item
              });
            });
          });
      } else {
        this.form.merchantType = option.merchantType;
        if (option.userCode != null) {
          if (this.form.merchantType === '1') {
            this.form.affiliationClubCode = option.userCode;
          }
          if (this.form.merchantType === '2') {
            this.form.affiliationBrandCode = option.userCode;
          }
        }
      }
    },
    onShow() {
      this.fetchInfo();
    },
    methods: {
      //获取首充信息
      async fetchInfo() {
        if (!this.type) return;
        const res = await $http({
          url: 'zxAdminCourse/web/commissionMeal/mealInfoByType',
          data: {
            mealType: this.type
          }
        });
        if (res) {
          this.form.firstStoreOpeningNum = res.data.data.invitationCodeNum;
          this.form.initialPaymentAmount = res.data.data.mealPrice;
        }
      },
      async submitInfo() {
        if (this.form.phone == '') {
          this.$util.alter('请输入登录账号');
          return false;
        }
        if (this.form.merchantName == '') {
          this.$util.alter(this.type === '1' ? '请输入登录超级合伙人名称' : '请输入登录超级俱乐部名称');
          return false;
        }
        if (this.form.realName == '') {
          this.$util.alter('请输入总负责人');
          return false;
        }
        if (this.form.idCard == '') {
          this.$util.alter('请输入负责人身份证');
          return false;
        }
        if (this.form.signupDate == '') {
          this.$util.alter('请选择签约时间');
          return false;
        }
        if (this.form.contractPhotoPathList.length === 0) {
          this.$util.alter('请上传合同照片');
          return false;
        }
        if (this.form.idPhotoPathList.length === 0) {
          this.$util.alter('请上传证件照片');
          return false;
        }
        if (this.form.province == '') {
          this.$util.alter('请选择所在地区');
          return false;
        }
        if (this.form.address == '') {
          this.$util.alter('请输入详细地址');
          return false;
        }
        if (this.form.description == '') {
          this.$util.alter('请输入门店简介');
          return false;
        }
        if (this.type === '2') {
          this.form.merchantType = 1;
        }
        if (this.type === '3') {
          this.form.merchantType = 2;
        }
        uni.showLoading({
          title: '添加中...'
        });
        await this.$httpUser.post('zxAdminCourse/web/piMerchant/merchantManage', this.form).then((res) => {
          uni.hideLoading();
          if (res.data.data) {
            uni.showToast({
              title: '添加成功'
            });
            uni.navigateBack();
          } else if (res.data.status == 10003) {
            uni.showToast({
              title: res.data.message,
              icon: 'none',
              duration: 2000
            });
          }
        });
      },
      // 新增图片
      contractAfterRead(event) {
        let _this = this;
        for (let i = 0; i < event.file.length; i++) {
          uni.uploadFile({
            url: `${Config.DXHost}zx/common/uploadFile`,
            filePath: event.file[i].url,
            name: 'file',
            header: {
              Token: uni.getStorageSync('token')
            },
            success: function (res) {
              if (res.data) {
                let data = JSON.parse(res.data);
                if (data.status == 1) {
                  _this.contractFileList.push({
                    url: data.data.fileUrl
                  });
                  _this.form.contractPhotoPathList.push(data.data.fileUrl);
                } else {
                  uni.showToast({
                    title: data.message,
                    icon: 'none'
                  });
                }
                return data;
              }
            }
          });
        }
      },
      cardAfterRead(event) {
        let _this = this;
        for (let i = 0; i < event.file.length; i++) {
          uni.uploadFile({
            url: `${Config.DXHost}zx/common/uploadFile`,
            filePath: event.file[i].url,
            name: 'file',
            header: {
              Token: uni.getStorageSync('token')
            },
            success: function (res) {
              if (res.data) {
                let data = JSON.parse(res.data);
                if (data.status == 1) {
                  _this.cardFileList.push({
                    url: data.data.fileUrl
                  });
                  _this.form.idPhotoPathList.push(data.data.fileUrl);
                } else {
                  uni.showToast({
                    title: data.message,
                    icon: 'none'
                  });
                }
                return data;
              }
            }
          });
        }
      },
      payAfterRead(event) {
        let _this = this;
        for (let i = 0; i < event.file.length; i++) {
          uni.uploadFile({
            url: `${Config.DXHost}zx/common/uploadFile`,
            filePath: event.file[i].url,
            name: 'file',
            header: {
              Token: uni.getStorageSync('token')
            },
            success: function (res) {
              if (res.data) {
                let data = JSON.parse(res.data);
                if (data.status == 1) {
                  _this.payFileList.push({
                    url: data.data.fileUrl
                  });
                  _this.form.paymentPhotoPathList.push(data.data.fileUrl);
                } else {
                  uni.showToast({
                    title: data.message,
                    icon: 'none'
                  });
                }
                return data;
              }
            }
          });
        }
      },
      storeAfterRead(event) {
        let _this = this;
        for (let i = 0; i < event.file.length; i++) {
          uni.uploadFile({
            url: `${Config.DXHost}zx/common/uploadFile`,
            filePath: event.file[i].url,
            name: 'file',
            header: {
              Token: uni.getStorageSync('token')
            },
            success: function (res) {
              if (res.data) {
                let data = JSON.parse(res.data);
                if (data.status == 1) {
                  _this.storeFileList.push({
                    url: data.data.fileUrl
                  });
                  _this.form.storePhotoPathList.push(data.data.fileUrl);
                } else {
                  uni.showToast({
                    title: data.message,
                    icon: 'none'
                  });
                }
                return data;
              }
            }
          });
        }
      },
      // 删除图片
      contractDeletePic(event) {
        let _this = this;
        _this.contractFileList = _this.contractFileList.filter((item) => item.url !== event.file.url);
        _this.form.contractPhotoPathList = _this.form.contractPhotoPathList.filter((item) => item !== event.file.url);
      },
      cardDeletePic(event) {
        let _this = this;
        _this.cardFileList = _this.cardFileList.filter((item) => item.url !== event.file.url);
        _this.form.idPhotoPathList = _this.form.idPhotoPathList.filter((item) => item !== event.file.url);
      },
      payDeletePic(event) {
        let _this = this;
        _this.payFileList = _this.payFileList.filter((item) => item.url !== event.file.url);
        _this.form.paymentPhotoPathList = _this.form.paymentPhotoPathList.filter((item) => item !== event.file.url);
      },
      storeDeletePic(event) {
        let _this = this;
        _this.storeFileList = _this.storeFileList.filter((item) => item.url !== event.file.url);
        _this.form.storePhotoPathList = _this.form.storePhotoPathList.filter((item) => item !== event.file.url);
      },
      confirm(val) {
        this.form.province = val.provinceName;
        this.form.city = val.cityName;
        this.form.area = val.areaName;
        this.visible = false;
      },
      cancel() {
        this.visible = false;
      },
      open(e) {
        this.visible = true;
      },
      //弹窗穿透
      change(e) {
        this.show = e.show;
      },
      back() {
        uni.navigateBack();
      }
    }
  };
</script>

<style lang="scss" scoped>
  $nav-height: 30px;

  // 导航栏
  .box-bg {
    height: 140rpx;

    /deep/.uni-navbar__header {
      width: 100%;
      position: fixed;
      z-index: 999;
    }

    .left-icon {
      margin-top: 100rpx;
    }

    .leave-title {
      // text-align: center;
      font-size: 36rpx;
      margin: auto;
      margin-top: 100rpx;
      font-weight: 700;
    }
  }

  .partner {
    width: 100vw;
    height: 100%;
    background-color: #fff;
    box-sizing: border-box;
    padding-bottom: 80rpx;
  }

  .uni-input {
    height: 64rpx;
    padding-left: 32rpx;
    background-color: #f3f8fc;
  }

  .partnerFlex {
    display: flex;
    justify-content: space-between;

    /deep/.u-textarea {
      background-color: #f3f8fc !important;
      padding-left: 32rpx;
    }

    /deep/.u-textarea__field {
      color: black !important;
    }

    /deep/.input-placeholder {
      color: #808080 !important;
    }
  }

  .lineHiehgt {
    line-height: 64rpx;
  }

  .submitButtonParent {
    width: 100vw;
    height: 100rpx;
    background-color: #fff;
    position: fixed;
    left: 0;
    bottom: 0;
    z-index: 11;
  }

  .submitButton {
    width: 686rpx;
    line-height: 74rpx;
    margin: 0 auto;
    background: #339378;
    border-radius: 38rpx;
  }

  .uni-input {
    height: 64rpx;
    padding-left: 32rpx;
    background-color: #f3f8fc;
  }

  .partnerFlex {
    display: flex;
    justify-content: space-between;
  }

  .lineHiehgt {
    line-height: 64rpx;
  }

  .addName {
    font-size: 20rpx;
  }

  .upload_content_css {
    width: 160rpx;
    height: 160rpx;
    background-color: #f6f6f6;
  }

  .textarea_style_css {
    position: relative;
    border: 1rpx solid #e1e1e1;
    border-radius: 8rpx;

    .textarem_number {
      color: red;
      position: absolute;
      bottom: 0;
      right: 10rpx;
    }
  }
</style>
