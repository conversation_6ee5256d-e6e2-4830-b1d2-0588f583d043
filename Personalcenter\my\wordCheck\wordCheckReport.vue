<template>
	<view class="wordcheckreport_container">
		<view class="word_count">
			<view style="font-weight: bold;">{{queryLastTest.vocabulary}}</view>
			<view style="color: #4b1515;font-size: 30rpx;">词汇量</view>
		</view>

		<view class="box_style user_info">
			<view>姓名：{{queryLastTest.studentName}}</view>
			<view>学校：{{queryLastTest.schoolName}}</view>
			<view>年级：{{queryLastTest.grade}}</view>
			<view>英语水平：{{queryLastTest.wordLevel}}</view>
		</view>
		<view class="box_style report_module">
			<view class="report_title">具备的能力</view>
			<view class="report_content">
				{{queryLastTest.levelDescription}}
			</view>
		</view>

		<view class="box_style report_module">
			<view class="report_title">测评分析</view>
			<view class="report_content">
				{{queryLastTest.evaluationAnalysis}}
			</view>
		</view>
		
		<view class="box_style report_module">
			<view class="report_title">建议</view>
			<view class="report_content">
				{{queryLastTest.learningAdvice}}
			</view>
		</view>
		
		<view class="word_footer">
			<view v-if="!isStartCheck" class="left" @click="goUrl('../pages/home/<USER>')">返回主页</view>
			<view v-if="!isStartCheck" class="right" @click="goUrl('/Personalcenter/my/wordCheck/wordCheck')">在测一次</view>
		</view>


	</view>
</template>

<script>
	export default {
		data() {
			return {
				queryLastTest: {},
			};
		},
		onLoad() {

			uni.showLoading({
				title: '加载中...'
			});
			this.getReport();
		},
		methods: {
			// 获取上一次记录
			async getReport() {
				let result = await this.$httpUser.get('znyy/course/query/last/test');
				if (result && result.data.success) {
					if (result.data.data) {
						this.queryLastTest = result.data.data;
					}
				}
				setTimeout(function() {
					uni.hideLoading();
				}, 200);
			},
			
			goUrl(url) {
				uni.redirectTo({
					url: url
				})
			},
		},
		


	}
</script>

<style lang="less">
	.word_count {
		display: flex;
		justify-content: center;
		align-items: center;
		flex-direction: column;
		width: 170rpx;
		height: 170rpx;
		border-radius: 100rpx;
		background-color: #fcb575;
		margin: 20rpx auto;
		color: #fff;
	}

	.user_info {
		font-size: 30rpx;
		color: #333;
		height: 200rpx !important;
		display: flex;
		justify-content: space-between;
		flex-direction: column;
		padding: 20rpx 50rpx !important;
	}

	.box_style {
		width: 590rpx;
		box-shadow: 0upx 30upx 60upx #e3e3e3;
		border-radius: 40rpx;
		background-color: #fff;
		margin: 40rpx auto 0;
		padding: 40rpx 50rpx;
	}

	.report_module {
		position: relative;
		.report_title {
			position: absolute;
			top: -10rpx;
			left: 225rpx;
			width: 220rpx;
			height: 65rpx;
			line-height: 65rpx;
			text-align: center;
			border-radius: 0 0 18rpx 18rpx;
			background-color: #3ac9b0;
			font-size: 34rpx;
			color: #fff;
		}

		.report_content {
			text-overflow: ellipsis; //溢出时用...
			overflow: hidden;
			margin-top: 30rpx;
			font-size: 30rpx;
		}
	}

	.word_footer {
		display: flex;
		justify-content: center;
		align-items: center;
		margin: 60rpx 0;

		>view {
			width: 200rpx;
			height: 90rpx;
			font-size: 32rpx;
			text-align: center;
			line-height: 90rpx;
			border-radius: 45rpx;
			font-weight: bold;
			color: #fff;
		}

		.left {
			background-color: #3ac9b0;
			margin: 0 40rpx;
		}

		.right {
			background-color: #fcb575;
			margin: 0 40rpx;
		}
	}
</style>
