<template>
  <view>
    <view class="items">
      <view class="card" v-for="(item, index) in newslist" :key="item.id">
        <view style="display: flex; height: 92rpx">
          <image :src="item.avatar" mode="" class="avater"></image>
          <view>
            <view style="display: flex; height: 44rpx; align-items: center">
              <span class="name">{{ item.nick }}</span>
              <view class="old">{{ item.gender == 0 ? '男孩' : '女孩' }}·{{ item.age }}岁</view>
              <view class="type">
                {{ item.type[0] }}
              </view>
            </view>
            <view style="display: flex; height: 44rpx; align-items: center">
              <span class="glard">{{ array.find((e) => e.value == item.grade).label }}</span>
              <span class="glard" style="margin-left: 16rpx">被邀约人:{{ item.parentMobile }}</span>
            </view>
          </view>
        </view>
        <view class="cardBtn" @click="go(index)">
          查看报告
          <!-- {{index}} -->
        </view>
      </view>
    </view>
    <view v-if="no_more && newslist != undefined && newslist.length > 0">
      <u-divider text="到底了"></u-divider>
    </view>

    <view v-if="newslist.length == 0" class="t-c flex-col bg-ff radius-15 mlr-30" :style="{ height: useHeight + 'rpx' }">
      <image :src="imgHost + 'dxSelect/fourthEdition/none-data.png'" class="mb-20 img"></image>
      <view style="color: #bdbdbd" class="f-30">暂无数据</view>
    </view>
  </view>
</template>

<script>
  export default {
    data() {
      return {
        data: {
          page: 1,
          pageSize: 10,
          mobile: uni.getStorageSync('phone')
        },
        array: [],
        newslist: [], // 消息通知列表
        totalItems: '', // 消息总数量
        status: 'more', // 加载更多
        contentText: {
          contentdown: '加载更多数据',
          contentrefresh: '加载中...',
          contentnomore: '暂无更多数据'
        },
        useHeight: 0,
        no_more: false,
        imgHost: getApp().globalData.imgsomeHost
      };
    },
    onUnload() {
      // #ifdef APP-PLUS
      if (this.app) {
        plus.runtime.quit();
      }
      // #endif
    },
    onLoad(e) {
      this.app = e.app;
      this.$handleTokenFormNative(e);
      this.data.mobile = uni.getStorageSync('phone');
      this.init();
      let token = uni.getStorageSync('token');
      console.log(token);
      if (token) {
        this.getNewslist(); // 消息通知
      } else {
        console.log('没有token');
      }
    },
    onReady() {
      uni.getSystemInfo({
        success: (res) => {
          // 可使用窗口高度，将px转换rpx
          let h = res.windowHeight * (750 / res.windowWidth);
          this.useHeight = h - 30;
        }
      });
    },
    onReachBottom() {
      if (this.data.page * 10 >= Number(this.totalItems)) {
        this.no_more = true;
        return false;
      }
      this.getNewslist(true, ++this.data.page);
    },
    methods: {
      // 跳转去报告页
      go(i) {
        let id = this.newslist[i].reportId;

        uni.navigateTo({
          url: '/Coursedetails/Career/planning?id=' + id
        });
      },
      init() {
        this.array = [
          {
            value: '18',
            label: '幼儿园',
            ext: '',
            children: null
          },
          {
            value: '1',
            label: '一年级',
            ext: '',
            children: null
          },
          {
            value: '2',
            label: '二年级',
            ext: '',
            children: null
          },
          {
            value: '3',
            label: '三年级',
            ext: '',
            children: null
          },
          {
            value: '4',
            label: '四年级',
            ext: '',
            children: null
          },
          {
            value: '5',
            label: '五年级',
            ext: '',
            children: null
          },
          {
            value: '6',
            label: '六年级',
            ext: '',
            children: null
          },
          {
            value: '7',
            label: '七年级',
            ext: '',
            children: null
          },
          {
            value: '8',
            label: '八年级',
            ext: '',
            children: null
          },
          {
            value: '9',
            label: '九年级',
            ext: '',
            children: null
          },
          {
            value: '10',
            label: '高中一年级',
            ext: '',
            children: null
          },
          {
            value: '11',
            label: '高中二年级',
            ext: '',
            children: null
          },
          {
            value: '12',
            label: '高中三年级',
            ext: '',
            children: null
          }
        ];
      },
      async getNewslist(isPage, page) {
        let res = await this.$httpUser.get('zx/career/student/getMessages', this.data);
        if (res.data.success) {
          if (isPage) {
            let old = this.newslist;
            this.newslist = [...old, ...res.data.data.data];
          } else {
            this.newslist = res.data.data.data;
          }
          this.totalItems = res.data.data.totalItems;
          // // 为数据赋值：通过展开运算符的形式，进行新旧数据的拼接
          // this.newslist = [...this.newslist, ...res.data.data.data]
          // this.totalItems = res.data.data.totalItems
          // this.status = this.newslist.length < res.data.data.totalItems ? 'more' : 'no-more'
        }
      }
    }
  };
</script>

<style lang="scss" scoped>
  .img {
    width: 160rpx;
    height: 165rpx;
  }
  .items {
    background-color: #f5f8fa;
    .card {
      // width: 600rpx;
      // height: 256rpx;
      margin: 30rpx 32rpx;
      background-color: #fff;
      border-radius: 24rpx;
      padding: 34rpx 24rpx;
      box-sizing: border-box;
      overflow: hidden;

      .cardBtn {
        color: #fff;
        font-size: 28rpx;
        font-weight: bold;
        width: 196rpx;
        // margin: 32rpx 0 0;
        margin-top: 32rpx;
        margin-left: 456rpx;
        text-align: center;
        line-height: 60rpx;
        height: 60rpx;
        background: linear-gradient(180deg, #88cfba 0%, #1d755c 100%);
        border-radius: 60rpx;
      }

      .avater {
        width: 82rpx;
        height: 82rpx;
        border-radius: 82rpx;
        margin-right: 48rpx;
      }

      .name {
        height: 44rpx;
        line-height: 44rpx;
        font-size: 32rpx;
        color: #555555;
        font-weight: bold;
        font-family: AlibabaPuHuiTi_2_85_Bold;
      }

      .old {
        height: 32rpx;
        background: rgba(51, 147, 120, 0.06);
        border-radius: 20rpx;
        border: 2rpx solid #aacac1;
        font-size: 24rpx;
        line-height: 34rpx;
        color: #2e836b;
        padding: 0 24rpx;
        margin-left: 8rpx;
        text-align: center;
      }

      .glard {
        font-size: 28rpx;
        color: #a7a7a7;
        margin-left: 24rpx;
      }

      .type {
        margin-left: 12rpx;
        width: 120rpx;
        height: 34rpx;
        line-height: 34rpx;
        font-size: 24rpx;
        color: #ffffff;
        text-align: center;
        background: #33cb9e;
        border-radius: 30rpx;
      }
    }
  }
</style>
