<template>
  <view class="join-group" :class="isSuccess ? 'success-group' : isFail ? 'fail-group' : isJoinGroup ? 'in-group' : ''"
    @click="handleClick">
    <view class="time-down">
      <text class="text">拼团剩余时间</text>
      <view class="time">{{ countdown.hours }}</view>
      <view class="colon">:</view>
      <view class="time">{{ countdown.minutes }}</view>
      <view class="colon">:</view>
      <view class="time">{{ countdown.seconds }}</view>
    </view>

    <view class="content">
      <view v-if="isSuccess" :style="{ marginBottom: '14rpx' }">{{ isInGroup ? '恭喜你，' : '' }}已经拼团成功啦！</view>
      <view v-else-if="isFail" :style="{ marginBottom: '14rpx' }">拼团结束啦！</view>
      <view v-else :style="{ marginBottom: '14rpx' }">
        再邀请
        <text class="orange">{{ groupOverNum }}</text>
        人 即可成团啦！
      </view>
      <view class="group-tips">仅需{{ groupSize }}人即可成团</view>

      <scroll-view class="group-user-list" scroll-x>
        <view class="user-item" v-for="(item, index) in groupUserList" :key="item">
          <image class="wh100" :src="item.headPortrait || avatarUrl" mode="aspectFill"></image>
        </view>
      </scroll-view>
    </view>

    <view v-if="isSuccess || isFail" class="invite go-group" @click="handleGo"></view>
    <view v-else-if="isInGroup">
      <!-- #ifdef MP-WEIXIN -->
      <button open-type="share" @click.stop="" :data-invite="true" class="invite" hover-class="none"></button>
      <!-- #endif -->
      <!-- #ifdef APP-PLUS -->
      <button open-type="share" @click.stop="shareFriend" :data-invite="true" class="invite"
        hover-class="none"></button>
      <!-- #endif -->

    </view>
    <view v-else class="invite join" @click="handleJoinGroup()"></view>
  </view>
</template>

<script>
  const {
    $http,
    $navigationTo
  } = require('@/util/methods.js');
  export default {
    props: {
      groupInstanceId: {
        type: String,
        default: ''
      },
      groupText: {
        type: String,
        default: ''
      },
      groupImg: {
        type: String,
        default: ''
      },
      groupPath: {
        type: String,
        default: ''
      },
      isJoinGroup: {
        type: Boolean,
        default: false
      },
      isInGroup: {
        type: Boolean,
        default: false
      }
    },
    data() {
      return {
        countdown: {
          days: '00',
          hours: '00',
          minutes: '00',
          seconds: '00'
        },
        groupInstanceId: '',
        groupUserList: [], // 参团用户信息
        groupSize: 0,
        groupOverNum: 0,
        endTime: 0,
        groupInstanceInfo: {},
        timeOver: false,
        avatarUrl: 'https://document.dxznjy.com/dxSelect/home_avaUrl.png',
        isSuccess: false, // 是否成功
        isFail: false // 拼团失败
      };
    },
    watch: {
      groupInstanceId: {
        handler(newVal, oldVal) {
          if (newVal && newVal !== oldVal) {
            this.getGroupInstanceInfo();
          }
        },
        immediate: true,
        deep: true
      }
    },
    methods: {
      //app 分享
      shareFriend() {
        let shareInfo = {
          title: this.groupText,
          imageUrl: this.groupImg, //分享封面
          path: this.groupPath
        };
        uni.$appShare(shareInfo, 2)
      },
      startCountdown() {
        const updateCountdown = () => {
          const now = new Date().getTime();
          const timeLeft = this.endTime - now;

          if (timeLeft <= 0) {
            clearInterval(this.countdownTimer);
            this.countdown = {
              days: '00',
              hours: '00',
              minutes: '00',
              seconds: '00',
              status: 'finish'
            };
            this.handleCountdownEnd();
            return;
          }

          const days = Math.floor(timeLeft / (1000 * 60 * 60 * 24));
          const hours = Math.floor((timeLeft % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60)) + days * 24;
          const minutes = Math.floor((timeLeft % (1000 * 60 * 60)) / (1000 * 60));
          const seconds = Math.floor((timeLeft % (1000 * 60)) / 1000);

          this.countdown = {
            days: String(days).padStart(2, '0'),
            hours: String(hours).padStart(2, '0'),
            minutes: String(minutes).padStart(2, '0'),
            seconds: String(seconds).padStart(2, '0')
          };
        };

        updateCountdown();
        this.countdownTimer = setInterval(updateCountdown, 1000);
      },
      handleCountdownEnd() {
        // 倒计时结束时的处理逻辑
        console.log('倒计时已结束');
        this.timeOver = true;
      },
      handleClick() {
        this.$refs.inviteGroupBuyingRef.open();
      },
      handleJoinGroup() {
        this.$emit('joinGroup', this.groupInstanceId);
      },
      getGroupInstanceInfo() {
        uni.showLoading({
          title: '加载中...',
          mask: true
        });
        $http({
          url: 'zx/wap/group/instances/single',
          method: 'get',
          data: {
            instancesId: this.groupInstanceId
          }
        }).then((res) => {
          if (res.data) {
            this.groupInstanceInfo = res.data;
            this.groupSize = res.data && res.data ? res.data.groupSize : 0;
            this.groupOverNum = res.data && res.data && res.data.groupOverNum > 0 ? res.data.groupOverNum : 0;
            this.groupUserList = res.data.piGroupParticipantsVos || [];
            this.isSuccess = this.groupInstanceInfo.groupInstancesStatus == 2;
            this.isFail = this.groupInstanceInfo.groupInstancesStatus == 0;
            this.$emit('getGroupUserList', this.groupUserList, this.isSuccess || this.isFail);

            // 补充空对象到 groupUserList，确保长度为 groupSize
            const emptySlots = this.groupOverNum;
            if (emptySlots > 0) {
              for (let i = 0; i < emptySlots; i++) {
                if (this.isSuccess) {
                  this.groupUserList.push({
                    headPortrait: this.avatarUrl,
                    isEmpty: true
                  });
                } else {
                  this.groupUserList.push({
                    headPortrait: 'https://document.dxznjy.com/dxSelect/6e51b22d-3410-4952-85b2-3247e9fb267c.png',
                    isEmpty: true
                  });
                }
              }
            }

            this.endTime = new Date(res.data.endTime).getTime();
            this.startCountdown();
          }
        });
      },
      handleGo() {
        $navigationTo('activity/groupBuying/myGroupBuying');
      }
    },
    mounted() {
      this.startCountdown();
    },
    beforeDestroy() {
      clearInterval(this.countdownTimer);
    }
  };
</script>

<style lang="scss" scoped>
  .join-group {
    position: relative;
    width: 686rpx;
    height: 772rpx;
    margin-top: 24rpx;
    background-image: url('https://document.dxznjy.com/dxSelect/28913395-a0d3-4413-9c86-8be7b353009e.png');
    background-size: 100% 100%;

    .time-down {
      display: flex;
      align-items: center;
      height: 76rpx;
      padding-top: 96rpx;
      padding-left: 40rpx;
      color: #333333;
      font-size: 28rpx;
      font-family: AlibabaPuHuiTiBold;

      .text {
        margin-right: 16rpx;
      }

      .time {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 56rpx;
        height: 56rpx;
        font-size: 32rpx;
        color: #ffffff;
        background: #fd9b2a;
        border-radius: 10rpx;
      }

      .colon {
        font-size: 32rpx;
        padding: 0 14rpx;
      }
    }

    .content {
      width: 100%;
      text-align: center;
      font-size: 36rpx;
      color: #333333;
      line-height: 50rpx;
      padding: 42rpx 0 0;

      .orange {
        font-size: 40rpx;
        color: #fd9b2a;
        line-height: 56rpx;
        padding: 0 8rpx;
        font-family: AlibabaPuHuiTiBold;
        font-weight: bold;
      }

      .group-tips {
        width: 100%;
        font-size: 28rpx;
        color: #a1a1a1;
        line-height: 40rpx;
      }
    }

    .group-user-list {
      white-space: nowrap;
      display: flex;
      align-items: center;
      flex-wrap: nowrap;
      width: 348rpx;
      margin: 30rpx auto;
      box-sizing: border-box;
      height: 80rpx;

      .user-item {
        display: inline-block;
        width: 80rpx;
        height: 80rpx;
        margin-right: 54rpx;
        border-radius: 50%;
        overflow: hidden;
      }

      .user-item:last-child {
        margin-right: 0;
      }
    }

    .invite {
      position: absolute;
      bottom: 54rpx;
      left: 50%;
      width: 440rpx;
      height: 80rpx;
      margin-left: -220rpx;
      background-image: url('https://document.dxznjy.com/dxSelect/1760644b-b3d9-4790-882d-93cf777e9f8e.png');
      background-size: 100% 100%;
    }

    .join {
      background-image: url('https://document.dxznjy.com/course/1be93981561949c097177640967f9846.png');
      background-size: 100% 100%;
    }

    .go-group {
      background-image: url('https://document.dxznjy.com/dxSelect/c92c1fda-4efc-4d61-b627-da1bf46c974f.png');
      background-size: 100% 100%;
    }
  }

  .in-group {
    background-image: url('https://document.dxznjy.com/course/bdc07e4e690d4b819a0acaa3c91c0779.png');
    background-size: 100% 100%;
  }

  .success-group {
    background-image: url('https://document.dxznjy.com/dxSelect/c07628ca-771e-482b-a23c-794a9784b3ba.png');
    background-size: 100% 100%;
  }

  .fail-group {
    background-image: url('https://document.dxznjy.com/dxSelect/2c7c5ba9-39e0-4b77-b8e2-35fea5768f2d.png');
    background-size: 100% 100%;
  }
</style>