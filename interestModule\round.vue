<template>
  <view class="wordContent">
    <view class="userHead" style="background: #18b48e">
      <image class="iconBack" :src="imgHost + 'title_back.png'" @click="gotoBack()" mode=""></image>
      关卡报告
    </view>

    <view class="turnListBox">
      <view class="turnList">
        <view class="turnlist_title1">
          <image class="turnlistTitle_img" :src="imgHost + 'interesting/class_icon' + needData.level + '.png'" mode=""></image>
          <text class="turnTitle_text">{{ showData.courseName }}</text>
          <text class="turnTitle_text1">第{{ needData.level }}关</text>
        </view>
      </view>

      <!-- 关卡正确率 -->
      <view class="turnList">
        <view class="turnlist_title">
          <text class="greenBlock"></text>
          <text class="turnTitle_text">关卡正确率</text>
        </view>

        <view class="turnChart_content">
          <view class="chart_round">
            <view class="roundChart_list roundChart_list1">
              <view class="roundProgress">
                <view class="circleTransbox">
                  <cmd-progress
                    type="circle"
                    class="circleTrans"
                    :percent="needData.rightRate * 100"
                    :width="148"
                    :showInfo="false"
                    :stroke-color="'rgb(206,166,227)'"
                  ></cmd-progress>
                  <view class="circleBox_center">
                    <text class="circleBox_center_progress">{{ needData.rightRate * 100 }}</text>
                    %
                  </view>
                </view>
              </view>
            </view>
            <view class="progressLine_box">
              <view class="progressLine_t1">
                <image class="progressLine_img" :src="imgHost + 'interesting/progress_line.png'" mode=""></image>
                <text class="progressLine_title1">单词数：{{ showData.wordCount }}</text>
              </view>
              <view class="progressLine_t1 progressLine_t2">
                <image class="progressLine_img" :src="imgHost + 'interesting/progress_line.png'" mode=""></image>
                <text class="progressLine_title1">组数：{{ showData.groupCount }}组</text>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 分组正确率 -->
      <view class="turnList">
        <view class="turnlist_title">
          <text class="greenBlock"></text>
          <text class="turnTitle_text">分组正确率</text>
        </view>

        <view class="turnChart_content turnChart_content1">
          <view class="bgwh">
            <uCharts
              :scroll="arr[0].opts.enableScroll"
              :show="true"
              :canvasId="arr[0].id"
              :chartType="arr[0].chartType"
              :extraType="arr[0].extraType"
              :cWidth="cWidth"
              :cHeight="cHeight"
              :opts="arr[0].opts"
              :ref="arr[0].id"
            />
          </view>
        </view>
      </view>

      <!-- 关卡得分 -->
      <view class="turnList">
        <view class="turnlist_title">
          <text class="greenBlock"></text>
          <text class="turnTitle_text">关卡得分</text>
        </view>

        <view class="turnChart_content" style="overflow: hidden">
          <view class="circleTransbox1" style="color: #449179">
            <cmd-progress
              type="circle"
              class="circleTrans"
              :percent="showData.score"
              :width="200"
              :showInfo="false"
              :stroke-color="'rgb(68,145,121)'"
              :stroke-width="3"
            ></cmd-progress>
            <text class="circle_title">本关你的得分</text>
            <text class="circle_title1">{{ showData.score }}</text>
          </view>

          <view class="circleTransbox1 circleTransbox2" style="color: #77d184">
            <cmd-progress
              type="circle"
              class="circleTrans"
              :percent="showData.scoreAvg"
              :width="190"
              :showInfo="false"
              :stroke-color="'rgb(119,209,132)'"
              :stroke-width="3"
            ></cmd-progress>
            <text class="circle_title">本关平均得分</text>
            <text class="circle_title1">{{ showData.scoreAvg }}</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
  import cmdProgress from './components/cmd-progress/cmd-progress.vue';
  import uCharts from '@/components/u-charts/u-charts.vue';

  export default {
    components: {
      cmdProgress,
      uCharts
    },
    data() {
      return {
        imgHost: getApp().globalData.imguseHost,
        blue: '#85d481',
        green: '#18b491',
        cWidth: '',
        cHeight: '',
        arr: [],
        data: {
          LineA: {
            categories: [],
            series: [
              {
                name: '关卡数',
                data: [],
                color: '#d2efe1'
              }
            ],
            yAxis: {
              min: 0,
              max: 100
            }
          }
        },

        needData: {},
        showData: {}
      };
    },

    onLoad(options) {
      this.needData = JSON.parse(decodeURIComponent(options.roundData));
      this.showData = JSON.parse(decodeURIComponent(options.roundReportData));

      this.init();
    },
    methods: {
      //获取关卡数据
      getShowData() {
        let that = this;
        uni.showLoading();
        that.$httpUser
          .get(`znyy/stats/review/getLevelReport?roundId=${that.needData.roundId}&scheduleCode=${that.needData.scheduleCode}&level=${that.needData.level}`)
          .then((res) => {
            if (res.data.success) {
              uni.hideLoading();
              //  console.log(res.data.data.data)
              that.showData = res.data.data.data;
              that.showData.groupRateList.forEach((item) => {
                var data = '第' + item.group + '组';
                that.data.LineA.categories.push(data);
                that.data.LineA.series[0].data.push(item.rightRate);
              });
            } else {
              uni.hideLoading();
              that.$util.alter(res.data.message);
            }
          });
      },
      init() {
        this.showData.groupRateList.forEach((item) => {
          var data = '第' + item.group + '组';
          this.data.LineA.categories.push(data);
          this.data.LineA.series[0].data.push(item.rightRate);
        });
        this.cWidth = uni.upx2px(630);
        this.cHeight = uni.upx2px(400);
        this.getServerData();
      },

      async getServerData() {
        var LineA = {
          enableScroll: false,
          unit: ''
        };
        LineA.categories = this.data.LineA.categories; //result.data.LineA.categories
        LineA.series = this.data.LineA.series; //result.data.LineA.series
        LineA.yAxis = this.data.LineA.yAxis;

        var serverData = [
          {
            group: '关卡报告',
            title: '分组正确率',
            opts: LineA,
            chartType: 'column',
            extraType: 'group',
            id: 'c0'
          }
        ];
        this.arr = serverData;
      },
      change(idx, type, etype) {
        this.$refs[this.arr[idx].id][0].changeData(this.arr[idx].id, this.arr[idx].opts, type, etype);
      },

      //返回按钮
      gotoBack() {
        uni.navigateBack({
          delta: 1
        });
      }
    }
  };
</script>

<style>
  page {
    background: #f9f9f9;
    height: 100vh;
    padding: 0;
  }

  .wordContent {
    height: 100%;
    position: relative;
  }

  .iconBack {
    width: 80rpx;
    height: 68rpx;
  }

  .turnlist_title1 {
    overflow: hidden;
  }

  .turnlist_title1 > text {
    float: left;
    margin: 20rpx 40rpx 0rpx 30rpx;
  }

  .turnlistTitle_img {
    width: 60rpx;
    height: 68rpx;
    float: left;
    overflow: hidden;
  }

  .circleTransbox1 {
    width: 240rpx;
    height: 230rpx;
    display: inline-block;
    float: left;
    margin: 60rpx 30rpx 0 42rpx;
    position: relative;
    text-align: center;
    overflow: hidden;
  }
  .circleTransbox1 text {
    display: inline-block;
    font-size: 24rpx;
    position: absolute;
    top: 0;
    left: 20rpx;
    width: 180rpx;
    height: 60rpx;
    line-height: 60rpx;
    text-align: center;
  }
  .circle_title {
    top: 40rpx !important;
    width: 240rpx !important;
  }
  .circle_title1 {
    top: 90rpx !important;
    font-size: 40rpx !important;
    width: 240rpx !important;
  }

  .circleTransbox2 {
    width: 190rpx;
    height: 190rpx;
    display: inline-block;
    margin-top: 70rpx;
  }
  .circleTransbox2 text {
    left: 0;
  }
  .circleTransbox2 .circle_title,
  .circleTransbox2 .circle_title1 {
    width: 190rpx !important;
  }
</style>
