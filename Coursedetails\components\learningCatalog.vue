<template>
  <view>
    <!-- <view class="video_top_css" v-if="itemInfo.videoUrl" @click="getVideoUrl(itemInfo, 1)">
      <image v-if="itemInfo.play" mode="widthFix" class="image_css" src="https://document.dxznjy.com/course/e68e7c737ae84ec88832288da8a081a9.png"></image>
      <image v-else mode="widthFix" class="image_css" src="https://document.dxznjy.com/course/5547f897939d493d8a151137c3a56241.png"></image>
      <text>{{ itemInfo.catalogueName }}</text>
    </view> -->
    <!-- <view v-else> -->
    <view>
      <view class="flex-space-between video_top_css">
        <view>{{ itemInfo.courseName }}</view>
        <view>
          <u-icon v-if="itemInfo.down" @click="changeDown(itemInfo)" name="arrow-up" color="#575757" size="32"></u-icon>
          <u-icon v-else name="arrow-down" @click="changeDown(itemInfo)" color="#575757" size="32"></u-icon>
        </view>
      </view>
      <view v-if="itemInfo.down">
        <view v-for="(info, index) in itemInfo.videoList" :key="info.id" class="course_two_css" @click="getVideoUrl(itemInfo.videoList, 2, index)">
          <!-- <image v-if="info.play" style="height: 22rpx" class="image_css" src="https://document.dxznjy.com/course/e68e7c737ae84ec88832288da8a081a9.png"></image> -->
          <image mode="widthFix" class="image_css" src="https://document.dxznjy.com/course/5547f897939d493d8a151137c3a56241.png"></image>
          <text class="title_css">{{ info.videoName }}</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
  export default {
    props: ['item', 'index'],
    data() {
      return {
        itemInfo: {}
      };
    },
    watch: {
      // item(newVal) {
      // 	console.log(newVal)
      // 	this.itemInfo=newVal
      // 	console.log('---------------newValnewValnewValnewValnewVal-----------------------')
      // },
      item: {
        immediate: true,
        handler(newVal) {
          this.itemInfo = { ...newVal };
          console.log('---------------newValnewValnewValnewValnewVal-----------------------', this.itemInfo);
        }
      }
    },

    methods: {
      changeDown(item) {
        // uni.navigateTo({
        //   url: '/Coursedetails/study/courseDetail?type=0'
        // });
        // item.down=!item.down
        this.$set(item, 'down', !item.down);
        this.$emit('changeDown', this.index);
      },
      getVideoUrl(info, key, index) {
        console.log('info视频切换', info, key);
        console.log('22222222222222222222', info, index);
        this.$emit('getVideoUrl', { info: info, key: key, index: index });
      }
    }
  };
</script>

<style lang="scss" scoped>
  .video_top_css {
    background-color: #f6f7f9;
    padding: 24rpx 32rpx;
    color: #555555;
    font-weight: bold;
    font-size: 28rpx;
  }
  .image_css {
    width: 24rpx;
    height: 10rpx;
    margin-right: 16rpx;
  }
  .flex-space-between {
    display: flex;
    justify-content: space-between;
  }
  .course_two_css {
    padding: 32rpx;
    .title_css {
      color: #555555;
      font-size: 28rpx;
    }
    border-bottom: 2rpx solid #f6f7f9;
  }
  .course_two_css:last-child {
    border: none;
  }
</style>
