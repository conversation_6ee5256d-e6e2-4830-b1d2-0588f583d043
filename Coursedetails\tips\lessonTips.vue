<template>
  <view class="content">
    <!-- 自定义导航栏 -->
    <view class="custom-nav">
      <view class="custom-nav-item">
        <view>
          <!-- #ifdef MP-WEIXIN -->
          <u-icon name="arrow-left" size="46" @click="goBack"></u-icon>
          <!-- #endif -->

          <!-- #ifdef APP-PLUS -->
          <u-icon name="arrow-left" size="23" @click="goBack"></u-icon>
          <!-- #endif -->
        </view>

        <view class="nav-title">温馨提示</view>
        <view></view>
      </view>
    </view>
    <view class="textTitle dis_center">
      <image class="imgIcon" :src="src"></image>
      <text v-if="lessonType == 0 || lessonType == 1">恭喜你购买试课成功</text>
      <text v-if="lessonType == 2 || lessonType == 3 || lessonType == 6">恭喜你购买正式课成功</text>
      <text v-if="lessonType == 5">恭喜你填写试课单成功</text>
      <text v-if="lessonType == 7">恭喜你上课信息对接表填写成功</text>
    </view>
    <view class="dis_center">
      <view class="textMiddle" v-if="lessonType == 0 || lessonType == 2">请下载或长按识别二维码，下载鼎校甄选APP，否则学员将无法上课</view>
      <view class="textMiddle" v-if="lessonType == 1 || lessonType == 3 || lessonType == 6 || lessonType == 7">
        请保存二维码并分享给家长，邀请家长及时下载鼎校甄选APP，否则学员将无法上课
      </view>
      <view class="textMiddle" v-if="lessonType == 5">请下载二维码，发送给试课学员家长邀请家长下载“鼎校甄选”APP，否则学员将无法上课</view>
    </view>
    <view class="dis_center">
      <image class="imgCode" :src="codeForm.qrCode" show-menu-by-longpress="true" @tap="previewImage"></image>
    </view>
    <view class="text-btn dis_center">
      <text @click="saveCode">下载二维码</text>
    </view>
    <!-- 处理所有返回 -->
    <view v-if="showPage">
      <page-container :show="showPage" :duration="false" :overlay="false" @afterleave="beforeleave"></page-container>
    </view>
  </view>
</template>

<script>
  const { httpUser } = require('@/util/luch-request/indexUser.js');
  export default {
    data() {
      return {
        app: 0,
        src: 'https://document.dxznjy.com/course/646ec3f4e378415da3834f3c33dce03c.png',
        codeForm: {
          qrCode: 'https://document.dxznjy.com/course/7b20a35bbdbd4afeb54e42d84e3a5cbc.png'
        },
        lessonType: null, // 0自购试成  1推购试成  2自购正成  3推购正成 5填写试课单成功
        showPage: true
      };
    },
    // 左滑返回方法
    onBackPress() {
      let url = '';
      if ([0, 1, 2, 3].includes(this.lessonType)) {
        // lessonType 是 0、1、2、3、6，退出 app
        plus.runtime.quit();
      }
      return false; // 拦截默认返回行为
    },

    onLoad(options) {
	  uni.setStorageSync('clearData', true);
      console.log('LessonTips options', options);
      // 5:填写试课单 6:充值时常 7:上课信息对接表
      console.log('options.goodsType', options.goodsType, options.referrerType);
      // 商品类型(1:实物商品，2:交付课-试课，3：交付课-正式课，4：录播课，5：会议)
      // referrerType : 0 自购 1 推购
      if (options.referrerType == 0 && options.goodsType == 2) {
        this.lessonType = 0;
      } else if (options.referrerType == 0 && options.goodsType == 3) {
        this.lessonType = 2;
      } else if (options.referrerType == 1 && options.goodsType == 2) {
        this.lessonType = 1;
      } else if (options.referrerType == 1 && options.goodsType == 3) {
        this.lessonType = 3;
      } else if (options.trailStatus === 'true') {
        this.lessonType = 5;
      } else if (options.offStatus === 'true') {
        this.lessonType = 6;
      } else if (options.schoolTable === 'true') {
        this.lessonType = 7;
      }
    },
    onUnload() {
      // #ifdef APP-PLUS
      if ([0, 1, 2, 3].includes(this.lessonType)) {
        // lessonType 是 0、1、2、3，退出 app
        plus.runtime.quit();
      }
      // #endif
    },

    methods: {
      // 返回订单列表页面
      goBack() {
        this.beforeleave();
      },
      beforeleave() {
        this.showPage = false;
        let url = '';
        // if (this.lessonType == 6) {
        //   // 返回到 我的 页面
        //   plus.runtime.quit();
        // } else {
          uni.navigateBack();
        // }
      },
      //保存二维码
      // wx.env.USER_DATA_PATH 是小程序沙箱文件夹的路径
      // wx.getFileSystemManager() 是微信小程序原生 API
      // 我们通过复制的方式添加 .png 后缀，再保存,适配不同ios机型保存图片格式不正确问题
      saveCode() {
        let that = this;
        uni.downloadFile({
          url: that.codeForm.qrCode,
          success: (res) => {
            console.log('res', res);
            if (res.statusCode === 200) {
              // #ifdef MP-WEIXIN
              // 手动加后缀
              const filePath = res.tempFilePath;
              const newFilePath = `${wx.env.USER_DATA_PATH}/qrcode.png`; // 改成你想要的文件名和后缀
              console.log('filePath', filePath);
              console.log('newFilePath', newFilePath);
              // 使用 FileSystemManager 复制并加后缀
              const fs = wx.getFileSystemManager();
              fs.copyFile({
                srcPath: filePath,
                destPath: newFilePath,

                success: () => {
                  uni.saveImageToPhotosAlbum({
                    filePath: newFilePath,
                    success: () => {
                      uni.showToast({ title: '图片已保存', duration: 2000 });
                    },
                    fail: (err) => {
                      uni.showToast({ title: '保存失败', duration: 2000, icon: 'none' });
                    }
                  });
                },
                fail: (err) => {
                  console.error('文件重命名失败', err);
                  uni.showToast({ title: '处理失败', duration: 2000, icon: 'none' });
                }
              });
              // #endif
              // #ifndef MP-WEIXIN
              uni.saveImageToPhotosAlbum({
                filePath: res.tempFilePath,
                success: function () {
                  uni.showToast({
                    title: '图片已保存',
                    duration: 2000
                  });
                },
                fail: function () {
                  uni.showToast({
                    title: '保存失败',
                    duration: 2000,
                    icon: 'none'
                  });
                }
              });
              // #endif
            }
          },
          fail: () => {
            uni.showToast({ title: '下载失败', duration: 2000, icon: 'none' });
          }
        });
      },
      previewImage() {
        uni.previewImage({
          current: this.codeForm.qrCode, // 当前显示图片的http链接
          urls: [this.codeForm.qrCode] // 需要预览的图片http链接列表
        });
      },
      onTapCode() {
        // 用户点击图片时跳转（轻触）
        // 这里只处理非长按跳转的场景
        // uni.navigateTo({
        //   url: '/tips/webview?url=' + encodeURIComponent('http://**************:9020')
        // });
        console.log('点击事件');

        uni.navigateTo({
          url: '/tips/webview'
        });
      }
    }
  };
</script>

<style>
  .dis_center {
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .back-btn {
    position: absolute;
    top: 40rpx;
    left: 30rpx;
    z-index: 10;
  }

  .content {
    display: flex;
    flex-direction: column;
	align-items: center;
    min-height: calc(100vh - 169rpx);
	padding-bottom: 343rpx;
	box-sizing: border-box;
    /* width: 752px; */
    /* height: 1390px; */
    background-image: url('https://document.dxznjy.com/course/da3435044a064572a64dd9a7a028c26d.png');
    background-size: 100% 100%;
    font-family: 'AlibabaPuHuiTi-3-85-Bold', 'Alibaba PuHuiTi', sans-serif;
    position: relative;
    top: 169rpx;
  }

  .custom-nav {
    width: 100%;
    height: 169rpx;
    background-color: #ffffff;

    position: fixed;
    top: 0;
    left: 0;
    z-index: 999;
    box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
    padding-top: 105rpx;
    box-sizing: border-box;
  }

  .custom-nav-item {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 10rpx;
  }

  .custom-nav-item view {
    width: 33%;
  }

  .nav-title {
    font-size: 32rpx;
    // margin-top: 82rpx;
    font-weight: bold;
    color: #333333;
    text-align: center;
  }

  .textTitle {
    /* margin-top: 406rpx; */

    margin-top: 28.8vh;
    margin-bottom: 41rpx;
    color: #006f57;
    font-size: 36rpx;
    font-weight: 700;
    white-space: nowrap;
    line-height: 50rpx;
    /* background-color: pink; */
  }

  .imgIcon {
    height: 55rpx;
    width: 55rpx;
    margin-right: 25rpx;
  }

  .textMiddle {
    color: #666666;
    width: 60vw;
    font-size: 30rpx;
    line-height: 50rpx;
  }

  .imgCode {
    margin-top: 60rpx;
    height: 360rpx;
    width: 360rpx;
  }

  .text-btn {
    height: 94.25rpx;
    width: 357.14rpx;
    background-color: #428a6f;
    border-radius: 47rpx;
    color: #ffffff;
    font-size: 36rpx;
	/* margin-top: 175rpx; */
    position: absolute;
    bottom: 74rpx;
    left: 50%;
    /* 将元素的左边缘移动到父容器的中心点 */
    transform: translateX(-50%);
    /* 将元素向左移动自身宽度的一半 */
  }
</style>
