<template>
  <uni-popup ref="tips_content_popup" type="center" style="padding: 0">
    <view class="bg-ff radius-25 plr-32 tips_content_css positionRelative ptb-45">
      <view class="flex-x-b flex-a-c f-24">
        <view class="popup_title f-32 c-55 lh-44 pl-30">提示</view>
        <view class="close-circle-fill positionAbsolute">
          <u-icon name="close-circle-fill" @click="$refs.tips_content_popup.close()" color="#B1B1B1" size="38"></u-icon>
        </view>
      </view>
      <view class="lh-44 mt-75 c-33 f-28">
        {{ tipsType == 1 ? '会员用户可免费查看专家回答' : tipsType == 2 ? '该课程超级会员专享。我们正在筹备升级，家长会员即将解锁。敬请期待！' : '该功能超级会员专享。我们正在筹备升级，家长会员即将解锁。敬请期待！' }}
      </view>
      <view class="flex-x-b flex-a-c f-24 mt-70 plr-15">
        <button @click="cancelOrBuy" class="f-32 lh-44 left_btn">{{ tipsType == 1 ? '支付1.00元查看' : '取消' }}</button>
        <!-- 2024-11-6 紧急修改 购买超级会员修改成购买家长会员 隐藏 -->
        <button v-if="false" class="rigth_btn f-32 lh-44 c-ff" @click="openMembership">开通会员</button>
      </view>
    </view>
  </uni-popup>
</template>

<script>
  const { $navigationTo } = require('@/util/methods.js');
  export default {
    props: ['tipsType'],
    data() {
      return {};
    },
    methods: {
      open() {
        this.$refs.tips_content_popup.open();
      },
      //
      cancelOrBuy() {
        if (this.tipsType == 1) {
          this.$emit('payAnswers');
          this.$refs.tips_content_popup.close();
        } else {
          this.$refs.tips_content_popup.close();
        }
      },
      openMembership() {
        $navigationTo('Personalcenter/my/nomyEquity?type=2');
      }
    }
  };
</script>

<style lang="scss" scoped>
  .tips_content_css {
    width: 624rpx;
    text-align: center;
    .popup_title {
      width: 500rpx;
    }
    .close-circle-fill {
      top: 32rpx;
      right: 32rpx;
    }
    .left_btn {
      width: 280rpx;
      height: 92rpx;
      border-radius: 46rpx;
      border: 2rpx solid #428a6f;
      color: #428a6f;
      line-height: 92rpx;
    }
    .rigth_btn {
      width: 280rpx;
      height: 92rpx;
      line-height: 92rpx;
      background: #428a6f;
      border-radius: 46rpx;
    }
  }
</style>
